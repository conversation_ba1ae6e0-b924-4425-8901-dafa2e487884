"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4471],{81330:function(e,t,n){n.d(t,{Z:function(){return v}});var r=n(57437),a=n(52323),s=n(78939),l=n(25429),i=n(62869),o=n(15681),d=n(95186),c=n(26815),u=n(53647),m=n(6512),f=n(1828),x=n(24458),p=n(94508),h=n(99397),j=n(21047),g=n(43949);function v(e){var t;let{form:n,logoImage:v}=e,{currencies:y,isLoading:b}=(0,x.j)(),{t:N}=(0,g.$G)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.Wi,{control:n.control,name:"uploadLogo",render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.lX,{children:N("Method logo")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(s.S,{id:"uploadLogo",defaultValue:(0,p.qR)(v),onChange:e=>t.onChange(e),className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,r.jsx)(l.X,{}),(0,r.jsx)("p",{className:"text-sm font-normal text-primary",children:N("Upload logo")})]})})})]})}}),(0,r.jsx)(o.Wi,{control:n.control,name:"name",render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.lX,{children:N("Name")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(d.I,{placeholder:N("Method name"),type:"text",className:"disabled:bg-input",...t})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{control:n.control,name:"countryCode",render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"col-span-12",children:[(0,r.jsx)(o.lX,{children:N("Country")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(a.g,{allCountry:!0,onSelectChange:e=>t.onChange(e.code.cca2)})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"currencyCode",control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.lX,{children:N("Currency")}),(0,r.jsx)(o.NI,{children:(0,r.jsxs)(u.Ph,{defaultValue:t.value,onValueChange:t.onChange,children:[(0,r.jsx)(u.i4,{className:"text-base disabled:bg-input",children:(0,r.jsx)(u.ki,{placeholder:N("Select currency")})}),(0,r.jsx)(u.Bw,{children:!b&&(null==y?void 0:y.map(e=>(0,r.jsx)(u.Ql,{value:e.code,children:e.code},e.code)))})]})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"active",control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,r.jsx)(c.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:N("Active")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(f.Z,{defaultChecked:!!t.value,onCheckedChange:t.onChange,className:"disabled:opacity-100"})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"recommended",control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,r.jsx)(c.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:N("Recommended")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(f.Z,{defaultChecked:!!t.value,onCheckedChange:t.onChange,className:"disabled:opacity-100"})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(m.Z,{}),(0,r.jsx)(o.Wi,{name:"minAmount",control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"mt-2",children:[(0,r.jsxs)(o.lX,{children:[N("Minimum amount"),n.watch("currencyCode")&&" (".concat(n.watch("currencyCode"),")")]}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(d.I,{type:"text",placeholder:"300",className:"disabled:bg-input",...t})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"maxAmount",control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"mt-2",children:[(0,r.jsxs)(o.lX,{children:[N("Maximum amount"),n.watch("currencyCode")&&" (".concat(n.watch("currencyCode"),")")]}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(d.I,{type:"text",placeholder:"3200000",className:"disabled:bg-input",...t})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"fixedCharge",control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"mt-2",children:[(0,r.jsxs)(o.lX,{children:[N("Fixed charge"),n.watch("currencyCode")&&" (".concat(n.watch("currencyCode"),")")]}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(d.I,{type:"text",placeholder:"300",className:"disabled:bg-input",...t})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"percentageCharge",control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"mt-2",children:[(0,r.jsx)(o.lX,{children:N("Percentage charge (%)")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(d.I,{type:"text",placeholder:"300",className:"disabled:bg-input",...t})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(m.Z,{}),(0,r.jsxs)("div",{className:"withdraw-method-repeater",children:[(0,r.jsx)("h3",{className:"mb-3",children:N("Method Fields")}),(0,r.jsxs)("div",{className:"controls mb-3 flex gap-2",children:[(0,r.jsxs)(i.z,{variant:"secondary",type:"button",onClick:()=>{n.setValue("params",[...n.watch("params")||[],{name:"",label:"",type:"text",required:!1}])},children:[N("Add"),(0,r.jsx)(h.Z,{size:20})]}),(0,r.jsxs)(i.z,{variant:"destructive",type:"button",onClick:()=>{var e;n.setValue("params",null===(e=n.watch("params"))||void 0===e?void 0:e.slice(0,-1))},children:[N("Remove"),(0,r.jsx)(j.Z,{size:20})]})]}),(0,r.jsx)("div",{className:"params-fields",children:null===(t=n.watch("params"))||void 0===t?void 0:t.map((e,t)=>(0,r.jsxs)("div",{className:"repeater-field-single mb-6 grid grid-cols-4 gap-6",children:[(0,r.jsx)(o.Wi,{name:"params.".concat(t,".name"),control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"mt-2",children:[(0,r.jsx)(o.lX,{children:N("Name (Unique)")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(d.I,{type:"text",placeholder:"Field Name",className:"disabled:bg-input",...t})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"params.".concat(t,".label"),control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"mt-2",children:[(0,r.jsx)(o.lX,{children:N("Label")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(d.I,{type:"text",placeholder:"Field Label",className:"disabled:bg-input",...t})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"params.".concat(t,".type"),control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"mt-2",children:[(0,r.jsx)(o.lX,{children:N("Type")}),(0,r.jsx)(o.NI,{children:(0,r.jsxs)(u.Ph,{defaultValue:t.value,onValueChange:t.onChange,children:[(0,r.jsx)(u.i4,{className:"disabled:bg-input",children:(0,r.jsx)(u.ki,{placeholder:N("Field type")})}),(0,r.jsx)(u.Bw,{children:["text","number"].map(e=>(0,r.jsx)(u.Ql,{value:e,children:e},e))})]})}),(0,r.jsx)(o.zG,{})]})}}),(0,r.jsx)(o.Wi,{name:"params.".concat(t,".required"),control:n.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{className:"space-y-auto flex flex-col gap-2",children:[(0,r.jsx)(c.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:N("Required")}),(0,r.jsx)(o.NI,{children:(0,r.jsx)(f.Z,{defaultChecked:!!t.value,onCheckedChange:t.onChange,className:"disabled:opacity-100"})}),(0,r.jsx)(o.zG,{})]})}})]},t))})]})]})}},52323:function(e,t,n){n.d(t,{g:function(){return f}});var r=n(57437),a=n(2265),s=n(85487),l=n(41062),i=n(23518),o=n(57054),d=n(40593),c=n(94508),u=n(36887),m=n(43949);function f(e){var t,n;let{allCountry:f=!1,defaultValue:x,defaultCountry:p,onSelectChange:h,disabled:j=!1,triggerClassName:g,arrowClassName:v,flagClassName:y,display:b,placeholderClassName:N,align:C="start",side:w="bottom"}=e,{t:z}=(0,m.$G)(),{countries:Z,getCountryByCode:I,isLoading:k}=(0,d.F)(),[P,F]=a.useState(!1),[R,A]=a.useState(x);return a.useEffect(()=>{x&&A(x)},[x]),a.useEffect(()=>{(async()=>{p&&await I(p,e=>{e&&(A(e),h(e))})})()},[p]),(0,r.jsxs)(o.J2,{open:P,onOpenChange:F,children:[(0,r.jsxs)(o.xo,{disabled:j,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",g),children:[R?(0,r.jsx)("div",{className:"flex flex-1 items-center",children:(0,r.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,r.jsx)(l.W,{className:y,countryCode:(null===(t=R.code)||void 0===t?void 0:t.cca2)==="*"?"UN":null===(n=R.code)||void 0===n?void 0:n.cca2}),void 0!==b?b(R):(0,r.jsx)("span",{children:R.name})]})}):(0,r.jsx)("span",{className:(0,c.ZP)("text-placeholder",N),children:z("Select country")}),(0,r.jsx)(u.Z,{className:(0,c.ZP)("size-6",v)})]}),(0,r.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:C,side:w,children:(0,r.jsxs)(i.mY,{children:[(0,r.jsx)(i.sZ,{placeholder:z("Search...")}),(0,r.jsx)(i.e8,{children:(0,r.jsxs)(i.fu,{children:[k&&(0,r.jsx)(s.Loader,{}),f&&(0,r.jsxs)(i.di,{value:z("All countries"),onSelect:()=>{A({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),h({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),F(!1)},children:[(0,r.jsx)(l.W,{countryCode:"UN"}),(0,r.jsx)("span",{className:"pl-1.5",children:z("All countries")})]}),null==Z?void 0:Z.map(e=>"officially-assigned"===e.status?(0,r.jsxs)(i.di,{value:e.name,onSelect:()=>{A(e),h(e),F(!1)},children:[(0,r.jsx)(l.W,{countryCode:e.code.cca2}),(0,r.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},78939:function(e,t,n){n.d(t,{S:function(){return o}});var r=n(57437),a=n(94508),s=n(33145),l=n(2265),i=n(85598);function o(e){let{defaultValue:t,onChange:n,className:o,children:d,disabled:c=!1,id:u}=e,[m,f]=l.useState(t);l.useEffect(()=>{f(t)},[t]);let{getRootProps:x,getInputProps:p}=(0,i.uI)({onDrop:e=>{let t=null==e?void 0:e[0];t&&(n(t),f(URL.createObjectURL(t)))},disabled:c});return(0,r.jsxs)("div",{...x({className:(0,a.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o)}),children:[!!m&&(0,r.jsx)(s.default,{src:m,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,r.jsx)("input",{id:u,...p()}),!m&&(0,r.jsx)("div",{children:d})]})}},25429:function(e,t,n){n.d(t,{X:function(){return s}});var r=n(57437),a=n(94508);function s(e){let{className:t}=e;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,a.ZP)("fill-primary",t),children:[(0,r.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},15681:function(e,t,n){n.d(t,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return p},xJ:function(){return x},zG:function(){return j}});var r=n(57437),a=n(37053),s=n(2265),l=n(29501),i=n(26815),o=n(94508);let d=l.RV,c=s.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(c.Provider,{value:{name:t.name},children:(0,r.jsx)(l.Qr,{...t})})},m=()=>{let e=s.useContext(c),t=s.useContext(f),{getFieldState:n,formState:r}=(0,l.Gc)(),a=n(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...a}},f=s.createContext({}),x=s.forwardRef((e,t)=>{let{className:n,...a}=e,l=s.useId();return(0,r.jsx)(f.Provider,{value:{id:l},children:(0,r.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",n),...a})})});x.displayName="FormItem";let p=s.forwardRef((e,t)=>{let{className:n,required:a,...s}=e,{error:l,formItemId:d}=m();return(0,r.jsx)("span",{children:(0,r.jsx)(i.Z,{ref:t,className:(0,o.ZP)(l&&"text-base font-medium text-destructive",n),htmlFor:d,...s})})});p.displayName="FormLabel";let h=s.forwardRef((e,t)=>{let{...n}=e,{error:s,formItemId:l,formDescriptionId:i,formMessageId:o}=m();return(0,r.jsx)(a.g7,{ref:t,id:l,"aria-describedby":s?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!s,...n})});h.displayName="FormControl",s.forwardRef((e,t)=>{let{className:n,...a}=e,{formDescriptionId:s}=m();return(0,r.jsx)("p",{ref:t,id:s,className:(0,o.ZP)("text-sm text-muted-foreground",n),...a})}).displayName="FormDescription";let j=s.forwardRef((e,t)=>{let{className:n,children:a,...s}=e,{error:l,formMessageId:i}=m(),d=l?String(null==l?void 0:l.message):a;return d?(0,r.jsx)("p",{ref:t,id:i,className:(0,o.ZP)("text-sm font-medium text-destructive",n),...s,children:d}):null});j.displayName="FormMessage"},57054:function(e,t,n){n.d(t,{J2:function(){return i},xo:function(){return o},yk:function(){return d}});var r=n(57437),a=n(2265),s=n(27312),l=n(94508);let i=s.fC,o=s.xz,d=a.forwardRef((e,t)=>{let{className:n,align:a="center",sideOffset:i=4,...o}=e;return(0,r.jsx)(s.h_,{children:(0,r.jsx)(s.VY,{ref:t,align:a,sideOffset:i,className:(0,l.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...o})})});d.displayName=s.VY.displayName},53647:function(e,t,n){n.d(t,{Bw:function(){return h},Ph:function(){return u},Ql:function(){return j},i4:function(){return f},ki:function(){return m}});var r=n(57437),a=n(68856),s=n(22135),l=n(40875),i=n(2265),o=n(94508),d=n(36887),c=n(22291);let u=a.fC;a.ZA;let m=a.B4,f=i.forwardRef((e,t)=>{let{className:n,children:s,...l}=e;return(0,r.jsxs)(a.xz,{ref:t,className:(0,o.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),...l,children:[s,(0,r.jsx)(a.JO,{asChild:!0,children:(0,r.jsx)(d.Z,{size:"24",color:"#292D32"})})]})});f.displayName=a.xz.displayName;let x=i.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,r.jsx)(a.u_,{ref:t,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",n),...l,children:(0,r.jsx)(s.Z,{className:"h-4 w-4"})})});x.displayName=a.u_.displayName;let p=i.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.$G,{ref:t,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",n),...s,children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})});p.displayName=a.$G.displayName;let h=i.forwardRef((e,t)=>{let{className:n,children:s,position:l="popper",...i}=e;return(0,r.jsx)(a.h_,{children:(0,r.jsxs)(a.VY,{ref:t,className:(0,o.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:l,...i,children:[(0,r.jsx)(x,{}),(0,r.jsx)(a.l_,{className:(0,o.ZP)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(p,{})]})})});h.displayName=a.VY.displayName,i.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.__,{ref:t,className:(0,o.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",n),...s})}).displayName=a.__.displayName;let j=i.forwardRef((e,t)=>{let{className:n,children:s,...l}=e;return(0,r.jsxs)(a.ck,{ref:t,className:(0,o.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.wU,{children:(0,r.jsx)(c.Z,{variant:"Bold",className:"h-4 w-4"})})}),(0,r.jsx)(a.eT,{children:s})]})});j.displayName=a.ck.displayName,i.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.Z0,{ref:t,className:(0,o.ZP)("-mx-1 my-1 h-px bg-muted",n),...s})}).displayName=a.Z0.displayName},6512:function(e,t,n){var r=n(57437),a=n(55156),s=n(2265),l=n(94508);let i=s.forwardRef((e,t)=>{let{className:n,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(a.f,{ref:t,decorative:i,orientation:s,className:(0,l.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",n),...o})});i.displayName=a.f.displayName,t.Z=i},1828:function(e,t,n){var r=n(57437),a=n(50721),s=n(2265),l=n(94508);let i=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.fC,{className:(0,l.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",n),...s,ref:t,children:(0,r.jsx)(a.bU,{className:(0,l.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});i.displayName=a.fC.displayName,t.Z=i},97751:function(e,t,n){n.d(t,{B:function(){return a},D:function(){return s}});var r=n(43577);function a(e){var t,n,r;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(r=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==r?r:"",data:null===(n=e.data)||void 0===n?void 0:n.data}}function s(e){let t=500,n="Internal Server Error",a="An unknown error occurred";if((0,r.IZ)(e)){var s,l,i,o,d,c,u,m,f,x,p,h;t=null!==(f=null===(s=e.response)||void 0===s?void 0:s.status)&&void 0!==f?f:500,n=null!==(x=null===(l=e.response)||void 0===l?void 0:l.statusText)&&void 0!==x?x:"Internal Server Error",a=null!==(h=null!==(p=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(i=o[0])||void 0===i?void 0:i.message)&&void 0!==p?p:null===(m=e.response)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:u.message)&&void 0!==h?h:e.message}else e instanceof Error&&(a=e.message);return{statusCode:t,statusText:n,status:!1,message:a,data:void 0,error:e}}},24458:function(e,t,n){n.d(t,{j:function(){return l}});var r=n(28315),a=n(79981),s=n(85323);function l(){let{data:e,isLoading:t,error:n,mutate:l}=(0,s.ZP)("/currencies",e=>a.Z.get(e)),i=null==e?void 0:e.data;return{currencies:i?i.map(e=>new r.F(e)):[],isLoading:t,error:n,mutate:l}}},54995:function(e,t,n){n.d(t,{K:function(){return l},S:function(){return i}});var r=n(31229);let a=["image/jpeg","image/jpg","image/png","image/svg+xml"],s=["image/x-icon","image/vnd.microsoft.icon","image/png"],l=r.z.union([r.z.string(),r.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&a.includes(e.type),"Invalid file format. Please upload a .jpg, .jpeg, .png or .svg file."),i=r.z.union([r.z.string(),r.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&s.includes(e.type),"Invalid file format. Please upload a .ico or .png file.")},30412:function(e,t,n){n.d(t,{L:function(){return s}});var r=n(31229),a=n(54995);let s=r.z.object({uploadLogo:a.K,name:r.z.string({required_error:"Name is required"}),countryCode:r.z.string({required_error:"Country is required"}),currencyCode:r.z.string({required_error:"Currency is required"}),active:r.z.boolean().default(!1),recommended:r.z.boolean().default(!1),minAmount:r.z.string().optional(),maxAmount:r.z.string().optional(),fixedCharge:r.z.string().optional(),percentageCharge:r.z.string().optional(),params:r.z.array(r.z.object({name:r.z.string().refine(e=>!e.includes(" "),"No Spaces in name field!"),label:r.z.string(),type:r.z.string(),required:r.z.boolean()})).optional()})},28315:function(e,t,n){n.d(t,{F:function(){return r}});class r{format(e){let{currencySymbol:t,amountText:n}=this.formatter(e);return"".concat(n," ").concat(t)}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}constructor(e){var t;this.formatter=e=>{var t,n;let r=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),a=null!==(n=null===(t=r.formatToParts(e).find(e=>"currency"===e.type))||void 0===t?void 0:t.value)&&void 0!==n?n:this.code,s=r.format(e),l=s.substring(a.length).trim();return{currencyCode:this.code,currencySymbol:a,formattedAmount:s,amountText:l}},this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.code=null==e?void 0:e.code,this.logo=null!==(t=null==e?void 0:e.logo)&&void 0!==t?t:"",this.usdRate=null==e?void 0:e.usdRate,this.acceptApiRate=!!(null==e?void 0:e.acceptApiRate),this.isCrypto=!!(null==e?void 0:e.isCrypto),this.active=!!(null==e?void 0:e.active),this.metaData=null==e?void 0:e.metaData,this.minAmount=null==e?void 0:e.minAmount,this.kycLimit=null==e?void 0:e.kycLimit,this.maxAmount=null==e?void 0:e.maxAmount,this.dailyTransferAmount=null==e?void 0:e.dailyTransferAmount,this.dailyTransferLimit=null==e?void 0:e.dailyTransferLimit,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}}]);