"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import Label from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/common/form/DatePicker";
import { RecurringTransferFilters as IRecurringTransferFilters, RecurringTransferStatus, TransferFrequency } from "@/types/recurring-transfer";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X, Filter, RotateCcw } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface RecurringTransferFiltersProps {
  filters: IRecurringTransferFilters;
  onFiltersChange: (filters: IRecurringTransferFilters) => void;
}

export function RecurringTransferFilters({ filters, onFiltersChange }: RecurringTransferFiltersProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const statusOptions: { value: RecurringTransferStatus; label: string }[] = [
    { value: 'active', label: 'Active' },
    { value: 'paused', label: 'Paused' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'failed', label: 'Failed' },
  ];

  const typeOptions = [
    { value: 'sent', label: 'Sent' },
    { value: 'received', label: 'Received' },
  ];

  const frequencyOptions: { value: TransferFrequency; label: string }[] = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'yearly', label: 'Yearly' },
  ];

  const updateFilter = (key: keyof IRecurringTransferFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    if (value === '' || value === undefined) {
      delete newFilters[key];
    }
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const getActiveFiltersCount = () => {
    return Object.keys(filters).filter(key => 
      filters[key as keyof IRecurringTransferFilters] !== undefined && 
      filters[key as keyof IRecurringTransferFilters] !== ''
    ).length;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="space-y-4">
      {/* Filter Toggle */}
      <div className="flex items-center justify-between">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <span>{t('Filters')}</span>
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                  {/* Status Filter */}
                  <div className="space-y-2">
                    <Label htmlFor="status">{t('Status')}</Label>
                    <Select
                      value={filters.status || ''}
                      onValueChange={(value) => updateFilter('status', value as RecurringTransferStatus)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('All statuses')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">{t('All statuses')}</SelectItem>
                        {statusOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {t(option.label)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Type Filter */}
                  <div className="space-y-2">
                    <Label htmlFor="type">{t('Type')}</Label>
                    <Select
                      value={filters.type || ''}
                      onValueChange={(value) => updateFilter('type', value as 'sent' | 'received')}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('All types')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">{t('All types')}</SelectItem>
                        {typeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {t(option.label)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Frequency Filter */}
                  <div className="space-y-2">
                    <Label htmlFor="frequency">{t('Frequency')}</Label>
                    <Select
                      value={filters.frequency || ''}
                      onValueChange={(value) => updateFilter('frequency', value as TransferFrequency)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('All frequencies')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">{t('All frequencies')}</SelectItem>
                        {frequencyOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {t(option.label)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Start Date Filter */}
                  <div className="space-y-2">
                    <Label>{t('Start Date')}</Label>
                    <DatePicker
                      date={filters.startDate ? new Date(filters.startDate) : undefined}
                      onDateChange={(date) => 
                        updateFilter('startDate', date?.toISOString().split('T')[0])
                      }
                      placeholder={t('Select start date')}
                    />
                  </div>

                  {/* End Date Filter */}
                  <div className="space-y-2">
                    <Label>{t('End Date')}</Label>
                    <DatePicker
                      date={filters.endDate ? new Date(filters.endDate) : undefined}
                      onDateChange={(date) => 
                        updateFilter('endDate', date?.toISOString().split('T')[0])
                      }
                      placeholder={t('Select end date')}
                    />
                  </div>
                </div>

                {/* Filter Actions */}
                <div className="flex items-center justify-between mt-6 pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    {activeFiltersCount > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearFilters}
                        className="flex items-center space-x-1"
                      >
                        <RotateCcw className="h-3 w-3" />
                        <span>{t('Clear All')}</span>
                      </Button>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground">
                    {activeFiltersCount > 0 
                      ? t('{{count}} filter(s) applied', { count: activeFiltersCount })
                      : t('No filters applied')
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </CollapsibleContent>
        </Collapsible>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.status && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('Status')}: {t(filters.status)}</span>
              <button
                onClick={() => updateFilter('status', undefined)}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.type && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('Type')}: {t(filters.type)}</span>
              <button
                onClick={() => updateFilter('type', undefined)}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.frequency && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('Frequency')}: {t(filters.frequency)}</span>
              <button
                onClick={() => updateFilter('frequency', undefined)}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.startDate && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('From')}: {new Date(filters.startDate).toLocaleDateString()}</span>
              <button
                onClick={() => updateFilter('startDate', undefined)}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.endDate && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <span>{t('To')}: {new Date(filters.endDate).toLocaleDateString()}</span>
              <button
                onClick={() => updateFilter('endDate', undefined)}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
