exports.id=4250,exports.ids=[4250],exports.modules={26807:(e,t,r)=>{Promise.resolve().then(r.bind(r,73e3)),Promise.resolve().then(r.bind(r,75285))},35303:()=>{},75285:(e,t,r)=>{"use strict";r.d(t,{default:()=>C});var n=r(10326),s=r(5158),a=r(90772),i=r(81638),l=r(6216),o=r(90434),c=r(35047),d=r(17577);function m({sidebarItem:e}){let[t,r]=d.useState("(dashboard)"),[m,u]=d.useState(!1),{setIsExpanded:f,device:h}=(0,i.q)(),p=(0,c.useSelectedLayoutSegment)();return d.useEffect(()=>{r(p)},[]),d.useEffect(()=>{u(e.segment===p)},[p,e.segment]),(0,n.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,n.jsxs)(o.default,{href:e.link,onClick:()=>{r(e.segment),e.children?.length||"Desktop"===h||f(!1)},"data-active":p===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[n.jsx(s.J,{condition:!!e.icon,children:n.jsx("div",{"data-active":p===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),n.jsx("span",{className:"flex-1",children:e.name}),n.jsx(s.J,{condition:!!e.children?.length,children:n.jsx(a.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:n.jsx(l.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),n.jsx(s.J,{condition:!!e.children?.length,children:n.jsx("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>n.jsx("li",{children:n.jsxs(o.default,{href:e.link,"data-active":t===e.segment,onClick:()=>{r(e.segment),"Desktop"!==h&&f(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[n.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=r(8281),f=r(4066),h=r(77863),p=r(1178),g=r(29169),x=r(40420),k=r(78564),y=r(53105),v=r(81770),b=r(45922),w=r(29764),j=r(26920),E=r(9155),N=r(41334),z=r(73686),L=r(75073),M=r(44221),P=r(46226),Z=r(70012);function C(){let{t:e}=(0,Z.$G)(),{isExpanded:t,setIsExpanded:r}=(0,i.q)(),{logo:s,siteName:l}=(0,f.T)(),c=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:n.jsx(p.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:n.jsx(g.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:n.jsx(x.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:n.jsx(k.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:n.jsx(y.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:n.jsx(v.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:n.jsx(b.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:n.jsx(w.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:n.jsx(j.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:n.jsx(E.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:n.jsx(N.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:n.jsx(z.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:n.jsx(L.Z,{size:"20"}),link:"/settings"}]}];return(0,n.jsxs)("div",{"data-expanded":t,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[n.jsx(a.z,{size:"icon",variant:"outline",onClick:()=>r(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${t?"":"hidden"} lg:hidden`,children:n.jsx(M.Z,{})}),n.jsx("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:n.jsx(o.default,{href:"/",className:"flex items-center justify-center",children:n.jsx(P.default,{src:(0,h.qR)(s),width:160,height:40,alt:l,className:"max-h-10 object-contain"})})}),n.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:c.map(e=>(0,n.jsxs)("div",{children:[""!==e.title?n.jsx("div",{children:n.jsx(u.Z,{className:"my-4"})}):null,n.jsx("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>n.jsx("li",{children:n.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},53313:(e,t,r)=>{"use strict";r.d(t,{X:()=>o});var n=r(10326),s=r(13635),a=r(32933),i=r(17577),l=r(77863);let o=i.forwardRef(({className:e,...t},r)=>n.jsx(s.fC,{ref:r,className:(0,l.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:n.jsx(s.z$,{className:(0,l.ZP)("flex items-center justify-center text-current"),children:n.jsx(a.Z,{className:"h-4 w-4"})})}));o.displayName=s.fC.displayName},55632:(e,t,r)=>{"use strict";r.d(t,{NI:()=>g,Wi:()=>m,l0:()=>c,lX:()=>p,xJ:()=>h,zG:()=>x});var n=r(10326),s=r(34214),a=r(17577),i=r(74723),l=r(31048),o=r(77863);let c=i.RV,d=a.createContext({}),m=({...e})=>n.jsx(d.Provider,{value:{name:e.name},children:n.jsx(i.Qr,{...e})}),u=()=>{let e=a.useContext(d),t=a.useContext(f),{getFieldState:r,formState:n}=(0,i.Gc)(),s=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...s}},f=a.createContext({}),h=a.forwardRef(({className:e,...t},r)=>{let s=a.useId();return n.jsx(f.Provider,{value:{id:s},children:n.jsx("div",{ref:r,className:(0,o.ZP)("space-y-2",e),...t})})});h.displayName="FormItem";let p=a.forwardRef(({className:e,required:t,...r},s)=>{let{error:a,formItemId:i}=u();return n.jsx("span",{children:n.jsx(l.Z,{ref:s,className:(0,o.ZP)(a&&"text-base font-medium text-destructive",e),htmlFor:i,...r})})});p.displayName="FormLabel";let g=a.forwardRef(({...e},t)=>{let{error:r,formItemId:a,formDescriptionId:i,formMessageId:l}=u();return n.jsx(s.g7,{ref:t,id:a,"aria-describedby":r?`${i} ${l}`:`${i}`,"aria-invalid":!!r,...e})});g.displayName="FormControl",a.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:s}=u();return n.jsx("p",{ref:r,id:s,className:(0,o.ZP)("text-sm text-muted-foreground",e),...t})}).displayName="FormDescription";let x=a.forwardRef(({className:e,children:t,...r},s)=>{let{error:a,formMessageId:i}=u(),l=a?String(a?.message):t;return l?n.jsx("p",{ref:s,id:i,className:(0,o.ZP)("text-sm font-medium text-destructive",e),...r,children:l}):null});x.displayName="FormMessage"},54432:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=r(10326),s=r(17577),a=r(77863);let i=s.forwardRef(({className:e,type:t,...r},s)=>n.jsx("input",{type:t,className:(0,a.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...r}));i.displayName="Input"},31048:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d});var n=r(10326),s=r(34478),a=r(79360),i=r(17577),l=r(77863);let o=(0,a.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...t},r)=>n.jsx(s.f,{ref:r,className:(0,l.ZP)(o(),e),...t}));c.displayName=s.f.displayName;let d=c},87673:(e,t,r)=>{"use strict";r.d(t,{g:()=>i});var n=r(10326),s=r(17577),a=r(77863);let i=s.forwardRef(({className:e,...t},r)=>n.jsx("textarea",{className:(0,a.ZP)("placeholder:text-placeholder flex min-h-[80px] w-full rounded-md border border-input bg-input px-3 py-2 text-base ring-offset-background placeholder:font-normal focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));i.displayName="Textarea"},1296:(e,t,r)=>{"use strict";r.d(t,{Y:()=>a});var n=r(49547),s=r(10734);async function a(e){try{let t=await n.Z.post("/admin/users/send-bulk-email",e);return(0,s.B)(t)}catch(e){return(0,s.D)(e)}}},75584:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var n=r(90799),s=r(35047);function a(e,t){let r=(0,s.usePathname)(),a=(0,s.useSearchParams)(),i=(0,s.useRouter)(),[l,o]=e.split("?"),c=new URLSearchParams(o);c.has("page")||c.set("page","1"),c.has("limit")||c.set("limit","10");let d=`${l}?${c.toString()}`,{data:m,error:u,isLoading:f,mutate:h,...p}=(0,n.d)(d,t);return{refresh:()=>h(m),data:m?.data?.data??[],meta:m?.data?.meta,filter:(e,t,n)=>{let s=new URLSearchParams(a.toString());t?s.set(e,t.toString()):s.delete(e),i.replace(`${r}?${s.toString()}`),n?.()},isLoading:f,error:u,...p}}},75817:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var n=r(52920),s=r(17577),a=r.n(s),i=r(78439),l=r.n(i),o=["variant","color","size"],c=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2zM18 12.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},d=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M12.82 12H3.5M20.33 12h-3.48"}))},m=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{fill:t,d:"M7.81 2h8.37C19.83 2 22 4.17 22 7.81v8.37c0 3.64-2.17 5.81-5.81 5.81H7.81C4.17 22 2 19.83 2 16.19V7.81C2 4.17 4.17 2 7.81 2z",opacity:".4"}),a().createElement("path",{fill:t,d:"M5.47 11.47l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06z"}))},u=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M20.5 12H3.67"}))},f=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{fill:t,d:"M9.57 18.82c-.19 0-.38-.07-.53-.22l-6.07-6.07a.754.754 0 010-1.06L9.04 5.4c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L4.56 12l5.54 5.54c.29.29.29.77 0 1.06-.14.15-.34.22-.53.22z"}),a().createElement("path",{fill:t,d:"M20.5 12.75H3.67c-.41 0-.75-.34-.75-.75s.34-.75.75-.75H20.5c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},h=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07"}),a().createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M20.5 12H3.67",opacity:".4"}))},p=function(e,t){switch(e){case"Bold":return a().createElement(c,{color:t});case"Broken":return a().createElement(d,{color:t});case"Bulk":return a().createElement(m,{color:t});case"Linear":default:return a().createElement(u,{color:t});case"Outline":return a().createElement(f,{color:t});case"TwoTone":return a().createElement(h,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,s=e.color,i=e.size,l=(0,n._)(e,o);return a().createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(r,s))});g.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="ArrowLeft"},44284:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var n=r(52920),s=r(17577),a=r.n(s),i=r(78439),l=r.n(i),o=["variant","color","size"],c=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{fill:t,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},d=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},m=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{fill:t,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),a().createElement("path",{fill:t,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},u=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},f=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{fill:t,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},h=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},p=function(e,t){switch(e){case"Bold":return a().createElement(c,{color:t});case"Broken":return a().createElement(d,{color:t});case"Bulk":return a().createElement(m,{color:t});case"Linear":default:return a().createElement(u,{color:t});case"Outline":return a().createElement(f,{color:t});case"TwoTone":return a().createElement(h,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,s=e.color,i=e.size,l=(0,n._)(e,o);return a().createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(r,s))});g.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="ArrowRight2"},11840:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(19510),s=r(40099);let a=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function i({children:e}){return(0,n.jsxs)("div",{className:"flex h-screen",children:[n.jsx(a,{}),(0,n.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[n.jsx(s.Z,{}),n.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},33661:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(19510),s=r(48413);function a(){return n.jsx("div",{className:"flex items-center justify-center py-10",children:n.jsx(s.a,{})})}},13635:(e,t,r)=>{"use strict";r.d(t,{fC:()=>E,z$:()=>N});var n=r(17577),s=r(48051),a=r(93095),i=r(82561),l=r(52067),o=r(53405),c=r(2566),d=r(9815),m=r(45226),u=r(10326),f="Checkbox",[h,p]=(0,a.b)(f),[g,x]=h(f),k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:o,defaultChecked:c,required:d,disabled:h,value:p="on",onCheckedChange:x,form:k,...y}=e,[v,E]=n.useState(null),N=(0,s.e)(t,e=>E(e)),z=n.useRef(!1),L=!v||k||!!v.closest("form"),[M,P]=(0,l.T)({prop:o,defaultProp:c??!1,onChange:x,caller:f}),Z=n.useRef(M);return n.useEffect(()=>{let e=v?.form;if(e){let t=()=>P(Z.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[v,P]),(0,u.jsxs)(g,{scope:r,state:M,disabled:h,children:[(0,u.jsx)(m.WV.button,{type:"button",role:"checkbox","aria-checked":w(M)?"mixed":M,"aria-required":d,"data-state":j(M),"data-disabled":h?"":void 0,disabled:h,value:p,...y,ref:N,onKeyDown:(0,i.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(e.onClick,e=>{P(e=>!!w(e)||!e),L&&(z.current=e.isPropagationStopped(),z.current||e.stopPropagation())})}),L&&(0,u.jsx)(b,{control:v,bubbles:!z.current,name:a,value:p,checked:M,required:d,disabled:h,form:k,style:{transform:"translateX(-100%)"},defaultChecked:!w(c)&&c})]})});k.displayName=f;var y="CheckboxIndicator",v=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...s}=e,a=x(y,r);return(0,u.jsx)(d.z,{present:n||w(a.state)||!0===a.state,children:(0,u.jsx)(m.WV.span,{"data-state":j(a.state),"data-disabled":a.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});v.displayName=y;var b=n.forwardRef(({__scopeCheckbox:e,control:t,checked:r,bubbles:a=!0,defaultChecked:i,...l},d)=>{let f=n.useRef(null),h=(0,s.e)(f,d),p=(0,o.D)(r),g=(0,c.t)(t);n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let n=new Event("click",{bubbles:a});e.indeterminate=w(r),t.call(e,!w(r)&&r),e.dispatchEvent(n)}},[p,r,a]);let x=n.useRef(!w(r)&&r);return(0,u.jsx)(m.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:i??x.current,...l,tabIndex:-1,ref:h,style:{...l.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return"indeterminate"===e}function j(e){return w(e)?"indeterminate":e?"checked":"unchecked"}b.displayName="CheckboxBubbleInput";var E=k,N=v},53405:(e,t,r)=>{"use strict";r.d(t,{D:()=>s});var n=r(17577);function s(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};