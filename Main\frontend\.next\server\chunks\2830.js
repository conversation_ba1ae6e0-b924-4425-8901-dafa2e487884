exports.id=2830,exports.ids=[2830],exports.modules={59106:(e,s,a)=>{Promise.resolve().then(a.bind(a,22874))},50477:(e,s,a)=>{Promise.resolve().then(a.bind(a,22874))},22874:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>P});var r=a(10326),t=a(63761),l=a(90772),d=a(33071),n=a(54432),i=a(19395),o=a(4066),c=a(90799),m=a(77863),f=a(23926),u=a(7310),x=a(35047),p=a(17577),h=a.n(p),g=a(70012),j=a(56140),b=a(19875),N=a(64561),v=a(46226);let w=new m.F;function y({referralUsers:e,isLoading:s}){let[a,t]=h().useState([]),{t:l}=(0,g.$G)();return r.jsx(j.Z,{data:e,sorting:a,setSorting:t,isLoading:s,structure:[{id:"name",header:l("Name"),cell:({row:e})=>{let s=e?.original;return(0,r.jsxs)("div",{className:"flex min-w-28 items-center gap-2.5 font-normal",children:[s?.customer?.profileImage?r.jsx(v.default,{src:(0,m.qR)(s?.customer?.profileImage),alt:s?.customer?.name,className:"size-8 rounded-full",width:32,height:32}):r.jsx("div",{className:"flex size-9 items-center justify-center rounded-full bg-muted",children:r.jsx(b.Z,{size:"16",variant:"Bold",className:"text-secondary-text"})}),r.jsx("span",{children:s?.customer?.name})]})}},{id:"contact_number",header:l("Contact Number"),cell:({row:e})=>{let s=e?.original,a=(0,N.h)((0,m.Fg)(s?.customer?.phone));return r.jsx("span",{className:"block min-w-32 font-normal text-secondary-text",children:a.formatInternational()})}},{id:"email",header:l("Email"),cell:({row:e})=>{let s=e?.original;return r.jsx("span",{className:"font-normal text-secondary-text",children:s?.email})}},{id:"totalBonus",header:l("Total bonus"),cell:({row:e})=>{let s=e?.original;return r.jsx("span",{className:"font-normal text-secondary-text",children:w.format(s?.totalBonus)})}}]})}let Z=new m.F;function P(){let{referral:e}=(0,o.T)(),s=(0,x.useSearchParams)(),{auth:a,isLoading:p}=(0,i.a)(),[j,b]=h().useState(""),N=(0,x.usePathname)(),v=(0,x.useRouter)(),{data:w,isLoading:P}=(0,c.d)(`/customers/referred-users?${s.toString()}`),{t:R}=(0,g.$G)();return(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 p-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row",children:[(0,r.jsxs)("div",{className:"inline-flex w-full items-center gap-4 rounded-xl border border-border bg-background p-6 md:w-1/3",children:[r.jsx("div",{className:"flex size-14 items-center justify-center rounded-full bg-muted",children:r.jsx(f.Z,{size:32})}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[r.jsx("h1",{className:"text-[32px] font-semibold leading-8",children:Z.format(Number(e?.bonusAmount))}),r.jsx("p",{className:"text-base font-medium leading-[22px]",children:R("Total Earnings")})]})]}),(0,r.jsxs)("div",{className:"w-full rounded-xl border border-border bg-background px-4 py-0",children:[r.jsx("div",{className:"py-6 hover:no-underline",children:r.jsx("p",{className:"text-base font-medium leading-[22px]",children:R("Refer a friend")})}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 border-t border-divider px-1 py-4 sm:flex-row",children:[r.jsx(n.I,{value:p?R("Loading..."):a?.getReferralLink(),className:"h-10",readOnly:!0,disabled:!0}),(0,r.jsxs)(l.z,{type:"button",onClick:()=>(0,m.Fp)(a?.getReferralLink()),className:"w-full rounded-lg sm:max-w-[302px]",children:[r.jsx(u.Z,{}),r.jsx("span",{children:R("Copy link")})]})]})]})]}),(0,r.jsxs)(d.Zb,{className:"rounded-xl",children:[(0,r.jsxs)(d.Ol,{className:"flex items-start py-4 sm:flex-row sm:items-center",children:[r.jsx(d.ll,{className:"flex-1 text-base font-medium leading-[22px]",children:R("Referrals")}),r.jsx(t.R,{value:j,onChange:e=>{e.preventDefault();let s=(0,m.w4)(e.target.value);b(e.target.value),v.replace(`${N}?${s.toString()}`)},iconPlacement:"end",placeholder:R("Search..."),className:"h-10 rounded-lg",containerClass:"w-full sm:w-[280px]"})]}),r.jsx(d.aY,{className:"border-t border-divider py-4",children:r.jsx(y,{referralUsers:w?.data?.referralUsers,isLoading:P})})]})]})}},56140:(e,s,a)=>{"use strict";a.d(s,{Z:()=>w});var r=a(10326),t=a(77863),l=a(86508),d=a(11798),n=a(77132),i=a(6216),o=a(75817),c=a(40420),m=a(35047),f=a(93327),u=a(17577),x=a(70012),p=a(90772);let h=u.forwardRef(({className:e,...s},a)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:a,className:(0,t.ZP)("w-full caption-bottom text-sm",e),...s})}));h.displayName="Table";let g=u.forwardRef(({className:e,...s},a)=>r.jsx("thead",{ref:a,className:(0,t.ZP)("",e),...s}));g.displayName="TableHeader";let j=u.forwardRef(({className:e,...s},a)=>r.jsx("tbody",{ref:a,className:(0,t.ZP)("[&_tr:last-child]:border-0",e),...s}));j.displayName="TableBody",u.forwardRef(({className:e,...s},a)=>r.jsx("tfoot",{ref:a,className:(0,t.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let b=u.forwardRef(({className:e,...s},a)=>r.jsx("tr",{ref:a,className:(0,t.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));b.displayName="TableRow";let N=u.forwardRef(({className:e,...s},a)=>r.jsx("th",{ref:a,className:(0,t.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));N.displayName="TableHead";let v=u.forwardRef(({className:e,...s},a)=>r.jsx("td",{ref:a,className:(0,t.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));function w({data:e,isLoading:s=!1,structure:a,sorting:w,setSorting:y,padding:Z=!1,className:P,onRefresh:R,pagination:S}){let C=(0,u.useMemo)(()=>a,[a]),k=(0,m.useRouter)(),z=(0,m.usePathname)(),T=(0,m.useSearchParams)(),{t:I}=(0,x.$G)(),L=(0,l.b7)({data:e||[],columns:C,state:{sorting:w,onRefresh:R},onSortingChange:y,getCoreRowModel:(0,d.sC)(),getSortedRowModel:(0,d.tj)(),debugTable:!1});return s?r.jsx("div",{className:"rounded-md bg-background p-10",children:r.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:I("Loading...")})}):e?.length?(0,r.jsxs)("div",{className:(0,t.ZP)(`${Z?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,P),children:[(0,r.jsxs)(h,{children:[r.jsx(g,{children:L.getHeaderGroups().map(e=>r.jsx(b,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>r.jsx(N,{className:(0,t.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,r.jsxs)(p.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[I((0,l.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:r.jsx(i.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:r.jsx(i.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??r.jsx(i.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),r.jsx(j,{children:L.getRowModel().rows.map(e=>r.jsx(b,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>r.jsx(v,{className:"py-3 text-sm font-semibold",children:(0,l.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),S&&S.total>10&&r.jsx("div",{className:"pb-2 pt-6",children:r.jsx(f.Z,{showTotal:(e,s)=>I("Showing {{start}}-{{end}} of {{total}}",{start:s[0],end:s[1],total:e}),align:"start",current:S?.page,total:S?.total,pageSize:S?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let s=new URLSearchParams(T);s.set("page",e.toString()),k.push(`${z}?${s.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>r.jsx("a",{...e,children:r.jsx(o.Z,{size:"18"})}),nextIcon:e=>r.jsx("a",{...e,children:r.jsx(c.Z,{size:"18"})})})})]}):r.jsx("div",{className:"rounded-md bg-background p-10",children:(0,r.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[r.jsx(n.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),I("No data found!")]})})}v.displayName="TableCell",u.forwardRef(({className:e,...s},a)=>r.jsx("caption",{ref:a,className:(0,t.ZP)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},63761:(e,s,a)=>{"use strict";a.d(s,{R:()=>n});var r=a(10326);a(17577);var t=a(54432),l=a(77863),d=a(32894);function n({iconPlacement:e="start",className:s,containerClass:a,...n}){return(0,r.jsxs)("div",{className:(0,l.ZP)("relative flex items-center",a),children:[r.jsx(d.Z,{size:"20",className:(0,l.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),r.jsx(t.I,{type:"text",className:(0,l.ZP)("h-10","end"===e?"pr-10":"pl-10",s),...n})]})}},33071:(e,s,a)=>{"use strict";a.d(s,{Ol:()=>n,SZ:()=>o,Zb:()=>d,aY:()=>c,eW:()=>m,ll:()=>i});var r=a(10326),t=a(17577),l=a(77863);let d=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,l.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));d.displayName="Card";let n=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,l.ZP)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let i=t.forwardRef(({className:e,...s},a)=>r.jsx("h3",{ref:a,className:(0,l.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let o=t.forwardRef(({className:e,...s},a)=>r.jsx("p",{ref:a,className:(0,l.ZP)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,l.ZP)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,l.ZP)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},54432:(e,s,a)=>{"use strict";a.d(s,{I:()=>d});var r=a(10326),t=a(17577),l=a(77863);let d=t.forwardRef(({className:e,type:s,...a},t)=>r.jsx("input",{type:s,className:(0,l.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:t,...a}));d.displayName="Input"},84514:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var r=a(19510),t=a(40099),l=a(76609);function d({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(l.Z,{userRole:"agent"}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(t.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}a(71159)},18406:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var r=a(19510),t=a(48413);function l(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(t.a,{})})}},77509:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var r=a(19510),t=a(18940);function l(){return r.jsx(t.default,{})}},88728:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var r=a(19510),t=a(40099),l=a(76609);function d({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(l.Z,{userRole:"customer"}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(t.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}a(71159)},80549:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var r=a(19510),t=a(48413);function l(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(t.a,{})})}},86749:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var r=a(19510),t=a(48413);function l(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(t.a,{})})}},18940:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\referral\page.tsx#default`)},76597:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var r=a(19510),t=a(18940);function l(){return r.jsx(t.default,{})}}};