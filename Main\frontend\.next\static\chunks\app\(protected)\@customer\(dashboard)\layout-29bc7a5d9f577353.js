(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22772],{85106:function(e,t,r){Promise.resolve().then(r.bind(r,39941))},39941:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return P}});var n=r(57437),l=r(41709),a=r(85539),s=r(3244),c=r(85487),o=r(96613),i=r(80933),d=r(16831),u=r(35974),m=r(62869),f=r(17814),x=r(19378),h=r(6512),p=r(93022),v=r(22788),j=r(11564),g=r(3612),k=r(48358),w=r(79981),b=r(94508),y=r(59532),N=r(22291),E=r(58926),L=r(8877),z=r(90433),M=r(43271),C=r(27648),S=r(2265),O=r(43949),R=r(14438),F=r(85323),B=r(19060),T=r(26815);function Z(e){let{id:t,checked:r=!1,onSelect:l,name:a,avatar:s,email:c}=e,{t:o}=(0,O.$G)();return(0,n.jsxs)("div",{className:"inline-flex w-full items-center justify-between gap-2.5",children:[(0,n.jsxs)("div",{className:"inline-flex gap-2.5",children:[(0,n.jsxs)(d.qE,{className:"h-8 w-8",children:[(0,n.jsx)(d.F$,{src:s,alt:a}),(0,n.jsx)(d.Q5,{children:(0,y.v)(a)})]}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"text-sm text-foreground",children:a}),(0,n.jsx)("p",{className:"text-xs",children:c})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,n.jsx)(B.X,{id:t,className:"size-4 border-foreground/40 hover:border-primary data-[state=checked]:border-primary",checked:!!r,onCheckedChange:()=>l(t)}),(0,n.jsx)(T.Z,{htmlFor:t,className:"cursor-pointer p-0 text-sm font-normal leading-5 text-foreground",children:o(r?"Added":"Add")})]})]})}function P(e){var t,r,B;let{tableSlot:T}=e,[P,_]=S.useState(""),{auth:W,isLoading:q}=(0,g.a)(),{wallets:D,isLoading:I,mutate:Q}=(0,k.r)(),{t:A}=(0,O.$G)(),{contacts:V,isLoading:$,mutate:K}=(0,j.t)("/contacts?search=".concat(P)),{data:X}=(0,F.ZP)("/customers/referred-users",e=>w.Z.get(e)),Y=e=>Array.isArray(e)?e.filter(e=>!!e.quickSend):[];console.log(D);let G=(e,t)=>{var r;(null===(r=Y(V))||void 0===r?void 0:r.length)>3&&"add"===t?R.toast.error(A("You already added 4 contact into quick send")):R.toast.promise((0,v.M)(e,t),{loading:A("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return K(),e.message},error:e=>e.message})};return console.log(D),(0,n.jsxs)("main",{className:"p-4",children:[(0,n.jsxs)("div",{className:"mb-4 flex flex-wrap items-end gap-y-4 md:gap-4",children:[(0,n.jsx)(l.J,{condition:q,children:(0,n.jsx)(p.O,{className:"h-[200px] w-[350px]"})}),(0,n.jsx)(s.X,{isVerified:!!(null==W?void 0:W.getKYCStatus()),documentStatus:(null==W?void 0:W.kyc)?"submitted":"not submitted"}),I?(0,n.jsx)(p.O,{className:"h-[200px] w-[350px]"}):null==D?void 0:D.map(e=>{var t;return e.pinDashboard&&(0,n.jsx)(o.P,{title:null==e?void 0:e.currency.code,balance:e.balance,currency:null==e?void 0:e.currency.code,walletId:e.id,card:null==e?void 0:null===(t=e.cards)||void 0===t?void 0:t[0],onMutate:Q},e.id)}),(0,n.jsxs)(C.default,{href:"/wallets",prefetch:!1,className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-700 transition duration-300 ease-out hover:text-primary hover:underline",children:[(0,n.jsx)("span",{children:A("Show all wallets")}),(0,n.jsx)(N.Z,{size:12})]})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-0 gap-y-4 md:gap-4 xl:flex-row",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)(S.Suspense,{children:T})}),(0,n.jsxs)("div",{className:"flex w-full flex-wrap gap-4 md:flex-row xl:max-w-[350px] xl:flex-col",children:[(0,n.jsxs)(f.dy,{direction:"right",children:[(0,n.jsxs)("div",{className:"w-full rounded-xl bg-background p-6 shadow-default",children:[(0,n.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,n.jsx)("p",{className:"font-semibold text-foreground",children:A("Quick Send")}),(0,n.jsx)(f.Qz,{children:(0,n.jsx)(m.z,{type:"button",size:"sm",variant:"ghost",children:(0,n.jsx)(E.Z,{size:20})})})]}),(0,n.jsxs)("div",{className:"flex items-center gap-[15px]",children:[$&&[void 0,void 0,void 0,void 0].map((e,t)=>(0,n.jsx)(p.O,{className:"size-10 rounded-full sm:size-12"},t)),V?null===(t=Y(V))||void 0===t?void 0:t.map(e=>{var t;return(0,n.jsx)(C.default,{href:"/transfer?email=".concat(null==e?void 0:null===(t=e.contact)||void 0===t?void 0:t.email),children:(0,n.jsxs)(d.qE,{className:"size-10 sm:size-12",children:[(0,n.jsx)(d.F$,{src:(0,b.qR)(e.contact.customer.profileImage),alt:e.contact.customer.name}),(0,n.jsx)(d.Q5,{children:(0,y.v)(e.contact.customer.name)})]})},e.id)}):null,(0,n.jsx)(f.Qz,{className:"flex size-10 items-center justify-center rounded-full border-2 border-btn-outline-border p-0 sm:size-12",children:(0,n.jsx)(L.Z,{size:20})})]})]}),(0,n.jsxs)(f.sc,{className:"inset-x-auto inset-y-0 bottom-auto left-auto right-0 top-0 m-0 flex h-full w-[95%] flex-col rounded-t-none bg-white px-0 py-8 md:w-[400px]",children:[(0,n.jsx)(f.iI,{className:"flex items-center justify-between gap-4 px-6",children:(0,n.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,n.jsx)(m.z,{variant:"outline",size:"icon",className:"h-10 w-10 rounded-lg",asChild:!0,children:(0,n.jsx)(f.uh,{children:(0,n.jsx)(z.Z,{})})}),(0,n.jsx)("span",{className:"inline-block text-base font-semibold leading-[22px]",children:A("Quick Send")})]})}),(0,n.jsx)(f.u6,{className:"hidden"}),(0,n.jsxs)("div",{className:"flex h-full w-full flex-1 flex-col p-0",children:[(0,n.jsx)("div",{className:"flex flex-col px-6 py-4",children:(0,n.jsx)(a.R,{value:P,onChange:e=>_(e.target.value),iconPlacement:"end",className:"h-10 rounded-lg bg-accent",placeholder:A("Search...")})}),(0,n.jsxs)(x.x,{className:"flex-1 px-6 pb-8",children:[(0,n.jsxs)("div",{className:"flex w-full flex-col items-stretch gap-4",children:[$&&(0,n.jsx)(c.Loader,{className:"mx-2"}),null==V?void 0:V.map(e=>e.quickSend?(0,n.jsx)(Z,{id:e.id,name:e.contact.customer.name,email:e.contact.email,checked:!0,onSelect:e=>G(e,"remove")},e.id):null)]}),(0,n.jsx)(h.Z,{className:"my-4"}),(0,n.jsxs)("div",{className:"flex w-full flex-col items-stretch gap-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-y-2.5",children:[(0,n.jsx)("h5",{className:"text-base font-semibold leading-[22px] text-foreground",children:A("Contacts")}),(0,n.jsx)("p",{className:"text-xs font-normal text-secondary-text",children:A("Select up to 5 contacts to add them in the Quick Send list.")})]}),$&&(0,n.jsx)(c.Loader,{className:"mx-2"}),null==V?void 0:V.map(e=>e.quickSend?null:(0,n.jsx)(Z,{id:e.id,name:e.contact.customer.name,email:e.contact.email,onSelect:e=>G(e,"add")},e.id))]})]})]})]})]}),(0,n.jsx)(i.Y,{}),(0,n.jsxs)("div",{className:"w-full rounded-xl bg-background p-6 shadow-default",children:[(0,n.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,n.jsx)("p",{className:"font-semibold text-foreground",children:A("Refer a friend")}),(0,n.jsx)(u.C,{variant:"secondary",className:"flex h-6 w-6 items-center justify-center bg-muted",children:(null==X?void 0:null===(B=X.data)||void 0===B?void 0:null===(r=B.referralUsers)||void 0===r?void 0:r.length)?X.data.referralUsers.length:"0"})]}),(0,n.jsx)("p",{className:"mb-6 text-sm text-secondary-text",children:A("Share this referral link to your friends and earn money.")}),(0,n.jsx)("div",{className:"mb-2 line-clamp-1 flex h-12 w-full items-center text-ellipsis whitespace-nowrap rounded-[8px] bg-input px-3",children:null==W?void 0:W.getReferralLink()}),(0,n.jsxs)(m.z,{className:"w-full",onClick:()=>{var e;return(0,b.Fp)(null!==(e=null==W?void 0:W.getReferralLink())&&void 0!==e?e:"")},children:[(0,n.jsx)(M.Z,{size:"24"}),(0,n.jsx)("span",{children:A("Copy link")})]})]})]})]})]})}},90433:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var n=r(74677),l=r(2265),a=r(40718),s=r.n(a),c=["variant","color","size"],o=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M13.978 5.319l-3.21 3.21-1.97 1.96a2.13 2.13 0 000 3.01l5.18 5.18c.68.68 1.84.19 1.84-.76V6.079c0-.96-1.16-1.44-1.84-.76z"}))},i=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M11.19 7.94l-2.62 2.62c-.77.77-.77 2.03 0 2.8l6.52 6.52M15.09 4.04l-1.04 1.04"}))},d=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M10.77 8.52l5.05 3.79v5.61c0 .96-1.16 1.44-1.84.76L8.8 13.51a2.13 2.13 0 010-3.01l1.97-1.98z",opacity:".4"}),l.createElement("path",{fill:t,d:"M15.82 6.08v6.23l-5.05-3.79 3.21-3.21c.68-.67 1.84-.19 1.84.77z"}))},u=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M15 19.92L8.48 13.4c-.77-.77-.77-2.03 0-2.8L15 4.08"}))},m=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M15 20.67c-.19 0-.38-.07-.53-.22l-6.52-6.52a2.74 2.74 0 010-3.86l6.52-6.52c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.52 6.52c-.48.48-.48 1.26 0 1.74l6.52 6.52c.29.29.29.77 0 1.06-.15.14-.34.22-.53.22z"}))},f=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M15 19.92L8.48 13.4c-.77-.77-.77-2.03 0-2.8L15 4.08"}))},x=function(e,t){switch(e){case"Bold":return l.createElement(o,{color:t});case"Broken":return l.createElement(i,{color:t});case"Bulk":return l.createElement(d,{color:t});case"Linear":default:return l.createElement(u,{color:t});case"Outline":return l.createElement(m,{color:t});case"TwoTone":return l.createElement(f,{color:t})}},h=(0,l.forwardRef)(function(e,t){var r=e.variant,a=e.color,s=e.size,o=(0,n._)(e,c);return l.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),x(r,a))});h.propTypes={variant:s().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:s().string,size:s().oneOfType([s().string,s().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="ArrowLeft2"},22291:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var n=r(74677),l=r(2265),a=r(40718),s=r.n(a),c=["variant","color","size"],o=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},i=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},d=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),l.createElement("path",{fill:t,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},u=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},m=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},f=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},x=function(e,t){switch(e){case"Bold":return l.createElement(o,{color:t});case"Broken":return l.createElement(i,{color:t});case"Bulk":return l.createElement(d,{color:t});case"Linear":default:return l.createElement(u,{color:t});case"Outline":return l.createElement(m,{color:t});case"TwoTone":return l.createElement(f,{color:t})}},h=(0,l.forwardRef)(function(e,t){var r=e.variant,a=e.color,s=e.size,o=(0,n._)(e,c);return l.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),x(r,a))});h.propTypes={variant:s().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:s().string,size:s().oneOfType([s().string,s().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="ArrowRight2"},30401:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25523:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(47043)._(r(2265)).default.createContext(null)},62484:function(e,t,r){"use strict";function n(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{u:function(){return n}})},9270:function(e,t,r){"use strict";r.d(t,{fC:function(){return N},z$:function(){return E}});var n=r(2265),l=r(98575),a=r(73966),s=r(6741),c=r(80886),o=r(6718),i=r(90420),d=r(71599),u=r(66840),m=r(57437),f="Checkbox",[x,h]=(0,a.b)(f),[p,v]=x(f),j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:o,defaultChecked:i,required:d,disabled:x,value:h="on",onCheckedChange:v,form:j,...g}=e,[k,N]=n.useState(null),E=(0,l.e)(t,e=>N(e)),L=n.useRef(!1),z=!k||j||!!k.closest("form"),[M,C]=(0,c.T)({prop:o,defaultProp:null!=i&&i,onChange:v,caller:f}),S=n.useRef(M);return n.useEffect(()=>{let e=null==k?void 0:k.form;if(e){let t=()=>C(S.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[k,C]),(0,m.jsxs)(p,{scope:r,state:M,disabled:x,children:[(0,m.jsx)(u.WV.button,{type:"button",role:"checkbox","aria-checked":b(M)?"mixed":M,"aria-required":d,"data-state":y(M),"data-disabled":x?"":void 0,disabled:x,value:h,...g,ref:E,onKeyDown:(0,s.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.M)(e.onClick,e=>{C(e=>!!b(e)||!e),z&&(L.current=e.isPropagationStopped(),L.current||e.stopPropagation())})}),z&&(0,m.jsx)(w,{control:k,bubbles:!L.current,name:a,value:h,checked:M,required:d,disabled:x,form:j,style:{transform:"translateX(-100%)"},defaultChecked:!b(i)&&i})]})});j.displayName=f;var g="CheckboxIndicator",k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...l}=e,a=v(g,r);return(0,m.jsx)(d.z,{present:n||b(a.state)||!0===a.state,children:(0,m.jsx)(u.WV.span,{"data-state":y(a.state),"data-disabled":a.disabled?"":void 0,...l,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=g;var w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,control:a,checked:s,bubbles:c=!0,defaultChecked:d,...f}=e,x=n.useRef(null),h=(0,l.e)(x,t),p=(0,o.D)(s),v=(0,i.t)(a);n.useEffect(()=>{let e=x.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==s&&t){let r=new Event("click",{bubbles:c});e.indeterminate=b(s),t.call(e,!b(s)&&s),e.dispatchEvent(r)}},[p,s,c]);let j=n.useRef(!b(s)&&s);return(0,m.jsx)(u.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=d?d:j.current,...f,tabIndex:-1,ref:h,style:{...f.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return"indeterminate"===e}function y(e){return b(e)?"indeterminate":e?"checked":"unchecked"}w.displayName="CheckboxBubbleInput";var N=j,E=k},29114:function(e,t,r){"use strict";r.d(t,{gm:function(){return a}});var n=r(2265);r(57437);var l=n.createContext(void 0);function a(e){let t=n.useContext(l);return e||t||"ltr"}},6718:function(e,t,r){"use strict";r.d(t,{D:function(){return l}});var n=r(2265);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,28453,49027,27648,19935,65236,20926,50527,55622,81766,10650,20947,92971,95030,1744],function(){return e(e.s=85106)}),_N_E=e.O()}]);