"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[80566],{5478:function(e,t,n){n.d(t,{Ry:function(){return l}});var r=new WeakMap,o=new WeakMap,a={},i=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,c){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,v=new Set(l),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};l.forEach(p);var h=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(c),a=null!==t&&"false"!==t,i=(r.get(e)||0)+1,u=(s.get(e)||0)+1;r.set(e,i),s.set(e,u),d.push(e),1===i&&a&&o.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),i++,function(){d.forEach(function(e){var t=r.get(e)-1,a=s.get(e)-1;r.set(e,t),s.set(e,a),t||(o.has(e)||e.removeAttribute(c),o.delete(e)),a||e.removeAttribute(n)}),--i||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),c(r,o,n,"aria-hidden")):function(){return null}}},87922:function(e,t,n){n.d(t,{Z:function(){return U}});var r,o,a,i,u,c,l,s=n(5853),d=n(2265),f="right-scroll-bar-position",v="width-before-scroll-bar";function p(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var h="undefined"!=typeof window?d.useLayoutEffect:d.useEffect,m=new WeakMap,y=(void 0===r&&(r={}),(void 0===o&&(o=function(e){return e}),a=[],i=!1,u={read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=o(e,i);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){i=!0;var t=[];if(a.length){var n=a;a=[],n.forEach(e),t=a}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),a={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),a}}}}).options=(0,s.pi)({async:!0,ssr:!1},r),u),g=function(){},b=d.forwardRef(function(e,t){var n,r,o,a,i=d.useRef(null),u=d.useState({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:g}),c=u[0],l=u[1],f=e.forwardProps,v=e.children,b=e.className,E=e.removeScrollBar,w=e.enabled,S=e.shards,C=e.sideCar,L=e.noIsolation,k=e.inert,x=e.allowPinchZoom,T=e.as,P=e.gapMode,M=(0,s._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[i,t],r=function(e){return n.forEach(function(t){return p(t,e)})},(o=(0,d.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,h(function(){var e=m.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||p(e,null)}),r.forEach(function(e){t.has(e)||p(e,o)})}m.set(a,n)},[n]),a),A=(0,s.pi)((0,s.pi)({},M),c);return d.createElement(d.Fragment,null,w&&d.createElement(C,{sideCar:y,removeScrollBar:E,shards:S,noIsolation:L,inert:k,setCallbacks:l,allowPinchZoom:!!x,lockRef:i,gapMode:P}),f?d.cloneElement(d.Children.only(v),(0,s.pi)((0,s.pi)({},A),{ref:R})):d.createElement(void 0===T?"div":T,(0,s.pi)({},A,{className:b,ref:R}),v))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:v,zeroRight:f};var E=function(e){var t=e.sideCar,n=(0,s._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return d.createElement(r,(0,s.pi)({},n))};E.isSideCarExport=!0;var w=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},S=function(){var e=w();return function(t,n){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},C=function(){var e=S();return function(t){return e(t.styles,t.dynamic),null}},L={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},x=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[k(n),k(r),k(o)]},T=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return L;var t=x(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},P=C(),M="data-scroll-locked",R=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(M,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(f," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(v," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(f," .").concat(f," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(v," .").concat(v," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(M,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(M)||"0",10);return isFinite(e)?e:0},N=function(){d.useEffect(function(){return document.body.setAttribute(M,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(M):document.body.setAttribute(M,e.toString())}},[])},W=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var a=d.useMemo(function(){return T(o)},[o]);return d.createElement(P,{styles:R(a,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){O=!1}var I=!!O&&{passive:!1},F=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},j=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),B(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},B=function(e,t){return"v"===e?F(t,"overflowY"):F(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},K=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,c=n.target,l=t.contains(c),s=!1,d=u>0,f=0,v=0;do{var p=_(e,c),h=p[0],m=p[1]-p[2]-i*h;(h||m)&&B(e,c)&&(f+=m,v+=h),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},X=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Y=function(e){return[e.deltaX,e.deltaY]},z=function(e){return e&&"current"in e?e.current:e},H=0,Z=[],V=(c=function(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),o=d.useState(H++)[0],a=d.useState(C)[0],i=d.useRef(e);d.useEffect(function(){i.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,s.ev)([e.lockRef.current],(e.shards||[]).map(z),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=d.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=X(e),u=n.current,c="deltaX"in e?e.deltaX:u[0]-a[0],l="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=j(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=j(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var v=r.current||o;return K(v,t,e,"h"===v?c:l,!0)},[]),c=d.useCallback(function(e){if(Z.length&&Z[Z.length-1]===a){var n="deltaY"in e?Y(e):X(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(z).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),l=d.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=d.useCallback(function(e){n.current=X(e),r.current=void 0},[]),v=d.useCallback(function(t){l(t.type,Y(t),t.target,u(t,e.lockRef.current))},[]),p=d.useCallback(function(t){l(t.type,X(t),t.target,u(t,e.lockRef.current))},[]);d.useEffect(function(){return Z.push(a),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:p}),document.addEventListener("wheel",c,I),document.addEventListener("touchmove",c,I),document.addEventListener("touchstart",f,I),function(){Z=Z.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,I),document.removeEventListener("touchmove",c,I),document.removeEventListener("touchstart",f,I)}},[]);var h=e.removeScrollBar,m=e.inert;return d.createElement(d.Fragment,null,m?d.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?d.createElement(W,{gapMode:e.gapMode}):null)},y.useMedium(c),E),q=d.forwardRef(function(e,t){return d.createElement(b,(0,s.pi)({},e,{ref:t,sideCar:V}))});q.classNames=b.classNames;var U=q},15278:function(e,t,n){n.d(t,{XB:function(){return f}});var r,o=n(2265),a=n(6741),i=n(66840),u=n(98575),c=n(26606),l=n(57437),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:b,onDismiss:E,...w}=e,S=o.useContext(d),[C,L]=o.useState(null),k=null!==(f=null==C?void 0:C.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,x]=o.useState({}),T=(0,u.e)(t,e=>L(e)),P=Array.from(S.layers),[M]=[...S.layersWithOutsidePointerEventsDisabled].slice(-1),R=P.indexOf(M),A=C?P.indexOf(C):-1,N=S.layersWithOutsidePointerEventsDisabled.size>0,W=A>=R,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.W)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...S.branches].some(e=>e.contains(t));!W||n||(null==y||y(e),null==b||b(e),e.defaultPrevented||null==E||E())},k),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.W)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...S.branches].some(e=>e.contains(t))||(null==g||g(e),null==b||b(e),e.defaultPrevented||null==E||E())},k);return!function(e,t=globalThis?.document){let n=(0,c.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{A!==S.layers.size-1||(null==m||m(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},k),o.useEffect(()=>{if(C)return h&&(0===S.layersWithOutsidePointerEventsDisabled.size&&(r=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),S.layersWithOutsidePointerEventsDisabled.add(C)),S.layers.add(C),v(),()=>{h&&1===S.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=r)}},[C,k,h,S]),o.useEffect(()=>()=>{C&&(S.layers.delete(C),S.layersWithOutsidePointerEventsDisabled.delete(C),v())},[C,S]),o.useEffect(()=>{let e=()=>x({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(i.WV.div,{...w,ref:T,style:{pointerEvents:N?W?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.M)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,a.M)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,a.M)(e.onPointerDownCapture,O.onPointerDownCapture)})});function v(){let e=new CustomEvent(s);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.jH)(a,u):a.dispatchEvent(u)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,u.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(i.WV.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},86097:function(e,t,n){n.d(t,{EW:function(){return a}});var r=n(2265),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:i()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},99103:function(e,t,n){let r;n.d(t,{M:function(){return f}});var o=n(2265),a=n(98575),i=n(66840),u=n(26606),c=n(57437),l="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},f=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:f,onUnmountAutoFocus:y,...g}=e,[b,E]=o.useState(null),w=(0,u.W)(f),S=(0,u.W)(y),C=o.useRef(null),L=(0,a.e)(t,e=>E(e)),k=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(k.paused||!b)return;let t=e.target;b.contains(t)?C.current=t:h(C.current,{select:!0})},t=function(e){if(k.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||h(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,b,k.paused]),o.useEffect(()=>{if(b){m.add(k);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(l,d);b.addEventListener(l,w),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(v(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(l,w),setTimeout(()=>{let t=new CustomEvent(s,d);b.addEventListener(s,S),b.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),b.removeEventListener(s,S),m.remove(k)},0)}}},[b,w,S,k]);let x=o.useCallback(e=>{if(!n&&!r||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=v(e);return[p(t,e),p(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&h(a,{select:!0})):(e.preventDefault(),n&&h(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,k.paused]);return(0,c.jsx)(i.WV.div,{tabIndex:-1,...g,ref:L,onKeyDown:x})});function v(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=y(r,e)).unshift(e)},remove(e){var t;null===(t=(r=y(r,e))[0])||void 0===t||t.resume()}});function y(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},99255:function(e,t,n){n.d(t,{M:function(){return c}});var r,o=n(2265),a=n(61188),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function c(e){let[t,n]=o.useState(i());return(0,a.b)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},83832:function(e,t,n){n.d(t,{h:function(){return c}});var r=n(2265),o=n(54887),a=n(66840),i=n(61188),u=n(57437),c=r.forwardRef((e,t)=>{var n,c;let{container:l,...s}=e,[d,f]=r.useState(!1);(0,i.b)(()=>f(!0),[]);let v=l||d&&(null===(c=globalThis)||void 0===c?void 0:null===(n=c.document)||void 0===n?void 0:n.body);return v?o.createPortal((0,u.jsx)(a.WV.div,{...s,ref:t}),v):null});c.displayName="Portal"},26606:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},5853:function(e,t,n){n.d(t,{Jh:function(){return i},_T:function(){return o},ev:function(){return u},mG:function(){return a},pi:function(){return r}});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function a(e,t,n,r){return new(n||(n=Promise))(function(o,a){function i(e){try{c(r.next(e))}catch(e){a(e)}}function u(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(i,u)}c((r=r.apply(e,t||[])).next())})}function i(e,t){var n,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=u(0),i.throw=u(1),i.return=u(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(u){return function(c){return function(u){if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,c])}}}function u(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError}}]);