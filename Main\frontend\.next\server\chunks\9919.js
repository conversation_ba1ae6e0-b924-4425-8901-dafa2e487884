"use strict";exports.id=9919,exports.ids=[9919],exports.modules={61718:(e,s,r)=>{r.d(s,{g:()=>x});var a=r(10326),t=r(17577),n=r(92392),l=r(80609),i=r(2454),d=r(30811),o=r(1868),c=r(77863),u=r(6216),m=r(70012);function x({allCountry:e=!1,defaultValue:s,defaultCountry:r,onSelectChange:x,disabled:p=!1,triggerClassName:f,arrowClassName:h,flagClassName:g,display:j,placeholderClassName:b,align:y="start",side:v="bottom"}){let{t:N}=(0,m.$G)(),{countries:w,getCountryByCode:C,isLoading:P}=(0,o.F)(),[I,Z]=t.useState(!1),[z,S]=t.useState(s);return t.useEffect(()=>{s&&S(s)},[s]),t.useEffect(()=>{(async()=>{r&&await C(r,e=>{e&&(S(e),x(e))})})()},[r]),(0,a.jsxs)(d.J2,{open:I,onOpenChange:Z,children:[(0,a.jsxs)(d.xo,{disabled:p,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",f),children:[z?a.jsx("div",{className:"flex flex-1 items-center",children:(0,a.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[a.jsx(l.W,{className:g,countryCode:z.code?.cca2==="*"?"UN":z.code?.cca2}),void 0!==j?j(z):a.jsx("span",{children:z.name})]})}):a.jsx("span",{className:(0,c.ZP)("text-placeholder",b),children:N("Select country")}),a.jsx(u.Z,{className:(0,c.ZP)("size-6",h)})]}),a.jsx(d.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:y,side:v,children:(0,a.jsxs)(i.mY,{children:[a.jsx(i.sZ,{placeholder:N("Search...")}),a.jsx(i.e8,{children:(0,a.jsxs)(i.fu,{children:[P&&a.jsx(n.Loader,{}),e&&(0,a.jsxs)(i.di,{value:N("All countries"),onSelect:()=>{S({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),x({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),Z(!1)},children:[a.jsx(l.W,{countryCode:"UN"}),a.jsx("span",{className:"pl-1.5",children:N("All countries")})]}),w?.map(e=>"officially-assigned"===e.status?a.jsxs(i.di,{value:e.name,onSelect:()=>{S(e),x(e),Z(!1)},children:[a.jsx(l.W,{countryCode:e.code.cca2}),a.jsxs("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},720:(e,s,r)=>{r.d(s,{M:()=>m});var a=r(10326),t=r(9346),n=r(30811),l=r(77863),i=r(71305),d=r(76129),o=r(17577),c=r.n(o),u=r(70012);let m=c().forwardRef(({value:e,onChange:s,className:r,placeholderClassName:o,options:m},x)=>{let{t:p}=(0,u.$G)(),[f,h]=c().useState(!1);return(0,a.jsxs)(n.J2,{open:f,onOpenChange:h,children:[(0,a.jsxs)(n.xo,{disabled:!!m?.disabled,className:(0,l.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",r),children:[a.jsx("div",{ref:x,className:"flex flex-1 items-center",children:a.jsx("div",{className:"flex flex-1 items-center gap-2 text-left",children:e?(0,i.WU)(e,"dd/MM/yyyy"):a.jsx("span",{className:(0,l.ZP)("text-placeholder",o),children:p("Pick a Date")})})}),a.jsx(d.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),a.jsx(n.yk,{className:"w-auto p-0",align:"start",children:a.jsx(t.f,{...m,mode:"single",initialFocus:!0,selected:e??void 0,onSelect:e=>{s(e),h(!1)}})})]})})},12104:(e,s,r)=>{r.d(s,{S:()=>o});var a=r(10326),t=r(77863),n=r(46226),l=r(17577),i=r.n(l),d=r(99447);function o({defaultValue:e,onChange:s,className:r,children:l,disabled:o=!1,id:c}){let[u,m]=i().useState(e),{getRootProps:x,getInputProps:p}=(0,d.uI)({onDrop:e=>{let r=e?.[0];r&&(s(r),m(URL.createObjectURL(r)))},disabled:o});return(0,a.jsxs)("div",{...x({className:(0,t.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r)}),children:[!!u&&a.jsx(n.default,{src:u,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),a.jsx("input",{id:c,...p()}),!u&&a.jsx("div",{children:l})]})}},71227:(e,s,r)=>{r.d(s,{E:()=>P});var a=r(10326),t=r(92392),n=r(80609),l=r(2454),i=r(54432),d=r(30811),o=r(1868),c=r(77863),u=r(26138),m=r(6216),x=r(33436),p=r(34197),f=r(4017),h=r(70107),g=r(55991),j=r(4981),b=r(71132),y=r(60715),v=r(5389),N=r(70012),w=r(17577);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function P({value:e,defaultValue:s="",onChange:r,onBlur:t,disabled:n,inputClassName:l,options:d}){let[o,u]=(0,w.useState)(s??""),[m,y]=(0,w.useState)(""),[N,P]=(0,w.useState)(d?.initialCountry),Z=e=>{if(e)try{let s=x.S(e,N);s?(P(s.country),y(`+${s.countryCallingCode}`),u(s.formatNational())):u(e)}catch(s){u(e)}else u(e)},z=p.L(N||d?.initialCountry||"US",v.Z);return(0,a.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(I,{country:N,disabled:n,initialCountry:d?.initialCountry,onSelect:e=>{let s=e.code.cca2?.toUpperCase(),r=f.G(s);y(`+${r}`),P(s)}}),a.jsx("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:m||`+${z?.countryCallingCode}`})]}),a.jsx(i.I,{type:"tel",className:(0,c.ZP)("rounded-l-none pl-2",l),value:o,onChange:e=>{let{value:s}=e.target,a=x.S(s,N);t?.(""),a&&h.t(s,N)&&g.q(s,N)?(P(a.country),y(`+${a.countryCallingCode}`),r?.(a.number),u(s)):(a?u(a.nationalNumber):u(s),r?.(s))},onPaste:e=>{let s=e.clipboardData.getData("Text"),a=x.S(s);if(a&&h.t(s))Z(a.formatNational()),P(a.country),y(`+${a.countryCallingCode}`),r?.(a.number),t?.("");else{let e=x.S(s,N);e&&h.t(s,N)&&(Z(e.formatNational()),r?.(e.number),t?.(""))}},onBlur:()=>{if(o&&!j.y(o,N)){let e=b.d(o,N);e&&t?.(C[e])}},placeholder:z?.formatNational(),disabled:n})]})}function I({initialCountry:e,country:s,onSelect:r,disabled:t}){let[l,i]=(0,w.useState)(!1);return(0,a.jsxs)(d.J2,{open:l,onOpenChange:i,children:[(0,a.jsxs)(d.xo,{disabled:t,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[e||s?a.jsx(n.W,{countryCode:s||e,className:"aspect-auto h-[18px] w-7 flex-1"}):a.jsx(u.Z,{}),a.jsx(m.Z,{variant:"Bold",size:16})]}),a.jsx(d.yk,{align:"start",className:"h-fit p-0",children:a.jsx(Z,{defaultValue:s||e,onSelect:e=>{r(e),i(!1)}})})]})}function Z({defaultValue:e,onSelect:s}){let{countries:r,isLoading:i}=(0,o.F)(),{t:d}=(0,N.$G)();return(0,a.jsxs)(l.mY,{children:[a.jsx(l.sZ,{placeholder:d("Search country by name"),className:"placeholder:text-input-placeholder"}),a.jsx(l.e8,{children:a.jsx(l.fu,{children:i?a.jsx(l.di,{children:a.jsx(t.Loader,{})}):r.filter(e=>{let s=e.code.cca2?.toUpperCase();return y.o().includes(s)})?.map(r=>a.jsxs(l.di,{value:r.name,"data-active":r.code.cca2===e,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>s(r),children:[a.jsx(n.W,{countryCode:r.code.cca2}),r.name]},r.code.ccn3))})})]})}},29612:(e,s,r)=>{r.d(s,{W:()=>c});var a=r(10326),t=r(17577),n=r(90772),l=r(54432),i=r(77863),d=r(9489),o=r(75138);let c=t.forwardRef(({className:e,type:s,...r},c)=>{let[u,m]=t.useState(!1);return(0,a.jsxs)("div",{className:"relative",children:[a.jsx(l.I,{type:u?"text":"password",className:(0,i.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:c,...r}),a.jsx(n.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),m(e=>!e)},children:u?a.jsx(d.Z,{}):a.jsx(o.Z,{})})]})});c.displayName="PasswordInput"},80609:(e,s,r)=>{r.d(s,{W:()=>l});var a=r(10326),t=r(77863),n=r(46226);function l({countryCode:e,className:s,url:r}){return e||r?a.jsx(n.default,{src:r??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,t.ZP)("rounded-[2px]",s)}):null}},31810:(e,s,r)=>{r.d(s,{X:()=>n});var a=r(10326),t=r(77863);function n({className:e}){return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,t.ZP)("fill-primary",e),children:[a.jsx("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},47752:(e,s,r)=>{r.d(s,{h:()=>P});var a=r(10326),t=r(5158),n=r(61718),l=r(92392),i=r(56672),d=r(90772),o=r(55632),c=r(54432),u=r(31048),m=r(49547),x=r(50833);async function p(e){try{let s=await m.Z.put("/customers/update-address",{addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city});return{statusCode:s.status,statusText:s.statusText,status:200===s.status||201===s.status,message:s.data?.message??""}}catch(a){let e=500,s="Internal Server Error",r="An unknown error occurred";return(0,x.IZ)(a)?(e=a.response?.status??500,s=a.response?.statusText??"Internal Server Error",r=a.response?.data?.message??a.message):a instanceof Error&&(r=a.message),{statusCode:e,statusText:s,status:!1,message:r}}}var f=r(1868),h=r(74064),g=r(44284),j=r(17577),b=r.n(j),y=r(74723),v=r(70012),N=r(85999),w=r(27256);let C=w.z.object({street:w.z.string({required_error:"Street is required."}),country:w.z.string({required_error:"Country is required."}),city:w.z.string({required_error:"city is required."}),zipCode:w.z.string({required_error:"Zip code is required."})});function P({address:e}){let[s,r]=(0,j.useTransition)(),[m,x]=b().useState(null),{getCountryByCode:w}=(0,f.F)(),{t:P}=(0,v.$G)(),I=(0,y.cI)({resolver:(0,h.F)(C),defaultValues:{street:"",city:"",country:"",zipCode:""}});return a.jsx(o.l0,{...I,children:a.jsx("form",{onSubmit:I.handleSubmit(e=>{r(async()=>{let s=await p(e);s&&s.status?N.toast.success(s.message):N.toast.error(P(s.message))})}),className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)(i.Qd,{value:"ADDRESS_INFORMATION",className:"border-none px-4 py-0",children:[a.jsx(i.o4,{className:"py-6 hover:no-underline",children:a.jsx("p",{className:"text-base font-medium leading-[22px]",children:P("Address")})}),(0,a.jsxs)(i.vF,{className:"flex flex-col gap-2 border-t border-divider px-1 pt-4",children:[a.jsx(u.Z,{children:P("Full mailing address")}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-2.5",children:[a.jsx(o.Wi,{control:I.control,name:"street",render:({field:e})=>(0,a.jsxs)(o.xJ,{className:"col-span-12",children:[a.jsx(o.NI,{children:a.jsx(c.I,{type:"text",placeholder:P("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...e})}),a.jsx(o.zG,{})]})}),a.jsx(o.Wi,{control:I.control,name:"country",render:({field:e})=>(0,a.jsxs)(o.xJ,{className:"col-span-12",children:[a.jsx(o.NI,{children:a.jsx(n.g,{defaultValue:m,onSelectChange:s=>e.onChange(s.code.cca2)})}),a.jsx(o.zG,{})]})}),a.jsx(o.Wi,{control:I.control,name:"city",render:({field:e})=>(0,a.jsxs)(o.xJ,{className:"col-span-12 md:col-span-6",children:[a.jsx(o.NI,{children:a.jsx("div",{className:"relative flex items-center gap-2.5",children:a.jsx(c.I,{type:"text",placeholder:P("City name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...e})})}),a.jsx(o.zG,{})]})}),a.jsx(o.Wi,{control:I.control,name:"zipCode",render:({field:e})=>(0,a.jsxs)(o.xJ,{className:"col-span-12 md:col-span-6",children:[a.jsx(o.NI,{children:a.jsx(c.I,{type:"text",placeholder:P("Zip code"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...e})}),a.jsx(o.zG,{})]})})]}),a.jsx("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,a.jsxs)(d.z,{disabled:s,children:[(0,a.jsxs)(t.J,{condition:!s,children:[P("Save"),a.jsx(g.Z,{size:20})]}),a.jsx(t.J,{condition:s,children:a.jsx(l.Loader,{title:P("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}},72847:(e,s,r)=>{r.d(s,{G:()=>I});var a=r(10326),t=r(5158),n=r(29612),l=r(56672),i=r(90772),d=r(55632),o=r(54432),c=r(74064),u=r(17577),m=r.n(u),x=r(74723),p=r(27256),f=r(92392),h=r(43273),g=r(49547),j=r(50833);async function b(e){try{let s=await g.Z.post("/auth/change-password",{currentPassword:e?.currentPassword,newPassword:e?.newPassword,newPasswordConfirmation:e?.confirmPassword});return{statusCode:s.status,statusText:s.statusText,status:200===s.status,message:s.data?.message??""}}catch(a){let e=500,s="Internal Server Error",r="An unknown error occurred";return(0,j.IZ)(a)?(e=a.response?.status??500,s=a.response?.statusText??"Internal Server Error",r=a.response?.data?.message??a.message):a instanceof Error&&(r=a.message),{statusCode:e,statusText:s,status:!1,message:r}}}var y=r(79957),v=r(44284),N=r(44221),w=r(70012),C=r(85999);let P=p.z.object({currentPassword:p.z.string({required_error:"Current password is required."}),newPassword:p.z.string({required_error:"Choose a new password"}),confirmPassword:p.z.string({required_error:"Confirm password is required."})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>e.newPassword!==e.currentPassword,{message:"New Password must be different",path:["newPassword"]});function I(){let{t:e}=(0,w.$G)(),[s,r]=m().useState(!1),[p,g]=(0,u.useTransition)(),j=(0,x.cI)({resolver:(0,c.F)(P),defaultValues:{currentPassword:"",newPassword:"",confirmPassword:""}});return a.jsx(d.l0,{...j,children:a.jsx("form",{onSubmit:j.handleSubmit(s=>{g(async()=>{let r=await b(s);r&&r.status?C.toast.success(e("Password successfully updated")):C.toast.error(e(r.message))})}),className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)(l.Qd,{value:"PASSWORD_INFORMATION",className:"border-none px-4 py-0",children:[a.jsx(l.o4,{className:"py-6 hover:no-underline",children:a.jsx("p",{className:"text-base font-medium leading-[22px]",children:e("Privacy & Security")})}),(0,a.jsxs)(l.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[(0,a.jsxs)(h.bZ,{className:"border-none bg-transparent shadow-default",children:[a.jsx(y.Z,{color:"#0B6A0B",variant:"Bulk"}),a.jsx(h.Cd,{className:"pl-2 text-sm font-semibold leading-5",children:e("Two-factor authentication is on.")}),a.jsx(h.X,{className:"pl-2 text-sm font-normal",children:e("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]}),a.jsx(t.J,{condition:!s,children:a.jsx(d.Wi,{control:j.control,name:"currentPassword",render:()=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.lX,{children:e("Password")}),a.jsx(d.NI,{children:a.jsx(o.I,{type:"password",defaultValue:"akdjfkjsdfjkdsf",readOnly:!0,disabled:!s,placeholder:e("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-80"})}),a.jsx(d.zG,{})]})})}),(0,a.jsxs)(t.J,{condition:s,children:[a.jsx(d.Wi,{control:j.control,name:"currentPassword",render:({field:r})=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.lX,{children:e("Current password")}),a.jsx(d.NI,{children:a.jsx(n.W,{placeholder:e("Enter current password"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",disabled:!s,...r})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:j.control,name:"newPassword",render:({field:r})=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.lX,{children:e("New Password")}),a.jsx(d.NI,{children:a.jsx(n.W,{...r,disabled:!s,className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",placeholder:e("Create a strong password")})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:j.control,name:"confirmPassword",render:({field:r})=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.lX,{children:e("Confirm New Password")}),a.jsx(d.NI,{children:a.jsx(n.W,{...r,disabled:!s,className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",placeholder:e("Enter the password again")})}),a.jsx(d.zG,{})]})})]}),(0,a.jsxs)("div",{className:"flex flex-row items-center justify-end gap-4",children:[a.jsx(t.J,{condition:!s,children:(0,a.jsxs)(i.z,{type:"button",onClick:()=>r(!0),children:[e("Edit Password"),a.jsx(v.Z,{size:20})]})}),a.jsx(t.J,{condition:s,children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i.z,{type:"button",variant:"outline",onClick:()=>r(!1),children:[a.jsx(N.Z,{size:20}),e("Cancel")]}),(0,a.jsxs)(i.z,{type:"submit",disabled:p,children:[(0,a.jsxs)(t.J,{condition:!p,children:[e("Save"),a.jsx(v.Z,{size:20})]}),a.jsx(t.J,{condition:p,children:a.jsx(f.Loader,{title:e("Processing..."),className:"text-primary-foreground"})})]})]})})]})]})]})})})}},71380:(e,s,r)=>{r.d(s,{O:()=>_});var a=r(10326),t=r(5158),n=r(720),l=r(12104),i=r(71227),d=r(92392),o=r(31810),c=r(56672),u=r(90772),m=r(55632),x=r(54432),p=r(31048),f=r(88846),h=r(49547),g=r(50833),j=r(71305);async function b(e){try{let s=new FormData;s.append("firstName",e.firstName),s.append("lastName",e.lastName),s.append("phone",e.phone),s.append("gender",e.gender),s.append("dob",(0,j.WU)(e.dateOfBirth,"yyyy-MM-dd")),s.append("profileImage",e.profileImage);let r=await h.Z.put("/customers/update",s,{headers:{"Content-Type":"multipart/form-data"}});return{statusCode:r.status,statusText:r.statusText,status:200===r.status||201===r.status,message:r.data?.message??""}}catch(a){let e=500,s="Internal Server Error",r="An unknown error occurred";return(0,g.IZ)(a)?(e=a.response?.status??500,s=a.response?.statusText??"Internal Server Error",r=a.response?.data?.message??a.message):a instanceof Error&&(r=a.message),{statusCode:e,statusText:s,status:!1,message:r}}}var y=r(77863),v=r(24820),N=r(74064),w=r(44284),C=r(17577),P=r.n(C),I=r(74723),Z=r(70012),z=r(85999),S=r(27256);let F=S.z.object({profileImage:v.K,firstName:S.z.string({required_error:"First name is required."}),lastName:S.z.string({required_error:"Last name is required."}),email:S.z.string({required_error:"Email is required."}),phone:S.z.string({required_error:"Phone is required."}),dateOfBirth:S.z.date({required_error:"Date of Birth is required."}),gender:S.z.string({required_error:"Sex is required"})});function _({user:e,isLoading:s}){let[r,h]=(0,C.useTransition)(),{t:g}=(0,Z.$G)(),j=(0,I.cI)({resolver:(0,N.F)(F),defaultValues:{profileImage:"",firstName:"",lastName:"",email:"",phone:"",dateOfBirth:void 0,gender:""}}),v=(0,C.useMemo)(()=>e,[e]);return P().useCallback(()=>{v&&j.reset({firstName:v?.firstName||"",lastName:v?.lastName||"",email:v?.email||"",phone:v?.phone||"",dateOfBirth:v?.dateOfBirth||void 0,gender:v?.gender||""})},[s]),a.jsx(m.l0,{...j,children:a.jsx("form",{onSubmit:j.handleSubmit(e=>{h(async()=>{let s=await b({...e,gender:e.gender?.toLowerCase()});s&&s.status?z.toast.success(s.message):z.toast.error(g(s.message))})}),className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)(c.Qd,{value:"PROFILE_INFORMATION",className:"border-none px-4 py-0",children:[a.jsx(c.o4,{className:"py-6 hover:no-underline",children:a.jsx("p",{className:"text-base font-medium leading-[22px]",children:g("Profile")})}),(0,a.jsxs)(c.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:[a.jsx(m.Wi,{control:j.control,name:"profileImage",render:({field:s})=>(0,a.jsxs)(m.xJ,{children:[a.jsx(m.lX,{children:g("Profile picture")}),a.jsx(m.NI,{children:a.jsx(l.S,{id:"profileImage",defaultValue:(0,y.qR)(e?.avatar),onChange:e=>{s.onChange(e)},className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[a.jsx(o.X,{}),a.jsx("p",{className:"text-sm font-normal text-primary",children:g("Upload photo")})]})})}),a.jsx(m.zG,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[a.jsx(m.Wi,{control:j.control,name:"firstName",render:({field:e})=>(0,a.jsxs)(m.xJ,{className:"col-span-12 lg:col-span-6",children:[a.jsx(m.lX,{children:g("First name")}),a.jsx(m.NI,{children:a.jsx(x.I,{type:"text",placeholder:g("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...e})}),a.jsx(m.zG,{})]})}),a.jsx(m.Wi,{control:j.control,name:"lastName",render:({field:e})=>(0,a.jsxs)(m.xJ,{className:"col-span-12 lg:col-span-6",children:[a.jsx(m.lX,{children:g("Last name")}),a.jsx(m.NI,{children:a.jsx(x.I,{type:"text",placeholder:g("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...e})}),a.jsx(m.zG,{})]})})]}),a.jsx(m.Wi,{control:j.control,name:"email",render:({field:e})=>(0,a.jsxs)(m.xJ,{children:[a.jsx(m.lX,{children:g("Email")}),a.jsx(m.NI,{children:a.jsx(x.I,{type:"email",disabled:!0,value:e.value,placeholder:g("Enter your email"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",readOnly:!0})}),a.jsx(m.zG,{})]})}),a.jsx(m.Wi,{control:j.control,name:"phone",render:({field:e})=>(0,a.jsxs)(m.xJ,{children:[a.jsx(m.lX,{children:g("Phone")}),a.jsx(m.NI,{children:a.jsx(i.E,{value:e.value,onChange:s=>e.onChange(s),onBlur:e=>{e?j.setError("phone",{type:"custom",message:g(e)}):j.clearErrors("phone")}})}),a.jsx(m.zG,{})]})}),a.jsx(m.Wi,{control:j.control,name:"dateOfBirth",render:({field:e})=>(0,a.jsxs)(m.xJ,{children:[a.jsx(m.lX,{children:g("Date of birth")}),a.jsx(m.NI,{children:a.jsx(n.M,{...e})}),a.jsx(m.zG,{})]})}),a.jsx(m.Wi,{control:j.control,name:"gender",render:({field:e})=>(0,a.jsxs)(m.xJ,{children:[a.jsx(m.lX,{children:g("Sex")}),a.jsx(m.NI,{children:(0,a.jsxs)(f.E,{defaultValue:e.value,onValueChange:e.onChange,className:"flex",children:[(0,a.jsxs)(p.Z,{htmlFor:"GenderMale","data-selected":"Male"===(0,y.fl)(e.value),className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[a.jsx(f.m,{id:"GenderMale",value:"Male",className:"absolute opacity-0"}),a.jsx("span",{children:g("Male")})]}),(0,a.jsxs)(p.Z,{htmlFor:"GenderFemale","data-selected":"Female"===(0,y.fl)(e.value),className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[a.jsx(f.m,{id:"GenderFemale",value:"Female",className:"absolute opacity-0"}),a.jsx("span",{children:g("Female")})]})]})}),a.jsx(m.zG,{})]})}),a.jsx("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,a.jsxs)(u.z,{disabled:r,children:[(0,a.jsxs)(t.J,{condition:!r,children:[g("Save"),a.jsx(w.Z,{size:20})]}),a.jsx(t.J,{condition:r,children:a.jsx(d.Loader,{title:g("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}},56672:(e,s,r)=>{r.d(s,{Qd:()=>o,UQ:()=>d,o4:()=>c,vF:()=>u});var a=r(10326),t=r(57793),n=r(17577),l=r(77863),i=r(6216);let d=t.fC,o=n.forwardRef(({className:e,...s},r)=>a.jsx(t.ck,{ref:r,className:(0,l.ZP)("border-b",e),...s}));o.displayName="AccordionItem";let c=n.forwardRef(({className:e,children:s,...r},n)=>a.jsx(t.h4,{className:"flex",children:(0,a.jsxs)(t.xz,{ref:n,className:(0,l.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...r,children:[s,a.jsx(i.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));c.displayName=t.xz.displayName;let u=n.forwardRef(({className:e,children:s,...r},n)=>a.jsx(t.VY,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:a.jsx("div",{className:(0,l.ZP)("pb-4 pt-0",e),children:s})}));u.displayName=t.VY.displayName},43273:(e,s,r)=>{r.d(s,{Cd:()=>o,X:()=>c,bZ:()=>d});var a=r(10326),t=r(79360),n=r(17577),l=r(77863);let i=(0,t.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=n.forwardRef(({className:e,variant:s,...r},t)=>a.jsx("div",{ref:t,role:"alert",className:(0,l.ZP)(i({variant:s}),e),...r}));d.displayName="Alert";let o=n.forwardRef(({className:e,...s},r)=>a.jsx("h5",{ref:r,className:(0,l.ZP)("mb-1 font-medium leading-none tracking-tight",e),...s}));o.displayName="AlertTitle";let c=n.forwardRef(({className:e,...s},r)=>a.jsx("div",{ref:r,className:(0,l.ZP)("text-sm [&_p]:leading-relaxed",e),...s}));c.displayName="AlertDescription"},9346:(e,s,r)=>{r.d(s,{f:()=>c});var a=r(10326),t=r(11890),n=r(39183),l=r(941);r(17577);var i=r(18493),d=r(90772),o=r(77863);function c({className:e,classNames:s,showOutsideDays:r=!0,...c}){return a.jsx(i._W,{showOutsideDays:r,className:(0,o.ZP)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,o.ZP)((0,d.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.ZP)((0,d.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:({...e})=>a.jsx(t.Z,{className:"h-4 w-4"}),IconRight:({...e})=>a.jsx(n.Z,{className:"h-4 w-4"}),Dropdown:({...e})=>(0,a.jsxs)("div",{className:"relative",children:[a.jsx("select",{...e,style:{opacity:0,position:"absolute"}}),(0,a.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[a.jsx("span",{className:"text-sm",children:e.caption}),a.jsx(l.Z,{className:"size-3"})]})]})},...c})}c.displayName="Calendar"},55632:(e,s,r)=>{r.d(s,{NI:()=>h,Wi:()=>u,l0:()=>o,lX:()=>f,xJ:()=>p,zG:()=>g});var a=r(10326),t=r(34214),n=r(17577),l=r(74723),i=r(31048),d=r(77863);let o=l.RV,c=n.createContext({}),u=({...e})=>a.jsx(c.Provider,{value:{name:e.name},children:a.jsx(l.Qr,{...e})}),m=()=>{let e=n.useContext(c),s=n.useContext(x),{getFieldState:r,formState:a}=(0,l.Gc)(),t=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=s;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...t}},x=n.createContext({}),p=n.forwardRef(({className:e,...s},r)=>{let t=n.useId();return a.jsx(x.Provider,{value:{id:t},children:a.jsx("div",{ref:r,className:(0,d.ZP)("space-y-2",e),...s})})});p.displayName="FormItem";let f=n.forwardRef(({className:e,required:s,...r},t)=>{let{error:n,formItemId:l}=m();return a.jsx("span",{children:a.jsx(i.Z,{ref:t,className:(0,d.ZP)(n&&"text-base font-medium text-destructive",e),htmlFor:l,...r})})});f.displayName="FormLabel";let h=n.forwardRef(({...e},s)=>{let{error:r,formItemId:n,formDescriptionId:l,formMessageId:i}=m();return a.jsx(t.g7,{ref:s,id:n,"aria-describedby":r?`${l} ${i}`:`${l}`,"aria-invalid":!!r,...e})});h.displayName="FormControl",n.forwardRef(({className:e,...s},r)=>{let{formDescriptionId:t}=m();return a.jsx("p",{ref:r,id:t,className:(0,d.ZP)("text-sm text-muted-foreground",e),...s})}).displayName="FormDescription";let g=n.forwardRef(({className:e,children:s,...r},t)=>{let{error:n,formMessageId:l}=m(),i=n?String(n?.message):s;return i?a.jsx("p",{ref:t,id:l,className:(0,d.ZP)("text-sm font-medium text-destructive",e),...r,children:i}):null});g.displayName="FormMessage"},54432:(e,s,r)=>{r.d(s,{I:()=>l});var a=r(10326),t=r(17577),n=r(77863);let l=t.forwardRef(({className:e,type:s,...r},t)=>a.jsx("input",{type:s,className:(0,n.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:t,...r}));l.displayName="Input"},31048:(e,s,r)=>{r.d(s,{Z:()=>c});var a=r(10326),t=r(34478),n=r(79360),l=r(17577),i=r(77863);let d=(0,n.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef(({className:e,...s},r)=>a.jsx(t.f,{ref:r,className:(0,i.ZP)(d(),e),...s}));o.displayName=t.f.displayName;let c=o},88846:(e,s,r)=>{r.d(s,{E:()=>d,m:()=>o});var a=r(10326),t=r(17577),n=r(18623),l=r(53982),i=r(77863);let d=t.forwardRef(({className:e,...s},r)=>a.jsx(n.fC,{className:(0,i.ZP)("grid gap-2",e),...s,ref:r}));d.displayName=n.fC.displayName;let o=t.forwardRef(({className:e,...s},r)=>a.jsx(n.ck,{ref:r,className:(0,i.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:a.jsx(n.z$,{className:"flex items-center justify-center",children:a.jsx(l.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));o.displayName=n.ck.displayName},1868:(e,s,r)=>{r.d(s,{F:()=>o});class a{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var t=r(44099),n=r(85999),l=r(84455);let i=t.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),d="name,cca2,ccn3,cca3,status,flag,flags";function o(){let{data:e,isLoading:s,...r}=(0,l.ZP)(`/all?fields=${d}`,e=>i.get(e)),o=e?.data,c=async(e,s)=>{try{let r=await i.get(`/alpha/${e.toLowerCase()}?fields=${d}`),t=r.data?new a(r.data):null;s(t)}catch(e){t.default.isAxiosError(e)&&n.toast.error("Failed to fetch country")}};return{countries:o?o.map(e=>new a(e)):[],isLoading:s,getCountryByCode:c,...r}}},41122:(e,s,r)=>{r.d(s,{h:()=>i});var a=r(49547),t=r(13263),n=r(72450),l=r(84455);function i(){let{data:e,error:s,isLoading:r}=(0,l.ZP)("/customers/detail",e=>a.Z.get(e));return{data:e?.data,user:e=>e?new n.n({...e,...e?.user,avatar:e?.profileImage}):null,address:e=>e?new t.k(e?.address):null,isLoading:r,error:s}}},24820:(e,s,r)=>{r.d(s,{K:()=>l,S:()=>i});var a=r(27256);let t=["image/jpeg","image/jpg","image/png","image/svg+xml"],n=["image/x-icon","image/vnd.microsoft.icon","image/png"],l=a.z.union([a.z.string(),a.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&t.includes(e.type),"Invalid file format. Please upload a .jpg, .jpeg, .png or .svg file."),i=a.z.union([a.z.string(),a.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&n.includes(e.type),"Invalid file format. Please upload a .ico or .png file.")},72450:(e,s,r)=>{r.d(s,{n:()=>l});var a=r(13263),t=r(13573),n=r(77863);class l{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,n.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new t.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}}};