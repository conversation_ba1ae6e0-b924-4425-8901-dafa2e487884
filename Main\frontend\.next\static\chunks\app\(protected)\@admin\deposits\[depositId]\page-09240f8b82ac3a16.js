(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[48689],{99185:function(e,s,l){Promise.resolve().then(l.bind(l,95378))},95378:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return D},runtime:function(){return k}});var a=l(57437),t=l(41709),i=l(85487),n=l(87806),d=l(62869),c=l(6512),r=l(79981),o=l(97751);async function m(e,s){try{let l=await r.Z.put("/admin/deposits/".concat(s,"/").concat(e),{id:e});return(0,o.B)(l)}catch(e){return(0,o.D)(e)}}var x=l(31117),p=l(94508),u=l(48408),v=l(77926),f=l(50934),h=l(43271),j=l(19571),g=l(60827),b=l(99376),N=l(43949),y=l(14438);let k="edge";function D(){var e,s,l,r,o,k;let D=(0,b.useParams)(),{data:Z,isLoading:w,mutate:C}=(0,x.d)("/admin/deposits/".concat(D.depositId)),{t:A}=(0,N.$G)();if(w)return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(i.Loader,{})});let z=(null==Z?void 0:Z.data)?new u.C(null==Z?void 0:Z.data):null,I=new p.F;if(!z)return(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,a.jsx)(v.Z,{}),A("No data found")]});let _=(null==z?void 0:z.metaData)?Object.entries(z.metaData).filter(e=>{let[s]=e;return"trxSecret"!==s}).map(e=>{let[s,l]=e;return{key:s,value:l}}):[],E=e=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());return(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,a.jsxs)("div",{className:"col-span-12 md:col-span-7",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,a.jsx)(f.Z,{variant:"Bulk",size:32,className:"text-primary"}),(0,a.jsxs)("h2",{className:"font-semibold",children:[" ",A("Deposit")," #",D.depositId]})]}),(0,a.jsx)(n.z,{senderAvatar:(0,p.qR)(z.from.image),senderName:z.from.label,senderInfo:[null===(e=z.from)||void 0===e?void 0:e.email,null==z?void 0:null===(s=z.from)||void 0===s?void 0:s.phone],receiverAvatar:(0,p.qR)(null==z?void 0:null===(l=z.to)||void 0===l?void 0:l.image),receiverName:null==z?void 0:null===(r=z.to)||void 0===r?void 0:r.label,receiverInfo:[null==z?void 0:null===(o=z.to)||void 0===o?void 0:o.email,null==z?void 0:null===(k=z.to)||void 0===k?void 0:k.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:A("Amount")}),(0,a.jsx)("div",{className:"col-span-6 pl-2.5 text-sm font-medium sm:text-base",children:I.formatVC(z.amount,z.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:A("Service charge")}),(0,a.jsx)("div",{className:"col-span-6 pl-2.5 text-sm font-medium sm:text-base",children:I.formatVC(z.fee,z.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:A("User gets")}),(0,a.jsx)("div",{className:"col-span-6 pl-2.5 text-sm font-semibold sm:text-base",children:I.formatVC(z.total,z.metaData.currency)})]})]}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:A("Transaction ID")}),(0,a.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[z.trxId,(0,a.jsx)(d.z,{type:"button",onClick:()=>(0,p.Fp)(z.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,a.jsx)(h.Z,{size:"20"})})]})]})}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4 rounded-xl bg-card px-4 py-6",children:[(0,a.jsx)("h4",{children:A("Deposit request")}),(0,a.jsx)(t.J,{condition:(null==z?void 0:z.status)==="completed",children:(0,a.jsx)("p",{children:A("Deposit approved")})}),(0,a.jsx)(t.J,{condition:(null==z?void 0:z.status)==="failed",children:(0,a.jsx)("p",{children:A("Deposit failed")})}),(0,a.jsx)(t.J,{condition:(null==z?void 0:z.status)==="pending",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,a.jsxs)(d.z,{type:"button",className:"bg-[#0B6A0B] text-white hover:bg-[#149014]",onClick:()=>{var e;y.toast.promise(m(null==Z?void 0:null===(e=Z.data)||void 0===e?void 0:e.id,"accept"),{loading:A("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return C(Z),e.message},error:e=>e.message})},children:[(0,a.jsx)(j.Z,{}),A("Accept deposit")]}),(0,a.jsxs)(d.z,{type:"button",className:"bg-[#D13438] text-white hover:bg-[#b42328]",onClick:()=>{var e;y.toast.promise(m(null==Z?void 0:null===(e=Z.data)||void 0===e?void 0:e.id,"decline"),{loading:A("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return C(Z),e.message},error:e=>e.message})},children:[(0,a.jsx)(g.Z,{}),A("Reject deposit")]})]})})]})]}),(0,a.jsxs)("div",{className:"col-span-12 md:col-span-5",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("h2",{children:A("Method info")})}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-base font-normal",children:A("Method used")}),(0,a.jsx)("div",{className:"col-span-6 text-base font-medium",children:null==z?void 0:z.method})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-base font-normal",children:A("Wallet")}),(0,a.jsx)("div",{className:"col-span-6 text-base font-medium",children:null==z?void 0:z.currency})]})]})]}),(0,a.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("h2",{children:A("Additional info")})}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsx)("div",{className:"flex flex-col",children:_.map((e,s)=>(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 ".concat(s%2==0?"bg-accent":""),children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:E(e.key)}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:e.value||"N/A"})]},e.key))})]})]})]})})}},41709:function(e,s,l){"use strict";function a(e){let{condition:s,children:l}=e;return s?l:null}l.d(s,{J:function(){return a}}),l(2265)}},function(e){e.O(0,[14438,31304,83464,2602,85323,2901,33157,28622,83113,92971,95030,1744],function(){return e(e.s=99185)}),_N_E=e.O()}]);