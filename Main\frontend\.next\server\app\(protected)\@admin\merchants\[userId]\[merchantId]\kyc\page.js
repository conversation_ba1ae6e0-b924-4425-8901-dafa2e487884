(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2675],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},58384:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>k,default:()=>w});var r,a={};s.r(a),s.d(a,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>f,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>x,pages:()=>h,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>g,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),s(67206);var n=s(79319),i=s(20518),o=s(61902),d=s(62042),c=s(44630),l=s(44828),m=s(65505),u=s(13839);let p=["",{children:["(protected)",{admin:["children",{children:["merchants",{children:["[userId]",{children:["[merchantId]",{children:["kyc",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,34e3)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\kyc\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,21981)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\kyc\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,26105)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,73722)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,76667)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,94626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\kyc\\page.tsx"],x="/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page",f={require:s,loadChunk:()=>Promise.resolve()},g=new c.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page",pathname:"/merchants/[userId]/[merchantId]/kyc",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var b=s(69094),v=s(5787),j=s(90527);let N=e=>e?JSON.parse(e):void 0,S=self.__BUILD_MANIFEST,y=N(self.__REACT_LOADABLE_MANIFEST),A=null==(r=self.__RSC_MANIFEST)?void 0:r["/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page"],P=N(self.__RSC_SERVER_MANIFEST),E=N(self.__NEXT_FONT_MANIFEST),I=N(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];A&&P&&(0,v.Mo)({clientReferenceManifest:A,serverActionsManifest:P,serverModuleMap:(0,j.w)({serverActionsManifest:P,pageName:"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page"})});let D=(0,i.d)({pagesType:b.s.APP,dev:!1,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page",appMod:null,pageMod:a,errorMod:null,error500Mod:null,Document:null,buildManifest:S,renderToHTML:d.f,reactLoadableManifest:y,clientReferenceManifest:A,serverActionsManifest:P,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:E,incrementalCacheHandler:null,interceptionRouteRewrites:I}),k=a;function w(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:D})}},83270:(e,t,s)=>{Promise.resolve().then(s.bind(s,89077))},31591:(e,t,s)=>{Promise.resolve().then(s.bind(s,13600))},35303:()=>{},89077:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var r=s(60926),a=s(58387),n=s(29411),i=s(59571),o=s(80317),d=s(28871),c=s(36162),l=s(68870),m=s(92207),u=s(40847),p=s(43291),h=s(43553),x=s(31809),f=s(66277),g=s(18825),b=s(32797),v=s(90543),j=s(51018),N=s(81279),S=s(66757),y=s(28277),A=s(64947),P=s(39228),E=s(32167);function I(){let e=(0,A.UO)(),{data:t,isLoading:s}=(0,p.d)(`/admin/merchants/${e.merchantId}`),{t:l}=(0,P.$G)(),N=(e,t)=>{E.toast.promise((0,u.$)(e,t),{loading:l("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return e.message},error:e=>e.message})},S=t?.data?.user?.kyc?new h.t(t.data.user.kyc):null;return(0,r.jsx)(i.UQ,{type:"multiple",defaultValue:["KYC_STATUS","DOCUMENT_INFORMATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,r.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(i.Qd,{value:"KYC_STATUS",className:"border-none px-4 py-0",children:[(0,r.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:l("KYC Status")}),(0,r.jsx)(a.J,{condition:!S,children:(0,r.jsx)(d.C,{className:"h-5 bg-foreground text-[10px] text-background",children:l("Awaiting Status")})}),(0,r.jsx)(a.J,{condition:S?.status==="pending",children:(0,r.jsx)(d.C,{className:"h-5 bg-primary text-[10px] text-primary-foreground",children:l("Pending")})}),(0,r.jsx)(a.J,{condition:S?.status==="verified",children:(0,r.jsx)(d.C,{className:"h-5 bg-spacial-green text-[10px] text-spacial-green-foreground",children:l("Verified")})}),(0,r.jsx)(a.J,{condition:S?.status==="failed",children:(0,r.jsx)(d.C,{className:"h-5 bg-danger text-[10px] text-destructive-foreground",children:l("Rejected")})})]})}),(0,r.jsxs)(i.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[(0,r.jsx)(a.J,{condition:!S,children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-foreground",children:[(0,r.jsx)(x.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:l("User have not submitted documents yet")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:l("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]})}),(0,r.jsx)(a.J,{condition:S?.status==="pending",children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-primary",children:[(0,r.jsx)(f.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:l("Pending verification")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:l("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]})}),(0,r.jsx)(a.J,{condition:S?.status==="verified",children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-spacial-green",children:[(0,r.jsx)(g.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:l("Your account is verified")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:l("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]})}),(0,r.jsx)(a.J,{condition:S?.status==="failed",children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-danger",children:[(0,r.jsx)(b.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:l("KYC Document Rejected")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:l("The submitted KYC document has been rejected. Please review the document for discrepancies or invalid details and request the user to submit accurate information for verification.")})]})})]})]})}),(0,r.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(i.Qd,{value:"DOCUMENT_INFORMATION",className:"border-none px-4 py-0",children:[(0,r.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("div",{className:"flex items-center gap-1",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:l("Documents")})})}),(0,r.jsx)(i.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:S?(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"max-w-[900px]",children:(0,r.jsxs)(m.iA,{className:"table-fixed",children:[(0,r.jsx)(m.xD,{className:"[&_tr]:border-b-0",children:(0,r.jsxs)(m.SC,{children:[(0,r.jsx)(m.ss,{children:l("KYC")}),(0,r.jsx)(m.ss,{children:l("Menu")})]})}),(0,r.jsx)(m.RM,{children:s?(0,r.jsx)(m.SC,{children:(0,r.jsx)(m.pj,{colSpan:2,children:(0,r.jsx)(n.Loader,{})})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(m.SC,{className:"odd:bg-accent",children:[(0,r.jsx)(m.pj,{children:l("Document type")}),(0,r.jsx)(m.pj,{children:(0,r.jsx)("div",{className:"flex items-center gap-1 sm:gap-10",children:(0,r.jsx)("span",{className:"hidden font-semibold sm:block",children:S?.documentType})})})]}),(0,r.jsx)(D,{title:l("Front image"),preview:S?.front}),(0,r.jsx)(D,{title:l("Back image"),preview:S?.back}),(0,r.jsx)(D,{title:l("Selfie"),preview:S?.selfie})]})})]})}),(0,r.jsx)(a.J,{condition:S.status?.toLowerCase()==="pending",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2.5 sm:gap-4",children:[(0,r.jsxs)(c.z,{type:"button",onClick:()=>N(S.id,"accept"),className:"bg-[#0B6A0B] text-white hover:bg-[#208c20]",children:[(0,r.jsx)(v.Z,{}),l("Approve")]}),(0,r.jsxs)(c.z,{type:"button",onClick:()=>N(S.id,"decline"),className:"bg-[#D13438] text-white hover:bg-[#c32d32]",children:[(0,r.jsx)(j.Z,{}),l("Reject")]})]})})]}):(0,r.jsx)("div",{className:"py-4",children:(0,r.jsx)("p",{className:"text-secondary-text",children:l("KYC Documents not submitted yet")})})})]})})]})})}function D({title:e,preview:t}){let{t:s}=(0,P.$G)();return(0,r.jsxs)(m.SC,{className:"odd:bg-accent",children:[(0,r.jsx)(m.pj,{children:e}),(0,r.jsx)(m.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-1 sm:gap-10",children:[(0,r.jsxs)(l.Vq,{children:[(0,r.jsx)(l.hg,{asChild:!0,children:(0,r.jsxs)(c.z,{type:"button",variant:"outline",className:"bg-background hover:bg-muted",children:[(0,r.jsx)(N.Z,{}),(0,r.jsx)("span",{className:"hidden sm:block",children:s("View")})]})}),(0,r.jsxs)(l.cZ,{className:"max-w-7xl",children:[(0,r.jsxs)(l.fK,{children:[(0,r.jsxs)(l.$N,{children:[" ",e," "]}),(0,r.jsx)(l.Be,{className:"hidden","aria-hidden":!0})]}),(0,r.jsx)("div",{className:"relative mx-auto aspect-square w-full max-w-xl",children:t?(0,r.jsx)(y.Z,{src:t,alt:e,fill:!0,sizes:"500px",style:{width:"100%",height:"100%"},quality:"90",loading:"lazy",placeholder:"blur",className:"object-contain",blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mN8Vw8AAmEBb87E6jIAAAAASUVORK5CYII="}):(0,r.jsx)("div",{className:"flex h-full w-full items-center justify-center rounded-md bg-accent",children:(0,r.jsx)("span",{className:"font-semibold opacity-70",children:s("No preview")})})})]})]}),(0,r.jsx)(c.z,{type:"button",variant:"outline",className:"bg-background hover:bg-muted",asChild:!0,children:(0,r.jsxs)("a",{href:t,download:!0,children:[(0,r.jsx)(S.Z,{}),(0,r.jsx)("span",{className:"hidden sm:inline-block",children:s("Download")})]})})]})})]})}},13600:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v,runtime:()=>b});var r=s(60926),a=s(14579),n=s(30417),i=s(89551),o=s(53042),d=s(44788),c=s(38071),l=s(28531),m=s(5764),u=s(47020),p=s(737),h=s(64947);s(29220);var x=s(39228),f=s(32167),g=s(91500);let b="edge";function v({children:e}){let t=(0,h.UO)(),s=(0,h.lr)(),b=(0,h.tv)(),v=(0,h.jD)(),{t:j}=(0,x.$G)(),N=[{title:j("Account Details"),icon:(0,r.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}?${s.toString()}`,id:"__DEFAULT__"},{title:j("Transactions"),icon:(0,r.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/transactions?${s.toString()}`,id:"transactions"},{title:j("KYC"),icon:(0,r.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/kyc?${s.toString()}`,id:"kyc"},{title:j("Fees"),icon:(0,r.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/fees?${s.toString()}`,id:"fees"},{title:j("Permissions"),icon:(0,r.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/permissions?${s.toString()}`,id:"permissions"},{title:j("Send Email"),icon:(0,r.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/send-email?${s.toString()}`,id:"send-email"}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,r.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,r.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,r.jsx)("li",{children:(0,r.jsxs)(p.Z,{href:"/merchants/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,r.jsx)(u.Z,{}),j("Back")]})}),(0,r.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",s.get("name")]}),(0,r.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",j("Merchant")," #",t.merchantId]})]}),(0,r.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,r.jsx)("span",{children:j("Active")}),(0,r.jsx)(n.Z,{defaultChecked:"1"===s.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:e=>{f.toast.promise((0,i.z)(t.userId),{loading:j("Loading..."),success:r=>{if(!r.status)throw Error(r.message);let a=new URLSearchParams(s);return(0,g.j)(`/admin/merchants/${t.merchantId}`),a.set("active",e?"1":"0"),b.push(`${v}?${a.toString()}`),r.message},error:e=>e.message})}})]})]}),(0,r.jsx)(a.a,{tabs:N})]}),e]})}},59571:(e,t,s)=>{"use strict";s.d(t,{Qd:()=>c,UQ:()=>d,o4:()=>l,vF:()=>m});var r=s(60926),a=s(73837),n=s(29220),i=s(65091),o=s(86059);let d=a.fC,c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.ck,{ref:s,className:(0,i.ZP)("border-b",e),...t}));c.displayName="AccordionItem";let l=n.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsx)(a.h4,{className:"flex",children:(0,r.jsxs)(a.xz,{ref:n,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...s,children:[t,(0,r.jsx)(o.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));l.displayName=a.xz.displayName;let m=n.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsx)(a.VY,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...s,children:(0,r.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",e),children:t})}));m.displayName=a.VY.displayName},80317:(e,t,s)=>{"use strict";s.d(t,{Cd:()=>c,X:()=>l,bZ:()=>d});var r=s(60926),a=s(8206),n=s(29220),i=s(65091);let o=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=n.forwardRef(({className:e,variant:t,...s},a)=>(0,r.jsx)("div",{ref:a,role:"alert",className:(0,i.ZP)(o({variant:t}),e),...s}));d.displayName="Alert";let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h5",{ref:s,className:(0,i.ZP)("mb-1 font-medium leading-none tracking-tight",e),...t}));c.displayName="AlertTitle";let l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.ZP)("text-sm [&_p]:leading-relaxed",e),...t}));l.displayName="AlertDescription"},28871:(e,t,s)=>{"use strict";s.d(t,{C:()=>o});var r=s(60926),a=s(8206);s(29220);var n=s(65091);let i=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,n.ZP)(i({variant:t}),e),...s})}},92207:(e,t,s)=>{"use strict";s.d(t,{RM:()=>d,SC:()=>c,iA:()=>i,pj:()=>m,ss:()=>l,xD:()=>o});var r=s(60926),a=s(29220),n=s(65091);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:s,className:(0,n.ZP)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("thead",{ref:s,className:(0,n.ZP)("",e),...t}));o.displayName="TableHeader";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tbody",{ref:s,className:(0,n.ZP)("[&_tr:last-child]:border-0",e),...t}));d.displayName="TableBody",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tfoot",{ref:s,className:(0,n.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tr",{ref:s,className:(0,n.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));c.displayName="TableRow";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("th",{ref:s,className:(0,n.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));l.displayName="TableHead";let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("td",{ref:s,className:(0,n.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));m.displayName="TableCell",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("caption",{ref:s,className:(0,n.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},89551:(e,t,s)=>{"use strict";s.d(t,{z:()=>n});var r=s(1181),a=s(25694);async function n(e){try{let t=await r.Z.put(`/admin/users/toggle-active/${e}`,{});return(0,a.B)(t)}catch(e){return(0,a.D)(e)}}},40847:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(1181),a=s(25694);let n=async(e,t)=>{try{let s=await r.Z.put(`/admin/kycs/${t}/${e}`,{});return(0,a.B)(s)}catch(e){return(0,a.D)(e)}}},43553:(e,t,s)=>{"use strict";s.d(t,{t:()=>a});var r=s(65091);class a{constructor(e){this.id=e?.id,this.userId=e?.userId,this.documentType=e?.documentType?.toUpperCase(),this.selfie=(0,r.qR)(e?.selfie),this.front=(0,r.qR)(e?.front),this.back=(0,r.qR)(e?.back),this.status=e?.status,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt)}}},21981:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(42416),a=s(21237);function n(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(a.a,{})})}},34e3:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\kyc\page.tsx#default`)},26105:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,runtime:()=>a});var r=s(18264);let a=(0,r.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#runtime`),n=(0,r.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#default`)},73722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(42416),a=s(21237);function n(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(a.a,{})})}},76667:(e,t,s)=>{"use strict";function r({children:e}){return e}s.r(t),s.d(t,{default:()=>r}),s(87908)},94626:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(42416),a=s(21237);function n(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(a.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,870,2607,7283,5089,3711],()=>t(58384));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page"]=s}]);
//# sourceMappingURL=page.js.map