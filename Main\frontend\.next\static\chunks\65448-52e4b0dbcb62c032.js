"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[65448],{65448:function(e,t,n){n.d(t,{a:function(){return u}});var r=n(57437),a=n(62869),o=n(84190),s=n(36887),i=n(27648),c=n(99376),d=n(2265),l=n(43949);function u(e){let{tabs:t,fullWidth:n=!0,defaultSegment:a}=e,[o,s]=(0,d.useState)(()=>t.map(e=>({...e,placeTo:"nav"}))),[i,l]=(0,d.useState)(""),u=(0,c.useSelectedLayoutSegment)();(0,d.useEffect)(()=>{l(u&&u!==a?u:"__DEFAULT__")},[u,a]);let p=(0,d.useCallback)((e,t)=>{s(n=>n.map(n=>n.id===e?{...n,placeTo:t}:n))},[]);return(0,r.jsxs)("div",{className:"inline-flex h-12 items-center rounded-lg bg-accent p-1 text-muted-foreground ".concat(n?"w-full":""),children:[o.map(e=>"nav"===e.placeTo?(0,r.jsx)(f,{...e,isActive:i===e.id,onClick:()=>l(e.id),updateTabPlace:p},e.id):null),(0,r.jsx)("div",{className:"ml-auto",children:(0,r.jsx)(m,{navItems:o,activeTabId:i})})]})}function f(e){let{title:t,id:n,icon:a,href:o,isActive:s,onClick:c,updateTabPlace:l}=e,u=(0,d.useRef)(null),f=(0,d.useCallback)(()=>{var e,t;let r=null===(e=u.current)||void 0===e?void 0:e.getBoundingClientRect(),a=null===(t=window)||void 0===t?void 0:t.innerWidth;r&&a<r.right+150?l(n,"menu"):l(n,"nav")},[n,l]);return(0,d.useEffect)(()=>(f(),window.addEventListener("resize",f),()=>{window.removeEventListener("resize",f)}),[f]),(0,r.jsxs)(i.default,{href:o,"data-state":s?"active":"",onClick:c,prefetch:!1,ref:u,className:"inline-flex h-10 w-56 items-center justify-center gap-1 whitespace-nowrap rounded-md px-4 py-1.5 text-sm font-medium text-secondary-text ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[a,(0,r.jsx)("span",{children:t})]})}function m(e){let{navItems:t,activeTabId:n}=e,[c,u]=d.useState(!1),f=t.filter(e=>"menu"===e.placeTo),{t:m}=(0,l.$G)();return 0===f.length?null:(0,r.jsxs)(o.h_,{open:c,onOpenChange:u,children:[(0,r.jsx)(o.$F,{asChild:!0,children:(0,r.jsxs)(a.z,{variant:"outline",className:"h-10 text-sm font-medium",children:[m("More"),(0,r.jsx)(s.Z,{size:16})]})}),(0,r.jsx)(o.AW,{children:f.map(e=>(0,r.jsx)(o.Xi,{"data-active":n===e.id,className:"data-[active=true]:bg-accent",children:(0,r.jsxs)(i.default,{href:e.href,prefetch:!1,onClick:()=>u(!1),className:"flex h-full w-full items-center gap-2",children:[e.icon,(0,r.jsx)("span",{children:e.title})]})},e.id))})]})}},62869:function(e,t,n){n.d(t,{d:function(){return c},z:function(){return d}});var r=n(57437),a=n(37053),o=n(90535),s=n(2265),i=n(94508);let c=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:n,variant:o,size:s,asChild:d=!1,...l}=e,u=d?a.g7:"button";return(0,r.jsx)(u,{className:(0,i.ZP)(c({variant:o,size:s,className:n})),ref:t,...l})});d.displayName="Button"},84190:function(e,t,n){n.d(t,{$F:function(){return u},AW:function(){return f},VD:function(){return p},Xi:function(){return m},h_:function(){return l}});var r=n(57437),a=n(70085),o=n(10407),s=n(30401),i=n(40519),c=n(2265),d=n(94508);let l=a.fC,u=a.xz;a.ZA,a.Uv,a.Tr,a.Ee,c.forwardRef((e,t)=>{let{className:n,inset:s,children:i,...c}=e;return(0,r.jsxs)(a.fF,{ref:t,className:(0,d.ZP)("items-left my-1 flex h-8 cursor-pointer select-none rounded-sm px-3 py-1 text-sm font-bold text-secondary-800 outline-none hover:bg-secondary focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",n),...c,children:[i,(0,r.jsx)(o.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=a.fF.displayName,c.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,r.jsx)(a.tu,{ref:t,className:(0,d.ZP)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...o})}).displayName=a.tu.displayName;let f=c.forwardRef((e,t)=>{let{className:n,sideOffset:o=4,...s}=e;return(0,r.jsx)(a.Uv,{children:(0,r.jsx)(a.VY,{ref:t,sideOffset:o,className:(0,d.ZP)("z-50 mx-5 min-w-[253px] overflow-hidden rounded-md border bg-white p-1 text-secondary-800 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...s})})});f.displayName=a.VY.displayName;let m=c.forwardRef((e,t)=>{let{className:n,onClick:o,inset:s,...i}=e;return(0,r.jsx)(a.ck,{ref:t,className:(0,d.ZP)("relative my-1 flex h-8 cursor-pointer select-none items-center rounded px-3 py-1 text-sm font-medium text-secondary-800 outline-none transition-colors hover:bg-secondary focus:bg-secondary focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",n),...i})});m.displayName=a.ck.displayName,c.forwardRef((e,t)=>{let{className:n,onClick:o,inset:s,...i}=e;return(0,r.jsx)(a.ck,{ref:t,onClick:e=>{e.preventDefault(),o&&o(e)},className:(0,d.ZP)("focus:secondary relative my-1 flex h-8 cursor-pointer select-none items-center rounded px-3 py-1 text-sm outline-none transition-colors hover:bg-secondary focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",n),...i})}).displayName=a.ck.displayName,c.forwardRef((e,t)=>{let{className:n,children:o,checked:i,...c}=e;return(0,r.jsxs)(a.oC,{ref:t,className:(0,d.ZP)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),checked:i,...c,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.wU,{children:(0,r.jsx)(s.Z,{className:"h-4 w-4"})})}),o]})}).displayName=a.oC.displayName,c.forwardRef((e,t)=>{let{className:n,children:o,...s}=e;return(0,r.jsxs)(a.Rk,{ref:t,className:(0,d.ZP)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.wU,{children:(0,r.jsx)(i.Z,{className:"h-2 w-2 fill-current"})})}),o]})}).displayName=a.Rk.displayName,c.forwardRef((e,t)=>{let{className:n,inset:o,...s}=e;return(0,r.jsx)(a.__,{ref:t,className:(0,d.ZP)("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",n),...s})}).displayName=a.__.displayName;let p=c.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,r.jsx)(a.Z0,{ref:t,className:(0,d.ZP)("-mx-1 my-1 h-px bg-muted",n),...o})});p.displayName=a.Z0.displayName},78040:function(e,t,n){n.d(t,{rH:function(){return r},sp:function(){return a}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){n.d(t,{F:function(){return l},Fg:function(){return m},Fp:function(){return d},Qp:function(){return f},ZP:function(){return i},fl:function(){return c},qR:function(){return u},w4:function(){return p}});var r=n(78040),a=n(61994),o=n(14438),s=n(53335);function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.m6)((0,a.W)(t))}function c(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>o.toast.success("Copied to clipboard!")).catch(()=>{o.toast.error("Failed to copy!")})};class l{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let a;let o=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:o,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let s=null!==(r=null===(n=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:o,i=a.format(e),c=i.substring(s.length).trim();return{currencyCode:o,currencySymbol:s,formattedAmount:i,amountText:c}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?a.set(r,e):a.delete(r),a}}}]);