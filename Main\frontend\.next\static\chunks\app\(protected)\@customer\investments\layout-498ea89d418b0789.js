(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[45408,97702,46402],{97325:function(e,n,t){Promise.resolve().then(t.bind(t,39828))},39828:function(e,n,t){"use strict";t.d(n,{Tabbar:function(){return c}});var i=t(57437),s=t(65448),a=t(99545),l=t(56157),r=t(43949);function c(){let{t:e}=(0,r.$G)(),n=[{title:e("My Investments"),icon:(0,i.jsx)(a.Z,{size:"24",variant:"Bulk"}),href:"/investments/",id:"__DEFAULT__"},{title:e("Available Plans"),icon:(0,i.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:"/investments/available-plans",id:"available-plans"}];return(0,i.jsx)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-black/[8%] bg-white p-4",children:(0,i.jsx)(s.a,{tabs:n})})}}},function(e){e.O(0,[14438,31304,5062,80566,93909,28453,27648,48248,13015,65448,92971,95030,1744],function(){return e(e.s=97325)}),_N_E=e.O()}]);