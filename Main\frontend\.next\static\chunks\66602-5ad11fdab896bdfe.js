"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[66602],{22291:function(e,t,n){n.d(t,{Z:function(){return p}});var r=n(74677),o=n(2265),i=n(40718),a=n.n(i),l=["variant","color","size"],u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),o.createElement("path",{fill:t,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},f=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},h=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},m=function(e,t){switch(e){case"Bold":return o.createElement(u,{color:t});case"Broken":return o.createElement(c,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(f,{color:t});case"TwoTone":return o.createElement(h,{color:t})}},p=(0,o.forwardRef)(function(e,t){var n=e.variant,i=e.color,a=e.size,u=(0,r._)(e,l);return o.createElement("svg",(0,r.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),m(n,i))});p.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},p.defaultProps={variant:"Linear",color:"currentColor",size:"24"},p.displayName="ArrowRight2"},25523:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(47043)._(n(2265)).default.createContext(null)},99255:function(e,t,n){n.d(t,{M:function(){return u}});var r,o=n(2265),i=n(61188),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},71599:function(e,t,n){n.d(t,{z:function(){return a}});var r=n(2265),o=n(98575),i=n(61188),a=e=>{var t,n;let a,u;let{present:c,children:s}=e,d=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=l(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(c),f="function"==typeof s?s({present:d.isPresent}):r.Children.only(s),h=(0,o.e)(d.ref,(a=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in a&&a.isReactWarning?f.ref:(a=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in a&&a.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?r.cloneElement(f,{ref:h}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},1353:function(e,t,n){n.d(t,{Pc:function(){return E},ck:function(){return j},fC:function(){return I}});var r=n(2265),o=n(6741),i=n(58068),a=n(98575),l=n(73966),u=n(99255),c=n(66840),s=n(26606),d=n(80886),f=n(29114),h=n(57437),m="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,b]=(0,i.B)(v),[w,E]=(0,l.b)(v,[b]),[M,T]=w(v),N=r.forwardRef((e,t)=>(0,h.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(k,{...e,ref:t})})}));N.displayName=v;var k=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:E,preventScrollOnEntryFocus:T=!1,...N}=e,k=r.useRef(null),x=(0,a.e)(t,k),A=(0,f.gm)(u),[O,I]=(0,d.T)({prop:g,defaultProp:null!=b?b:null,onChange:w,caller:v}),[j,F]=r.useState(!1),L=(0,s.W)(E),C=y(n),S=r.useRef(!1),[D,P]=r.useState(0);return r.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(m,L),()=>e.removeEventListener(m,L)},[L]),(0,h.jsx)(M,{scope:n,orientation:i,dir:A,loop:l,currentTabStopId:O,onItemFocus:r.useCallback(e=>I(e),[I]),onItemShiftTab:r.useCallback(()=>F(!0),[]),onFocusableItemAdd:r.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>P(e=>e-1),[]),children:(0,h.jsx)(c.WV.div,{tabIndex:j||0===D?-1:0,"data-orientation":i,...N,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(m,p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=C().filter(e=>e.focusable);R([e.find(e=>e.active),e.find(e=>e.id===O),...e].filter(Boolean).map(e=>e.ref.current),T)}}S.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>F(!1))})})}),x="RovingFocusGroupItem",A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:s,...d}=e,f=(0,u.M)(),m=l||f,p=T(x,n),v=p.currentTabStopId===m,b=y(n),{onFocusableItemAdd:w,onFocusableItemRemove:E,currentTabStopId:M}=p;return r.useEffect(()=>{if(i)return w(),()=>E()},[i,w,E]),(0,h.jsx)(g.ItemSlot,{scope:n,id:m,focusable:i,active:a,children:(0,h.jsx)(c.WV.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i?p.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>p.onItemFocus(m)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let i=o.indexOf(e.currentTarget);o=p.loop?(n=o,r=i+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(i+1)}setTimeout(()=>R(o))}}),children:"function"==typeof s?s({isCurrentTabStop:v,hasTabStop:null!=M}):s})})});A.displayName=x;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function R(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=N,j=A},20271:function(e,t,n){n.d(t,{VY:function(){return j},aV:function(){return R},fC:function(){return O},xz:function(){return I}});var r=n(2265),o=n(6741),i=n(73966),a=n(1353),l=n(71599),u=n(66840),c=n(29114),s=n(80886),d=n(99255),f=n(57437),h="Tabs",[m,p]=(0,i.b)(h,[a.Pc]),v=(0,a.Pc)(),[g,y]=m(h),b=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:l,activationMode:m="automatic",...p}=e,v=(0,c.gm)(l),[y,b]=(0,s.T)({prop:r,onChange:o,defaultProp:null!=i?i:"",caller:h});return(0,f.jsx)(g,{scope:n,baseId:(0,d.M)(),value:y,onValueChange:b,orientation:a,dir:v,activationMode:m,children:(0,f.jsx)(u.WV.div,{dir:v,"data-orientation":a,...p,ref:t})})});b.displayName=h;var w="TabsList",E=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=y(w,n),l=v(n);return(0,f.jsx)(a.fC,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,f.jsx)(u.WV.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});E.displayName=w;var M="TabsTrigger",T=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...l}=e,c=y(M,n),s=v(n),d=x(c.baseId,r),h=A(c.baseId,r),m=r===c.value;return(0,f.jsx)(a.ck,{asChild:!0,...s,focusable:!i,active:m,children:(0,f.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":h,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...l,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;m||i||!e||c.onValueChange(r)})})})});T.displayName=M;var N="TabsContent",k=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:a,...c}=e,s=y(N,n),d=x(s.baseId,o),h=A(s.baseId,o),m=o===s.value,p=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.z,{present:i||m,children:n=>{let{present:r}=n;return(0,f.jsx)(u.WV.div,{"data-state":m?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:h,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&a})}})});function x(e,t){return"".concat(e,"-trigger-").concat(t)}function A(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=N;var O=b,R=E,I=T,j=k},26606:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},55988:function(e,t,n){n.d(t,{EQ:function(){return T}});let r=Symbol.for("@ts-pattern/matcher"),o=Symbol.for("@ts-pattern/isVariadic"),i="@ts-pattern/anonymous-select-key",a=e=>!!(e&&"object"==typeof e),l=e=>e&&!!e[r],u=(e,t,n)=>{if(l(e)){let{matched:o,selections:i}=e[r]().match(t);return o&&i&&Object.keys(i).forEach(e=>n(e,i[e])),o}if(a(e)){if(!a(t))return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let r=[],i=[],a=[];for(let t of e.keys()){let n=e[t];l(n)&&n[o]?a.push(n):a.length?i.push(n):r.push(n)}if(a.length){if(a.length>1)throw Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<r.length+i.length)return!1;let e=t.slice(0,r.length),o=0===i.length?[]:t.slice(-i.length),l=t.slice(r.length,0===i.length?1/0:-i.length);return r.every((t,r)=>u(t,e[r],n))&&i.every((e,t)=>u(e,o[t],n))&&(0===a.length||u(a[0],l,n))}return e.length===t.length&&e.every((e,r)=>u(e,t[r],n))}return Reflect.ownKeys(e).every(o=>{let i=e[o];return(o in t||l(i)&&"optional"===i[r]().matcherType)&&u(i,t[o],n)})}return Object.is(t,e)},c=e=>{var t,n,o;return a(e)?l(e)?null!=(t=null==(n=(o=e[r]()).getSelectionKeys)?void 0:n.call(o))?t:[]:Array.isArray(e)?s(e,c):s(Object.values(e),c):[]},s=(e,t)=>e.reduce((e,n)=>e.concat(t(n)),[]);function d(e){return Object.assign(e,{optional:()=>d({[r]:()=>({match:t=>{let n={},r=(e,t)=>{n[e]=t};return void 0===t?(c(e).forEach(e=>r(e,void 0)),{matched:!0,selections:n}):{matched:u(e,t,r),selections:n}},getSelectionKeys:()=>c(e),matcherType:"optional"})}),and:t=>f(e,t),or:t=>(function(...e){return d({[r]:()=>({match:t=>{let n={},r=(e,t)=>{n[e]=t};return s(e,c).forEach(e=>r(e,void 0)),{matched:e.some(e=>u(e,t,r)),selections:n}},getSelectionKeys:()=>s(e,c),matcherType:"or"})})})(e,t),select:t=>void 0===t?m(e):m(t,e)})}function f(...e){return d({[r]:()=>({match:t=>{let n={},r=(e,t)=>{n[e]=t};return{matched:e.every(e=>u(e,t,r)),selections:n}},getSelectionKeys:()=>s(e,c),matcherType:"and"})})}function h(e){return{[r]:()=>({match:t=>({matched:!!e(t)})})}}function m(...e){let t="string"==typeof e[0]?e[0]:void 0,n=2===e.length?e[1]:"string"==typeof e[0]?void 0:e[0];return d({[r]:()=>({match:e=>{let r={[null!=t?t:i]:e};return{matched:void 0===n||u(n,e,(e,t)=>{r[e]=t}),selections:r}},getSelectionKeys:()=>[null!=t?t:i].concat(void 0===n?[]:c(n))})})}function p(e){return"number"==typeof e}function v(e){return"string"==typeof e}function g(e){return"bigint"==typeof e}d(h(function(e){return!0}));let y=e=>Object.assign(d(e),{startsWith:t=>y(f(e,h(e=>v(e)&&e.startsWith(t)))),endsWith:t=>y(f(e,h(e=>v(e)&&e.endsWith(t)))),minLength:t=>y(f(e,h(e=>v(e)&&e.length>=t))),length:t=>y(f(e,h(e=>v(e)&&e.length===t))),maxLength:t=>y(f(e,h(e=>v(e)&&e.length<=t))),includes:t=>y(f(e,h(e=>v(e)&&e.includes(t)))),regex:t=>y(f(e,h(e=>v(e)&&!!e.match(t))))}),b=(y(h(v)),e=>Object.assign(d(e),{between:(t,n)=>b(f(e,h(e=>p(e)&&t<=e&&n>=e))),lt:t=>b(f(e,h(e=>p(e)&&e<t))),gt:t=>b(f(e,h(e=>p(e)&&e>t))),lte:t=>b(f(e,h(e=>p(e)&&e<=t))),gte:t=>b(f(e,h(e=>p(e)&&e>=t))),int:()=>b(f(e,h(e=>p(e)&&Number.isInteger(e)))),finite:()=>b(f(e,h(e=>p(e)&&Number.isFinite(e)))),positive:()=>b(f(e,h(e=>p(e)&&e>0))),negative:()=>b(f(e,h(e=>p(e)&&e<0)))})),w=(b(h(p)),e=>Object.assign(d(e),{between:(t,n)=>w(f(e,h(e=>g(e)&&t<=e&&n>=e))),lt:t=>w(f(e,h(e=>g(e)&&e<t))),gt:t=>w(f(e,h(e=>g(e)&&e>t))),lte:t=>w(f(e,h(e=>g(e)&&e<=t))),gte:t=>w(f(e,h(e=>g(e)&&e>=t))),positive:()=>w(f(e,h(e=>g(e)&&e>0))),negative:()=>w(f(e,h(e=>g(e)&&e<0)))}));w(h(g)),d(h(function(e){return"boolean"==typeof e})),d(h(function(e){return"symbol"==typeof e})),d(h(function(e){return null==e})),d(h(function(e){return null!=e}));class E extends Error{constructor(e){let t;try{t=JSON.stringify(e)}catch(n){t=e}super(`Pattern matching error: no pattern matches value ${t}`),this.input=void 0,this.input=e}}let M={matched:!1,value:void 0};function T(e){return new N(e,M)}class N{constructor(e,t){this.input=void 0,this.state=void 0,this.input=e,this.state=t}with(...e){let t;if(this.state.matched)return this;let n=e[e.length-1],r=[e[0]];3===e.length&&"function"==typeof e[1]?t=e[1]:e.length>2&&r.push(...e.slice(1,e.length-1));let o=!1,a={},l=(e,t)=>{o=!0,a[e]=t},c=r.some(e=>u(e,this.input,l))&&(!t||t(this.input))?{matched:!0,value:n(o?i in a?a[i]:a:this.input,this.input)}:M;return new N(this.input,c)}when(e,t){if(this.state.matched)return this;let n=!!e(this.input);return new N(this.input,n?{matched:!0,value:t(this.input,this.input)}:M)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(e=k){return this.state.matched?this.state.value:e(this.input)}run(){return this.exhaustive()}returnType(){return this}}function k(e){throw new E(e)}}}]);