(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[77902],{59176:function(i,t,e){Promise.resolve().then(e.bind(e,31007))},31007:function(i,t,e){"use strict";e.d(t,{Tabbar:function(){return f}});var s=e(57437),n=e(65448),r=e(32293),o=e(74337),a=e(78210),c=e(5205),u=e(95550),l=e(22076),g=e(43949);function f(){let{t:i}=(0,g.$G)(),t=[{title:i("Account Settings"),icon:(0,s.jsx)(r.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:i("KYC Verification"),icon:(0,s.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:i("Merchant Settings"),icon:(0,s.jsx)(a.Z,{size:"24",variant:"Bulk"}),href:"/settings/merchant-settings",id:"merchant-settings"},{title:i("MPay API"),icon:(0,s.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:"/settings/mpay-api",id:"mpay-api"},{title:i("Webhook URL"),icon:(0,s.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:"/settings/webhook-url-settings",id:"webhook-url-settings"},{title:i("Login Sessions"),icon:(0,s.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return(0,s.jsx)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:(0,s.jsx)(n.a,{tabs:t})})}}},function(i){i.O(0,[14438,31304,5062,80566,93909,28453,27648,48248,24872,58699,65448,92971,95030,1744],function(){return i(i.s=59176)}),_N_E=i.O()}]);