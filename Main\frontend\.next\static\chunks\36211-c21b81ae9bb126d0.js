"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[36211],{37781:function(e,t,n){n.d(t,{T:function(){return d}});var i=n(57437),r=n(62869),s=n(78040),a=n(94508),o=n(80093),l=n(43949);function d(e){let{trxId:t,className:n}=e,{t:d}=(0,l.$G)();return(0,i.jsx)(r.z,{variant:"outline",type:"button",className:(0,a.ZP)("w-full md:w-auto",n),asChild:!0,children:(0,i.jsxs)("a",{href:"".concat(s.rH.API_URL,"/transactions/download-receipt/").concat(t),children:[(0,i.jsx)(o.Z,{size:16}),(0,i.jsx)("span",{children:d("Download Receipt")})]})})}},80114:function(e,t,n){n.d(t,{default:function(){return o}});var i=n(57437),r=n(85487),s=n(94508),a=n(43949);function o(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,i.jsx)("div",{className:(0,s.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,i.jsx)(r.Loader,{title:n("Loading..."),className:"text-foreground"})})}},27300:function(e,t,n){n.d(t,{Z:function(){return o}});var i=n(57437),r=n(94508),s=n(27648),a=n(43949);function o(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,i.jsx)("div",{className:(0,r.ZP)("flex items-center justify-center",t),children:(0,i.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[(0,i.jsx)("h3",{className:"mb-2.5",children:n("This feature is temporarily unavailable")}),(0,i.jsxs)("p",{className:"text-sm text-secondary-text",children:[n("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),(0,i.jsx)(s.default,{href:"/contact-supports",className:"text-primary hover:underline",children:n("support")}),"."]}),(0,i.jsx)("p",{className:"mt-2 text-sm text-secondary-text",children:n("Thank you for your understanding.")})]})})}},70880:function(e,t,n){n.d(t,{Q:function(){return c},R:function(){return d}});var i=n(57437),r=n(12339),s=n(94508),a=n(19571),o=n(2265),l=n(6512);function d(e){let{value:t="",tabs:n=[],children:d,onTabChange:c}=e,[u,h]=o.useState(0),m=n.filter(e=>void 0===e.isVisible||!0===e.isVisible),f=m.findIndex(e=>e.value===t),v=m.length;return o.useEffect(()=>{h((f+1)/v*100)},[f,v,t]),(0,i.jsxs)(r.mQ,{value:t,onValueChange:c,children:[(0,i.jsx)("div",{className:"hidden h-0.5 w-full bg-background-body md:flex",children:(0,i.jsx)(l.Z,{className:(0,s.ZP)("h-0.5 bg-primary transition-[width] duration-200"),style:{width:"".concat(u,"%")}})}),(0,i.jsx)(r.dr,{className:"hidden bg-transparent md:flex",children:m.map((e,t)=>(0,i.jsxs)(r.SP,{value:e.value,disabled:t>f,"data-complete":e.complete,className:"ring-none group h-8 justify-start rounded-lg border-none border-border px-3 text-sm font-normal leading-5 text-foreground shadow-none outline-none transition-all duration-200 hover:bg-accent hover:text-primary data-[state=active]:bg-transparent data-[complete=true]:text-primary data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:hover:bg-accent",children:[(0,i.jsx)(a.Z,{size:19,className:"mr-2 group-hover:text-primary",variant:e.complete?"Bold":"Linear"}),e.title]},e.value))}),d]})}function c(e){let{children:t,...n}=e;return(0,i.jsx)(r.nU,{...n,children:t})}},3697:function(e,t,n){n.d(t,{T:function(){return d}});var i=n(57437),r=n(62869),s=n(94508),a=n(43271),o=n(43949),l=n(14438);function d(e){let{id:t,className:n}=e,{t:d}=(0,o.$G)();return(0,i.jsxs)("div",{className:(0,s.ZP)("inline-flex w-full items-center gap-4",n),children:[(0,i.jsx)("div",{className:"flex-1",children:d("Transaction ID")}),(0,i.jsxs)("div",{className:"inline-flex items-center gap-4",children:[(0,i.jsx)("span",{children:t}),(0,i.jsx)(r.z,{type:"button",onClick:()=>{navigator.clipboard.writeText(t).then(()=>l.toast.success("Copied to clipboard!")).catch(()=>{l.toast.error("Failed to copy!")})},variant:"outline",size:"sm",children:(0,i.jsx)(a.Z,{size:"20"})})]})]})}},87806:function(e,t,n){n.d(t,{z:function(){return l}});var i=n(57437),r=n(16831),s=n(94508),a=n(59532),o=n(19571);function l(e){let{senderName:t,senderAvatar:n,senderInfo:r,receiverName:a,receiverAvatar:o,receiverInfo:l,className:c}=e;return(0,i.jsxs)("div",{className:(0,s.ZP)("mb-4 flex items-start justify-around gap-1",c),children:[(0,i.jsx)(d,{name:t,avatar:n,info:r}),a&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),(0,i.jsx)(d,{name:a,avatar:o,info:l})]})]})}function d(e){let{avatar:t,name:n,info:s=[]}=e,l=s.filter(Boolean);return(0,i.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,i.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,i.jsxs)(r.qE,{className:"size-10 rounded-full sm:size-14",children:[(0,i.jsx)(r.F$,{src:t,alt:n,width:56,height:56}),(0,i.jsx)(r.Q5,{className:"font-semibold",children:(0,a.v)(n)})]}),(0,i.jsx)("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:(0,i.jsx)(o.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:n}),l.length>0&&l.map((e,t)=>(0,i.jsx)("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},t))]})]})}},45932:function(e,t,n){n.d(t,{R:function(){return f}});var i=n(57437),r=n(41709),s=n(33145),a=n(43949);function o(e){let{walletId:t,logo:n,name:r,balance:o,selectedWallet:l,onSelect:d,id:c}=e,{t:u}=(0,a.$G)();return(0,i.jsxs)("label",{htmlFor:"wallet-".concat(t,"-").concat(c),"data-active":t===l,className:"relative flex w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 px-6 py-4 transition-all duration-300 ease-linear hover:border-transparent hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,i.jsx)("input",{type:"radio",id:"wallet-".concat(t,"-").concat(c),checked:t===l,onChange:()=>d(t),className:"absolute inset-0 left-0 top-0 z-10 cursor-pointer opacity-0"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,i.jsx)(s.default,{src:n,alt:r,width:100,height:100,className:"size-8"}),(0,i.jsx)("h6",{className:"text-sm font-bold leading-5",children:r})]}),(0,i.jsxs)("div",{className:"mt-2.5",children:[(0,i.jsx)("p",{className:"text-xs font-normal leading-4 text-foreground",children:u("Your Balance")}),(0,i.jsx)("p",{className:"text-base font-medium leading-[22px]",children:Number(o).toFixed(2)})]})]})}var l=n(62869),d=n(93022),c=n(48358),u=n(66605),h=n(36887),m=n(2265);let f=(0,m.forwardRef)(function(e,t){var n;let{value:s,onChange:f,id:v}=e,{t:p}=(0,a.$G)(),[x,g]=m.useState(!1),{wallets:b,isLoading:y}=(0,c.r)(),w=m.useMemo(()=>b,[b]);return(m.useEffect(()=>{let e=w.find(e=>e.defaultStatus);e&&!s&&f(null==e?void 0:e.currency.code)},[w]),y)?(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:[(0,i.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,i.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,i.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"})]}):(0,i.jsxs)("div",{ref:t,id:v,children:[(0,i.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:null===(n=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;return t?e:e.slice(0,n)}(b,x))||void 0===n?void 0:n.map(e=>(null==e?void 0:e.currency.code)&&(0,i.jsx)(m.Fragment,{children:(0,i.jsx)(o,{walletId:null==e?void 0:e.currency.code,logo:e.logo,name:null==e?void 0:e.currency.code,balance:e.balance,selectedWallet:s,onSelect:f,id:v})},e.walletId))}),(0,i.jsx)(r.J,{condition:(null==b?void 0:b.length)>3,children:(0,i.jsx)("div",{className:"mt-2 flex justify-end",children:(0,i.jsxs)(l.z,{type:"button",variant:"link",onClick:()=>g(!x),className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[(0,i.jsx)("span",{className:"text-inherit",children:p(x?"Show less":"Show more")}),x?(0,i.jsx)(u.Z,{size:12}):(0,i.jsx)(h.Z,{size:12})]})})})]})})},35974:function(e,t,n){n.d(t,{C:function(){return o}});var i=n(57437),r=n(90535);n(2265);var s=n(94508);let a=(0,r.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:n,...r}=e;return(0,i.jsx)("div",{className:(0,s.ZP)(a({variant:n}),t),...r})}},15681:function(e,t,n){n.d(t,{NI:function(){return p},Wi:function(){return u},l0:function(){return d},lX:function(){return v},xJ:function(){return f},zG:function(){return x}});var i=n(57437),r=n(37053),s=n(2265),a=n(29501),o=n(26815),l=n(94508);let d=a.RV,c=s.createContext({}),u=e=>{let{...t}=e;return(0,i.jsx)(c.Provider,{value:{name:t.name},children:(0,i.jsx)(a.Qr,{...t})})},h=()=>{let e=s.useContext(c),t=s.useContext(m),{getFieldState:n,formState:i}=(0,a.Gc)(),r=n(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...r}},m=s.createContext({}),f=s.forwardRef((e,t)=>{let{className:n,...r}=e,a=s.useId();return(0,i.jsx)(m.Provider,{value:{id:a},children:(0,i.jsx)("div",{ref:t,className:(0,l.ZP)("space-y-2",n),...r})})});f.displayName="FormItem";let v=s.forwardRef((e,t)=>{let{className:n,required:r,...s}=e,{error:a,formItemId:d}=h();return(0,i.jsx)("span",{children:(0,i.jsx)(o.Z,{ref:t,className:(0,l.ZP)(a&&"text-base font-medium text-destructive",n),htmlFor:d,...s})})});v.displayName="FormLabel";let p=s.forwardRef((e,t)=>{let{...n}=e,{error:s,formItemId:a,formDescriptionId:o,formMessageId:l}=h();return(0,i.jsx)(r.g7,{ref:t,id:a,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...n})});p.displayName="FormControl",s.forwardRef((e,t)=>{let{className:n,...r}=e,{formDescriptionId:s}=h();return(0,i.jsx)("p",{ref:t,id:s,className:(0,l.ZP)("text-sm text-muted-foreground",n),...r})}).displayName="FormDescription";let x=s.forwardRef((e,t)=>{let{className:n,children:r,...s}=e,{error:a,formMessageId:o}=h(),d=a?String(null==a?void 0:a.message):r;return d?(0,i.jsx)("p",{ref:t,id:o,className:(0,l.ZP)("text-sm font-medium text-destructive",n),...s,children:d}):null});x.displayName="FormMessage"},12339:function(e,t,n){n.d(t,{SP:function(){return d},dr:function(){return l},mQ:function(){return o},nU:function(){return c}});var i=n(57437),r=n(2265),s=n(20271),a=n(94508);let o=s.fC,l=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,i.jsx)(s.aV,{ref:t,className:(0,a.ZP)("inline-flex h-10 w-full items-center justify-center rounded-md bg-secondary p-1 text-muted-foreground",n),...r})});l.displayName=s.aV.displayName;let d=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,i.jsx)(s.xz,{ref:t,className:(0,a.ZP)("inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-semibold text-secondary-800 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite",n),...r})});d.displayName=s.xz.displayName;let c=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,i.jsx)(s.VY,{ref:t,className:(0,a.ZP)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",n),...r})});c.displayName=s.VY.displayName},17062:function(e,t,n){n.d(t,{Z:function(){return v},O:function(){return f}});var i=n(57437),r=n(80114);n(83079);var s=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=n(31117),o=n(79981),l=n(78040),d=n(83130);class c{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var u=n(99376),h=n(2265);let m=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),f=()=>h.useContext(m);function v(e){let{children:t}=e,[n,f]=h.useState("Desktop"),[v,p]=h.useState(!1),[x,g]=h.useState(),{data:b,isLoading:y,error:w,mutate:j}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:N,isLoading:A}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:C,isLoading:k}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),I=(0,u.useRouter)(),z=(0,u.usePathname)();h.useEffect(()=>{(async()=>{f((await s()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;f(e<768?"Mobile":e<1024?"Tablet":"Desktop"),p(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");g(new c(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{w&&!l.sp.includes(z)&&I.push("/signin")},[w]);let F=h.useMemo(()=>{var e,t,i;return{isAuthenticate:!!(null==b?void 0:null===(e=b.data)||void 0===e?void 0:e.login),auth:(null==b?void 0:null===(t=b.data)||void 0===t?void 0:t.user)?new d.n(null==b?void 0:null===(i=b.data)||void 0===i?void 0:i.user):null,isLoading:y,deviceLocation:x,refreshAuth:()=>j(b),isExpanded:v,device:n,setIsExpanded:p,branding:null==N?void 0:N.data,googleAnalytics:(null==C?void 0:C.data)?{active:null==C?void 0:C.data.active,apiKey:null==C?void 0:C.data.apiKey}:{active:!1,apiKey:""}}},[b,x,v,n]),Z=!y&&!A&&!k;return(0,i.jsx)(m.Provider,{value:F,children:Z?t:(0,i.jsx)(r.default,{})})}},70569:function(e,t,n){n.d(t,{y:function(){return s}});var i=n(79981),r=n(97751);async function s(e,t){try{let n=await i.Z.put("".concat(null!=t?t:"/transactions/toggle-bookmark","/").concat(e),{id:e});return(0,r.B)(n)}catch(e){return(0,r.D)(e)}}},3612:function(e,t,n){n.d(t,{a:function(){return r}});var i=n(17062);let r=()=>{let e=(0,i.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},31117:function(e,t,n){n.d(t,{d:function(){return s}});var i=n(79981),r=n(85323);let s=(e,t)=>(0,r.ZP)(e||null,e=>i.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},74539:function(e,t,n){n.d(t,{k:function(){return i}});class i{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){n.d(t,{n:function(){return l}});class i{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var r=n(84937);class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=n(66419),o=n(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new r.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new s(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new i(e.agent):void 0}}},84937:function(e,t,n){n.d(t,{O:function(){return r}});var i=n(74539);class r{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new i.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){n.d(t,{u:function(){return i}});class i{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},59532:function(e,t,n){n.d(t,{v:function(){return i}});function i(e){if(!e)return"";let t=e.split(" ");return(t.length>2?t[0].length>3?t[0][0]+t[t.length-1][0]:t[1][0]+t[t.length-1][0]:2===t.length?t[0][0]+t[1][0]:t[0][0]).toUpperCase()}}}]);