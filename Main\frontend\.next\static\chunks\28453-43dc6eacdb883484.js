"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[28453],{79205:function(t,e,n){n.d(e,{Z:function(){return f}});var r=n(2265);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.filter((t,e,n)=>!!t&&n.indexOf(t)===e).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((t,e)=>{let{color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:f,className:u="",children:c,iconNode:s,...d}=t;return(0,r.createElement)("svg",{ref:e,...l,width:i,height:i,stroke:n,strokeWidth:f?24*Number(a)/Number(i):a,className:o("lucide",u),...d},[...s.map(t=>{let[e,n]=t;return(0,r.createElement)(e,n)}),...Array.isArray(c)?c:[c]])}),f=(t,e)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:f,...u}=n;return(0,r.createElement)(a,{ref:l,iconNode:e,className:o("lucide-".concat(i(t)),f),...u})});return n.displayName="".concat(t),n}},26008:function(t,e,n){n.d(e,{ee:function(){return tJ},Eh:function(){return tQ},VY:function(){return tK},fC:function(){return tG},D7:function(){return tD}});var r=n(2265);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,u=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function d(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}function y(t){return["top","bottom"].includes(p(t))?"y":"x"}function w(t){return t.replace(/start|end/g,t=>s[t])}function v(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function x(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function b(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function A(t,e,n){let r,{reference:i,floating:o}=t,l=y(e),a=m(y(e)),f=g(a),u=p(e),c="y"===l,s=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,w=i[f]/2-o[f]/2;switch(u){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}let R=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(e)),u=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:s}=A(u,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:v}=await m({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:t,floating:e}});c=null!=g?g:c,s=null!=y?y:s,p={...p,[o]:{...p[o],...w}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(u=!0===v.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):v.rects),{x:c,y:s}=A(u,d,f)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:p}};async function E(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=t,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:s="floating",altBoundary:p=!1,padding:h=0}=d(e,t),m=x(h),g=a[p?"floating"===s?"reference":"floating":s],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:f})),w="floating"===s?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),A=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:v,strategy:f}):w);return{top:(y.top-R.top+m.top)/A.y,bottom:(R.bottom-y.bottom+m.bottom)/A.y,left:(y.left-R.left+m.left)/A.x,right:(R.right-y.right+m.right)/A.x}}function L(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function C(t){return i.some(e=>t[e]>=0)}async function T(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),u=["left","top"].includes(l)?-1:1,c=o&&f?-1:1,s=d(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof s?{mainAxis:s,crossAxis:0,alignmentAxis:null}:{mainAxis:s.mainAxis||0,crossAxis:s.crossAxis||0,alignmentAxis:s.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),f?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function S(){return"undefined"!=typeof window}function O(t){return H(t)?(t.nodeName||"").toLowerCase():"#document"}function P(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function k(t){var e;return null==(e=(H(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function H(t){return!!S()&&(t instanceof Node||t instanceof P(t).Node)}function D(t){return!!S()&&(t instanceof Element||t instanceof P(t).Element)}function W(t){return!!S()&&(t instanceof HTMLElement||t instanceof P(t).HTMLElement)}function N(t){return!!S()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof P(t).ShadowRoot)}function j(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=B(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(i)}function F(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function z(t){let e=M(),n=D(t)?B(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function M(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(t){return["html","body","#document"].includes(O(t))}function B(t){return P(t).getComputedStyle(t)}function _(t){return D(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Y(t){if("html"===O(t))return t;let e=t.assignedSlot||t.parentNode||N(t)&&t.host||k(t);return N(e)?e.host:e}function I(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=Y(e);return V(n)?e.ownerDocument?e.ownerDocument.body:e.body:W(n)&&j(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=P(i);if(o){let t=$(l);return e.concat(l,l.visualViewport||[],j(i)?i:[],t&&n?I(t):[])}return e.concat(i,I(i,[],n))}function $(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function X(t){let e=B(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=W(t),o=i?t.offsetWidth:n,l=i?t.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function q(t){return D(t)?t:t.contextElement}function Z(t){let e=q(t);if(!W(e))return u(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=X(e),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let G=u(0);function J(t){let e=P(t);return M()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:G}function K(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=q(t),a=u(1);e&&(r?D(r)&&(a=Z(r)):a=Z(t));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===P(l))&&i)?J(l):u(0),c=(o.left+f.x)/a.x,s=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let t=P(l),e=r&&D(r)?P(r):r,n=t,i=$(n);for(;i&&r&&e!==n;){let t=Z(i),e=i.getBoundingClientRect(),r=B(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,s*=t.y,d*=t.x,p*=t.y,c+=o,s+=l,i=$(n=P(i))}}return b({width:d,height:p,x:c,y:s})}function Q(t,e){let n=_(t).scrollLeft;return e?e.left+n:K(k(t)).left+n}function U(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:Q(t,r)),y:r.top+e.scrollTop}}function tt(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=P(t),r=k(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let t=M();(!t||t&&"fixed"===e)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(t,n);else if("document"===e)r=function(t){let e=k(t),n=_(t),r=t.ownerDocument.body,i=l(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=l(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(t),f=-n.scrollTop;return"rtl"===B(r).direction&&(a+=l(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(k(t));else if(D(e))r=function(t,e){let n=K(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=W(t)?Z(t):u(1),l=t.clientWidth*o.x;return{width:l,height:t.clientHeight*o.y,x:i*o.x,y:r*o.y}}(e,n);else{let n=J(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return b(r)}function te(t){return"static"===B(t).position}function tn(t,e){if(!W(t)||"fixed"===B(t).position)return null;if(e)return e(t);let n=t.offsetParent;return k(t)===n&&(n=n.ownerDocument.body),n}function tr(t,e){let n=P(t);if(F(t))return n;if(!W(t)){let e=Y(t);for(;e&&!V(e);){if(D(e)&&!te(e))return e;e=Y(e)}return n}let r=tn(t,e);for(;r&&["table","td","th"].includes(O(r))&&te(r);)r=tn(r,e);return r&&V(r)&&te(r)&&!z(r)?n:r||function(t){let e=Y(t);for(;W(e)&&!V(e);){if(z(e))return e;if(F(e))break;e=Y(e)}return null}(t)||n}let ti=async function(t){let e=this.getOffsetParent||tr,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=W(e),i=k(e),o="fixed"===n,l=K(t,!0,o,e),a={scrollLeft:0,scrollTop:0},f=u(0);if(r||!r&&!o){if(("body"!==O(e)||j(i))&&(a=_(e)),r){let t=K(e,!0,o,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else i&&(f.x=Q(i))}let c=!i||r||o?u(0):U(i,a);return{x:l.left+a.scrollLeft-f.x-c.x,y:l.top+a.scrollTop-f.y-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},to={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=k(r),a=!!e&&F(e.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},c=u(1),s=u(0),d=W(r);if((d||!d&&!o)&&(("body"!==O(r)||j(l))&&(f=_(r)),W(r))){let t=K(r);c=Z(r),s.x=t.x+r.clientLeft,s.y=t.y+r.clientTop}let p=!l||d||o?u(0):U(l,f,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+s.x+p.x,y:n.y*c.y-f.scrollTop*c.y+s.y+p.y}},getDocumentElement:k,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t,a=[..."clippingAncestors"===n?F(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=I(t,[],!1).filter(t=>D(t)&&"body"!==O(t)),i=null,o="fixed"===B(t).position,l=o?Y(t):t;for(;D(l)&&!V(l);){let e=B(l),n=z(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&["absolute","fixed"].includes(i.position)||j(l)&&!n&&function t(e,n){let r=Y(e);return!(r===n||!D(r)||V(r))&&("fixed"===B(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=Y(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],f=a[0],u=a.reduce((t,n)=>{let r=tt(e,n,i);return t.top=l(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=l(r.left,t.left),t},tt(e,f,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:tr,getElementRects:ti,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=X(t);return{width:e,height:n}},getScale:Z,isElement:D,isRTL:function(t){return"rtl"===B(t).direction}};function tl(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}let ta=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:i,rects:a,platform:f,elements:u,middlewareData:c}=e,{element:s,padding:p=0}=d(t,e)||{};if(null==s)return{};let w=x(p),v={x:n,y:r},b=m(y(i)),A=g(b),R=await f.getDimensions(s),E="y"===b,L=E?"clientHeight":"clientWidth",C=a.reference[A]+a.reference[b]-v[b]-a.floating[A],T=v[b]-a.reference[b],S=await (null==f.getOffsetParent?void 0:f.getOffsetParent(s)),O=S?S[L]:0;O&&await (null==f.isElement?void 0:f.isElement(S))||(O=u.floating[L]||a.floating[A]);let P=O/2-R[A]/2-1,k=o(w[E?"top":"left"],P),H=o(w[E?"bottom":"right"],P),D=O-R[A]-H,W=O/2-R[A]/2+(C/2-T/2),N=l(k,o(W,D)),j=!c.arrow&&null!=h(i)&&W!==N&&a.reference[A]/2-(W<k?k:H)-R[A]/2<0,F=j?W<k?W-k:W-D:0;return{[b]:v[b]+F,data:{[b]:N,centerOffset:W-N-F,...j&&{alignmentOffset:F}},reset:j}}}),tf=(t,e,n)=>{let r=new Map,i={platform:to,...n},o={...i.platform,_c:r};return R(t,e,{...i,platform:o})};var tu=n(54887),tc="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ts(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!ts(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!ts(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function td(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function tp(t,e){let n=td(t);return Math.round(e*n)/n}function th(t){let e=r.useRef(t);return tc(()=>{e.current=t}),e}let tm=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ta({element:n.current,padding:r}).fn(e):{}:n?ta({element:n,padding:r}).fn(e):{}}}),tg=(t,e)=>{var n;return{...(void 0===(n=t)&&(n=0),{name:"offset",options:n,async fn(t){var e,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await T(t,n);return l===(null==(e=a.offset)?void 0:e.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}),options:[t,e]}},ty=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"shift",options:n,async fn(t){let{x:e,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:u={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=d(n,t),s={x:e,y:r},h=await E(t,c),g=y(p(i)),w=m(g),v=s[w],x=s[g];if(a){let t="y"===w?"top":"left",e="y"===w?"bottom":"right",n=v+h[t],r=v-h[e];v=l(n,o(v,r))}if(f){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=x+h[t],r=x-h[e];x=l(n,o(x,r))}let b=u.fn({...t,[w]:v,[g]:x});return{...b,data:{x:b.x-e,y:b.y-r,enabled:{[w]:a,[g]:f}}}}}),options:[t,e]}},tw=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{options:n,fn(t){let{x:e,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:u=!0}=d(n,t),c={x:e,y:r},s=y(i),h=m(s),g=c[h],w=c[s],v=d(a,t),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(f){let t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+x.mainAxis,n=o.reference[h]+o.reference[t]-x.mainAxis;g<e?g=e:g>n&&(g=n)}if(u){var b,A;let t="y"===h?"width":"height",e=["top","left"].includes(p(i)),n=o.reference[s]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[s])||0)+(e?0:x.crossAxis),r=o.reference[s]+o.reference[t]+(e?0:(null==(A=l.offset)?void 0:A[s])||0)-(e?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[s]:w}}}),options:[t,e]}},tv=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"flip",options:n,async fn(t){var e,r,i,o,l;let{placement:a,middlewareData:f,rects:u,initialPlacement:c,platform:s,elements:x}=t,{mainAxis:b=!0,crossAxis:A=!0,fallbackPlacements:R,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:T=!0,...S}=d(n,t);if(null!=(e=f.arrow)&&e.alignmentOffset)return{};let O=p(a),P=y(c),k=p(c)===c,H=await (null==s.isRTL?void 0:s.isRTL(x.floating)),D=R||(k||!T?[v(c)]:function(t){let e=v(t);return[w(t),e,w(e)]}(c)),W="none"!==C;!R&&W&&D.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){let r=["left","right"],i=["right","left"];switch(t){case"top":case"bottom":if(n)return e?i:r;return e?r:i;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(p(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(w)))),o}(c,T,C,H));let N=[c,...D],j=await E(t,S),F=[],z=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&F.push(j[O]),A){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=m(y(t)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=v(l)),[l,v(l)]}(a,u,H);F.push(j[t[0]],j[t[1]])}if(z=[...z,{placement:a,overflows:F}],!F.every(t=>t<=0)){let t=((null==(i=f.flip)?void 0:i.index)||0)+1,e=N[t];if(e)return{data:{index:t,overflows:z},reset:{placement:e}};let n=null==(o=z.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(L){case"bestFit":{let t=null==(l=z.filter(t=>{if(W){let e=y(t.placement);return e===P||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[t,e]}},tx=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"size",options:n,async fn(t){var e,r;let i,a;let{placement:f,rects:u,platform:c,elements:s}=t,{apply:m=()=>{},...g}=d(n,t),w=await E(t,g),v=p(f),x=h(f),b="y"===y(f),{width:A,height:R}=u.floating;"top"===v||"bottom"===v?(i=v,a=x===(await (null==c.isRTL?void 0:c.isRTL(s.floating))?"start":"end")?"left":"right"):(a=v,i="end"===x?"top":"bottom");let L=R-w.top-w.bottom,C=A-w.left-w.right,T=o(R-w[i],L),S=o(A-w[a],C),O=!t.middlewareData.shift,P=T,k=S;if(null!=(e=t.middlewareData.shift)&&e.enabled.x&&(k=C),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=L),O&&!x){let t=l(w.left,0),e=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?k=A-2*(0!==t||0!==e?t+e:l(w.left,w.right)):P=R-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:k,availableHeight:P});let H=await c.getDimensions(s.floating);return A!==H.width||R!==H.height?{reset:{rects:!0}}:{}}}),options:[t,e]}},tb=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"hide",options:n,async fn(t){let{rects:e}=t,{strategy:r="referenceHidden",...i}=d(n,t);switch(r){case"referenceHidden":{let n=L(await E(t,{...i,elementContext:"reference"}),e.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:C(n)}}}case"escaped":{let n=L(await E(t,{...i,altBoundary:!0}),e.floating);return{data:{escapedOffsets:n,escaped:C(n)}}}default:return{}}}}),options:[t,e]}},tA=(t,e)=>({...tm(t),options:[t,e]});var tR=n(66840),tE=n(57437),tL=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,tE.jsx)(tR.WV.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,tE.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tL.displayName="Arrow";var tC=n(98575),tT=n(73966),tS=n(26606),tO=n(61188),tP=n(90420),tk="Popper",[tH,tD]=(0,tT.b)(tk),[tW,tN]=tH(tk),tj=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,tE.jsx)(tW,{scope:e,anchor:i,onAnchorChange:o,children:n})};tj.displayName=tk;var tF="PopperAnchor",tz=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,l=tN(tF,n),a=r.useRef(null),f=(0,tC.e)(e,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,tE.jsx)(tR.WV.div,{...o,ref:f})});tz.displayName=tF;var tM="PopperContent",[tV,tB]=tH(tM),t_=r.forwardRef((t,e)=>{var n,i,a,u,c,s,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:v=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:A=0,sticky:R="partial",hideWhenDetached:E=!1,updatePositionStrategy:L="optimized",onPlaced:C,...T}=t,S=tN(tM,h),[O,P]=r.useState(null),H=(0,tC.e)(e,t=>P(t)),[D,W]=r.useState(null),N=(0,tP.t)(D),j=null!==(d=null==N?void 0:N.width)&&void 0!==d?d:0,F=null!==(p=null==N?void 0:N.height)&&void 0!==p?p:0,z="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},M=Array.isArray(b)?b:[b],V=M.length>0,B={padding:z,boundary:M.filter(tX),altBoundary:V},{refs:_,floatingStyles:Y,placement:$,isPositioned:X,middlewareData:Z}=function(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:u,open:c}=t,[s,d]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);ts(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),v=r.useCallback(t=>{t!==R.current&&(R.current=t,g(t))},[]),x=r.useCallback(t=>{t!==E.current&&(E.current=t,w(t))},[]),b=l||m,A=a||y,R=r.useRef(null),E=r.useRef(null),L=r.useRef(s),C=null!=u,T=th(u),S=th(o),O=th(c),P=r.useCallback(()=>{if(!R.current||!E.current)return;let t={placement:e,strategy:n,middleware:p};S.current&&(t.platform=S.current),tf(R.current,E.current,t).then(t=>{let e={...t,isPositioned:!1!==O.current};k.current&&!ts(L.current,e)&&(L.current=e,tu.flushSync(()=>{d(e)}))})},[p,e,n,S,O]);tc(()=>{!1===c&&L.current.isPositioned&&(L.current.isPositioned=!1,d(t=>({...t,isPositioned:!1})))},[c]);let k=r.useRef(!1);tc(()=>(k.current=!0,()=>{k.current=!1}),[]),tc(()=>{if(b&&(R.current=b),A&&(E.current=A),b&&A){if(T.current)return T.current(b,A,P);P()}},[b,A,P,T,C]);let H=r.useMemo(()=>({reference:R,floating:E,setReference:v,setFloating:x}),[v,x]),D=r.useMemo(()=>({reference:b,floating:A}),[b,A]),W=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!D.floating)return t;let e=tp(D.floating,s.x),r=tp(D.floating,s.y);return f?{...t,transform:"translate("+e+"px, "+r+"px)",...td(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,f,D.floating,s.x,s.y]);return r.useMemo(()=>({...s,update:P,refs:H,elements:D,floatingStyles:W}),[s,P,H,D,W])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t,e,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(t),h=a||u?[...p?I(p):[],...I(e)]:[];h.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),u&&t.addEventListener("resize",n)});let m=p&&s?function(t,e){let n,r=null,i=k(t);function a(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function u(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),a();let d=t.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||e(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),v={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,s))||1},x=!0;function b(e){let r=e[0].intersectionRatio;if(r!==s){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||tl(d,t.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...v,root:i.ownerDocument})}catch(t){r=new IntersectionObserver(b,v)}r.observe(t)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let w=d?K(t):null;return d&&function e(){let r=K(t);w&&!tl(w,r)&&n(),w=r,i=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{a&&t.removeEventListener("scroll",n),u&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...e,{animationFrame:"always"===L})},elements:{reference:S.anchor},middleware:[tg({mainAxis:g+F,alignmentAxis:w}),x&&ty({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?tw():void 0,...B}),x&&tv({...B}),tx({...B,apply:t=>{let{elements:e,rects:n,availableWidth:r,availableHeight:i}=t,{width:o,height:l}=n.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&tA({element:D,padding:v}),tq({arrowWidth:j,arrowHeight:F}),E&&tb({strategy:"referenceHidden",...B})]}),[G,J]=tZ($),Q=(0,tS.W)(C);(0,tO.b)(()=>{X&&(null==Q||Q())},[X,Q]);let U=null===(n=Z.arrow)||void 0===n?void 0:n.x,tt=null===(i=Z.arrow)||void 0===i?void 0:i.y,te=(null===(a=Z.arrow)||void 0===a?void 0:a.centerOffset)!==0,[tn,tr]=r.useState();return(0,tO.b)(()=>{O&&tr(window.getComputedStyle(O).zIndex)},[O]),(0,tE.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...Y,transform:X?Y.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:tn,"--radix-popper-transform-origin":[null===(u=Z.transformOrigin)||void 0===u?void 0:u.x,null===(c=Z.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(s=Z.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,tE.jsx)(tV,{scope:h,placedSide:G,onArrowChange:W,arrowX:U,arrowY:tt,shouldHideArrow:te,children:(0,tE.jsx)(tR.WV.div,{"data-side":G,"data-align":J,...T,ref:H,style:{...T.style,animation:X?void 0:"none"}})})})});t_.displayName=tM;var tY="PopperArrow",tI={top:"bottom",right:"left",bottom:"top",left:"right"},t$=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=tB(tY,n),o=tI[i.placedSide];return(0,tE.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,tE.jsx)(tL,{...r,ref:e,style:{...r.style,display:"block"}})})});function tX(t){return null!==t}t$.displayName=tY;var tq=t=>({name:"transformOrigin",options:t,fn(e){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:u}=e,c=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,s=c?0:t.arrowWidth,d=c?0:t.arrowHeight,[p,h]=tZ(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!==(o=null===(r=u.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+s/2,y=(null!==(l=null===(i=u.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,w="",v="";return"bottom"===p?(w=c?m:"".concat(g,"px"),v="".concat(-d,"px")):"top"===p?(w=c?m:"".concat(g,"px"),v="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),v=c?m:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),v=c?m:"".concat(y,"px")),{data:{x:w,y:v}}}});function tZ(t){let[e,n="center"]=t.split("-");return[e,n]}var tG=tj,tJ=tz,tK=t_,tQ=t$},90420:function(t,e,n){n.d(e,{t:function(){return o}});var r=n(2265),i=n(61188);function o(t){let[e,n]=r.useState(void 0);return(0,i.b)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}}}]);