exports.id=9156,exports.ids=[9156],exports.modules={81281:(e,s,t)=>{Promise.resolve().then(t.bind(t,73e3)),Promise.resolve().then(t.bind(t,44992))},44992:(e,s,t)=>{"use strict";t.d(s,{default:()=>T});var i=t(10326),n=t(90772),a=t(8281),r=t(81638),o=t(4066),l=t(60102),c=t(77863),d=t(44221),u=t(1178),m=t(29169),x=t(40420),v=t(78564),f=t(53105),g=t(81770),h=t(57900),p=t(45922),b=t(29764),y=t(76662),k=t(70346),j=t(13924),w=t(99387),Z=t(62047),z=t(26920),N=t(52350),A=t(75629),q=t(75073),S=t(46226),D=t(90434),P=t(70012),C=t(567),R=t(95028),_=t(41135),E=t(35047);function L({title:e="",nav:s}){let t=(0,E.useSelectedLayoutSegment)(),{setIsExpanded:n}=(0,r.q)(),{width:a}=(0,R.B)(),o=()=>{a<1024&&n(!1)};return(0,i.jsxs)("div",{className:"py-4",children:[e&&i.jsx("div",{className:"mb-2 whitespace-nowrap",children:i.jsx("span",{className:"px-2 text-sm font-medium tracking-wide text-secondary-600",children:e})}),i.jsx("div",{className:"flex flex-col gap-2",children:s.map(e=>void 0===e.visible||e.visible?(0,i.jsxs)(D.default,{href:e?.link,"aria-disabled":void 0===e?.isActive||e.isActive,onClick:s=>{void 0===e?.isActive||e.isActive?o():s.preventDefault()},className:(0,_.Z)("flex w-full items-center gap-2 whitespace-nowrap rounded-2xl px-2 py-2 transition-all duration-150 ease-in-out hover:bg-secondary active:bg-important/20",t===e.key&&"bg-secondary",("(dashboard)"===t||"__DEFAULT__"===t)&&"dashboard"===e.key&&"bg-secondary",void 0!==e.isActive&&!e.isActive&&"opacity-50"),children:[e?.icon&&i.jsx("div",{"data-active":t===e.key,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon}),i.jsx("p",{className:"font-medium",children:e?.name}),void 0!==e.badge?i.jsx(C.C,{variant:e.badge?.variant??"secondary",className:(0,c.ZP)("",e.badge?.className),children:e.badge.title}):null]},e?.key):null)})]})}function T({userRole:e="customer"}){let{t:s}=(0,P.$G)(),{isExpanded:t,setIsExpanded:C}=(0,r.q)(),{settings:R,isLoading:_}=(0,l.h)(),{logo:E,siteName:T}=(0,o.T)();return(0,i.jsxs)("div",{"data-expanded":t,className:"group absolute z-[60] flex h-full min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full sm:pb-1 lg:relative lg:z-auto",children:[i.jsx(n.z,{size:"icon",variant:"outline",onClick:()=>C(!1),className:"absolute -right-5 top-4 rounded-full bg-background group-data-[expanded=false]:hidden lg:hidden",children:i.jsx(d.Z,{})}),i.jsx("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary",children:i.jsx(D.default,{href:"/",className:"flex items-center justify-center",children:i.jsx(S.default,{src:(0,c.qR)(E),width:160,height:40,alt:T,className:"max-h-10 object-contain"})})}),(0,i.jsxs)("div",{className:"flex w-full flex-1 flex-col overflow-y-auto overflow-x-hidden px-4",children:[i.jsx(L,{nav:[{key:"(dashboard)",name:s("Dashboard"),icon:i.jsx(u.Z,{size:"20"}),link:"/",isLoading:!1},{key:"deposit",name:s("Deposit"),icon:i.jsx(m.Z,{size:"20"}),link:"/deposit",isLoading:_,isActive:R?.deposit?.status==="on"},{key:"transfer",name:s("Transfer"),icon:i.jsx(x.Z,{size:"20"}),link:"/transfer",visible:"agent"!==e,isLoading:_,isActive:R?.transfer?.status==="on"},{key:"withdraw",name:s("Withdraw"),icon:i.jsx(v.Z,{size:"20"}),link:"/withdraw",isLoading:_,isActive:R?.withdraw?.status==="on"},{key:"exchange",name:s("Exchange"),icon:i.jsx(f.Z,{size:"20"}),link:"/exchange",isLoading:_,isActive:R?.exchange?.status==="on"},{key:"payment",name:s("Payment"),icon:i.jsx(g.Z,{size:"20"}),link:"/payment",visible:"agent"!==e,isLoading:_,isActive:R?.payment?.status==="on"},{key:"services",name:s("Services"),icon:i.jsx(h.Z,{size:"20"}),link:"/services",visible:"agent"!==e,isLoading:_,isActive:!0},{key:"cards",name:s("Cards"),icon:i.jsx(p.Z,{size:"20"}),link:"/cards",isLoading:_,isActive:R?.virtual_card?.status==="on",visible:R?.virtual_card?.status==="on"},{key:"investments",name:s("Investments"),icon:i.jsx(b.Z,{size:"20"}),link:"/investments",isLoading:_}]}),i.jsx(a.Z,{className:"bg-divider-secondary"}),i.jsx(L,{nav:[{key:"direct-deposit",name:s("Deposit to Customer"),icon:i.jsx(m.Z,{size:"20"}),link:"/direct-deposit",visible:"agent"===e,isLoading:_,isActive:!0},{key:"deposit-request",name:s("Deposit Requests"),icon:i.jsx(m.Z,{size:"20"}),link:"/deposit-request",visible:"agent"===e,isLoading:_},{key:"withdraw-request",name:s("Withdraw Requests"),icon:i.jsx(y.Z,{size:"20"}),link:"/withdraw-request",visible:"agent"===e,isLoading:_},{key:"transaction-history",name:s("Transaction History"),icon:i.jsx(k.Z,{size:"20"}),link:"/transaction-history",isLoading:_},{key:"investments-history",name:s("Investments History"),icon:i.jsx(k.Z,{size:"20"}),link:"/investments-history",isLoading:_},{key:"settlements",name:s("Settlements"),icon:i.jsx(k.Z,{size:"20"}),link:"/settlements",visible:"agent"===e,isLoading:_},{key:"merchant-transactions",name:s("Merchant Transaction"),icon:i.jsx(j.Z,{size:"20"}),link:"/merchant-transactions",visible:"merchant"===e,isLoading:_},{key:"payment-requests",name:s("Payment Requests"),icon:i.jsx(w.Z,{size:"20"}),link:"/payment-requests",visible:"merchant"===e,isLoading:_},{key:"favorites",name:s("Favorites"),icon:i.jsx(Z.Z,{size:"20"}),link:"/favorites",visible:"agent"!==e,isLoading:_},{key:"contacts",name:s("Contacts"),icon:i.jsx(z.Z,{size:"20"}),link:"/contacts",visible:"agent"!==e,isLoading:_},{key:"wallets",name:s("Wallets"),icon:i.jsx(N.Z,{size:"20"}),link:"/wallets",isLoading:_},{key:"referral",name:s("Referral"),icon:i.jsx(A.Z,{size:"20"}),link:"/referral",isLoading:_},{key:"settings",name:s("Settings"),icon:i.jsx(q.Z,{size:"20"}),link:"/settings",isLoading:_}]})]})]})}t(17577)},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>o});var i=t(10326),n=t(79360);t(17577);var a=t(77863);let r=(0,n.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:s,...t}){return i.jsx("div",{className:(0,a.ZP)(r({variant:s}),e),...t})}},60102:(e,s,t)=>{"use strict";t.d(s,{h:()=>n});var i=t(90799);function n(){let{data:e,...s}=(0,i.d)("/settings/global");return{settings:e?.data,...s}}},19928:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var i=t(19510),n=t(40099),a=t(76609);function r({children:e}){return(0,i.jsxs)("div",{className:"flex h-screen",children:[i.jsx(a.Z,{userRole:"merchant"}),(0,i.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[i.jsx(n.Z,{}),i.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},15989:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var i=t(19510),n=t(48413);function a(){return i.jsx("div",{className:"flex items-center justify-center py-10",children:i.jsx(n.a,{})})}},76609:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\SideNav.tsx#default`)}};