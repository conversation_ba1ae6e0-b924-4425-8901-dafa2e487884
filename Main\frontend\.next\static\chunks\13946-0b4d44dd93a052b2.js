"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[13946],{52764:function(e,t,r){r.r(t),r.d(t,{default:function(){return Z}});var n=r(57437),s=r(62869),a=r(26110),l=r(6512),c=r(1828),o=r(83277),i=r(80167),d=r(3612),u=r(21251),m=r(94508),x=r(83504),f=r(54882),h=r(8877),p=r(47480),v=r(27648),g=r(43949),j=r(14438);function b(e){var t;let{card:r,balance:s,currency:a,onMutate:l}=e,{t:c}=(0,g.$G)(),{auth:o}=(0,d.a)(),{cardBg:i}=(0,u.T)();return(0,n.jsxs)("div",{style:{backgroundImage:"url(".concat((0,m.qR)(i),")")},className:"mb-5 flex min-h-[280px] w-full max-w-[450px] flex-col justify-end rounded-3xl bg-cover p-5",children:[(0,n.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:["active"===r.status?(0,n.jsx)("div",{className:"h-3 w-3 rounded-full bg-success"}):(0,n.jsx)("div",{className:"h-3 w-3 rounded-full bg-danger"}),(0,n.jsx)("p",{className:"text-sm capitalize text-white",children:r.status})]}),(0,n.jsx)("p",{className:"mb-5 text-2xl font-semibold text-white",children:null==r?void 0:r.number.replace(/(\d{4})(?=\d)/g,"$1 ")}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-5",children:[(0,n.jsxs)("div",{className:"text-white",children:[(0,n.jsx)("p",{className:"text-sm",children:c("Card holder name")}),(0,n.jsx)("p",{className:"text-base font-semibold",children:null==o?void 0:null===(t=o.customer)||void 0===t?void 0:t.name})]}),(0,n.jsxs)("div",{className:"text-white",children:[(0,n.jsx)("p",{className:"text-sm",children:c("Expiry date")}),(0,n.jsxs)("p",{className:"text-base font-semibold",children:[r.expMonth.toString().padStart(2,"0"),"/",4===r.expYear.toString().length?r.expYear.toString().slice(2):r.expYear.toString()]})]}),(0,n.jsxs)("div",{className:"text-white",children:[(0,n.jsx)("p",{className:"text-sm",children:c("CVV")}),(0,n.jsx)("p",{className:"text-base font-semibold",children:r.cvc})]})]}),(0,n.jsx)(N,{card:r,balance:s,currency:a,onMutate:l})]})]})}function N(e){var t;let{card:r,balance:b,currency:N,onMutate:k=()=>{}}=e,{t:w}=(0,g.$G)(),{auth:y}=(0,d.a)(),{cardBg:E}=(0,u.T)(),Z=e=>{j.toast.promise((0,i.a)({cardId:r.id,dataList:{status:e}}),{loading:w("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return k(),e.message},error:e=>e.message})},M=()=>{j.toast.promise((0,o.f)({cardId:r.id}),{loading:w("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return k(),e.message},error:e=>e.message})};return(0,n.jsxs)(a.Vq,{children:[(0,n.jsx)(a.hg,{asChild:!0,children:(0,n.jsx)(s.z,{variant:"secondary",size:"icon",type:"button",className:"rounded-md",children:(0,n.jsx)(x.Z,{size:20})})}),(0,n.jsxs)(a.cZ,{className:"sm:max-w-[525px]",children:[(0,n.jsx)(a.$N,{children:w("Card Details")}),(0,n.jsxs)("div",{className:"flex flex-col items-center",children:[(0,n.jsxs)("div",{style:{backgroundImage:"url(".concat((0,m.qR)(E),")")},className:"mb-5 flex min-h-[280px] w-full max-w-[450px] flex-col justify-end gap-7 rounded-3xl bg-cover p-7",children:[(0,n.jsx)("p",{className:"text-[28px] font-semibold text-white",children:r.number.replace(/(\d{4})(?=\d)/g,"$1 ")}),(0,n.jsxs)("div",{className:"flex items-center gap-8",children:[(0,n.jsxs)("div",{className:"text-white",children:[(0,n.jsx)("p",{className:"text-sm",children:w("Card holder name")}),(0,n.jsx)("p",{className:"text-xl font-semibold",children:null==y?void 0:null===(t=y.customer)||void 0===t?void 0:t.name})]}),(0,n.jsxs)("div",{className:"text-white",children:[(0,n.jsx)("p",{className:"text-sm",children:w("Expiry date")}),(0,n.jsxs)("p",{className:"text-xl font-semibold",children:[r.expMonth.toString().padStart(2,"0"),"/",4===r.expYear.toString().length?r.expYear.toString().slice(2):r.expYear.toString()]})]}),(0,n.jsxs)("div",{className:"text-white",children:[(0,n.jsx)("p",{className:"text-sm",children:w("CVV")}),(0,n.jsx)("p",{className:"text-xl font-semibold",children:r.cvc})]})]})]}),(0,n.jsxs)("div",{className:"mb-5 flex gap-8",children:[(0,n.jsxs)("button",{type:"button",onClick:()=>(0,m.Fp)(r.number),className:"flex flex-col items-center justify-center gap-2",children:[(0,n.jsx)("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:(0,n.jsx)(f.Z,{size:"24",color:"#000"})}),(0,n.jsx)("span",{className:"text-xs font-semibold",children:w("Copy Number")})]}),(0,n.jsxs)(v.default,{href:"/deposit",className:"flex flex-col items-center justify-center gap-2",children:[(0,n.jsx)("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:(0,n.jsx)(h.Z,{size:"24",color:"#000"})}),(0,n.jsx)("span",{className:"text-xs font-semibold",children:w("Deposit Money")})]}),(0,n.jsxs)("button",{type:"button",onClick:()=>M(),className:"flex flex-col items-center justify-center gap-2",children:[(0,n.jsx)("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary text-black transition duration-300 ease-in-out hover:bg-spacial-red-foreground hover:text-danger",children:(0,n.jsx)(p.Z,{size:"24"})}),(0,n.jsx)("span",{className:"text-xs font-semibold",children:w("Close Card")})]})]}),(0,n.jsx)(l.Z,{className:"mb-5 border-b bg-transparent"}),(0,n.jsxs)("div",{className:"w-full space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:w("Status")}),(0,n.jsx)(c.Z,{defaultChecked:"active"===r.status,onCheckedChange:e=>{Z(e?"active":"inactive")}})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:w("Balance")}),(0,n.jsxs)("span",{className:"text-sm font-semibold",children:[b," ",N]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:w("Card Type")}),(0,n.jsx)("span",{className:"text-sm font-semibold",children:null==r?void 0:r.brand})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:w("Expiry Date")}),(0,n.jsxs)("span",{className:"text-sm font-semibold",children:[r.expMonth.toString().padStart(2,"0"),"/",4===r.expYear.toString().length?r.expYear.toString().slice(2):r.expYear.toString()]})]})]})]})]})]})}var k=r(85487),w=r(31117),y=r(502),E=r(73490);function Z(){var e;let{data:t,isLoading:r,mutate:s}=(0,w.d)("/cards"),{t:a}=(0,g.$G)();if(r)return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(k.Loader,{})});let l=null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.map(e=>new y.Z(e));return r||0!==l.length?(0,n.jsx)("div",{className:"w-full bg-background p-4",children:(0,n.jsx)("div",{className:"flex flex-wrap gap-4",children:l.map(e=>(0,n.jsx)(b,{balance:e.wallet.balance,currency:e.brand,card:e,onMutate:s},e.id))})}):(0,n.jsx)("div",{className:"h-full w-full bg-background p-4",children:(0,n.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,n.jsx)(E.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),a("No cards found!")]})})}},83504:function(e,t,r){r.d(t,{Z:function(){return h}});var n=r(74677),s=r(2265),a=r(40718),l=r.n(a),c=["variant","color","size"],o=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7 13.31c-.72 0-1.31-.59-1.31-1.31 0-.72.59-1.31 1.31-1.31.72 0 1.31.59 1.31 1.31 0 .72-.59 1.31-1.31 1.31Zm5 0c-.72 0-1.31-.59-1.31-1.31 0-.72.59-1.31 1.31-1.31.72 0 1.31.59 1.31 1.31 0 .72-.59 1.31-1.31 1.31Zm5 0c-.72 0-1.31-.59-1.31-1.31 0-.72.59-1.31 1.31-1.31.72 0 1.31.59 1.31 1.31 0 .72-.59 1.31-1.31 1.31Z",fill:t}))},i=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M5 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM19 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:t,strokeWidth:"1.5"}),s.createElement("path",{d:"M10 12c0 1.1.9 2 2 2s2-.9 2-2-.9-2-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Z",fill:t}),s.createElement("path",{d:"M12 10.691c-.72 0-1.31.59-1.31 1.31 0 .72.59 1.31 1.31 1.31.72 0 1.31-.59 1.31-1.31 0-.72-.59-1.31-1.31-1.31ZM7 10.691c-.72 0-1.31.59-1.31 1.31 0 .72.59 1.31 1.31 1.31.72 0 1.31-.59 1.31-1.31 0-.72-.59-1.31-1.31-1.31ZM17 10.691c-.72 0-1.31.59-1.31 1.31 0 .72.59 1.31 1.31 1.31.72 0 1.31-.59 1.31-1.31 0-.72-.59-1.31-1.31-1.31Z",fill:t}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M5 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM19 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM12 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:t,strokeWidth:"1.5"}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M5 14.75c-1.52 0-2.75-1.23-2.75-2.75S3.48 9.25 5 9.25 7.75 10.48 7.75 12 6.52 14.75 5 14.75Zm0-4a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5ZM19 14.75c-1.52 0-2.75-1.23-2.75-2.75S17.48 9.25 19 9.25s2.75 1.23 2.75 2.75-1.23 2.75-2.75 2.75Zm0-4a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5ZM12 14.75c-1.52 0-2.75-1.23-2.75-2.75S10.48 9.25 12 9.25s2.75 1.23 2.75 2.75-1.23 2.75-2.75 2.75Zm0-4a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5Z",fill:t}))},x=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M5 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM19 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:t,strokeWidth:"1.5"}),s.createElement("path",{opacity:".4",d:"M12 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:t,strokeWidth:"1.5"}))},f=function(e,t){switch(e){case"Bold":return s.createElement(o,{color:t});case"Broken":return s.createElement(i,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(u,{color:t});case"Outline":return s.createElement(m,{color:t});case"TwoTone":return s.createElement(x,{color:t})}},h=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,l=e.size,o=(0,n._)(e,c);return s.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),f(r,a))});h.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="More"},73490:function(e,t,r){r.d(t,{Z:function(){return h}});var n=r(74677),s=r(2265),a=r(40718),l=r.n(a),c=["variant","color","size"],o=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"m19.51 5.85-5.94-3.43c-.97-.56-2.17-.56-3.15 0L4.49 5.85a3.15 3.15 0 0 0-1.57 2.73v6.84c0 1.12.6 2.16 1.57 2.73l5.94 3.43c.97.56 2.17.56 3.15 0l5.94-3.43a3.15 3.15 0 0 0 1.57-2.73V8.58a3.192 3.192 0 0 0-1.58-2.73Zm-8.26 1.9c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75s-.75-.34-.75-.75V7.75Zm1.67 8.88c-.05.12-.12.23-.21.33a.99.99 0 0 1-1.09.21c-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38Z",fill:t}))},i=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 7.75V13M2.92 8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.56 1.57 1.6 1.57 2.73v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73v-2.76",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M12 16.2v.1",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",fill:t}),s.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.249c-.13 0-.26-.03-.38-.08-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:t}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 7.75V13M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M12 16.2v.1",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.25a.99.99 0 0 1-.71-.29c-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.11-.52.29-.71.37-.37 1.05-.37 1.42 0 .18.19.29.45.29.71 0 .13-.03.26-.08.38s-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:t}),s.createElement("path",{d:"M12 22.751c-.67 0-1.35-.17-1.95-.52l-5.94-3.43c-1.2-.7-1.95-1.99-1.95-3.38v-6.84c0-1.39.75-2.68 1.95-3.38l5.94-3.43c1.2-.7 2.69-.7 3.9 0l5.94 3.43c1.2.7 1.95 1.99 1.95 3.38v6.84c0 1.39-.75 2.68-1.95 3.38l-5.94 3.43c-.6.35-1.28.52-1.95.52Zm0-20c-.41 0-.83.11-1.2.32l-5.94 3.43c-.74.43-1.2 1.22-1.2 2.08v6.84c0 .85.46 1.65 1.2 2.08l5.94 3.43c.74.43 1.66.43 2.39 0l5.94-3.43c.74-.43 1.2-1.22 1.2-2.08v-6.84c0-.85-.46-1.65-1.2-2.08l-5.94-3.43c-.36-.21-.78-.32-1.19-.32Z",fill:t}))},x=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 7.75V13",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"M12 16.2v.1",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e,t){switch(e){case"Bold":return s.createElement(o,{color:t});case"Broken":return s.createElement(i,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(u,{color:t});case"Outline":return s.createElement(m,{color:t});case"TwoTone":return s.createElement(x,{color:t})}},h=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,l=e.size,o=(0,n._)(e,c);return s.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),f(r,a))});h.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="Warning2"},79205:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(2265);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:c=2,absoluteStrokeWidth:o,className:i="",children:d,iconNode:u,...m}=e;return(0,n.createElement)("svg",{ref:t,...l,width:s,height:s,stroke:r,strokeWidth:o?24*Number(c)/Number(s):c,className:a("lucide",i),...m},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:o,...i}=r;return(0,n.createElement)(c,{ref:l,iconNode:t,className:a("lucide-".concat(s(e)),o),...i})});return r.displayName="".concat(e),r}},25523:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(47043)._(r(2265)).default.createContext(null)},6718:function(e,t,r){r.d(t,{D:function(){return s}});var n=r(2265);function s(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90420:function(e,t,r){r.d(t,{t:function(){return a}});var n=r(2265),s=r(61188);function a(e){let[t,r]=n.useState(void 0);return(0,s.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,s;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,s=t.blockSize}else n=e.offsetWidth,s=e.offsetHeight;r({width:n,height:s})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},43577:function(e,t,r){r.d(t,{IZ:function(){return u}});let{Axios:n,AxiosError:s,CanceledError:a,isCancel:l,CancelToken:c,VERSION:o,all:i,Cancel:d,isAxiosError:u,spread:m,toFormData:x,AxiosHeaders:f,HttpStatusCode:h,formToJSON:p,getAdapter:v,mergeConfig:g}=r(83464).default}}]);