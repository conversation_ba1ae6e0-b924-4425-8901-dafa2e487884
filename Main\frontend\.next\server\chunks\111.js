exports.id=111,exports.ids=[111],exports.modules={8454:(e,t,s)=>{Promise.resolve().then(s.bind(s,42064))},76571:(e,t,s)=>{Promise.resolve().then(s.bind(s,53881))},28814:(e,t,s)=>{Promise.resolve().then(s.bind(s,68411))},42064:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(10326),r=s(17577),n=s.n(r),l=s(2454),i=s(30811),o=s(77863),d=s(6216),c=s(29169),m=s(78564),x=s(53105),u=s(29764),p=s(75629),h=s(90748),f=s(35047),g=s(70012);function j({filter:e}){let t=(0,f.useSearchParams)(),{t:s}=(0,g.$G)(),[n,j]=r.useState(!1);return(0,a.jsxs)(i.J2,{open:n,onOpenChange:j,children:[(0,a.jsxs)(i.xo,{className:"flex h-10 w-full items-center gap-2 rounded-sm bg-background px-3 text-foreground shadow-defaultLite sm:w-72",children:[a.jsx("span",{className:"line-clamp-1 flex-1 text-left font-medium leading-[22px] text-foreground",children:t.get("type")?(0,o.fl)(t.get("type")):s("All Transactions")}),a.jsx(d.Z,{size:"24",strokeWidth:1.5,className:"text-secondary-text"})]}),a.jsx(i.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:a.jsx(l.mY,{className:"p-1",children:a.jsx(l.e8,{className:"max-h-[450px]",children:(0,a.jsxs)(l.fu,{className:"p-0",children:[a.jsx("div",{className:"px-2 py-1.5",children:a.jsx("p",{className:"text-[10px] font-normal leading-4 text-secondary-text",children:s("Select what you want to see")})}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(h.Z,{size:24}),s("All Transactions")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","deposit",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(c.Z,{size:24}),s("Deposits")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","deposit_request",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(c.Z,{size:24}),s("Deposit request")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","withdraw",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(m.Z,{size:"24"}),s("Withdraws")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","withdraw_request",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(m.Z,{size:"24"}),s("Withdraw request")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","exchange",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(x.Z,{size:"24"}),s("Exchange")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","investment",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(u.Z,{size:"24"}),s("Investments")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","investment_return",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(u.Z,{size:"24"}),s("Investment return")]}),a.jsx(l.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(l.di,{onSelect:()=>e("type","referral_bonus",()=>j(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(p.Z,{size:"24"}),s("Referral bonus")]})]})})})})]})}var N=s(63761),y=s(41800),b=s(59331),w=s(53313),v=s(31048),S=s(8281),k=s(75584),z=s(56140),A=s(95757),D=s(28758),Z=s(567),P=s(54033),C=s(71305),F=s(43173);let E=new o.F;function $({data:e,meta:t,isLoading:s}){let[r,l]=n().useState([]),{t:i}=(0,g.$G)();return a.jsx(z.Z,{data:e,sorting:r,isLoading:s,setSorting:l,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"createdAt",header:i("Date"),cell:({row:e})=>{let t=e.original;return t.createdAt?a.jsx("span",{className:"block w-[100px] text-sm font-normal leading-5 text-foreground",children:(0,C.WU)(t.createdAt,"dd MMM yyyy")}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"type",header:i("Type"),cell:({row:e})=>{let t=e.original;return t.type?a.jsx("div",{children:a.jsx("p",{className:"text-xs font-bold text-foreground",children:(0,o.fl)(t.type)})}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"from",header:i("From"),cell:({row:e})=>{let t=e.original,s=t?.from&&JSON.parse(t.from);return s&&"deposit"!==t.type?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(D.qE,{className:"size-7 border-2 border-primary p-1",children:[a.jsx(D.F$,{src:(0,o.qR)(s.image),alt:s.label}),(0,a.jsxs)(D.Q5,{children:[" ",(0,P.v)(s.label)," "]})]}),a.jsx("span",{children:s.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[a.jsx("span",{className:"block size-5 rounded-full bg-primary"}),a.jsx("span",{children:" N/A"})]})}},{id:"to",header:i("To"),cell:({row:e})=>{let t=e.original,s=t?.to&&JSON.parse(t.to);return s&&"withdraw"!==t.type?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(D.qE,{className:"size-7 border-2 border-primary p-1",children:[a.jsx(D.F$,{src:(0,o.qR)(s.image),alt:s.label}),(0,a.jsxs)(D.Q5,{children:[" ",(0,P.v)(s.label)," "]})]}),a.jsx("span",{children:s.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[a.jsx("span",{className:"block size-5 rounded-full bg-primary"}),a.jsx("span",{children:" N/A"})]})}},{id:"amount",header:i("Amount"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,F.EQ)(t).with({type:"exchange"},()=>E.format(r?.amountFrom,r?.currencyFrom)).with({type:"deposit"},()=>E.format(t.amount,r?.currency)).otherwise(()=>E.format(t.amount,s?.currency))})}},{id:"fee",header:i("Fee"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,F.EQ)(t).with({type:"exchange"},()=>E.format(t?.fee,r?.currency)).with({type:"deposit"},()=>E.format(t.fee,r?.currency)).otherwise(()=>E.format(t.fee,s?.currency))})}},{id:"after_processing",header:i("After Processing"),cell:({row:e})=>{let t=e?.original,s=t?.to&&JSON.parse(t.to),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,F.EQ)(t).with({type:"exchange"},()=>E.format(t.total,r?.currencyTo)).with({type:"deposit"},()=>E.format(t.total,r?.currency)).otherwise(()=>E.format(t.total,s?.currency))})}},{id:"status",header:i("Status"),cell:({row:e})=>{let t=e?.original;return t.status?"completed"===t.status?a.jsx(Z.C,{variant:"success",children:(0,o.fl)(t.status)}):"pending"===t.status?a.jsx(Z.C,{variant:"secondary",className:"bg-muted",children:(0,o.fl)(t.status)}):"failed"===t.status?a.jsx(Z.C,{variant:"destructive",children:(0,o.fl)(t.status)}):a.jsx(Z.C,{variant:"secondary",className:"bg-muted",children:i("Pending")}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"trxId",header:i("Trx ID"),cell:({row:e})=>{let t=e.original;return t?.trxId?a.jsx("span",{className:"text-xs font-normal text-foreground",children:t.trxId}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"method",header:i("Method"),cell:({row:e})=>{let t=e.original;return t?.method?a.jsx("span",{className:"line-clamp-2 w-[100px] text-sm font-normal text-foreground",children:t.method}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"menu",header:i("Menu"),cell:({row:e})=>a.jsx(A.Z,{row:e.original})}]})}function I(){let{t:e}=(0,g.$G)(),t=(0,f.useSearchParams)(),[s,n]=r.useState(t.get("search")??""),l=(0,f.useRouter)(),i=(0,f.usePathname)(),{data:d,meta:c,isLoading:m,filter:x}=(0,k.Z)(`/transactions?${t.toString()}`,{keepPreviousData:!0});return a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,a.jsxs)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:[a.jsx(j,{filter:x}),(0,a.jsxs)("div",{className:"flex h-full w-full items-center gap-2.5 px-4 py-2.5 sm:w-40 sm:px-0",children:[a.jsx(w.X,{id:"bookmark",defaultChecked:"true"===t.get("bookmark"),onCheckedChange:e=>x("bookmark",e.toString()),className:"border-foreground/40 data-[state=checked]:border-primary"}),a.jsx(v.Z,{htmlFor:"bookmark",className:"text-sm font-normal hover:cursor-pointer",children:e("Show bookmarks")})]})]}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center gap-4 xl:flex-nowrap xl:justify-end",children:[a.jsx(N.R,{value:s,onChange:e=>{e.preventDefault();let t=(0,o.w4)(e.target.value);n(e.target.value),l.replace(`${i}?${t.toString()}`)},iconPlacement:"end",placeholder:e("Search..."),containerClass:"w-full sm:w-auto"}),a.jsx(b.k,{canFilterByGateway:!0,canFilterByMethod:!0}),a.jsx(y._,{url:"/transactions/export/all",align:"end"})]})]}),a.jsx(S.Z,{className:"my-4"}),a.jsx($,{data:d,meta:c,isLoading:m})]})})}},53881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var a=s(10326),r=s(56140),n=s(95757),l=s(28758),i=s(567),o=s(77863),d=s(54033),c=s(71305),m=s(17577),x=s.n(m),u=s(70012),p=s(43173);let h=new o.F;function f({data:e,meta:t,isLoading:s}){let[m,f]=x().useState([]),{t:g}=(0,u.$G)();return a.jsx(r.Z,{data:e,sorting:m,isLoading:s,setSorting:f,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"createdAt",header:g("Date"),cell:({row:e})=>{let t=e.original;return t.createdAt?a.jsx("span",{className:"block w-[100px] text-sm font-normal leading-5 text-foreground",children:(0,c.WU)(t.createdAt,"dd MMM yyyy; \n hh:mm a")}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"type",header:g("Type"),cell:({row:e})=>{let t=e.original;return t.type?a.jsx("div",{children:a.jsx("p",{className:"text-xs font-bold text-foreground",children:(0,o.fl)(t.type)})}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"from",header:g("From"),cell:({row:e})=>{let t=e.original,s=t?.from&&JSON.parse(t.from);return s&&"deposit"!==t.type?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(l.qE,{className:"size-7 border-2 border-primary p-1",children:[a.jsx(l.F$,{src:(0,o.qR)(s.image),alt:s.label}),(0,a.jsxs)(l.Q5,{children:[" ",(0,d.v)(s.label)," "]})]}),a.jsx("span",{children:s.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[a.jsx("span",{className:"block size-5 rounded-full bg-primary"}),a.jsx("span",{children:"N/A"})]})}},{id:"to",header:g("To"),cell:({row:e})=>{let t=e.original,s=t?.to&&JSON.parse(t.to);return s&&"withdraw"!==t.type?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(l.qE,{className:"size-7 border-2 border-primary p-1",children:[a.jsx(l.F$,{src:(0,o.qR)(s.image),alt:s.label}),(0,a.jsxs)(l.Q5,{children:[" ",(0,d.v)(s.label)," "]})]}),a.jsx("span",{children:s.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[a.jsx("span",{className:"block size-5 rounded-full bg-primary"}),a.jsx("span",{children:"N/A"})]})}},{id:"amount",header:g("Amount"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return(0,p.EQ)(t).with({type:"exchange"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(r?.amountFrom,r?.currencyFrom)})).with({type:"deposit"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t.amount,r?.currency)})).otherwise(()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t.amount,s?.currency)}))}},{id:"fee",header:g("Fee"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return(0,p.EQ)(t).with({type:"exchange"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t?.fee,r?.currency)})).with({type:"deposit"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t.fee,r?.currency)})).otherwise(()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t.fee,s?.currency)}))}},{id:"after_processing",header:g("After Processing"),cell:({row:e})=>{let t=e?.original,s=t?.to&&JSON.parse(t.to),r=t?.metaData&&JSON.parse(t?.metaData);return(0,p.EQ)(t).with({type:"exchange"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t.total,r?.currencyTo)})).with({type:"deposit"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t.total,r?.currency)})).otherwise(()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:h.format(t.total,s?.currency)}))}},{id:"status",header:g("Status"),cell:({row:e})=>{let t=e?.original;return t.status?"completed"===t.status?a.jsx(i.C,{variant:"success",children:(0,o.fl)(t.status)}):"pending"===t.status?a.jsx(i.C,{variant:"secondary",className:"bg-muted",children:(0,o.fl)(t.status)}):"failed"===t.status?a.jsx(i.C,{variant:"destructive",children:(0,o.fl)(t.status)}):a.jsx(i.C,{variant:"secondary",className:"bg-muted",children:g("Pending")}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"trxId",header:g("Trx ID"),cell:({row:e})=>{let t=e.original;return t?.trxId?a.jsx("span",{className:"text-xs font-normal text-foreground",children:t.trxId}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"method",header:g("Method"),cell:({row:e})=>{let t=e.original;return t?.method?a.jsx("span",{className:"line-clamp-2 w-[100px] text-sm font-normal text-foreground",children:t.method}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"menu",header:g("Menu"),cell:({row:e})=>a.jsx(n.Z,{row:e.original})}]})}var g=s(63761),j=s(41800),N=s(59331),y=s(55457),b=s(53313),w=s(31048),v=s(8281),S=s(75584),k=s(35047);function z(){let{t:e}=(0,u.$G)(),t=(0,k.useSearchParams)(),[s,r]=m.useState(t.get("search")??""),n=(0,k.useRouter)(),l=(0,k.usePathname)(),{data:i,meta:d,isLoading:c,filter:x}=(0,S.Z)(`/transactions?${t.toString()}`,{keepPreviousData:!0});return a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,a.jsxs)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:[a.jsx(y.Z,{filter:x}),(0,a.jsxs)("div",{className:"flex h-full w-full items-center gap-2.5 px-4 py-2.5 sm:w-40 sm:px-0",children:[a.jsx(b.X,{id:"bookmark",defaultChecked:"true"===t.get("bookmark"),onCheckedChange:e=>x("bookmark",e.toString()),className:"border-foreground/40 data-[state=checked]:border-primary"}),a.jsx(w.Z,{htmlFor:"bookmark",className:"text-sm font-normal hover:cursor-pointer",children:e("Show bookmarks")})]})]}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center gap-4 md:flex-nowrap md:justify-end",children:[a.jsx(g.R,{value:s,onChange:e=>{e.preventDefault();let t=(0,o.w4)(e.target.value);r(e.target.value),n.replace(`${l}?${t.toString()}`)},iconPlacement:"end",placeholder:e("Search..."),containerClass:"w-full sm:w-auto"}),a.jsx(N.k,{canFilterByAgent:!0,canFilterByGateway:!0,canFilterByMethod:!0}),a.jsx(j._,{url:"/transactions/export/all",align:"end"})]})]}),a.jsx(v.Z,{className:"my-4"}),a.jsx(f,{data:i,meta:d,isLoading:c})]})})}},68411:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(10326),r=s(63761),n=s(41800),l=s(59331),i=s(55457),o=s(53313),d=s(31048),c=s(8281),m=s(75584),x=s(77863),u=s(35047),p=s(17577),h=s.n(p),f=s(70012),g=s(56140),j=s(95757),N=s(28758),y=s(567),b=s(66114),w=s(54033),v=s(71305),S=s(43173);let k=new x.F;function z({data:e,meta:t,isLoading:s,refresh:r}){let[n,l]=h().useState([]),{t:i}=(0,f.$G)();return a.jsx(g.Z,{data:e?e?.map(e=>new b.C(e)):[],sorting:n,setSorting:l,isLoading:s,onRefresh:r,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"createdAt",header:i("Date"),cell:({row:e})=>{let t=e.original;return t.createdAt?a.jsx("span",{className:"block w-[100px] text-sm font-normal leading-5 text-foreground",children:(0,v.WU)(t.createdAt,"dd MMM yyyy; \n hh:mm a")}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"type",header:i("Type"),cell:({row:e})=>e.original?.type?a.jsx("div",{children:a.jsx("p",{className:"text-xs font-bold text-foreground",children:(0,x.fl)(e.original?.type)})}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})},{id:"from.label",header:i("From"),cell:({row:e})=>e.original.from||"deposit"!==e.original.type?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(N.qE,{className:"size-7 border-2 border-primary p-1",children:[a.jsx(N.F$,{src:e.original.from?.image,alt:e.original.from?.label}),a.jsx(N.Q5,{children:(0,w.v)(e.original.from.label)})]}),a.jsx("span",{children:e.original.from.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[a.jsx("span",{className:"block size-5 rounded-full bg-primary"}),a.jsx("span",{children:" N/A"})]})},{id:"from.label",header:i("To"),cell:({row:e})=>e.original.to||"withdraw"!==e.original.type?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(N.qE,{className:"size-7 border-2 border-primary p-1",children:[a.jsx(N.F$,{src:e.original.to?.image,alt:e.original.to?.label}),a.jsx(N.Q5,{children:(0,w.v)(e.original.to.label)})]}),a.jsx("span",{children:e.original.to.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[a.jsx("span",{className:"block size-5 rounded-full bg-primary"}),a.jsx("span",{children:" N/A"})]})},{id:"amount",header:i("Amount"),cell:({row:e})=>{let t=e.original;return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,S.EQ)(t).with({type:"exchange"},()=>k.format(t?.metaData?.amountFrom,t?.metaData?.currencyFrom)).with({type:"deposit"},()=>k.format(t.amount,t?.metaData?.currency)).otherwise(()=>k.format(t.amount,t?.from?.currency))})}},{id:"fee",header:i("Fee"),cell:({row:e})=>{let t=e.original;return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,S.EQ)(t).with({type:"exchange"},()=>k.format(t?.fee,t.metaData?.currency)).with({type:"deposit"},()=>k.format(t.fee,t.metaData?.currency)).otherwise(()=>k.format(t.fee,t.from?.currency))})}},{id:"total",header:i("After Processing"),cell:({row:e})=>{let t=e.original;return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,S.EQ)(t).with({type:"exchange"},()=>k.format(t.total,t.metaData?.currencyTo)).with({type:"deposit"},()=>k.format(t.total,t.metaData?.currency)).otherwise(()=>k.format(t.total,t.to?.currency))})}},{id:"status",header:i("Status"),cell:({row:e})=>e.original?.status==="completed"?a.jsx(y.C,{variant:"success",children:i((0,x.fl)(e.original?.status))}):e.original?.status==="pending"?a.jsx(y.C,{variant:"secondary",className:"bg-muted",children:i((0,x.fl)(e.original?.status))}):e.original?.status==="failed"?a.jsx(y.C,{variant:"destructive",children:i((0,x.fl)(e.original?.status))}):a.jsx(y.C,{variant:"secondary",className:"bg-muted",children:i("Pending")})},{id:"trxId",header:i("Trx ID"),cell:({row:e})=>e.original?.trxId?a.jsx("span",{className:"text-xs font-normal text-foreground",children:e.original?.trxId}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})},{id:"method",header:i("Method"),cell:({row:e})=>e.original?.method?a.jsx("span",{className:"line-clamp-2 w-[100px] text-sm font-normal text-foreground",children:e.original?.method}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})},{id:"menu",header:i("Menu"),cell:({row:e})=>a.jsx(j.Z,{row:e.original})}]})}function A(){let e=(0,u.useSearchParams)(),[t,s]=p.useState(e.get("search")??""),h=(0,u.useRouter)(),g=(0,u.usePathname)(),{t:j}=(0,f.$G)(),{data:N,meta:y,isLoading:b,refresh:w,filter:v}=(0,m.Z)(`/transactions/?${e.toString()}`,{keepPreviousData:!0});return a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,a.jsxs)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:[a.jsx(i.Z,{filter:v}),(0,a.jsxs)("div",{className:"flex h-full w-full items-center gap-2.5 px-4 py-2.5 sm:w-40 sm:px-0",children:[a.jsx(o.X,{id:"bookmark",defaultChecked:"true"===e.get("bookmark"),onCheckedChange:e=>v("bookmark",e.toString()),className:"border-foreground/40 data-[state=checked]:border-primary"}),a.jsx(d.Z,{htmlFor:"bookmark",className:"text-sm font-normal hover:cursor-pointer",children:j("Show bookmarks")})]})]}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center gap-4 md:flex-nowrap md:justify-end",children:[a.jsx(r.R,{value:t,onChange:e=>{e.preventDefault();let t=(0,x.w4)(e.target.value);s(e.target.value),h.replace(`${g}?${t.toString()}`)},iconPlacement:"end",placeholder:j("Search..."),containerClass:"w-full sm:w-auto"}),a.jsx(l.k,{canFilterByAgent:!0,canFilterByGateway:!0,canFilterByMethod:!0}),a.jsx(n._,{url:"/transactions/export/all",align:"end"})]}),a.jsx("div",{})]}),a.jsx(c.Z,{className:"my-4"}),a.jsx(z,{data:N,meta:y,isLoading:b,refresh:w})]})})}},55457:(e,t,s)=>{"use strict";s.d(t,{Z:()=>y});var a=s(10326),r=s(17577),n=s(2454),l=s(30811),i=s(77863),o=s(6216),d=s(29169),c=s(40420),m=s(78564),x=s(53105),u=s(81770),p=s(57900),h=s(29764),f=s(75629),g=s(90748),j=s(35047),N=s(70012);function y({filter:e}){let t=(0,j.useSearchParams)(),{t:s}=(0,N.$G)(),[y,b]=r.useState(!1);return(0,a.jsxs)(l.J2,{open:y,onOpenChange:b,children:[(0,a.jsxs)(l.xo,{className:"flex h-10 w-full items-center gap-2 rounded-sm bg-background px-3 text-foreground shadow-defaultLite sm:w-72",children:[a.jsx("span",{className:"line-clamp-1 flex-1 text-left font-medium leading-[22px] text-foreground",children:t.get("type")?(0,i.fl)(t.get("type")):s("All Transactions")}),a.jsx(o.Z,{size:"24",strokeWidth:1.5,className:"text-secondary-text"})]}),a.jsx(l.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:a.jsx(n.mY,{className:"p-1",children:a.jsx(n.e8,{className:"max-h-[450px]",children:(0,a.jsxs)(n.fu,{className:"p-0",children:[a.jsx("div",{className:"px-2 py-1.5",children:a.jsx("p",{className:"text-[10px] font-normal leading-4 text-secondary-text",children:s("Select what you want to see")})}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(g.Z,{size:24}),s("All Transactions")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","deposit",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(d.Z,{size:24}),s("Deposits")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","transfer",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(c.Z,{size:24}),s("Transfer")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","withdraw",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(m.Z,{size:"24"}),s("Withdraws")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","exchange",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(x.Z,{size:"24"}),s("Exchange")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","payment",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(u.Z,{size:"24"}),s("Payment")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","service",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(p.Z,{size:"24"}),s("Services")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","investment",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(h.Z,{size:"24"}),s("Investments")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","investment_return",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(h.Z,{size:"24"}),s("Investment return")]}),a.jsx(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","referral_bonus",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[a.jsx(f.Z,{size:"24"}),s("Referral bonus")]})]})})})})]})}},95757:(e,t,s)=>{"use strict";s.d(t,{Z:()=>x});var a=s(10326),r=s(90772),n=s(30464),l=s(23182),i=s(27922),o=s(35047),d=s(70012),c=s(85999),m=s(7291);function x({row:e}){let{mutate:t}=(0,m.kY)(),s=(0,o.useSearchParams)(),{t:x}=(0,d.$G)(),u=e=>{c.toast.promise((0,n.y)(e.toString()),{loading:x("Processing..."),success:e=>{if(!e.status)throw Error(e.message);return t(`/transactions?page=${s.get("page")??1}&limit=${s.get("limit")??10}`),e.message},error:e=>e.message})};return a.jsx("div",{className:"flex items-center gap-2",children:a.jsx(r.z,{type:"button",variant:"outline",size:"icon",className:"h-8 w-8",onClick:()=>u(e.id),children:e?.isBookmarked?a.jsx(l.Z,{size:"20",variant:"Bold",color:"#D13438"}):a.jsx(i.Z,{size:"20",color:"#D13438"})})})}},53313:(e,t,s)=>{"use strict";s.d(t,{X:()=>o});var a=s(10326),r=s(13635),n=s(32933),l=s(17577),i=s(77863);let o=l.forwardRef(({className:e,...t},s)=>a.jsx(r.fC,{ref:s,className:(0,i.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:a.jsx(r.z$,{className:(0,i.ZP)("flex items-center justify-center text-current"),children:a.jsx(n.Z,{className:"h-4 w-4"})})}));o.displayName=r.fC.displayName},30464:(e,t,s)=>{"use strict";s.d(t,{y:()=>n});var a=s(49547),r=s(10734);async function n(e,t){try{let s=await a.Z.put(`${t??"/transactions/toggle-bookmark"}/${e}`,{id:e});return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}},66114:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var a=s(72450),r=s(71305),n=s(79308);class l{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new a.n(e?.user),customer:e?.user?.customer?new n.O(e?.user?.customer):null,merchant:e?.user?.merchant?new n.O(e?.user?.merchant):null,agent:e?.user?.agent?new n.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,r.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,r.WU)(this.updatedAt,e):"N/A"}}},72450:(e,t,s)=>{"use strict";s.d(t,{n:()=>l});var a=s(13263),r=s(13573),n=s(77863);class l{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,n.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new r.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}},84514:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(40099),n=s(76609);function l({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(n.Z,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}s(71159)},18406:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(19510),r=s(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},61594:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(19510),r=s(48413);function n(){return a.jsx("div",{className:"flex justify-center",children:a.jsx(r.a,{})})}},17412:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\transaction-history\page.tsx#default`)},88728:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(40099),n=s(76609);function l({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(n.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}s(71159)},80549:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(19510),r=s(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},74917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(19510),r=s(48413);function n(){return a.jsx("div",{className:"flex justify-center",children:a.jsx(r.a,{})})}},27536:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\transaction-history\page.tsx#default`)},74242:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(19510),r=s(48413);function n(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},10654:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\transaction-history\page.tsx#default`)}};