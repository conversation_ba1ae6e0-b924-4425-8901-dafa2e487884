{"version": 3, "file": "app/(protected)/@admin/customers/[customerId]/send-email/page.js", "mappings": "qFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,2CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,kGEAAC,gxBDKA,IAAAC,EACA,CACA,GACA,CACAC,SAAA,CACA,cACA,CACAC,MAAA,CACA,WACA,CACAD,SAAA,CACA,YACA,CACAA,SAAA,CACA,eACA,CACAA,SAAA,CACA,aACA,CACAA,SAAA,eAAiC,CACjCE,KAAA,KAAuBC,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAA4K,4IAE1L,EAET,CACA,aAA0BH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA+K,+IAGzM,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkK,iIAC3L,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAmK,mIAGrL,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoJ,mHAC7K,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqJ,qHAGvK,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAyI,wGAClK,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0I,0GAG5J,CACAN,SAAA,CACA,cACA,GACA,CACAQ,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAG,MAAA,CACA,cACA,GACA,CACAD,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAI,SAAA,CACA,cACA,GACA,CACAF,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAK,SAAA,CACA,cACA,GACA,CACAH,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAiI,gGAC1J,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkI,kGAGpJ,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoH,mFAC7I,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqH,oFACvI,iBAAoBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAAuH,uFAG3I,CAEAM,EAAA,4IAKOC,EAAA,6DACAC,EAAA,CACPjB,QAJ6BS,EAK7BS,UAJA,IAAAZ,QAAAC,OAAA,EAKA,EAGOY,EAAA,IAAwBC,EAAAC,kBAAkB,EACjDC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,QAAA,CACvBrB,KAAA,6DACAsB,SAAA,qCAEAC,WAAA,GACAC,SAAA,GACAC,SAAA,IAEAC,SAAA,CACAC,WAAA9B,CACA,CACA,uCCxGA,IAAA+B,EAAA,GAAAC,EAAAC,KAAAC,KAAA,CAAAF,GAAAG,KAAAA,EACAC,EAAAC,KAAAC,gBAAA,CACAC,EAAAR,EAAAM,KAAAG,yBAAA,EACAC,EAAA,MAAA1C,CAAAA,EAAAsC,KAAAK,cAAA,SAAA3C,CAAA,+DACA4C,EAAAZ,EAAAM,KAAAO,qBAAA,EAEAC,EAAAd,EAAAM,KAAAS,oBAAA,EACAC,EAAAhB,EAAAM,KAAAW,qCAAA,MACAP,GAAAE,GACI,GAAAM,EAAAC,EAAA,EAA8B,CAClCC,wBAAAV,EACAW,sBAAAT,EACAU,gBAAyB,GAAAC,EAAAC,CAAA,EAAqB,CAC9CH,sBAAAT,EACAa,SAAA,4DACA,EACA,GAEA,IAAMC,EAAS,GAAAC,EAAAC,CAAA,EAAS,CACxBC,UAAeC,EAAAC,CAAU,CAAAC,GAAA,CACzBC,IAvBA,GAwBA7D,KAAA,6DACA8D,OA9BA,KA+BAC,QAAWC,EACXC,SA/BA,KAgCAC,YA/BA,KAgCAC,SAnCA,KAoCAlC,cAAAA,EACAmC,aAAgBC,EAAAC,CAAA,CAChBlC,sBAAAA,EACAY,wBAAAV,EACAW,sBAAAT,EACA+B,cAlCAvC,KAAAA,EAmCAwC,6BA5BAxC,KAAAA,EA6BAyC,OAnCA,CAAoB,OAAQ,qBAA0B,uBAA2B,YAAe,mDAAyD,qPAAwR,wTAAkV,iDAAuD,sCAAyC,CAAE,gCAAmC,iBAAsB,eAAkB,uDAA4D,iBAAoB,wCAA6C,KAAQ,kBAAmB,2BAA+B,iIAAuJ,uBAAyB,mEAA6E,cAAiB,oFAAiG,uBAAuB,0CAA2C,EAAE,QAAW,6BAA8B,GAAG,cAAiB,w6BAA4lC,uBAA0B,o9BAAs9B,+HAoCnqHC,QAAAC,QAAAC,GAAA,CAAAC,eAAA,CACAnC,iBAAAA,EACAoC,wBA/CA,KAgDAlC,0BAAAA,CACA,GACOmC,EAAqBf,EACb,SAAAgB,EAAAC,CAAA,EACf,MAAW,GAAAC,EAAAC,CAAA,EAAO,CAClB,GAAAF,CAAA,CACAG,iBAAwBC,EAAAC,CAAA,CACxBC,QAAiBjC,CACjB,EACA,kBCnEArD,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,yBCAAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,qQCyBO,IAAMoF,EAAU,OAER,SAASC,EAAsB,CAC5C3F,SAAAA,CAAQ,CAGT,EACC,IAAM4F,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTC,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,IACfC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTzE,EAAW0E,CAAAA,EAAAA,EAAAA,EAAAA,IAEX,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAERC,EAAO,CACX,CACEC,MAAOH,EAAE,mBACTI,KAAM,GAAAC,EAAAC,GAAA,EAACC,EAAAA,CAAQA,CAAAA,CAACC,KAAK,KAAKC,QAAQ,SAClCC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,WAAW,CAAC,EAAEhB,EAAaiB,QAAQ,GAAG,CAAC,CACnEC,GAAI,aACN,EACA,CACEV,MAAOH,EAAE,gBACTI,KAAM,GAAAC,EAAAC,GAAA,EAACQ,EAAAA,CAAKA,CAAAA,CAACN,KAAK,KAAKC,QAAQ,SAC/BC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,WAAW,cAAc,EAAEhB,EAAaiB,QAAQ,GAAG,CAAC,CAChFC,GAAI,cACN,EAEA,CACEV,MAAOH,EAAE,OACTI,KAAM,GAAAC,EAAAC,GAAA,EAACS,EAAAA,CAAcA,CAAAA,CAACP,KAAK,KAAKC,QAAQ,SACxCC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,WAAW,KAAK,EAAEhB,EAAaiB,QAAQ,GAAG,CAAC,CACvEC,GAAI,KACN,EACA,CACEV,MAAOH,EAAE,eACTI,KAAM,GAAAC,EAAAC,GAAA,EAACU,EAAAA,CAAOA,CAAAA,CAACR,KAAK,KAAKC,QAAQ,SACjCC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,WAAW,aAAa,EAAEhB,EAAaiB,QAAQ,GAAG,CAAC,CAC/EC,GAAI,aACN,EAEA,CACEV,MAAOH,EAAE,cACTI,KAAM,GAAAC,EAAAC,GAAA,EAACW,EAAAA,CAAGA,CAAAA,CAACT,KAAK,KAAKC,QAAQ,SAC7BC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,WAAW,YAAY,EAAEhB,EAAaiB,QAAQ,GAAG,CAAC,CAC9EC,GAAI,YACN,EACD,CAEKK,EAASC,IAAAA,OAAOxB,EAAayB,GAAG,CAAC,WAEvC,MACE,GAAAf,EAAAgB,IAAA,EAAAhB,EAAAiB,QAAA,YACE,GAAAjB,EAAAgB,IAAA,EAACE,MAAAA,CAAIC,UAAU,+FACb,GAAAnB,EAAAgB,IAAA,EAACE,MAAAA,CAAIC,UAAU,mEACb,GAAAnB,EAAAgB,IAAA,EAACI,KAAAA,CAAGD,UAAU,iJACZ,GAAAnB,EAAAC,GAAA,EAACoB,KAAAA,UACC,GAAArB,EAAAgB,IAAA,EAACM,EAAAA,CAAIA,CAAAA,CACHjB,KAAK,aACLc,UAAU,0FAEV,GAAAnB,EAAAC,GAAA,EAACsB,EAAAA,CAAUA,CAAAA,CAACJ,UAAU,qBACrBxB,EAAE,aAGP,GAAAK,EAAAgB,IAAA,EAACK,KAAAA,CAAGF,UAAU,2CAAiC,KAC1C7B,EAAayB,GAAG,CAAC,WAEtB,GAAAf,EAAAgB,IAAA,EAACK,KAAAA,CAAGF,UAAU,2CAAiC,KAC1CxB,EAAE,QAAQ,KAAGP,EAAOkB,UAAU,OAGrC,GAAAN,EAAAgB,IAAA,EAACE,MAAAA,CAAIC,UAAU,wEACb,GAAAnB,EAAAC,GAAA,EAACuB,OAAAA,UAAM7B,EAAE,YACT,GAAAK,EAAAC,GAAA,EAACwB,EAAAA,CAAMA,CAAAA,CACLN,UAAU,kCACVO,eAAgBb,EAChBc,gBAAiB,IACfC,EAAAA,KAAKA,CAACC,OAAO,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,EAAe1C,EAAOkB,UAAU,EAAa,CACzDyB,QAASpC,EAAE,cACXqC,QAAS,IACP,GAAI,CAACC,EAAIpB,MAAM,CAAE,MAAM,MAAUoB,EAAIC,OAAO,EAC5C,IAAMC,EAAK,IAAIC,gBAAgB9C,GAI/B,OAHA6C,EAAGE,GAAG,CAAC,SAAUC,EAAU,IAAM,KACjCC,CAAAA,EAAAA,EAAAA,CAAAA,EAAO,CAAC,iBAAiB,EAAEnD,EAAOkB,UAAU,CAAC,CAAC,EAC9Cd,EAAOgD,IAAI,CAAC,CAAC,EAAExH,EAAS,CAAC,EAAEmH,EAAG5B,QAAQ,GAAG,CAAC,EACnC0B,EAAIC,OAAO,EAEpBO,MAAO,GAASC,EAAIR,OAAO,EAE/B,UAKN,GAAAlC,EAAAC,GAAA,EAAC0C,EAAAA,CAAYA,CAAAA,CAAC9C,KAAMA,OAGrBrG,IAGP,kPCjGA,IAAMoJ,EAAaC,EAAAA,CAACA,CAACC,MAAM,CAAC,CAC1BC,QAASF,EAAAA,CAACA,CAACG,MAAM,CAAC,CAAEC,eAAgB,sBAAuB,GAC3Df,QAASW,EAAAA,CAACA,CAACG,MAAM,CAAC,CAAEC,eAAgB,yBAA0B,EAChE,GAIe,SAASC,IACtB,GAAM,CAACC,EAAWC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,aAAAA,IAC/B/D,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,IACfH,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IAET,CAAEM,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAER0D,EAAOC,CAAAA,EAAAA,EAAAA,EAAAA,EAAmB,CAC9BC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,EAAYb,GACtBc,cAAe,CACbX,QAAS,GACTb,QAAS,EACX,CACF,GAaA,MACE,GAAAlC,EAAAC,GAAA,EAAC0D,EAAAA,EAASA,CAAAA,CAACC,KAAK,WAAWC,aAAc,CAAC,MAAM,UAC9C,GAAA7D,EAAAC,GAAA,EAACiB,MAAAA,CAAIC,UAAU,mCACb,GAAAnB,EAAAC,GAAA,EAACiB,MAAAA,CAAIC,UAAU,yDACb,GAAAnB,EAAAgB,IAAA,EAAC8C,EAAAA,EAAaA,CAAAA,CAACC,MAAM,MAAM5C,UAAU,kCACnC,GAAAnB,EAAAC,GAAA,EAAC+D,EAAAA,EAAgBA,CAAAA,CAAC7C,UAAU,mCAC1B,GAAAnB,EAAAC,GAAA,EAACiB,MAAAA,CAAIC,UAAU,mCACb,GAAAnB,EAAAgB,IAAA,EAACiD,IAAAA,CAAE9C,UAAU,iDACVxB,EAAE,oBAAoB,IAAEL,EAAayB,GAAG,CAAC,eAIhD,GAAAf,EAAAC,GAAA,EAACiE,EAAAA,EAAgBA,CAAAA,CAAC/C,UAAU,iEAC1B,GAAAnB,EAAAC,GAAA,EAACkE,EAAAA,EAAIA,CAAAA,CAAE,GAAGb,CAAI,UACZ,GAAAtD,EAAAgB,IAAA,EAACsC,OAAAA,CACCc,SAAUd,EAAKe,YAAY,CA1B1B,IACfjB,EAAgB,UACd,IAAMnB,EAAM,MAAMqC,CAAAA,EAAAA,EAAAA,CAAAA,EAASC,EAAQnF,EAAOkB,UAAU,CAChD2B,CAAAA,EAAIpB,MAAM,CACZe,EAAAA,KAAKA,CAACI,OAAO,CAACC,EAAIC,OAAO,EAEzBN,EAAAA,KAAKA,CAACa,KAAK,CAAC9C,EAAEsC,EAAIC,OAAO,EAE7B,EACF,GAkBgBf,UAAU,gCAEV,GAAAnB,EAAAC,GAAA,EAACuE,EAAAA,EAASA,CAAAA,CACRC,KAAK,UACLC,QAASpB,EAAKoB,OAAO,CACrBzH,OAAQ,CAAC,CAAE0H,MAAAA,CAAK,CAAO,GACrB,GAAA3E,EAAAgB,IAAA,EAAC4D,EAAAA,EAAQA,CAAAA,WACP,GAAA5E,EAAAC,GAAA,EAAC4E,EAAAA,EAASA,CAAAA,UAAElF,EAAE,aACd,GAAAK,EAAAC,GAAA,EAAC6E,EAAAA,CAAKA,CAAAA,CACJlB,KAAK,OACLmB,YAAapF,EAAE,2BACd,GAAGgF,CAAK,GAEX,GAAA3E,EAAAC,GAAA,EAAC+E,EAAAA,EAAWA,CAAAA,CAAAA,QAKlB,GAAAhF,EAAAC,GAAA,EAACuE,EAAAA,EAASA,CAAAA,CACRC,KAAK,UACLC,QAASpB,EAAKoB,OAAO,CACrBzH,OAAQ,CAAC,CAAE0H,MAAAA,CAAK,CAAO,GACrB,GAAA3E,EAAAgB,IAAA,EAAC4D,EAAAA,EAAQA,CAAAA,WACP,GAAA5E,EAAAC,GAAA,EAAC4E,EAAAA,EAASA,CAAAA,UAAElF,EAAE,aACd,GAAAK,EAAAC,GAAA,EAACgF,EAAAA,CAAQA,CAAAA,CACPF,YAAapF,EAAE,2BACfuF,KAAM,GACL,GAAGP,CAAK,GAEX,GAAA3E,EAAAC,GAAA,EAAC+E,EAAAA,EAAWA,CAAAA,CAAAA,QAKlB,GAAAhF,EAAAC,GAAA,EAACiB,MAAAA,CAAIC,UAAU,+CACb,GAAAnB,EAAAC,GAAA,EAACkF,EAAAA,CAAMA,CAAAA,CAACC,SAAUjC,EAAWhC,UAAU,sBACpCgC,EACC,GAAAnD,EAAAC,GAAA,EAACoF,EAAAA,MAAMA,CAAAA,CACLvF,MAAOH,EAAE,aACTwB,UAAU,4BAGZ,GAAAnB,EAAAgB,IAAA,EAAAhB,EAAAiB,QAAA,YACGtB,EAAE,QACH,GAAAK,EAAAC,GAAA,EAACqF,EAAAA,CAAWA,CAAAA,CAACnF,KAAM,2BAa7C,qdCrIe,SAASoF,IACtB,MACE,GAAAvF,EAAAC,GAAA,EAACiB,MAAAA,CAAIC,UAAU,kDACb,GAAAnB,EAAAC,GAAA,EAACoF,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,wFCNe,SAASE,IACtB,MACE,GAAAvF,EAAAC,GAAA,EAACiB,MAAAA,CAAIC,UAAU,kDACb,GAAAnB,EAAAC,GAAA,EAACoF,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,+PCNe,SAASG,EAAe,CACrChM,SAAAA,CAAQ,CAGT,EACC,OAAOA,CACT,8HCNe,SAAS+L,IACtB,MACE,GAAAvF,EAAAC,GAAA,EAACiB,MAAAA,CAAIC,UAAU,kDACb,GAAAnB,EAAAC,GAAA,EAACoF,EAAAA,CAAMA,CAAAA,CAAAA,IAGb", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/(protected)/@admin/customers/[customerId]/send-email/page.tsx?86c1", "webpack://_N_E/|ssr?6c30", "webpack://_N_E/?aafc", "webpack://_N_E/?9f07", "webpack://_N_E/./app/(protected)/@admin/customers/[customerId]/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/customers/[customerId]/send-email/page.tsx", "webpack://_N_E/./app/(protected)/@admin/customers/[customerId]/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/customers/[customerId]/send-email/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/customers/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/customers/loading.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "\"TURBOPACK { transition: next-ssr }\";\nimport { AppPageRouteModule } from \"next/dist/server/future/route-modules/app-page/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(protected)',\n        {\n        admin: [\n        'children',\n        {\n        children: [\n        'customers',\n        {\n        children: [\n        '[customerId]',\n        {\n        children: [\n        'send-email',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\send-email\\\\page.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\send-email\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\send-email\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\send-email\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"],\n        \n      }\n      ],\nchildren: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nagent: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\ncustomer: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nmerchant: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\send-email\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const originalPathname = \"/(protected)/@admin/customers/[customerId]/send-email/page\";\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(protected)/@admin/customers/[customerId]/send-email/page\",\n        pathname: \"/customers/[customerId]/send-email\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2F(protected)%2F%40admin%2Fcustomers%2F%5BcustomerId%5D%2Fsend-email%2Fpage&page=%2F(protected)%2F%40admin%2Fcustomers%2F%5BcustomerId%5D%2Fsend-email%2Fpage&pagePath=private-next-app-dir%2F(protected)%2F%40admin%2Fcustomers%2F%5BcustomerId%5D%2Fsend-email%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CIDEES%20SQQS%5CPAYSNAPo%5CMain%5Cfrontend%5Capp&appPaths=%2F(protected)%2F%40admin%2Fcustomers%2F%5BcustomerId%5D%2Fsend-email%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/(protected)/@admin/customers/[customerId]/send-email/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"webpack\":null,\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"analyticsId\":\"\",\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":200,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"inline\",\"remotePatterns\":[{\"protocol\":\"http\",\"hostname\":\"localhost\"},{\"protocol\":\"https\",\"hostname\":\"**\"}],\"unoptimized\":false},\"devIndicators\":{\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"optimizeFonts\":true,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"httpAgentOptions\":{\"keepAlive\":true},\"outputFileTracing\":true,\"staticPageGenerationTimeout\":60,\"swcMinify\":true,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"experimental\":{\"multiZoneDraftMode\":false,\"prerenderEarlyExit\":false,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"outputFileTracingRoot\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\",\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"adjustFontFallbacks\":false,\"adjustFontFallbacksWithSizeAdjust\":false,\"typedRoutes\":false,\"instrumentationHook\":false,\"bundlePagesExternals\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"missingSuspenseWithCSRBailout\":true,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":30,\"static\":300},\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"configFile\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/(protected)/@admin/customers/[customerId]/send-email/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest,\n            pageName: \"/(protected)/@admin/customers/[customerId]/send-email/page\"\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/(protected)/@admin/customers/[customerId]/send-email/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\layout.tsx\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\customers\\\\[customerId]\\\\send-email\\\\page.tsx\");\n", "\"use client\";\r\n\r\nimport { SecondaryNav } from \"@/components/common/layout/SecondaryNav\";\r\nimport Switch from \"@/components/ui/switch\";\r\nimport { toggleActivity } from \"@/data/admin/toggleActivity\";\r\nimport {\r\n  ArrowLeft2,\r\n  Candle2,\r\n  Clock,\r\n  ShieldSecurity,\r\n  Sms,\r\n  UserEdit,\r\n} from \"iconsax-react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  useParams,\r\n  usePathname,\r\n  useRouter,\r\n  useSearchParams,\r\n} from \"next/navigation\";\r\nimport React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\nimport { mutate } from \"swr\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport default function CustomerDetailsLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const params = useParams();\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const { t } = useTranslation();\r\n\r\n  const tabs = [\r\n    {\r\n      title: t(\"Account Details\"),\r\n      icon: <UserEdit size=\"24\" variant=\"Bulk\" />,\r\n      href: `/customers/${params?.customerId}?${searchParams.toString()}`,\r\n      id: \"__DEFAULT__\",\r\n    },\r\n    {\r\n      title: t(\"Transactions\"),\r\n      icon: <Clock size=\"24\" variant=\"Bulk\" />,\r\n      href: `/customers/${params?.customerId}/transactions?${searchParams.toString()}`,\r\n      id: \"transactions\",\r\n    },\r\n\r\n    {\r\n      title: t(\"KYC\"),\r\n      icon: <ShieldSecurity size=\"24\" variant=\"Bulk\" />,\r\n      href: `/customers/${params?.customerId}/kyc?${searchParams.toString()}`,\r\n      id: \"kyc\",\r\n    },\r\n    {\r\n      title: t(\"Permissions\"),\r\n      icon: <Candle2 size=\"24\" variant=\"Bulk\" />,\r\n      href: `/customers/${params?.customerId}/permissions?${searchParams.toString()}`,\r\n      id: \"permissions\",\r\n    },\r\n\r\n    {\r\n      title: t(\"Send Email\"),\r\n      icon: <Sms size=\"24\" variant=\"Bulk\" />,\r\n      href: `/customers/${params?.customerId}/send-email?${searchParams.toString()}`,\r\n      id: \"send-email\",\r\n    },\r\n  ];\r\n\r\n  const status = Number(searchParams.get(\"active\")) === 1;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4\">\r\n        <div className=\"mb-4 flex flex-wrap items-center justify-between gap-2\">\r\n          <ul className=\"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center\">\r\n            <li>\r\n              <Link\r\n                href=\"/customers\"\r\n                className=\"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4\"\r\n              >\r\n                <ArrowLeft2 className=\"size-4 sm:size-6\" />\r\n                {t(\"Back\")}\r\n              </Link>\r\n            </li>\r\n            <li className=\"line-clamp-1 whitespace-nowrap\">\r\n              / {searchParams.get(\"name\")}\r\n            </li>\r\n            <li className=\"line-clamp-1 whitespace-nowrap\">\r\n              / {t(\"User\")} #{params.customerId}\r\n            </li>\r\n          </ul>\r\n          <div className=\"ml-auto inline-flex items-center gap-2 text-sm sm:text-base\">\r\n            <span>{t(\"Active\")}</span>\r\n            <Switch\r\n              className=\"data-[state=unchecked]:bg-muted\"\r\n              defaultChecked={status}\r\n              onCheckedChange={(checked) => {\r\n                toast.promise(toggleActivity(params.customerId as string), {\r\n                  loading: t(\"Loading...\"),\r\n                  success: (res) => {\r\n                    if (!res.status) throw new Error(res.message);\r\n                    const sp = new URLSearchParams(searchParams);\r\n                    sp.set(\"active\", checked ? \"1\" : \"0\");\r\n                    mutate(`/admin/customers/${params.customerId}`);\r\n                    router.push(`${pathname}?${sp.toString()}`);\r\n                    return res.message;\r\n                  },\r\n                  error: (err) => err.message,\r\n                });\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <SecondaryNav tabs={tabs} />\r\n      </div>\r\n\r\n      {children}\r\n    </>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport { Loader } from \"@/components/common/Loader\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { sendMail } from \"@/data/admin/sendMail\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { ArrowRight2 } from \"iconsax-react\";\r\nimport { useParams, useSearchParams } from \"next/navigation\";\r\nimport { useTransition } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\nimport { z } from \"zod\";\r\n\r\nconst formSchema = z.object({\r\n  subject: z.string({ required_error: \"User Key is required\" }),\r\n  message: z.string({ required_error: \"User Secret is required\" }),\r\n});\r\n\r\ntype TFormData = z.infer<typeof formSchema>;\r\n\r\nexport default function APIMobileMoney() {\r\n  const [isPending, startTransition] = useTransition();\r\n  const searchParams = useSearchParams();\r\n  const params = useParams();\r\n\r\n  const { t } = useTranslation();\r\n\r\n  const form = useForm<TFormData>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      subject: \"\",\r\n      message: \"\",\r\n    },\r\n  });\r\n\r\n  const onSubmit = (values: TFormData) => {\r\n    startTransition(async () => {\r\n      const res = await sendMail(values, params.customerId as string);\r\n      if (res.status) {\r\n        toast.success(res.message);\r\n      } else {\r\n        toast.error(t(res.message));\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Accordion type=\"multiple\" defaultValue={[\"API\"]}>\r\n      <div className=\"flex flex-col gap-4 p-4\">\r\n        <div className=\"rounded-xl border border-border bg-background\">\r\n          <AccordionItem value=\"API\" className=\"border-none px-4 py-0\">\r\n            <AccordionTrigger className=\"py-6 hover:no-underline\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <p className=\"text-base font-medium leading-[22px]\">\r\n                  {t(\"Send an email to\")} {searchParams.get(\"name\")}\r\n                </p>\r\n              </div>\r\n            </AccordionTrigger>\r\n            <AccordionContent className=\"flex flex-col gap-6 border-t border-divider px-1 pt-4\">\r\n              <Form {...form}>\r\n                <form\r\n                  onSubmit={form.handleSubmit(onSubmit)}\r\n                  className=\"flex flex-col gap-4\"\r\n                >\r\n                  <FormField\r\n                    name=\"subject\"\r\n                    control={form.control}\r\n                    render={({ field }: any) => (\r\n                      <FormItem>\r\n                        <FormLabel>{t(\"Subject\")}</FormLabel>\r\n                        <Input\r\n                          type=\"text\"\r\n                          placeholder={t(\"Subject of your mail...\")}\r\n                          {...field}\r\n                        />\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    name=\"message\"\r\n                    control={form.control}\r\n                    render={({ field }: any) => (\r\n                      <FormItem>\r\n                        <FormLabel>{t(\"Message\")}</FormLabel>\r\n                        <Textarea\r\n                          placeholder={t(\"Write a message here...\")}\r\n                          rows={10}\r\n                          {...field}\r\n                        />\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <div className=\"flex items-center justify-end gap-4\">\r\n                    <Button disabled={isPending} className=\"rounded-xl\">\r\n                      {isPending ? (\r\n                        <Loader\r\n                          title={t(\"Sending..\")}\r\n                          className=\"text-primary-foreground\"\r\n                        />\r\n                      ) : (\r\n                        <>\r\n                          {t(\"Send\")}\r\n                          <ArrowRight2 size={16} />\r\n                        </>\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                </form>\r\n              </Form>\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </div>\r\n      </div>\r\n    </Accordion>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import React from \"react\";\r\n\r\nexport default function CustomerLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return children;\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "_self___RSC_MANIFEST", "tree", "children", "admin", "page", "Promise", "resolve", "then", "__webpack_require__", "bind", "defaultPage", "agent", "customer", "merchant", "pages", "originalPathname", "__next_app__", "loadChunk", "routeModule", "module_compiled", "AppPageRouteModule", "definition", "kind", "route_kind", "x", "APP_PAGE", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "self", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "encryption_utils", "Mo", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "action_utils", "w", "pageName", "page_stringifiedConfig_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_pagesType_app_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRmN1c3RvbWVycyUyRiU1QmN1c3RvbWVySWQlNUQlMkZzZW5kLWVtYWlsJTJGcGFnZSZwYWdlPSUyRihwcm90ZWN0ZWQpJTJGJTQwYWRtaW4lMkZjdXN0b21lcnMlMkYlNUJjdXN0b21lcklkJTVEJTJGc2VuZC1lbWFpbCUyRnBhZ2UmcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkYocHJvdGVjdGVkKSUyRiU0MGFkbWluJTJGY3VzdG9tZXJzJTJGJTVCY3VzdG9tZXJJZCU1RCUyRnNlbmQtZW1haWwlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rlc2t0b3AlNUNJREVFUyUyMFNRUVMlNUNQQVlTTkFQbyU1Q01haW4lNUNmcm9udGVuZCU1Q2FwcCZhcHBQYXRocz0lMkYocHJvdGVjdGVkKSUyRiU0MGFkbWluJTJGY3VzdG9tZXJzJTJGJTVCY3VzdG9tZXJJZCU1RCUyRnNlbmQtZW1haWwlMkZwYWdlJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEIQ_sriEnabled_false_middlewareConfig_e30_render", "render", "d", "pagesType", "page_types", "s", "APP", "dev", "appMod", "pageMod", "page_next_edge_ssr_entry_namespaceObject", "errorMod", "error500Mod", "Document", "renderToHTML", "app_render", "f", "serverActions", "subresourceIntegrityManifest", "config", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "adapter", "C", "IncrementalCache", "incremental_cache", "k", "handler", "runtime", "CustomerDetailsLayout", "params", "useParams", "searchParams", "useSearchParams", "router", "useRouter", "usePathname", "t", "useTranslation", "tabs", "title", "icon", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "UserEdit", "size", "variant", "href", "customerId", "toString", "id", "Clock", "ShieldSecurity", "Candle2", "Sms", "status", "Number", "get", "jsxs", "Fragment", "div", "className", "ul", "li", "Link", "ArrowLeft2", "span", "Switch", "defaultChecked", "onCheckedChange", "toast", "promise", "toggleActivity", "loading", "success", "res", "message", "sp", "URLSearchParams", "set", "checked", "mutate", "push", "error", "err", "SecondaryNav", "formSchema", "z", "object", "subject", "string", "required_error", "APIMobileMoney", "isPending", "startTransition", "useTransition", "form", "useForm", "resolver", "zodResolver", "defaultValues", "Accordion", "type", "defaultValue", "AccordionItem", "value", "AccordionTrigger", "p", "Accordi<PERSON><PERSON><PERSON><PERSON>", "Form", "onSubmit", "handleSubmit", "sendMail", "values", "FormField", "name", "control", "field", "FormItem", "FormLabel", "Input", "placeholder", "FormMessage", "Textarea", "rows", "<PERSON><PERSON>", "disabled", "Loader", "ArrowRight2", "Loading", "CustomerLayout"], "sourceRoot": ""}