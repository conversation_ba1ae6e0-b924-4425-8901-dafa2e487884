(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[24418],{51963:function(e,t,r){Promise.resolve().then(r.bind(r,18691))},18691:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return b}});var n=r(57437),a=r(25833),i=r(85539),o=r(16831),l=r(35974),s=r(62869),d=r(66070),c=r(75730),u=r(94508),f=r(59532),m=r(8877),g=r(58926),v=r(27648),x=r(99376),h=r(2265),p=r(43949);function b(){var e;let{t}=(0,p.$G)(),r=(0,x.useSearchParams)(),[b,y]=h.useState([]),[N,j]=h.useState(null!==(e=r.get("search"))&&void 0!==e?e:""),w=(0,x.useRouter)(),C=(0,x.usePathname)(),{data:P,meta:S,isLoading:R}=(0,c.Z)("/admin/methods?".concat(r.toString()));return(0,n.jsxs)(d.Zb,{className:"rounded-xl",children:[(0,n.jsxs)(d.Ol,{className:"flex items-start py-4 sm:flex-row sm:items-center",children:[(0,n.jsx)(d.ll,{className:"flex-1 text-base font-medium leading-[22px]",children:t("Withdraw Methods")}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(s.z,{size:"sm",variant:"outline",type:"button",className:"mr-2.5 text-sm",asChild:!0,children:(0,n.jsxs)(v.default,{href:"/settings/withdraw-methods/create",children:[(0,n.jsx)(m.Z,{}),(0,n.jsx)("span",{children:t("Add New Method")})]})}),(0,n.jsx)(i.R,{value:N,onChange:e=>{e.preventDefault();let t=(0,u.w4)(e.target.value);j(e.target.value),w.replace("".concat(C,"?").concat(t.toString()))},iconPlacement:"end",placeholder:t("Search...")})]})]}),(0,n.jsx)(d.aY,{className:"border-t border-divider py-4",children:(0,n.jsx)("div",{className:"flex flex-col gap-4",children:(0,n.jsx)(a.Z,{data:P,sorting:b,setSorting:y,isLoading:R,pagination:{total:null==S?void 0:S.total,page:null==S?void 0:S.currentPage,limit:null==S?void 0:S.perPage},structure:[{id:"id",header:t("Id"),cell:e=>{var t;let{row:r}=e;return(0,n.jsx)("div",{className:"font-normal text-secondary-text",children:"#".concat(null===(t=r.original)||void 0===t?void 0:t.id)})}},{id:"name",header:t("Name"),cell:e=>{var t,r,a,i,l,s;let{row:d}=e;return(0,n.jsxs)(v.default,{href:"/settings/withdraw-methods/".concat(null===(t=d.original)||void 0===t?void 0:t.id,"?name=").concat(null===(r=d.original)||void 0===r?void 0:r.name),className:"flex items-center gap-2 font-normal",children:[(0,n.jsxs)(o.qE,{children:[(0,n.jsx)(o.F$,{src:(0,u.qR)(null===(a=d.original)||void 0===a?void 0:a.logoImage)}),(0,n.jsx)(o.Q5,{className:"font-semibold",children:(0,f.v)(null===(l=d.original)||void 0===l?void 0:null===(i=l.name)||void 0===i?void 0:i.split(/\s+/)[0])})]}),(0,n.jsx)("span",{className:"text-secondary-text hover:text-primary hover:underline",children:null===(s=d.original)||void 0===s?void 0:s.name})]})}},{id:"currency",header:t("Currency"),cell:e=>{var t;let{row:r}=e;return(0,n.jsx)("div",{className:"font-normal text-secondary-text",children:(null===(t=r.original)||void 0===t?void 0:t.currencyCode)||"N/A"})}},{id:"limit",header:t("Limit"),cell:e=>{var t,r,a,i;let{row:o}=e;return(0,n.jsxs)("div",{className:"min-w-28 font-normal text-secondary-text",children:[null===(t=o.original)||void 0===t?void 0:t.minAmount," ",null===(r=o.original)||void 0===r?void 0:r.currencyCode," -"," ",null===(a=o.original)||void 0===a?void 0:a.maxAmount," ",null===(i=o.original)||void 0===i?void 0:i.currencyCode]})}},{id:"charge",header:t("Charge"),cell:e=>{var t,r,a;let{row:i}=e;return(0,n.jsxs)("div",{className:"min-w-28 font-normal text-secondary-text",children:[null===(t=i.original)||void 0===t?void 0:t.fixedCharge," ",null===(r=i.original)||void 0===r?void 0:r.currencyCode," +"," ",null===(a=i.original)||void 0===a?void 0:a.percentageCharge,"%"]})}},{id:"status",header:t("Status"),cell:e=>{var r,a;let{row:i}=e;return(0,n.jsx)(l.C,{variant:(null===(r=i.original)||void 0===r?void 0:r.active)?"success":"secondary",children:(null===(a=i.original)||void 0===a?void 0:a.active)?t("Active"):t("Inactive")})}},{id:"recommended",header:t("Recommended"),cell:e=>{var r,a;let{row:i}=e;return(0,n.jsx)(l.C,{variant:(null===(r=i.original)||void 0===r?void 0:r.recommended)?"important":"secondary",children:(null===(a=i.original)||void 0===a?void 0:a.recommended)?t("Yes"):t("No")})}},{id:"menu",header:t("Menu"),cell:e=>{var t,r;let{row:a}=e;return(0,n.jsx)(s.z,{size:"icon",variant:"outline",className:"size-8 bg-background p-1.5 text-primary",children:(0,n.jsx)(v.default,{href:"/settings/withdraw-methods/".concat(null===(t=a.original)||void 0===t?void 0:t.id,"?name=").concat(null===(r=a.original)||void 0===r?void 0:r.name),children:(0,n.jsx)(g.Z,{size:"20",className:"text-primary"})})})}}]})})})]})}},25833:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var n=r(57437),a=r(94508),i=r(71594),o=r(24525),l=r(73490),s=r(36887),d=r(64394),c=r(61756),u=r(99376),f=r(4751),m=r(2265),g=r(43949),v=r(62869),x=r(73578);function h(e){let{data:t,isLoading:r=!1,structure:h,sorting:p,setSorting:b,padding:y=!1,className:N,onRefresh:j,pagination:w}=e,C=(0,m.useMemo)(()=>h,[h]),P=(0,u.useRouter)(),S=(0,u.usePathname)(),R=(0,u.useSearchParams)(),{t:Z}=(0,g.$G)(),T=(0,i.b7)({data:t||[],columns:C,state:{sorting:p,onRefresh:j},onSortingChange:b,getCoreRowModel:(0,o.sC)(),getSortedRowModel:(0,o.tj)(),debugTable:!1});return r?(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:Z("Loading...")})}):(null==t?void 0:t.length)?(0,n.jsxs)("div",{className:(0,a.ZP)("".concat(y?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),N),children:[(0,n.jsxs)(x.iA,{children:[(0,n.jsx)(x.xD,{children:T.getHeaderGroups().map(e=>(0,n.jsx)(x.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,r,o,l;return(0,n.jsx)(x.ss,{className:(0,a.ZP)("",null==e?void 0:null===(o=e.column)||void 0===o?void 0:null===(r=o.columnDef)||void 0===r?void 0:null===(t=r.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,n.jsxs)(v.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[Z((0,i.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(l=({asc:(0,n.jsx)(s.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,n.jsx)(s.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==l?l:(0,n.jsx)(s.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,n.jsx)(x.RM,{children:T.getRowModel().rows.map(e=>(0,n.jsx)(x.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,n.jsx)(x.pj,{className:"py-3 text-sm font-semibold",children:(0,i.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),w&&w.total>10&&(0,n.jsx)("div",{className:"pb-2 pt-6",children:(0,n.jsx)(f.Z,{showTotal:(e,t)=>Z("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==w?void 0:w.page,total:null==w?void 0:w.total,pageSize:null==w?void 0:w.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(R);t.set("page",e.toString()),P.push("".concat(S,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(d.Z,{size:"18"})}),nextIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(c.Z,{size:"18"})})})})]}):(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,n.jsx)(l.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),Z("No data found!")]})})}},85539:function(e,t,r){"use strict";r.d(t,{R:function(){return l}});var n=r(57437);r(2265);var a=r(95186),i=r(94508),o=r(48674);function l(e){let{iconPlacement:t="start",className:r,containerClass:l,...s}=e;return(0,n.jsxs)("div",{className:(0,i.ZP)("relative flex items-center",l),children:[(0,n.jsx)(o.Z,{size:"20",className:(0,i.ZP)("absolute top-1/2 -translate-y-1/2","end"===t?"right-2.5":"left-2.5")}),(0,n.jsx)(a.I,{type:"text",className:(0,i.ZP)("h-10","end"===t?"pr-10":"pl-10",r),...s})]})}},16831:function(e,t,r){"use strict";r.d(t,{F$:function(){return s},Q5:function(){return d},qE:function(){return l}});var n=r(57437),a=r(2265),i=r(61146),o=r(94508);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.fC,{ref:t,className:(0,o.ZP)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...a})});l.displayName=i.fC.displayName;let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.Ee,{ref:t,className:(0,o.ZP)("aspect-square h-full w-full",r),...a})});s.displayName=i.Ee.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.NY,{ref:t,className:(0,o.ZP)("flex h-full w-full items-center justify-center rounded-full bg-muted",r),...a})});d.displayName=i.NY.displayName},35974:function(e,t,r){"use strict";r.d(t,{C:function(){return l}});var n=r(57437),a=r(90535);r(2265);var i=r(94508);let o=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{className:(0,i.ZP)(o({variant:r}),t),...a})}},62869:function(e,t,r){"use strict";r.d(t,{d:function(){return s},z:function(){return d}});var n=r(57437),a=r(37053),i=r(90535),o=r(2265),l=r(94508);let s=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef((e,t)=>{let{className:r,variant:i,size:o,asChild:d=!1,...c}=e,u=d?a.g7:"button";return(0,n.jsx)(u,{className:(0,l.ZP)(s({variant:i,size:o,className:r})),ref:t,...c})});d.displayName="Button"},66070:function(e,t,r){"use strict";r.d(t,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return o},aY:function(){return c},eW:function(){return u},ll:function(){return s}});var n=r(57437),a=r(2265),i=r(94508);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});o.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.ZP)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,i.ZP)("text-2xl font-semibold leading-none tracking-tight",r),...a})});s.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,i.ZP)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.ZP)("p-6 pt-0",r),...a})});c.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.ZP)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return o}});var n=r(57437),a=r(2265),i=r(94508);let o=a.forwardRef((e,t)=>{let{className:r,type:a,...o}=e;return(0,n.jsx)("input",{type:a,className:(0,i.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...o})});o.displayName="Input"},73578:function(e,t,r){"use strict";r.d(t,{RM:function(){return s},SC:function(){return d},iA:function(){return o},pj:function(){return u},ss:function(){return c},xD:function(){return l}});var n=r(57437),a=r(2265),i=r(94508);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,i.ZP)("w-full caption-bottom text-sm",r),...a})})});o.displayName="Table";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("thead",{ref:t,className:(0,i.ZP)("",r),...a})});l.displayName="TableHeader";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,i.ZP)("[&_tr:last-child]:border-0",r),...a})});s.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,i.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tr",{ref:t,className:(0,i.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});d.displayName="TableRow";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("th",{ref:t,className:(0,i.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});c.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("td",{ref:t,className:(0,i.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("caption",{ref:t,className:(0,i.ZP)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},31117:function(e,t,r){"use strict";r.d(t,{d:function(){return i}});var n=r(79981),a=r(85323);let i=(e,t)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},75730:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(31117),a=r(99376);function i(e,t){var r,i,o;let l=(0,a.usePathname)(),s=(0,a.useSearchParams)(),d=(0,a.useRouter)(),[c,u]=e.split("?"),f=new URLSearchParams(u);f.has("page")||f.set("page","1"),f.has("limit")||f.set("limit","10");let m="".concat(c,"?").concat(f.toString()),{data:g,error:v,isLoading:x,mutate:h,...p}=(0,n.d)(m,t);return{refresh:()=>h(g),data:null!==(o=null==g?void 0:null===(r=g.data)||void 0===r?void 0:r.data)&&void 0!==o?o:[],meta:null==g?void 0:null===(i=g.data)||void 0===i?void 0:i.meta,filter:(e,t,r)=>{let n=new URLSearchParams(s.toString());t?n.set(e,t.toString()):n.delete(e),d.replace("".concat(l,"?").concat(n.toString())),null==r||r()},isLoading:x,error:v,...p}}},79981:function(e,t,r){"use strict";var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return c},Fg:function(){return m},Fp:function(){return d},Qp:function(){return f},ZP:function(){return l},fl:function(){return s},qR:function(){return u},w4:function(){return g}});var n=r(78040),a=r(61994),i=r(14438),o=r(53335);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.m6)((0,a.W)(t))}function s(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>i.toast.success("Copied to clipboard!")).catch(()=>{i.toast.error("Failed to copy!")})};class c{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let i=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:i,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let o=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:i,l=a.format(e),s=l.substring(o.length).trim();return{currencyCode:i,currencySymbol:o,formattedAmount:l,amountText:s}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",g=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}},59532:function(e,t,r){"use strict";function n(e){if(!e)return"";let t=e.split(" ");return(t.length>2?t[0].length>3?t[0][0]+t[t.length-1][0]:t[1][0]+t[t.length-1][0]:2===t.length?t[0][0]+t[1][0]:t[0][0]).toUpperCase()}r.d(t,{v:function(){return n}})}},function(e){e.O(0,[14438,31304,83464,2602,85323,27648,85210,16116,92971,95030,1744],function(){return e(e.s=51963)}),_N_E=e.O()}]);