import { IEscrow } from "@/types/escrow";
import { IRecurringTransfer } from "@/types/recurring-transfer";
import { IFundraisingPool } from "@/types/fundraising-pool";
import { TransactionData } from "@/types/transaction-data";

// Admin dashboard overview interface
export interface AdminDashboardOverview {
  system: SystemStats;
  period: PeriodStats;
  activity: ActivityMetrics;
  financial: FinancialMetrics;
  health: SystemHealth;
  performance: DashboardPerformanceMetrics;
  alerts: SystemAlert[];
  recentActivities: RecentActivities;
}

// System statistics interface
export interface SystemStats {
  users: number;
  escrows: number;
  recurringTransfers: number;
  fundraisingPools: number;
  transactions: number;
}

// Period statistics interface
export interface PeriodStats {
  newUsers: number;
  newEscrows: number;
  newRecurringTransfers: number;
  newFundraisingPools: number;
  newTransactions: number;
}

// Activity metrics interface
export interface ActivityMetrics {
  activeEscrows: number;
  completedEscrows: number;
  activeRecurringTransfers: number;
  activeFundraisingPools: number;
  successfulTransactions: number;
  failedTransactions: number;
  transactionSuccessRate: number;
}

// Financial metrics interface
export interface FinancialMetrics {
  escrowVolume: number;
  recurringTransferVolume: number;
  fundraisingVolume: number;
  totalFees: number;
  totalVolume: number;
}

// System health interface
export interface SystemHealth {
  pendingNotifications: number;
  failedNotifications: number;
  expiredEscrows: number;
  failedRecurringTransfers: number;
  expiredFundraisingPools: number;
  totalIssues: number;
  healthScore: number;
}

// Performance metrics interface for dashboard
export interface DashboardPerformanceMetrics {
  responseTime: number;
  uptime: number;
  errorRate: number;
  throughput: number;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkLatency: number;
}

// Recent activities interface
export interface RecentActivities {
  recentEscrows: IEscrow[];
  recentHighValueTransactions: TransactionData[];
  recentFundraisingPools: IFundraisingPool[];
}

// Admin escrow statistics interface
export interface AdminEscrowStatistics {
  overview: {
    total: number;
    active: number;
    completed: number;
    expired: number;
    disputed: number;
  };
  period: {
    escrows: number;
    volume: number;
    highValue: number;
    averageAmount: number;
  };
  breakdowns: {
    status: Array<{ status: string; count: number }>;
    currency: Array<{ currency_code: string; count: number; volume: number }>;
  };
  charts: {
    escrowsByDay: Array<{ created_at: string; count: number }>;
  };
}

// Admin recurring transfer statistics interface
export interface AdminRecurringTransferStatistics {
  overview: {
    total: number;
    active: number;
    paused: number;
    failed: number;
    totalExecutions: number;
  };
  period: {
    transfers: number;
    recentFailures: number;
    averageAmount: number;
  };
  breakdowns: {
    status: Array<{ status: string; count: number }>;
    frequency: Array<{ frequency: string; count: number }>;
    currency: Array<{ currency_code: string; count: number; volume: number }>;
  };
  charts: {
    transfersByDay: Array<{ created_at: string; count: number }>;
  };
}

// Admin fundraising pool statistics interface
export interface AdminFundraisingPoolStatistics {
  overview: {
    total: number;
    active: number;
    completed: number;
    expired: number;
    totalContributions: number;
  };
  period: {
    pools: number;
    totalRaised: number;
    highValue: number;
    averageTarget: number;
    successRate: number;
  };
  breakdowns: {
    status: Array<{ status: string; count: number }>;
    category: Array<{ category: string; count: number }>;
    currency: Array<{ currency_code: string; count: number; volume: number }>;
  };
  charts: {
    poolsByDay: Array<{ created_at: string; count: number }>;
  };
}

// Items requiring attention interfaces
export interface EscrowsRequiringAttention {
  disputed: IEscrow[];
  expired: IEscrow[];
  highValue: IEscrow[];
  longRunning: IEscrow[];
}

export interface RecurringTransfersRequiringAttention {
  failed: IRecurringTransfer[];
  highFailure: IRecurringTransfer[];
  highValue: IRecurringTransfer[];
  longRunning: IRecurringTransfer[];
}

export interface PoolsRequiringAttention {
  expired: IFundraisingPool[];
  highValue: IFundraisingPool[];
  suspicious: IFundraisingPool[];
  longRunning: IFundraisingPool[];
}

// Admin alerts interface
export interface AdminAlert {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'urgent';
  count: number;
  message: string;
}

export interface AdminAlerts {
  alerts: AdminAlert[];
  totalAlerts: number;
  lastUpdated: string;
}

// System alert interface for AdminAlertsCard
export interface SystemAlert {
  id: string;
  type: 'security' | 'financial' | 'system' | 'user' | 'performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  timestamp: string;
  isResolved: boolean;
  actionRequired: boolean;
  affectedUsers?: number;
  estimatedImpact?: string;
}

// Performance metrics interface
export interface PerformanceMetrics {
  averageEscrowCreationTime: number;
  averageTransactionProcessingTime: number;
  averageNotificationDeliveryTime: number;
  systemUptime: number;
  apiResponseTime: number;
  databaseResponseTime: number;
  errorRate: number;
  transactionsLast24h: number;
  failedTransactionsLast24h: number;
}

// Admin action data interfaces
export interface ForceReleaseData {
  reason: string;
}

export interface ForceCancelData {
  reason: string;
}

export interface ForcePauseData {
  reason: string;
}

export interface ForceActivateData {
  reason: string;
}

export interface ForceDistributeData {
  reason: string;
}

// Audit trail interfaces
export interface EscrowAuditTrail {
  escrow: IEscrow;
  transactions: TransactionData[];
  milestones: any[];
  statusChanges: StatusChange[];
}

export interface PoolAuditTrail {
  pool: IFundraisingPool;
  contributions: any[];
  statusChanges: StatusChange[];
}

export interface StatusChange {
  id: number;
  entityType: string;
  entityId: number;
  fromStatus: string;
  toStatus: string;
  changedBy: number;
  reason?: string;
  timestamp: Date;
}

// Admin filters interfaces
export interface AdminEscrowFilters {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface AdminRecurringTransferFilters {
  page?: number;
  limit?: number;
  status?: string;
  frequency?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface AdminPoolFilters {
  page?: number;
  limit?: number;
  status?: string;
  category?: string;
  visibility?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  targetMin?: number;
  targetMax?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Admin dashboard period type
export type AdminPeriod = '24h' | '7d' | '30d' | '90d' | '1y';

// Admin role permissions interface
export interface AdminPermissions {
  canViewDashboard: boolean;
  canManageEscrows: boolean;
  canManageRecurringTransfers: boolean;
  canManageFundraisingPools: boolean;
  canForceActions: boolean;
  canViewAuditTrails: boolean;
  canExportData: boolean;
  canManageUsers: boolean;
  canViewSystemHealth: boolean;
  canManageNotifications: boolean;
}

// Admin user interface
export interface AdminUser {
  id: number;
  name: string;
  email: string;
  role: string;
  permissions: AdminPermissions;
  lastLogin?: Date;
  isActive: boolean;
}

// Export data options interface
export interface ExportDataOptions {
  format: 'csv' | 'excel' | 'pdf';
  dateFrom?: string;
  dateTo?: string;
  filters?: Record<string, any>;
  includeDetails?: boolean;
}

// Bulk action interfaces
export interface BulkActionResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

export interface BulkEscrowAction {
  action: 'force_release' | 'force_cancel' | 'send_reminder';
  escrowIds: number[];
  reason?: string;
}

export interface BulkRecurringTransferAction {
  action: 'force_pause' | 'force_cancel' | 'retry_failed';
  transferIds: number[];
  reason?: string;
}

export interface BulkPoolAction {
  action: 'force_activate' | 'force_pause' | 'force_distribute';
  poolIds: number[];
  reason?: string;
}

// System configuration interface
export interface SystemConfiguration {
  escrowSettings: {
    defaultDeadlineHours: number;
    maxDeadlineHours: number;
    autoReleaseEnabled: boolean;
    reminderIntervals: number[];
  };
  recurringTransferSettings: {
    maxRetries: number;
    retryDelayMinutes: number;
    maxActiveTransfers: number;
  };
  fundraisingSettings: {
    maxPoolDuration: number;
    defaultPlatformFee: number;
    maxPlatformFee: number;
  };
  notificationSettings: {
    emailEnabled: boolean;
    pushEnabled: boolean;
    smsEnabled: boolean;
    batchSize: number;
  };
}

// Real-time dashboard data interface
export interface RealTimeDashboardData {
  activeUsers: number;
  pendingTransactions: number;
  systemLoad: number;
  queueSize: number;
  lastUpdated: Date;
}

// Dashboard widget configuration interface
export interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'alert';
  title: string;
  size: 'small' | 'medium' | 'large';
  position: { x: number; y: number };
  config: Record<string, any>;
  isVisible: boolean;
}

export interface DashboardLayout {
  widgets: DashboardWidget[];
  lastModified: Date;
}
