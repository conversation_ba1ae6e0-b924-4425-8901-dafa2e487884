(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[77315],{77315:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return p}});var n=r(57437),a=r(25833),o=r(85539),i=r(35974),l=r(6512),s=r(75730),c=r(94508),u=r(2901),d=r(99376),f=r(2265),m=r(43949);function p(){var e;let{t}=(0,m.$G)(),r=(0,d.useSearchParams)(),[p,v]=f.useState(null!==(e=r.get("search"))&&void 0!==e?e:""),[g,h]=f.useState([]),x=(0,d.useRouter)(),y=(0,d.usePathname)(),{data:b,meta:w,isLoading:j}=(0,s.Z)("/investments/history?".concat(r.toString()),{keepPreviousData:!0});return(0,n.jsx)("div",{className:"p-4",children:(0,n.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,n.jsx)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:(0,n.jsx)("div",{className:"flex w-full flex-wrap items-center gap-4 md:flex-nowrap md:justify-end",children:(0,n.jsx)(o.R,{value:p,onChange:e=>{e.preventDefault();let t=(0,c.w4)(e.target.value);v(e.target.value),x.replace("".concat(y,"?").concat(t.toString()))},iconPlacement:"end",placeholder:t("Search..."),containerClass:"w-full sm:w-auto"})})}),(0,n.jsx)(l.Z,{className:"my-4"}),(0,n.jsx)(a.Z,{data:b,sorting:g,isLoading:j,setSorting:h,pagination:{total:null==w?void 0:w.total,page:null==w?void 0:w.currentPage,limit:null==w?void 0:w.perPage},structure:[{id:"createdAt",header:t("Date"),cell:e=>{let{row:t}=e,r=t.original;return r.createdAt?(0,n.jsx)("span",{className:"block w-[100px] text-sm font-normal leading-5 text-foreground",children:(0,u.WU)(r.createdAt,"dd MMM yyyy; \n hh:mm a")}):(0,n.jsx)("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"name",header:t("Name"),cell:e=>{var t;let{row:r}=e;return(0,n.jsx)("p",{children:null===(t=r.original)||void 0===t?void 0:t.name})}},{id:"status",header:t("Status"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return"completed"===r.status?(0,n.jsx)(i.C,{variant:"success",children:(0,c.fl)(r.status)}):"withdrawn"===r.status?(0,n.jsx)(i.C,{variant:"default",children:(0,c.fl)(r.status)}):"on_hold"===r.status?(0,n.jsx)(i.C,{variant:"warning",children:(0,c.fl)(r.status)}):(0,n.jsx)(i.C,{variant:"secondary",children:(0,c.fl)(r.status)})}},{id:"amountInvested",header:t("Amount Invested"),cell:e=>{var t,r,a;let{row:o}=e;return(0,n.jsxs)("p",{className:"whitespace-nowrap",children:[null===(t=o.original)||void 0===t?void 0:t.amountInvested," ",null===(a=o.original)||void 0===a?void 0:null===(r=a.currency)||void 0===r?void 0:r.toUpperCase()]})}},{id:"interestRate",header:t("Interest Rate"),cell:e=>{var t;let{row:r}=e;return(0,n.jsxs)("p",{children:[null===(t=r.original)||void 0===t?void 0:t.interestRate,"%"]})}},{id:"profit",header:t("Profit"),cell:e=>{var t,r,a;let{row:o}=e;return(0,n.jsxs)("p",{className:"whitespace-nowrap",children:[null===(t=o.original)||void 0===t?void 0:t.profit," ",null===(a=o.original)||void 0===a?void 0:null===(r=a.currency)||void 0===r?void 0:r.toUpperCase()]})}},{id:"duration",header:t("Duration"),cell:e=>{var r,a;let{row:o}=e;return(0,n.jsxs)("p",{className:"font-normal",children:[null===(r=o.original)||void 0===r?void 0:r.duration," ",(null===(a=o.original)||void 0===a?void 0:a.duration)>1?t("Days"):t("Day")]})}},{id:"durationType",header:t("Duration Type"),cell:e=>{var t;let{row:r}=e;return(0,n.jsx)("p",{className:"font-normal",children:(0,c.fl)(null===(t=r.original)||void 0===t?void 0:t.durationType)})}},{id:"withdrawAfterMatured",header:t("Withdraw Type"),cell:e=>{var r;let{row:a}=e;return(null===(r=a.original)||void 0===r?void 0:r.withdrawAfterMatured)?(0,n.jsx)(i.C,{variant:"important",children:t("Yes")}):(0,n.jsx)(i.C,{variant:"secondary",children:t("No")})}}]})]})})}},25833:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var n=r(57437),a=r(94508),o=r(71594),i=r(24525),l=r(73490),s=r(36887),c=r(64394),u=r(61756),d=r(99376),f=r(4751),m=r(2265),p=r(43949),v=r(62869),g=r(73578);function h(e){let{data:t,isLoading:r=!1,structure:h,sorting:x,setSorting:y,padding:b=!1,className:w,onRefresh:j,pagination:N}=e,S=(0,m.useMemo)(()=>h,[h]),P=(0,d.useRouter)(),R=(0,d.usePathname)(),C=(0,d.useSearchParams)(),{t:Z}=(0,p.$G)(),k=(0,o.b7)({data:t||[],columns:S,state:{sorting:x,onRefresh:j},onSortingChange:y,getCoreRowModel:(0,i.sC)(),getSortedRowModel:(0,i.tj)(),debugTable:!1});return r?(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:Z("Loading...")})}):(null==t?void 0:t.length)?(0,n.jsxs)("div",{className:(0,a.ZP)("".concat(b?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),w),children:[(0,n.jsxs)(g.iA,{children:[(0,n.jsx)(g.xD,{children:k.getHeaderGroups().map(e=>(0,n.jsx)(g.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,r,i,l;return(0,n.jsx)(g.ss,{className:(0,a.ZP)("",null==e?void 0:null===(i=e.column)||void 0===i?void 0:null===(r=i.columnDef)||void 0===r?void 0:null===(t=r.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,n.jsxs)(v.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[Z((0,o.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(l=({asc:(0,n.jsx)(s.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,n.jsx)(s.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==l?l:(0,n.jsx)(s.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,n.jsx)(g.RM,{children:k.getRowModel().rows.map(e=>(0,n.jsx)(g.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,n.jsx)(g.pj,{className:"py-3 text-sm font-semibold",children:(0,o.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),N&&N.total>10&&(0,n.jsx)("div",{className:"pb-2 pt-6",children:(0,n.jsx)(f.Z,{showTotal:(e,t)=>Z("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==N?void 0:N.page,total:null==N?void 0:N.total,pageSize:null==N?void 0:N.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(C);t.set("page",e.toString()),P.push("".concat(R,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(c.Z,{size:"18"})}),nextIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(u.Z,{size:"18"})})})})]}):(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,n.jsx)(l.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),Z("No data found!")]})})}},85539:function(e,t,r){"use strict";r.d(t,{R:function(){return l}});var n=r(57437);r(2265);var a=r(95186),o=r(94508),i=r(48674);function l(e){let{iconPlacement:t="start",className:r,containerClass:l,...s}=e;return(0,n.jsxs)("div",{className:(0,o.ZP)("relative flex items-center",l),children:[(0,n.jsx)(i.Z,{size:"20",className:(0,o.ZP)("absolute top-1/2 -translate-y-1/2","end"===t?"right-2.5":"left-2.5")}),(0,n.jsx)(a.I,{type:"text",className:(0,o.ZP)("h-10","end"===t?"pr-10":"pl-10",r),...s})]})}},35974:function(e,t,r){"use strict";r.d(t,{C:function(){return l}});var n=r(57437),a=r(90535);r(2265);var o=r(94508);let i=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{className:(0,o.ZP)(i({variant:r}),t),...a})}},62869:function(e,t,r){"use strict";r.d(t,{d:function(){return s},z:function(){return c}});var n=r(57437),a=r(37053),o=r(90535),i=r(2265),l=r(94508);let s=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:o,size:i,asChild:c=!1,...u}=e,d=c?a.g7:"button";return(0,n.jsx)(d,{className:(0,l.ZP)(s({variant:o,size:i,className:r})),ref:t,...u})});c.displayName="Button"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var n=r(57437),a=r(2265),o=r(94508);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,n.jsx)("input",{type:a,className:(0,o.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...i})});i.displayName="Input"},6512:function(e,t,r){"use strict";var n=r(57437),a=r(55156),o=r(2265),i=r(94508);let l=o.forwardRef((e,t)=>{let{className:r,orientation:o="horizontal",decorative:l=!0,...s}=e;return(0,n.jsx)(a.f,{ref:t,decorative:l,orientation:o,className:(0,i.ZP)("shrink-0 bg-divider","horizontal"===o?"h-[1px] w-full":"h-full w-[1px]",r),...s})});l.displayName=a.f.displayName,t.Z=l},73578:function(e,t,r){"use strict";r.d(t,{RM:function(){return s},SC:function(){return c},iA:function(){return i},pj:function(){return d},ss:function(){return u},xD:function(){return l}});var n=r(57437),a=r(2265),o=r(94508);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,o.ZP)("w-full caption-bottom text-sm",r),...a})})});i.displayName="Table";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("thead",{ref:t,className:(0,o.ZP)("",r),...a})});l.displayName="TableHeader";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,o.ZP)("[&_tr:last-child]:border-0",r),...a})});s.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,o.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tr",{ref:t,className:(0,o.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});c.displayName="TableRow";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("th",{ref:t,className:(0,o.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableHead";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("td",{ref:t,className:(0,o.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});d.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("caption",{ref:t,className:(0,o.ZP)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},31117:function(e,t,r){"use strict";r.d(t,{d:function(){return o}});var n=r(79981),a=r(85323);let o=(e,t)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},75730:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(31117),a=r(99376);function o(e,t){var r,o,i;let l=(0,a.usePathname)(),s=(0,a.useSearchParams)(),c=(0,a.useRouter)(),[u,d]=e.split("?"),f=new URLSearchParams(d);f.has("page")||f.set("page","1"),f.has("limit")||f.set("limit","10");let m="".concat(u,"?").concat(f.toString()),{data:p,error:v,isLoading:g,mutate:h,...x}=(0,n.d)(m,t);return{refresh:()=>h(p),data:null!==(i=null==p?void 0:null===(r=p.data)||void 0===r?void 0:r.data)&&void 0!==i?i:[],meta:null==p?void 0:null===(o=p.data)||void 0===o?void 0:o.meta,filter:(e,t,r)=>{let n=new URLSearchParams(s.toString());t?n.set(e,t.toString()):n.delete(e),c.replace("".concat(l,"?").concat(n.toString())),null==r||r()},isLoading:g,error:v,...x}}},79981:function(e,t,r){"use strict";var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return u},Fg:function(){return m},Fp:function(){return c},Qp:function(){return f},ZP:function(){return l},fl:function(){return s},qR:function(){return d},w4:function(){return p}});var n=r(78040),a=r(61994),o=r(14438),i=r(53335);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,a.W)(t))}function s(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>o.toast.success("Copied to clipboard!")).catch(()=>{o.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let o=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:o,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:o,l=a.format(e),s=l.substring(i.length).trim();return{currencyCode:o,currencySymbol:i,formattedAmount:l,amountText:s}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}},48674:function(e,t,r){"use strict";r.d(t,{Z:function(){return v}});var n=r(74677),a=r(2265),o=r(40718),i=r.n(o),l=["variant","color","size"],s=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},c=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{opacity:".4",d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",fill:t}),a.createElement("path",{d:"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},d=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},m=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{opacity:".4",d:"m22 22-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return a.createElement(s,{color:t});case"Broken":return a.createElement(c,{color:t});case"Bulk":return a.createElement(u,{color:t});case"Linear":default:return a.createElement(d,{color:t});case"Outline":return a.createElement(f,{color:t});case"TwoTone":return a.createElement(m,{color:t})}},v=(0,a.forwardRef)(function(e,t){var r=e.variant,o=e.color,i=e.size,s=(0,n._)(e,l);return a.createElement("svg",(0,n.a)({},s,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(r,o))});v.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},v.defaultProps={variant:"Linear",color:"currentColor",size:"24"},v.displayName="SearchNormal1"},74677:function(e,t,r){"use strict";function n(){return(n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function a(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}r.d(t,{_:function(){return a},a:function(){return n}})},48049:function(e,t,r){"use strict";var n=r(14397);function a(){}function o(){}o.resetWarningCache=a,e.exports=function(){function e(e,t,r,a,o,i){if(i!==n){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:a};return r.PropTypes=r,r}},40718:function(e,t,r){e.exports=r(48049)()},14397:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},98575:function(e,t,r){"use strict";r.d(t,{F:function(){return o},e:function(){return i}});var n=r(2265);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},66840:function(e,t,r){"use strict";r.d(t,{WV:function(){return l},jH:function(){return s}});var n=r(2265),a=r(54887),o=r(37053),i=r(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.Z8)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...o}=e,l=a?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function s(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},55156:function(e,t,r){"use strict";r.d(t,{f:function(){return c}});var n=r(2265),a=r(66840),o=r(57437),i="horizontal",l=["horizontal","vertical"],s=n.forwardRef((e,t)=>{let{decorative:r,orientation:n=i,...s}=e,c=l.includes(n)?n:i;return(0,o.jsx)(a.WV.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...s,ref:t})});s.displayName="Separator";var c=s},37053:function(e,t,r){"use strict";r.d(t,{Z8:function(){return i},g7:function(){return l},sA:function(){return c}});var n=r(2265),a=r(98575),o=r(57437);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,s=function(e,t){let r={...t};for(let n in t){let a=e[n],o=t[n];/^on[A-Z]/.test(n)?a&&o?r[n]=(...e)=>{o(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...o}:"className"===n&&(r[n]=[a,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,a.F)(t,l):l),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...i}=e,l=n.Children.toArray(a),s=l.find(u);if(s){let e=s.props.children,a=l.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,o.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),s=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=s,t}function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},90535:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var n=r(61994);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.W,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,s=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let o=a(t)||a(n);return i[e][o]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}}]);