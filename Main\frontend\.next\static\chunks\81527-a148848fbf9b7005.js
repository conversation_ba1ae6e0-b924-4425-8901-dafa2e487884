"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[81527],{25833:function(e,t,r){r.d(t,{Z:function(){return x}});var n=r(57437),a=r(94508),o=r(71594),s=r(24525),i=r(73490),l=r(36887),d=r(64394),c=r(61756),u=r(99376),f=r(4751),m=r(2265),v=r(43949),g=r(62869),p=r(73578);function x(e){let{data:t,isLoading:r=!1,structure:x,sorting:h,setSorting:b,padding:y=!1,className:w,onRefresh:N,pagination:j}=e,S=(0,m.useMemo)(()=>x,[x]),C=(0,u.useRouter)(),R=(0,u.usePathname)(),Z=(0,u.useSearchParams)(),{t:P}=(0,v.$G)(),T=(0,o.b7)({data:t||[],columns:S,state:{sorting:h,onRefresh:N},onSortingChange:b,getCoreRowModel:(0,s.sC)(),getSortedRowModel:(0,s.tj)(),debugTable:!1});return r?(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:P("Loading...")})}):(null==t?void 0:t.length)?(0,n.jsxs)("div",{className:(0,a.ZP)("".concat(y?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),w),children:[(0,n.jsxs)(p.iA,{children:[(0,n.jsx)(p.xD,{children:T.getHeaderGroups().map(e=>(0,n.jsx)(p.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,r,s,i;return(0,n.jsx)(p.ss,{className:(0,a.ZP)("",null==e?void 0:null===(s=e.column)||void 0===s?void 0:null===(r=s.columnDef)||void 0===r?void 0:null===(t=r.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,n.jsxs)(g.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[P((0,o.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(i=({asc:(0,n.jsx)(l.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,n.jsx)(l.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==i?i:(0,n.jsx)(l.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,n.jsx)(p.RM,{children:T.getRowModel().rows.map(e=>(0,n.jsx)(p.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,n.jsx)(p.pj,{className:"py-3 text-sm font-semibold",children:(0,o.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),j&&j.total>10&&(0,n.jsx)("div",{className:"pb-2 pt-6",children:(0,n.jsx)(f.Z,{showTotal:(e,t)=>P("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==j?void 0:j.page,total:null==j?void 0:j.total,pageSize:null==j?void 0:j.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(Z);t.set("page",e.toString()),C.push("".concat(R,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(d.Z,{size:"18"})}),nextIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(c.Z,{size:"18"})})})})]}):(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,n.jsx)(i.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),P("No data found!")]})})}},41169:function(e,t,r){r.d(t,{Z:function(){return g}});var n=r(57437),a=r(25833),o=r(35974),s=r(2901),i=r(2265),l=r(43949),d=r(62869),c=r(97054),u=r(12554),f=r(99376),m=r(14438);function v(e){let{row:t}=e,r=(0,f.useRouter)(),{t:a}=(0,l.$G)();return(0,n.jsx)(d.z,{type:"button",disabled:!(null==t?void 0:t.active),variant:"destructive",size:"icon",className:"h-8 w-8",onClick:()=>{m.toast.promise((0,c.e)(t.id),{loading:a("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return r.refresh(),e.message},error:e=>e.message})},children:(0,n.jsx)(u.Z,{size:17})})}function g(e){let{data:t,meta:r,isLoading:d}=e,[c,u]=i.useState([]),{t:f}=(0,l.$G)();return(0,n.jsx)(a.Z,{data:t,sorting:c,isLoading:d,setSorting:u,pagination:{total:null==r?void 0:r.total,page:null==r?void 0:r.page,limit:null==r?void 0:r.limit},structure:[{id:"logged in",header:f("Logged in"),cell:e=>{let{createdAt:t}=e.row.original;return t?(0,n.jsx)("p",{className:"block min-w-24 text-sm font-normal text-secondary-text",children:(0,s.WU)(t,"dd MMM yyyy; hh:mm b")}):(0,n.jsx)("span",{className:"font-normal text-secondary-text",children:" N/A "})}},{id:"ipAddress",header:f("IP Address"),cell:e=>{var t,r;return(null===(t=e.row.original)||void 0===t?void 0:t.ipAddress)?(0,n.jsx)("div",{className:"font-normal text-secondary-text",children:null===(r=e.row.original)||void 0===r?void 0:r.ipAddress}):(0,n.jsx)("span",{className:"font-normal text-secondary-text",children:" N/A "})}},{id:"country",header:f("Country"),cell:e=>{var t,r;return(null===(t=e.row.original)||void 0===t?void 0:t.country)?(0,n.jsx)("div",{className:"font-normal text-secondary-text",children:null===(r=e.row.original)||void 0===r?void 0:r.country}):(0,n.jsx)("span",{className:"font-normal text-secondary-text",children:" N/A "})}},{id:"deviceName",header:f("Device"),cell:e=>{var t,r;return(null===(t=e.row.original)||void 0===t?void 0:t.deviceName)?(0,n.jsx)("div",{className:"font-normal text-secondary-text",children:null===(r=e.row.original)||void 0===r?void 0:r.deviceName}):(0,n.jsx)("span",{className:"font-normal text-secondary-text",children:" N/A "})}},{id:"active",header:f("Status"),cell:e=>{var t;return(null===(t=e.row.original)||void 0===t?void 0:t.active)?(0,n.jsx)(o.C,{variant:"success",children:f("Active")}):(0,n.jsx)(o.C,{variant:"secondary",className:"bg-muted",children:f("Inactive")})}},{id:"menu",header:f("Menu"),cell:e=>(0,n.jsx)(v,{row:e.row.original})}]})}},35974:function(e,t,r){r.d(t,{C:function(){return i}});var n=r(57437),a=r(90535);r(2265);var o=r(94508);let s=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{className:(0,o.ZP)(s({variant:r}),t),...a})}},62869:function(e,t,r){r.d(t,{d:function(){return l},z:function(){return d}});var n=r(57437),a=r(37053),o=r(90535),s=r(2265),i=r(94508);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:o,size:s,asChild:d=!1,...c}=e,u=d?a.g7:"button";return(0,n.jsx)(u,{className:(0,i.ZP)(l({variant:o,size:s,className:r})),ref:t,...c})});d.displayName="Button"},66070:function(e,t,r){r.d(t,{Ol:function(){return i},SZ:function(){return d},Zb:function(){return s},aY:function(){return c},eW:function(){return u},ll:function(){return l}});var n=r(57437),a=r(2265),o=r(94508);let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});s.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,o.ZP)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,o.ZP)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("p-6 pt-0",r),...a})});c.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},6512:function(e,t,r){var n=r(57437),a=r(55156),o=r(2265),s=r(94508);let i=o.forwardRef((e,t)=>{let{className:r,orientation:o="horizontal",decorative:i=!0,...l}=e;return(0,n.jsx)(a.f,{ref:t,decorative:i,orientation:o,className:(0,s.ZP)("shrink-0 bg-divider","horizontal"===o?"h-[1px] w-full":"h-full w-[1px]",r),...l})});i.displayName=a.f.displayName,t.Z=i},73578:function(e,t,r){r.d(t,{RM:function(){return l},SC:function(){return d},iA:function(){return s},pj:function(){return u},ss:function(){return c},xD:function(){return i}});var n=r(57437),a=r(2265),o=r(94508);let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,o.ZP)("w-full caption-bottom text-sm",r),...a})})});s.displayName="Table";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("thead",{ref:t,className:(0,o.ZP)("",r),...a})});i.displayName="TableHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,o.ZP)("[&_tr:last-child]:border-0",r),...a})});l.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,o.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tr",{ref:t,className:(0,o.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});d.displayName="TableRow";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("th",{ref:t,className:(0,o.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});c.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("td",{ref:t,className:(0,o.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("caption",{ref:t,className:(0,o.ZP)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},97751:function(e,t,r){r.d(t,{B:function(){return a},D:function(){return o}});var n=r(43577);function a(e){var t,r,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function o(e){let t=500,r="Internal Server Error",a="An unknown error occurred";if((0,n.IZ)(e)){var o,s,i,l,d,c,u,f,m,v,g,p;t=null!==(m=null===(o=e.response)||void 0===o?void 0:o.status)&&void 0!==m?m:500,r=null!==(v=null===(s=e.response)||void 0===s?void 0:s.statusText)&&void 0!==v?v:"Internal Server Error",a=null!==(p=null!==(g=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(l=d.messages)||void 0===l?void 0:null===(i=l[0])||void 0===i?void 0:i.message)&&void 0!==g?g:null===(f=e.response)||void 0===f?void 0:null===(u=f.data)||void 0===u?void 0:u.message)&&void 0!==p?p:e.message}else e instanceof Error&&(a=e.message);return{statusCode:t,statusText:r,status:!1,message:a,data:void 0,error:e}}},97054:function(e,t,r){r.d(t,{e:function(){return o},x:function(){return s}});var n=r(79981),a=r(97751);async function o(e){try{let t=await n.Z.put("/login-sessions/remove/".concat(e),{});return(0,a.B)(t)}catch(e){return(0,a.D)(e)}}async function s(){try{let e=await n.Z.put("/login-sessions/remove-all",{});return(0,a.B)(e)}catch(e){return(0,a.D)(e)}}},31117:function(e,t,r){r.d(t,{d:function(){return o}});var n=r(79981),a=r(85323);let o=(e,t)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},75730:function(e,t,r){r.d(t,{Z:function(){return o}});var n=r(31117),a=r(99376);function o(e,t){var r,o,s;let i=(0,a.usePathname)(),l=(0,a.useSearchParams)(),d=(0,a.useRouter)(),[c,u]=e.split("?"),f=new URLSearchParams(u);f.has("page")||f.set("page","1"),f.has("limit")||f.set("limit","10");let m="".concat(c,"?").concat(f.toString()),{data:v,error:g,isLoading:p,mutate:x,...h}=(0,n.d)(m,t);return{refresh:()=>x(v),data:null!==(s=null==v?void 0:null===(r=v.data)||void 0===r?void 0:r.data)&&void 0!==s?s:[],meta:null==v?void 0:null===(o=v.data)||void 0===o?void 0:o.meta,filter:(e,t,r)=>{let n=new URLSearchParams(l.toString());t?n.set(e,t.toString()):n.delete(e),d.replace("".concat(i,"?").concat(n.toString())),null==r||r()},isLoading:p,error:g,...h}}},79981:function(e,t,r){var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){r.d(t,{F:function(){return c},Fg:function(){return m},Fp:function(){return d},Qp:function(){return f},ZP:function(){return i},fl:function(){return l},qR:function(){return u},w4:function(){return v}});var n=r(78040),a=r(61994),o=r(14438),s=r(53335);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>o.toast.success("Copied to clipboard!")).catch(()=>{o.toast.error("Failed to copy!")})};class c{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let o=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:o,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let s=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:o,i=a.format(e),l=i.substring(s.length).trim();return{currencyCode:o,currencySymbol:s,formattedAmount:i,amountText:l}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",v=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}}}]);