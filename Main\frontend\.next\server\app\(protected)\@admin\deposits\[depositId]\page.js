(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8689],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},29417:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>P,default:()=>A});var n,s={};r.r(s),r.d(s,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>g,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>f,pages:()=>h,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>x,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),r(67206);var a=r(79319),i=r(20518),o=r(61902),l=r(62042),c=r(44630),d=r(44828),m=r(65505),u=r(13839);let p=["",{children:["(protected)",{admin:["children",{children:["deposits",{children:["[depositId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76566)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\deposits\\[depositId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,43469)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\deposits\\[depositId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,71220)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\deposits\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\deposits\\[depositId]\\page.tsx"],f="/(protected)/@admin/deposits/[depositId]/page",g={require:r,loadChunk:()=>Promise.resolve()},x=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/deposits/[depositId]/page",pathname:"/deposits/[depositId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var k=r(69094),v=r(5787),E=r(90527);let y=e=>e?JSON.parse(e):void 0,j=self.__BUILD_MANIFEST,b=y(self.__REACT_LOADABLE_MANIFEST),S=null==(n=self.__RSC_MANIFEST)?void 0:n["/(protected)/@admin/deposits/[depositId]/page"],N=y(self.__RSC_SERVER_MANIFEST),M=y(self.__NEXT_FONT_MANIFEST),w=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];S&&N&&(0,v.Mo)({clientReferenceManifest:S,serverActionsManifest:N,serverModuleMap:(0,E.w)({serverActionsManifest:N,pageName:"/(protected)/@admin/deposits/[depositId]/page"})});let L=(0,i.d)({pagesType:k.s.APP,dev:!1,page:"/(protected)/@admin/deposits/[depositId]/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:j,renderToHTML:l.f,reactLoadableManifest:b,clientReferenceManifest:S,serverActionsManifest:N,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:M,incrementalCacheHandler:null,interceptionRouteRewrites:w}),P=s;function A(e){return(0,a.C)({...e,IncrementalCache:o.k,handler:L})}},60310:(e,t,r)=>{Promise.resolve().then(r.bind(r,27630))},26807:(e,t,r)=>{Promise.resolve().then(r.bind(r,44450)),Promise.resolve().then(r.bind(r,40098))},27630:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S,runtime:()=>b});var n=r(60926),s=r(58387),a=r(29411),i=r(62797),o=r(36162),l=r(74988),c=r(1181),d=r(25694);async function m(e,t){try{let r=await c.Z.put(`/admin/deposits/${t}/${e}`,{id:e});return(0,d.B)(r)}catch(e){return(0,d.D)(e)}}var u=r(43291),p=r(65091),h=r(3632),f=r(37988),g=r(55071),x=r(31949),k=r(90543),v=r(51018),E=r(64947),y=r(39228),j=r(32167);let b="edge";function S(){let e=(0,E.UO)(),{data:t,isLoading:r,mutate:c}=(0,u.d)(`/admin/deposits/${e.depositId}`),{t:d}=(0,y.$G)();if(r)return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(a.Loader,{})});let b=t?.data?new h.C(t?.data):null,S=new p.F;if(!b)return(0,n.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,n.jsx)(f.Z,{}),d("No data found")]});let N=b?.metaData?Object.entries(b.metaData).filter(([e])=>"trxSecret"!==e).map(([e,t])=>({key:e,value:t})):[],M=e=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());return(0,n.jsx)("div",{className:"p-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,n.jsxs)("div",{className:"col-span-12 md:col-span-7",children:[(0,n.jsxs)("div",{className:"mb-4 flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,n.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,n.jsx)(g.Z,{variant:"Bulk",size:32,className:"text-primary"}),(0,n.jsxs)("h2",{className:"font-semibold",children:[" ",d("Deposit")," #",e.depositId]})]}),(0,n.jsx)(i.z,{senderAvatar:(0,p.qR)(b.from.image),senderName:b.from.label,senderInfo:[b.from?.email,b?.from?.phone],receiverAvatar:(0,p.qR)(b?.to?.image),receiverName:b?.to?.label,receiverInfo:[b?.to?.email,b?.to?.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,n.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsxs)("div",{className:"grid grid-cols-12 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,n.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:d("Amount")}),(0,n.jsx)("div",{className:"col-span-6 pl-2.5 text-sm font-medium sm:text-base",children:S.formatVC(b.amount,b.metaData.currency)})]}),(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,n.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:d("Service charge")}),(0,n.jsx)("div",{className:"col-span-6 pl-2.5 text-sm font-medium sm:text-base",children:S.formatVC(b.fee,b.metaData.currency)})]}),(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,n.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:d("User gets")}),(0,n.jsx)("div",{className:"col-span-6 pl-2.5 text-sm font-semibold sm:text-base",children:S.formatVC(b.total,b.metaData.currency)})]})]}),(0,n.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,n.jsx)("div",{className:"flex flex-col",children:(0,n.jsxs)("div",{className:"grid grid-cols-12 px-3 py-3 odd:bg-accent sm:px-6",children:[(0,n.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:d("Transaction ID")}),(0,n.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[b.trxId,(0,n.jsx)(o.z,{type:"button",onClick:()=>(0,p.Fp)(b.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,n.jsx)(x.Z,{size:"20"})})]})]})}),(0,n.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-4 rounded-xl bg-card px-4 py-6",children:[(0,n.jsx)("h4",{children:d("Deposit request")}),(0,n.jsx)(s.J,{condition:b?.status==="completed",children:(0,n.jsx)("p",{children:d("Deposit approved")})}),(0,n.jsx)(s.J,{condition:b?.status==="failed",children:(0,n.jsx)("p",{children:d("Deposit failed")})}),(0,n.jsx)(s.J,{condition:b?.status==="pending",children:(0,n.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,n.jsxs)(o.z,{type:"button",className:"bg-[#0B6A0B] text-white hover:bg-[#149014]",onClick:()=>{j.toast.promise(m(t?.data?.id,"accept"),{loading:d("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return c(t),e.message},error:e=>e.message})},children:[(0,n.jsx)(k.Z,{}),d("Accept deposit")]}),(0,n.jsxs)(o.z,{type:"button",className:"bg-[#D13438] text-white hover:bg-[#b42328]",onClick:()=>{j.toast.promise(m(t?.data?.id,"decline"),{loading:d("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return c(t),e.message},error:e=>e.message})},children:[(0,n.jsx)(v.Z,{}),d("Reject deposit")]})]})})]})]}),(0,n.jsxs)("div",{className:"col-span-12 md:col-span-5",children:[(0,n.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)("h2",{children:d("Method info")})}),(0,n.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,n.jsx)("div",{className:"col-span-6 text-base font-normal",children:d("Method used")}),(0,n.jsx)("div",{className:"col-span-6 text-base font-medium",children:b?.method})]}),(0,n.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,n.jsx)("div",{className:"col-span-6 text-base font-normal",children:d("Wallet")}),(0,n.jsx)("div",{className:"col-span-6 text-base font-medium",children:b?.currency})]})]})]}),(0,n.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)("h2",{children:d("Additional info")})}),(0,n.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,n.jsx)("div",{className:"flex flex-col",children:N.map((e,t)=>(0,n.jsxs)("div",{className:`grid grid-cols-12 px-6 py-3 ${t%2==0?"bg-accent":""}`,children:[(0,n.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:M(e.key)}),(0,n.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:e.value||"N/A"})]},e.key))})]})]})]})})}},62797:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var n=r(60926),s=r(15185),a=r(65091),i=r(9172),o=r(90543);function l({senderName:e,senderAvatar:t,senderInfo:r,receiverName:s,receiverAvatar:i,receiverInfo:o,className:l}){return(0,n.jsxs)("div",{className:(0,a.ZP)("mb-4 flex items-start justify-around gap-1",l),children:[(0,n.jsx)(c,{name:e,avatar:t,info:r}),s&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),(0,n.jsx)(c,{name:s,avatar:i,info:o})]})]})}function c({avatar:e,name:t,info:r=[]}){let a=r.filter(Boolean);return(0,n.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,n.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,n.jsxs)(s.qE,{className:"size-10 rounded-full sm:size-14",children:[(0,n.jsx)(s.F$,{src:e,alt:t,width:56,height:56}),(0,n.jsx)(s.Q5,{className:"font-semibold",children:(0,i.v)(t)})]}),(0,n.jsx)("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:(0,n.jsx)(o.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:t}),a.length>0&&a.map((e,t)=>(0,n.jsx)("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},t))]})]})}},40098:(e,t,r)=>{"use strict";r.d(t,{default:()=>Z});var n=r(60926),s=r(58387),a=r(36162),i=r(84607),o=r(86059),l=r(737),c=r(64947),d=r(29220);function m({sidebarItem:e}){let[t,r]=d.useState("(dashboard)"),[m,u]=d.useState(!1),{setIsExpanded:p,device:h}=(0,i.q)(),f=(0,c.BT)();return d.useEffect(()=>{r(f)},[]),d.useEffect(()=>{u(e.segment===f)},[f,e.segment]),(0,n.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,n.jsxs)(l.Z,{href:e.link,onClick:()=>{r(e.segment),e.children?.length||"Desktop"===h||p(!1)},"data-active":f===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[(0,n.jsx)(s.J,{condition:!!e.icon,children:(0,n.jsx)("div",{"data-active":f===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),(0,n.jsx)("span",{className:"flex-1",children:e.name}),(0,n.jsx)(s.J,{condition:!!e.children?.length,children:(0,n.jsx)(a.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:(0,n.jsx)(o.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),(0,n.jsx)(s.J,{condition:!!e.children?.length,children:(0,n.jsx)("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>n.jsx("li",{children:n.jsxs(l.Z,{href:e.link,"data-active":t===e.segment,onClick:()=>{r(e.segment),"Desktop"!==h&&p(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[n.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=r(74988),p=r(840),h=r(65091),f=r(51496),g=r(32917),x=r(65694),k=r(34870),v=r(48132),E=r(55929),y=r(41529),j=r(95334),b=r(5147),S=r(76409),N=r(24112),M=r(69628),w=r(73634),L=r(47020),P=r(28277),A=r(39228);function Z(){let{t:e}=(0,A.$G)(),{isExpanded:t,setIsExpanded:r}=(0,i.q)(),{logo:s,siteName:o}=(0,p.T)(),c=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:(0,n.jsx)(f.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:(0,n.jsx)(g.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:(0,n.jsx)(x.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:(0,n.jsx)(k.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:(0,n.jsx)(v.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:(0,n.jsx)(E.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:(0,n.jsx)(y.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:(0,n.jsx)(j.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:(0,n.jsx)(b.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:(0,n.jsx)(S.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:(0,n.jsx)(N.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:(0,n.jsx)(M.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:(0,n.jsx)(w.Z,{size:"20"}),link:"/settings"}]}];return(0,n.jsxs)("div",{"data-expanded":t,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[(0,n.jsx)(a.z,{size:"icon",variant:"outline",onClick:()=>r(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${t?"":"hidden"} lg:hidden`,children:(0,n.jsx)(L.Z,{})}),(0,n.jsx)("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:(0,n.jsx)(l.Z,{href:"/",className:"flex items-center justify-center",children:(0,n.jsx)(P.Z,{src:(0,h.qR)(s),width:160,height:40,alt:o,className:"max-h-10 object-contain"})})}),(0,n.jsx)("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:c.map(e=>(0,n.jsxs)("div",{children:[""!==e.title?(0,n.jsx)("div",{children:(0,n.jsx)(u.Z,{className:"my-4"})}):null,(0,n.jsx)("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>n.jsx("li",{children:n.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},51018:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm3.36 12.3c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-2.3-2.3-2.3 2.3c-.15.15-.34.22-.53.22s-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l2.3-2.3-2.3-2.3a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 2.3-2.3c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-2.3 2.3 2.3 2.3Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"m13.99 10.012.84-.84M9.17 14.828l2.75-2.75M14.83 14.832l-5.66-5.66M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),s.createElement("path",{d:"m13.06 12 2.3-2.3c.29-.29.29-.77 0-1.06a.754.754 0 0 0-1.06 0l-2.3 2.3-2.3-2.3a.754.754 0 0 0-1.06 0c-.29.29-.29.77 0 1.06l2.3 2.3-2.3 2.3c-.29.29-.29.77 0 1.06.15.15.34.22.53.22s.38-.07.53-.22l2.3-2.3 2.3 2.3c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77 0-1.06l-2.3-2.3Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10ZM9.17 14.83l5.66-5.66M14.83 14.83 9.17 9.17",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),s.createElement("path",{d:"M9.17 15.58c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l5.66-5.66c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.7 15.36c-.14.15-.34.22-.53.22Z",fill:t}),s.createElement("path",{d:"M14.83 15.58c-.19 0-.38-.07-.53-.22L8.64 9.7a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l5.66 5.66c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("g",{opacity:".4",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},s.createElement("path",{d:"m9.17 14.832 5.66-5.66M14.83 14.832l-5.66-5.66"})))},h=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,a))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="CloseCircle"},31949:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M15.5 13.15h-2.17c-1.78 0-3.23-1.44-3.23-3.23V7.75c0-.41-.33-.75-.75-.75H6.18C3.87 7 2 8.5 2 11.18v6.64C2 20.5 3.87 22 6.18 22h5.89c2.31 0 4.18-1.5 4.18-4.18V13.9c0-.42-.34-.75-.75-.75Z",fill:t}),s.createElement("path",{d:"M17.82 2H11.93C9.67 2 7.84 3.44 7.76 6.01c.06 0 .11-.01.17-.01h5.89C16.13 6 18 7.5 18 10.18V16.83c0 .06-.01.11-.01.16 2.23-.07 4.01-1.55 4.01-4.16V6.18C22 3.5 20.13 2 17.82 2Z",fill:t}),s.createElement("path",{d:"M11.98 7.152c-.31-.31-.84-.1-.84.33v2.62c0 1.1.93 2 2.07 2 .71.01 1.7.01 2.55.01.43 0 .65-.5.35-.8-1.09-1.09-3.03-3.04-4.13-4.16Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M2 12.6C2 8.6 3.6 7 7.6 7h3M17 13.398v3c0 4-1.6 5.6-5.6 5.6H7.6c-4 0-5.6-1.6-5.6-5.6",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M13.8 13.4c-2.4 0-3.2-.8-3.2-3.2V7l6.4 6.4M11.6 2h4M7 5c0-1.66 1.34-3 3-3h2.62M22 8v6.19c0 1.55-1.26 2.81-2.81 2.81M22 8h-3c-2.25 0-3-.75-3-3V2l6 6Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M15.5 13.15h-2.17c-1.78 0-3.23-1.44-3.23-3.23V7.75c0-.41-.33-.75-.75-.75H6.18C3.87 7 2 8.5 2 11.18v6.64C2 20.5 3.87 22 6.18 22h5.89c2.31 0 4.18-1.5 4.18-4.18V13.9c0-.42-.34-.75-.75-.75Z",fill:t}),s.createElement("path",{d:"M17.82 2H11.93C9.67 2 7.84 3.44 7.76 6.01c.06 0 .11-.01.17-.01h5.89C16.13 6 18 7.5 18 10.18V16.83c0 .06-.01.11-.01.16 2.23-.07 4.01-1.55 4.01-4.16V6.18C22 3.5 20.13 2 17.82 2Z",fill:t}),s.createElement("path",{d:"M11.98 7.152c-.31-.31-.84-.1-.84.33v2.62c0 1.1.93 2 2.07 2 .71.01 1.7.01 2.55.01.43 0 .65-.5.35-.8-1.09-1.09-3.03-3.04-4.13-4.16Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M17 13.4v3c0 4-1.6 5.6-5.6 5.6H7.6c-4 0-5.6-1.6-5.6-5.6v-3.8C2 8.6 3.6 7 7.6 7h3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M17 13.4h-3.2c-2.4 0-3.2-.8-3.2-3.2V7l6.4 6.4ZM11.6 2h4M7 5c0-1.66 1.34-3 3-3h2.62M22 8v6.19c0 1.55-1.26 2.81-2.81 2.81M22 8h-3c-2.25 0-3-.75-3-3V2l6 6Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.4 22.75H7.6c-4.39 0-6.35-1.96-6.35-6.35v-3.8c0-4.39 1.96-6.35 6.35-6.35h3c.41 0 .75.34.75.75s-.34.75-.75.75h-3c-3.58 0-4.85 1.27-4.85 4.85v3.8c0 3.58 1.27 4.85 4.85 4.85h3.8c3.58 0 4.85-1.27 4.85-4.85v-3c0-.41.34-.75.75-.75s.75.34.75.75v3c0 4.39-1.96 6.35-6.35 6.35Z",fill:t}),s.createElement("path",{d:"M17 14.149h-3.2c-2.81 0-3.95-1.14-3.95-3.95v-3.2c0-.3.18-.58.46-.69.28-.12.6-.05.82.16l6.4 6.4c.21.21.28.54.16.82a.74.74 0 0 1-.69.46Zm-5.65-5.34v1.39c0 1.99.46 2.45 2.45 2.45h1.39l-3.84-3.84ZM15.6 2.75h-4c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h4c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}),s.createElement("path",{d:"M7 5.75c-.41 0-.75-.34-.75-.75 0-2.07 1.68-3.75 3.75-3.75h2.62c.41 0 .75.34.75.75s-.34.75-.75.75H10C8.76 2.75 7.75 3.76 7.75 5c0 .41-.34.75-.75.75ZM19.19 17.75c-.41 0-.75-.34-.75-.75s.34-.75.75-.75c1.14 0 2.06-.93 2.06-2.06V8c0-.41.34-.75.75-.75s.75.34.75.75v6.19c0 1.96-1.6 3.56-3.56 3.56Z",fill:t}),s.createElement("path",{d:"M22 8.749h-3c-2.66 0-3.75-1.09-3.75-3.75v-3c0-.3.18-.58.46-.69.28-.12.6-.05.82.16l6 6c.21.21.28.54.16.82a.74.74 0 0 1-.69.46Zm-5.25-4.94v1.19c0 1.83.42 2.25 2.25 2.25h1.19l-3.44-3.44Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M16 12.4v3c0 4-1.6 5.6-5.6 5.6H6.6c-4 0-5.6-1.6-5.6-5.6v-3.8C1 7.6 2.6 6 6.6 6h3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M16 12.4h-3.2c-2.4 0-3.2-.8-3.2-3.2V6l6.4 6.4Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"M6 4c0-1.66 1.34-3 3-3h6M21 7v6.19c0 1.55-1.26 2.81-2.81 2.81",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M21 7h-3c-2.25 0-3-.75-3-3V1l6 6Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,a))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="DocumentCopy"},55071:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm3.92 10.75h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8a.749.749 0 1 1 0 1.5Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M14.99 12H16M8 12h4M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),s.createElement("path",{d:"M15.92 12.75h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8a.749.749 0 1 1 0 1.5Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.92 22c5.5 0 10-4.5 10-10s-4.5-10-10-10-10 4.5-10 10 4.5 10 10 10ZM7.92 12h8",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.92 22.75C6 22.75 1.17 17.93 1.17 12S6 1.25 11.92 1.25 22.67 6.07 22.67 12s-4.82 10.75-10.75 10.75Zm0-20c-5.1 0-9.25 4.15-9.25 9.25s4.15 9.25 9.25 9.25 9.25-4.15 9.25-9.25-4.15-9.25-9.25-9.25Z",fill:t}),s.createElement("path",{d:"M15.92 12.75h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8a.749.749 0 1 1 0 1.5Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.92 22c5.5 0 10-4.5 10-10s-4.5-10-10-10-10 4.5-10 10 4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".34",d:"M7.92 12h8",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,a))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="MinusCirlce"},37988:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"m19.53 5.53-14 14c-.02.02-.03.03-.05.04-.38-.32-.73-.67-1.05-1.05A9.903 9.903 0 0 1 2 12C2 6.48 6.48 2 12 2c2.49 0 4.77.91 6.52 2.43.38.32.73.67 1.05 1.05-.01.02-.02.03-.04.05ZM22 12c0 5.49-4.51 10-10 10-1.5 0-2.92-.33-4.2-.93-.62-.29-.74-1.12-.26-1.61L19.46 7.54c.48-.48 1.32-.36 1.61.26.6 1.27.93 2.7.93 4.2Z",fill:t}),s.createElement("path",{d:"M21.77 2.229c-.3-.3-.79-.3-1.09 0L2.23 20.689c-.3.3-.3.79 0 1.09a.758.758 0 0 0 1.08-.01l18.46-18.46c.31-.3.31-.78 0-1.08Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"m18.9 5-14 14",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"m19.53 5.53-14 14c-.02.02-.03.03-.05.04-.38-.32-.73-.67-1.05-1.05A9.903 9.903 0 0 1 2 12C2 6.48 6.48 2 12 2c2.49 0 4.77.91 6.52 2.43.38.32.73.67 1.05 1.05-.01.02-.02.03-.04.05ZM22 12.002c0 5.52-4.48 10-10 10-1.99 0-3.84-.58-5.4-1.6l13.8-13.8a9.815 9.815 0 0 1 1.6 5.4Z",fill:t}),s.createElement("path",{d:"M21.77 2.229c-.3-.3-.79-.3-1.09 0L2.23 20.689c-.3.3-.3.79 0 1.09a.758.758 0 0 0 1.08-.01l18.46-18.46c.31-.3.31-.78 0-1.08Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.52 0 10-4.48 10-10S17.52 2 12 2 2 6.48 2 12s4.48 10 10 10ZM18.9 5l-14 14",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),s.createElement("path",{d:"M4.9 19.751c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l14-14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-14 14c-.15.15-.34.22-.53.22Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.52 0 10-4.48 10-10S17.52 2 12 2 2 6.48 2 12s4.48 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".34",d:"m18.9 5-14 14",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,a))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="Slash"},90543:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm4.78 7.7-5.67 5.67a.75.75 0 0 1-1.06 0l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.76 0 1.06Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85M15 10.38l1.12-1.13",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"m7.88 12 2.74 2.75 2.55-2.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),s.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"m7.75 12 2.83 2.83 5.67-5.66",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),s.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".34",d:"m7.75 12.002 2.83 2.83 5.67-5.66",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,a))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="TickCircle"},3632:(e,t,r)=>{"use strict";r.d(t,{C:()=>c});var n=r(73244),s=r(73146),a=r(65091);class i{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,a.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new s.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new n.k(e?.address):null}}var o=r(14455),l=r(74190);class c{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new i(e?.user),customer:e?.user?.customer?new l.O(e?.user?.customer):null,merchant:e?.user?.merchant?new l.O(e?.user?.merchant):null,agent:e?.user?.agent?new l.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,o.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,o.WU)(this.updatedAt,e):"N/A"}}},43469:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}},76566:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,runtime:()=>s});var n=r(18264);let s=(0,n.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\deposits\[depositId]\page.tsx#runtime`),a=(0,n.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\deposits\[depositId]\page.tsx#default`)},71220:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}},73391:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(42416),s=r(33908);let a=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function i({children:e}){return(0,n.jsxs)("div",{className:"flex h-screen",children:[(0,n.jsx)(a,{}),(0,n.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[(0,n.jsx)(s.Z,{}),(0,n.jsx)("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(87908)},50517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,7283,5089],()=>t(29417));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/deposits/[depositId]/page"]=r}]);
//# sourceMappingURL=page.js.map