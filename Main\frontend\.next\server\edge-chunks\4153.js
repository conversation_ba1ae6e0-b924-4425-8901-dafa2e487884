"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4153],{3770:(e,t,s)=>{s.d(t,{Z:()=>h});var a=s(60926),n=s(44335),r=s(15185),l=s(28871),i=s(65091),d=s(9172),x=s(14455),c=s(29220),o=s(39228),m=s(56999);let p=new i.F;function h({data:e,meta:t,isLoading:s}){let[h,u]=c.useState([]),{t:f}=(0,o.$G)();return(0,a.jsx)(n.Z,{data:e,sorting:h,setSorting:u,isLoading:s,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"trxId",header:f("Trx ID"),cell:e=>(0,a.jsx)("span",{className:"text-sm font-normal text-secondary-text",children:e.row.original?.trxId})},{id:"createdAt",header:f("Date"),cell:e=>{let t=e.row.original?.createdAt;return t?(0,a.jsx)("span",{className:"block w-24 text-sm font-normal leading-5 text-secondary-text",children:(0,x.WU)(t,"dd MMM yyyy hh:mm b")}):(0,a.jsx)("span",{className:"text-secondary-text",children:" N/A "})}},{id:"type",header:f("Type"),cell:e=>(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-xs font-bold text-secondary-text",children:(0,i.fl)(e.row.original?.type)})})},{id:"status",header:f("Status"),cell:e=>{let t=e.row.original?.status;return"completed"===t?(0,a.jsx)(l.C,{variant:"success",children:f("Complete")}):"failed"===t?(0,a.jsx)(l.C,{variant:"destructive",children:f("Failed")}):(0,a.jsx)(l.C,{variant:"secondary",children:f("Pending")})}},{id:"amount",header:f("Amount"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),n=t?.metaData&&JSON.parse(t?.metaData);return(0,a.jsx)("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,m.EQ)(t).with({type:"exchange"},()=>p.format(n?.amountFrom,n?.currencyFrom)).with({type:"deposit"},()=>p.format(t.amount,n?.currency)).otherwise(()=>p.format(t.amount,s?.currency))})}},{id:"fee",header:f("Fee"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),n=t?.metaData&&JSON.parse(t?.metaData);return(0,a.jsx)("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,m.EQ)(t).with({type:"exchange"},()=>p.format(t?.fee,n?.currency)).with({type:"deposit"},()=>p.format(t.fee,n?.currency)).otherwise(()=>p.format(t.fee,s?.currency))})}},{id:"after_processing",header:f("After Processing"),cell:({row:e})=>{let t=e?.original,s=t?.to&&JSON.parse(t.to),n=t?.metaData&&JSON.parse(t?.metaData);return(0,a.jsx)("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,m.EQ)(t).with({type:"exchange"},()=>p.format(t.total,n?.currencyTo)).with({type:"deposit"},()=>p.format(t.total,n?.currency)).otherwise(()=>p.format(t.total,s?.currency))})}},{id:"method",header:f("Method"),cell:({row:e})=>{let t=e.original;return t?.method?(0,a.jsx)("span",{className:"line-clamp-2 w-[100px] text-sm font-normal text-foreground",children:t.method}):(0,a.jsx)("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"agent",header:f("Agent"),cell:({row:e})=>{let t=e.original,s=t?.to&&JSON.parse(t.to);return s&&"agent"===t.method?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(r.qE,{className:"size-7 border-2 border-primary p-1",children:[(0,a.jsx)(r.F$,{src:s.image,alt:s.label}),(0,a.jsxs)(r.Q5,{children:[" ",(0,d.v)(s.label)," "]})]}),(0,a.jsx)("span",{children:s.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsx)("span",{className:"block size-5 rounded-full bg-primary"}),(0,a.jsx)("span",{children:"N/A"})]})}},{id:"merchant",header:f("Merchant"),cell:({row:e})=>{let t=e.original,s=t?.to&&JSON.parse(t.to);return s&&"agent"!==t.method?(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(r.qE,{className:"size-7 border-2 border-primary p-1",children:[(0,a.jsx)(r.F$,{src:s.image,alt:s.label}),(0,a.jsx)(r.Q5,{children:(0,d.v)(s.label)})]}),(0,a.jsx)("span",{children:s.label})]}):(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsx)("span",{className:"block size-5 rounded-full bg-primary"}),(0,a.jsx)("span",{children:"N/A"})]})}}]})}},44942:(e,t,s)=>{s.d(t,{x:()=>l});var a=s(60926),n=s(87198),r=s(65091);function l({title:e,value:t,status:s,icon:l,iconClass:i,statusClass:d,className:x,isLoading:c}){return c?(0,a.jsx)(n.O,{className:(0,r.ZP)("",x)}):(0,a.jsxs)("div",{className:(0,r.ZP)("inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default",x),children:[(0,a.jsx)("div",{className:(0,r.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full bg-muted",i),children:l({size:34,variant:"Outline"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)("h1",{children:t}),(0,a.jsxs)("span",{className:"block text-xs font-normal leading-4",children:[e," "]}),(0,a.jsx)("h6",{className:(0,r.ZP)("text-sm font-semibold leading-5",d),children:s})]})]})}s(29220)},58904:(e,t,s)=>{s.d(t,{Z:()=>y});var a=s(60926),n=s(29220),r=s(98019),l=s(23183),i=s(65091),d=s(86059),x=s(32917),c=s(65694),o=s(34870),m=s(48132),p=s(55929),h=s(50201),u=s(95334),f=s(32793),g=s(36926),j=s(64947),N=s(39228);function y({filter:e}){let t=(0,j.lr)(),{t:s}=(0,N.$G)(),[y,b]=n.useState(!1);return(0,a.jsxs)(l.J2,{open:y,onOpenChange:b,children:[(0,a.jsxs)(l.xo,{className:"flex h-10 w-full items-center gap-2 rounded-sm bg-background px-3 text-foreground shadow-defaultLite sm:w-72",children:[(0,a.jsx)("span",{className:"line-clamp-1 flex-1 text-left font-medium leading-[22px] text-foreground",children:t.get("type")?(0,i.fl)(t.get("type")):s("All Transactions")}),(0,a.jsx)(d.Z,{size:"24",strokeWidth:1.5,className:"text-secondary-text"})]}),(0,a.jsx)(l.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:(0,a.jsx)(r.mY,{className:"p-1",children:(0,a.jsx)(r.e8,{className:"max-h-[450px]",children:(0,a.jsxs)(r.fu,{className:"p-0",children:[(0,a.jsx)("div",{className:"px-2 py-1.5",children:(0,a.jsx)("p",{className:"text-[10px] font-normal leading-4 text-secondary-text",children:s("Select what you want to see")})}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(g.Z,{size:24}),s("All Transactions")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","deposit",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(x.Z,{size:24}),s("Deposits")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","transfer",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(c.Z,{size:24}),s("Transfer")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","withdraw",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(o.Z,{size:"24"}),s("Withdraws")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","exchange",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(m.Z,{size:"24"}),s("Exchange")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","payment",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(p.Z,{size:"24"}),s("Payment")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","service",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(h.Z,{size:"24"}),s("Services")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","investment",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(u.Z,{size:"24"}),s("Investments")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","investment_return",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(u.Z,{size:"24"}),s("Investment return")]}),(0,a.jsx)(r.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(r.di,{onSelect:()=>e("type","referral_bonus",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(f.Z,{size:"24"}),s("Referral bonus")]})]})})})})]})}},87198:(e,t,s)=>{s.d(t,{O:()=>r});var a=s(60926),n=s(65091);function r({className:e,...t}){return(0,a.jsx)("div",{className:(0,n.ZP)("animate-pulse rounded-md bg-muted",e),...t})}}}]);
//# sourceMappingURL=4153.js.map