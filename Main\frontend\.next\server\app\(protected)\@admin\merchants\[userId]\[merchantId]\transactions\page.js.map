{"version": 3, "file": "app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page.js", "mappings": "qFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,2CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,kGEAAC,gxBDKA,IAAAC,EACA,CACA,GACA,CACAC,SAAA,CACA,cACA,CACAC,MAAA,CACA,WACA,CACAD,SAAA,CACA,YACA,CACAA,SAAA,CACA,WACA,CACAA,SAAA,CACA,eACA,CACAA,SAAA,CACA,eACA,CACAA,SAAA,eAAiC,CACjCE,KAAA,KAAuBC,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAwL,wJAEtM,EAET,CACA,aAA0BH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAA2L,2JAGrN,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA4K,2IACrM,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA6K,6IAG/L,EAEA,CAGA,EACA,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoJ,mHAC7K,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqJ,qHAGvK,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAyI,wGAClK,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0I,0GAG5J,CACAN,SAAA,CACA,cACA,GACA,CACAQ,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAG,MAAA,CACA,cACA,GACA,CACAD,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAI,SAAA,CACA,cACA,GACA,CACAF,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAK,SAAA,CACA,cACA,GACA,CACAH,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAiI,gGAC1J,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkI,kGAGpJ,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoH,mFAC7I,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqH,oFACvI,iBAAoBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAAuH,uFAG3I,CAEAM,EAAA,wJAKOC,EAAA,wEACAC,EAAA,CACPjB,QAJ6BS,EAK7BS,UAJA,IAAAZ,QAAAC,OAAA,EAKA,EAGOY,EAAA,IAAwBC,EAAAC,kBAAkB,EACjDC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,QAAA,CACvBrB,KAAA,wEACAsB,SAAA,gDAEAC,WAAA,GACAC,SAAA,GACAC,SAAA,IAEAC,SAAA,CACAC,WAAA9B,CACA,CACA,uCCjHA,IAAA+B,EAAA,GAAAC,EAAAC,KAAAC,KAAA,CAAAF,GAAAG,KAAAA,EACAC,EAAAC,KAAAC,gBAAA,CACAC,EAAAR,EAAAM,KAAAG,yBAAA,EACAC,EAAA,MAAA1C,CAAAA,EAAAsC,KAAAK,cAAA,SAAA3C,CAAA,0EACA4C,EAAAZ,EAAAM,KAAAO,qBAAA,EAEAC,EAAAd,EAAAM,KAAAS,oBAAA,EACAC,EAAAhB,EAAAM,KAAAW,qCAAA,MACAP,GAAAE,GACI,GAAAM,EAAAC,EAAA,EAA8B,CAClCC,wBAAAV,EACAW,sBAAAT,EACAU,gBAAyB,GAAAC,EAAAC,CAAA,EAAqB,CAC9CH,sBAAAT,EACAa,SAAA,uEACA,EACA,GAEA,IAAMC,EAAS,GAAAC,EAAAC,CAAA,EAAS,CACxBC,UAAeC,EAAAC,CAAU,CAAAC,GAAA,CACzBC,IAvBA,GAwBA7D,KAAA,wEACA8D,OA9BA,KA+BAC,QAAWC,EACXC,SA/BA,KAgCAC,YA/BA,KAgCAC,SAnCA,KAoCAlC,cAAAA,EACAmC,aAAgBC,EAAAC,CAAA,CAChBlC,sBAAAA,EACAY,wBAAAV,EACAW,sBAAAT,EACA+B,cAlCAvC,KAAAA,EAmCAwC,6BA5BAxC,KAAAA,EA6BAyC,OAnCA,CAAoB,OAAQ,qBAA0B,uBAA2B,YAAe,mDAAyD,qPAAwR,wTAAkV,iDAAuD,sCAAyC,CAAE,gCAAmC,iBAAsB,eAAkB,uDAA4D,iBAAoB,wCAA6C,KAAQ,kBAAmB,2BAA+B,iIAAuJ,uBAAyB,mEAA6E,cAAiB,oFAAiG,uBAAuB,0CAA2C,EAAE,QAAW,6BAA8B,GAAG,cAAiB,w6BAA4lC,uBAA0B,o9BAAs9B,+HAoCnqHC,QAAAC,QAAAC,GAAA,CAAAC,eAAA,CACAnC,iBAAAA,EACAoC,wBA/CA,KAgDAlC,0BAAAA,CACA,GACOmC,EAAqBf,EACb,SAAAgB,EAAAC,CAAA,EACf,MAAW,GAAAC,EAAAC,CAAA,EAAO,CAClB,GAAAF,CAAA,CACAG,iBAAwBC,EAAAC,CAAA,CACxBC,QAAiBjC,CACjB,EACA,mBCnEArD,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,yBCAAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,qQCyBO,IAAMoF,EAAU,OAER,SAASC,EAAsB,CAC5C3F,SAAAA,CAAQ,CAGT,EACC,IAAM4F,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTC,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,IACfC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTzE,EAAW0E,CAAAA,EAAAA,EAAAA,EAAAA,IAEX,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAERC,EAAO,CACX,CACEC,MAAOH,EAAE,mBACTI,KAAM,GAAAC,EAAAC,GAAA,EAACC,EAAAA,CAAQA,CAAAA,CAACC,KAAK,KAAKC,QAAQ,SAClCC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,WAAW,CAAC,EAAEjB,EAAakB,QAAQ,GAAG,CAAC,CACrFC,GAAI,aACN,EACA,CACEX,MAAOH,EAAE,gBACTI,KAAM,GAAAC,EAAAC,GAAA,EAACS,EAAAA,CAAKA,CAAAA,CAACP,KAAK,KAAKC,QAAQ,SAC/BC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,WAAW,cAAc,EAAEjB,EAAakB,QAAQ,GAAG,CAAC,CAClGC,GAAI,cACN,EACA,CACEX,MAAOH,EAAE,OACTI,KAAM,GAAAC,EAAAC,GAAA,EAACU,EAAAA,CAAcA,CAAAA,CAACR,KAAK,KAAKC,QAAQ,SACxCC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,WAAW,KAAK,EAAEjB,EAAakB,QAAQ,GAAG,CAAC,CACzFC,GAAI,KACN,EACA,CACEX,MAAOH,EAAE,QACTI,KAAM,GAAAC,EAAAC,GAAA,EAACU,EAAAA,CAAcA,CAAAA,CAACR,KAAK,KAAKC,QAAQ,SACxCC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,WAAW,MAAM,EAAEjB,EAAakB,QAAQ,GAAG,CAAC,CAC1FC,GAAI,MACN,EACA,CACEX,MAAOH,EAAE,eACTI,KAAM,GAAAC,EAAAC,GAAA,EAACW,EAAAA,CAAOA,CAAAA,CAACT,KAAK,KAAKC,QAAQ,SACjCC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,WAAW,aAAa,EAAEjB,EAAakB,QAAQ,GAAG,CAAC,CACjGC,GAAI,aACN,EACA,CACEX,MAAOH,EAAE,cACTI,KAAM,GAAAC,EAAAC,GAAA,EAACY,EAAAA,CAAGA,CAAAA,CAACV,KAAK,KAAKC,QAAQ,SAC7BC,KAAM,CAAC,WAAW,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,WAAW,YAAY,EAAEjB,EAAakB,QAAQ,GAAG,CAAC,CAChGC,GAAI,YACN,EACD,CAED,MACE,GAAAT,EAAAc,IAAA,EAAAd,EAAAe,QAAA,YACE,GAAAf,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,+FACb,GAAAjB,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,mEACb,GAAAjB,EAAAc,IAAA,EAACI,KAAAA,CAAGD,UAAU,iJACZ,GAAAjB,EAAAC,GAAA,EAACkB,KAAAA,UACC,GAAAnB,EAAAc,IAAA,EAACM,EAAAA,CAAIA,CAAAA,CACHf,KAAK,kBACLY,UAAU,0FAEV,GAAAjB,EAAAC,GAAA,EAACoB,EAAAA,CAAUA,CAAAA,CAAAA,GACV1B,EAAE,aAGP,GAAAK,EAAAc,IAAA,EAACK,KAAAA,CAAGF,UAAU,2CAAiC,KAC1C3B,EAAagC,GAAG,CAAC,WAEtB,GAAAtB,EAAAc,IAAA,EAACK,KAAAA,CAAGF,UAAU,2CAAiC,KAC1CtB,EAAE,YAAY,KAAGP,EAAOmB,UAAU,OAGzC,GAAAP,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,wEACb,GAAAjB,EAAAC,GAAA,EAACsB,OAAAA,UAAM5B,EAAE,YACT,GAAAK,EAAAC,GAAA,EAACuB,EAAAA,CAAMA,CAAAA,CACLC,eAAgBnC,MAAAA,EAAagC,GAAG,CAAC,UACjCL,UAAU,kCACVS,gBAAiB,IACfC,EAAAA,KAAKA,CAACC,OAAO,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,EAAezC,EAAOkB,MAAM,EAAa,CACrDwB,QAASnC,EAAE,cACXoC,QAAS,IACP,GAAI,CAACC,EAAIC,MAAM,CAAE,MAAM,MAAUD,EAAIE,OAAO,EAC5C,IAAMC,EAAK,IAAIC,gBAAgB9C,GAI/B,MAHA+C,CAAAA,EAAAA,EAAAA,CAAAA,EAAO,CAAC,iBAAiB,EAAEjD,EAAOmB,UAAU,CAAC,CAAC,EAC9C4B,EAAGG,GAAG,CAAC,SAAUC,EAAU,IAAM,KACjC/C,EAAOgD,IAAI,CAAC,CAAC,EAAExH,EAAS,CAAC,EAAEmH,EAAG3B,QAAQ,GAAG,CAAC,EACnCwB,EAAIE,OAAO,EAEpBO,MAAO,GAASC,EAAIR,OAAO,EAE/B,UAKN,GAAAlC,EAAAC,GAAA,EAAC0C,EAAAA,CAAYA,CAAAA,CAAC9C,KAAMA,OAGrBrG,IAGP,uQC1Ge,SAASoJ,IACtB,IAAMtD,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,IACfC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTL,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTrE,EAAW0E,CAAAA,EAAAA,EAAAA,EAAAA,IAEX,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAER,CAACiD,EAAQC,EAAU,CAAGC,EAAAA,QAAc,CAACzD,EAAagC,GAAG,CAAC,WAAa,IACnE,CAAE0B,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,OAAAA,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,EACxC,CAAC,oBAAoB,EAAEhE,EAAOkB,MAAM,CAAC,CAAC,EAAEhB,EAAakB,QAAQ,GAAG,CAAC,EAG7D,CAAEwC,KAAMK,CAAQ,CAAEH,UAAWI,CAAe,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,EACrD,CAAC,2BAA2B,EAAEnE,EAAOkB,MAAM,CAAC,CAAC,EAW/C,MACE,GAAAN,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,uBACb,GAAAjB,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,yCACb,GAAAjB,EAAAC,GAAA,EAACuD,EAAAA,CAAUA,CAAAA,CAEPC,MAAOJ,GAAUL,MAAMU,QACvB5D,MAAOH,EAAE,iBACTI,KAAM,GAAW,GAAAC,EAAAC,GAAA,EAAC0D,EAAAA,CAAGA,CAAAA,CAAE,GAAGC,CAAK,GAC/B3B,OAAQ,GACRhB,UAAW,wDACX4C,YAAa,qBACbC,UAAW,8BACXZ,UAAWI,IAIf,GAAAtD,EAAAC,GAAA,EAACuD,EAAAA,CAAUA,CAAAA,CAEPC,MAAOJ,GAAUL,MAAMe,SACvBjE,MAAOH,EAAE,kBACTI,KAAM,GAAW,GAAAC,EAAAC,GAAA,EAAC+D,EAAAA,CAAOA,CAAAA,CAAE,GAAGJ,CAAK,GACnC3B,OAAQ,GACRhB,UAAW,wDACX6C,UAAW,6CACXD,YAAa,mBACbX,UAAWI,IAIf,GAAAtD,EAAAC,GAAA,EAACuD,EAAAA,CAAUA,CAAAA,CAEPC,MAAOJ,GAAUL,MAAMiB,SACvBnE,MAAOH,EAAE,mBACTI,KAAM,GAAW,GAAAC,EAAAC,GAAA,EAACiE,EAAAA,CAAUA,CAAAA,CAAE,GAAGN,CAAK,GACtC3B,OAAQ,GACRhB,UAAW,wDACX6C,UAAW,+CACXD,YAAa,oBACbX,UAAWI,IAIf,GAAAtD,EAAAC,GAAA,EAACuD,EAAAA,CAAUA,CAAAA,CAEPC,MAAOJ,GAAUL,MAAMmB,SACvBrE,MAAOH,EAAE,kBACTI,KAAM,GAAW,GAAAC,EAAAC,GAAA,EAACmE,EAAAA,CAAMA,CAAAA,CAAE,GAAGR,CAAK,GAClC3B,OAAQ,GACRhB,UAAW,wDACXiC,UAAWI,OAKjB,GAAAtD,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,mFAEb,GAAAjB,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,8FACb,GAAAjB,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,wGACb,GAAAjB,EAAAC,GAAA,EAACoE,EAAAA,CAAyBA,CAAAA,CAAClB,OAAQA,MAErC,GAAAnD,EAAAc,IAAA,EAACE,MAAAA,CAAIC,UAAU,8CAEb,GAAAjB,EAAAC,GAAA,EAACqE,EAAAA,CAASA,CAAAA,CACRb,MAAOZ,EACP0B,SAvES,IACnBC,EAAEC,cAAc,GAChB,IAAMC,EAAIC,CAAAA,EAAAA,EAAAA,EAAAA,EAAYH,EAAEI,MAAM,CAACnB,KAAK,EACpCX,EAAU0B,EAAEI,MAAM,CAACnB,KAAK,EACxBjE,EAAOqF,OAAO,CAAC,CAAC,EAAE7J,EAAS,CAAC,EAAE0J,EAAElE,QAAQ,GAAG,CAAC,CAC9C,EAmEYsE,cAAc,MACdC,YAAapF,EAAE,eAGjB,GAAAK,EAAAC,GAAA,EAAC+E,EAAAA,CAAWA,CAAAA,CACVC,iBAAgB,GAChBC,kBAAiB,GACjBC,mBAAkB,KAGpB,GAAAnF,EAAAC,GAAA,EAACmF,EAAAA,CAAiBA,CAAAA,CAChBC,IAAK,CAAC,2BAA2B,EAAEjG,EAAOkB,MAAM,CAAC,CAAC,SAKxD,GAAAN,EAAAC,GAAA,EAACqF,EAAAA,CAASA,CAAAA,CAACrE,UAAU,SAGrB,GAAAjB,EAAAC,GAAA,EAACsF,EAAAA,CAAiBA,CAAAA,CAACvC,KAAMA,EAAMC,KAAMA,EAAMC,UAAWA,SAI9D,ueCrIe,SAASsC,IACtB,MACE,GAAAxF,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,kDACb,GAAAjB,EAAAC,GAAA,EAACwF,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,uFCNe,SAASD,IACtB,MACE,GAAAxF,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,+BACb,GAAAjB,EAAAC,GAAA,EAACwF,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,2QCNe,SAASC,EAAe,CACrClM,SAAAA,CAAQ,CAGT,EACC,OAAOA,CACT,8HCNe,SAASgM,IACtB,MACE,GAAAxF,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,kDACb,GAAAjB,EAAAC,GAAA,EAACwF,EAAAA,CAAMA,CAAAA,CAAAA,IAGb", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page.tsx?9b7a", "webpack://_N_E/|ssr?ecb3", "webpack://_N_E/?054d", "webpack://_N_E/?447e", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/loading.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "\"TURBOPACK { transition: next-ssr }\";\nimport { AppPageRouteModule } from \"next/dist/server/future/route-modules/app-page/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(protected)',\n        {\n        admin: [\n        'children',\n        {\n        children: [\n        'merchants',\n        {\n        children: [\n        '[userId]',\n        {\n        children: [\n        '[merchantId]',\n        {\n        children: [\n        'transactions',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\transactions\\\\page.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\transactions\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\transactions\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\transactions\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"],\n        \n      }\n      ],\nchildren: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nagent: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\ncustomer: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nmerchant: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\transactions\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const originalPathname = \"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page\";\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page\",\n        pathname: \"/merchants/[userId]/[merchantId]/transactions\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Ftransactions%2Fpage&page=%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Ftransactions%2Fpage&pagePath=private-next-app-dir%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Ftransactions%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CIDEES%20SQQS%5CPAYSNAPo%5CMain%5Cfrontend%5Capp&appPaths=%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Ftransactions%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"webpack\":null,\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"analyticsId\":\"\",\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":200,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"inline\",\"remotePatterns\":[{\"protocol\":\"http\",\"hostname\":\"localhost\"},{\"protocol\":\"https\",\"hostname\":\"**\"}],\"unoptimized\":false},\"devIndicators\":{\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"optimizeFonts\":true,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"httpAgentOptions\":{\"keepAlive\":true},\"outputFileTracing\":true,\"staticPageGenerationTimeout\":60,\"swcMinify\":true,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"experimental\":{\"multiZoneDraftMode\":false,\"prerenderEarlyExit\":false,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"outputFileTracingRoot\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\",\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"adjustFontFallbacks\":false,\"adjustFontFallbacksWithSizeAdjust\":false,\"typedRoutes\":false,\"instrumentationHook\":false,\"bundlePagesExternals\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"missingSuspenseWithCSRBailout\":true,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":30,\"static\":300},\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"configFile\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest,\n            pageName: \"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page\"\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\layout.tsx\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\transactions\\\\page.tsx\");\n", "\"use client\";\r\n\r\nimport { SecondaryNav } from \"@/components/common/layout/SecondaryNav\";\r\nimport Switch from \"@/components/ui/switch\";\r\nimport { toggleActivity } from \"@/data/admin/toggleActivity\";\r\nimport {\r\n  ArrowLeft2,\r\n  Candle2,\r\n  Clock,\r\n  ShieldSecurity,\r\n  Sms,\r\n  UserEdit,\r\n} from \"iconsax-react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  useParams,\r\n  usePathname,\r\n  useRouter,\r\n  useSearchParams,\r\n} from \"next/navigation\";\r\nimport React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\nimport { mutate } from \"swr\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport default function CustomerDetailsLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const params = useParams();\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const { t } = useTranslation();\r\n\r\n  const tabs = [\r\n    {\r\n      title: t(\"Account Details\"),\r\n      icon: <UserEdit size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}?${searchParams.toString()}`,\r\n      id: \"__DEFAULT__\",\r\n    },\r\n    {\r\n      title: t(\"Transactions\"),\r\n      icon: <Clock size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/transactions?${searchParams.toString()}`,\r\n      id: \"transactions\",\r\n    },\r\n    {\r\n      title: t(\"KYC\"),\r\n      icon: <ShieldSecurity size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/kyc?${searchParams.toString()}`,\r\n      id: \"kyc\",\r\n    },\r\n    {\r\n      title: t(\"Fees\"),\r\n      icon: <ShieldSecurity size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/fees?${searchParams.toString()}`,\r\n      id: \"fees\",\r\n    },\r\n    {\r\n      title: t(\"Permissions\"),\r\n      icon: <Candle2 size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/permissions?${searchParams.toString()}`,\r\n      id: \"permissions\",\r\n    },\r\n    {\r\n      title: t(\"Send Email\"),\r\n      icon: <Sms size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/send-email?${searchParams.toString()}`,\r\n      id: \"send-email\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <div className=\"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4\">\r\n        <div className=\"mb-4 flex flex-wrap items-center justify-between gap-2\">\r\n          <ul className=\"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center\">\r\n            <li>\r\n              <Link\r\n                href=\"/merchants/list\"\r\n                className=\"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4\"\r\n              >\r\n                <ArrowLeft2 />\r\n                {t(\"Back\")}\r\n              </Link>\r\n            </li>\r\n            <li className=\"line-clamp-1 whitespace-nowrap\">\r\n              / {searchParams.get(\"name\")}\r\n            </li>\r\n            <li className=\"line-clamp-1 whitespace-nowrap\">\r\n              / {t(\"Merchant\")} #{params.merchantId}\r\n            </li>\r\n          </ul>\r\n          <div className=\"ml-auto inline-flex items-center gap-2 text-sm sm:text-base\">\r\n            <span>{t(\"Active\")}</span>\r\n            <Switch\r\n              defaultChecked={searchParams.get(\"active\") === \"1\"}\r\n              className=\"data-[state=unchecked]:bg-muted\"\r\n              onCheckedChange={(checked) => {\r\n                toast.promise(toggleActivity(params.userId as string), {\r\n                  loading: t(\"Loading...\"),\r\n                  success: (res) => {\r\n                    if (!res.status) throw new Error(res.message);\r\n                    const sp = new URLSearchParams(searchParams);\r\n                    mutate(`/admin/merchants/${params.merchantId}`);\r\n                    sp.set(\"active\", checked ? \"1\" : \"0\");\r\n                    router.push(`${pathname}?${sp.toString()}`);\r\n                    return res.message;\r\n                  },\r\n                  error: (err) => err.message,\r\n                });\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <SecondaryNav tabs={tabs} />\r\n      </div>\r\n\r\n      {children}\r\n    </>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport TransactionsTable from \"@/app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/_components/transactions-table\";\r\nimport { SearchBox } from \"@/components/common/form/SearchBox\";\r\nimport { TableExportButton } from \"@/components/common/TableExportButton\";\r\nimport { TableFilter } from \"@/components/common/TableFilter\";\r\nimport TransactionCategoryFilter from \"@/components/common/TransactionCategoryFilter\";\r\nimport Separator from \"@/components/ui/separator\";\r\nimport { useSWR } from \"@/hooks/useSWR\";\r\nimport { useTableData } from \"@/hooks/useTableData\";\r\nimport { searchQuery } from \"@/lib/utils\";\r\nimport { Add, ArrowRight, Receive, Repeat } from \"iconsax-react\";\r\nimport {\r\n  useParams,\r\n  usePathname,\r\n  useRouter,\r\n  useSearchParams,\r\n} from \"next/navigation\";\r\nimport * as React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { ReportCard } from \"../../../../../../../components/common/ReportCard\";\r\n\r\nexport default function TransactionHistoryPage() {\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const params = useParams();\r\n  const pathname = usePathname();\r\n\r\n  const { t } = useTranslation();\r\n\r\n  const [search, setSearch] = React.useState(searchParams.get(\"search\") ?? \"\");\r\n  const { data, meta, isLoading, filter } = useTableData(\r\n    `/admin/transactions/${params.userId}?${searchParams.toString()}`,\r\n  );\r\n\r\n  const { data: trxCount, isLoading: trxCountLoading } = useSWR(\r\n    `/admin/transactions/counts/${params.userId}`,\r\n  );\r\n\r\n  // handle search query\r\n  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    e.preventDefault();\r\n    const q = searchQuery(e.target.value);\r\n    setSearch(e.target.value);\r\n    router.replace(`${pathname}?${q.toString()}`);\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full p-4\">\r\n      <div className=\"mb-4 grid grid-cols-12 gap-4\">\r\n        <ReportCard\r\n          {...{\r\n            value: trxCount?.data?.deposit,\r\n            title: t(\"Total Deposit\"),\r\n            icon: (props) => <Add {...props} />,\r\n            status: \"\",\r\n            className: \"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3\",\r\n            statusClass: \"text-spacial-green\",\r\n            iconClass: \"bg-spacial-green-foreground\",\r\n            isLoading: trxCountLoading,\r\n          }}\r\n        />\r\n\r\n        <ReportCard\r\n          {...{\r\n            value: trxCount?.data?.withdraw,\r\n            title: t(\"Total Withdraw\"),\r\n            icon: (props) => <Receive {...props} />,\r\n            status: \"\",\r\n            className: \"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3\",\r\n            iconClass: \"bg-spacial-red-foreground text-spacial-red\",\r\n            statusClass: \"text-spacial-red\",\r\n            isLoading: trxCountLoading,\r\n          }}\r\n        />\r\n\r\n        <ReportCard\r\n          {...{\r\n            value: trxCount?.data?.transfer,\r\n            title: t(\"Total Transfers\"),\r\n            icon: (props) => <ArrowRight {...props} />,\r\n            status: \"\",\r\n            className: \"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3\",\r\n            iconClass: \"bg-spacial-blue-foreground text-spacial-blue\",\r\n            statusClass: \"text-spacial-blue\",\r\n            isLoading: trxCountLoading,\r\n          }}\r\n        />\r\n\r\n        <ReportCard\r\n          {...{\r\n            value: trxCount?.data?.exchange,\r\n            title: t(\"Total Exchange\"),\r\n            icon: (props) => <Repeat {...props} />,\r\n            status: \"\",\r\n            className: \"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3\",\r\n            isLoading: trxCountLoading,\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"h-fit w-full overflow-auto rounded-xl bg-background p-6 shadow-default\">\r\n        {/* filter bar */}\r\n        <div className=\"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center\">\r\n          <div className=\"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row\">\r\n            <TransactionCategoryFilter filter={filter} />\r\n          </div>\r\n          <div className=\"flex flex-wrap items-center gap-4\">\r\n            {/* Search box */}\r\n            <SearchBox\r\n              value={search}\r\n              onChange={handleSearch}\r\n              iconPlacement=\"end\"\r\n              placeholder={t(\"Search...\")}\r\n            />\r\n\r\n            <TableFilter\r\n              canFilterByAgent\r\n              canFilterByMethod\r\n              canFilterByGateway\r\n            />\r\n\r\n            <TableExportButton\r\n              url={`/admin/transactions/export/${params.userId}`}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <Separator className=\"my-4\" />\r\n\r\n        {/* Data table */}\r\n        <TransactionsTable data={data} meta={meta} isLoading={isLoading} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex justify-center\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import React from \"react\";\r\n\r\nexport default function CustomerLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return children;\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "_self___RSC_MANIFEST", "tree", "children", "admin", "page", "Promise", "resolve", "then", "__webpack_require__", "bind", "defaultPage", "agent", "customer", "merchant", "pages", "originalPathname", "__next_app__", "loadChunk", "routeModule", "module_compiled", "AppPageRouteModule", "definition", "kind", "route_kind", "x", "APP_PAGE", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "self", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "encryption_utils", "Mo", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "action_utils", "w", "pageName", "page_stringifiedConfig_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_pagesType_app_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRm1lcmNoYW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1Qm1lcmNoYW50SWQlNUQlMkZ0cmFuc2FjdGlvbnMlMkZwYWdlJnBhZ2U9JTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRm1lcmNoYW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1Qm1lcmNoYW50SWQlNUQlMkZ0cmFuc2FjdGlvbnMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRm1lcmNoYW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1Qm1lcmNoYW50SWQlNUQlMkZ0cmFuc2FjdGlvbnMlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rlc2t0b3AlNUNJREVFUyUyMFNRUVMlNUNQQVlTTkFQbyU1Q01haW4lNUNmcm9udGVuZCU1Q2FwcCZhcHBQYXRocz0lMkYocHJvdGVjdGVkKSUyRiU0MGFkbWluJTJGbWVyY2hhbnRzJTJGJTVCdXNlcklkJTVEJTJGJTVCbWVyY2hhbnRJZCU1RCUyRnRyYW5zYWN0aW9ucyUyRnBhZ2UmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0Qh_sriEnabled_false_middlewareConfig_e30_render", "render", "d", "pagesType", "page_types", "s", "APP", "dev", "appMod", "pageMod", "page_next_edge_ssr_entry_namespaceObject", "errorMod", "error500Mod", "Document", "renderToHTML", "app_render", "f", "serverActions", "subresourceIntegrityManifest", "config", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "adapter", "C", "IncrementalCache", "incremental_cache", "k", "handler", "runtime", "CustomerDetailsLayout", "params", "useParams", "searchParams", "useSearchParams", "router", "useRouter", "usePathname", "t", "useTranslation", "tabs", "title", "icon", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "UserEdit", "size", "variant", "href", "userId", "merchantId", "toString", "id", "Clock", "ShieldSecurity", "Candle2", "Sms", "jsxs", "Fragment", "div", "className", "ul", "li", "Link", "ArrowLeft2", "get", "span", "Switch", "defaultChecked", "onCheckedChange", "toast", "promise", "toggleActivity", "loading", "success", "res", "status", "message", "sp", "URLSearchParams", "mutate", "set", "checked", "push", "error", "err", "SecondaryNav", "TransactionHistoryPage", "search", "setSearch", "React", "data", "meta", "isLoading", "filter", "useTableData", "trxCount", "trxCountLoading", "useSWR", "ReportCard", "value", "deposit", "Add", "props", "statusClass", "iconClass", "withdraw", "Receive", "transfer", "ArrowRight", "exchange", "Repeat", "TransactionCategoryFilter", "SearchBox", "onChange", "e", "preventDefault", "q", "searchQuery", "target", "replace", "iconPlacement", "placeholder", "TableFilter", "canFilterByAgent", "canFilterByMethod", "canFilterByGateway", "TableExportButton", "url", "Separator", "TransactionsTable", "Loading", "Loader", "CustomerLayout"], "sourceRoot": ""}