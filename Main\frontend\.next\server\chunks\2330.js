exports.id=2330,exports.ids=[2330],exports.modules={70004:(e,s,t)=>{Promise.resolve().then(t.bind(t,99881))},94973:(e,s,t)=>{Promise.resolve().then(t.bind(t,48263))},72388:(e,s,t)=>{Promise.resolve().then(t.bind(t,66477))},15260:(e,s,t)=>{Promise.resolve().then(t.bind(t,27337))},13234:(e,s,t)=>{Promise.resolve().then(t.bind(t,48652))},20376:(e,s,t)=>{Promise.resolve().then(t.bind(t,66539))},25959:(e,s,t)=>{Promise.resolve().then(t.bind(t,96827))},99881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(10326),r=t(35047);t(17577);var i=t(44386),n=t(90772),l=t(33071),o=t(8281),d=t(54829),c=t(75584),m=t(70012),u=t(85999);function x(){let{t:e}=(0,m.$G)(),s=(0,r.useSearchParams)(),t=(0,r.useRouter)(),{data:x,meta:f,isLoading:g}=(0,c.Z)(`/login-sessions?page=${s.get("page")??1}&limit=${s.get("limit")??10}`);return a.jsx("div",{className:"flex flex-col gap-4",children:(0,a.jsxs)(l.Zb,{className:"flex flex-col gap-4 rounded-xl p-4 shadow-default",children:[a.jsx(l.Ol,{className:"justify-center p-0 sm:h-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 sm:flex-row sm:items-center",children:[a.jsx(l.ll,{className:"text-base font-medium leading-[22px]",children:e("Login Sessions")}),a.jsx(n.z,{onClick:s=>{s.preventDefault(),u.toast.promise(d.x,{loading:e("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return t.refresh(),e.message},error:e=>e.message})},variant:"outline",size:"sm",type:"button",className:"ml-2.5 cursor-pointer text-sm",asChild:!0,children:a.jsx("div",{children:e("Logout from all device")})})]})}),a.jsx(o.Z,{className:"mb-1 mt-[5px]"}),a.jsx(l.aY,{className:"p-0",children:a.jsx(i.Z,{data:x,isLoading:g,meta:f})})]})})}},48263:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>m});var a=t(10326),r=t(33646),i=t(53844),n=t(88589),l=t(40508),o=t(20187),d=t(50493),c=t(70012);function m(){let{t:e}=(0,c.$G)(),s=[{title:e("Account Settings"),icon:a.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:e("Charges/Commissions"),icon:a.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/fees-commissions",id:"fees-commissions"},{title:e("KYC Verification"),icon:a.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Methods"),icon:a.jsx(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/methods",id:"methods"},{title:e("Login Sessions"),icon:a.jsx(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return a.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:a.jsx(r.a,{tabs:s})})}},66477:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(10326),r=t(44386),i=t(90772),n=t(33071),l=t(8281),o=t(54829),d=t(75584),c=t(35047);t(17577);var m=t(70012),u=t(85999);function x(){let{t:e}=(0,m.$G)(),s=(0,c.useSearchParams)(),t=(0,c.useRouter)(),{data:x,meta:f,isLoading:g}=(0,d.Z)(`/login-sessions?page=${s.get("page")??1}&limit=${s.get("limit")??10}`);return a.jsx("div",{className:"flex flex-col gap-4",children:(0,a.jsxs)(n.Zb,{className:"flex flex-col gap-4 rounded-xl p-4 shadow-default",children:[a.jsx(n.Ol,{className:"justify-center p-0 sm:h-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 sm:flex-row sm:items-center",children:[a.jsx(n.ll,{className:"text-base font-medium leading-[22px]",children:e("Login Sessions")}),a.jsx(i.z,{onClick:s=>{s.preventDefault(),u.toast.promise(o.x,{loading:e("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return t.refresh(),e.message},error:e=>e.message})},variant:"outline",size:"sm",type:"button",className:"ml-2.5 cursor-pointer text-sm",asChild:!0,children:a.jsx("div",{children:e("Logout from all device")})})]})}),a.jsx(l.Z,{className:"mb-1 mt-[5px]"}),a.jsx(n.aY,{className:"p-0",children:a.jsx(r.Z,{data:x,isLoading:g,meta:f})})]})})}},27337:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>d});var a=t(10326),r=t(33646),i=t(53844),n=t(40508),l=t(50493),o=t(70012);function d(){let{t:e}=(0,o.$G)(),s=[{title:e("Account Settings"),icon:a.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings/",id:"__DEFAULT__"},{title:e("KYC Verification"),icon:a.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Login Sessions"),icon:a.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return a.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-black/[8%] bg-white p-4",children:a.jsx(r.a,{tabs:s})})}},48652:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(10326),r=t(44386),i=t(90772),n=t(33071),l=t(8281),o=t(54829),d=t(75584),c=t(35047);t(17577);var m=t(70012),u=t(85999);function x(){let e=(0,c.useSearchParams)(),s=(0,c.useRouter)(),{t}=(0,m.$G)(),{data:x,meta:f,isLoading:g}=(0,d.Z)(`/login-sessions?page=${e.get("page")??1}&limit=${e.get("limit")??10}`);return a.jsx("div",{className:"flex flex-col gap-4",children:(0,a.jsxs)(n.Zb,{className:"flex flex-col gap-4 rounded-xl p-4 shadow-default",children:[a.jsx(n.Ol,{className:"justify-center p-0 sm:h-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 sm:flex-row sm:items-center",children:[a.jsx(n.ll,{className:"text-base font-medium leading-[22px]",children:t("Login Sessions")}),a.jsx(i.z,{onClick:e=>{e.preventDefault(),u.toast.promise(o.x,{loading:t("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return s.refresh(),e.message},error:e=>e.message})},variant:"outline",size:"sm",type:"button",className:"ml-2.5 cursor-pointer text-sm",asChild:!0,children:a.jsx("div",{children:t("Logout from all device")})})]})}),a.jsx(l.Z,{className:"mb-1 mt-[5px]"}),a.jsx(n.aY,{className:"p-0",children:a.jsx(r.Z,{data:x,isLoading:g,meta:f})})]})})}},66539:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>u});var a=t(10326),r=t(33646),i=t(53844),n=t(40508),l=t(9155),o=t(418),d=t(26138),c=t(50493),m=t(70012);function u(){let{t:e}=(0,m.$G)(),s=[{title:e("Account Settings"),icon:a.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:e("KYC Verification"),icon:a.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Merchant Settings"),icon:a.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/merchant-settings",id:"merchant-settings"},{title:e("MPay API"),icon:a.jsx(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/mpay-api",id:"mpay-api"},{title:e("Webhook URL"),icon:a.jsx(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/webhook-url-settings",id:"webhook-url-settings"},{title:e("Login Sessions"),icon:a.jsx(c.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return a.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:a.jsx(r.a,{tabs:s})})}},96827:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(10326),r=t(35047);t(17577);var i=t(44386),n=t(90772),l=t(33071),o=t(8281),d=t(54829),c=t(75584),m=t(70012),u=t(85999);function x(){let{t:e}=(0,m.$G)(),s=(0,r.useSearchParams)(),t=(0,r.useRouter)(),{data:x,meta:f,isLoading:g}=(0,c.Z)(`/login-sessions?page=${s.get("page")??1}&limit=${s.get("limit")??10}`);return a.jsx("div",{className:"flex flex-col gap-4",children:(0,a.jsxs)(l.Zb,{className:"flex flex-col gap-4 rounded-xl p-4 shadow-default",children:[a.jsx(l.Ol,{className:"justify-center p-0 sm:h-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 sm:flex-row sm:items-center",children:[a.jsx(l.ll,{className:"text-base font-medium leading-[22px]",children:e("Login Sessions")}),a.jsx(n.z,{onClick:s=>{s.preventDefault(),u.toast.promise(d.x,{loading:e("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return t.refresh(),e.message},error:e=>e.message})},variant:"outline",size:"sm",type:"button",className:"ml-2.5 cursor-pointer text-sm",asChild:!0,children:a.jsx("div",{children:e("Logout from all device")})})]})}),a.jsx(o.Z,{className:"mb-1 mt-[5px]"}),a.jsx(l.aY,{className:"p-0",children:a.jsx(i.Z,{data:x,isLoading:g,meta:f})})]})})}},56140:(e,s,t)=>{"use strict";t.d(s,{Z:()=>y});var a=t(10326),r=t(77863),i=t(86508),n=t(11798),l=t(77132),o=t(6216),d=t(75817),c=t(40420),m=t(35047),u=t(93327),x=t(17577),f=t(70012),g=t(90772);let h=x.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,r.ZP)("w-full caption-bottom text-sm",e),...s})}));h.displayName="Table";let p=x.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,r.ZP)("",e),...s}));p.displayName="TableHeader";let j=x.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,r.ZP)("[&_tr:last-child]:border-0",e),...s}));j.displayName="TableBody",x.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,r.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let v=x.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,r.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));v.displayName="TableRow";let N=x.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,r.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));N.displayName="TableHead";let b=x.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,r.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));function y({data:e,isLoading:s=!1,structure:t,sorting:y,setSorting:w,padding:S=!1,className:Z,onRefresh:P,pagination:k}){let L=(0,x.useMemo)(()=>t,[t]),C=(0,m.useRouter)(),z=(0,m.usePathname)(),D=(0,m.useSearchParams)(),{t:R}=(0,f.$G)(),A=(0,i.b7)({data:e||[],columns:L,state:{sorting:y,onRefresh:P},onSortingChange:w,getCoreRowModel:(0,n.sC)(),getSortedRowModel:(0,n.tj)(),debugTable:!1});return s?a.jsx("div",{className:"rounded-md bg-background p-10",children:a.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:R("Loading...")})}):e?.length?(0,a.jsxs)("div",{className:(0,r.ZP)(`${S?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,Z),children:[(0,a.jsxs)(h,{children:[a.jsx(p,{children:A.getHeaderGroups().map(e=>a.jsx(v,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>a.jsx(N,{className:(0,r.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,a.jsxs)(g.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[R((0,i.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:a.jsx(o.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:a.jsx(o.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??a.jsx(o.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),a.jsx(j,{children:A.getRowModel().rows.map(e=>a.jsx(v,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>a.jsx(b,{className:"py-3 text-sm font-semibold",children:(0,i.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),k&&k.total>10&&a.jsx("div",{className:"pb-2 pt-6",children:a.jsx(u.Z,{showTotal:(e,s)=>R("Showing {{start}}-{{end}} of {{total}}",{start:s[0],end:s[1],total:e}),align:"start",current:k?.page,total:k?.total,pageSize:k?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let s=new URLSearchParams(D);s.set("page",e.toString()),C.push(`${z}?${s.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>a.jsx("a",{...e,children:a.jsx(d.Z,{size:"18"})}),nextIcon:e=>a.jsx("a",{...e,children:a.jsx(c.Z,{size:"18"})})})})]}):a.jsx("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(l.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),R("No data found!")]})})}b.displayName="TableCell",x.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,r.ZP)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},44386:(e,s,t)=>{"use strict";t.d(s,{Z:()=>h});var a=t(10326),r=t(56140),i=t(567),n=t(71305),l=t(17577),o=t.n(l),d=t(70012),c=t(90772),m=t(54829),u=t(67322),x=t(35047),f=t(85999);function g({row:e}){let s=(0,x.useRouter)(),{t}=(0,d.$G)();return a.jsx(c.z,{type:"button",disabled:!e?.active,variant:"destructive",size:"icon",className:"h-8 w-8",onClick:()=>{f.toast.promise((0,m.e)(e.id),{loading:t("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return s.refresh(),e.message},error:e=>e.message})},children:a.jsx(u.Z,{size:17})})}function h({data:e,meta:s,isLoading:t}){let[l,c]=o().useState([]),{t:m}=(0,d.$G)();return a.jsx(r.Z,{data:e,sorting:l,isLoading:t,setSorting:c,pagination:{total:s?.total,page:s?.page,limit:s?.limit},structure:[{id:"logged in",header:m("Logged in"),cell:e=>{let{createdAt:s}=e.row.original;return s?a.jsx("p",{className:"block min-w-24 text-sm font-normal text-secondary-text",children:(0,n.WU)(s,"dd MMM yyyy; hh:mm b")}):a.jsx("span",{className:"font-normal text-secondary-text",children:" N/A "})}},{id:"ipAddress",header:m("IP Address"),cell:e=>e.row.original?.ipAddress?a.jsx("div",{className:"font-normal text-secondary-text",children:e.row.original?.ipAddress}):a.jsx("span",{className:"font-normal text-secondary-text",children:" N/A "})},{id:"country",header:m("Country"),cell:e=>e.row.original?.country?a.jsx("div",{className:"font-normal text-secondary-text",children:e.row.original?.country}):a.jsx("span",{className:"font-normal text-secondary-text",children:" N/A "})},{id:"deviceName",header:m("Device"),cell:e=>e.row.original?.deviceName?a.jsx("div",{className:"font-normal text-secondary-text",children:e.row.original?.deviceName}):a.jsx("span",{className:"font-normal text-secondary-text",children:" N/A "})},{id:"active",header:m("Status"),cell:e=>e.row.original?.active?a.jsx(i.C,{variant:"success",children:m("Active")}):a.jsx(i.C,{variant:"secondary",className:"bg-muted",children:m("Inactive")})},{id:"menu",header:m("Menu"),cell:e=>a.jsx(g,{row:e.row.original})}]})}},33071:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>m,ll:()=>o});var a=t(10326),r=t(17577),i=t(77863);let n=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let l=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,i.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,i.ZP)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},54829:(e,s,t)=>{"use strict";t.d(s,{e:()=>i,x:()=>n});var a=t(49547),r=t(10734);async function i(e){try{let s=await a.Z.put(`/login-sessions/remove/${e}`,{});return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}async function n(){try{let e=await a.Z.put("/login-sessions/remove-all",{});return(0,r.B)(e)}catch(e){return(0,r.D)(e)}}},75584:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});var a=t(90799),r=t(35047);function i(e,s){let t=(0,r.usePathname)(),i=(0,r.useSearchParams)(),n=(0,r.useRouter)(),[l,o]=e.split("?"),d=new URLSearchParams(o);d.has("page")||d.set("page","1"),d.has("limit")||d.set("limit","10");let c=`${l}?${d.toString()}`,{data:m,error:u,isLoading:x,mutate:f,...g}=(0,a.d)(c,s);return{refresh:()=>f(m),data:m?.data?.data??[],meta:m?.data?.meta,filter:(e,s,a)=>{let r=new URLSearchParams(i.toString());s?r.set(e,s.toString()):r.delete(e),n.replace(`${t}?${r.toString()}`),a?.()},isLoading:x,error:u,...g}}},15240:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},1694:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\login-sessions\page.tsx#default`)},84514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(40099),i=t(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(i.Z,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},18406:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},40903:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510);t(71159);let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,a.jsxs)("div",{className:"",children:[a.jsx(r,{}),a.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},285:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center",children:a.jsx(r.a,{})})}},17197:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},92561:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\settings\login-sessions\page.tsx#default`)},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(40099),i=t(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(i.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},48218:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510);t(71159);let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,a.jsxs)("div",{children:[a.jsx(r,{}),a.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},80161:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},68915:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},63579:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\settings\login-sessions\page.tsx#default`)},80482:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510);t(71159);let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,a.jsxs)("div",{children:[a.jsx(r,{}),a.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},7016:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center",children:a.jsx(r.a,{})})}},29984:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},65016:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\settings\login-sessions\page.tsx#default`)}};