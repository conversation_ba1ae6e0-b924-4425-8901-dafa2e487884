"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[92580],{79205:function(e,t,n){n.d(t,{Z:function(){return u}});var r=n(2265);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:u,className:c="",children:s,iconNode:d,...p}=e;return(0,r.createElement)("svg",{ref:t,...a,width:l,height:l,stroke:n,strokeWidth:u?24*Number(i)/Number(l):i,className:o("lucide",c),...p},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:u,...c}=n;return(0,r.createElement)(i,{ref:a,iconNode:t,className:o("lucide-".concat(l(e)),u),...c})});return n.displayName="".concat(e),n}},10407:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},62319:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]])},99376:function(e,t,n){var r=n(35475);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useSelectedLayoutSegment")&&n.d(t,{useSelectedLayoutSegment:function(){return r.useSelectedLayoutSegment}}),n.o(r,"useSelectedLayoutSegments")&&n.d(t,{useSelectedLayoutSegments:function(){return r.useSelectedLayoutSegments}})},98575:function(e,t,n){n.d(t,{F:function(){return o},e:function(){return a}});var r=n(2265);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function a(...e){return r.useCallback(o(...e),e)}},66840:function(e,t,n){n.d(t,{WV:function(){return i},jH:function(){return u}});var r=n(2265),l=n(54887),o=n(37053),a=n(57437),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.Z8)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...o}=e,i=l?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...o,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function u(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},37053:function(e,t,n){n.d(t,{Z8:function(){return a},g7:function(){return i},sA:function(){return c}});var r=n(2265),l=n(98575),o=n(57437);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,a;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,u=function(e,t){let n={...t};for(let r in t){let l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...e)=>{o(...e),l(...e)}:l&&(n[r]=l):"style"===r?n[r]={...l,...o}:"className"===r&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,l.F)(t,i):i),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...a}=e,i=r.Children.toArray(l),u=i.find(s);if(u){let e=u.props.children,l=i.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...a,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}var i=a("Slot"),u=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},43577:function(e,t,n){n.d(t,{IZ:function(){return d}});let{Axios:r,AxiosError:l,CanceledError:o,isCancel:a,CancelToken:i,VERSION:u,all:c,Cancel:s,isAxiosError:d,spread:p,toFormData:f,AxiosHeaders:m,HttpStatusCode:h,formToJSON:v,getAdapter:g,mergeConfig:y}=n(83464).default},90535:function(e,t,n){n.d(t,{j:function(){return a}});var r=n(61994);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.W,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:i}=t,u=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let o=l(t)||l(r);return a[e][o]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,u,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...c}[t]):({...i,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},35934:function(e,t,n){n.d(t,{VM:function(){return m},uZ:function(){return h}});var r=n(2265),l=Object.defineProperty,o=Object.defineProperties,a=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&s(e,n,t[n]);if(i)for(var n of i(t))c.call(t,n)&&s(e,n,t[n]);return e},p=(e,t)=>o(e,a(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n},m=r.createContext({}),h=r.forwardRef((e,t)=>{let n;var l,o,a,i,u,{value:c,onChange:s,maxLength:h,textAlign:y="left",pattern:b,placeholder:w,inputMode:S="numeric",onComplete:E,pushPasswordManagerStrategy:C="increase-width",pasteTransformer:P,containerClassName:k,noScriptCSSFallback:x=g,render:R,children:M}=e,j=f(e,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]);let[O,W]=r.useState("string"==typeof j.defaultValue?j.defaultValue:""),A=null!=c?c:O,_=(n=r.useRef(),r.useEffect(()=>{n.current=A}),n.current),D=r.useCallback(e=>{null==s||s(e),W(e)},[s]),N=r.useMemo(()=>b?"string"==typeof b?new RegExp(b):b:null,[b]),T=r.useRef(null),B=r.useRef(null),L=r.useRef({value:A,onChange:D,isIOS:"undefined"!=typeof window&&(null==(o=null==(l=null==window?void 0:window.CSS)?void 0:l.supports)?void 0:o.call(l,"-webkit-touch-callout","none"))}),I=r.useRef({prev:[null==(a=T.current)?void 0:a.selectionStart,null==(i=T.current)?void 0:i.selectionEnd,null==(u=T.current)?void 0:u.selectionDirection]});r.useImperativeHandle(t,()=>T.current,[]),r.useEffect(()=>{let e=T.current,t=B.current;if(!e||!t)return;function n(){if(document.activeElement!==e){z(null),q(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,l=e.maxLength,o=e.value,a=I.current.prev,i=-1,u=-1,c;if(0!==o.length&&null!==t&&null!==n){let e=t===n,r=t===o.length&&o.length<l;if(e&&!r){if(0===t)i=0,u=1,c="forward";else if(t===l)i=t-1,u=t,c="backward";else if(l>1&&o.length>1){let e=0;if(null!==a[0]&&null!==a[1]){c=t<a[1]?"backward":"forward";let n=a[0]===a[1]&&a[0]<l;"backward"!==c||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&T.current.setSelectionRange(i,u,c)}let s=-1!==i?i:t,d=-1!==u?u:n,p=null!=c?c:r;z(s),q(d),I.current.prev=[s,d,p]}if(L.current.value!==e.value&&L.current.onChange(e.value),I.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&H(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";v(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),v(e.sheet,`[data-input-otp]:autofill { ${t} }`),v(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),v(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),v(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let l=new ResizeObserver(r);return l.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),l.disconnect()}},[]);let[F,$]=r.useState(!1),[Z,H]=r.useState(!1),[V,z]=r.useState(null),[G,q]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=T.current)||e.dispatchEvent(new Event("input"));let l=null==(t=T.current)?void 0:t.selectionStart,o=null==(n=T.current)?void 0:n.selectionEnd,a=null==(r=T.current)?void 0:r.selectionDirection;null!==l&&null!==o&&(z(l),q(o),I.current.prev=[l,o,a])},0),setTimeout(e,10),setTimeout(e,50)},[A,Z]),r.useEffect(()=>{void 0!==_&&A!==_&&_.length<h&&A.length===h&&(null==E||E(A))},[h,E,_,A]);let U=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:l}){let[o,a]=r.useState(!1),[i,u]=r.useState(!1),[c,s]=r.useState(!1),d=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&o&&i,[o,i,n]),p=r.useCallback(()=>{let r=e.current,l=t.current;if(!r||!l||c||"none"===n)return;let o=r.getBoundingClientRect().left+r.offsetWidth,i=r.getBoundingClientRect().top+r.offsetHeight/2;0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(o-18,i)===r||(a(!0),s(!0))},[e,t,c,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){u(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let l=setInterval(r,1e3);return()=>{clearInterval(l)}},[e,n]),r.useEffect(()=>{let e=l||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(p,0),o=setTimeout(p,2e3),a=setTimeout(p,5e3),i=setTimeout(()=>{s(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(o),clearTimeout(a),clearTimeout(i)}},[t,l,n,p]),{hasPWMBadge:o,willPushPWMBadge:d,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:B,inputRef:T,pushPasswordManagerStrategy:C,isFocused:Z}),J=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,h);if(t.length>0&&N&&!N.test(t)){e.preventDefault();return}"string"==typeof _&&t.length<_.length&&document.dispatchEvent(new Event("selectionchange")),D(t)},[h,D,_,N]),K=r.useCallback(()=>{var e;if(T.current){let t=Math.min(T.current.value.length,h-1),n=T.current.value.length;null==(e=T.current)||e.setSelectionRange(t,n),z(t),q(n)}H(!0)},[h]),Q=r.useCallback(e=>{var t,n;let r=T.current;if(!P&&(!L.current.isIOS||!e.clipboardData||!r))return;let l=e.clipboardData.getData("text/plain"),o=P?P(l):l;e.preventDefault();let a=null==(t=T.current)?void 0:t.selectionStart,i=null==(n=T.current)?void 0:n.selectionEnd,u=(a!==i?A.slice(0,a)+o+A.slice(i):A.slice(0,a)+o+A.slice(a)).slice(0,h);if(u.length>0&&N&&!N.test(u))return;r.value=u,D(u);let c=Math.min(u.length,h-1),s=u.length;r.setSelectionRange(c,s),z(c),q(s)},[h,D,N,A]),X=r.useMemo(()=>({position:"relative",cursor:j.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[j.disabled]),Y=r.useMemo(()=>({position:"absolute",inset:0,width:U.willPushPWMBadge?`calc(100% + ${U.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:U.willPushPWMBadge?`inset(0 ${U.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:y,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[U.PWM_BADGE_SPACE_WIDTH,U.willPushPWMBadge,y]),ee=r.useMemo(()=>r.createElement("input",p(d({autoComplete:j.autoComplete||"one-time-code"},j),{"data-input-otp":!0,"data-input-otp-placeholder-shown":0===A.length||void 0,"data-input-otp-mss":V,"data-input-otp-mse":G,inputMode:S,pattern:null==N?void 0:N.source,"aria-placeholder":w,style:Y,maxLength:h,value:A,ref:T,onPaste:e=>{var t;Q(e),null==(t=j.onPaste)||t.call(j,e)},onChange:J,onMouseOver:e=>{var t;$(!0),null==(t=j.onMouseOver)||t.call(j,e)},onMouseLeave:e=>{var t;$(!1),null==(t=j.onMouseLeave)||t.call(j,e)},onFocus:e=>{var t;K(),null==(t=j.onFocus)||t.call(j,e)},onBlur:e=>{var t;H(!1),null==(t=j.onBlur)||t.call(j,e)}})),[J,K,Q,S,Y,h,G,V,j,null==N?void 0:N.source,A]),et=r.useMemo(()=>({slots:Array.from({length:h}).map((e,t)=>{var n;let r=Z&&null!==V&&null!==G&&(V===G&&t===V||t>=V&&t<G),l=void 0!==A[t]?A[t]:null;return{char:l,placeholderChar:void 0!==A[0]?null:null!=(n=null==w?void 0:w[t])?n:null,isActive:r,hasFakeCaret:r&&null===l}}),isFocused:Z,isHovering:!j.disabled&&F}),[Z,F,h,G,V,j.disabled,A]),en=r.useMemo(()=>R?R(et):r.createElement(m.Provider,{value:et},M),[M,et,R]);return r.createElement(r.Fragment,null,null!==x&&r.createElement("noscript",null,r.createElement("style",null,x)),r.createElement("div",{ref:B,"data-input-otp-container":!0,style:X,className:k},en,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},ee)))});function v(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}h.displayName="Input";var g=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`}}]);