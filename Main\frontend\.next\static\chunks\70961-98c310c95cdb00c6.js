"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[70961],{15054:function(e,t,r){r.d(t,{h:function(){return o}});var n=r(57437);function o(e){let{title:t,subTitle:r}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"mb-2.5 text-sm text-secondary-text",children:r}),(0,n.jsx)("h1",{className:"text-[28px] font-medium leading-10 md:text-[32px]",children:t})]})}},41709:function(e,t,r){function n(e){let{condition:t,children:r}=e;return t?r:null}r.d(t,{J:function(){return n}}),r(2265)},62869:function(e,t,r){r.d(t,{d:function(){return u},z:function(){return l}});var n=r(57437),o=r(37053),s=r(90535),a=r(2265),i=r(94508);let u=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:s,size:a,asChild:l=!1,...d}=e,c=l?o.g7:"button";return(0,n.jsx)(c,{className:(0,i.ZP)(u({variant:s,size:a,className:r})),ref:t,...d})});l.displayName="Button"},15681:function(e,t,r){r.d(t,{NI:function(){return g},Wi:function(){return c},l0:function(){return l},lX:function(){return p},xJ:function(){return m},zG:function(){return h}});var n=r(57437),o=r(37053),s=r(2265),a=r(29501),i=r(26815),u=r(94508);let l=a.RV,d=s.createContext({}),c=e=>{let{...t}=e;return(0,n.jsx)(d.Provider,{value:{name:t.name},children:(0,n.jsx)(a.Qr,{...t})})},v=()=>{let e=s.useContext(d),t=s.useContext(f),{getFieldState:r,formState:n}=(0,a.Gc)(),o=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...o}},f=s.createContext({}),m=s.forwardRef((e,t)=>{let{className:r,...o}=e,a=s.useId();return(0,n.jsx)(f.Provider,{value:{id:a},children:(0,n.jsx)("div",{ref:t,className:(0,u.ZP)("space-y-2",r),...o})})});m.displayName="FormItem";let p=s.forwardRef((e,t)=>{let{className:r,required:o,...s}=e,{error:a,formItemId:l}=v();return(0,n.jsx)("span",{children:(0,n.jsx)(i.Z,{ref:t,className:(0,u.ZP)(a&&"text-base font-medium text-destructive",r),htmlFor:l,...s})})});p.displayName="FormLabel";let g=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:a,formDescriptionId:i,formMessageId:u}=v();return(0,n.jsx)(o.g7,{ref:t,id:a,"aria-describedby":s?"".concat(i," ").concat(u):"".concat(i),"aria-invalid":!!s,...r})});g.displayName="FormControl",s.forwardRef((e,t)=>{let{className:r,...o}=e,{formDescriptionId:s}=v();return(0,n.jsx)("p",{ref:t,id:s,className:(0,u.ZP)("text-sm text-muted-foreground",r),...o})}).displayName="FormDescription";let h=s.forwardRef((e,t)=>{let{className:r,children:o,...s}=e,{error:a,formMessageId:i}=v(),l=a?String(null==a?void 0:a.message):o;return l?(0,n.jsx)("p",{ref:t,id:i,className:(0,u.ZP)("text-sm font-medium text-destructive",r),...s,children:l}):null});h.displayName="FormMessage"},26815:function(e,t,r){var n=r(57437),o=r(6394),s=r(90535),a=r(2265),i=r(94508);let u=(0,s.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(o.f,{ref:t,className:(0,i.ZP)(u(),r),...s})});l.displayName=o.f.displayName,t.Z=l},6512:function(e,t,r){var n=r(57437),o=r(55156),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,orientation:s="horizontal",decorative:i=!0,...u}=e;return(0,n.jsx)(o.f,{ref:t,decorative:i,orientation:s,className:(0,a.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",r),...u})});i.displayName=o.f.displayName,t.Z=i},31092:function(e,t,r){r.d(t,{V2:function(){return a},WG:function(){return i},c0:function(){return l},gF:function(){return u},z9:function(){return s}});var n=r(79981),o=r(83464);async function s(e){var t,r,s,a,i,u,l,d,c,v,f,m,p;try{let o=await n.Z.post("/auth/login",e);return{statusCode:o.status,statusText:o.statusText,status:200===o.status,message:null!==(a=null===(t=o.data)||void 0===t?void 0:t.message)&&void 0!==a?a:"",token:null!==(i=null===(r=o.data)||void 0===r?void 0:r.token)&&void 0!==i?i:null,redirectURL:null!==(u=null===(s=o.data)||void 0===s?void 0:s.redirectUrl)&&void 0!==u?u:null}}catch(n){let e=500,t="Internal Server Error",r="An unknown error occurred";return o.default.isAxiosError(n)?(e=null!==(f=null===(l=n.response)||void 0===l?void 0:l.status)&&void 0!==f?f:500,t=null!==(m=null===(d=n.response)||void 0===d?void 0:d.statusText)&&void 0!==m?m:"Internal Server Error",r=null!==(p=null===(v=n.response)||void 0===v?void 0:null===(c=v.data)||void 0===c?void 0:c.message)&&void 0!==p?p:n.message):n instanceof Error&&(r=n.message),{statusCode:e,statusText:t,status:!1,message:r,token:null,redirectURL:null}}}async function a(e){var t,r,s,a,i,u,l,d,c;let{token:v,otp:f,isRememberMe:m,fingerprint:p}=e;try{let e=await n.Z.post("/auth/verify-otp",{token:v,otp:f,isRememberMe:m,fingerprint:p});return{statusCode:e.status,statusText:e.statusText,status:200===e.status,message:null!==(r=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(n){let e=500,t="Internal Server Error",r="An unknown error occurred";return o.default.isAxiosError(n)?(e=null!==(l=null===(s=n.response)||void 0===s?void 0:s.status)&&void 0!==l?l:500,t=null!==(d=null===(a=n.response)||void 0===a?void 0:a.statusText)&&void 0!==d?d:"Internal Server Error",r=null!==(c=null===(u=n.response)||void 0===u?void 0:null===(i=u.data)||void 0===i?void 0:i.message)&&void 0!==c?c:n.message):n instanceof Error&&(r=n.message),{statusCode:e,statusText:t,status:!1,message:r}}}async function i(e){var t,r,s,a,i,u,l,d,c,v,f;try{let o=await n.Z.post("/auth/resend-otp",{token:e});return{statusCode:o.status,statusText:o.statusText,status:200===o.status,message:null!==(s=null===(t=o.data)||void 0===t?void 0:t.message)&&void 0!==s?s:"",token:null!==(a=null===(r=o.data)||void 0===r?void 0:r.token)&&void 0!==a?a:""}}catch(n){let e=500,t="Internal Server Error",r="An unknown error occurred";return o.default.isAxiosError(n)?(e=null!==(c=null===(i=n.response)||void 0===i?void 0:i.status)&&void 0!==c?c:500,t=null!==(v=null===(u=n.response)||void 0===u?void 0:u.statusText)&&void 0!==v?v:"Internal Server Error",r=null!==(f=null===(d=n.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.message)&&void 0!==f?f:n.message):n instanceof Error&&(r=n.message),{statusCode:e,statusText:t,status:!1,message:r,token:""}}}async function u(e){var t,r,s,a,i,u,l,d,c;let{email:v}=e;try{let e=await n.Z.post("/auth/forgot-password",{email:v});return{statusCode:e.status,statusText:e.statusText,status:200===e.status,message:null!==(r=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(n){let e=500,t="Internal Server Error",r="An unknown error occurred";return o.default.isAxiosError(n)?(e=null!==(l=null===(s=n.response)||void 0===s?void 0:s.status)&&void 0!==l?l:500,t=null!==(d=null===(a=n.response)||void 0===a?void 0:a.statusText)&&void 0!==d?d:"Internal Server Error",r=null!==(c=null===(u=n.response)||void 0===u?void 0:null===(i=u.data)||void 0===i?void 0:i.message)&&void 0!==c?c:n.message):n instanceof Error&&(r=n.message),{statusCode:e,statusText:t,status:!1,message:r}}}async function l(e){var t,r,s,a,i,u,l,d,c;let{password:v,passwordConfirmation:f,token:m}=e;try{let e=await n.Z.post("/auth/reset-password",{password:v,passwordConfirmation:f,token:m});return{statusCode:e.status,statusText:e.statusText,status:200===e.status,message:null!==(r=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(n){let e=500,t="Internal Server Error",r="An unknown error occurred";return o.default.isAxiosError(n)?(e=null!==(l=null===(s=n.response)||void 0===s?void 0:s.status)&&void 0!==l?l:500,t=null!==(d=null===(a=n.response)||void 0===a?void 0:a.statusText)&&void 0!==d?d:"Internal Server Error",r=null!==(c=null===(u=n.response)||void 0===u?void 0:null===(i=u.data)||void 0===i?void 0:i.message)&&void 0!==c?c:n.message):n instanceof Error&&(r=n.message),{statusCode:e,statusText:t,status:!1,message:r}}}},79981:function(e,t,r){var n=r(78040),o=r(83464);t.Z=o.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){r.d(t,{rH:function(){return n},sp:function(){return o}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},o=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){r.d(t,{F:function(){return d},Fg:function(){return f},Fp:function(){return l},Qp:function(){return v},ZP:function(){return i},fl:function(){return u},qR:function(){return c},w4:function(){return m}});var n=r(78040),o=r(61994),s=r(14438),a=r(53335);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,o.W)(t))}function u(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let l=e=>{e&&navigator.clipboard.writeText(e).then(()=>s.toast.success("Copied to clipboard!")).catch(()=>{s.toast.error("Failed to copy!")})};class d{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let o;let s=void 0===t?this.currencyCode:t;try{o=new Intl.NumberFormat("en-US",{style:"currency",currency:s,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){o=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let a=null!==(n=null===(r=o.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:s,i=o.format(e),u=i.substring(a.length).trim();return{currencyCode:s,currencySymbol:a,formattedAmount:i,amountText:u}},this.currencyCode=e||"USD"}}let c=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",v=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",m=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",o=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?o.set(n,e):o.delete(n),o}}}]);