(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[45323],{30479:function(e,s,r){Promise.resolve().then(r.bind(r,60747))},43925:function(e,s,r){"use strict";r.d(s,{n:function(){return o}});var l=r(57437),t=r(41709),n=r(31117),a=r(99545),i=r(99376),d=r(43949);function o(){var e,s,r;let o=(0,i.useParams)(),{t:c}=(0,d.$G)(),{data:u,isLoading:x}=(0,n.d)("/commissions/total-pending/".concat(o.userId));return(0,l.jsxs)("div",{className:"col-span-12 inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default md:col-span-6",children:[(0,l.jsx)("div",{className:"flex h-[54px] w-[54px] items-center justify-center rounded-full bg-muted",children:(0,l.jsx)(a.Z,{variant:"Bulk",size:34})}),(0,l.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,l.jsx)(t.J,{condition:x,children:(0,l.jsx)("h1",{children:"0.00"})}),(0,l.jsx)(t.J,{condition:!x,children:(0,l.jsx)("h1",{children:"".concat(null!==(r=null==u?void 0:null===(e=u.data)||void 0===e?void 0:e.total)&&void 0!==r?r:"0.00"," ").concat(null==u?void 0:null===(s=u.data)||void 0===s?void 0:s.currency)})}),(0,l.jsx)("span",{className:"block text-xs font-normal leading-4",children:c("Total Commission")})]})]})}},60747:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return ec}});var l=r(57437),t=r(85487),n=r(6596),a=r(31117),i=r(94508),d=r(39544),o=r(19571),c=r(19049),u=r(15066),x=r(39748),m=r(1350),h=r(99376),p=r(43949),v=r(41709),j=r(52323),g=r(62869),b=r(15681),f=r(95186),y=r(26815),N=r(81123),I=r(40593),C=r(13590),z=r(22291),w=r(2265),A=r(29501),k=r(14438),S=r(31229);let Z=S.z.object({street:S.z.string({required_error:"Street is required."}),country:S.z.string({required_error:"Country is required."}),city:S.z.string({required_error:"city is required."}),zipCode:S.z.string({required_error:"Zip code is required."})});function G(e){let{customer:s,onMutate:r}=e,[a,i]=w.useTransition(),[d,o]=w.useState(),{getCountryByCode:c}=(0,I.F)(),{t:u}=(0,p.$G)(),x=(0,A.cI)({resolver:(0,C.F)(Z),defaultValues:{street:"",city:"",country:"",zipCode:""}});return w.useEffect(()=>{var e,r,l,t,n,a,i,d,u,m;return s&&(null==s?void 0:null===(e=s.customer)||void 0===e?void 0:e.address)&&(c(null==s?void 0:null===(l=s.customer)||void 0===l?void 0:null===(r=l.address)||void 0===r?void 0:r.countryCode,o),x.reset({street:null==s?void 0:null===(n=s.customer)||void 0===n?void 0:null===(t=n.address)||void 0===t?void 0:t.addressLine,city:null==s?void 0:null===(i=s.customer)||void 0===i?void 0:null===(a=i.address)||void 0===a?void 0:a.city,country:null==s?void 0:null===(d=s.customer)||void 0===d?void 0:d.address.countryCode,zipCode:null==s?void 0:null===(m=s.customer)||void 0===m?void 0:null===(u=m.address)||void 0===u?void 0:u.zipCode})),()=>{x.reset({street:"",city:"",country:"",zipCode:""})}},[s]),(0,l.jsx)(b.l0,{...x,children:(0,l.jsx)("form",{onSubmit:x.handleSubmit(e=>{i(async()=>{let l=await (0,N.H)(e,s.id);(null==l?void 0:l.status)?(r(),k.toast.success(l.message)):k.toast.error(u(l.message))})}),className:"rounded-xl border border-border bg-background",children:(0,l.jsxs)(n.Qd,{value:"ADDRESS_INFORMATION",className:"border-none px-4 py-0",children:[(0,l.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,l.jsx)("p",{className:"text-base font-medium leading-[22px]",children:u("Address")})}),(0,l.jsxs)(n.vF,{className:"flex flex-col gap-2 border-t px-1 pt-4",children:[(0,l.jsx)(y.Z,{children:u("Full mailing address")}),(0,l.jsxs)("div",{className:"grid grid-cols-12 gap-2.5",children:[(0,l.jsx)(b.Wi,{control:x.control,name:"street",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{className:"col-span-12",children:[(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:u("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:x.control,name:"country",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{className:"col-span-12",children:[(0,l.jsx)(b.NI,{children:(0,l.jsx)(j.g,{defaultValue:d,onSelectChange:e=>s.onChange(e.code.cca2)})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:x.control,name:"city",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{className:"col-span-12 md:col-span-6",children:[(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:u("City"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:x.control,name:"zipCode",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{className:"col-span-12 md:col-span-6",children:[(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:u("Zip code"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}})]}),(0,l.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,l.jsxs)(g.z,{disabled:a,children:[(0,l.jsxs)(v.J,{condition:!a,children:[u("Save"),(0,l.jsx)(z.Z,{size:20})]}),(0,l.jsx)(v.J,{condition:a,children:(0,l.jsx)(t.Loader,{title:u("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}var F=r(79981),R=r(97751);async function q(e,s){try{let r=await F.Z.put("/admin/agents/update/".concat(s),e);return(0,R.B)(r)}catch(e){return(0,R.D)(e)}}let E=S.z.object({name:S.z.string({required_error:"Agent name is required."}),occupation:S.z.string({required_error:"Occupation is required."}),whatsapp:S.z.string({required_error:"Whatsapp is required."}),agentId:S.z.string({required_error:"Agent ID is required."}),processingTime:S.z.string({required_error:"Processing time is required."})});function D(e){let{agentInfo:s,onMutate:r}=e,[a,i]=(0,w.useTransition)(),d=(0,h.useParams)(),{t:o}=(0,p.$G)(),c=(0,A.cI)({resolver:(0,C.F)(E),defaultValues:{name:"",occupation:"",whatsapp:"",processingTime:"",agentId:""}});return w.useEffect(()=>{if(s){var e,r,l,t,n;c.reset({name:null!==(e=s.name)&&void 0!==e?e:"",occupation:null!==(r=s.occupation)&&void 0!==r?r:"",whatsapp:null!==(l=s.whatsapp)&&void 0!==l?l:"",processingTime:null!==(t=String(s.processingTime))&&void 0!==t?t:"",agentId:null!==(n=s.agentId)&&void 0!==n?n:""})}},[s]),(0,l.jsx)(b.l0,{...c,children:(0,l.jsx)("form",{onSubmit:c.handleSubmit(e=>{var l,t,n,a;let c={...e,email:null==s?void 0:s.email,addressLine:null==s?void 0:null===(l=s.address)||void 0===l?void 0:l.addressLine,zipCode:null==s?void 0:null===(t=s.address)||void 0===t?void 0:t.zipCode,countryCode:null==s?void 0:null===(n=s.address)||void 0===n?void 0:n.countryCode,city:null==s?void 0:null===(a=s.address)||void 0===a?void 0:a.city};i(async()=>{let e=await q(c,null==d?void 0:d.userId);e.status?(r(),k.toast.success(e.message)):k.toast.error(o(e.message))})}),className:"rounded-xl border border-border bg-background",children:(0,l.jsxs)(n.Qd,{value:"AGENT_INFORMATION",className:"border-none px-4 py-0",children:[(0,l.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,l.jsx)("p",{className:"text-base font-medium leading-[22px]",children:o("Agent profile")})}),(0,l.jsxs)(n.vF,{className:"flex flex-col gap-6 border-t px-1 py-4",children:[(0,l.jsx)(b.Wi,{control:c.control,name:"name",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:o("Agent name")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:o("Enter your name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:c.control,name:"occupation",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:o("Job/Occupation")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:o("Enter your job"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:c.control,name:"whatsapp",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:o("WhatsApp number/link")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:o("Enter your WhatsApp account number or link"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:c.control,name:"agentId",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:o("Agent ID")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",disabled:!0,placeholder:"1241SDFE3",className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:c.control,name:"processingTime",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:o("Processing Time (Hours)")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:o("Enter processing time"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,l.jsx)(g.z,{children:a?(0,l.jsx)(t.Loader,{title:o("Updating..."),className:"text-primary-foreground"}):(0,l.jsxs)(l.Fragment,{children:[o("Save"),(0,l.jsx)(z.Z,{size:20})]})})})]})]})})})}var O=r(1828);async function _(e,s){try{let r=await F.Z.put("/admin/agents/".concat(s,"/").concat(e),{});return(0,R.B)(r)}catch(e){return(0,R.D)(e)}}async function T(e,s){try{let r=await F.Z.put("/admin/agents/update-status/".concat(e),s);return(0,R.B)(r)}catch(e){return(0,R.D)(e)}}var J=r(60827);function L(e){let{id:s,agentId:r,status:t,suspended:a,recommended:i,onMutate:d}=e,{t:c}=(0,p.$G)(),u=e=>{let{isSuspend:r=a,isRecommended:l=i}=e;k.toast.promise(T(s,{isSuspend:r,isRecommended:l}),{loading:c("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return d(),e.message},error:e=>e.message})},x=e=>{k.toast.promise(_(r,e),{loading:c("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return d(),e.message},error:e=>e.message})};return(0,l.jsxs)(n.Qd,{value:"AgentStatus",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,l.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,l.jsx)("p",{className:"text-base font-medium leading-[22px]",children:c("Agent status")})}),(0,l.jsxs)(n.vF,{className:"flex flex-col gap-6 border-t pt-4",children:[(0,l.jsxs)("div",{className:"inline-flex items-center gap-2",children:[(0,l.jsx)("h6",{className:"w-[150px]",children:c("Suspended")}),(0,l.jsx)(O.Z,{defaultChecked:a,onCheckedChange:()=>u({isSuspend:!a})})]}),(0,l.jsxs)("div",{className:"inline-flex items-center gap-2",children:[(0,l.jsx)("h6",{className:"w-[150px]",children:c("Recommended")}),(0,l.jsx)(O.Z,{defaultChecked:i,onCheckedChange:()=>u({isRecommended:!i})})]}),"pending"===t?(0,l.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,l.jsx)("h5",{className:"text-base font-medium",children:c("Suspended")}),(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,l.jsxs)(g.z,{type:"button",onClick:()=>x("accept"),className:"bg-[#0B6A0B] text-white hover:bg-[#149014]",children:[(0,l.jsx)(o.Z,{}),c("Grant Access")]}),(0,l.jsxs)(g.z,{type:"button",onClick:()=>x("decline"),className:"bg-[#D13438] text-white hover:bg-[#b42328]",children:[(0,l.jsx)(J.Z,{}),c("Reject")]})]})]}):null]})]})}var B=r(19060),P=r(26110),W=r(6512),M=r(4995),X=r(54763),V=r(8877),$=r(31525);function U(e){let{wallets:s}=e,{t:r}=(0,p.$G)();return(0,l.jsxs)(n.Qd,{value:"BALANCE",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,l.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,l.jsx)("p",{className:"text-base font-medium leading-[22px]",children:r("Balance")})}),(0,l.jsx)(n.vF,{className:"grid grid-cols-12 gap-4 border-t pt-4",children:null==s?void 0:s.map(e=>(0,l.jsx)(K,{item:e},e.id))})]})}function K(e){let{item:s}=e,r=new X.w(s);return(0,l.jsxs)("div",{className:"relative col-span-12 flex flex-col gap-2 rounded-xl border border-border bg-accent p-6 text-accent-foreground sm:col-span-6 md:col-span-4 lg:col-span-3",children:[(0,l.jsxs)("div",{className:"absolute right-1 top-1 flex items-center gap-1",children:[(0,l.jsx)(Y,{wallet:r,userId:r.userId}),(0,l.jsx)(Q,{wallet:r,userId:r.userId})]}),(0,l.jsx)("span",{className:"text-xs font-normal leading-4",children:null==r?void 0:r.currency.code}),(0,l.jsxs)("h6",{className:"text-sm font-semibold leading-5",children:[r.balance," ",null==r?void 0:r.currency.code]})]})}function Q(e){let{userId:s,wallet:r}=e,[n,a]=w.useState(!1),[i,d]=w.useState(!1),{t:o}=(0,p.$G)(),[c,u]=w.useState({amount:"0",currencyCode:null==r?void 0:r.currency.code,userId:s,keepRecords:!0}),x=()=>{u({amount:"0",currencyCode:null==r?void 0:r.currency.code,userId:s,keepRecords:!0})},m=async e=>{e.preventDefault(),d(!0);let s=await (0,M.y)({amount:Number(c.amount),currencyCode:c.currencyCode,userId:c.userId,keepRecords:c.keepRecords},"add");s.status?(k.toast.success(s.message),d(!1),a(!1)):(k.toast.error(s.message),d(!1))};return(0,l.jsxs)(P.Vq,{open:n,onOpenChange:e=>{a(e),x()},children:[(0,l.jsx)(P.hg,{asChild:!0,children:(0,l.jsx)(g.z,{variant:"ghost",size:"icon",className:"size-8 hover:bg-secondary-text/30 active:bg-secondary-text/50",children:(0,l.jsx)(V.Z,{strokeWidth:3,size:17})})}),(0,l.jsxs)(P.cZ,{children:[(0,l.jsxs)(P.fK,{children:[(0,l.jsx)(P.$N,{className:"text-semibold",children:o("Add Balance")}),(0,l.jsx)(P.Be,{className:"hidden"})]}),(0,l.jsx)(W.Z,{}),(0,l.jsx)("div",{children:(0,l.jsxs)("form",{onSubmit:m,className:"flex flex-col space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,l.jsxs)(y.Z,{className:"text-sm",children:[" ",o("Balance")," "]}),(0,l.jsx)(f.I,{type:"number",value:c.amount,min:0,onChange:e=>u(s=>({...s,amount:e.target.value}))})]}),(0,l.jsxs)(y.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,l.jsx)(B.X,{checked:c.keepRecords,onCheckedChange:e=>u(s=>({...s,keepRecords:e}))}),(0,l.jsx)("span",{children:o("Keep in record")})]}),(0,l.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,l.jsx)(P.GG,{asChild:!0,children:(0,l.jsx)(g.z,{type:"button",variant:"ghost",children:o("Cancel")})}),(0,l.jsx)(g.z,{disabled:i,children:i?(0,l.jsx)(t.Loader,{title:o("Uploading..."),className:"text-primary-foreground"}):o("Update")})]})]})})]})]})}function Y(e){let{userId:s,wallet:r}=e,[n,a]=w.useState(!1),[i,d]=w.useState(!1),{t:o}=(0,p.$G)(),[c,u]=w.useState({amount:"0",currencyCode:null==r?void 0:r.currency.code,userId:s,keepRecords:!0}),x=()=>{u({amount:"0",currencyCode:null==r?void 0:r.currency.code,userId:s,keepRecords:!0})},m=async e=>{e.preventDefault(),a(!0);let s=await (0,M.y)({amount:Number(c.amount),currencyCode:c.currencyCode,userId:c.userId,keepRecords:c.keepRecords},"remove");s.status?(x(),d(!1),a(!1),k.toast.success(s.status)):(a(!1),k.toast.error(s.status))};return(0,l.jsxs)(P.Vq,{open:i,onOpenChange:e=>{d(e),x()},children:[(0,l.jsx)(P.hg,{asChild:!0,children:(0,l.jsx)(g.z,{variant:"ghost",size:"icon",className:"size-8 hover:bg-secondary-text/30 active:bg-secondary-text/50",children:(0,l.jsx)($.Z,{strokeWidth:3,size:17})})}),(0,l.jsxs)(P.cZ,{children:[(0,l.jsxs)(P.fK,{children:[(0,l.jsx)(P.$N,{className:"text-semibold",children:o("Remove Balance")}),(0,l.jsx)(P.Be,{className:"hidden"})]}),(0,l.jsx)(W.Z,{}),(0,l.jsx)("div",{children:(0,l.jsxs)("form",{onSubmit:m,className:"flex flex-col space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,l.jsxs)(y.Z,{className:"text-sm",children:[" ",o("Balance")," "]}),(0,l.jsx)(f.I,{type:"number",value:c.amount,min:0,onChange:e=>u(s=>({...s,amount:e.target.value}))})]}),(0,l.jsxs)(y.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,l.jsx)(B.X,{checked:c.keepRecords,onCheckedChange:e=>u(s=>({...s,keepRecords:e}))}),(0,l.jsx)("span",{children:o("Keep in record")})]}),(0,l.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,l.jsx)(P.GG,{asChild:!0,children:(0,l.jsx)(g.z,{type:"button",variant:"ghost",children:o("Cancel")})}),(0,l.jsx)(g.z,{disabled:n,children:n?(0,l.jsx)(t.Loader,{title:o("Uploading..."),className:"text-primary-foreground"}):o("Update")})]})]})})]})]})}var H=r(43925),ee=r(39785),es=r(78939),er=r(18629),el=r(25429),et=r(74991),en=r(17110),ea=r(54995);let ei=S.z.object({profile:ea.K,firstName:S.z.string({required_error:"First name is required."}),lastName:S.z.string({required_error:"Last name is required."}),email:S.z.string({required_error:"Email is required."}),phone:S.z.string({required_error:"Phone is required."}),dateOfBirth:S.z.date({required_error:"Date of Birth is required."}),gender:S.z.string({required_error:"Gender is required"})});function ed(e){let{customer:s,onMutate:r,isLoading:a=!1}=e,[d,o]=(0,w.useTransition)(),{t:c}=(0,p.$G)(),u=(0,A.cI)({resolver:(0,C.F)(ei),defaultValues:{profile:void 0,firstName:"",lastName:"",email:"",phone:"",dateOfBirth:void 0,gender:""}}),x=(0,w.useCallback)(()=>{if(s){var e,r,l,t,n;u.reset({firstName:null==s?void 0:null===(e=s.customer)||void 0===e?void 0:e.firstName,lastName:null==s?void 0:null===(r=s.customer)||void 0===r?void 0:r.lastName,email:null==s?void 0:s.email,phone:null==s?void 0:null===(l=s.customer)||void 0===l?void 0:l.phone,dateOfBirth:new Date(null==s?void 0:null===(t=s.customer)||void 0===t?void 0:t.dob),gender:null==s?void 0:null===(n=s.customer)||void 0===n?void 0:n.gender})}},[a]);return(0,w.useEffect)(()=>x(),[x]),(0,l.jsx)(b.l0,{...u,children:(0,l.jsx)("form",{onSubmit:u.handleSubmit(e=>{o(async()=>{let l=await (0,en.n)(e,s.id);(null==l?void 0:l.status)?(r(),k.toast.success(l.message)):k.toast.error(c(l.message))})}),className:"rounded-xl border border-border bg-background",children:(0,l.jsxs)(n.Qd,{value:"PROFILE_INFORMATION",className:"border-none px-4 py-0",children:[(0,l.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,l.jsx)("p",{className:"text-base font-medium leading-[22px]",children:c("Profile")})}),(0,l.jsxs)(n.vF,{className:"flex flex-col gap-6 border-t px-1 py-4",children:[(0,l.jsx)(b.Wi,{control:u.control,name:"profile",render:e=>{var r;let{field:t}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:c("Profile picture")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(es.S,{defaultValue:(0,i.qR)(null==s?void 0:null===(r=s.customer)||void 0===r?void 0:r.profileImage),id:"documentFrontSideFile",onChange:e=>t.onChange(e),className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,l.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,l.jsx)(el.X,{}),(0,l.jsx)("p",{className:"text-sm font-normal text-primary",children:c("Upload photo")})]})})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,l.jsx)(b.Wi,{control:u.control,name:"firstName",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{className:"col-span-12 md:col-span-6",children:[(0,l.jsx)(b.lX,{children:"First name"}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:c("First name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:u.control,name:"lastName",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{className:"col-span-12 md:col-span-6",children:[(0,l.jsx)(b.lX,{children:"Last name"}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"text",placeholder:c("Last name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}})]}),(0,l.jsx)(b.Wi,{control:u.control,name:"email",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:c("Email")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(f.I,{type:"email",placeholder:c("Enter your email"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:u.control,name:"phone",render:e=>{var r;let{field:t}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:c("Phone")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(er.E,{value:null==s?void 0:null===(r=s.customer)||void 0===r?void 0:r.phone,onChange:t.onChange,inputClassName:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",onBlur:e=>{e?u.setError("phone",{type:"custom",message:c(e)}):u.clearErrors("phone")}})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:u.control,name:"dateOfBirth",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:c("Date of birth")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(ee.M,{...s})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{control:u.control,name:"gender",render:e=>{let{field:s}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:c("Gender")}),(0,l.jsx)(b.NI,{children:(0,l.jsxs)(et.E,{defaultValue:s.value,onValueChange:s.onChange,className:"flex",children:[(0,l.jsxs)(y.Z,{htmlFor:"GenderMale","data-selected":"male"===s.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,l.jsx)(et.m,{id:"GenderMale",value:"male",className:"absolute opacity-0"}),(0,l.jsx)("span",{children:c("Male")})]}),(0,l.jsxs)(y.Z,{htmlFor:"GenderFemale","data-selected":"female"===s.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,l.jsx)(et.m,{id:"GenderFemale",value:"female",className:"absolute opacity-0"}),(0,l.jsx)("span",{children:c("Female")})]})]})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,l.jsxs)(g.z,{disabled:d,children:[(0,l.jsx)(v.J,{condition:d,children:(0,l.jsx)(t.Loader,{className:"text-primary-foreground"})}),(0,l.jsxs)(v.J,{condition:!d,children:[c("Save"),(0,l.jsx)(z.Z,{size:20})]})]})})]})]})})})}function eo(e){let{title:s,status:r,icon:t,iconClass:n,statusClass:a,className:d}=e;return(0,l.jsxs)("div",{className:(0,i.ZP)("inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default",d),children:[(0,l.jsx)("div",{className:(0,i.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full bg-important/20",n),children:t({size:34,variant:"Bulk"})}),(0,l.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,l.jsxs)("span",{className:"block text-xs font-normal leading-4",children:[s," "]}),(0,l.jsx)("h6",{className:(0,i.ZP)("text-sm font-semibold leading-5",a),children:r})]})]})}function ec(){var e,s,r,v,j,g,b;let{t:f}=(0,p.$G)(),y=(0,h.useParams)(),{data:N,isLoading:I,mutate:C}=(0,a.d)("/admin/agents/".concat(y.agentId));if(I)return(0,l.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,l.jsx)(t.Loader,{})});let z=null==N?void 0:N.data;return(0,l.jsx)(n.UQ,{type:"multiple",defaultValue:["ADDRESS_INFORMATION","BALANCE","PROFILE_INFORMATION","ConvertAccountType","AGENT_INFORMATION","AgentStatus"],children:(0,l.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,l.jsxs)("div",{className:"grid w-full grid-cols-12 gap-4",children:[(0,l.jsx)(H.n,{}),(0,l.jsx)(eo,{title:f("Agent access"),icon:e=>(0,l.jsx)(d.Z,{...e}),status:f((null==z?void 0:z.status)==="verified"?"Granted":(null==z?void 0:z.status)==="failed"?"Not Granted":"Pending"),className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,l.jsx)(eo,{title:f("Account Status"),icon:e=>(0,l.jsx)(o.Z,{...e,variant:"Outline"}),statusClass:(null==z?void 0:null===(e=z.user)||void 0===e?void 0:e.status)?"text-success":"",status:f((null==z?void 0:null===(s=z.user)||void 0===s?void 0:s.status)?"Active":"Inactive"),iconClass:"bg-success/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,l.jsx)(eo,{title:f("KYC Status"),icon:e=>{var s;return(null==z?void 0:null===(s=z.user)||void 0===s?void 0:s.kycStatus)?(0,l.jsx)(c.Z,{className:(0,i.ZP)(e.className,"text-success"),...e}):(0,l.jsx)(u.Z,{className:(0,i.ZP)(e.className,"text-primary"),...e})},statusClass:(null==z?void 0:null===(r=z.user)||void 0===r?void 0:r.kycStatus)?"text-success":"text-primary",status:f((null==z?void 0:null===(v=z.user)||void 0===v?void 0:v.kycStatus)?"Verified":(null==z?void 0:null===(j=z.user)||void 0===j?void 0:j.kyc)?"Pending Verification":"Not Submitted Yet"),iconClass:(null==z?void 0:null===(g=z.user)||void 0===g?void 0:g.kycStatus)?"bg-success/20":"bg-primary/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,l.jsx)(eo,{title:f("Suspended"),icon:e=>(0,l.jsx)(x.Z,{...e}),statusClass:(null==z?void 0:z.isSuspend)?"text-danger":"",status:f((null==z?void 0:z.isSuspend)?"Yes":"No"),iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,l.jsx)(eo,{title:f("Recommended"),icon:e=>(0,l.jsx)(m.Z,{...e}),statusClass:"text-spacial-blue",status:f((null==z?void 0:z.isRecommended)?"Yes":"No"),iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"})]}),(0,l.jsx)(U,{wallets:null==z?void 0:null===(b=z.user)||void 0===b?void 0:b.wallets,onMutate:()=>C(N)}),(0,l.jsx)(ed,{isLoading:I,customer:null==z?void 0:z.user,onMutate:()=>C(N)}),(0,l.jsx)(D,{agentInfo:z,onMutate:()=>C(N)}),(0,l.jsx)(G,{customer:null==z?void 0:z.user,onMutate:()=>C(N)}),(0,l.jsx)(L,{id:null==z?void 0:z.userId,agentId:null==z?void 0:z.id,status:null==z?void 0:z.status,recommended:!!(null==z?void 0:z.isRecommended),suspended:!!(null==z?void 0:z.isSuspend),onMutate:()=>C()})]})})}},31117:function(e,s,r){"use strict";r.d(s,{d:function(){return n}});var l=r(79981),t=r(85323);let n=(e,s)=>(0,t.ZP)(e||null,e=>l.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...s})},502:function(e,s,r){"use strict";r.d(s,{Z:function(){return l}});class l{constructor(e){this.id=null==e?void 0:e.id,this.cardId=null==e?void 0:e.cardId,this.userId=null==e?void 0:e.userId,this.walletId=null==e?void 0:e.walletId,this.number=null==e?void 0:e.number,this.cvc=null==e?void 0:e.cvc,this.lastFour=null==e?void 0:e.lastFour,this.brand=null==e?void 0:e.brand,this.expMonth=null==e?void 0:e.expMonth,this.expYear=null==e?void 0:e.expYear,this.status=null==e?void 0:e.status,this.type=null==e?void 0:e.type,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.wallet=null==e?void 0:e.wallet,this.user=null==e?void 0:e.user}}},28315:function(e,s,r){"use strict";r.d(s,{F:function(){return l}});class l{format(e){let{currencySymbol:s,amountText:r}=this.formatter(e);return"".concat(r," ").concat(s)}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:s}=this.formatter(e);return s}constructor(e){var s;this.formatter=e=>{var s,r;let l=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),t=null!==(r=null===(s=l.formatToParts(e).find(e=>"currency"===e.type))||void 0===s?void 0:s.value)&&void 0!==r?r:this.code,n=l.format(e),a=n.substring(t.length).trim();return{currencyCode:this.code,currencySymbol:t,formattedAmount:n,amountText:a}},this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.code=null==e?void 0:e.code,this.logo=null!==(s=null==e?void 0:e.logo)&&void 0!==s?s:"",this.usdRate=null==e?void 0:e.usdRate,this.acceptApiRate=!!(null==e?void 0:e.acceptApiRate),this.isCrypto=!!(null==e?void 0:e.isCrypto),this.active=!!(null==e?void 0:e.active),this.metaData=null==e?void 0:e.metaData,this.minAmount=null==e?void 0:e.minAmount,this.kycLimit=null==e?void 0:e.kycLimit,this.maxAmount=null==e?void 0:e.maxAmount,this.dailyTransferAmount=null==e?void 0:e.dailyTransferAmount,this.dailyTransferLimit=null==e?void 0:e.dailyTransferLimit,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},54763:function(e,s,r){"use strict";r.d(s,{w:function(){return n}});var l=r(502),t=r(28315);class n{constructor(e){var s;this.id=null==e?void 0:e.id,this.walletId=null==e?void 0:e.walletId,this.logo=null==e?void 0:e.logo,this.userId=null==e?void 0:e.userId,this.balance=null==e?void 0:e.balance,this.defaultStatus=!!(null==e?void 0:e.default),this.pinDashboard=!!(null==e?void 0:e.pinDashboard),this.currencyId=null==e?void 0:e.currencyId,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.currency=new t.F(null==e?void 0:e.currency),this.cards=null==e?void 0:null===(s=e.cards)||void 0===s?void 0:s.map(e=>new l.Z(e))}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,31384,60627,85598,21564,46044,24450,227,14509,92971,95030,1744],function(){return e(e.s=30479)}),_N_E=e.O()}]);