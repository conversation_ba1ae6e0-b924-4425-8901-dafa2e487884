exports.id=8295,exports.ids=[8295],exports.modules={23607:(e,t,s)=>{Promise.resolve().then(s.bind(s,23767))},65922:(e,t,s)=>{Promise.resolve().then(s.bind(s,72408))},64339:(e,t,s)=>{Promise.resolve().then(s.bind(s,22208))},88042:(e,t,s)=>{Promise.resolve().then(s.bind(s,35424))},59875:(e,t,s)=>{Promise.resolve().then(s.bind(s,6624))},34019:(e,t,s)=>{Promise.resolve().then(s.bind(s,46314))},23767:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var a=s(10326),r=s(5158),l=s(92392),n=s(65304),i=s(77863),d=s(17577),c=s.n(d);function o({title:e,value:t,status:s,icon:r,iconClass:l,statusClass:d,className:c,isLoading:o}){return o?a.jsx(n.O,{className:(0,i.ZP)("",c)}):(0,a.jsxs)("div",{className:(0,i.ZP)("inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default",c),children:[a.jsx("div",{className:(0,i.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full bg-muted",l),children:r({size:34,variant:"Outline"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[a.jsx("h1",{children:t}),(0,a.jsxs)("span",{className:"block text-xs font-normal leading-4",children:[e," "]}),a.jsx("h6",{className:(0,i.ZP)("text-sm font-semibold leading-5",d),children:s})]})]})}var m=s(90799),x=s(29169),u=s(78564),f=s(40420),h=s(53105),p=s(26920),g=s(9155),j=s(41334),b=s(70012),y=s(28758),N=s(567),v=s(54033);function w({image:e,name:t,email:s,userId:r,className:l,isActive:n}){let{t:d}=(0,b.$G)();return(0,a.jsxs)("div",{className:(0,i.ZP)("inline-flex items-center gap-2",l),children:[(0,a.jsxs)(y.qE,{className:"size-8",children:[a.jsx(y.F$,{src:(0,i.qR)(e),alt:t}),a.jsx(y.Q5,{children:(0,v.v)(t)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h6",{className:"text-xs font-normal leading-4",children:t}),a.jsx("p",{className:"text-xs font-normal leading-4 text-secondary-text",children:s}),(0,a.jsxs)("p",{className:"text-xs font-normal leading-4 text-secondary-text",children:["#",r]})]}),a.jsx(N.C,{variant:n?"success":"secondary",children:d(n?"Active":"Inactive")})]})}var S=s(33071),C=s(8281),Z=s(4066);let k=(0,s(33265).default)(async()=>{},{loadableGenerated:{modules:["app\\(protected)\\@admin\\(dashboard)\\_components\\recent-transaction-graph.tsx -> react-apexcharts"]},ssr:!1}),D=e=>new Intl.NumberFormat("en-US",{notation:"compact",compactDisplay:"short",style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:5}).format(e);function P(){let{defaultCurrency:e}=(0,Z.T)(),t=new URLSearchParams;["deposit","withdraw","exchange"].forEach(e=>t.append("type[]",e)),t.append("currency",e);let{data:s,isLoading:r}=(0,m.d)(`/admin/transactions/chart/dashboard?${t.toString()}`),{t:n}=(0,b.$G)();if(r)return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(l.Loader,{})});if(!s?.data?.data)return null;let i=Object.keys(s?.data?.data),c=i.map(e=>s?.data?.data[e].deposit),o=i.map(e=>s?.data?.data[e].withdraw),x=i.map(e=>s?.data?.data[e].exchange),u=[{name:n("Deposits"),data:c},{name:n("Withdraw"),data:o},{name:n("Exchange"),data:x}];return(0,a.jsxs)(S.Zb,{className:"flex flex-col gap-4 rounded-xl px-6 pb-4 pt-6 shadow-default",children:[a.jsx(S.Ol,{className:"flex-row items-center justify-between gap-4 p-0",children:(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[a.jsx(S.ll,{className:"text-xl font-semibold leading-10",children:n("Recent transactions")}),a.jsx(S.SZ,{className:"hidden"}),a.jsx("div",{className:"text-xs font-normal leading-4 text-foreground",children:(0,a.jsxs)("ul",{className:"inline-flex gap-2",children:[(0,a.jsxs)("li",{className:"inline-flex items-center gap-1",children:[a.jsx("span",{className:"block size-2 rounded-full bg-spacial-green"}),n("Deposits")]}),(0,a.jsxs)("li",{className:"inline-flex items-center gap-1",children:[a.jsx("span",{className:"block size-2 rounded-full bg-spacial-red"}),n("Withdraw")]}),(0,a.jsxs)("li",{className:"inline-flex items-center gap-1",children:[a.jsx("span",{className:"block size-2 rounded-full bg-gray"}),n("Exchange")]})]})})]})}),a.jsx(C.Z,{className:"mb-1 mt-[5px]"}),a.jsx(S.aY,{className:"p-0",children:a.jsx(d.Suspense,{fallback:a.jsx(l.Loader,{}),children:a.jsx(k,{options:{chart:{id:"basic-bar",stacked:!1,offsetY:0,toolbar:{show:!1}},xaxis:{categories:i,labels:{style:{cssClass:"text-[11px] font-normal leading-4 fill-secondary-text"}},axisBorder:{show:!1},axisTicks:{show:!1}},dataLabels:{enabled:!1},legend:{show:!1},plotOptions:{bar:{horizontal:!1,borderRadius:10,borderRadiusApplication:"end"}},colors:["#107C10","#B10E1C","#718096"],forecastDataPoints:{count:1,fillOpacity:1},stroke:{show:!0,width:5,colors:["transparent"]},yaxis:{labels:{formatter:e=>D(e),style:{cssClass:"text-[11px] font-normal leading-4 fill-secondary-text"}}},grid:{borderColor:"#E0E0E0",padding:{bottom:0}}},series:u,type:"bar",width:"100%",height:"300px"})})})]})}var z=s(56140),A=s(66114),L=s(43173);let R=new i.F;function $({data:e,isLoading:t}){let[s,r]=c().useState([]),{t:l}=(0,b.$G)();return a.jsx(z.Z,{data:e?[...e.map(e=>new A.C(e))]:null,sorting:s,isLoading:t,setSorting:r,structure:[{id:"type",header:l("Type"),cell:({row:e})=>a.jsx("div",{children:a.jsx("p",{className:"text-xs font-bold text-secondary-text",children:(0,i.fl)(e.original?.type)})})},{id:"amount",header:l("Amount"),cell:({row:e})=>{let t=e.original;return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,L.EQ)(t).with({type:"exchange"},()=>R.format(t?.metaData?.amountFrom,t?.metaData?.currencyFrom)).with({type:"deposit"},()=>R.format(t.amount,t?.metaData?.currency)).otherwise(()=>R.format(t.amount,t?.from?.currency))})}},{id:"fee",header:l("Fee"),cell:({row:e})=>{let t=e.original;return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,L.EQ)(t).with({type:"exchange"},()=>R.format(t?.fee,t.metaData?.currency)).with({type:"deposit"},()=>R.format(t.fee,t.metaData?.currency)).otherwise(()=>R.format(t.fee,t.from?.currency))})}},{id:"total",header:l("After Processing"),cell:({row:e})=>{let t=e.original;return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,L.EQ)(t).with({type:"exchange"},()=>R.format(t.total,t.metaData?.currencyTo)).with({type:"deposit"},()=>R.format(t.total,t.metaData?.currency)).otherwise(()=>R.format(t.total,t.to?.currency))})}},{id:"status",header:l("Status"),cell:({row:e})=>{let t=e?.original;return"completed"===t.status?a.jsx(N.C,{variant:"success",children:(0,i.fl)(t.status)}):"failed"===t.status?a.jsx(N.C,{variant:"destructive",children:(0,i.fl)(t.status)}):a.jsx(N.C,{variant:"secondary",children:(0,i.fl)(t.status)})}}]})}var I=s(79210),E=s(75584),O=s(39974);function F(){let[e,t]=d.useState(2),{data:s,isLoading:r}=(0,E.Z)(`/admin/transactions/role/${e}`),{t:l}=(0,b.$G)();return a.jsx(I.mQ,{onValueChange:e=>t("customer"===e?2:3),defaultValue:"customer",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background px-4 py-6 shadow-default",children:[a.jsx("div",{className:"mb-4 flex items-center justify-between gap-4",children:(0,a.jsxs)(I.dr,{className:"h-12 max-w-[392px] p-1",children:[(0,a.jsxs)(I.SP,{value:"customer",className:"inline-flex h-10 gap-0.5 bg-accent text-sm font-semibold leading-5 data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[a.jsx(O.Z,{variant:"Bulk",size:24}),l("Customer")]}),(0,a.jsxs)(I.SP,{value:"merchant",className:"inline-flex h-10 gap-0.5 bg-accent text-sm font-semibold leading-5 data-[state=active]:text-foreground [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[a.jsx(g.Z,{variant:"Bulk",size:24}),l("Merchant")]})]})}),(0,a.jsxs)("div",{children:[a.jsx(I.nU,{value:"customer",children:a.jsx($,{data:s,isLoading:r})}),a.jsx(I.nU,{value:"merchant",children:a.jsx($,{data:s,isLoading:r})})]})]})})}function M(){let{data:e,isLoading:t}=(0,m.d)("/admin/users/total-count/dashboard"),{data:s,isLoading:n}=(0,m.d)("/admin/users?page=1&limit=10"),{t:i}=(0,b.$G)();return(0,a.jsxs)("main",{className:"p-4",children:[a.jsx(r.J,{condition:t,children:a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(l.Loader,{})})}),a.jsx(r.J,{condition:!t,children:(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[a.jsx(o,{icon:e=>a.jsx(x.Z,{...e}),title:i("Total Deposits"),value:t?0:e?.data?.deposit,status:`${e?.data?.depositAmount.toLocaleString()} USD`,className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-green-foreground text-spacial-green",statusClass:"text-spacial-green"}),a.jsx(o,{icon:e=>a.jsx(u.Z,{...e}),title:i("Total Withdraws"),value:t?0:e?.data?.withdraw,status:`${e?.data?.withdrawAmount.toLocaleString()} USD`,className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-red-foreground text-spacial-red",statusClass:"text-spacial-red"}),a.jsx(o,{icon:e=>a.jsx(f.Z,{...e}),title:i("Total Transfers"),value:t?0:e?.data?.transfer,status:`${e?.data?.transferAmount.toLocaleString()} USD`,className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-blue-foreground text-spacial-blue",statusClass:"text-spacial-blue"}),a.jsx(o,{icon:e=>a.jsx(h.Z,{...e}),title:i("Total Exchanges"),value:t?0:e?.data?.exchange,status:`${e?.data?.exchangeAmount.toLocaleString()} USD`,className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),a.jsx(o,{icon:e=>a.jsx(p.Z,{...e}),title:i("Customers"),value:t?0:e?.data?.customer?.total,status:t?"0 New":`${e?.data?.customer?.total} New`,className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",statusClass:"text-spacial-green"}),a.jsx(o,{icon:e=>a.jsx(g.Z,{...e}),title:i("Merchants"),value:t?0:e?.data?.merchant?.total,status:t?"0 New":`${e?.data?.merchant?.total} New`,className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",statusClass:"text-spacial-green"}),a.jsx(o,{icon:e=>a.jsx(j.Z,{...e}),title:i("Agents"),value:t?0:e?.data?.agent?.total,status:t?"0 New":`${e?.data?.agent?.total} New`,className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",statusClass:"text-spacial-red"})]})}),(0,a.jsxs)("div",{className:"flex flex-col items-start gap-4 py-4 xl:flex-row",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 xl:flex-1",children:[a.jsx(P,{}),a.jsx(F,{})]}),a.jsx("div",{className:"w-full xl:w-[350px]",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4 rounded-xl bg-background p-6 shadow-default",children:[a.jsx("h3",{children:i("Recently registered")}),a.jsx("div",{className:"flex flex-col gap-4",children:n?null:s?.data?.data?.map(e=>1===e.roleId?null:a.jsx(w,{name:e?.customer?.name,email:e?.email,userId:e?.id,isActive:e?.status,image:e?.customer?.profileImage},e.id))})]})})]})]})}},72408:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D});var a=s(10326),r=s(5158),l=s(16150),n=s(38428),i=s(33071),d=s(34474),c=s(8281),o=s(65304),m=s(19395),x=s(4066),u=s(90799),f=s(60814),h=s(71305),p=s(44284),g=s(29169),j=s(78564),b=s(88589),y=s(90434),N=s(17577),v=s(70012),w=s(92392);let S=(0,s(33265).default)(async()=>{},{loadableGenerated:{modules:["app\\(protected)\\@agent\\(dashboard)\\_components\\payment-report-chart.tsx -> react-apexcharts"]},ssr:!1});function C({data:e=[],currencyCode:t,isLoading:s=!0}){let{t:r}=(0,v.$G)(),l=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],n=e=>new Intl.NumberFormat("en-US",{notation:"compact",compactDisplay:"short",style:"currency",currency:t,currencyDisplay:"code",minimumFractionDigits:2,maximumFractionDigits:5}).format(e),i={chart:{id:"payment-report-bar",stacked:!1,offsetY:0,toolbar:{show:!1}},xaxis:{type:"category",categories:l,labels:{style:{cssClass:"text-[11px] font-normal leading-4 fill-secondary-text"}},axisBorder:{show:!1},axisTicks:{show:!1}},dataLabels:{enabled:!1},colors:l.map(e=>e===(0,h.WU)(new Date,"MMM")?"#EE792B":"#718096"),legend:{show:!1},plotOptions:{bar:{distributed:!0,borderRadius:10,borderRadiusApplication:"end"}},yaxis:{labels:{formatter:e=>n(e),style:{cssClass:"text-[11px] font-normal leading-4 fill-secondary-text"}}},grid:{borderColor:"#E0E0E0",padding:{bottom:0}}},d=[{name:r("Total Payments"),data:e?.map(e=>e.total)}];return s?a.jsx("div",{children:r("Loading...")}):a.jsx("div",{children:a.jsx(N.Suspense,{fallback:a.jsx(w.Loader,{}),children:a.jsx(S,{options:i,series:d,type:"bar",width:"100%",height:"200px"})})})}var Z=s(77863);function k({isLoading:e,title:t,value:s,currency:l,Icon:n,iconVariant:i="Bulk",iconContainerClass:d}){let{t:c}=(0,v.$G)();return a.jsx("div",{className:"w-full rounded-xl bg-background p-6 shadow-default md:max-w-[376px]",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-4",children:[a.jsx("div",{className:(0,Z.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full bg-important/10",d),children:a.jsx(n,{size:"32",variant:i})}),(0,a.jsxs)("div",{children:[a.jsx("h6",{className:"mb-1 font-semibold text-warning",children:c("Pending")}),a.jsx("p",{className:"text-xs font-normal leading-4",children:c(t)})]})]}),a.jsx(r.J,{condition:e,children:a.jsx("div",{className:"text-4xl font-semibold leading-[32px]",children:"00"})}),a.jsx(r.J,{condition:!e,children:(0,a.jsxs)("div",{className:"text-xl font-semibold",children:[s,l?a.jsx("span",{className:"pl-1.5 text-xl",children:l}):null]})})]})})}function D(){let{defaultCurrency:e}=(0,x.T)(),{t}=(0,v.$G)(),[s,w]=(0,N.useState)(e),{wallets:S,isLoading:Z}=(0,f.r)(),{auth:D,isLoading:P}=(0,m.a)(),{data:z,isLoading:A}=(0,u.d)(`/transactions/chart/moonthly-report?currency=${s}`),{data:L,isLoading:R}=(0,u.d)("/transactions/counts/total?status=pending"),{data:$,isLoading:I}=(0,u.d)(D?`/commissions/total-pending/${D?.id}`:""),E=new Intl.NumberFormat("en-US",{minimumIntegerDigits:2});return(0,a.jsxs)("main",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-end gap-4",children:[a.jsx(r.J,{condition:P,children:a.jsx(o.O,{className:"h-[200px] w-[350px]"})}),a.jsx(l.X,{isVerified:!!D?.getKYCStatus(),documentStatus:D?.kyc?"submitted":"not submitted"}),Z?a.jsx(o.O,{className:"h-[200px] w-[350px]"}):S?.map(e=>e.pinDashboard&&a.jsx(n.P,{logo:e?.logo,title:e?.currency.code,balance:e.balance,currency:e?.currency.code,walletId:e.id,card:e?.cards?.[0]},e.id)),(0,a.jsxs)(y.default,{href:"/wallets",prefetch:!1,className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-700 transition duration-300 ease-out hover:text-secondary-800",children:[a.jsx("span",{children:t("Show all wallets")}),a.jsx(p.Z,{size:12})]})]}),(0,a.jsxs)("div",{className:"flex flex-col flex-wrap gap-4 lg:flex-row",children:[a.jsx("div",{className:"flex flex-1 flex-col gap-4",children:(0,a.jsxs)(i.Zb,{className:"flex flex-col gap-4 px-6 pb-4 pt-6 shadow-default",children:[(0,a.jsxs)(i.Ol,{className:"flex-row items-center justify-between gap-4 p-0",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)(i.SZ,{className:"text-xs font-normal leading-4 text-foreground",children:[t("Total transaction /")," ",(0,h.WU)(new Date,"MMMM")]}),a.jsx("div",{className:"flex h-10 items-center",children:(0,a.jsxs)(i.ll,{className:"text-xl font-semibold leading-10",children:[(z?.data?.data?.find(({month:e})=>E.format(e)?.toString()===h.WU(new Date,"MM"))?.total??0)?.toFixed(2),` ${s}`]})})]}),(0,a.jsxs)(d.Ph,{value:s,onValueChange:w,children:[a.jsx(d.i4,{className:"h-8 w-[100px] text-sm font-normal leading-5 [&>svg]:size-4",children:a.jsx(d.ki,{placeholder:e})}),a.jsx(d.Bw,{children:S?.map(e=>a.jsx(d.Ql,{value:e?.currency.code,children:e?.currency.code},e.id))})]})]}),a.jsx(c.Z,{className:"mb-1 mt-[5px]"}),a.jsx(i.aY,{className:"p-0",children:a.jsx(C,{data:z?.data?.data??[],currencyCode:s,isLoading:A})})]})}),(0,a.jsxs)("div",{className:"grid w-full grid-cols-1 gap-4 lg:max-w-[350px]",children:[a.jsx(k,{title:t("Deposit request"),Icon:g.Z,iconContainerClass:"bg-spacial-green-foreground",iconVariant:"Outline",value:L?.data?.deposit_request,isLoading:R}),a.jsx(k,{title:t("Withdraw request"),Icon:j.Z,value:L?.data?.withdraw_request,isLoading:R}),a.jsx(k,{title:t("Commission"),Icon:b.Z,value:$?.data?.total,isLoading:I,currency:$?.data?.currency})]})]})]})}},22208:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(10326),r=s(90772),l=s(79210),n=s(75584),i=s(44284),d=s(90434),c=s(70012),o=s(56140),m=s(567),x=s(77863),u=s(17577),f=s.n(u),h=s(43173);let p=new x.F;function g({data:e,isLoading:t}){let[s,r]=f().useState([]),{t:l}=(0,c.$G)();return a.jsx(o.Z,{data:e,sorting:s,setSorting:r,isLoading:t,structure:[{id:"type",header:l("Type"),cell:({row:e})=>{let t=e.original;return t.type?a.jsx("div",{children:a.jsx("p",{className:"text-xs font-bold text-foreground",children:(0,x.fl)(t.type)})}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"amount",header:l("Amount"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return(0,h.EQ)(t).with({type:"exchange"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(r?.amountFrom,r?.currencyFrom)})).with({type:"deposit"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t.amount,r?.currency)})).otherwise(()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t.amount,s?.currency)}))}},{id:"fee",header:l("Fee"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return(0,h.EQ)(t).with({type:"exchange"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t?.fee,r?.currency)})).with({type:"deposit"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t.fee,r?.currency)})).otherwise(()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t.fee,s?.currency)}))}},{id:"after_processing",header:l("After Processing"),cell:({row:e})=>{let t=e?.original,s=t?.to&&JSON.parse(t.to),r=t?.metaData&&JSON.parse(t?.metaData);return(0,h.EQ)(t).with({type:"exchange"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t.total,r?.currencyTo)})).with({type:"deposit"},()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t.total,r?.currency)})).otherwise(()=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:p.format(t.total,s?.currency)}))}},{id:"status",header:l("Status"),cell:({row:e})=>{let t=e?.original;return t.status?"completed"===t.status?a.jsx(m.C,{variant:"success",children:(0,x.fl)(t.status)}):"pending"===t.status?a.jsx(m.C,{variant:"secondary",className:"bg-muted",children:(0,x.fl)(t.status)}):"failed"===t.status?a.jsx(m.C,{variant:"destructive",children:(0,x.fl)(t.status)}):a.jsx(m.C,{variant:"secondary",className:"bg-muted",children:l("Pending")}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}}]})}function j(){let{t:e}=(0,c.$G)(),{data:t,isLoading:s}=(0,n.Z)("/transactions?page=1&limit=10");return a.jsx(l.mQ,{defaultValue:"personal",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background px-4 py-6 shadow-default",children:[a.jsx("div",{className:"mb-4 flex items-center justify-between gap-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("p",{className:"font-medium text-foreground",children:e("Transaction History")}),a.jsx(r.z,{variant:"secondary",size:"sm",className:"px-2.5",asChild:!0,children:(0,a.jsxs)(d.default,{href:"/transaction-history",children:[a.jsx("span",{children:e("See all")}),a.jsx(i.Z,{size:20})]})})]})}),a.jsx(l.nU,{value:"personal",children:a.jsx(g,{data:t,isLoading:s})})]})})}},35424:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var a=s(10326),r=s(5158),l=s(63761),n=s(16150),i=s(92392),d=s(38428),c=s(15495),o=s(28758),m=s(567),x=s(90772),u=s(30458),f=s(65685),h=s(8281),p=s(65304),g=s(69519),j=s(81431),b=s(19395),y=s(60814),N=s(49547),v=s(77863),w=s(54033),S=s(44284),C=s(3001),Z=s(29169),k=s(44221),D=s(7310),P=s(90434),z=s(17577),A=s(70012),L=s(85999),R=s(84455),$=s(53313),I=s(31048);function E({id:e,checked:t=!1,onSelect:s,name:r,avatar:l,email:n}){let{t:i}=(0,A.$G)();return(0,a.jsxs)("div",{className:"inline-flex w-full items-center justify-between gap-2.5",children:[(0,a.jsxs)("div",{className:"inline-flex gap-2.5",children:[(0,a.jsxs)(o.qE,{className:"h-8 w-8",children:[a.jsx(o.F$,{src:l,alt:r}),a.jsx(o.Q5,{children:(0,w.v)(r)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm text-foreground",children:r}),a.jsx("p",{className:"text-xs",children:n})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1.5",children:[a.jsx($.X,{id:e,className:"size-4 border-foreground/40 hover:border-primary data-[state=checked]:border-primary",checked:!!t,onCheckedChange:()=>s(e)}),a.jsx(I.Z,{htmlFor:e,className:"cursor-pointer p-0 text-sm font-normal leading-5 text-foreground",children:i(t?"Added":"Add")})]})]})}function O({tableSlot:e}){let[t,s]=z.useState(""),{auth:$,isLoading:I}=(0,b.a)(),{wallets:O,isLoading:F,mutate:M}=(0,y.r)(),{t:Q}=(0,A.$G)(),{contacts:U,isLoading:J,mutate:T}=(0,j.t)(`/contacts?search=${t}`),{data:q}=(0,R.ZP)("/customers/referred-users",e=>N.Z.get(e)),B=e=>Array.isArray(e)?e.filter(e=>!!e.quickSend):[];console.log(O);let Y=(e,t)=>{B(U)?.length>3&&"add"===t?L.toast.error(Q("You already added 4 contact into quick send")):L.toast.promise((0,g.M)(e,t),{loading:Q("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return T(),e.message},error:e=>e.message})};return console.log(O),(0,a.jsxs)("main",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-end gap-y-4 md:gap-4",children:[a.jsx(r.J,{condition:I,children:a.jsx(p.O,{className:"h-[200px] w-[350px]"})}),a.jsx(n.X,{isVerified:!!$?.getKYCStatus(),documentStatus:$?.kyc?"submitted":"not submitted"}),F?a.jsx(p.O,{className:"h-[200px] w-[350px]"}):O?.map(e=>e.pinDashboard&&a.jsx(d.P,{title:e?.currency.code,balance:e.balance,currency:e?.currency.code,walletId:e.id,card:e?.cards?.[0],onMutate:M},e.id)),(0,a.jsxs)(P.default,{href:"/wallets",prefetch:!1,className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-700 transition duration-300 ease-out hover:text-primary hover:underline",children:[a.jsx("span",{children:Q("Show all wallets")}),a.jsx(S.Z,{size:12})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-0 gap-y-4 md:gap-4 xl:flex-row",children:[a.jsx("div",{className:"flex-1",children:a.jsx(z.Suspense,{children:e})}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap gap-4 md:flex-row xl:max-w-[350px] xl:flex-col",children:[(0,a.jsxs)(u.dy,{direction:"right",children:[(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-6 shadow-default",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[a.jsx("p",{className:"font-semibold text-foreground",children:Q("Quick Send")}),a.jsx(u.Qz,{children:a.jsx(x.z,{type:"button",size:"sm",variant:"ghost",children:a.jsx(C.Z,{size:20})})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-[15px]",children:[J&&[void 0,void 0,void 0,void 0].map((e,t)=>a.jsx(p.O,{className:"size-10 rounded-full sm:size-12"},t)),U?B(U)?.map(e=>a.jsx(P.default,{href:`/transfer?email=${e?.contact?.email}`,children:a.jsxs(o.qE,{className:"size-10 sm:size-12",children:[a.jsx(o.F$,{src:v.qR(e.contact.customer.profileImage),alt:e.contact.customer.name}),a.jsx(o.Q5,{children:w.v(e.contact.customer.name)})]})},e.id)):null,a.jsx(u.Qz,{className:"flex size-10 items-center justify-center rounded-full border-2 border-btn-outline-border p-0 sm:size-12",children:a.jsx(Z.Z,{size:20})})]})]}),(0,a.jsxs)(u.sc,{className:"inset-x-auto inset-y-0 bottom-auto left-auto right-0 top-0 m-0 flex h-full w-[95%] flex-col rounded-t-none bg-white px-0 py-8 md:w-[400px]",children:[a.jsx(u.iI,{className:"flex items-center justify-between gap-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2.5",children:[a.jsx(x.z,{variant:"outline",size:"icon",className:"h-10 w-10 rounded-lg",asChild:!0,children:a.jsx(u.uh,{children:a.jsx(k.Z,{})})}),a.jsx("span",{className:"inline-block text-base font-semibold leading-[22px]",children:Q("Quick Send")})]})}),a.jsx(u.u6,{className:"hidden"}),(0,a.jsxs)("div",{className:"flex h-full w-full flex-1 flex-col p-0",children:[a.jsx("div",{className:"flex flex-col px-6 py-4",children:a.jsx(l.R,{value:t,onChange:e=>s(e.target.value),iconPlacement:"end",className:"h-10 rounded-lg bg-accent",placeholder:Q("Search...")})}),(0,a.jsxs)(f.x,{className:"flex-1 px-6 pb-8",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col items-stretch gap-4",children:[J&&a.jsx(i.Loader,{className:"mx-2"}),U?.map(e=>e.quickSend?a.jsx(E,{id:e.id,name:e.contact.customer.name,email:e.contact.email,checked:!0,onSelect:e=>Y(e,"remove")},e.id):null)]}),a.jsx(h.Z,{className:"my-4"}),(0,a.jsxs)("div",{className:"flex w-full flex-col items-stretch gap-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-2.5",children:[a.jsx("h5",{className:"text-base font-semibold leading-[22px] text-foreground",children:Q("Contacts")}),a.jsx("p",{className:"text-xs font-normal text-secondary-text",children:Q("Select up to 5 contacts to add them in the Quick Send list.")})]}),J&&a.jsx(i.Loader,{className:"mx-2"}),U?.map(e=>e.quickSend?null:a.jsx(E,{id:e.id,name:e.contact.customer.name,email:e.contact.email,onSelect:e=>Y(e,"add")},e.id))]})]})]})]})]}),a.jsx(c.Y,{}),(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-6 shadow-default",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[a.jsx("p",{className:"font-semibold text-foreground",children:Q("Refer a friend")}),a.jsx(m.C,{variant:"secondary",className:"flex h-6 w-6 items-center justify-center bg-muted",children:q?.data?.referralUsers?.length?q.data.referralUsers.length:"0"})]}),a.jsx("p",{className:"mb-6 text-sm text-secondary-text",children:Q("Share this referral link to your friends and earn money.")}),a.jsx("div",{className:"mb-2 line-clamp-1 flex h-12 w-full items-center text-ellipsis whitespace-nowrap rounded-[8px] bg-input px-3",children:$?.getReferralLink()}),(0,a.jsxs)(x.z,{className:"w-full",onClick:()=>(0,v.Fp)($?.getReferralLink()??""),children:[a.jsx(D.Z,{size:"24"}),a.jsx("span",{children:Q("Copy link")})]})]})]})]})]})}},6624:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(10326),r=s(79210),l=s(9155),n=s(39974),i=s(70012),d=s(56140),c=s(567),o=s(75584),m=s(77863),x=s(35047),u=s(17577),f=s(43173);let h=new m.F;function p(){let{t:e}=(0,i.$G)(),t=(0,x.useSearchParams)(),[s,r]=(0,u.useState)([]),{data:l,isLoading:n}=(0,o.Z)(`/payments/merchants/list-view?${t.toString()}`);return a.jsx(d.Z,{data:l,sorting:s,setSorting:r,isLoading:n,structure:[{id:"description",header:e("Description"),cell:({row:e})=>{let t=e?.original,s=t?.type;return a.jsx("div",{children:a.jsx("p",{className:"text-xs font-bold text-secondary-text",children:(0,m.fl)(s)})})}},{id:"amount",header:e("Amount"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,f.EQ)(t).with({type:"exchange"},()=>h.format(r?.amountFrom,r?.currencyFrom)).with({type:"deposit"},()=>h.format(t.amount,r?.currency)).otherwise(()=>h.format(t.amount,s?.currency))})}},{id:"fee",header:e("Fee"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,f.EQ)(t).with({type:"exchange"},()=>h.format(t?.fee,r?.currency)).with({type:"deposit"},()=>h.format(t.fee,r?.currency)).otherwise(()=>h.format(t.fee,s?.currency))})}},{id:"afterProcessing",accessorKey:"afterProcessing",header:e("After Processing"),cell:({row:e})=>{let t=e?.original,s=t?.to&&JSON.parse(t.to),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,f.EQ)(t).with({type:"exchange"},()=>h.format(t.total,r?.currencyTo)).with({type:"deposit"},()=>h.format(t.total,r?.currency)).otherwise(()=>h.format(t.total,s?.currency))})}},{id:"status",header:e("Status"),cell:({row:t})=>{let s=t?.original;return"completed"===s.status?a.jsx(c.C,{variant:"success",children:(0,m.fl)(e(s.status))}):"failed"===s.status?a.jsx(c.C,{variant:"destructive",children:(0,m.fl)(e(s.status))}):a.jsx(c.C,{variant:"secondary",children:(0,m.fl)(e(s.status))})}}]})}let g=new m.F;function j(){let{t:e}=(0,i.$G)(),t=(0,x.useSearchParams)(),[s,r]=(0,u.useState)([]),{data:l,isLoading:n}=(0,o.Z)(`/transactions/?${t.toString()}`);return a.jsx(d.Z,{data:l,sorting:s,setSorting:r,isLoading:n,structure:[{id:"description",header:e("Description"),cell:({row:e})=>{let t=e?.original,s=t?.type;return a.jsx("div",{children:a.jsx("p",{className:"text-xs font-bold text-secondary-text",children:(0,m.fl)(s)})})}},{id:"amount",header:e("Amount"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,f.EQ)(t).with({type:"exchange"},()=>g.format(r?.amountFrom,r?.currencyFrom)).with({type:"deposit"},()=>g.format(t.amount,r?.currency)).otherwise(()=>g.format(t.amount,s?.currency))})}},{id:"fee",header:e("Fee"),cell:({row:e})=>{let t=e?.original,s=t?.from&&JSON.parse(t.from),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,f.EQ)(t).with({type:"exchange"},()=>g.format(t?.fee,r?.currency)).with({type:"deposit"},()=>g.format(t.fee,r?.currency)).otherwise(()=>g.format(t.fee,s?.currency))})}},{id:"afterProcessing",accessorKey:"afterProcessing",header:e("After Processing"),cell:({row:e})=>{let t=e?.original,s=t?.to&&JSON.parse(t.to),r=t?.metaData&&JSON.parse(t?.metaData);return a.jsx("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,f.EQ)(t).with({type:"exchange"},()=>g.format(t.total,r?.currencyTo)).with({type:"deposit"},()=>g.format(t.total,r?.currency)).otherwise(()=>g.format(t.total,s?.currency))})}},{id:"status",header:e("Status"),cell:({row:t})=>{let s=t?.original;return"completed"===s.status?a.jsx(c.C,{variant:"success",children:(0,m.fl)(e(s.status))}):"failed"===s.status?a.jsx(c.C,{variant:"destructive",children:(0,m.fl)(e(s.status))}):a.jsx(c.C,{variant:"secondary",children:(0,m.fl)(e(s.status))})}}]})}function b(){let{t:e}=(0,i.$G)();return a.jsx(r.mQ,{defaultValue:"merchant",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background px-4 py-6 shadow-default",children:[a.jsx("div",{className:"mb-4 flex items-center justify-between gap-4",children:(0,a.jsxs)(r.dr,{className:"h-12 max-w-[392px] p-1",children:[(0,a.jsxs)(r.SP,{value:"merchant",className:"inline-flex h-10 gap-0.5 bg-accent text-sm font-semibold leading-5 data-[state=active]:text-foreground [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[a.jsx(l.Z,{variant:"Bulk",size:24}),e("Merchant")]}),(0,a.jsxs)(r.SP,{value:"personal",className:"inline-flex h-10 gap-0.5 bg-accent text-sm font-semibold leading-5 data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[a.jsx(n.Z,{variant:"Bulk",size:24}),e("Personal")]})]})}),(0,a.jsxs)("div",{children:[a.jsx(r.nU,{value:"personal",children:a.jsx(j,{})}),a.jsx(r.nU,{value:"merchant",children:a.jsx(p,{})})]})]})})}},46314:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>G});var a=s(10326),r=s(5158),l=s(63761),n=s(16150),i=s(92392),d=s(38428),c=s(15495),o=s(28758),m=s(567),x=s(90772),u=s(33071),f=s(30458),h=s(65685),p=s(34474),g=s(8281),j=s(65304),b=s(69519),y=s(81431),N=s(19395),v=s(4066),w=s(90799),S=s(60814),C=s(77863),Z=s(54033),k=s(71305),D=s(39642),P=s(44284),z=s(81770),A=s(3001),L=s(29169),R=s(44221),$=s(88010),I=s(7310),E=s(90434),O=s(97885),F=s(17577),M=s(70012),Q=s(85999),U=s(61625);let J=(0,s(33265).default)(async()=>{},{loadableGenerated:{modules:["app\\(protected)\\@merchant\\(dashboard)\\_components\\payment-report-chart.tsx -> react-apexcharts"]},ssr:!1});function T({data:e=[],currencyCode:t,isLoading:s=!0}){let{t:r}=(0,M.$G)(),l=["Jan","Fab","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],n=e=>new Intl.NumberFormat("en-US",{notation:"compact",compactDisplay:"short",style:"currency",currency:t,currencyDisplay:"code",minimumFractionDigits:2,maximumFractionDigits:5}).format(e),d={chart:{id:"basic-bar",stacked:!1,offsetY:0,toolbar:{show:!1}},xaxis:{categories:l,labels:{style:{cssClass:"text-[11px] font-normal leading-4 fill-secondary-text"}},axisBorder:{show:!1},axisTicks:{show:!1}},dataLabels:{enabled:!1},colors:l.map(e=>e===(0,k.WU)(new Date,"MMM")?"#EE792B":"#718096"),legend:{show:!1},plotOptions:{bar:{distributed:!0,borderRadius:10,borderRadiusApplication:"end"}},yaxis:{labels:{formatter:e=>n(e),style:{cssClass:"text-[11px] font-normal leading-4 fill-secondary-text"}}},grid:{borderColor:"#E0E0E0",padding:{bottom:0}}},c=[{name:r("Total Payments"),data:e?.map(e=>e.total)}];return s?a.jsx("div",{children:r("Loading...")}):a.jsx("div",{children:a.jsx(F.Suspense,{fallback:a.jsx(i.Loader,{}),children:a.jsx(J,{options:d,series:c,type:"bar",width:"100%",height:"200px"})})})}var q=s(53313),B=s(31048);function Y({id:e,checked:t=!1,onSelect:s,name:r,avatar:l,email:n}){let{t:i}=(0,M.$G)();return(0,a.jsxs)("div",{className:"inline-flex w-full items-center justify-between gap-2.5",children:[(0,a.jsxs)("div",{className:"inline-flex gap-2.5",children:[(0,a.jsxs)(o.qE,{className:"h-8 w-8",children:[a.jsx(o.F$,{src:l,alt:r}),a.jsx(o.Q5,{children:(0,Z.v)(r)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm text-foreground",children:r}),a.jsx("p",{className:"text-xs",children:n})]})]}),(0,a.jsxs)("div",{className:"inline-flex items-center gap-1",children:[a.jsx(q.X,{id:e,className:"size-4 border-foreground/40 hover:border-primary data-[state=checked]:border-primary",checked:!!t,onCheckedChange:()=>s(e)}),a.jsx(B.Z,{htmlFor:e,className:"-mt-1 cursor-pointer p-0 text-sm font-normal leading-5 text-foreground",children:i(t?"Added":"Add")})]})]})}function G({tableSlot:e}){let[t,s]=F.useState(""),{wallets:J,isLoading:q}=(0,S.r)(),{contacts:B,isLoading:G,mutate:V}=(0,y.t)(`/contacts?search=${t}`),{auth:W,isLoading:_}=(0,N.a)(),{siteUrl:H,defaultCurrency:X}=(0,v.T)(),[K,ee]=F.useState(X),{t:et}=(0,M.$G)(),{data:es,isLoading:ea}=(0,w.d)(`/transactions/chart/moonthly-report?currency=${K}`),{data:er,isLoading:el}=(0,w.d)(`/transactions/counts/total?data=${(0,k.WU)(new Date,"yyyy-MM-dd")}`),{data:en}=(0,w.d)("/customers/referred-users"),ei=e=>Array.isArray(e)?e.filter(e=>!!e.quickSend):[],ed=(e,t)=>{ei(B)?.length>3&&"add"===t?Q.toast.error("You already added 4 contact into quick send"):Q.toast.promise((0,b.M)(e,t),{loading:et("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return V(),e.message},error:e=>e.message})},ec=()=>{let e=document.getElementById("merchant-qr-code");if(e)return(0,D.Z)(e,{onclone:e=>{let t=e.getElementById("merchant-qr-code");t&&(t.style.display="block")}})},eo=async()=>{let e=await ec();if(!e)throw Error("<canvas> not found in DOM");let t=e.toDataURL("image/png").replace("image/png","image/octet-stream"),s=document.createElement("a");s.href=t,s.download="QR code.png",document.body.appendChild(s),s.click(),document.body.removeChild(s)},em=new Intl.NumberFormat("en-US",{minimumIntegerDigits:2});return(0,a.jsxs)("main",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-end gap-y-4 md:gap-4",children:[a.jsx(r.J,{condition:_,children:a.jsx(j.O,{className:"h-[200px] w-[350px]"})}),a.jsx(n.X,{isVerified:!!W?.getKYCStatus(),documentStatus:W?.kyc?"submitted":"not submitted"}),q?a.jsx(j.O,{className:"h-[200px] w-[350px]"}):J?.map(e=>e.pinDashboard&&a.jsx(d.P,{title:e?.currency.code,balance:e.balance,currency:e?.currency.code,walletId:e.id,card:e?.cards?.[0]},e.id)),(0,a.jsxs)(E.default,{href:"/wallets",prefetch:!1,className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-700 transition duration-300 ease-out hover:text-secondary-800",children:[a.jsx("span",{children:et("Show all wallets")}),a.jsx(P.Z,{size:12})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-0 gap-y-4 md:gap-x-4 xl:flex-row",children:[(0,a.jsxs)("div",{className:"flex flex-1 flex-col gap-4",children:[(0,a.jsxs)(u.Zb,{className:"flex flex-col gap-4 px-6 pb-4 pt-6 shadow-default",children:[(0,a.jsxs)(u.Ol,{className:"flex-row items-center justify-between gap-4 p-0",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)(u.SZ,{className:"text-xs font-normal leading-4 text-foreground",children:[et("Total merchant payments received")," /"," ",(0,k.WU)(new Date,"MMMM")]}),a.jsx("div",{className:"flex h-10 items-center",children:(0,a.jsxs)(u.ll,{className:"text-xl font-semibold leading-10",children:[(es?.data?.data?.find(({month:e})=>em.format(e)?.toString()===k.WU(new Date,"MM"))?.total??0)?.toFixed(2),` ${K}`]})})]}),(0,a.jsxs)(p.Ph,{value:K,onValueChange:ee,children:[a.jsx(p.i4,{className:"data-[placeholder]:text-placeholder h-8 w-[100px] bg-accent text-sm font-normal leading-5 [&>svg]:size-4",children:a.jsx(p.ki,{placeholder:X})}),a.jsx(p.Bw,{children:J?.map(e=>a.jsx(p.Ql,{value:e?.currency.code,children:e?.currency.code},e.id))})]})]}),a.jsx(g.Z,{className:"mb-1 mt-[5px]"}),a.jsx(u.aY,{className:"p-0",children:a.jsx(T,{data:es?.data?.data??[],currencyCode:K,isLoading:ea})})]}),a.jsx(F.Suspense,{children:e})]}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap gap-4 md:flex-row xl:max-w-[350px] xl:flex-col",children:[a.jsx("div",{className:"flex flex-1 items-center rounded-xl bg-background p-6 shadow-default md:flex-initial",children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-4",children:[a.jsx("div",{className:"flex h-[54px] w-[54px] items-center justify-center rounded-full bg-spacial-green-foreground text-spacial-green",children:a.jsx(z.Z,{size:"32"})}),(0,a.jsxs)("div",{children:[a.jsx("h6",{className:"mb-1 font-semibold text-spacial-green",children:et("Received")}),a.jsx("p",{className:"text-xs font-normal leading-4",children:et("Total payments today")})]})]}),a.jsx("div",{className:"text-5xl font-semibold leading-[32px]",children:em.format(el?0:er?.data?.payment)})]})}),(0,a.jsxs)(f.dy,{direction:"right",children:[(0,a.jsxs)("div",{className:"flex-1 rounded-xl bg-background p-6 shadow-default xl:flex-initial",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[a.jsx("p",{className:"font-semibold text-foreground",children:et("Quick Send")}),a.jsx(f.Qz,{children:a.jsx(A.Z,{size:20})})]}),(0,a.jsxs)("div",{className:"flex gap-[15px]",children:[G&&[void 0,void 0,void 0,void 0].map((e,t)=>a.jsx(j.O,{className:"size-10 rounded-full sm:size-12"},t)),B?ei(B)?.map(e=>a.jsx(E.default,{href:`/transfer?email=${e?.contact?.email}`,children:a.jsxs(o.qE,{className:"size-10 sm:size-12",children:[a.jsx(o.F$,{src:C.qR(e.contact.customer.profileImage),alt:e.contact.customer.name}),a.jsx(o.Q5,{children:Z.v(e.contact.customer.name)})]})},e.id)):null,a.jsx(f.Qz,{className:"flex size-10 items-center justify-center rounded-full border-2 border-border p-0 sm:size-12",children:a.jsx(L.Z,{size:20})})]})]}),(0,a.jsxs)(f.sc,{className:"inset-x-auto inset-y-0 bottom-auto left-auto right-0 top-0 m-0 flex h-full w-[95%] flex-col rounded-t-none bg-white px-0 py-8 md:w-[400px]",children:[a.jsx(f.iI,{className:"flex items-center justify-between gap-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2.5",children:[a.jsx(x.z,{variant:"outline",size:"icon",type:"button",className:"h-10 w-10 rounded-lg",asChild:!0,children:a.jsx(f.uh,{children:a.jsx(R.Z,{})})}),a.jsx("span",{className:"inline-block text-base font-semibold leading-[22px]",children:et("Quick Send")})]})}),a.jsx(f.u6,{className:"hidden"}),(0,a.jsxs)("div",{className:"flex h-full w-full flex-1 flex-col p-0",children:[a.jsx("div",{className:"flex flex-col px-6 py-4",children:a.jsx(l.R,{iconPlacement:"end",value:t,onChange:e=>s(e.target.value),className:"h-10 rounded-lg bg-accent",placeholder:et("Search...")})}),(0,a.jsxs)(h.x,{className:"flex-1 px-6 pb-8",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col items-stretch gap-4",children:[G&&a.jsx(i.Loader,{className:"mx-2"}),B?.map(e=>e.quickSend?a.jsx(Y,{id:e.id,name:e.contact.customer.name,email:e.contact.email,checked:!0,onSelect:e=>ed(e,"remove")},e.id):null)]}),a.jsx(g.Z,{className:"my-4"}),(0,a.jsxs)("div",{className:"flex w-full flex-col items-stretch gap-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-2.5",children:[a.jsx("h5",{className:"text-base font-semibold leading-[22px] text-foreground",children:et("Contacts")}),a.jsx("p",{className:"text-xs font-normal text-secondary-text",children:et("Select up to 5 contacts to add them in the Quick Send list.")})]}),G&&a.jsx(i.Loader,{className:"mx-2"}),B?.map(e=>e.quickSend?null:a.jsx(Y,{id:e.id,name:e.contact.customer.name,email:e.contact.email,onSelect:e=>ed(e,"add")},e.id))]})]})]})]})]}),a.jsx(c.Y,{}),(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-6 shadow-default",children:[a.jsx("div",{className:"mb-2 flex items-center justify-between",children:a.jsx("p",{className:"font-semibold text-foreground",children:et("Your QR Code")})}),a.jsx("p",{className:"mb-6 text-sm text-secondary-text",children:et("Customers can scan this QR code to make payments.")}),a.jsx("div",{className:"mx-auto mb-6 flex max-w-[200px] items-center justify-center",children:a.jsx(U.Z,{id:"merchant-qr-code",name:W?.merchant?.name??"",email:W?.merchant?.email??"",children:a.jsx(O.Q,{value:`${H}/mpay/qrform?merchantId=${W?.merchant?.id}`,size:200,id:"merchant-qr-code"})})}),(0,a.jsxs)(x.z,{onClick:eo,className:"w-full",children:[a.jsx($.Z,{size:"24"}),a.jsx("span",{children:et("Download")})]})]}),(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-6 shadow-default",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[a.jsx("p",{className:"font-semibold text-foreground",children:et("Refer a friend")}),a.jsx(m.C,{variant:"secondary",className:"bg-muted",children:en?.data?.referralUsers?.length?en.data.referralUsers.length:"0"})]}),a.jsx("p",{className:"mb-6 text-sm text-secondary-text",children:et("Share this referral link to your friends and earn money.")}),a.jsx("div",{className:"mb-2 line-clamp-1 flex h-12 w-full items-center text-ellipsis whitespace-nowrap rounded-[8px] bg-input px-3",children:W?.getReferralLink()}),(0,a.jsxs)(x.z,{className:"w-full",onClick:()=>(0,C.Fp)(W?.getReferralLink()??""),children:[a.jsx(I.Z,{size:"24"}),a.jsx("span",{children:et("Copy link")})]})]})]})]})]})}},61625:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(10326),r=s(8281),l=s(4066),n=s(77863),i=s(46226),d=s(70012);let c=(0,s(14505).QM)(()=>({root:{backgroundColor:"#fff",borderRadius:30,border:"5px solid #EFEFF7",padding:12},header:{marginBottom:10},description:{fontStyle:"normal",fontWeight:"normal",fontSize:"14px",lineHeight:"22px",color:"#000",textAlign:"center",marginBottom:10},logo:{display:"flex",justifyContent:"center"},merchantLogo:{width:40,height:40,backgroundColor:"#F3F4F6",borderRadius:50,padding:10,fontSize:18,color:"#000"},qrContainer:{position:"relative",backgroundColor:"#01a79e",borderRadius:20,width:250,height:250,margin:8,padding:10},qrInner:{backgroundColor:"white",borderRadius:10,flex:1,display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},name:{fontStyle:"normal",fontWeight:"500",fontSize:"18px",lineHeight:"29px",textAlign:"center"},email:{fontStyle:"normal",fontWeight:"normal",fontSize:"14px",lineHeight:"22px",color:"#000",textAlign:"center"}})),o=({children:e,name:t,id:s,email:o})=>{let m=c(),{logo:x,siteName:u}=(0,l.T)(),{t:f}=(0,d.$G)();return(0,a.jsxs)("div",{className:m.root,id:s,children:[(0,a.jsxs)("div",{className:m.header,children:[a.jsx("div",{className:m.logo,children:a.jsx(i.default,{src:(0,n.qR)(x),width:160,height:40,alt:u,className:"max-h-10 object-contain"})}),a.jsx("p",{className:m.description,children:f("To make payment, scan the QR Code.")})]}),a.jsx(r.Z,{className:"my-2"}),a.jsx("div",{className:m.qrContainer,children:a.jsx("div",{className:m.qrInner,children:e})}),t&&(0,a.jsxs)("div",{className:"mb-1",children:[a.jsx("div",{className:m.name,children:t}),a.jsx("div",{className:m.email,children:o})]})]})}},56140:(e,t,s)=>{"use strict";s.d(t,{Z:()=>v});var a=s(10326),r=s(77863),l=s(86508),n=s(11798),i=s(77132),d=s(6216),c=s(75817),o=s(40420),m=s(35047),x=s(93327),u=s(17577),f=s(70012),h=s(90772);let p=u.forwardRef(({className:e,...t},s)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:s,className:(0,r.ZP)("w-full caption-bottom text-sm",e),...t})}));p.displayName="Table";let g=u.forwardRef(({className:e,...t},s)=>a.jsx("thead",{ref:s,className:(0,r.ZP)("",e),...t}));g.displayName="TableHeader";let j=u.forwardRef(({className:e,...t},s)=>a.jsx("tbody",{ref:s,className:(0,r.ZP)("[&_tr:last-child]:border-0",e),...t}));j.displayName="TableBody",u.forwardRef(({className:e,...t},s)=>a.jsx("tfoot",{ref:s,className:(0,r.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let b=u.forwardRef(({className:e,...t},s)=>a.jsx("tr",{ref:s,className:(0,r.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));b.displayName="TableRow";let y=u.forwardRef(({className:e,...t},s)=>a.jsx("th",{ref:s,className:(0,r.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));y.displayName="TableHead";let N=u.forwardRef(({className:e,...t},s)=>a.jsx("td",{ref:s,className:(0,r.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));function v({data:e,isLoading:t=!1,structure:s,sorting:v,setSorting:w,padding:S=!1,className:C,onRefresh:Z,pagination:k}){let D=(0,u.useMemo)(()=>s,[s]),P=(0,m.useRouter)(),z=(0,m.usePathname)(),A=(0,m.useSearchParams)(),{t:L}=(0,f.$G)(),R=(0,l.b7)({data:e||[],columns:D,state:{sorting:v,onRefresh:Z},onSortingChange:w,getCoreRowModel:(0,n.sC)(),getSortedRowModel:(0,n.tj)(),debugTable:!1});return t?a.jsx("div",{className:"rounded-md bg-background p-10",children:a.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:L("Loading...")})}):e?.length?(0,a.jsxs)("div",{className:(0,r.ZP)(`${S?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,C),children:[(0,a.jsxs)(p,{children:[a.jsx(g,{children:R.getHeaderGroups().map(e=>a.jsx(b,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>a.jsx(y,{className:(0,r.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,a.jsxs)(h.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[L((0,l.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:a.jsx(d.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:a.jsx(d.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??a.jsx(d.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),a.jsx(j,{children:R.getRowModel().rows.map(e=>a.jsx(b,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>a.jsx(N,{className:"py-3 text-sm font-semibold",children:(0,l.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),k&&k.total>10&&a.jsx("div",{className:"pb-2 pt-6",children:a.jsx(x.Z,{showTotal:(e,t)=>L("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:k?.page,total:k?.total,pageSize:k?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(A);t.set("page",e.toString()),P.push(`${z}?${t.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>a.jsx("a",{...e,children:a.jsx(c.Z,{size:"18"})}),nextIcon:e=>a.jsx("a",{...e,children:a.jsx(o.Z,{size:"18"})})})})]}):a.jsx("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(i.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),L("No data found!")]})})}N.displayName="TableCell",u.forwardRef(({className:e,...t},s)=>a.jsx("caption",{ref:s,className:(0,r.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},16150:(e,t,s)=>{"use strict";s.d(t,{X:()=>c});var a=s(10326),r=s(90772),l=s(34612),n=s(44284),i=s(90434),d=s(70012);function c({isVerified:e=!1,documentStatus:t}){let{t:s}=(0,d.$G)();return e?null:(0,a.jsxs)("div",{className:"flex h-[182px] w-[350px] flex-col items-center justify-center rounded-2xl border border-primary bg-background p-4",children:[a.jsx(l.Z,{variant:"Bulk",className:"mb-2.5 text-important",size:64}),a.jsx("h6",{className:"font-bold",children:s("Awaiting KYC verification")}),"not submitted"===t?a.jsx(r.z,{className:"mt-auto w-full gap-[2px] rounded-lg px-4 py-2 text-base font-medium leading-[22px]",asChild:!0,children:(0,a.jsxs)(i.default,{href:"/settings/kyc-verification-settings",children:[s("Submit Documents"),a.jsx(n.Z,{size:16})]})}):null]})}},38428:(e,t,s)=>{"use strict";s.d(t,{P:()=>C});var a=s(10326),r=s(99440),l=s(90772),n=s(33071),i=s(62288),d=s(8281),c=s(33261),o=s(94487),m=s(54825),x=s(28707),u=s(19395),f=s(4066),h=s(60102),p=s(77863),g=s(20187),j=s(52078),b=s(46680),y=s(29169),N=s(54639),v=s(90434),w=s(70012),S=s(85999);function C({card:e,title:t,balance:s,currency:r,walletId:l,onMutate:i}){let{t:d}=(0,w.$G)(),{settings:c}=(0,h.h)();return r&&(0,a.jsxs)(n.Zb,{className:"w-[350px] overflow-hidden rounded-2xl border-primary bg-gradient-to-b from-[#48E1D8] to-primary",children:[(0,a.jsxs)(n.aY,{className:"relative overflow-hidden px-6 py-4",children:[a.jsx("h2",{className:"text-shadow pointer-events-none absolute bottom-3 right-0 text-[104px] font-bold text-primary opacity-30",children:r}),a.jsx("div",{className:"relative z-20 mb-4 flex items-center gap-2.5",children:a.jsx("h6",{className:"font-bold text-primary-foreground",children:t})}),(0,a.jsxs)("div",{className:"relative z-20 text-primary-foreground",children:[a.jsx("span",{className:"text-xs font-normal",children:d("Balance")}),(0,a.jsxs)("h1",{className:"flex items-center gap-1 align-baseline text-[32px] font-semibold leading-10",children:[Number(s).toFixed(2),a.jsx("span",{className:"text-sm font-normal leading-5",children:r})]})]})]}),c?.virtual_card?.status==="on"&&a.jsx(n.eW,{className:"justify-end bg-primary px-6 py-2 text-primary-foreground",children:e?.lastFour?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex-1 text-sm font-normal leading-5",children:["**** **** **** ",e?.lastFour]}),a.jsx(k,{card:e,balance:s,currency:r,onMutate:i})]}):a.jsx(Z,{walletId:l,currency:r,onMutate:i})})]})}function Z({walletId:e,currency:t,onMutate:s=()=>{}}){let{t:n}=(0,w.$G)();return(0,a.jsxs)(r.aR,{children:[a.jsx(r.vW,{asChild:!0,children:(0,a.jsxs)(l.z,{size:"sm",disabled:t?.toUpperCase()!=="USD"&&t?.toUpperCase()!=="NGN",className:"text-sm font-semibold leading-5 opacity-100 hover:opacity-90",children:[a.jsx(g.Z,{size:"20"}),a.jsx("span",{children:t?.toUpperCase()==="USD"||t?.toUpperCase()==="NGN"?n("Issue Card"):n("Card Not Available")})]})}),(0,a.jsxs)(r._T,{children:[(0,a.jsxs)(r.fY,{children:[a.jsx(r.f$,{children:n("Confirm Your Card")}),a.jsx(r.yT,{children:n("Are you sure you want to issue a card for this wallet?")})]}),(0,a.jsxs)(r.xo,{children:[a.jsx(r.le,{children:n("Cancel")}),a.jsx(r.OL,{onClick:()=>{S.toast.promise((0,m.m)(e),{loading:n("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return s(),e.message},error:e=>e.message})},children:n("Issue Card")})]})]})]})}function k({card:e,balance:t,currency:s,onMutate:r=()=>{}}){let{cardBg:n}=(0,f.T)(),{t:m}=(0,w.$G)(),{auth:h}=(0,u.a)(),g=t=>{S.toast.promise((0,x.a)({cardId:e.id,dataList:{status:t}}),{loading:m("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return r(),e.message},error:e=>e.message})},C=()=>{S.toast.promise((0,o.f)({cardId:e.id}),{loading:m("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return r(),e.message},error:e=>e.message})};return(0,a.jsxs)(i.Vq,{children:[a.jsx(i.hg,{asChild:!0,children:(0,a.jsxs)(l.z,{size:"sm",className:"text-sm font-semibold leading-5 opacity-100 hover:opacity-90",children:[a.jsx(j.Z,{size:"20"}),a.jsx("span",{children:m("View Card")})]})}),(0,a.jsxs)(i.cZ,{className:"sm:max-w-[525px]",children:[a.jsx(i.$N,{children:m("Card Details")}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{style:{backgroundImage:`url(${(0,p.qR)(n)})`},className:"mb-5 flex min-h-[280px] w-full max-w-[450px] flex-col justify-end gap-7 rounded-3xl bg-cover p-7",children:[a.jsx("p",{className:"text-[28px] font-semibold text-white",children:e.number.replace(/(\d{4})(?=\d)/g,"$1 ")}),(0,a.jsxs)("div",{className:"flex items-center gap-8",children:[(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:m("Card holder name")}),a.jsx("p",{className:"text-xl font-semibold",children:h?.customer?.name})]}),(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:m("Expiry date")}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:[e.expMonth.toString().padStart(2,"0"),"/",4===e.expYear.toString().length?e.expYear.toString().slice(2):e.expYear.toString()]})]}),(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:m("CVV")}),a.jsx("p",{className:"text-xl font-semibold",children:e.cvc})]})]})]}),(0,a.jsxs)("div",{className:"mb-5 flex gap-8",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>(0,p.Fp)(e.number),className:"flex flex-col items-center justify-center gap-2",children:[a.jsx("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:a.jsx(b.Z,{size:"24",color:"#000"})}),a.jsx("span",{className:"text-xs font-semibold",children:m("Copy Number")})]}),(0,a.jsxs)(v.default,{href:"/deposit",className:"flex flex-col items-center justify-center gap-2",children:[a.jsx("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:a.jsx(y.Z,{size:"24",color:"#000"})}),a.jsx("span",{className:"text-xs font-semibold",children:m("Deposit Money")})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>C(),className:"flex flex-col items-center justify-center gap-2",children:[a.jsx("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary text-black transition duration-300 ease-in-out hover:bg-spacial-red-foreground hover:text-danger",children:a.jsx(N.Z,{size:"24"})}),a.jsx("span",{className:"text-xs font-semibold",children:m("Close Card")})]})]}),a.jsx(d.Z,{className:"mb-5 border-b bg-transparent"}),(0,a.jsxs)("div",{className:"w-full space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:m("Status")}),a.jsx(c.Z,{defaultChecked:"active"===e.status,onCheckedChange:e=>{g(e?"active":"inactive")}})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:m("Balance")}),(0,a.jsxs)("span",{className:"text-sm font-semibold",children:[t," ",s]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:m("Card Type")}),a.jsx("span",{className:"text-sm font-semibold",children:e?.brand})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:m("Expiry Date")}),(0,a.jsxs)("span",{className:"text-sm font-semibold",children:[e.expMonth.toString().padStart(2,"0"),"/",4===e.expYear.toString().length?e.expYear.toString().slice(2):e.expYear.toString()]})]})]})]})]})]})}},63761:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var a=s(10326);s(17577);var r=s(54432),l=s(77863),n=s(32894);function i({iconPlacement:e="start",className:t,containerClass:s,...i}){return(0,a.jsxs)("div",{className:(0,l.ZP)("relative flex items-center",s),children:[a.jsx(n.Z,{size:"20",className:(0,l.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),a.jsx(r.I,{type:"text",className:(0,l.ZP)("h-10","end"===e?"pr-10":"pl-10",t),...i})]})}},15495:(e,t,s)=>{"use strict";s.d(t,{Y:()=>w});var a=s(10326),r=s(5158),l=s(75584),n=s(77863),i=s(35047),d=s(70012),c=s(28758),o=s(567),m=s(90772),x=s(97185),u=s(17577),f=s.n(u);let h=x.fC,p=x.xz,g=u.forwardRef(({className:e,align:t="center",sideOffset:s=4,...r},l)=>a.jsx(x.VY,{ref:l,align:t,sideOffset:s,className:(0,n.ZP)("z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-light-16 outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));g.displayName=x.VY.displayName;var j=s(8281),b=s(81638),y=s(54033),N=s(44284);function v({avatar:e,name:t,email:s,wallet:r,onCallback:l,callbackBtnLabel:n}){let[i,d]=f().useState(!1),{device:x}=(0,b.q)();return(0,a.jsxs)(h,{open:i,onOpenChange:d,children:[(0,a.jsxs)(p,{onClick:()=>d(!0),className:"flex w-full items-center gap-2 rounded-lg px-4 py-2 hover:cursor-pointer hover:bg-accent",children:[(0,a.jsxs)(c.qE,{className:"h-8 w-8",children:[a.jsx(c.F$,{src:e,alt:t}),a.jsx(c.Q5,{children:(0,y.v)(t)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm text-foreground",children:t}),a.jsx("p",{className:"text-xs text-secondary-text",children:s})]}),r?a.jsx(o.C,{variant:"secondary",children:r}):null]}),a.jsx(g,{align:"Desktop"===x?"start":"end",side:"Desktop"===x?"left":"bottom",sideOffset:10,className:"rounded-lg border-none px-0 py-2",children:(0,a.jsxs)("div",{className:"flex flex-col items-center p-1",children:[(0,a.jsxs)(c.qE,{className:"mb-3 h-14 w-14",children:[a.jsx(c.F$,{src:e,alt:t}),a.jsx(c.Q5,{children:(0,y.v)(t)})]}),a.jsx("h5",{className:"m-0 p-0 text-base font-medium leading-[22px] text-foreground",children:t}),a.jsx("p",{className:"p-0 text-sm font-normal leading-5 text-secondary-text",children:s}),a.jsx(j.Z,{className:"mb-2.5 mt-[11px]"}),(0,a.jsxs)(m.z,{type:"button",onClick:l,className:"mx-auto h-10 w-[228px] rounded-lg px-4 py-2 text-base font-medium leading-[22px]",children:[n,a.jsx(N.Z,{size:16})]})]})})]})}function w(){let{t:e}=(0,d.$G)(),t=(0,i.useRouter)(),{data:s}=(0,l.Z)("/saves?page=1&limit=6");return(0,a.jsxs)("div",{className:"w-full flex-1 rounded-xl bg-background px-2 py-6 shadow-default xl:flex-auto",children:[a.jsx("p",{className:"mb-4 px-4 font-semibold text-foreground",children:e("Favorites")}),a.jsx(r.J,{condition:!!s?.length,children:a.jsx("div",{className:"flex flex-col",children:s?.map(s=>{if("phone"===s.type)return a.jsx(v,{avatar:"",name:s?.info?JSON.parse(s.info)?.label:"",email:s.value,wallet:"",onCallback:()=>t.push(`/services/top-up?phone=${s.value}`),callbackBtnLabel:e("Top-up money")},s.id);if("wallet"===s.type)return a.jsx(v,{avatar:s?.info?n.qR(JSON.parse(s.info)?.image):"",name:s?.info?JSON.parse(s.info)?.label:"",email:s?.info?JSON.parse(s.info)?.email:"",wallet:s?.metaData?JSON.parse(s.metaData)?.currency:"",onCallback:()=>{t.push(`/transfer?walletId=${s.value}`)},callbackBtnLabel:e("Transfer")},s.id);if("merchant"===s.type)return a.jsx(v,{avatar:s?.info?n.qR(JSON.parse(s.info)?.image):"",name:s?.info?JSON.parse(s.info)?.label:"",email:s?.info?JSON.parse(s.info)?.email:"",wallet:s?.metaData?JSON.parse(s.metaData)?.currency:"",onCallback:()=>{t.push(`/payment?email=${JSON.parse(s.info)?.email}`)},callbackBtnLabel:e("Payment")},s.id);if("electricity"===s.type){let r=s?.metaData?JSON.parse(s.metaData)?.billerId:"",l=new URLSearchParams;return r&&l.set("billerId",r),l.set("meterId",s.value),a.jsx(v,{avatar:s?.info?n.qR(JSON.parse(s.info)?.image):"",name:s?.info?JSON.parse(s.info)?.label:"",email:s.value,wallet:"",onCallback:()=>{t.push(`/services/electricity-bill?${l.toString()}`)},callbackBtnLabel:e("Pay bill")},s.id)}return null})})}),a.jsx(r.J,{condition:s?.length===0,children:a.jsx("p",{className:"px-4 text-sm text-secondary-text",children:e("No favorite item")})})]})}},33071:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>i,SZ:()=>c,Zb:()=>n,aY:()=>o,eW:()=>m,ll:()=>d});var a=s(10326),r=s(17577),l=s(77863);let n=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let i=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.ZP)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("h3",{ref:s,className:(0,l.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...t},s)=>a.jsx("p",{ref:s,className:(0,l.ZP)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.ZP)("p-6 pt-0",e),...t}));o.displayName="CardContent";let m=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,l.ZP)("flex items-center p-6 pt-0",e),...t}));m.displayName="CardFooter"},53313:(e,t,s)=>{"use strict";s.d(t,{X:()=>d});var a=s(10326),r=s(13635),l=s(32933),n=s(17577),i=s(77863);let d=n.forwardRef(({className:e,...t},s)=>a.jsx(r.fC,{ref:s,className:(0,i.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:a.jsx(r.z$,{className:(0,i.ZP)("flex items-center justify-center text-current"),children:a.jsx(l.Z,{className:"h-4 w-4"})})}));d.displayName=r.fC.displayName},54432:(e,t,s)=>{"use strict";s.d(t,{I:()=>n});var a=s(10326),r=s(17577),l=s(77863);let n=r.forwardRef(({className:e,type:t,...s},r)=>a.jsx("input",{type:t,className:(0,l.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:r,...s}));n.displayName="Input"},31048:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(10326),r=s(34478),l=s(79360),n=s(17577),i=s(77863);let d=(0,l.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef(({className:e,...t},s)=>a.jsx(r.f,{ref:s,className:(0,i.ZP)(d(),e),...t}));c.displayName=r.f.displayName;let o=c},65685:(e,t,s)=>{"use strict";s.d(t,{x:()=>i});var a=s(10326),r=s(16999),l=s(17577),n=s(77863);let i=l.forwardRef(({className:e,children:t,...s},l)=>(0,a.jsxs)(r.fC,{ref:l,className:(0,n.ZP)("relative overflow-hidden",e),...s,children:[a.jsx(r.l_,{className:"h-full w-full rounded-[inherit]",children:t}),a.jsx(d,{}),a.jsx(r.Ns,{})]}));i.displayName=r.fC.displayName;let d=l.forwardRef(({className:e,orientation:t="vertical",...s},l)=>a.jsx(r.gb,{ref:l,orientation:t,className:(0,n.ZP)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...s,children:a.jsx(r.q4,{className:"relative flex-1 rounded-full bg-border"})}));d.displayName=r.gb.displayName},34474:(e,t,s)=>{"use strict";s.d(t,{Bw:()=>p,Ph:()=>m,Ql:()=>g,i4:()=>u,ki:()=>x});var a=s(10326),r=s(13869),l=s(96633),n=s(941),i=s(17577),d=s(77863),c=s(6216),o=s(44284);let m=r.fC;r.ZA;let x=r.B4,u=i.forwardRef(({className:e,children:t,...s},l)=>(0,a.jsxs)(r.xz,{ref:l,className:(0,d.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,a.jsx(r.JO,{asChild:!0,children:a.jsx(c.Z,{size:"24",color:"#292D32"})})]}));u.displayName=r.xz.displayName;let f=i.forwardRef(({className:e,...t},s)=>a.jsx(r.u_,{ref:s,className:(0,d.ZP)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(l.Z,{className:"h-4 w-4"})}));f.displayName=r.u_.displayName;let h=i.forwardRef(({className:e,...t},s)=>a.jsx(r.$G,{ref:s,className:(0,d.ZP)("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(n.Z,{className:"h-4 w-4"})}));h.displayName=r.$G.displayName;let p=i.forwardRef(({className:e,children:t,position:s="popper",...l},n)=>a.jsx(r.h_,{children:(0,a.jsxs)(r.VY,{ref:n,className:(0,d.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...l,children:[a.jsx(f,{}),a.jsx(r.l_,{className:(0,d.ZP)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(h,{})]})}));p.displayName=r.VY.displayName,i.forwardRef(({className:e,...t},s)=>a.jsx(r.__,{ref:s,className:(0,d.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=r.__.displayName;let g=i.forwardRef(({className:e,children:t,...s},l)=>(0,a.jsxs)(r.ck,{ref:l,className:(0,d.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(r.wU,{children:a.jsx(o.Z,{variant:"Bold",className:"h-4 w-4"})})}),a.jsx(r.eT,{children:t})]}));g.displayName=r.ck.displayName,i.forwardRef(({className:e,...t},s)=>a.jsx(r.Z0,{ref:s,className:(0,d.ZP)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=r.Z0.displayName},65304:(e,t,s)=>{"use strict";s.d(t,{O:()=>l});var a=s(10326),r=s(77863);function l({className:e,...t}){return a.jsx("div",{className:(0,r.ZP)("animate-pulse rounded-md bg-muted",e),...t})}},33261:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var a=s(10326),r=s(41959),l=s(17577),n=s(77863);let i=l.forwardRef(({className:e,...t},s)=>a.jsx(r.fC,{className:(0,n.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",e),...t,ref:s,children:a.jsx(r.bU,{className:(0,n.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})}));i.displayName=r.fC.displayName;let d=i},79210:(e,t,s)=>{"use strict";s.d(t,{SP:()=>c,dr:()=>d,mQ:()=>i,nU:()=>o});var a=s(10326),r=s(17577),l=s(28407),n=s(77863);let i=l.fC,d=r.forwardRef(({className:e,...t},s)=>a.jsx(l.aV,{ref:s,className:(0,n.ZP)("inline-flex h-10 w-full items-center justify-center rounded-md bg-secondary p-1 text-muted-foreground",e),...t}));d.displayName=l.aV.displayName;let c=r.forwardRef(({className:e,...t},s)=>a.jsx(l.xz,{ref:s,className:(0,n.ZP)("inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-semibold text-secondary-800 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite",e),...t}));c.displayName=l.xz.displayName;let o=r.forwardRef(({className:e,...t},s)=>a.jsx(l.VY,{ref:s,className:(0,n.ZP)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));o.displayName=l.VY.displayName},94487:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var a=s(49547),r=s(10734);async function l({cardId:e,isAdmin:t=!1}){try{let s=await a.Z.delete(`${t?"/admin/cards/":"/cards/"}${e}`);return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}},54825:(e,t,s)=>{"use strict";s.d(t,{m:()=>l});var a=s(49547),r=s(10734);async function l(e){try{let t=await a.Z.post("/cards/generate-virtual",{walletId:e});return(0,r.B)(t)}catch(e){return(0,r.D)(e)}}},28707:(e,t,s)=>{"use strict";s.d(t,{a:()=>l});var a=s(49547),r=s(10734);async function l({cardId:e,dataList:t,isAdmin:s=!1}){try{let l=await a.Z.put(`${s?"/admin/cards/change-status/":"/cards/change-status/"}${e}`,t);return(0,r.B)(l)}catch(e){return(0,r.D)(e)}}},69519:(e,t,s)=>{"use strict";s.d(t,{M:()=>l});var a=s(49547),r=s(10734);async function l(e,t){try{let s=await a.Z.put(`/contacts/quicksend/${t}/${e}`,{});return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}},81431:(e,t,s)=>{"use strict";s.d(t,{t:()=>l});var a=s(84455),r=s(49547);function l(e){let{data:t,isLoading:s,error:l,mutate:n,...i}=(0,a.ZP)(e||"/contacts",e=>r.Z.get(e));return{contacts:t?.data??[],isLoading:s,error:l,mutate:()=>n(t),...i}}},75584:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(90799),r=s(35047);function l(e,t){let s=(0,r.usePathname)(),l=(0,r.useSearchParams)(),n=(0,r.useRouter)(),[i,d]=e.split("?"),c=new URLSearchParams(d);c.has("page")||c.set("page","1"),c.has("limit")||c.set("limit","10");let o=`${i}?${c.toString()}`,{data:m,error:x,isLoading:u,mutate:f,...h}=(0,a.d)(o,t);return{refresh:()=>f(m),data:m?.data?.data??[],meta:m?.data?.meta,filter:(e,t,a)=>{let r=new URLSearchParams(l.toString());t?r.set(e,t.toString()):r.delete(e),n.replace(`${s}?${r.toString()}`),a?.()},isLoading:u,error:x,...h}}},60814:(e,t,s)=>{"use strict";s.d(t,{r:()=>n});var a=s(49547),r=s(9652),l=s(84455);function n(){let{data:e,isLoading:t,mutate:s}=(0,l.ZP)("/wallets",e=>a.Z.get(e));return{wallets:e?.data?.map(e=>new r.w(e))??[],isLoading:t,getWalletByCurrencyCode:(e,t)=>e?.find(e=>e?.currency?.code===t),mutate:s}}},48444:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});class a{constructor(e){this.id=e?.id,this.cardId=e?.cardId,this.userId=e?.userId,this.walletId=e?.walletId,this.number=e?.number,this.cvc=e?.cvc,this.lastFour=e?.lastFour,this.brand=e?.brand,this.expMonth=e?.expMonth,this.expYear=e?.expYear,this.status=e?.status,this.type=e?.type,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.wallet=e?.wallet,this.user=e?.user}}},59598:(e,t,s)=>{"use strict";s.d(t,{F:()=>a});class a{constructor(e){this.formatter=e=>{let t=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),s=t.formatToParts(e),a=s.find(e=>"currency"===e.type)?.value??this.code,r=t.format(e),l=r.substring(a.length).trim();return{currencyCode:this.code,currencySymbol:a,formattedAmount:r,amountText:l}},this.id=e?.id,this.name=e?.name,this.code=e?.code,this.logo=e?.logo??"",this.usdRate=e?.usdRate,this.acceptApiRate=!!e?.acceptApiRate,this.isCrypto=!!e?.isCrypto,this.active=!!e?.active,this.metaData=e?.metaData,this.minAmount=e?.minAmount,this.kycLimit=e?.kycLimit,this.maxAmount=e?.maxAmount,this.dailyTransferAmount=e?.dailyTransferAmount,this.dailyTransferLimit=e?.dailyTransferLimit,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}format(e){let{currencySymbol:t,amountText:s}=this.formatter(e);return`${s} ${t}`}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}}},66114:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});var a=s(72450),r=s(71305),l=s(79308);class n{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new a.n(e?.user),customer:e?.user?.customer?new l.O(e?.user?.customer):null,merchant:e?.user?.merchant?new l.O(e?.user?.merchant):null,agent:e?.user?.agent?new l.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,r.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,r.WU)(this.updatedAt,e):"N/A"}}},72450:(e,t,s)=>{"use strict";s.d(t,{n:()=>n});var a=s(13263),r=s(13573),l=s(77863);class n{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,l.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new r.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}},9652:(e,t,s)=>{"use strict";s.d(t,{w:()=>l});var a=s(48444),r=s(59598);class l{constructor(e){this.id=e?.id,this.walletId=e?.walletId,this.logo=e?.logo,this.userId=e?.userId,this.balance=e?.balance,this.defaultStatus=!!e?.default,this.pinDashboard=!!e?.pinDashboard,this.currencyId=e?.currencyId,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.currency=new r.F(e?.currency),this.cards=e?.cards?.map(e=>new a.Z(e))}}},64638:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},44514:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\(dashboard)\page.tsx#default`)},14861:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},93505:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\(dashboard)\page.tsx#default`)},84514:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(19510),r=s(40099),l=s(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(l.Z,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}s(71159)},18406:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},22630:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center",children:a.jsx(r.a,{})})}},34619:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\(dashboard)\@tableSlot\page.tsx#default`)},87873:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\(dashboard)\layout.tsx#default`)},50134:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},88728:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(19510),r=s(40099),l=s(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(l.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}s(71159)},80549:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},11919:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center",children:a.jsx(r.a,{})})}},98192:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\(dashboard)\@tableSlot\page.tsx#default`)},489:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\(dashboard)\layout.tsx#default`)},336:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(19510),r=s(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}}};