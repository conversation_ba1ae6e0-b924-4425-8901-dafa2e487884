(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[24746],{99274:function(e,s,a){Promise.resolve().then(a.bind(a,48634))},48634:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return y},runtime:function(){return N}});var t=a(57437),l=a(41709),n=a(85487),i=a(87806),c=a(62869),r=a(6512),d=a(31117),o=a(94508),m=a(48408),x=a(2901),u=a(77926),p=a(64394),v=a(19571),f=a(60827),j=a(55036),h=a(43271),b=a(99376),g=a(43949);let N="edge";function y(){var e,s,a,N,y,z;let{t:k}=(0,g.$G)(),w=(0,b.useParams)(),Z=(0,b.useRouter)(),{data:C,isLoading:I}=(0,d.d)("/transactions/trx/".concat(w.trxId));if(I)return(0,t.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,t.jsx)(n.Loader,{})});let A=(null==C?void 0:C.data)?new m.C(null==C?void 0:C.data):null,_=new o.F;return A?(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("div",{className:"grid grid-cols-12 gap-4",children:(0,t.jsx)("div",{className:"col-span-12 lg:col-span-7",children:(0,t.jsxs)("div",{className:"relative flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,t.jsx)(c.z,{type:"button",variant:"outline",size:"icon",onClick:Z.back,className:"absolute left-4 top-4",children:(0,t.jsx)(p.Z,{})}),(0,t.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,t.jsx)(l.J,{condition:"completed"===A.status,children:(0,t.jsx)(v.Z,{variant:"Bulk",size:32,className:"text-success"})}),(0,t.jsx)(l.J,{condition:"failed"===A.status,children:(0,t.jsx)(f.Z,{variant:"Bulk",size:32,className:"text-destructive"})}),(0,t.jsx)(l.J,{condition:"pending"===A.status,children:(0,t.jsx)(j.Z,{variant:"Bulk",size:32,className:"text-primary"})}),(0,t.jsxs)("h2",{className:"font-semibold",children:[k("Transaction")," #",w.trxId]})]}),(0,t.jsx)(i.z,{senderAvatar:(0,o.qR)(A.from.image),senderName:A.from.label,senderInfo:[null===(e=A.from)||void 0===e?void 0:e.email,null==A?void 0:null===(s=A.from)||void 0===s?void 0:s.phone],receiverAvatar:(0,o.qR)(null==A?void 0:null===(a=A.to)||void 0===a?void 0:a.image),receiverName:null==A?void 0:null===(N=A.to)||void 0===N?void 0:N.label,receiverInfo:[null==A?void 0:null===(y=A.to)||void 0===y?void 0:y.email,null==A?void 0:null===(z=A.to)||void 0===z?void 0:z.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,t.jsx)(r.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:"Date"}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:(null==A?void 0:A.createdAt)?(0,x.WU)(A.createdAt,"dd MMM yyyy; hh:mm a"):""})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:"Amount"}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:_.formatVC(A.amount,A.metaData.currency)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:k("Service charge")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:_.formatVC(A.fee,A.metaData.currency)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:k("User gets")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-semibold sm:text-base",children:_.formatVC(A.total,A.metaData.currency)})]})]}),(0,t.jsx)(r.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,t.jsx)("div",{className:"flex flex-col",children:(0,t.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:k("Transaction ID")}),(0,t.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[A.trxId,(0,t.jsx)(c.z,{type:"button",onClick:()=>(0,o.Fp)(A.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,t.jsx)(h.Z,{size:"20"})})]})]})}),(0,t.jsx)(r.Z,{className:"mb-1 mt-[5px] bg-border"})]})})})}):(0,t.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,t.jsx)(u.Z,{}),k("No data found")]})}},41709:function(e,s,a){"use strict";function t(e){let{condition:s,children:a}=e;return s?a:null}a.d(s,{J:function(){return t}}),a(2265)},87806:function(e,s,a){"use strict";a.d(s,{z:function(){return r}});var t=a(57437),l=a(16831),n=a(94508),i=a(59532),c=a(19571);function r(e){let{senderName:s,senderAvatar:a,senderInfo:l,receiverName:i,receiverAvatar:c,receiverInfo:r,className:o}=e;return(0,t.jsxs)("div",{className:(0,n.ZP)("mb-4 flex items-start justify-around gap-1",o),children:[(0,t.jsx)(d,{name:s,avatar:a,info:l}),i&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),(0,t.jsx)(d,{name:i,avatar:c,info:r})]})]})}function d(e){let{avatar:s,name:a,info:n=[]}=e,r=n.filter(Boolean);return(0,t.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,t.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,t.jsxs)(l.qE,{className:"size-10 rounded-full sm:size-14",children:[(0,t.jsx)(l.F$,{src:s,alt:a,width:56,height:56}),(0,t.jsx)(l.Q5,{className:"font-semibold",children:(0,i.v)(a)})]}),(0,t.jsx)("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:(0,t.jsx)(c.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:a}),r.length>0&&r.map((e,s)=>(0,t.jsx)("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},s))]})]})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,2901,33157,48669,28622,92971,95030,1744],function(){return e(e.s=99274)}),_N_E=e.O()}]);