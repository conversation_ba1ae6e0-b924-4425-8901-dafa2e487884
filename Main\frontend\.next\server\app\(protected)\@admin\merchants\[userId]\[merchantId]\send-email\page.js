(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6068],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},95346:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>N,default:()=>k});var s,a={};r.r(a),r.d(a,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>S,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>f,pages:()=>h,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>g,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),r(67206);var n=r(79319),i=r(20518),o=r(61902),c=r(62042),l=r(44630),d=r(44828),m=r(65505),u=r(13839);let p=["",{children:["(protected)",{admin:["children",{children:["merchants",{children:["[userId]",{children:["[merchantId]",{children:["send-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30836)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\send-email\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,64101)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\send-email\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,26105)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,73722)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,76667)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,94626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\send-email\\page.tsx"],f="/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page",S={require:r,loadChunk:()=>Promise.resolve()},g=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page",pathname:"/merchants/[userId]/[merchantId]/send-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var x=r(69094),E=r(5787),P=r(90527);let I=e=>e?JSON.parse(e):void 0,v=self.__BUILD_MANIFEST,D=I(self.__REACT_LOADABLE_MANIFEST),b=null==(s=self.__RSC_MANIFEST)?void 0:s["/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page"],j=I(self.__RSC_SERVER_MANIFEST),A=I(self.__NEXT_FONT_MANIFEST),_=I(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];b&&j&&(0,E.Mo)({clientReferenceManifest:b,serverActionsManifest:j,serverModuleMap:(0,P.w)({serverActionsManifest:j,pageName:"/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page"})});let y=(0,i.d)({pagesType:x.s.APP,dev:!1,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page",appMod:null,pageMod:a,errorMod:null,error500Mod:null,Document:null,buildManifest:v,renderToHTML:c.f,reactLoadableManifest:D,clientReferenceManifest:b,serverActionsManifest:j,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:A,incrementalCacheHandler:null,interceptionRouteRewrites:_}),N=a;function k(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:y})}},31591:(e,t,r)=>{Promise.resolve().then(r.bind(r,13600))},61511:(e,t,r)=>{Promise.resolve().then(r.bind(r,78847))},13600:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E,runtime:()=>x});var s=r(60926),a=r(14579),n=r(30417),i=r(89551),o=r(53042),c=r(44788),l=r(38071),d=r(28531),m=r(5764),u=r(47020),p=r(737),h=r(64947);r(29220);var f=r(39228),S=r(32167),g=r(91500);let x="edge";function E({children:e}){let t=(0,h.UO)(),r=(0,h.lr)(),x=(0,h.tv)(),E=(0,h.jD)(),{t:P}=(0,f.$G)(),I=[{title:P("Account Details"),icon:(0,s.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}?${r.toString()}`,id:"__DEFAULT__"},{title:P("Transactions"),icon:(0,s.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/transactions?${r.toString()}`,id:"transactions"},{title:P("KYC"),icon:(0,s.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/kyc?${r.toString()}`,id:"kyc"},{title:P("Fees"),icon:(0,s.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/fees?${r.toString()}`,id:"fees"},{title:P("Permissions"),icon:(0,s.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/permissions?${r.toString()}`,id:"permissions"},{title:P("Send Email"),icon:(0,s.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/send-email?${r.toString()}`,id:"send-email"}];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,s.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,s.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,s.jsx)("li",{children:(0,s.jsxs)(p.Z,{href:"/merchants/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,s.jsx)(u.Z,{}),P("Back")]})}),(0,s.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",r.get("name")]}),(0,s.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",P("Merchant")," #",t.merchantId]})]}),(0,s.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,s.jsx)("span",{children:P("Active")}),(0,s.jsx)(n.Z,{defaultChecked:"1"===r.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:e=>{S.toast.promise((0,i.z)(t.userId),{loading:P("Loading..."),success:s=>{if(!s.status)throw Error(s.message);let a=new URLSearchParams(r);return(0,g.j)(`/admin/merchants/${t.merchantId}`),a.set("active",e?"1":"0"),x.push(`${E}?${a.toString()}`),s.message},error:e=>e.message})}})]})]}),(0,s.jsx)(a.a,{tabs:I})]}),e]})}},78847:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var s=r(60926),a=r(29411),n=r(59571),i=r(36162),o=r(34451),c=r(18662),l=r(23009),d=r(52419),m=r(15487),u=r(14761),p=r(64947),h=r(29220),f=r(45475),S=r(39228),g=r(32167),x=r(93633);let E=x.z.object({subject:x.z.string({required_error:"User Key is required"}),message:x.z.string({required_error:"User Secret is required"})});function P(){let[e,t]=(0,h.useTransition)(),r=(0,p.lr)(),x=(0,p.UO)(),{t:P}=(0,S.$G)(),I=(0,f.cI)({resolver:(0,m.F)(E),defaultValues:{subject:"",message:""}});return(0,s.jsx)(n.UQ,{type:"multiple",defaultValue:["API"],children:(0,s.jsx)("div",{className:"flex flex-col gap-4 p-4",children:(0,s.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,s.jsxs)(n.Qd,{value:"API",className:"border-none px-4 py-0",children:[(0,s.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("div",{className:"flex items-center gap-1",children:(0,s.jsxs)("p",{className:"text-base font-medium leading-[22px]",children:[P("Send an email to")," ",r.get("name")]})})}),(0,s.jsx)(n.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:(0,s.jsx)(o.l0,{...I,children:(0,s.jsxs)("form",{onSubmit:I.handleSubmit(e=>{t(async()=>{let t=await (0,d.Y)(e,x.userId);t.status?g.toast.success(t.message):g.toast.error(P(t.message))})}),className:"flex flex-col gap-4",children:[(0,s.jsx)(o.Wi,{name:"subject",control:I.control,render:({field:e})=>(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:P("Subject")}),(0,s.jsx)(c.I,{type:"text",placeholder:P("Subject of your mail..."),...e}),(0,s.jsx)(o.zG,{})]})}),(0,s.jsx)(o.Wi,{name:"message",control:I.control,render:({field:e})=>(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:P("Message")}),(0,s.jsx)(l.g,{placeholder:P("Write a message here..."),rows:10,...e}),(0,s.jsx)(o.zG,{})]})}),(0,s.jsx)("div",{className:"flex items-center justify-end gap-4",children:(0,s.jsx)(i.z,{disabled:e,className:"rounded-xl",children:e?(0,s.jsx)(a.Loader,{title:P("Sending.."),className:"text-primary-foreground"}):(0,s.jsxs)(s.Fragment,{children:[P("Send"),(0,s.jsx)(u.Z,{size:16})]})})})]})})})]})})})})}},26105:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,runtime:()=>a});var s=r(18264);let a=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#runtime`),n=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#default`)},73722:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(42416),a=r(21237);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(a.a,{})})}},64101:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(42416),a=r(21237);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(a.a,{})})}},30836:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\send-email\page.tsx#default`)},76667:(e,t,r)=>{"use strict";function s({children:e}){return e}r.r(t),r.d(t,{default:()=>s}),r(87908)},94626:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(42416),a=r(21237);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(a.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4969,7283,5089,3711,7066],()=>t(95346));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page"]=r}]);
//# sourceMappingURL=page.js.map