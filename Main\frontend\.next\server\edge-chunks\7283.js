(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7283],{1116:(e,t,s)=>{let a={"56c3f2e139a4c6bb157d9b7969ec956e3494f518":()=>Promise.resolve().then(s.bind(s,72220)).then(e=>e.agentDevice),"58f67a650e6daf4f61edd9160d8ad96f0e6c3504":()=>Promise.resolve().then(s.bind(s,72220)).then(e=>e.$$ACTION_0)};async function r(e,...t){return(await a[e]()).apply(null,t)}e.exports={"56c3f2e139a4c6bb157d9b7969ec956e3494f518":r.bind(null,"56c3f2e139a4c6bb157d9b7969ec956e3494f518"),"58f67a650e6daf4f61edd9160d8ad96f0e6c3504":r.bind(null,"58f67a650e6daf4f61edd9160d8ad96f0e6c3504")}},65109:(e,t,s)=>{Promise.resolve().then(s.bind(s,79330))},50818:(e,t,s)=>{Promise.resolve().then(s.bind(s,93696))},31971:(e,t,s)=>{Promise.resolve().then(s.bind(s,26334))},47802:(e,t,s)=>{Promise.resolve().then(s.bind(s,29411))},61615:(e,t,s)=>{Promise.resolve().then(s.bind(s,47034)),Promise.resolve().then(s.bind(s,33785)),Promise.resolve().then(s.bind(s,49027)),Promise.resolve().then(s.bind(s,5758)),Promise.resolve().then(s.bind(s,37245)),Promise.resolve().then(s.bind(s,14782)),Promise.resolve().then(s.bind(s,55291)),Promise.resolve().then(s.bind(s,24504)),Promise.resolve().then(s.bind(s,15975)),Promise.resolve().then(s.bind(s,8823))},61227:(e,t,s)=>{"use strict";s.d(t,{w:()=>f});var a=s(60926),r=s(58387),i=s(23560),n=s(36162),o=s(840),d=s(65091),l=s(8440),c=s(28277),u=s(737),h=s(64947),m=s(39228);function f({path:e}){let t=(0,h.BT)(),s=(0,h.jD)(),{logo:f,siteName:p,customerRegistration:g,merchantRegistration:x,agentRegistration:v}=(0,o.T)(),{t:y}=(0,m.$G)(),b=[{type:"customer",enabled:g},{type:"merchant",enabled:x},{type:"agent",enabled:v}],w=b.filter(e=>e.enabled).length;return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex min-h-16 items-center justify-between gap-4 border-b px-4",children:[(0,a.jsx)(u.Z,{href:e,children:(0,a.jsx)(c.Z,{src:(0,d.qR)(f),width:160,height:40,alt:p,className:"max-h-10 object-contain"})}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"hidden items-center md:flex",children:[(0,a.jsx)(r.J,{condition:"signin"===t,children:(0,a.jsxs)(r.J,{condition:w>0,children:[(0,a.jsx)("span",{className:"hidden xl:inline-block",children:y("Don’t have an account?")}),(0,a.jsx)(n.z,{variant:"secondary",className:"mx-2.5 rounded-2xl px-6",asChild:!0,children:(0,a.jsxs)(u.Z,{href:(()=>{if(1===w){let e=b.find(e=>e.enabled);return`/register/${e?.type}?`}return"/register"})(),prefetch:!1,children:[y("Sign up"),(0,a.jsx)(l.Z,{size:15})]})})]})}),(0,a.jsx)(r.J,{condition:"/register"===s,children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"hidden xl:inline-block",children:y("Have an account?")}),(0,a.jsx)(n.z,{variant:"secondary",className:"mx-2.5 rounded-2xl px-6",asChild:!0,children:(0,a.jsxs)(u.Z,{href:"/signin",prefetch:!1,children:[y("Sign in"),(0,a.jsx)(l.Z,{size:15})]})})]})})]}),(0,a.jsx)(i.k,{triggerClassName:"flex min-w-fit w-fit items-center gap-2 font-medium text-sm rounded-2xl bg-secondary px-6 h-10 transition duration-300 ease-in-out hover:bg-secondary-500",arrowClassName:"size-4"})]})]})})}},79330:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var a=s(60926),r=s(11743),i=s.n(r),n=s(26334),o=s(64947);s(29220);let d=function(){return(0,o.jD)(),null};var l=s(43291),c=s(40327);let u=function(){let{data:e,isLoading:t}=(0,l.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1});return t||!e?.data?.active?null:(0,a.jsx)(c.GoogleAnalytics,{gaId:e?.data?.apiKey})};var h=s(82418),m=s(1100);let f=function(){let{data:e,isLoading:t}=(0,l.d)("/external-plugins/tawk-to",{revalidateOnFocus:!1}),{auth:s,isLoading:r}=(0,h.a)();return t||r||!e?.data?.active||s?.role?.name==="Admin"?null:(0,a.jsx)(m.Z,{propertyId:e?.data?.secretKey,widgetId:e?.data?.apiKey})};var p=s(23065),g=s(82844),x=s(19332),v=s(94470),y=s(39228);x.ZP.use(y.Db).use(v.Z).init({interpolation:{escapeValue:!1},lng:"en",fallbackLng:"en",react:{useSuspense:!1},saveMissing:!1,backend:{loadPath:"http://localhost:8000/langs/{{lng}}.json",addPath:"http://localhost:8000/translations/{{lng}}",parse:e=>e,parsePayload:(e,t,s)=>({key:s||""}),request:(e,t,s,a)=>{s?g.default.post(t,s).then(e=>a(null,e)).catch(e=>a(e,null)):g.default.get(t).then(e=>a(null,e)).catch(e=>a(e,null))}}});let b=x.ZP;var w=s(78291);function j({children:e}){let{width:t}=(0,p.B)(),s=(0,o.jD)(),r=t>=640||t<640&&["/contact-supports"].includes(s);return(0,a.jsx)(y.a3,{i18n:b,children:(0,a.jsxs)(w.Z,{children:[e,r&&(0,a.jsx)(f,{}),(0,a.jsx)(u,{})]})})}var N=s(65091),k=s(28160),S=s(32167);function P({children:e}){let{t}=(0,y.$G)(),{data:s,isLoading:r}=(0,l.d)("/settings/global/branding",{revalidateOnFocus:!1});return r?(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsx)("head",{children:(0,a.jsx)("title",{children:t("Loading...")})}),(0,a.jsx)("body",{children:(0,a.jsx)(n.default,{})})]}):(0,a.jsxs)("html",{dir:"ltr",lang:"en",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("title",{children:s?.data?.siteName}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),(0,a.jsx)("link",{rel:"icon",href:(0,N.qR)(s?.data?.favicon),type:"image/png"})]}),(0,a.jsx)("body",{className:`${i().className} ${i().variable} overflow-hidden`,children:(0,a.jsx)(k.w,{height:"3px",color:"#01a79e",options:{showSpinner:!1},shallowRouting:!0,children:(0,a.jsxs)(j,{children:[(0,a.jsx)(S.x,{toastOptions:{closeButton:!0,classNames:{error:"toast bg-danger/90 text-danger-foreground border-danger/40",success:"toast bg-success/90 text-success-foreground border-success/40",warning:"toast bg-warning/90 text-warning-foreground border-warning/40",info:"toast bg-info/90 text-info-foreground border-info/40",closeButton:"bg-background text-foreground border-2"}}}),e,(0,a.jsx)(d,{})]})})})]})}s(96435)},93696:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(60926),r=s(61227),i=s(737),n=s(39228);function o(){let{t:e}=(0,n.$G)();return(0,a.jsx)("div",{className:"flex h-screen overflow-hidden",children:(0,a.jsxs)("div",{className:"flex h-screen flex-1 flex-col overflow-hidden bg-background",children:[(0,a.jsx)(r.w,{path:"/"}),(0,a.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"mb-1 text-[28px] font-medium leading-10 md:text-[32px]",children:"404"}),(0,a.jsx)("h2",{className:"mb-4 w-full text-center",children:e("This page isn't here anymore.")}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("p",{className:"text-center text-sm font-normal text-secondary-text sm:text-base",children:e("You can go back to the home page.")}),(0,a.jsx)(i.Z,{href:"/",className:"text-primary-300 hover:underline",children:e("Go back")})]})]})})]})})}},58387:(e,t,s)=>{"use strict";function a({condition:e,children:t}){return e?t:null}s.d(t,{J:()=>a}),s(29220)},26334:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(60926),r=s(29411),i=s(65091),n=s(39228);function o({className:e}){let{t}=(0,n.$G)();return(0,a.jsx)("div",{className:(0,i.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",e),children:(0,a.jsx)(r.Loader,{title:t("Loading..."),className:"text-foreground"})})}},23560:(e,t,s)=>{"use strict";s.d(t,{k:()=>c});var a=s(60926),r=s(29220),i=s(98019),n=s(23183),o=s(65091),d=s(86059),l=s(39228);function c({disabled:e=!1,triggerClassName:t,arrowClassName:s}){let[c,u]=r.useState(!1),[h,m]=r.useState(JSON.parse(localStorage.getItem("lang")||'{"name": "English", "code": "en"}')),{i18n:f}=(0,l.$G)();r.useEffect(()=>{f.changeLanguage(h?.code)},[f,h]);let p=[{name:"English",code:"en"},{name:"French",code:"fr"}],g=e=>{let t=p.find(t=>t.name===e);m(t),localStorage.setItem("lang",JSON.stringify(t))};return(0,a.jsxs)(n.J2,{open:c,onOpenChange:u,children:[(0,a.jsxs)(n.xo,{disabled:e,className:(0,o.ZP)("flex h-12 w-full items-center justify-between rounded-2xl border-none border-input bg-accent px-3 text-base",t),children:[(0,a.jsx)("div",{className:"flex flex-1 items-center",children:(0,a.jsx)("div",{className:"flex flex-1 items-center gap-2 text-left",children:h?.name})}),(0,a.jsx)(d.Z,{className:(0,o.ZP)("size-6",s)})]}),(0,a.jsx)(n.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:(0,a.jsx)(i.mY,{children:(0,a.jsx)(i.e8,{children:(0,a.jsx)(i.fu,{children:p?.map(e=>a.jsx(i.di,{value:e.name,onSelect:()=>{u(!1),g(e.name)},children:a.jsx("span",{className:"pl-1.5",children:e.name})},e.code))})})})})]})}},29411:(e,t,s)=>{"use strict";s.d(t,{Loader:()=>n});var a=s(60926),r=s(65091),i=s(39228);function n({title:e="Loading...",className:t}){let{t:s}=(0,i.$G)();return(0,a.jsxs)("div",{className:(0,r.ZP)("flex items-center gap-1 text-sm text-foreground",t),children:[(0,a.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{className:"text-inherit",children:s(e)})]})}},36162:(e,t,s)=>{"use strict";s.d(t,{d:()=>d,z:()=>l});var a=s(60926),r=s(62001),i=s(8206),n=s(29220),o=s(65091);let d=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef(({className:e,variant:t,size:s,asChild:i=!1,...n},l)=>{let c=i?r.g7:"button";return(0,a.jsx)(c,{className:(0,o.ZP)(d({variant:t,size:s,className:e})),ref:l,...n})});l.displayName="Button"},98019:(e,t,s)=>{"use strict";s.d(t,{di:()=>m,e8:()=>c,fu:()=>u,mY:()=>d,sZ:()=>l,zz:()=>h});var a=s(60926),r=s(89832),i=s(91144),n=s(29220);s(68870);var o=s(65091);let d=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.mY,{ref:s,className:(0,o.ZP)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...t}));d.displayName=r.mY.displayName;let l=n.forwardRef(({className:e,...t},s)=>(0,a.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[(0,a.jsx)(i.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),(0,a.jsx)(r.mY.Input,{ref:s,className:(0,o.ZP)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...t})]}));l.displayName=r.mY.Input.displayName;let c=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.mY.List,{ref:s,className:(0,o.ZP)("max-h-[300px] overflow-y-auto overflow-x-hidden",e),...t}));c.displayName=r.mY.List.displayName,n.forwardRef((e,t)=>(0,a.jsx)(r.mY.Empty,{ref:t,className:"py-6 text-center text-sm",...e})).displayName=r.mY.Empty.displayName;let u=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.mY.Group,{ref:s,className:(0,o.ZP)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...t}));u.displayName=r.mY.Group.displayName;let h=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.mY.Separator,{ref:s,className:(0,o.ZP)("-mx-1 h-px bg-border",e),...t}));h.displayName=r.mY.Separator.displayName;let m=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.mY.Item,{ref:s,className:(0,o.ZP)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",e),...t}));m.displayName=r.mY.Item.displayName},68870:(e,t,s)=>{"use strict";s.d(t,{$N:()=>p,Be:()=>g,GG:()=>u,Vq:()=>d,cZ:()=>m,fK:()=>f,hg:()=>l});var a=s(60926),r=s(29220),i=s(98144),n=s(55506),o=s(65091);let d=i.fC,l=i.xz,c=i.h_,u=i.x8,h=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.aV,{ref:s,className:(0,o.ZP)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));h.displayName=i.aV.displayName;let m=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(h,{}),(0,a.jsxs)(i.VY,{ref:r,className:(0,o.ZP)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.VY.displayName;let f=({className:e,...t})=>(0,a.jsx)("div",{className:(0,o.ZP)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});f.displayName="DialogHeader";let p=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.Dx,{ref:s,className:(0,o.ZP)("text-lg font-semibold leading-none tracking-tight",e),...t}));p.displayName=i.Dx.displayName;let g=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.dk,{ref:s,className:(0,o.ZP)("text-sm text-muted-foreground",e),...t}));g.displayName=i.dk.displayName},23183:(e,t,s)=>{"use strict";s.d(t,{J2:()=>o,xo:()=>d,yk:()=>l});var a=s(60926),r=s(29220),i=s(49642),n=s(65091);let o=i.fC,d=i.xz,l=r.forwardRef(({className:e,align:t="center",sideOffset:s=4,...r},o)=>(0,a.jsx)(i.h_,{children:(0,a.jsx)(i.VY,{ref:o,align:t,sideOffset:s,className:(0,n.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));l.displayName=i.VY.displayName},74988:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var a=s(60926),r=s(60332),i=s(29220),n=s(65091);let o=i.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...i},o)=>(0,a.jsx)(r.f,{ref:o,decorative:s,orientation:t,className:(0,n.ZP)("shrink-0 bg-divider","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...i}));o.displayName=r.f.displayName;let d=o},78291:(e,t,s)=>{"use strict";s.d(t,{Z:()=>p,O:()=>f});var a=s(60926),r=s(26334);s(5303);var i=(0,s(30335).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),n=s(43291),o=s(1181),d=s(24013),l=s(12393);class c{constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}printLocation(){return`${this.city}, ${this.country} (${this.lat}, ${this.lon})`}}var u=s(64947),h=s(29220);let m=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),f=()=>h.useContext(m);function p({children:e}){let[t,s]=h.useState("Desktop"),[f,p]=h.useState(!1),[g,x]=h.useState(),{data:v,isLoading:y,error:b,mutate:w}=(0,n.d)("/auth/check",{revalidateOnFocus:!1}),{data:j,isLoading:N}=(0,n.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:k,isLoading:S}=(0,n.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),P=(0,u.tv)(),A=(0,u.jD)();h.useEffect(()=>{(async()=>{s((await i()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;s(e<768?"Mobile":e<1024?"Tablet":"Desktop"),p(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");x(new c(e))}catch{}})()},[]),h.useLayoutEffect(()=>{b&&!d.sp.includes(A)&&P.push("/signin")},[b]);let C=h.useMemo(()=>({isAuthenticate:!!v?.data?.login,auth:v?.data?.user?new l.n(v?.data?.user):null,isLoading:y,deviceLocation:g,refreshAuth:()=>w(v),isExpanded:f,device:t,setIsExpanded:p,branding:j?.data,googleAnalytics:k?.data?{active:k?.data.active,apiKey:k?.data.apiKey}:{active:!1,apiKey:""}}),[v,g,f,t]),L=!y&&!N&&!S;return(0,a.jsx)(m.Provider,{value:C,children:L?e:(0,a.jsx)(r.default,{})})}},82418:(e,t,s)=>{"use strict";s.d(t,{a:()=>r});var a=s(78291);let r=()=>{let e=(0,a.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},840:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});var a=s(78291);let r=()=>{let{branding:e}=(0,a.O)();return e}},23065:(e,t,s)=>{"use strict";s.d(t,{B:()=>r});var a=s(29220);let r=()=>{let[e,t]=a.useState(0),[s,r]=a.useState(0);function i(){window&&(t(window.innerWidth),r(window.innerHeight))}return a.useEffect(()=>(i(),window.addEventListener("resize",i),()=>window.removeEventListener("resize",i)),[]),{width:e,height:s}}},43291:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var a=s(1181),r=s(32898);let i=(e,t)=>(0,r.ZP)(e||null,e=>a.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},1181:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(24013);let r=s(82844).default.create({baseURL:a.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},24013:(e,t,s)=>{"use strict";s.d(t,{rH:()=>a,sp:()=>r});let a={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:process.env.SESSION_SECRET,get STATIC_URL(){return`${this.API_URL}/uploads`}},r=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},65091:(e,t,s)=>{"use strict";s.d(t,{F:()=>c,Fg:()=>h,Fp:()=>l,ZP:()=>o,fl:()=>d,qR:()=>u,w4:()=>m});var a=s(24013),r=s(65116),i=s(32167),n=s(99576);function o(...e){return(0,n.m6)((0,r.W)(e))}function d(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let l=e=>{e&&navigator.clipboard.writeText(e).then(()=>i.toast.success("Copied to clipboard!")).catch(()=>{i.toast.error("Failed to copy!")})};class c{constructor(e){this.formatter=(e,t)=>{let s;let a=void 0===t?this.currencyCode:t;try{s=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch{s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let r=s.formatToParts(e),i=r.find(e=>"currency"===e.type)?.value??a,n=s.format(e),o=n.substring(i.length).trim();return{currencyCode:a,currencySymbol:i,formattedAmount:n,amountText:o}},this.currencyCode=e||"USD"}format(e,t){let{currencyCode:s,amountText:a}=this.formatter(e,t);return`${a} ${s}`}formatVC(e,t){let{currencyCode:s,amountText:a}=this.formatter(e,t);return`${a} ${s} `}}let u=e=>e?`${a.rH.STATIC_URL}/${e}`:"",h=e=>e?e?.match(/^\+/)?e:`+${e}`:"",m=(e,t="search")=>{let s=new URLSearchParams(window?.location?.search);return e?s.set(t,e):s.delete(t),s}},73244:(e,t,s)=>{"use strict";s.d(t,{k:()=>a});class a{constructor(e){this.id=e?.id,this.city=e?.city,this.countryCode=e?.countryCode,this.addressLine=e?.addressLine,this.street=e?.street,this.type=e?.type,this.zipCode=e?.zipCode,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}}},12393:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});class a{constructor(e){this.id=e?.id,this.userId=e?.userId,this.agentId=e?.agentId,this.name=e?.name,this.email=e?.email,this.occupation=e?.occupation,this.status=e?.status,this.isRecommended=!!e?.isRecommended,this.isSuspend=!!e?.isSuspend,this.proof=e?.proof,this.depositFee=e?.depositFee,this.withdrawFee=e?.withdrawFee,this.withdrawCommission=e?.withdrawCommission,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}}var r=s(74190);class i{constructor(e){this.id=e?.id,this.userId=e?.userId,this.merchantId=e?.merchantId,this.name=e?.name,this.email=e?.email,this.status=e?.status,this.isSuspend=e?.isSuspend,this.proof=e?.proof,this.depositFee=e?.depositFee,this.withdrawFee=e?.withdrawFee,this.webhookUrl=e?.webhookUrl,this.allowedIp=e?.allowedIp,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}}var n=s(73146),o=s(24013);class d{constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?`${o.rH.APP_URL}/register?referral=${this.referralCode}`:"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new n.u(e.role),this.permission=e.permission,this.customer=e?.customer?new r.O(e.customer):void 0,this.merchant=e?.merchant?new i(e.merchant):void 0,this.agent=e?.agent?new a(e.agent):void 0}canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}}},74190:(e,t,s)=>{"use strict";s.d(t,{O:()=>r});var a=s(73244);class r{constructor(e){this.id=e?.id,this.userId=e?.userId,this.name=e?.name,this.email=e?.user?.email,this.phone=e?.phone?.match(/^\+/)?e.phone:`+${e?.phone}`,this.gender=e?.gender,this.dob=new Date(e?.dob),this.avatar=e?.profileImage,this.address=new a.k(e?.address),this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}}},73146:(e,t,s)=>{"use strict";s.d(t,{u:()=>a});class a{constructor(e){this.id=e?.id,this.name=e?.name,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}}},57665:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\layout.tsx#default`)},38694:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(42416),r=s(26339);function i(){return(0,a.jsx)(r.Z,{})}},4710:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\not-found.tsx#default`)},26339:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\GlobalLoader.tsx#default`)},21237:(e,t,s)=>{"use strict";s.d(t,{a:()=>a});let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\Loader.tsx#Loader`)},72220:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$ACTION_0:()=>o,agentDevice:()=>n});var a=s(28201);s(85974);var r=s(86007),i=s(85845);let n=(0,a.j)("58f67a650e6daf4f61edd9160d8ad96f0e6c3504",o);async function o(){let e=(0,r.A)().get("user-agent")||"",t=/mobile|iphone|ipod|android|blackberry|iemobile|opera mini/i.test(e),s=/ipad|tablet|playbook|silk/i.test(e);return{userAgent:e,deviceType:t?"Mobile":s?"Tablet":"Desktop"}}(0,i.h)([n]),(0,a.j)("56c3f2e139a4c6bb157d9b7969ec956e3494f518",n)},96435:()=>{}}]);
//# sourceMappingURL=7283.js.map