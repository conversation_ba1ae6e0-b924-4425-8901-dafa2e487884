"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[56219],{22291:function(e,r,t){t.d(r,{Z:function(){return m}});var n=t(74677),o=t(2265),a=t(40718),l=t.n(a),i=["variant","color","size"],u=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:r,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},c=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},s=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:r,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),o.createElement("path",{fill:r,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},d=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},f=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:r,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},p=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},v=function(e,r){switch(e){case"Bold":return o.createElement(u,{color:r});case"Broken":return o.createElement(c,{color:r});case"Bulk":return o.createElement(s,{color:r});case"Linear":default:return o.createElement(d,{color:r});case"Outline":return o.createElement(f,{color:r});case"TwoTone":return o.createElement(p,{color:r})}},m=(0,o.forwardRef)(function(e,r){var t=e.variant,a=e.color,l=e.size,u=(0,n._)(e,i);return o.createElement("svg",(0,n.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),v(t,a))});m.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="ArrowRight2"},79205:function(e,r,t){t.d(r,{Z:function(){return u}});var n=t(2265);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:u,className:c="",children:s,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:r,...l,width:o,height:o,stroke:t,strokeWidth:u?24*Number(i)/Number(o):i,className:a("lucide",c),...f},[...d.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(s)?s:[s]])}),u=(e,r)=>{let t=(0,n.forwardRef)((t,l)=>{let{className:u,...c}=t;return(0,n.createElement)(i,{ref:l,iconNode:r,className:a("lucide-".concat(o(e)),u),...c})});return t.displayName="".concat(e),t}},40519:function(e,r,t){t.d(r,{Z:function(){return n}});let n=(0,t(79205).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},42325:function(e,r,t){t.d(r,{ck:function(){return W},fC:function(){return B},z$:function(){return P}});var n=t(2265),o=t(6741),a=t(98575),l=t(73966),i=t(66840),u=t(1353),c=t(80886),s=t(29114),d=t(90420),f=t(6718),p=t(71599),v=t(57437),m="Radio",[h,w]=(0,l.b)(m),[k,g]=h(m),b=n.forwardRef((e,r)=>{let{__scopeRadio:t,name:l,checked:u=!1,required:c,disabled:s,value:d="on",onCheck:f,form:p,...m}=e,[h,w]=n.useState(null),g=(0,a.e)(r,e=>w(e)),b=n.useRef(!1),y=!h||p||!!h.closest("form");return(0,v.jsxs)(k,{scope:t,checked:u,disabled:s,children:[(0,v.jsx)(i.WV.button,{type:"button",role:"radio","aria-checked":u,"data-state":x(u),"data-disabled":s?"":void 0,disabled:s,value:d,...m,ref:g,onClick:(0,o.M)(e.onClick,e=>{u||null==f||f(),y&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),y&&(0,v.jsx)(R,{control:h,bubbles:!b.current,name:l,value:d,checked:u,required:c,disabled:s,form:p,style:{transform:"translateX(-100%)"}})]})});b.displayName=m;var y="RadioIndicator",E=n.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:n,...o}=e,a=g(y,t);return(0,v.jsx)(p.z,{present:n||a.checked,children:(0,v.jsx)(i.WV.span,{"data-state":x(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:r})})});E.displayName=y;var R=n.forwardRef((e,r)=>{let{__scopeRadio:t,control:o,checked:l,bubbles:u=!0,...c}=e,s=n.useRef(null),p=(0,a.e)(s,r),m=(0,f.D)(l),h=(0,d.t)(o);return n.useEffect(()=>{let e=s.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==l&&r){let t=new Event("click",{bubbles:u});r.call(e,l),e.dispatchEvent(t)}},[m,l,u]),(0,v.jsx)(i.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:p,style:{...c.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}R.displayName="RadioBubbleInput";var L=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],M="RadioGroup",[A,j]=(0,l.b)(M,[u.Pc,w]),C=(0,u.Pc)(),F=w(),[T,I]=A(M),z=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:n,defaultValue:o,value:a,required:l=!1,disabled:d=!1,orientation:f,dir:p,loop:m=!0,onValueChange:h,...w}=e,k=C(t),g=(0,s.gm)(p),[b,y]=(0,c.T)({prop:a,defaultProp:null!=o?o:"",onChange:h,caller:M});return(0,v.jsx)(T,{scope:t,name:n,required:l,disabled:d,value:b,onValueChange:y,children:(0,v.jsx)(u.fC,{asChild:!0,...k,orientation:f,dir:g,loop:m,children:(0,v.jsx)(i.WV.div,{role:"radiogroup","aria-required":l,"aria-orientation":f,"data-disabled":d?"":void 0,dir:g,...w,ref:r})})})});z.displayName=M;var D="RadioGroupItem",N=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:l,...i}=e,c=I(D,t),s=c.disabled||l,d=C(t),f=F(t),p=n.useRef(null),m=(0,a.e)(r,p),h=c.value===i.value,w=n.useRef(!1);return n.useEffect(()=>{let e=e=>{L.includes(e.key)&&(w.current=!0)},r=()=>w.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,v.jsx)(u.ck,{asChild:!0,...d,focusable:!s,active:h,children:(0,v.jsx)(b,{disabled:s,required:c.required,checked:h,...f,...i,name:c.name,ref:m,onCheck:()=>c.onValueChange(i.value),onKeyDown:(0,o.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.M)(i.onFocus,()=>{var e;w.current&&(null===(e=p.current)||void 0===e||e.click())})})})});N.displayName=D;var S=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...n}=e,o=F(t);return(0,v.jsx)(E,{...o,...n,ref:r})});S.displayName="RadioGroupIndicator";var B=z,W=N,P=S},1353:function(e,r,t){t.d(r,{Pc:function(){return y},ck:function(){return T},fC:function(){return F}});var n=t(2265),o=t(6741),a=t(58068),l=t(98575),i=t(73966),u=t(99255),c=t(66840),s=t(26606),d=t(80886),f=t(29114),p=t(57437),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,k,g]=(0,a.B)(h),[b,y]=(0,i.b)(h,[g]),[E,R]=b(h),x=n.forwardRef((e,r)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(L,{...e,ref:r})})}));x.displayName=h;var L=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:a,loop:i=!1,dir:u,currentTabStopId:w,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:b,onEntryFocus:y,preventScrollOnEntryFocus:R=!1,...x}=e,L=n.useRef(null),M=(0,l.e)(r,L),A=(0,f.gm)(u),[j,F]=(0,d.T)({prop:w,defaultProp:null!=g?g:null,onChange:b,caller:h}),[T,I]=n.useState(!1),z=(0,s.W)(y),D=k(t),N=n.useRef(!1),[S,B]=n.useState(0);return n.useEffect(()=>{let e=L.current;if(e)return e.addEventListener(v,z),()=>e.removeEventListener(v,z)},[z]),(0,p.jsx)(E,{scope:t,orientation:a,dir:A,loop:i,currentTabStopId:j,onItemFocus:n.useCallback(e=>F(e),[F]),onItemShiftTab:n.useCallback(()=>I(!0),[]),onFocusableItemAdd:n.useCallback(()=>B(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>B(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:T||0===S?-1:0,"data-orientation":a,...x,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let r=!N.current;if(e.target===e.currentTarget&&r&&!T){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=D().filter(e=>e.focusable);C([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),R)}}N.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>I(!1))})})}),M="RovingFocusGroupItem",A=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:l=!1,tabStopId:i,children:s,...d}=e,f=(0,u.M)(),v=i||f,m=R(M,t),h=m.currentTabStopId===v,g=k(t),{onFocusableItemAdd:b,onFocusableItemRemove:y,currentTabStopId:E}=m;return n.useEffect(()=>{if(a)return b(),()=>y()},[a,b,y]),(0,p.jsx)(w.ItemSlot,{scope:t,id:v,focusable:a,active:l,children:(0,p.jsx)(c.WV.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...d,ref:r,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return j[o]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)o.reverse();else if("prev"===r||"next"===r){var t,n;"prev"===r&&o.reverse();let a=o.indexOf(e.currentTarget);o=m.loop?(t=o,n=a+1,t.map((e,r)=>t[(n+r)%t.length])):o.slice(a+1)}setTimeout(()=>C(o))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=E}):s})})});A.displayName=M;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function C(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var F=x,T=A},6718:function(e,r,t){t.d(r,{D:function(){return o}});var n=t(2265);function o(e){let r=n.useRef({value:e,previous:e});return n.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},90420:function(e,r,t){t.d(r,{t:function(){return a}});var n=t(2265),o=t(61188);function a(e){let[r,t]=n.useState(void 0);return(0,o.b)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let r=new ResizeObserver(r=>{let n,o;if(!Array.isArray(r)||!r.length)return;let a=r[0];if("borderBoxSize"in a){let e=a.borderBoxSize,r=Array.isArray(e)?e[0]:e;n=r.inlineSize,o=r.blockSize}else n=e.offsetWidth,o=e.offsetHeight;t({width:n,height:o})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}t(void 0)},[e]),r}}}]);