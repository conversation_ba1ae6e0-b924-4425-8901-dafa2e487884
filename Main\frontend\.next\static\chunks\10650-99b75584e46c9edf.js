"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[10650],{41709:function(e,t,s){function a(e){let{condition:t,children:s}=e;return t?s:null}s.d(t,{J:function(){return a}}),s(2265)},3244:function(e,t,s){s.d(t,{X:function(){return o}});var a=s(57437),n=s(62869),r=s(77376),l=s(22291),i=s(27648),d=s(43949);function o(e){let{isVerified:t=!1,documentStatus:s}=e,{t:o}=(0,d.$G)();return t?null:(0,a.jsxs)("div",{className:"flex h-[182px] w-[350px] flex-col items-center justify-center rounded-2xl border border-primary bg-background p-4",children:[(0,a.jsx)(r.Z,{variant:"Bulk",className:"mb-2.5 text-important",size:64}),(0,a.jsx)("h6",{className:"font-bold",children:o("Awaiting KYC verification")}),"not submitted"===s?(0,a.jsx)(n.z,{className:"mt-auto w-full gap-[2px] rounded-lg px-4 py-2 text-base font-medium leading-[22px]",asChild:!0,children:(0,a.jsxs)(i.default,{href:"/settings/kyc-verification-settings",children:[o("Submit Documents"),(0,a.jsx)(l.Z,{size:16})]})}):null]})}},96613:function(e,t,s){s.d(t,{P:function(){return Z}});var a=s(57437),n=s(37099),r=s(62869),l=s(66070),i=s(26110),d=s(6512),o=s(1828),c=s(83277),u=s(32268),m=s(80167),x=s(3612),f=s(21251),p=s(17897),h=s(94508),v=s(80034),j=s(25856),g=s(54882),y=s(8877),N=s(47480),b=s(27648),w=s(43949),C=s(14438);function Z(e){var t;let{card:s,title:n,balance:r,currency:i,walletId:d,onMutate:o}=e,{t:c}=(0,w.$G)(),{settings:u}=(0,p.h)();return i&&(0,a.jsxs)(l.Zb,{className:"w-[350px] overflow-hidden rounded-2xl border-primary bg-gradient-to-b from-[#48E1D8] to-primary",children:[(0,a.jsxs)(l.aY,{className:"relative overflow-hidden px-6 py-4",children:[(0,a.jsx)("h2",{className:"text-shadow pointer-events-none absolute bottom-3 right-0 text-[104px] font-bold text-primary opacity-30",children:i}),(0,a.jsx)("div",{className:"relative z-20 mb-4 flex items-center gap-2.5",children:(0,a.jsx)("h6",{className:"font-bold text-primary-foreground",children:n})}),(0,a.jsxs)("div",{className:"relative z-20 text-primary-foreground",children:[(0,a.jsx)("span",{className:"text-xs font-normal",children:c("Balance")}),(0,a.jsxs)("h1",{className:"flex items-center gap-1 align-baseline text-[32px] font-semibold leading-10",children:[Number(r).toFixed(2),(0,a.jsx)("span",{className:"text-sm font-normal leading-5",children:i})]})]})]}),(null==u?void 0:null===(t=u.virtual_card)||void 0===t?void 0:t.status)==="on"&&(0,a.jsx)(l.eW,{className:"justify-end bg-primary px-6 py-2 text-primary-foreground",children:(null==s?void 0:s.lastFour)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex-1 text-sm font-normal leading-5",children:["**** **** **** ",null==s?void 0:s.lastFour]}),(0,a.jsx)(A,{card:s,balance:r,currency:i,onMutate:o})]}):(0,a.jsx)(D,{walletId:d,currency:i,onMutate:o})})]})}function D(e){let{walletId:t,currency:s,onMutate:l=()=>{}}=e,{t:i}=(0,w.$G)();return(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.vW,{asChild:!0,children:(0,a.jsxs)(r.z,{size:"sm",disabled:(null==s?void 0:s.toUpperCase())!=="USD"&&(null==s?void 0:s.toUpperCase())!=="NGN",className:"text-sm font-semibold leading-5 opacity-100 hover:opacity-90",children:[(0,a.jsx)(v.Z,{size:"20"}),(0,a.jsx)("span",{children:(null==s?void 0:s.toUpperCase())==="USD"||(null==s?void 0:s.toUpperCase())==="NGN"?i("Issue Card"):i("Card Not Available")})]})}),(0,a.jsxs)(n._T,{children:[(0,a.jsxs)(n.fY,{children:[(0,a.jsx)(n.f$,{children:i("Confirm Your Card")}),(0,a.jsx)(n.yT,{children:i("Are you sure you want to issue a card for this wallet?")})]}),(0,a.jsxs)(n.xo,{children:[(0,a.jsx)(n.le,{children:i("Cancel")}),(0,a.jsx)(n.OL,{onClick:()=>{C.toast.promise((0,u.m)(t),{loading:i("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return l(),e.message},error:e=>e.message})},children:i("Issue Card")})]})]})]})}function A(e){var t;let{card:s,balance:n,currency:l,onMutate:u=()=>{}}=e,{cardBg:p}=(0,f.T)(),{t:v}=(0,w.$G)(),{auth:Z}=(0,x.a)(),D=e=>{C.toast.promise((0,m.a)({cardId:s.id,dataList:{status:e}}),{loading:v("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return u(),e.message},error:e=>e.message})},A=()=>{C.toast.promise((0,c.f)({cardId:s.id}),{loading:v("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return u(),e.message},error:e=>e.message})};return(0,a.jsxs)(i.Vq,{children:[(0,a.jsx)(i.hg,{asChild:!0,children:(0,a.jsxs)(r.z,{size:"sm",className:"text-sm font-semibold leading-5 opacity-100 hover:opacity-90",children:[(0,a.jsx)(j.Z,{size:"20"}),(0,a.jsx)("span",{children:v("View Card")})]})}),(0,a.jsxs)(i.cZ,{className:"sm:max-w-[525px]",children:[(0,a.jsx)(i.$N,{children:v("Card Details")}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{style:{backgroundImage:"url(".concat((0,h.qR)(p),")")},className:"mb-5 flex min-h-[280px] w-full max-w-[450px] flex-col justify-end gap-7 rounded-3xl bg-cover p-7",children:[(0,a.jsx)("p",{className:"text-[28px] font-semibold text-white",children:s.number.replace(/(\d{4})(?=\d)/g,"$1 ")}),(0,a.jsxs)("div",{className:"flex items-center gap-8",children:[(0,a.jsxs)("div",{className:"text-white",children:[(0,a.jsx)("p",{className:"text-sm",children:v("Card holder name")}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:null==Z?void 0:null===(t=Z.customer)||void 0===t?void 0:t.name})]}),(0,a.jsxs)("div",{className:"text-white",children:[(0,a.jsx)("p",{className:"text-sm",children:v("Expiry date")}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:[s.expMonth.toString().padStart(2,"0"),"/",4===s.expYear.toString().length?s.expYear.toString().slice(2):s.expYear.toString()]})]}),(0,a.jsxs)("div",{className:"text-white",children:[(0,a.jsx)("p",{className:"text-sm",children:v("CVV")}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:s.cvc})]})]})]}),(0,a.jsxs)("div",{className:"mb-5 flex gap-8",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>(0,h.Fp)(s.number),className:"flex flex-col items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:(0,a.jsx)(g.Z,{size:"24",color:"#000"})}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:v("Copy Number")})]}),(0,a.jsxs)(b.default,{href:"/deposit",className:"flex flex-col items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:(0,a.jsx)(y.Z,{size:"24",color:"#000"})}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:v("Deposit Money")})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>A(),className:"flex flex-col items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary text-black transition duration-300 ease-in-out hover:bg-spacial-red-foreground hover:text-danger",children:(0,a.jsx)(N.Z,{size:"24"})}),(0,a.jsx)("span",{className:"text-xs font-semibold",children:v("Close Card")})]})]}),(0,a.jsx)(d.Z,{className:"mb-5 border-b bg-transparent"}),(0,a.jsxs)("div",{className:"w-full space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:v("Status")}),(0,a.jsx)(o.Z,{defaultChecked:"active"===s.status,onCheckedChange:e=>{D(e?"active":"inactive")}})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:v("Balance")}),(0,a.jsxs)("span",{className:"text-sm font-semibold",children:[n," ",l]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:v("Card Type")}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:null==s?void 0:s.brand})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:v("Expiry Date")}),(0,a.jsxs)("span",{className:"text-sm font-semibold",children:[s.expMonth.toString().padStart(2,"0"),"/",4===s.expYear.toString().length?s.expYear.toString().slice(2):s.expYear.toString()]})]})]})]})]})]})}},37099:function(e,t,s){s.d(t,{OL:function(){return v},_T:function(){return m},aR:function(){return d},f$:function(){return p},fY:function(){return x},le:function(){return j},vW:function(){return o},xo:function(){return f},yT:function(){return h}});var a=s(57437),n=s(90277),r=s(2265),l=s(62869),i=s(94508);let d=n.fC,o=n.xz,c=n.h_,u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.aV,{className:(0,i.ZP)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r,ref:t})});u.displayName=n.aV.displayName;let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsx)(n.VY,{ref:t,className:(0,i.ZP)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...r})]})});m.displayName=n.VY.displayName;let x=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.ZP)("flex flex-col space-y-2 text-center sm:text-left",t),...s})};x.displayName="AlertDialogHeader";let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.ZP)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};f.displayName="AlertDialogFooter";let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.Dx,{ref:t,className:(0,i.ZP)("text-lg font-semibold",s),...r})});p.displayName=n.Dx.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.dk,{ref:t,className:(0,i.ZP)("text-sm text-muted-foreground",s),...r})});h.displayName=n.dk.displayName;let v=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.aU,{ref:t,className:(0,i.ZP)((0,l.d)(),s),...r})});v.displayName=n.aU.displayName;let j=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.$j,{ref:t,className:(0,i.ZP)((0,l.d)({variant:"outline"}),"mt-2 sm:mt-0",s),...r})});j.displayName=n.$j.displayName},66070:function(e,t,s){s.d(t,{Ol:function(){return i},SZ:function(){return o},Zb:function(){return l},aY:function(){return c},eW:function(){return u},ll:function(){return d}});var a=s(57437),n=s(2265),r=s(94508);let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...n})});l.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.ZP)("flex flex-col space-y-1.5 p-6",s),...n})});i.displayName="CardHeader";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("h3",{ref:t,className:(0,r.ZP)("text-2xl font-semibold leading-none tracking-tight",s),...n})});d.displayName="CardTitle";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("p",{ref:t,className:(0,r.ZP)("text-sm text-muted-foreground",s),...n})});o.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.ZP)("p-6 pt-0",s),...n})});c.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.ZP)("flex items-center p-6 pt-0",s),...n})});u.displayName="CardFooter"},93022:function(e,t,s){s.d(t,{O:function(){return r}});var a=s(57437),n=s(94508);function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,n.ZP)("animate-pulse rounded-md bg-muted",t),...s})}},32268:function(e,t,s){s.d(t,{m:function(){return r}});var a=s(79981),n=s(97751);async function r(e){try{let t=await a.Z.post("/cards/generate-virtual",{walletId:e});return(0,n.B)(t)}catch(e){return(0,n.D)(e)}}},17897:function(e,t,s){s.d(t,{h:function(){return n}});var a=s(31117);function n(){let{data:e,...t}=(0,a.d)("/settings/global");return{settings:null==e?void 0:e.data,...t}}},48358:function(e,t,s){s.d(t,{r:function(){return l}});var a=s(79981),n=s(54763),r=s(85323);function l(){var e,t;let{data:s,isLoading:l,mutate:i}=(0,r.ZP)("/wallets",e=>a.Z.get(e));return{wallets:null!==(t=null==s?void 0:null===(e=s.data)||void 0===e?void 0:e.map(e=>new n.w(e)))&&void 0!==t?t:[],isLoading:l,getWalletByCurrencyCode:(e,t)=>null==e?void 0:e.find(e=>{var s;return(null==e?void 0:null===(s=e.currency)||void 0===s?void 0:s.code)===t}),mutate:i}}},28315:function(e,t,s){s.d(t,{F:function(){return a}});class a{format(e){let{currencySymbol:t,amountText:s}=this.formatter(e);return"".concat(s," ").concat(t)}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}constructor(e){var t;this.formatter=e=>{var t,s;let a=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),n=null!==(s=null===(t=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===t?void 0:t.value)&&void 0!==s?s:this.code,r=a.format(e),l=r.substring(n.length).trim();return{currencyCode:this.code,currencySymbol:n,formattedAmount:r,amountText:l}},this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.code=null==e?void 0:e.code,this.logo=null!==(t=null==e?void 0:e.logo)&&void 0!==t?t:"",this.usdRate=null==e?void 0:e.usdRate,this.acceptApiRate=!!(null==e?void 0:e.acceptApiRate),this.isCrypto=!!(null==e?void 0:e.isCrypto),this.active=!!(null==e?void 0:e.active),this.metaData=null==e?void 0:e.metaData,this.minAmount=null==e?void 0:e.minAmount,this.kycLimit=null==e?void 0:e.kycLimit,this.maxAmount=null==e?void 0:e.maxAmount,this.dailyTransferAmount=null==e?void 0:e.dailyTransferAmount,this.dailyTransferLimit=null==e?void 0:e.dailyTransferLimit,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},54763:function(e,t,s){s.d(t,{w:function(){return r}});var a=s(502),n=s(28315);class r{constructor(e){var t;this.id=null==e?void 0:e.id,this.walletId=null==e?void 0:e.walletId,this.logo=null==e?void 0:e.logo,this.userId=null==e?void 0:e.userId,this.balance=null==e?void 0:e.balance,this.defaultStatus=!!(null==e?void 0:e.default),this.pinDashboard=!!(null==e?void 0:e.pinDashboard),this.currencyId=null==e?void 0:e.currencyId,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.currency=new n.F(null==e?void 0:e.currency),this.cards=null==e?void 0:null===(t=e.cards)||void 0===t?void 0:t.map(e=>new a.Z(e))}}}}]);