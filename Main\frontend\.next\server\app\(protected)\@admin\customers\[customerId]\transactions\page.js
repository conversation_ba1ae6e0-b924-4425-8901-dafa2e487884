(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5017],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},23533:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>N,default:()=>L});var a,r={};s.r(r),s.d(r,{AppRouter:()=>m.WY,ClientPageRoot:()=>m.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>m.yO,NotFoundBoundary:()=>m.O4,Postpone:()=>m.hQ,RenderFromTemplateContext:()=>m.b5,__next_app__:()=>x,actionAsyncStorage:()=>m.Wz,createDynamicallyTrackedSearchParams:()=>m.rL,createUntrackedSearchParams:()=>m.S5,decodeAction:()=>m.Hs,decodeFormState:()=>m.dH,decodeReply:()=>m.kf,originalPathname:()=>g,pages:()=>f,patchFetch:()=>m.XH,preconnect:()=>m.$P,preloadFont:()=>m.C5,preloadStyle:()=>m.oH,renderToReadableStream:()=>m.aW,requestAsyncStorage:()=>m.Fg,routeModule:()=>S,serverHooks:()=>m.GP,staticGenerationAsyncStorage:()=>m.AT,taintObjectReference:()=>m.nr,tree:()=>p}),s(67206);var n=s(79319),i=s(20518),o=s(61902),c=s(62042),l=s(44630),d=s(44828),u=s(65505),m=s(13839);let p=["",{children:["(protected)",{admin:["children",{children:["customers",{children:["[customerId]",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,32336)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\transactions\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,35032)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\transactions\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,38520)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,96104)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,78174)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,73081)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],f=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\transactions\\page.tsx"],g="/(protected)/@admin/customers/[customerId]/transactions/page",x={require:s,loadChunk:()=>Promise.resolve()},S=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/customers/[customerId]/transactions/page",pathname:"/customers/[customerId]/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var h=s(69094),E=s(5787),P=s(90527);let v=e=>e?JSON.parse(e):void 0,D=self.__BUILD_MANIFEST,A=v(self.__REACT_LOADABLE_MANIFEST),b=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/customers/[customerId]/transactions/page"],I=v(self.__RSC_SERVER_MANIFEST),_=v(self.__NEXT_FONT_MANIFEST),j=v(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];b&&I&&(0,E.Mo)({clientReferenceManifest:b,serverActionsManifest:I,serverModuleMap:(0,P.w)({serverActionsManifest:I,pageName:"/(protected)/@admin/customers/[customerId]/transactions/page"})});let y=(0,i.d)({pagesType:h.s.APP,dev:!1,page:"/(protected)/@admin/customers/[customerId]/transactions/page",appMod:null,pageMod:r,errorMod:null,error500Mod:null,Document:null,buildManifest:D,renderToHTML:c.f,reactLoadableManifest:A,clientReferenceManifest:b,serverActionsManifest:I,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:_,incrementalCacheHandler:null,interceptionRouteRewrites:j}),N=r;function L(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:y})}},1212:(e,t,s)=>{Promise.resolve().then(s.bind(s,49666))},97187:(e,t,s)=>{Promise.resolve().then(s.bind(s,73349))},49666:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E,runtime:()=>h});var a=s(60926),r=s(14579),n=s(30417),i=s(89551),o=s(53042),c=s(44788),l=s(38071),d=s(28531),u=s(5764),m=s(47020),p=s(737),f=s(64947);s(29220);var g=s(39228),x=s(32167),S=s(91500);let h="edge";function E({children:e}){let t=(0,f.UO)(),s=(0,f.lr)(),h=(0,f.tv)(),E=(0,f.jD)(),{t:P}=(0,g.$G)(),v=[{title:P("Account Details"),icon:(0,a.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}?${s.toString()}`,id:"__DEFAULT__"},{title:P("Transactions"),icon:(0,a.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/transactions?${s.toString()}`,id:"transactions"},{title:P("KYC"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/kyc?${s.toString()}`,id:"kyc"},{title:P("Permissions"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/permissions?${s.toString()}`,id:"permissions"},{title:P("Send Email"),icon:(0,a.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/send-email?${s.toString()}`,id:"send-email"}],D=1===Number(s.get("active"));return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,a.jsx)("li",{children:(0,a.jsxs)(p.Z,{href:"/customers",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,a.jsx)(m.Z,{className:"size-4 sm:size-6"}),P("Back")]})}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",s.get("name")]}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",P("User")," #",t.customerId]})]}),(0,a.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,a.jsx)("span",{children:P("Active")}),(0,a.jsx)(n.Z,{className:"data-[state=unchecked]:bg-muted",defaultChecked:D,onCheckedChange:e=>{x.toast.promise((0,i.z)(t.customerId),{loading:P("Loading..."),success:a=>{if(!a.status)throw Error(a.message);let r=new URLSearchParams(s);return r.set("active",e?"1":"0"),(0,S.j)(`/admin/customers/${t.customerId}`),h.push(`${E}?${r.toString()}`),a.message},error:e=>e.message})}})]})]}),(0,a.jsx)(r.a,{tabs:v})]}),e]})}},73349:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(60926),r=s(3770),n=s(78133),i=s(44942),o=s(69226),c=s(20293),l=s(58904),d=s(74988),u=s(43291),m=s(98903),p=s(65091),f=s(32917),g=s(34870),x=s(65694),S=s(48132),h=s(64947),E=s(29220),P=s(39228);function v(){let e=(0,h.lr)(),t=(0,h.tv)(),s=(0,h.UO)(),v=(0,h.jD)(),{t:D}=(0,P.$G)(),[A,b]=E.useState(""),{data:I,meta:_,isLoading:j,filter:y}=(0,m.Z)(`/admin/transactions/${s.customerId}?${e.toString()}`),{data:N,isLoading:L}=(0,u.d)(`/admin/transactions/counts/${s.customerId}`);return(0,a.jsxs)("div",{className:"h-full p-4",children:[(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-12 gap-4",children:[(0,a.jsx)(i.x,{value:N?.data?.deposit,title:D("Total Deposit"),icon:e=>(0,a.jsx)(f.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",statusClass:"text-spacial-green",iconClass:"bg-spacial-green-foreground",isLoading:L}),(0,a.jsx)(i.x,{value:N?.data?.withdraw,title:D("Total Withdraw"),icon:e=>(0,a.jsx)(g.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-red-foreground text-spacial-red",statusClass:"text-spacial-red",isLoading:L}),(0,a.jsx)(i.x,{value:N?.data?.transfer,title:D("Total Transfers"),icon:e=>(0,a.jsx)(x.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-blue-foreground text-spacial-blue",statusClass:"text-spacial-blue",isLoading:L}),(0,a.jsx)(i.x,{value:N?.data?.exchange,title:D("Total Exchange"),icon:e=>(0,a.jsx)(S.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",isLoading:L})]}),(0,a.jsxs)("div",{className:"h-fit w-full overflow-auto rounded-xl bg-background p-6 shadow-default",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,a.jsx)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:(0,a.jsx)(l.Z,{filter:y})}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,a.jsx)(n.R,{value:A,onChange:e=>{e.preventDefault();let s=(0,p.w4)(e.target.value);b(e.target.value),t.replace(`${v}?${s.toString()}`)},iconPlacement:"end",placeholder:D("Search..."),containerClass:"w-full sm:w-auto"}),(0,a.jsx)(c.k,{canFilterByAgent:!0,canFilterByMethod:!0,canFilterByGateway:!0}),(0,a.jsx)(o._,{url:`/admin/transactions/export/${s.userId}`})]})]}),(0,a.jsx)(d.Z,{className:"my-4"}),(0,a.jsx)(r.Z,{data:I,meta:_,isLoading:j})]})]})}},38520:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,runtime:()=>r});var a=s(18264);let r=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\layout.tsx#runtime`),n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\layout.tsx#default`)},96104:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}},35032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}},32336:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\transactions\page.tsx#default`)},78174:(e,t,s)=>{"use strict";function a({children:e}){return e}s.r(t),s.d(t,{default:()=>a}),s(87908)},73081:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4774,7848,6147,1991,7283,5089,3711,4656,8748,4153],()=>t(23533));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/customers/[customerId]/transactions/page"]=s}]);
//# sourceMappingURL=page.js.map