(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[82822],{58369:function(e,t,r){Promise.resolve().then(r.bind(r,57380))},57380:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return P}});var a=r(57437),n=r(25833),s=r(85539),l=r(37099),i=r(16831),o=r(62869),d=r(6512),c=r(1828),u=r(83277),f=r(80167),m=r(75730),v=r(94508),g=r(502),h=r(59532),p=r(2901),x=r(93824),b=r(32706),y=r(15641),N=r(27648),j=r(99376),w=r(2265),S=r(43949),Z=r(14438);function P(){var e;let{t}=(0,S.$G)(),r=(0,j.useSearchParams)(),[l,u]=w.useState(!1),[y,P]=w.useState([]),[C,k]=w.useState(null!==(e=r.get("search"))&&void 0!==e?e:""),I=(0,j.useRouter)(),T=(0,j.usePathname)(),{data:z,meta:A,isLoading:U,refresh:D}=(0,m.Z)("/admin/cards?".concat(r.toString())),L=e=>{let{cardId:r,status:a}=e;Z.toast.promise((0,f.a)({cardId:r,dataList:{status:a},isAdmin:!0}),{loading:t("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return D(),e.message},error:e=>e.message})};return(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,a.jsx)("div",{className:"flex h-12 items-center",children:(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsx)(s.R,{value:C,onChange:e=>{e.preventDefault();let t=(0,v.w4)(e.target.value);k(e.target.value),I.replace("".concat(T,"?").concat(t.toString()))},iconPlacement:"end",placeholder:t("Search...")})})}),(0,a.jsx)(d.Z,{className:"my-4"}),(0,a.jsx)(n.Z,{data:z?z.map(e=>new g.Z(e)):[],sorting:y,setSorting:P,isLoading:U,pagination:{total:null==A?void 0:A.total,page:null==A?void 0:A.currentPage,limit:null==A?void 0:A.perPage},structure:[{id:"id",header:t("Card ID"),cell:e=>{var t;let{row:r}=e;return(0,a.jsxs)("p",{className:"font-normal",children:["#",null===(t=r.original)||void 0===t?void 0:t.id]})}},{id:"brand",header:t("Type"),cell:e=>{var t;let{row:r}=e;return(0,a.jsx)("p",{className:"font-normal",children:null===(t=r.original)||void 0===t?void 0:t.brand})}},{id:"number",header:t("Number"),meta:{className:"w-[22%]"},cell:e=>{var t,r;let{row:n}=e;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"font-normal",children:l?null===(t=n.original)||void 0===t?void 0:t.number.replace(/(\d{4})(?=\d)/g,"$1 "):"**** **** **** ".concat(null===(r=n.original)||void 0===r?void 0:r.lastFour)}),(0,a.jsx)(o.z,{"aria-label":"VisibilityToggler",variant:"secondary",size:"icon",type:"button",className:"rounded-md",onClick:()=>u(e=>!e),children:l?(0,a.jsx)(x.Z,{size:16}):(0,a.jsx)(b.Z,{size:16})})]})}},{id:"cvc",header:t("CVV"),cell:e=>{var t;let{row:r}=e;return(0,a.jsx)("p",{className:"font-normal",children:null===(t=r.original)||void 0===t?void 0:t.cvc})}},{id:"expYear",header:t("Exp. Date"),cell:e=>{var t,r,n,s;let{row:l}=e;return(0,a.jsxs)("p",{className:"font-normal",children:[null===(t=l.original)||void 0===t?void 0:t.expMonth.toString().padStart(2,"0"),"/",(null===(r=l.original)||void 0===r?void 0:r.expYear.toString().length)===4?null===(n=l.original)||void 0===n?void 0:n.expYear.toString().slice(2):null===(s=l.original)||void 0===s?void 0:s.expYear.toString()]})}},{id:"status",header:t("Status"),cell:e=>{var t;let{row:r}=e;return(0,a.jsx)(c.Z,{defaultChecked:(null===(t=r.original)||void 0===t?void 0:t.status)==="active",onCheckedChange:e=>{var t;L({cardId:null===(t=r.original)||void 0===t?void 0:t.id,status:e?"active":"inactive"})}})}},{id:"user",header:t("User"),cell:e=>{var t,r,n,s,l,o,d;let{row:c}=e;return(0,a.jsxs)(N.default,{href:"/customers/".concat(null===(t=c.original.user.customer)||void 0===t?void 0:t.id,"?name=").concat(null===(n=c.original.user)||void 0===n?void 0:null===(r=n.customer)||void 0===r?void 0:r.name,"&active=").concat(null==c?void 0:null===(l=c.original)||void 0===l?void 0:null===(s=l.user)||void 0===s?void 0:s.status),className:"flex min-w-[80px] items-center gap-2 font-normal text-secondary-text hover:text-foreground",children:[(0,a.jsxs)(i.qE,{children:[(0,a.jsx)(i.F$,{src:c.original.user.customer.profileImage}),(0,a.jsx)(i.Q5,{children:(0,h.v)(c.original.user.customer.name)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-normal",children:c.original.user.customer.name}),(null===(o=c.original)||void 0===o?void 0:o.user.email)?(0,a.jsx)("p",{className:"text-xs font-normal",children:null===(d=c.original)||void 0===d?void 0:d.user.email}):null]})]})}},{id:"createdAt",header:t("Issue date"),cell:e=>{var t,r;let{row:n}=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-normal",children:(0,p.WU)(null===(t=n.original)||void 0===t?void 0:t.createdAt,"dd MMM yyyy;")}),(0,a.jsx)("p",{className:"font-normal",children:(0,p.WU)(null===(r=n.original)||void 0===r?void 0:r.createdAt,"hh:mm a")})]})}},{id:"actions",header:t("Actions"),cell:e=>{var t;let{row:r}=e;return(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(R,{cardId:null===(t=r.original)||void 0===t?void 0:t.id,onMutate:D})})}}]})]})})}function R(e){let{cardId:t,onMutate:r}=e,{t:n}=(0,S.$G)(),s=async()=>{(await (0,u.f)({cardId:t,isAdmin:!0})).status&&(r(),Z.toast.success(n("Investment deleted successfully")))};return(0,a.jsxs)(l.aR,{children:[(0,a.jsx)(l.vW,{asChild:!0,children:(0,a.jsx)(o.z,{size:"icon",variant:"outline",color:"danger",className:"h-8 w-8 rounded-md bg-background text-danger hover:bg-background",children:(0,a.jsx)(y.Z,{size:20})})}),(0,a.jsxs)(l._T,{children:[(0,a.jsxs)(l.fY,{children:[(0,a.jsx)(l.f$,{children:n("Close Card")}),(0,a.jsx)(l.yT,{children:n("Are you sure you want to close this card?")})]}),(0,a.jsxs)(l.xo,{className:"mt-2",children:[(0,a.jsx)(l.le,{children:n("No")}),(0,a.jsx)(l.OL,{type:"button",onClick:s,className:"action:bg-destructive/80 bg-destructive text-destructive-foreground hover:bg-destructive/90",children:n("Yes")})]})]})]})}},25833:function(e,t,r){"use strict";r.d(t,{Z:function(){return p}});var a=r(57437),n=r(94508),s=r(71594),l=r(24525),i=r(73490),o=r(36887),d=r(64394),c=r(61756),u=r(99376),f=r(4751),m=r(2265),v=r(43949),g=r(62869),h=r(73578);function p(e){let{data:t,isLoading:r=!1,structure:p,sorting:x,setSorting:b,padding:y=!1,className:N,onRefresh:j,pagination:w}=e,S=(0,m.useMemo)(()=>p,[p]),Z=(0,u.useRouter)(),P=(0,u.usePathname)(),R=(0,u.useSearchParams)(),{t:C}=(0,v.$G)(),k=(0,s.b7)({data:t||[],columns:S,state:{sorting:x,onRefresh:j},onSortingChange:b,getCoreRowModel:(0,l.sC)(),getSortedRowModel:(0,l.tj)(),debugTable:!1});return r?(0,a.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,a.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:C("Loading...")})}):(null==t?void 0:t.length)?(0,a.jsxs)("div",{className:(0,n.ZP)("".concat(y?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),N),children:[(0,a.jsxs)(h.iA,{children:[(0,a.jsx)(h.xD,{children:k.getHeaderGroups().map(e=>(0,a.jsx)(h.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,r,l,i;return(0,a.jsx)(h.ss,{className:(0,n.ZP)("",null==e?void 0:null===(l=e.column)||void 0===l?void 0:null===(r=l.columnDef)||void 0===r?void 0:null===(t=r.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,a.jsxs)(g.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[C((0,s.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(i=({asc:(0,a.jsx)(o.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,a.jsx)(o.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==i?i:(0,a.jsx)(o.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,a.jsx)(h.RM,{children:k.getRowModel().rows.map(e=>(0,a.jsx)(h.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,a.jsx)(h.pj,{className:"py-3 text-sm font-semibold",children:(0,s.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),w&&w.total>10&&(0,a.jsx)("div",{className:"pb-2 pt-6",children:(0,a.jsx)(f.Z,{showTotal:(e,t)=>C("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==w?void 0:w.page,total:null==w?void 0:w.total,pageSize:null==w?void 0:w.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(R);t.set("page",e.toString()),Z.push("".concat(P,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,a.jsx)("a",{...e,children:(0,a.jsx)(d.Z,{size:"18"})}),nextIcon:e=>(0,a.jsx)("a",{...e,children:(0,a.jsx)(c.Z,{size:"18"})})})})]}):(0,a.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,a.jsx)(i.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),C("No data found!")]})})}},85539:function(e,t,r){"use strict";r.d(t,{R:function(){return i}});var a=r(57437);r(2265);var n=r(95186),s=r(94508),l=r(48674);function i(e){let{iconPlacement:t="start",className:r,containerClass:i,...o}=e;return(0,a.jsxs)("div",{className:(0,s.ZP)("relative flex items-center",i),children:[(0,a.jsx)(l.Z,{size:"20",className:(0,s.ZP)("absolute top-1/2 -translate-y-1/2","end"===t?"right-2.5":"left-2.5")}),(0,a.jsx)(n.I,{type:"text",className:(0,s.ZP)("h-10","end"===t?"pr-10":"pl-10",r),...o})]})}},37099:function(e,t,r){"use strict";r.d(t,{OL:function(){return p},_T:function(){return f},aR:function(){return o},f$:function(){return g},fY:function(){return m},le:function(){return x},vW:function(){return d},xo:function(){return v},yT:function(){return h}});var a=r(57437),n=r(90277),s=r(2265),l=r(62869),i=r(94508);let o=n.fC,d=n.xz,c=n.h_,u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.aV,{className:(0,i.ZP)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...s,ref:t})});u.displayName=n.aV.displayName;let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsx)(n.VY,{ref:t,className:(0,i.ZP)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...s})]})});f.displayName=n.VY.displayName;let m=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.ZP)("flex flex-col space-y-2 text-center sm:text-left",t),...r})};m.displayName="AlertDialogHeader";let v=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.ZP)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};v.displayName="AlertDialogFooter";let g=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.Dx,{ref:t,className:(0,i.ZP)("text-lg font-semibold",r),...s})});g.displayName=n.Dx.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.dk,{ref:t,className:(0,i.ZP)("text-sm text-muted-foreground",r),...s})});h.displayName=n.dk.displayName;let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.aU,{ref:t,className:(0,i.ZP)((0,l.d)(),r),...s})});p.displayName=n.aU.displayName;let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.$j,{ref:t,className:(0,i.ZP)((0,l.d)({variant:"outline"}),"mt-2 sm:mt-0",r),...s})});x.displayName=n.$j.displayName},16831:function(e,t,r){"use strict";r.d(t,{F$:function(){return o},Q5:function(){return d},qE:function(){return i}});var a=r(57437),n=r(2265),s=r(61146),l=r(94508);let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.fC,{ref:t,className:(0,l.ZP)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...n})});i.displayName=s.fC.displayName;let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.Ee,{ref:t,className:(0,l.ZP)("aspect-square h-full w-full",r),...n})});o.displayName=s.Ee.displayName;let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.NY,{ref:t,className:(0,l.ZP)("flex h-full w-full items-center justify-center rounded-full bg-muted",r),...n})});d.displayName=s.NY.displayName},62869:function(e,t,r){"use strict";r.d(t,{d:function(){return o},z:function(){return d}});var a=r(57437),n=r(37053),s=r(90535),l=r(2265),i=r(94508);let o=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:d=!1,...c}=e,u=d?n.g7:"button";return(0,a.jsx)(u,{className:(0,i.ZP)(o({variant:s,size:l,className:r})),ref:t,...c})});d.displayName="Button"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return l}});var a=r(57437),n=r(2265),s=r(94508);let l=n.forwardRef((e,t)=>{let{className:r,type:n,...l}=e;return(0,a.jsx)("input",{type:n,className:(0,s.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...l})});l.displayName="Input"},6512:function(e,t,r){"use strict";var a=r(57437),n=r(55156),s=r(2265),l=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,a.jsx)(n.f,{ref:t,decorative:i,orientation:s,className:(0,l.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",r),...o})});i.displayName=n.f.displayName,t.Z=i},1828:function(e,t,r){"use strict";var a=r(57437),n=r(50721),s=r(2265),l=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.fC,{className:(0,l.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",r),...s,ref:t,children:(0,a.jsx)(n.bU,{className:(0,l.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});i.displayName=n.fC.displayName,t.Z=i},73578:function(e,t,r){"use strict";r.d(t,{RM:function(){return o},SC:function(){return d},iA:function(){return l},pj:function(){return u},ss:function(){return c},xD:function(){return i}});var a=r(57437),n=r(2265),s=r(94508);let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,s.ZP)("w-full caption-bottom text-sm",r),...n})})});l.displayName="Table";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("thead",{ref:t,className:(0,s.ZP)("",r),...n})});i.displayName="TableHeader";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,s.ZP)("[&_tr:last-child]:border-0",r),...n})});o.displayName="TableBody",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,s.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...n})}).displayName="TableFooter";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("tr",{ref:t,className:(0,s.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...n})});d.displayName="TableRow";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("th",{ref:t,className:(0,s.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...n})});c.displayName="TableHead";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("td",{ref:t,className:(0,s.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...n})});u.displayName="TableCell",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("caption",{ref:t,className:(0,s.ZP)("mt-4 text-sm text-muted-foreground",r),...n})}).displayName="TableCaption"},83277:function(e,t,r){"use strict";r.d(t,{f:function(){return s}});var a=r(79981),n=r(97751);async function s(e){let{cardId:t,isAdmin:r=!1}=e;try{let e=await a.Z.delete("".concat(r?"/admin/cards/":"/cards/").concat(t));return(0,n.B)(e)}catch(e){return(0,n.D)(e)}}},80167:function(e,t,r){"use strict";r.d(t,{a:function(){return s}});var a=r(79981),n=r(97751);async function s(e){let{cardId:t,dataList:r,isAdmin:s=!1}=e;try{let e=await a.Z.put("".concat(s?"/admin/cards/change-status/":"/cards/change-status/").concat(t),r);return(0,n.B)(e)}catch(e){return(0,n.D)(e)}}},97751:function(e,t,r){"use strict";r.d(t,{B:function(){return n},D:function(){return s}});var a=r(43577);function n(e){var t,r,a;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(a=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==a?a:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function s(e){let t=500,r="Internal Server Error",n="An unknown error occurred";if((0,a.IZ)(e)){var s,l,i,o,d,c,u,f,m,v,g,h;t=null!==(m=null===(s=e.response)||void 0===s?void 0:s.status)&&void 0!==m?m:500,r=null!==(v=null===(l=e.response)||void 0===l?void 0:l.statusText)&&void 0!==v?v:"Internal Server Error",n=null!==(h=null!==(g=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(i=o[0])||void 0===i?void 0:i.message)&&void 0!==g?g:null===(f=e.response)||void 0===f?void 0:null===(u=f.data)||void 0===u?void 0:u.message)&&void 0!==h?h:e.message}else e instanceof Error&&(n=e.message);return{statusCode:t,statusText:r,status:!1,message:n,data:void 0,error:e}}},31117:function(e,t,r){"use strict";r.d(t,{d:function(){return s}});var a=r(79981),n=r(85323);let s=(e,t)=>(0,n.ZP)(e||null,e=>a.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},75730:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(31117),n=r(99376);function s(e,t){var r,s,l;let i=(0,n.usePathname)(),o=(0,n.useSearchParams)(),d=(0,n.useRouter)(),[c,u]=e.split("?"),f=new URLSearchParams(u);f.has("page")||f.set("page","1"),f.has("limit")||f.set("limit","10");let m="".concat(c,"?").concat(f.toString()),{data:v,error:g,isLoading:h,mutate:p,...x}=(0,a.d)(m,t);return{refresh:()=>p(v),data:null!==(l=null==v?void 0:null===(r=v.data)||void 0===r?void 0:r.data)&&void 0!==l?l:[],meta:null==v?void 0:null===(s=v.data)||void 0===s?void 0:s.meta,filter:(e,t,r)=>{let a=new URLSearchParams(o.toString());t?a.set(e,t.toString()):a.delete(e),d.replace("".concat(i,"?").concat(a.toString())),null==r||r()},isLoading:h,error:g,...x}}},79981:function(e,t,r){"use strict";var a=r(78040),n=r(83464);t.Z=n.default.create({baseURL:a.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return a},sp:function(){return n}});let a={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},n=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return c},Fg:function(){return m},Fp:function(){return d},Qp:function(){return f},ZP:function(){return i},fl:function(){return o},qR:function(){return u},w4:function(){return v}});var a=r(78040),n=r(61994),s=r(14438),l=r(53335);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.m6)((0,n.W)(t))}function o(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>s.toast.success("Copied to clipboard!")).catch(()=>{s.toast.error("Failed to copy!")})};class c{format(e,t){let{currencyCode:r,amountText:a}=this.formatter(e,t);return"".concat(a," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:a}=this.formatter(e,t);return"".concat(a," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,a;let n;let s=void 0===t?this.currencyCode:t;try{n=new Intl.NumberFormat("en-US",{style:"currency",currency:s,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){n=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let l=null!==(a=null===(r=n.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==a?a:s,i=n.format(e),o=i.substring(l.length).trim();return{currencyCode:s,currencySymbol:l,formattedAmount:i,amountText:o}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(a.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(a.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",v=function(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",n=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?n.set(a,e):n.delete(a),n}},502:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});class a{constructor(e){this.id=null==e?void 0:e.id,this.cardId=null==e?void 0:e.cardId,this.userId=null==e?void 0:e.userId,this.walletId=null==e?void 0:e.walletId,this.number=null==e?void 0:e.number,this.cvc=null==e?void 0:e.cvc,this.lastFour=null==e?void 0:e.lastFour,this.brand=null==e?void 0:e.brand,this.expMonth=null==e?void 0:e.expMonth,this.expYear=null==e?void 0:e.expYear,this.status=null==e?void 0:e.status,this.type=null==e?void 0:e.type,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.wallet=null==e?void 0:e.wallet,this.user=null==e?void 0:e.user}}},59532:function(e,t,r){"use strict";function a(e){if(!e)return"";let t=e.split(" ");return(t.length>2?t[0].length>3?t[0][0]+t[t.length-1][0]:t[1][0]+t[t.length-1][0]:2===t.length?t[0][0]+t[1][0]:t[0][0]).toUpperCase()}r.d(t,{v:function(){return a}})}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,49027,27648,2901,85210,63071,92971,95030,1744],function(){return e(e.s=58369)}),_N_E=e.O()}]);