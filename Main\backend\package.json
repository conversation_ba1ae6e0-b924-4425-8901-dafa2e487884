{"name": "paysnap-backend", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node ace serve --hmr", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#utils/*": "./app/utils/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.7.0", "@adonisjs/eslint-config": "^1.3.0", "@adonisjs/prettier-config": "^1.3.0", "@adonisjs/tsconfig": "^1.3.0", "@japa/api-client": "^3.0.3", "@japa/assert": "^4.0.1", "@japa/plugin-adonisjs": "^3.0.1", "@japa/runner": "^3.1.4", "@swc/core": "1.7.26", "@types/json2csv": "^5.0.7", "@types/luxon": "^3.4.2", "@types/node": "^20.14.9", "@types/ua-parser-js": "^0.7.39", "eslint": "^8.57.0", "hot-hook": "^0.2.6", "pino-pretty": "^11.2.1", "prettier": "^3.3.2", "ts-node": "^10.9.2", "typescript": "~5.4"}, "dependencies": {"@adonisjs/auth": "^9.2.3", "@adonisjs/core": "^6.12.1", "@adonisjs/cors": "^2.2.1", "@adonisjs/drive": "^3.2.0", "@adonisjs/lucid": "^21.1.0", "@adonisjs/mail": "^9.2.2", "@adonisjs/session": "^7.4.2", "@adonisjs/static": "^1.1.1", "@adonisjs/transmit": "^2.0.2", "@jrmc/adonis-attachment": "^3.2.0", "@mollie/api-client": "^4.1.0", "@types/pdfkit": "^0.13.5", "@vinejs/vine": "^2.1.0", "adonis-lucid-filter": "^5.2.0", "adonisjs-scheduler": "^1.0.5", "axios": "^1.7.3", "csv-parse": "^5.5.6", "edge.js": "^6.0.2", "json2csv": "^6.0.0-alpha.2", "luxon": "^3.4.4", "mysql2": "^3.10.2", "nanoid": "^5.0.7", "node-html-parser": "^6.1.13", "pdfkit": "^0.15.0", "prettier-edgejs": "^0.2.36", "prettier-plugin-edgejs": "^1.0.2", "razorpay": "^2.9.5", "reflect-metadata": "^0.2.2", "stripe": "^17.6.0", "ua-parser-js": "^0.7.39", "uuid": "^10.0.0", "uuidv4": "^6.2.13"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "eslintConfig": {"extends": "@adonisjs/eslint-config/app"}, "prettier": {"trailingComma": "es5", "semi": true, "singleQuote": true, "useTabs": false, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100, "plugins": ["prettier-plugin-edgejs"]}}