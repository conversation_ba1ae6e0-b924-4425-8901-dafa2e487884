(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4551],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},17589:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ComponentMod:()=>P,default:()=>z});var o,n={};t.r(n),t.d(n,{AppRouter:()=>p.WY,ClientPageRoot:()=>p.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>p.yO,NotFoundBoundary:()=>p.O4,Postpone:()=>p.hQ,RenderFromTemplateContext:()=>p.b5,__next_app__:()=>h,actionAsyncStorage:()=>p.Wz,createDynamicallyTrackedSearchParams:()=>p.rL,createUntrackedSearchParams:()=>p.S5,decodeAction:()=>p.Hs,decodeFormState:()=>p.dH,decodeReply:()=>p.kf,originalPathname:()=>g,pages:()=>f,patchFetch:()=>p.XH,preconnect:()=>p.$P,preloadFont:()=>p.C5,preloadStyle:()=>p.oH,renderToReadableStream:()=>p.aW,requestAsyncStorage:()=>p.Fg,routeModule:()=>b,serverHooks:()=>p.GP,staticGenerationAsyncStorage:()=>p.AT,taintObjectReference:()=>p.nr,tree:()=>m}),t(67206);var a=t(79319),l=t(20518),s=t(61902),i=t(62042),c=t(44630),d=t(44828),u=t(65505),p=t(13839);let m=["",{children:["(auth)",{children:["register",{children:["email-verification-status",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36257)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(auth)\\register\\email-verification-status\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,96302)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(auth)\\register\\email-verification-status\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,94875)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(auth)\\register\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,14512)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(auth)\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,97042)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(auth)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],f=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(auth)\\register\\email-verification-status\\page.tsx"],g="/(auth)/register/email-verification-status/page",h={require:t,loadChunk:()=>Promise.resolve()},b=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(auth)/register/email-verification-status/page",pathname:"/register/email-verification-status",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}});var x=t(69094),v=t(5787),y=t(90527);let w=e=>e?JSON.parse(e):void 0,k=self.__BUILD_MANIFEST,E=w(self.__REACT_LOADABLE_MANIFEST),S=null==(o=self.__RSC_MANIFEST)?void 0:o["/(auth)/register/email-verification-status/page"],j=w(self.__RSC_SERVER_MANIFEST),N=w(self.__NEXT_FONT_MANIFEST),L=w(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];S&&j&&(0,v.Mo)({clientReferenceManifest:S,serverActionsManifest:j,serverModuleMap:(0,y.w)({serverActionsManifest:j,pageName:"/(auth)/register/email-verification-status/page"})});let M=(0,l.d)({pagesType:x.s.APP,dev:!1,page:"/(auth)/register/email-verification-status/page",appMod:null,pageMod:n,errorMod:null,error500Mod:null,Document:null,buildManifest:k,renderToHTML:i.f,reactLoadableManifest:E,clientReferenceManifest:S,serverActionsManifest:j,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:N,incrementalCacheHandler:null,interceptionRouteRewrites:L}),P=n;function z(e){return(0,a.C)({...e,IncrementalCache:s.k,handler:M})}},56450:(e,r,t)=>{Promise.resolve().then(t.bind(t,75640))},35637:(e,r,t)=>{Promise.resolve().then(t.bind(t,17687))},48335:(e,r,t)=>{Promise.resolve().then(t.bind(t,32167))},75640:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var o=t(60926),n=t(26334),a=t(82418),l=t(64947),s=t(29220),i=t(61227),c=t(840),d=t(65091);function u(){let{authBanner:e}=(0,c.T)();return(0,o.jsx)("div",{style:{backgroundImage:`url(${(0,d.qR)(e)})`},className:"hidden h-full w-full border-r bg-cover bg-no-repeat md:block md:max-w-[350px] lg:max-w-[510px]"})}function p({children:e}){let{isAuthenticate:r,isLoading:t}=(0,a.a)(),c=(0,l.tv)(),[d,p]=s.useState(!1);return(s.useLayoutEffect(()=>{!t&&r&&c.push("/")},[t]),s.useLayoutEffect(()=>{t||r||p(!0)},[t,r]),d)?(0,o.jsxs)("div",{className:"flex h-screen",children:[(0,o.jsx)(u,{}),(0,o.jsxs)("div",{className:"flex h-full w-full flex-col bg-background",children:[(0,o.jsx)(i.w,{path:"/signin"}),(0,o.jsx)("div",{className:"overflow-y-auto",children:e})]})]}):(0,o.jsx)(n.default,{})}},17687:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>Z,runtime:()=>_});var o=t(60926),n=t(58387),a=t(29411),l=t(36162),s=t(29220),i=t(65091);let c=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,i.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));c.displayName="Card";let d=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,i.ZP)("flex flex-col space-y-1.5 p-6",e),...r}));d.displayName="CardHeader";let u=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("h3",{ref:t,className:(0,i.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...r}));u.displayName="CardTitle";let p=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("p",{ref:t,className:(0,i.ZP)("text-sm text-muted-foreground",e),...r}));p.displayName="CardDescription";let m=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,i.ZP)("p-6 pt-0",e),...r}));m.displayName="CardContent",s.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,i.ZP)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";var f=t(74988);t(1181),t(24013);var g=t(61394),h=t(31036),b=t.n(h),x=["variant","color","size"],v=function(e){var r=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Zm.59 7.7-5.67 5.67a.75.75 0 0 1-1.06 0l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .*********** 0 1.06Z",fill:r}))},y=function(e){var r=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M2 12.96V15c0 5 2 7 7 7h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9M15 10.38l1.12-1.13",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"m7.88 12 2.74 2.75 2.55-2.54",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},w=function(e){var r=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Z",fill:r}),s.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:r}))},k=function(e){var r=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M9 22h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9v6c0 5 2 7 7 7Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"m7.75 12 2.83 2.83 5.67-5.66",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},E=function(e){var r=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h6c5.43 0 7.75 2.32 7.75 7.75v6c0 5.43-2.32 7.75-7.75 7.75Zm-6-20C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25V9c0-4.61-1.64-6.25-6.25-6.25H9Z",fill:r}),s.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:r}))},S=function(e){var r=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M9 22h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9v6c0 5 2 7 7 7Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".34",d:"m7.75 12.002 2.83 2.83 5.67-5.66",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},j=function(e,r){switch(e){case"Bold":return s.createElement(v,{color:r});case"Broken":return s.createElement(y,{color:r});case"Bulk":return s.createElement(w,{color:r});case"Linear":default:return s.createElement(k,{color:r});case"Outline":return s.createElement(E,{color:r});case"TwoTone":return s.createElement(S,{color:r})}},N=(0,s.forwardRef)(function(e,r){var t=e.variant,o=e.color,n=e.size,a=(0,g._)(e,x);return s.createElement("svg",(0,g.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:n,height:n,viewBox:"0 0 24 24",fill:"none"}),j(t,o))});N.propTypes={variant:b().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:b().string,size:b().oneOfType([b().string,b().number])},N.defaultProps={variant:"Linear",color:"currentColor",size:"24"},N.displayName="TickSquare";var L=t(90543),M=t(14761),P=t(66697),z=t(59141);let C=(0,z.Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),D=(0,z.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var T=t(737),A=t(39228);let _="edge";function Z({searchParams:e}){let{token:r}=e,[t,i]=s.useState(!0),[g,h]=s.useState(),{t:b}=(0,A.$G)();return(0,o.jsxs)("div",{className:"container mt-10 max-w-[716px] px-4 py-6",children:[(0,o.jsx)(n.J,{condition:!t&&!0===g,children:(0,o.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,o.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,o.jsx)(N,{size:70,variant:"Bulk",className:"text-success"}),(0,o.jsx)("h1",{className:"text-[32px] font-medium leading-10",children:b("Verification Successful")})]}),(0,o.jsx)(f.Z,{className:"mb-[2px] mt-[3px]"}),(0,o.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(L.Z,{size:17,variant:"Bold",className:"text-spacial-green"}),(0,o.jsx)("span",{className:"text-sm font-semibold leading-5",children:b("Account creation")})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(L.Z,{size:17,variant:"Bold",className:"text-spacial-green"}),(0,o.jsx)("span",{className:"text-sm font-semibold leading-5",children:b("Email verification")})]}),(0,o.jsx)("p",{className:"text-sm font-normal leading-[22px]",children:b("Congratulations! Your account has been successfully created and ready to use.")})]}),(0,o.jsx)(l.z,{className:"h-10 max-w-[286px] gap-0.5 rounded-lg text-base font-medium leading-[22px]",asChild:!0,children:(0,o.jsxs)(T.Z,{href:"/signin",prefetch:!1,children:[b("Sign in to continue"),(0,o.jsx)(M.Z,{size:"16"})]})})]})}),(0,o.jsx)(n.J,{condition:!t&&!1===g,children:(0,o.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,o.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,o.jsx)(P.Z,{size:70,variant:"Bulk",className:"text-primary"}),(0,o.jsx)("h1",{className:"text-[32px] font-medium leading-10",children:b("Verification failed, but don’t worry")})]}),(0,o.jsx)(f.Z,{className:"mb-[2px] mt-[3px]"}),(0,o.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(L.Z,{size:17,variant:"Bold",className:"text-spacial-green"}),(0,o.jsx)("span",{className:"text-sm font-semibold leading-5",children:b("Account creation")})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(C,{size:17,className:"fill-spacial-red text-background"}),(0,o.jsx)("span",{className:"text-sm font-semibold leading-5 text-secondary-text",children:b("Email verification")})]}),(0,o.jsx)("p",{className:"text-sm font-normal leading-[22px]",children:b("Your account has been created but we’ve failed to verify your email. Don’t worry at all, you can sign in again to get a new link anytime.")})]}),(0,o.jsx)(l.z,{className:"h-10 max-w-[286px] gap-0.5 rounded-lg text-base font-medium leading-[22px]",asChild:!0,children:(0,o.jsxs)(T.Z,{href:"/signin",prefetch:!1,children:[b("Sign in again to fix it"),(0,o.jsx)(M.Z,{size:"16"})]})})]})}),(0,o.jsx)(n.J,{condition:t,children:(0,o.jsx)("div",{className:"flex h-full w-full flex-1 items-center justify-center",children:(0,o.jsxs)(c,{className:"w-full max-w-[400px] border-none shadow-none",children:[(0,o.jsxs)(d,{className:"items-center",children:[(0,o.jsx)(D,{size:48,strokeWidth:1.5}),(0,o.jsx)(u,{className:"mb-1",children:b("Email Verifying...")}),(0,o.jsx)(p,{className:"text-center",children:b("We are verifying your email address. This might take a few moments.")})]}),(0,o.jsx)(m,{className:"flex items-center justify-center",children:(0,o.jsx)(a.Loader,{title:b("Please wait...")})})]})})})]})}},14761:(e,r,t)=>{"use strict";t.d(r,{Z:()=>g});var o=t(61394),n=t(29220),a=t(31036),l=t.n(a),s=["variant","color","size"],i=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{fill:r,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},c=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},d=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{fill:r,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),n.createElement("path",{fill:r,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},u=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},p=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{fill:r,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},m=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},f=function(e,r){switch(e){case"Bold":return n.createElement(i,{color:r});case"Broken":return n.createElement(c,{color:r});case"Bulk":return n.createElement(d,{color:r});case"Linear":default:return n.createElement(u,{color:r});case"Outline":return n.createElement(p,{color:r});case"TwoTone":return n.createElement(m,{color:r})}},g=(0,n.forwardRef)(function(e,r){var t=e.variant,a=e.color,l=e.size,i=(0,o._)(e,s);return n.createElement("svg",(0,o.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),f(t,a))});g.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="ArrowRight2"},90543:(e,r,t)=>{"use strict";t.d(r,{Z:()=>g});var o=t(61394),n=t(29220),a=t(31036),l=t.n(a),s=["variant","color","size"],i=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm4.78 7.7-5.67 5.67a.75.75 0 0 1-1.06 0l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .*********** 0 1.06Z",fill:r}))},c=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85M15 10.38l1.12-1.13",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"m7.88 12 2.74 2.75 2.55-2.54",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:r}),n.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:r}))},u=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"m7.75 12 2.83 2.83 5.67-5.66",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:r}),n.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:r}))},m=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{opacity:".34",d:"m7.75 12.002 2.83 2.83 5.67-5.66",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e,r){switch(e){case"Bold":return n.createElement(i,{color:r});case"Broken":return n.createElement(c,{color:r});case"Bulk":return n.createElement(d,{color:r});case"Linear":default:return n.createElement(u,{color:r});case"Outline":return n.createElement(p,{color:r});case"TwoTone":return n.createElement(m,{color:r})}},g=(0,n.forwardRef)(function(e,r){var t=e.variant,a=e.color,l=e.size,i=(0,o._)(e,s);return n.createElement("svg",(0,o.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),f(t,a))});g.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="TickCircle"},66697:(e,r,t)=>{"use strict";t.d(r,{Z:()=>g});var o=t(61394),n=t(29220),a=t(31036),l=t.n(a),s=["variant","color","size"],i=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"m19.51 5.85-5.94-3.43c-.97-.56-2.17-.56-3.15 0L4.49 5.85a3.15 3.15 0 0 0-1.57 2.73v6.84c0 1.12.6 2.16 1.57 2.73l5.94 3.43c.97.56 2.17.56 3.15 0l5.94-3.43a3.15 3.15 0 0 0 1.57-2.73V8.58a3.192 3.192 0 0 0-1.58-2.73Zm-8.26 1.9c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75s-.75-.34-.75-.75V7.75Zm1.67 8.88c-.05.12-.12.23-.21.33a.99.99 0 0 1-1.09.21c-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38Z",fill:r}))},c=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 7.75V13M2.92 8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.56 1.57 1.6 1.57 2.73v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73v-2.76",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M12 16.2v.1",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",fill:r}),n.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.249c-.13 0-.26-.03-.38-.08-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:r}))},u=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 7.75V13M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M12 16.2v.1",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.25a.99.99 0 0 1-.71-.29c-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.11-.52.29-.71.37-.37 1.05-.37 1.42 0 .18.19.29.45.29.71 0 .13-.03.26-.08.38s-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:r}),n.createElement("path",{d:"M12 22.751c-.67 0-1.35-.17-1.95-.52l-5.94-3.43c-1.2-.7-1.95-1.99-1.95-3.38v-6.84c0-1.39.75-2.68 1.95-3.38l5.94-3.43c1.2-.7 2.69-.7 3.9 0l5.94 3.43c1.2.7 1.95 1.99 1.95 3.38v6.84c0 1.39-.75 2.68-1.95 3.38l-5.94 3.43c-.6.35-1.28.52-1.95.52Zm0-20c-.41 0-.83.11-1.2.32l-5.94 3.43c-.74.43-1.2 1.22-1.2 2.08v6.84c0 .85.46 1.65 1.2 2.08l5.94 3.43c.74.43 1.66.43 2.39 0l5.94-3.43c.74-.43 1.2-1.22 1.2-2.08v-6.84c0-.85-.46-1.65-1.2-2.08l-5.94-3.43c-.36-.21-.78-.32-1.19-.32Z",fill:r}))},m=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M12 7.75V13",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{opacity:".4",d:"M12 16.2v.1",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e,r){switch(e){case"Bold":return n.createElement(i,{color:r});case"Broken":return n.createElement(c,{color:r});case"Bulk":return n.createElement(d,{color:r});case"Linear":default:return n.createElement(u,{color:r});case"Outline":return n.createElement(p,{color:r});case"TwoTone":return n.createElement(m,{color:r})}},g=(0,n.forwardRef)(function(e,r){var t=e.variant,a=e.color,l=e.size,i=(0,o._)(e,s);return n.createElement("svg",(0,o.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),f(t,a))});g.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="Warning2"},14512:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(auth)\layout.tsx#default`)},97042:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var o=t(42416),n=t(26339);function a(){return(0,o.jsx)(n.Z,{})}},96302:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var o=t(42416),n=t(21237);function a(){return(0,o.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,o.jsx)(n.a,{})})}},36257:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,runtime:()=>n});var o=t(18264);let n=(0,o.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(auth)\register\email-verification-status\page.tsx#runtime`),a=(0,o.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(auth)\register\email-verification-status\page.tsx#default`)},94875:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>K});var o=t(42416);process.env.SESSION_SECRET;var n=t(18264);(0,n.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\node_modules\sonner\dist\index.mjs#Toaster`),(0,n.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\node_modules\sonner\dist\index.mjs#toast`),(0,n.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\node_modules\sonner\dist\index.mjs#useSonner`);let a=e=>{let r=c(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),l(t,r)||i(e)},getConflictingClassGroupIds:(e,r)=>{let n=t[e]||[];return r&&o[e]?[...n,...o[e]]:n}}},l=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),n=o?l(e.slice(1),o):void 0;if(n)return n;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},s=/^\[(.+)\]$/,i=e=>{if(s.test(e)){let r=s.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},c=e=>{let{theme:r,prefix:t}=e,o={nextPart:new Map,validators:[]};return m(Object.entries(e.classGroups),t).forEach(([e,t])=>{d(t,o,e,r)}),o},d=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:u(r,e)).classGroupId=t;return}if("function"==typeof e){if(p(e)){d(e(o),r,t,o);return}r.validators.push({validator:e,classGroupId:t});return}Object.entries(e).forEach(([e,n])=>{d(n,u(r,e),t,o)})})},u=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},p=e=>e.isThemeGetter,m=(e,r)=>r?e.map(([e,t])=>[e,t.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[r+e,t])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}},g=e=>{let{separator:r,experimentalParseClassName:t}=e,o=1===r.length,n=r[0],a=r.length,l=e=>{let t;let l=[],s=0,i=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===s){if(d===n&&(o||e.slice(c,c+a)===r)){l.push(e.slice(i,c)),i=c+a;continue}if("/"===d){t=c;continue}}"["===d?s++:"]"===d&&s--}let c=0===l.length?e:e.substring(i),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:l,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};return t?e=>t({className:e,parseClassName:l}):l},h=e=>{if(e.length<=1)return e;let r=[],t=[];return e.forEach(e=>{"["===e[0]?(r.push(...t.sort(),e),t=[]):t.push(e)}),r.push(...t.sort()),r},b=e=>({cache:f(e.cacheSize),parseClassName:g(e),...a(e)}),x=/\s+/,v=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n}=r,a=[],l=e.trim().split(x),s="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{modifiers:i,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=t(r),p=!!u,m=o(p?d.substring(0,u):d);if(!m){if(!p||!(m=o(d))){s=r+(s.length>0?" "+s:s);continue}p=!1}let f=h(i).join(":"),g=c?f+"!":f,b=g+m;if(a.includes(b))continue;a.push(b);let x=n(m,p);for(let e=0;e<x.length;++e){let r=x[e];a.push(g+r)}s=r+(s.length>0?" "+s:s)}return s};function y(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=w(e))&&(o&&(o+=" "),o+=r);return o}let w=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=w(e[o]))&&(t&&(t+=" "),t+=r);return t},k=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},E=/^\[(?:([a-z-]+):)?(.+)\]$/i,S=/^\d+\/\d+$/,j=new Set(["px","full","screen"]),N=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,L=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,M=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,P=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,z=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=e=>T(e)||j.has(e)||S.test(e),D=e=>Y(e,"length",G),T=e=>!!e&&!Number.isNaN(Number(e)),A=e=>Y(e,"number",T),_=e=>!!e&&Number.isInteger(Number(e)),Z=e=>e.endsWith("%")&&T(e.slice(0,-1)),F=e=>E.test(e),R=e=>N.test(e),I=new Set(["length","size","percentage"]),B=e=>Y(e,I,H),W=e=>Y(e,"position",H),O=new Set(["image","url"]),Q=e=>Y(e,O,$),V=e=>Y(e,"",q),U=()=>!0,Y=(e,r,t)=>{let o=E.exec(e);return!!o&&(o[1]?"string"==typeof r?o[1]===r:r.has(o[1]):t(o[2]))},G=e=>L.test(e)&&!M.test(e),H=()=>!1,q=e=>P.test(e),$=e=>z.test(e);Symbol.toStringTag;let J=function(e,...r){let t,o,n;let a=function(s){return o=(t=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=t.cache.set,a=l,l(s)};function l(e){let r=o(e);if(r)return r;let a=v(e,t);return n(e,a),a}return function(){return a(y.apply(null,arguments))}}(()=>{let e=k("colors"),r=k("spacing"),t=k("blur"),o=k("brightness"),n=k("borderColor"),a=k("borderRadius"),l=k("borderSpacing"),s=k("borderWidth"),i=k("contrast"),c=k("grayscale"),d=k("hueRotate"),u=k("invert"),p=k("gap"),m=k("gradientColorStops"),f=k("gradientColorStopPositions"),g=k("inset"),h=k("margin"),b=k("opacity"),x=k("padding"),v=k("saturate"),y=k("scale"),w=k("sepia"),E=k("skew"),S=k("space"),j=k("translate"),N=()=>["auto","contain","none"],L=()=>["auto","hidden","clip","visible","scroll"],M=()=>["auto",F,r],P=()=>[F,r],z=()=>["",C,D],I=()=>["auto",T,F],O=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Y=()=>["solid","dashed","dotted","double","none"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],H=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",F],$=()=>["auto","avoid","all","avoid-page","page","left","right","column"],J=()=>[T,F];return{cacheSize:500,separator:":",theme:{colors:[U],spacing:[C,D],blur:["none","",R,F],brightness:J(),borderColor:[e],borderRadius:["none","","full",R,F],borderSpacing:P(),borderWidth:z(),contrast:J(),grayscale:q(),hueRotate:J(),invert:q(),gap:P(),gradientColorStops:[e],gradientColorStopPositions:[Z,D],inset:M(),margin:M(),opacity:J(),padding:P(),saturate:J(),scale:J(),sepia:q(),skew:J(),space:P(),translate:P()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[R]}],"break-after":[{"break-after":$()}],"break-before":[{"break-before":$()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...O(),F]}],overflow:[{overflow:L()}],"overflow-x":[{"overflow-x":L()}],"overflow-y":[{"overflow-y":L()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",_,F]}],basis:[{basis:M()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",_,F]}],"grid-cols":[{"grid-cols":[U]}],"col-start-end":[{col:["auto",{span:["full",_,F]},F]}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":[U]}],"row-start-end":[{row:["auto",{span:[_,F]},F]}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...H()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...H(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...H(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[x]}],px:[{px:[x]}],py:[{py:[x]}],ps:[{ps:[x]}],pe:[{pe:[x]}],pt:[{pt:[x]}],pr:[{pr:[x]}],pb:[{pb:[x]}],pl:[{pl:[x]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",F,r]}],"min-w":[{"min-w":[F,r,"min","max","fit"]}],"max-w":[{"max-w":[F,r,"none","full","min","max","fit","prose",{screen:[R]},R]}],h:[{h:[F,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[F,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[F,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[F,r,"auto","min","max","fit"]}],"font-size":[{text:["base",R,D]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",A]}],"font-family":[{font:[U]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",T,A]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",C,F]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Y(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",C,D]}],"underline-offset":[{"underline-offset":["auto",C,F]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...O(),W]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",B]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Q]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...Y(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:Y()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...Y()]}],"outline-offset":[{"outline-offset":[C,F]}],"outline-w":[{outline:[C,D]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[C,D]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",R,V]}],"shadow-color":[{shadow:[U]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...G(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":G()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[o]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",R,F]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:J()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:J()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[_,F]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[C,D,A]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function X({className:e,...r}){return(0,o.jsx)("div",{className:function(...e){return J(function(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r){if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o)}return n}(e))&&(o&&(o+=" "),o+=r);return o}(e))}("animate-pulse rounded-md bg-muted",e),...r})}function K(){return(0,o.jsxs)("div",{className:"container max-w-2xl",children:[(0,o.jsx)(X,{className:"mb-2 h-3 w-full max-w-[500px]"}),(0,o.jsx)(X,{className:"mb-2 h-7 w-full max-w-[300px]"}),(0,o.jsx)(X,{className:"my-6 h-[1px] w-full"}),(0,o.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[(0,o.jsx)(X,{className:"mb-2 aspect-square w-full"}),(0,o.jsx)(X,{className:"mb-2 aspect-square w-full"}),(0,o.jsx)(X,{className:"mb-2 aspect-square w-full"})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[529,7283],()=>r(17589));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(auth)/register/email-verification-status/page"]=t}]);
//# sourceMappingURL=page.js.map