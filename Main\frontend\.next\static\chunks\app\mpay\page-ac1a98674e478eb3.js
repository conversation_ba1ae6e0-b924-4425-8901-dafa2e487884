(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2913],{40362:function(e,t,r){Promise.resolve().then(r.bind(r,47139))},49420:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var n=r(57437),o=r(43949);function a(e){var t,r;let{data:a}=e,{t:s}=(0,o.$G)();return(0,n.jsx)("div",{className:"mt-6 text-center",children:(0,n.jsxs)("p",{className:"text-slate-500",children:[s("For any issues please contact at")," ",(0,n.jsx)("a",{href:"mailto:".concat(null==a?void 0:null===(t=a.merchant)||void 0===t?void 0:t.email),className:"text-blue-500",children:null==a?void 0:null===(r=a.merchant)||void 0===r?void 0:r.email})]})})}},51548:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(57437),o=r(33145),a=r(43949);function s(e){var t;let{data:r}=e,{t:s}=(0,a.$G)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"mb-3 flex flex-col items-center",children:[(null==r?void 0:r.logo)&&(0,n.jsx)(o.default,{src:null==r?void 0:r.logo,alt:"Merchant Logo",className:"mb-3",width:200,height:80}),(0,n.jsxs)("h2",{children:[s("Pay")," ",null==r?void 0:r.paymentAmount," ",(0,n.jsx)("span",{className:"font-normal text-slate-500",children:null==r?void 0:r.currency})]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("span",{className:"text-slate-500",children:s("to")})," ",null==r?void 0:null===(t=r.merchant)||void 0===t?void 0:t.name]})]}),(0,n.jsx)("hr",{})]})}},47139:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return y}});var n=r(57437),o=r(49420),a=r(51548),s=r(80114),i=r(35615),c=r(79981),l=r(97751);async function u(e,t){try{let r=await c.Z.post("/mapi-global/init/".concat(e),{method:t});return(0,l.B)(r)}catch(e){return(0,l.D)(e)}}var d=r(94508),m=r(22291),f=r(33145),v=r(99376),h=r(2265),p=r(43949),g=r(14438);function y(){let e=(0,v.useSearchParams)().get("trxId"),t=(0,v.useRouter)(),[r,c]=(0,h.useState)(!1),{data:l,isLoading:y}=(0,i.b)({trxId:e}),{t:x}=(0,p.$G)(),w=async r=>{if("otp_pay"===r)return t.push("/mpay/otp-pay?trxId=".concat(e));try{c(!0);let t=await u(e,r);(null==t?void 0:t.status)||g.toast.error(null==t?void 0:t.message),(null==t?void 0:t.type)==="redirect"&&(window.location.href=null==t?void 0:t.redirect)}catch(e){e instanceof Error?g.toast.error(e.message):g.toast.error(x("An unknown error occurred"))}finally{c(!1)}return null};return y||r?(0,n.jsx)(s.default,{}):(0,n.jsxs)("div",{children:[(0,n.jsx)(a.Z,{data:l}),(0,n.jsxs)("div",{className:"mt-3",children:[(0,n.jsx)("div",{className:"py-2",children:(0,n.jsx)("p",{className:"font-semibold",children:x("How would you like to pay?")})}),(0,n.jsx)("div",{children:null==l?void 0:l.gateways.map(e=>(0,n.jsxs)("button",{type:"button",className:"gateway-card my-2 flex h-20 w-full cursor-pointer items-center justify-between rounded-lg border p-4 transition-all duration-300 ease-in-out hover:bg-slate-100",onClick:()=>w(e.value),children:[(0,n.jsxs)("div",{className:"gateway-info flex items-center gap-4",children:[(null==e?void 0:e.logoImage)&&(0,n.jsx)("div",{className:"gateway-logo flex h-12 w-12 items-center justify-center overflow-hidden rounded-full border bg-white bg-contain bg-center bg-no-repeat p-1",children:(0,n.jsx)(f.default,{src:(0,d.qR)(null==e?void 0:e.logoImage),alt:null==e?void 0:e.name,width:80,height:80})}),(0,n.jsx)("div",{className:"gateway-name text-left",children:(0,n.jsx)("h3",{className:"text-lg font-semibold",children:e.name})})]}),(0,n.jsx)("div",{className:"gateway-action",children:(0,n.jsx)(m.Z,{size:32,className:"text-slate-400"})})]},e.value))})]}),(0,n.jsx)(o.Z,{data:l})]})}},80114:function(e,t,r){"use strict";r.d(t,{default:function(){return i}});var n=r(57437),o=r(85487),a=r(94508),s=r(43949);function i(e){let{className:t}=e,{t:r}=(0,s.$G)();return(0,n.jsx)("div",{className:(0,a.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,n.jsx)(o.Loader,{title:r("Loading..."),className:"text-foreground"})})}},85487:function(e,t,r){"use strict";r.d(t,{Loader:function(){return s}});var n=r(57437),o=r(94508),a=r(43949);function s(e){let{title:t="Loading...",className:r}=e,{t:s}=(0,a.$G)();return(0,n.jsxs)("div",{className:(0,o.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:s(t)})]})}},35615:function(e,t,r){"use strict";r.d(t,{b:function(){return a}});var n=r(79981),o=r(85323);function a(e){let{trxId:t}=e,{data:r,isLoading:a}=(0,o.ZP)("/mapi-global/".concat(t),e=>n.Z.get(e).then(e=>e.data),{refreshInterval:0,revalidateOnFocus:!1,revalidateOnReconnect:!1,shouldRetryOnError:!1});return{data:r,isLoading:a}}},97751:function(e,t,r){"use strict";r.d(t,{B:function(){return o},D:function(){return a}});var n=r(43577);function o(e){var t,r,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function a(e){let t=500,r="Internal Server Error",o="An unknown error occurred";if((0,n.IZ)(e)){var a,s,i,c,l,u,d,m,f,v,h,p;t=null!==(f=null===(a=e.response)||void 0===a?void 0:a.status)&&void 0!==f?f:500,r=null!==(v=null===(s=e.response)||void 0===s?void 0:s.statusText)&&void 0!==v?v:"Internal Server Error",o=null!==(p=null!==(h=null===(u=e.response)||void 0===u?void 0:null===(l=u.data)||void 0===l?void 0:null===(c=l.messages)||void 0===c?void 0:null===(i=c[0])||void 0===i?void 0:i.message)&&void 0!==h?h:null===(m=e.response)||void 0===m?void 0:null===(d=m.data)||void 0===d?void 0:d.message)&&void 0!==p?p:e.message}else e instanceof Error&&(o=e.message);return{statusCode:t,statusText:r,status:!1,message:o,data:void 0,error:e}}},79981:function(e,t,r){"use strict";var n=r(78040),o=r(83464);t.Z=o.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return n},sp:function(){return o}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},o=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return u},Fg:function(){return f},Fp:function(){return l},Qp:function(){return m},ZP:function(){return i},fl:function(){return c},qR:function(){return d},w4:function(){return v}});var n=r(78040),o=r(61994),a=r(14438),s=r(53335);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,o.W)(t))}function c(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let l=e=>{e&&navigator.clipboard.writeText(e).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let o;let a=void 0===t?this.currencyCode:t;try{o=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){o=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let s=null!==(n=null===(r=o.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:a,i=o.format(e),c=i.substring(s.length).trim();return{currencyCode:a,currencySymbol:s,formattedAmount:i,amountText:c}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",v=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",o=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?o.set(n,e):o.delete(n),o}},22291:function(e,t,r){"use strict";r.d(t,{Z:function(){return h}});var n=r(74677),o=r(2265),a=r(40718),s=r.n(a),i=["variant","color","size"],c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},l=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),o.createElement("path",{fill:t,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},f=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},v=function(e,t){switch(e){case"Bold":return o.createElement(c,{color:t});case"Broken":return o.createElement(l,{color:t});case"Bulk":return o.createElement(u,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(m,{color:t});case"TwoTone":return o.createElement(f,{color:t})}},h=(0,o.forwardRef)(function(e,t){var r=e.variant,a=e.color,s=e.size,c=(0,n._)(e,i);return o.createElement("svg",(0,n.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),v(r,a))});h.propTypes={variant:s().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:s().string,size:s().oneOfType([s().string,s().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="ArrowRight2"},74677:function(e,t,r){"use strict";function n(){return(n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function o(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}r.d(t,{_:function(){return o},a:function(){return n}})},99376:function(e,t,r){"use strict";var n=r(35475);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useSelectedLayoutSegment")&&r.d(t,{useSelectedLayoutSegment:function(){return n.useSelectedLayoutSegment}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})},48049:function(e,t,r){"use strict";var n=r(14397);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,a,s){if(s!==n){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return r.PropTypes=r,r}},40718:function(e,t,r){e.exports=r(48049)()},14397:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},43577:function(e,t,r){"use strict";r.d(t,{IZ:function(){return d}});let{Axios:n,AxiosError:o,CanceledError:a,isCancel:s,CancelToken:i,VERSION:c,all:l,Cancel:u,isAxiosError:d,spread:m,toFormData:f,AxiosHeaders:v,HttpStatusCode:h,formToJSON:p,getAdapter:g,mergeConfig:y}=r(83464).default}},function(e){e.O(0,[14438,31304,83464,2602,85323,33145,92971,95030,1744],function(){return e(e.s=40362)}),_N_E=e.O()}]);