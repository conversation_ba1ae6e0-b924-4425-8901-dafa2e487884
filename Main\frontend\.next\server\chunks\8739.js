exports.id=8739,exports.ids=[8739],exports.modules={43813:(e,s,t)=>{Promise.resolve().then(t.bind(t,79939))},4085:(e,s,t)=>{Promise.resolve().then(t.bind(t,79939))},79939:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ea});var a=t(10326),r=t(5158),n=t(92392),i=t(14926),l=t(82548),c=t(48054),d=t(28758),o=t(55632),m=t(8281),x=t(49547),u=t(10734);async function f(e){try{let s=await x.Z.post("/transfers/create",e);return(0,u.B)(s)}catch(e){return(0,u.D)(e)}}var h=t(81431),j=t(19395),p=t(77863),v=t(54033),g=t(74064),N=t(64561),b=t(35047),y=t(17577),w=t.n(y),z=t(74723),Z=t(70012),C=t(85999),$=t(27256),k=t(74743),T=t(90772),I=t(54432),S=t(44284);function P({form:e,isCheckingUser:s,onNext:t}){let{t:i}=(0,Z.$G)();return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h2",{children:i("Add recipient")}),a.jsx(o.Wi,{name:"email",control:e.control,render:({field:e})=>(0,a.jsxs)(o.xJ,{className:"w-full",children:[a.jsx(o.NI,{children:a.jsx(I.I,{type:"text",placeholder:i("Enter recipient’s email"),...e})}),a.jsx(o.zG,{})]})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h2",{children:i("Select wallet")}),a.jsx(o.Wi,{name:"currencyCode",control:e.control,render:({field:e})=>(0,a.jsxs)(o.xJ,{children:[a.jsx(o.NI,{children:a.jsx(k.R,{...e})}),a.jsx(o.zG,{})]})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h2",{children:i("Add amount")}),a.jsx(o.Wi,{name:"amount",control:e.control,render:({field:e})=>(0,a.jsxs)(o.xJ,{children:[a.jsx(o.NI,{children:a.jsx(I.I,{type:"number",min:0,placeholder:i("Enter transfer amount"),...e})}),a.jsx(o.zG,{})]})})]}),a.jsx("div",{className:"flex justify-end",children:(0,a.jsxs)(T.z,{type:"submit",onClick:t,className:"min-w-48",disabled:s,children:[a.jsx(r.J,{condition:s,children:a.jsx(n.Loader,{title:i("Checking..."),className:"text-primary-foreground"})}),(0,a.jsxs)(r.J,{condition:!s,children:[a.jsx("span",{children:i("Next")}),a.jsx(S.Z,{size:16})]})]})})]})}var F=t(29197),L=t(12649),E=t(45806),A=t(91778),q=t(567),D=t(60097),_=t(30414),B=t(30464),G=t(60814),R=t(47237),J=t(31112),Q=t(7310),V=t(31739),O=t(62047),X=t(66678),U=t(86169),M=t(90434),W=t(84077);function Y({res:e,user:s,onTransferAgain:t}){let{t:n}=(0,Z.$G)(),i=e?.data,{getWalletByCurrencyCode:l,wallets:c}=(0,G.r)(),o=l(c,i?.from?.currency),x=e=>{C.toast.promise((0,B.y)(`${e}`),{loading:n("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return e.message},error:e=>e.message})};return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,a.jsxs)("h2",{className:"mb-1 flex items-center justify-center gap-2 text-2xl font-semibold text-foreground",children:[a.jsx(R.Z,{size:"32",color:"#13A10E",variant:"Bold"}),a.jsx("span",{children:n("Transfer successful")})]}),a.jsx(m.Z,{className:"mb-1 mt-[5px] bg-divider"}),a.jsx(A.z,{senderName:i?.from?.label,senderAvatar:(0,p.qR)(i?.from?.image),receiverName:i?.to?.label,receiverAvatar:(0,p.qR)(i?.to?.image)}),(0,a.jsxs)(L.Y,{groupName:n("Transfer details"),children:[a.jsx(L.r,{title:`${i?.to?.label} ${n("will get")}`,value:`${i?.total} ${i?.from?.currency}`}),a.jsx(L.r,{title:n("Service charge"),value:(0,a.jsxs)(a.Fragment,{children:[a.jsx(r.J,{condition:i?.fee,children:`${i?.fee} ${i?.from?.currency}`}),a.jsx(r.J,{condition:0===Number(i?.fee),children:a.jsx(q.C,{variant:"success",children:n("Free")})})]})}),a.jsx(L.r,{title:n("Total"),value:`${i?.amount} ${i?.from?.currency}`,valueClassName:"text-xl sm:text-2xl font-semibold"})]}),a.jsx(m.Z,{className:"mb-1 mt-[5px] bg-divider"}),a.jsx(E.T,{id:i?.trxId,className:"mb-4 text-sm sm:text-base"}),(0,a.jsxs)("div",{className:"mb-8 space-y-4 text-sm sm:text-base",children:[a.jsx("h4",{className:"text-base font-medium sm:text-lg",children:n("New balance")}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(d.qE,{children:[a.jsx(d.F$,{src:o?.logo}),a.jsx(d.Q5,{className:"bg-important text-important-foreground",children:o?.currency?.code})]}),a.jsx("span",{className:"text-sm font-bold",children:o?.currency?.code})]}),a.jsx("p",{className:"font-medium",children:`${o?.balance} ${o?.currency?.code}`})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[a.jsx(F.T,{trxId:i?.trxId,className:"w-full md:w-auto"}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap gap-4 md:w-auto md:justify-end",children:[(0,a.jsxs)(D.h_,{children:[(0,a.jsxs)(D.$F,{className:(0,p.ZP)("flex w-full items-center space-x-1.5 md:w-fit",(0,T.d)({variant:"outline"})),children:[a.jsx("span",{children:n("Menu")}),a.jsx(J.Z,{size:16})]}),(0,a.jsxs)(D.AW,{align:"start",className:"m-0",children:[(0,a.jsxs)(D.Xi,{onSelect:()=>(0,p.Fp)(i?.trxId),className:"flex items-center gap-2 text-sm font-medium focus:text-primary [&>svg]:hover:text-primary",children:[a.jsx(Q.Z,{size:"20",variant:"Outline"}),n("Copy transaction ID")]}),(0,a.jsxs)(D.Xi,{onSelect:()=>{C.toast.promise((0,_.Az)({contactId:s?.id}),{loading:n("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return(0,W.j)("/contacts"),e.message},error:e=>e.message})},className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[a.jsx(V.Z,{size:"20",variant:"Outline"}),n("Add to contact")]}),(0,a.jsxs)(D.Xi,{onSelect:()=>x(i?.id),className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[a.jsx(O.Z,{size:"20",variant:"Outline"}),n("Bookmark receipt")]}),a.jsx(D.VD,{}),(0,a.jsxs)(D.Xi,{onSelect:t,className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[a.jsx(X.Z,{size:"20",variant:"Outline"}),n("Transfer again")]})]})]}),a.jsx(T.z,{type:"button",className:"w-full md:max-w-48",asChild:!0,children:(0,a.jsxs)(M.default,{href:"/",children:[a.jsx("span",{children:n("Go to dashboard")}),a.jsx(U.Z,{size:16})]})})]})]})]})}var H=t(65304),K=t(90799),ee=t(44221);function es({onNext:e,onPrev:s,nextButtonLabel:t,isLoading:i=!1,formData:l,user:c}){let{t:o}=(0,Z.$G)(),{data:x,isLoading:u}=(0,K.d)(`/transfers/preview/create?amount=${l.amount}`),{wallets:f,isLoading:h,getWalletByCurrencyCode:j}=(0,G.r)(),g=new p.F(l.currencyCode),N=j(f,l.currencyCode);return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[a.jsx("h2",{children:o("Confirm and proceed")}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h5",{className:"text-sm font-medium sm:text-base",children:o("Selected wallet")}),a.jsx("div",{className:"flex flex-row items-center gap-2.5",children:h?(0,a.jsxs)(a.Fragment,{children:[a.jsx(H.O,{className:"size-8 rounded-full"}),a.jsx(H.O,{className:"h-4 w-20 rounded-full"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(d.qE,{className:"size-8",children:[a.jsx(d.F$,{src:N?.logo}),a.jsx(d.Q5,{className:"bg-important text-xs font-bold text-important-foreground",children:N?.currency?.code})]}),a.jsx("h6",{className:"font-bold",children:N?.currency?.code})]})})]}),a.jsx(m.Z,{className:"mb-1 mt-[5px] bg-divider"}),(0,a.jsxs)(L.Y,{groupName:o("Transfer details"),children:[a.jsx(L.r,{title:`${c?.name} ${o("will get")}`,value:g.formatVC(x?.data?.totalAmount),isLoading:u}),a.jsx(L.r,{title:o("Service charge"),isLoading:u,value:x?.data?.fee>0?g.formatVC(x?.data?.fee):a.jsx(q.C,{variant:"success",children:o("Free")})}),a.jsx(L.r,{title:o("Total"),value:g.formatVC(x?.data?.formatedAmount),isLoading:u})]}),a.jsx(m.Z,{className:"mb-1 mt-[5px] bg-divider"}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h5",{className:"text-sm font-medium sm:text-base",children:o("Recipient")}),(0,a.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,a.jsxs)(d.qE,{children:[a.jsx(d.F$,{src:(0,p.qR)(c?.avatar)}),a.jsx(d.Q5,{children:(0,v.v)(c?.name)})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-0.5",children:[a.jsx("p",{className:"text-sm font-medium",children:c?.name}),a.jsx("p",{className:"text-xs text-secondary-text",children:c?.email})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex justify-end gap-4",children:[(0,a.jsxs)(T.z,{variant:"outline",onClick:s,type:"button",children:[a.jsx(ee.Z,{size:16}),a.jsx("span",{children:o("Back")})]}),(0,a.jsxs)(T.z,{type:"submit",onClick:e,disabled:i,className:"min-w-48",children:[(0,a.jsxs)(r.J,{condition:!i,children:[a.jsx("span",{children:t}),a.jsx(S.Z,{size:16})]}),a.jsx(r.J,{condition:i,children:a.jsx(n.Loader,{title:o("Processing..."),className:"text-primary-foreground"})})]})]})]})}let et=$.z.object({email:$.z.string().min(1,"Recipient email is required."),amount:$.z.string().min(1,"Transfer amount is required."),currencyCode:$.z.string({required_error:"Currency is required."})});function ea(){let{auth:e}=(0,j.a)(),{t:s}=(0,Z.$G)(),t=(0,b.useSearchParams)(),[u,$]=(0,y.useState)("transfer_details"),[k,T]=(0,y.useState)(null),[I,S]=(0,y.useTransition)(),[F,L]=(0,y.useState)(null),[E,A]=(0,y.useState)(!1),{contacts:q,isLoading:D}=(0,h.t)();t.get("email");let[_,B]=(0,y.useState)([{id:"transfer_details",value:"transfer_details",title:s("Transfer details"),complete:!1},{id:"review",value:"review",title:s("Review"),complete:!1},{id:"finish",value:"finish",title:s("Finish"),complete:!1}]),G=(0,z.cI)({resolver:(0,g.F)(et),defaultValues:{email:"",amount:"",currencyCode:""},mode:"all"}),R=e=>{B(s=>s.map(s=>s.id===e?{...s,complete:!0}:s))};if(!e?.canMakeTransfer())return a.jsx(i.Z,{className:"flex-1 p-10"});let J=async()=>{let e=G.getValues("email");A(!0);try{let s=await x.Z.get(`/users/check/?email=${e}`);if(s&&s.status){A(!1);let e=s?.data;L({id:e?.id,avatar:e?.profileImage,name:e?.name,email:e?.email}),$("review"),R("transfer_details")}}catch(e){A(!1),C.toast.error(s("User not found.")),G.setError("email",{message:s("User not found."),type:"required"})}};return a.jsx(l.Xg,{children:a.jsx(o.l0,{...G,children:a.jsx("form",{className:"md:h-full",children:(0,a.jsxs)("div",{className:"relative flex md:h-full",children:[a.jsx("div",{className:"w-full p-4 pb-10 md:h-full md:p-12",children:(0,a.jsxs)("div",{className:"mx-auto max-w-3xl",children:[a.jsx(r.J,{condition:"transfer_details"===u,children:a.jsx(l.cI,{})}),a.jsx(c.R,{tabs:_,value:u,onTabChange:e=>$(e),children:(0,a.jsxs)("div",{className:"p-4",children:[a.jsx(c.Q,{value:"transfer_details",children:a.jsx(P,{form:G,isCheckingUser:E,onNext:G.handleSubmit((e,s)=>{s?.preventDefault(),J()})})}),a.jsx(c.Q,{value:"review",children:a.jsx(es,{onPrev:()=>$("transfer_details"),onNext:G.handleSubmit(e=>{if(k){$("finish");return}S(async()=>{let t=await f(e);t&&t.status?($("finish"),R("review"),R("finish"),T(t),C.toast.success(t.message)):C.toast.error(s(t.message))})}),nextButtonLabel:s("Transfer"),isLoading:I,formData:G.getValues(),user:F})}),a.jsx(c.Q,{value:"finish",children:a.jsx(Y,{res:k,user:F,onTransferAgain:()=>{B([{id:"transfer_details",value:"transfer_details",title:s("Transfer details"),complete:!1},{id:"review",value:"review",title:s("Review"),complete:!1},{id:"finish",value:"finish",title:s("Finish"),complete:!1}]),G.reset(),$("transfer_details"),T(null),L(null)}})})]})})]})}),a.jsx(r.J,{condition:"transfer_details"===u,children:a.jsx(l.jT,{children:(0,a.jsxs)("div",{className:"mb-4 rounded-xl bg-background p-6 shadow-default",children:[(0,a.jsxs)("div",{className:"mb-6 border-b border-divider-secondary pb-6",children:[a.jsx("p",{className:"mb-2 font-medium text-foreground",children:s("Contacts")}),a.jsx("p",{className:"text-xs text-secondary-text",children:s("Click to autofill recipient")})]}),(0,a.jsxs)("div",{className:"flex h-full max-h-72 flex-col overflow-y-auto",children:[!D&&(!q||q?.length===0)&&a.jsx("p",{className:"text-sm font-medium text-foreground/50",children:s("No data found")}),D?a.jsx(n.Loader,{}):q?.map(e=>a.jsxs(w().Fragment,{children:[a.jsx("div",{role:"presentation",onClick:()=>{L({id:e.contact.id,avatar:e.contact.customer.profileImage,email:e.contact.email,name:e.contact.customer.name}),G.setValue("email",e.email)},className:"flex items-center justify-between py-2 first:pt-0 hover:cursor-pointer",children:a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsxs(d.qE,{className:"size-8",children:[a.jsx(d.F$,{src:p.qR(e?.contact?.customer?.profileImage)}),a.jsx(d.Q5,{className:"font-semibold",children:v.v(e?.contact?.customer?.name)})]}),a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-normal leading-5 text-foreground",children:e?.contact?.customer?.name}),a.jsx("p",{className:"text-xs font-normal leading-4 text-secondary-text",children:e?.contact?.customer?.phone&&N.h(p.Fg(e?.contact?.customer?.phone)).formatInternational()})]})]})}),a.jsx(m.Z,{className:"mb-1 mt-[5px]"})]},e.id))]})]})})})]})})})})}},29197:(e,s,t)=>{"use strict";t.d(s,{T:()=>d});var a=t(10326),r=t(90772),n=t(98196),i=t(77863),l=t(88010),c=t(70012);function d({trxId:e,className:s}){let{t}=(0,c.$G)();return a.jsx(r.z,{variant:"outline",type:"button",className:(0,i.ZP)("w-full md:w-auto",s),asChild:!0,children:(0,a.jsxs)("a",{href:`${n.rH.API_URL}/transactions/download-receipt/${e}`,children:[a.jsx(l.Z,{size:16}),a.jsx("span",{children:t("Download Receipt")})]})})}},14926:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});var a=t(10326),r=t(77863),n=t(90434),i=t(70012);function l({className:e}){let{t:s}=(0,i.$G)();return a.jsx("div",{className:(0,r.ZP)("flex items-center justify-center",e),children:(0,a.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[a.jsx("h3",{className:"mb-2.5",children:s("This feature is temporarily unavailable")}),(0,a.jsxs)("p",{className:"text-sm text-secondary-text",children:[s("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),a.jsx(n.default,{href:"/contact-supports",className:"text-primary hover:underline",children:s("support")}),"."]}),a.jsx("p",{className:"mt-2 text-sm text-secondary-text",children:s("Thank you for your understanding.")})]})})}},82548:(e,s,t)=>{"use strict";t.d(s,{Xg:()=>f,cI:()=>u,jT:()=>h});var a=t(10326),r=t(90772),n=t(95028),i=t(77863),l=t(62047),c=t(44221),d=t(17577),o=t(70012);let m=d.createContext(null),x=()=>{let e=d.useContext(m);if(!e)throw Error("usePageLayout must be used within an PageLayoutCtx. Please ensure that your component is wrapped with an PageLayoutCtx.");return e};function u({className:e}){let{t:s}=(0,o.$G)(),{setRightSidebar:t}=x();return a.jsx("div",{className:(0,i.ZP)("flex items-center justify-end md:mb-4 xl:hidden",e),children:(0,a.jsxs)(r.z,{onClick:()=>t(e=>!e),variant:"outline",size:"sm",type:"button",className:"text-sm",children:[a.jsx(l.Z,{size:"20"}),s("Bookmarks")]})})}function f({children:e}){let[s,t]=d.useState(!1),{width:r}=(0,n.B)();d.useEffect(()=>{r>=1280&&t(!0)},[r]);let i=d.useMemo(()=>({width:r,rightSidebar:s,setRightSidebar:t}),[r,s]);return a.jsx(m.Provider,{value:i,children:e})}function h({children:e}){let{t:s}=(0,o.$G)(),{width:t,rightSidebar:n,setRightSidebar:i}=x();return(0,a.jsxs)("div",{"data-expanded":t>=1280||t<1280&&n,className:"absolute inset-y-0 right-0 top-0 w-full max-w-96 translate-x-full bg-background-body p-6 transition-all duration-300 ease-in-out data-[expanded=true]:translate-x-0 xl:relative",children:[(0,a.jsxs)(r.z,{variant:"outline",size:"sm",type:"button",onClick:()=>i(!1),className:"mb-4 gap-[2px] bg-background text-sm hover:bg-background xl:hidden",children:[a.jsx(c.Z,{size:14}),s("Hide bookmarks")]}),e]})}},45806:(e,s,t)=>{"use strict";t.d(s,{T:()=>d});var a=t(10326),r=t(90772),n=t(77863),i=t(7310),l=t(70012),c=t(85999);function d({id:e,className:s}){let{t}=(0,l.$G)();return(0,a.jsxs)("div",{className:(0,n.ZP)("inline-flex w-full items-center gap-4",s),children:[a.jsx("div",{className:"flex-1",children:t("Transaction ID")}),(0,a.jsxs)("div",{className:"inline-flex items-center gap-4",children:[a.jsx("span",{children:e}),a.jsx(r.z,{type:"button",onClick:()=>{navigator.clipboard.writeText(e).then(()=>c.toast.success("Copied to clipboard!")).catch(()=>{c.toast.error("Failed to copy!")})},variant:"outline",size:"sm",children:a.jsx(i.Z,{size:"20"})})]})]})}},91778:(e,s,t)=>{"use strict";t.d(s,{z:()=>c});var a=t(10326),r=t(28758),n=t(77863),i=t(54033),l=t(47237);function c({senderName:e,senderAvatar:s,senderInfo:t,receiverName:r,receiverAvatar:i,receiverInfo:l,className:c}){return(0,a.jsxs)("div",{className:(0,n.ZP)("mb-4 flex items-start justify-around gap-1",c),children:[a.jsx(d,{name:e,avatar:s,info:t}),r&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),a.jsx(d,{name:r,avatar:i,info:l})]})]})}function d({avatar:e,name:s,info:t=[]}){let n=t.filter(Boolean);return(0,a.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,a.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,a.jsxs)(r.qE,{className:"size-10 rounded-full sm:size-14",children:[a.jsx(r.F$,{src:e,alt:s,width:56,height:56}),a.jsx(r.Q5,{className:"font-semibold",children:(0,i.v)(s)})]}),a.jsx("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:a.jsx(l.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:s}),n.length>0&&n.map((e,s)=>a.jsx("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},s))]})]})}},30414:(e,s,t)=>{"use strict";t.d(s,{Az:()=>l,Ch:()=>i,hs:()=>n});var a=t(49547),r=t(10734);async function n(e){try{let s=await a.Z.post("/merchants/save",e);return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}async function i(e){try{let s=await a.Z.post("/services/phone/save",e);return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}async function l(e){try{if(!e.contactId)throw Error("contact id not found");let s=await a.Z.post("/contacts/create",e);return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}},30464:(e,s,t)=>{"use strict";t.d(s,{y:()=>n});var a=t(49547),r=t(10734);async function n(e,s){try{let t=await a.Z.put(`${s??"/transactions/toggle-bookmark"}/${e}`,{id:e});return(0,r.B)(t)}catch(e){return(0,r.D)(e)}}},81431:(e,s,t)=>{"use strict";t.d(s,{t:()=>n});var a=t(84455),r=t(49547);function n(e){let{data:s,isLoading:t,error:n,mutate:i,...l}=(0,a.ZP)(e||"/contacts",e=>r.Z.get(e));return{contacts:s?.data??[],isLoading:t,error:n,mutate:()=>i(s),...l}}},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(40099),n=t(76609);function i({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(n.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},45486:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(48413);function n(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},35652:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\transfer\page.tsx#default`)},39839:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(35652);function n(){return a.jsx(r.default,{})}}};