"use client";

import { useTranslation } from "react-i18next";
import { SystemHealth } from "@/types/admin";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Progress from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock,
  Bell,
  Shield,
  Target,
  RotateCcw
} from "lucide-react";

interface SystemHealthCardProps {
  health: SystemHealth;
}

export function SystemHealthCard({ health }: SystemHealthCardProps) {
  const { t } = useTranslation();

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthBadgeColor = (score: number) => {
    if (score >= 90) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 70) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getHealthStatus = (score: number) => {
    if (score >= 90) return t('Excellent');
    if (score >= 70) return t('Good');
    return t('Needs Attention');
  };

  const healthIssues = [
    {
      type: 'notifications',
      count: health.pendingNotifications + health.failedNotifications,
      label: t('Notification Issues'),
      icon: <Bell className="h-4 w-4" />,
      severity: health.failedNotifications > 0 ? 'high' : 'medium'
    },
    {
      type: 'escrows',
      count: health.expiredEscrows,
      label: t('Expired Escrows'),
      icon: <Shield className="h-4 w-4" />,
      severity: health.expiredEscrows > 10 ? 'high' : 'medium'
    },
    {
      type: 'transfers',
      count: health.failedRecurringTransfers,
      label: t('Failed Transfers'),
      icon: <RotateCcw className="h-4 w-4" />,
      severity: health.failedRecurringTransfers > 5 ? 'high' : 'medium'
    },
    {
      type: 'pools',
      count: health.expiredFundraisingPools,
      label: t('Expired Pools'),
      icon: <Target className="h-4 w-4" />,
      severity: 'low'
    }
  ].filter(issue => issue.count > 0);

  return (
    <div className="space-y-6">
      {/* Overall Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>{t('System Health Score')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className={`text-4xl font-bold ${getHealthColor(health.healthScore)}`}>
                {health.healthScore}%
              </div>
              <Badge 
                variant="outline" 
                className={`mt-2 ${getHealthBadgeColor(health.healthScore)}`}
              >
                {getHealthStatus(health.healthScore)}
              </Badge>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">{t('Total Issues')}</p>
              <p className="text-2xl font-semibold">{health.totalIssues}</p>
            </div>
          </div>
          
          <Progress value={health.healthScore} className="h-3" />
          
          <div className="text-sm text-muted-foreground">
            {health.healthScore >= 90 && (
              <p className="flex items-center space-x-1 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>{t('All systems are operating normally')}</span>
              </p>
            )}
            {health.healthScore >= 70 && health.healthScore < 90 && (
              <p className="flex items-center space-x-1 text-yellow-600">
                <AlertTriangle className="h-4 w-4" />
                <span>{t('Some issues detected but system is stable')}</span>
              </p>
            )}
            {health.healthScore < 70 && (
              <p className="flex items-center space-x-1 text-red-600">
                <XCircle className="h-4 w-4" />
                <span>{t('Multiple issues require immediate attention')}</span>
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Health Issues Breakdown */}
      {healthIssues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>{t('Issues Requiring Attention')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {healthIssues.map((issue, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${
                    issue.severity === 'high' ? 'bg-red-100 text-red-600' :
                    issue.severity === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                    'bg-blue-100 text-blue-600'
                  }`}>
                    {issue.icon}
                  </div>
                  <div>
                    <p className="font-medium">{issue.label}</p>
                    <p className="text-sm text-muted-foreground">
                      {issue.count} {t('items need attention')}
                    </p>
                  </div>
                </div>
                <Badge 
                  variant="outline" 
                  className={
                    issue.severity === 'high' ? 'border-red-200 text-red-800' :
                    issue.severity === 'medium' ? 'border-yellow-200 text-yellow-800' :
                    'border-blue-200 text-blue-800'
                  }
                >
                  {issue.count}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Detailed Health Metrics */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>{t('Notification Health')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">{t('Pending')}</p>
                <p className="text-lg font-semibold">{health.pendingNotifications}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t('Failed')}</p>
                <p className="text-lg font-semibold text-red-600">{health.failedNotifications}</p>
              </div>
            </div>
            
            {health.failedNotifications > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {t('{{count}} notifications have failed delivery', {
                    count: health.failedNotifications
                  })}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>{t('Escrow Health')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground">{t('Expired Escrows')}</p>
              <p className="text-lg font-semibold text-orange-600">{health.expiredEscrows}</p>
            </div>
            
            {health.expiredEscrows > 0 && (
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  {t('{{count}} escrows have expired and may need admin intervention', {
                    count: health.expiredEscrows
                  })}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <RotateCcw className="h-5 w-5" />
              <span>{t('Transfer Health')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground">{t('Failed Transfers')}</p>
              <p className="text-lg font-semibold text-red-600">{health.failedRecurringTransfers}</p>
            </div>
            
            {health.failedRecurringTransfers > 0 && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  {t('{{count}} recurring transfers have failed execution', {
                    count: health.failedRecurringTransfers
                  })}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>{t('Pool Health')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground">{t('Expired Pools')}</p>
              <p className="text-lg font-semibold text-gray-600">{health.expiredFundraisingPools}</p>
            </div>
            
            {health.expiredFundraisingPools > 0 && (
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  {t('{{count}} fundraising pools have expired', {
                    count: health.expiredFundraisingPools
                  })}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Health Recommendations */}
      {health.healthScore < 90 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5" />
              <span>{t('Recommendations')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {health.failedNotifications > 0 && (
                <p className="text-sm">• {t('Review and retry failed notifications')}</p>
              )}
              {health.expiredEscrows > 0 && (
                <p className="text-sm">• {t('Review expired escrows for potential disputes')}</p>
              )}
              {health.failedRecurringTransfers > 0 && (
                <p className="text-sm">• {t('Investigate and retry failed recurring transfers')}</p>
              )}
              {health.pendingNotifications > 100 && (
                <p className="text-sm">• {t('Check notification queue processing')}</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
