"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[17373],{36887:function(e,t,r){r.d(t,{Z:function(){return v}});var n=r(74677),o=r(2265),a=r(40718),i=r.n(a),l=["variant","color","size"],u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M17.919 8.18H6.079c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M16.01 12.85l-2.62 2.62c-.77.77-2.03.77-2.8 0L4.08 8.95M19.92 8.95l-1.04 1.04"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M15.48 13.23l-3.79-5.05H6.08c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97z",opacity:".4"}),o.createElement("path",{fill:t,d:"M17.92 8.18h-6.23l3.79 5.05 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},f=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M12 16.8c-.7 0-1.4-.27-1.93-.8L3.55 9.48a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.48.48 1.26.48 1.74 0l6.52-6.52c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L13.93 16c-.53.53-1.23.8-1.93.8z"}))},p=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},m=function(e,t){switch(e){case"Bold":return o.createElement(u,{color:t});case"Broken":return o.createElement(c,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(f,{color:t});case"TwoTone":return o.createElement(p,{color:t})}},v=(0,o.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,u=(0,n._)(e,l);return o.createElement("svg",(0,n.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),m(r,a))});v.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},v.defaultProps={variant:"Linear",color:"currentColor",size:"24"},v.displayName="ArrowDown2"},40519:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},99376:function(e,t,r){var n=r(35475);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useSelectedLayoutSegment")&&r.d(t,{useSelectedLayoutSegment:function(){return n.useSelectedLayoutSegment}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})},12119:function(e,t,r){Object.defineProperty(t,"$",{enumerable:!0,get:function(){return o}});let n=r(83079);function o(e){let{createServerReference:t}=r(6671);return t(e,n.callServer)}},13134:function(e,t,r){r.d(t,{VY:function(){return ei},h4:function(){return eo},ck:function(){return en},fC:function(){return er},xz:function(){return ea}});var n=r(2265),o=r(73966),a=r(58068),i=r(98575),l=r(6741),u=r(80886),c=r(66840),s=r(61188),d=r(71599),f=r(99255),p=r(57437),m="Collapsible",[v,h]=(0,o.b)(m),[b,w]=v(m),g=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:a,disabled:i,onOpenChange:l,...s}=e,[d,v]=(0,u.T)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:m});return(0,p.jsx)(b,{scope:r,disabled:i,contentId:(0,f.M)(),open:d,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(c.WV.div,{"data-state":j(d),"data-disabled":i?"":void 0,...s,ref:t})})});g.displayName=m;var y="CollapsibleTrigger",k=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,o=w(y,r);return(0,p.jsx)(c.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":j(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:t,onClick:(0,l.M)(e.onClick,o.onOpenToggle)})});k.displayName=y;var R="CollapsibleContent",x=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=w(R,e.__scopeCollapsible);return(0,p.jsx)(d.z,{present:r||o.open,children:e=>{let{present:r}=e;return(0,p.jsx)(E,{...n,ref:t,present:r})}})});x.displayName=R;var E=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:a,...l}=e,u=w(R,r),[d,f]=n.useState(o),m=n.useRef(null),v=(0,i.e)(t,m),h=n.useRef(0),b=h.current,g=n.useRef(0),y=g.current,k=u.open||d,x=n.useRef(k),E=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=m.current;if(e){E.current=E.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();h.current=t.height,g.current=t.width,x.current||(e.style.transitionDuration=E.current.transitionDuration,e.style.animationName=E.current.animationName),f(o)}},[u.open,o]),(0,p.jsx)(c.WV.div,{"data-state":j(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!k,...l,ref:v,style:{"--radix-collapsible-content-height":b?"".concat(b,"px"):void 0,"--radix-collapsible-content-width":y?"".concat(y,"px"):void 0,...e.style},children:k&&a})});function j(e){return e?"open":"closed"}var N=r(29114),C="Accordion",A=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[M,I,T]=(0,a.B)(C),[L,S]=(0,o.b)(C,[T,h]),P=h(),D=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,p.jsx)(M.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,p.jsx)(V,{...n,ref:t}):(0,p.jsx)(U,{...n,ref:t})})});D.displayName=C;var[O,F]=L(C),[_,W]=L(C,{collapsible:!1}),U=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},collapsible:i=!1,...l}=e,[c,s]=(0,u.T)({prop:r,defaultProp:null!=o?o:"",onChange:a,caller:C});return(0,p.jsx)(O,{scope:e.__scopeAccordion,value:n.useMemo(()=>c?[c]:[],[c]),onItemOpen:s,onItemClose:n.useCallback(()=>i&&s(""),[i,s]),children:(0,p.jsx)(_,{scope:e.__scopeAccordion,collapsible:i,children:(0,p.jsx)(K,{...l,ref:t})})})}),V=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},...i}=e,[l,c]=(0,u.T)({prop:r,defaultProp:null!=o?o:[],onChange:a,caller:C}),s=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),d=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,p.jsx)(O,{scope:e.__scopeAccordion,value:l,onItemOpen:s,onItemClose:d,children:(0,p.jsx)(_,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(K,{...i,ref:t})})})}),[z,B]=L(C),K=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:o,dir:a,orientation:u="vertical",...s}=e,d=n.useRef(null),f=(0,i.e)(d,t),m=I(r),v="ltr"===(0,N.gm)(a),h=(0,l.M)(e.onKeyDown,e=>{var t;if(!A.includes(e.key))return;let r=e.target,n=m().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),o=n.findIndex(e=>e.ref.current===r),a=n.length;if(-1===o)return;e.preventDefault();let i=o,l=a-1,c=()=>{(i=o+1)>l&&(i=0)},s=()=>{(i=o-1)<0&&(i=l)};switch(e.key){case"Home":i=0;break;case"End":i=l;break;case"ArrowRight":"horizontal"===u&&(v?c():s());break;case"ArrowDown":"vertical"===u&&c();break;case"ArrowLeft":"horizontal"===u&&(v?s():c());break;case"ArrowUp":"vertical"===u&&s()}null===(t=n[i%a].ref.current)||void 0===t||t.focus()});return(0,p.jsx)(z,{scope:r,disabled:o,direction:a,orientation:u,children:(0,p.jsx)(M.Slot,{scope:r,children:(0,p.jsx)(c.WV.div,{...s,"data-orientation":u,ref:f,onKeyDown:o?void 0:h})})})}),G="AccordionItem",[H,q]=L(G),Z=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...o}=e,a=B(G,r),i=F(G,r),l=P(r),u=(0,f.M)(),c=n&&i.value.includes(n)||!1,s=a.disabled||e.disabled;return(0,p.jsx)(H,{scope:r,open:c,disabled:s,triggerId:u,children:(0,p.jsx)(g,{"data-orientation":a.orientation,"data-state":et(c),...l,...o,ref:t,disabled:s,open:c,onOpenChange:e=>{e?i.onItemOpen(n):i.onItemClose(n)}})})});Z.displayName=G;var X="AccordionHeader",$=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=B(C,r),a=q(X,r);return(0,p.jsx)(c.WV.h3,{"data-orientation":o.orientation,"data-state":et(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});$.displayName=X;var Y="AccordionTrigger",J=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=B(C,r),a=q(Y,r),i=W(Y,r),l=P(r);return(0,p.jsx)(M.ItemSlot,{scope:r,children:(0,p.jsx)(k,{"aria-disabled":a.open&&!i.collapsible||void 0,"data-orientation":o.orientation,id:a.triggerId,...l,...n,ref:t})})});J.displayName=Y;var Q="AccordionContent",ee=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=B(C,r),a=q(Q,r),i=P(r);return(0,p.jsx)(x,{role:"region","aria-labelledby":a.triggerId,"data-orientation":o.orientation,...i,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=Q;var er=D,en=Z,eo=$,ea=J,ei=ee},71599:function(e,t,r){r.d(t,{z:function(){return i}});var n=r(2265),o=r(98575),a=r(61188),i=e=>{var t,r;let i,u;let{present:c,children:s}=e,d=function(e){var t,r;let[o,i]=n.useState(),u=n.useRef(null),c=n.useRef(e),s=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(u.current);s.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let t=u.current,r=c.current;if(r!==e){let n=s.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,a.b)(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=l(u.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(s.current=l(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(c),f="function"==typeof s?s({present:d.isPresent}):n.Children.only(s),p=(0,o.e)(d.ref,(i=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in i&&i.isReactWarning?f.ref:(i=null===(r=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in i&&i.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?n.cloneElement(f,{ref:p}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},42325:function(e,t,r){r.d(t,{ck:function(){return _},fC:function(){return F},z$:function(){return W}});var n=r(2265),o=r(6741),a=r(98575),i=r(73966),l=r(66840),u=r(1353),c=r(80886),s=r(29114),d=r(90420),f=r(6718),p=r(71599),m=r(57437),v="Radio",[h,b]=(0,i.b)(v),[w,g]=h(v),y=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:u=!1,required:c,disabled:s,value:d="on",onCheck:f,form:p,...v}=e,[h,b]=n.useState(null),g=(0,a.e)(t,e=>b(e)),y=n.useRef(!1),k=!h||p||!!h.closest("form");return(0,m.jsxs)(w,{scope:r,checked:u,disabled:s,children:[(0,m.jsx)(l.WV.button,{type:"button",role:"radio","aria-checked":u,"data-state":E(u),"data-disabled":s?"":void 0,disabled:s,value:d,...v,ref:g,onClick:(0,o.M)(e.onClick,e=>{u||null==f||f(),k&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),k&&(0,m.jsx)(x,{control:h,bubbles:!y.current,name:i,value:d,checked:u,required:c,disabled:s,form:p,style:{transform:"translateX(-100%)"}})]})});y.displayName=v;var k="RadioIndicator",R=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,a=g(k,r);return(0,m.jsx)(p.z,{present:n||a.checked,children:(0,m.jsx)(l.WV.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t})})});R.displayName=k;var x=n.forwardRef((e,t)=>{let{__scopeRadio:r,control:o,checked:i,bubbles:u=!0,...c}=e,s=n.useRef(null),p=(0,a.e)(s,t),v=(0,f.D)(i),h=(0,d.t)(o);return n.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==i&&t){let r=new Event("click",{bubbles:u});t.call(e,i),e.dispatchEvent(r)}},[v,i,u]),(0,m.jsx)(l.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...c,tabIndex:-1,ref:p,style:{...c.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function E(e){return e?"checked":"unchecked"}x.displayName="RadioBubbleInput";var j=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],N="RadioGroup",[C,A]=(0,i.b)(N,[u.Pc,b]),M=(0,u.Pc)(),I=b(),[T,L]=C(N),S=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:a,required:i=!1,disabled:d=!1,orientation:f,dir:p,loop:v=!0,onValueChange:h,...b}=e,w=M(r),g=(0,s.gm)(p),[y,k]=(0,c.T)({prop:a,defaultProp:null!=o?o:"",onChange:h,caller:N});return(0,m.jsx)(T,{scope:r,name:n,required:i,disabled:d,value:y,onValueChange:k,children:(0,m.jsx)(u.fC,{asChild:!0,...w,orientation:f,dir:g,loop:v,children:(0,m.jsx)(l.WV.div,{role:"radiogroup","aria-required":i,"aria-orientation":f,"data-disabled":d?"":void 0,dir:g,...b,ref:t})})})});S.displayName=N;var P="RadioGroupItem",D=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...l}=e,c=L(P,r),s=c.disabled||i,d=M(r),f=I(r),p=n.useRef(null),v=(0,a.e)(t,p),h=c.value===l.value,b=n.useRef(!1);return n.useEffect(()=>{let e=e=>{j.includes(e.key)&&(b.current=!0)},t=()=>b.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,m.jsx)(u.ck,{asChild:!0,...d,focusable:!s,active:h,children:(0,m.jsx)(y,{disabled:s,required:c.required,checked:h,...f,...l,name:c.name,ref:v,onCheck:()=>c.onValueChange(l.value),onKeyDown:(0,o.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.M)(l.onFocus,()=>{var e;b.current&&(null===(e=p.current)||void 0===e||e.click())})})})});D.displayName=P;var O=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=I(r);return(0,m.jsx)(R,{...o,...n,ref:t})});O.displayName="RadioGroupIndicator";var F=S,_=D,W=O},1353:function(e,t,r){r.d(t,{Pc:function(){return k},ck:function(){return T},fC:function(){return I}});var n=r(2265),o=r(6741),a=r(58068),i=r(98575),l=r(73966),u=r(99255),c=r(66840),s=r(26606),d=r(80886),f=r(29114),p=r(57437),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[b,w,g]=(0,a.B)(h),[y,k]=(0,l.b)(h,[g]),[R,x]=y(h),E=n.forwardRef((e,t)=>(0,p.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:t})})}));E.displayName=h;var j=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:l=!1,dir:u,currentTabStopId:b,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:k,preventScrollOnEntryFocus:x=!1,...E}=e,j=n.useRef(null),N=(0,i.e)(t,j),C=(0,f.gm)(u),[A,I]=(0,d.T)({prop:b,defaultProp:null!=g?g:null,onChange:y,caller:h}),[T,L]=n.useState(!1),S=(0,s.W)(k),P=w(r),D=n.useRef(!1),[O,F]=n.useState(0);return n.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(m,S),()=>e.removeEventListener(m,S)},[S]),(0,p.jsx)(R,{scope:r,orientation:a,dir:C,loop:l,currentTabStopId:A,onItemFocus:n.useCallback(e=>I(e),[I]),onItemShiftTab:n.useCallback(()=>L(!0),[]),onFocusableItemAdd:n.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>F(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:T||0===O?-1:0,"data-orientation":a,...E,ref:N,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),x)}}D.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>L(!1))})})}),N="RovingFocusGroupItem",C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:s,...d}=e,f=(0,u.M)(),m=l||f,v=x(N,r),h=v.currentTabStopId===m,g=w(r),{onFocusableItemAdd:y,onFocusableItemRemove:k,currentTabStopId:R}=v;return n.useEffect(()=>{if(a)return y(),()=>k()},[a,y,k]),(0,p.jsx)(b.ItemSlot,{scope:r,id:m,focusable:a,active:i,children:(0,p.jsx)(c.WV.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var r,n;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=v.loop?(r=o,n=a+1,r.map((e,t)=>r[(n+t)%r.length])):o.slice(a+1)}setTimeout(()=>M(o))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=R}):s})})});C.displayName=N;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var I=E,T=C},50721:function(e,t,r){r.d(t,{bU:function(){return x},fC:function(){return R}});var n=r(2265),o=r(6741),a=r(98575),i=r(73966),l=r(80886),u=r(6718),c=r(90420),s=r(66840),d=r(57437),f="Switch",[p,m]=(0,i.b)(f),[v,h]=p(f),b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:u,defaultChecked:c,required:p,disabled:m,value:h="on",onCheckedChange:b,form:w,...g}=e,[R,x]=n.useState(null),E=(0,a.e)(t,e=>x(e)),j=n.useRef(!1),N=!R||w||!!R.closest("form"),[C,A]=(0,l.T)({prop:u,defaultProp:null!=c&&c,onChange:b,caller:f});return(0,d.jsxs)(v,{scope:r,checked:C,disabled:m,children:[(0,d.jsx)(s.WV.button,{type:"button",role:"switch","aria-checked":C,"aria-required":p,"data-state":k(C),"data-disabled":m?"":void 0,disabled:m,value:h,...g,ref:E,onClick:(0,o.M)(e.onClick,e=>{A(e=>!e),N&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),N&&(0,d.jsx)(y,{control:R,bubbles:!j.current,name:i,value:h,checked:C,required:p,disabled:m,form:w,style:{transform:"translateX(-100%)"}})]})});b.displayName=f;var w="SwitchThumb",g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=h(w,r);return(0,d.jsx)(s.WV.span,{"data-state":k(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});g.displayName=w;var y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:l=!0,...s}=e,f=n.useRef(null),p=(0,a.e)(f,t),m=(0,u.D)(i),v=(0,c.t)(o);return n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==i&&t){let r=new Event("click",{bubbles:l});t.call(e,i),e.dispatchEvent(r)}},[m,i,l]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...s,tabIndex:-1,ref:p,style:{...s.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var R=b,x=g},43577:function(e,t,r){r.d(t,{IZ:function(){return d}});let{Axios:n,AxiosError:o,CanceledError:a,isCancel:i,CancelToken:l,VERSION:u,all:c,Cancel:s,isAxiosError:d,spread:f,toFormData:p,AxiosHeaders:m,HttpStatusCode:v,formToJSON:h,getAdapter:b,mergeConfig:w}=r(83464).default}}]);