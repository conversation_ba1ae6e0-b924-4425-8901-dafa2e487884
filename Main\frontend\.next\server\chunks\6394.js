exports.id=6394,exports.ids=[6394],exports.modules={30009:(e,s,t)=>{Promise.resolve().then(t.bind(t,33635))},96619:(e,s,t)=>{Promise.resolve().then(t.bind(t,33635))},35303:()=>{},33635:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ea});var r=t(10326),a=t(5158),l=t(92392),i=t(12649),n=t(45806),c=t(80609),d=t(28758),o=t(567),m=t(90772),x=t(60097),u=t(8281),j=t(30464),h=t(90799),f=t(60814),p=t(77863),v=t(72116),b=t(47237),g=t(32458),y=t(88010),N=t(31112),w=t(7310),_=t(62047),z=t(66678),C=t(64561),P=t(90434),Z=t(35047),k=t(70012),S=t(85999);function $({formData:e}){let{t:s}=(0,k.$G)(),t=(0,Z.useSearchParams)(),$=(0,Z.useRouter)(),{wallets:J,getWalletByCurrencyCode:E}=(0,f.r)(),{data:F,isLoading:I}=(0,h.d)(`/transactions/trx/${t.get("trxId")}`);if(I)return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(l.Loader,{})});let D=e=>{S.toast.promise((0,j.y)(`${e}`),{loading:s("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return e.message},error:e=>e.message})},G=F?.data,M=G?.status,B=G?.from?JSON.parse(G.from):null,L=G?.metaData?JSON.parse(G.metaData):null,T=E(J,B.currency),V=new p.F(B?.currency),A=(0,C.h)((0,p.Fg)(e.phone_number??""));return(0,r.jsxs)("div",{className:"mx-auto max-w-3xl",children:[(0,r.jsxs)("h2",{className:"mb-1 flex items-center justify-center gap-2 text-2xl font-semibold text-foreground",children:[(0,r.jsxs)(a.J,{condition:"pending"===M,children:[r.jsx(v.Z,{variant:"Bold",className:"size-8 text-warning"}),r.jsx("span",{children:s("Payment pending")})]}),(0,r.jsxs)(a.J,{condition:"completed"===M,children:[r.jsx(b.Z,{size:"32",color:"#13A10E",variant:"Bold"}),r.jsx("span",{children:s("Payment successful")})]}),(0,r.jsxs)(a.J,{condition:"failed"===M,children:[r.jsx(g.Z,{variant:"Bold",className:"size-8 text-destructive"}),r.jsx("span",{children:s("Payment failed")})]})]}),r.jsx(u.Z,{orientation:"horizontal",className:"my-7"}),(0,r.jsxs)(i.Y,{groupName:s("Meter details"),children:[r.jsx(i.r,{title:s("Phone number"),value:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(c.W,{countryCode:A.country}),A.formatInternational()]})}),r.jsx(i.r,{title:s("Electricity name"),value:"Electricity 1"}),r.jsx(i.r,{title:s("Meter type"),value:r.jsx(o.C,{className:"bg-primary text-primary-foreground",children:e.meter_type})}),r.jsx(i.r,{title:s("Meter number"),value:e.meter_number})]}),r.jsx(u.Z,{className:"my-8"}),(0,r.jsxs)(i.Y,{groupName:s("Payment details"),children:[r.jsx(i.r,{title:`${L?.billerName} ${s("will get")}`,value:V.formatVC(G?.amount)}),r.jsx(i.r,{title:s("Service charge"),value:(0,r.jsxs)(r.Fragment,{children:[r.jsx(a.J,{condition:G?.fee===0,children:r.jsx(o.C,{className:"bg-success text-success-foreground",children:s("Free")})}),r.jsx(a.J,{condition:G?.fee!==0,children:V.formatVC(G?.fee)})]})}),r.jsx(i.r,{title:s("Total"),value:V.formatVC(G?.total)})]}),r.jsx(u.Z,{orientation:"horizontal",className:"my-7"}),r.jsx(n.T,{id:L?.transactionId,className:"mb-4 text-sm sm:text-base"}),(0,r.jsxs)("div",{className:"mb-8 space-y-4",children:[r.jsx("h5",{className:"text-sm font-medium sm:text-base",children:s("New balance")}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(d.qE,{children:[r.jsx(d.F$,{src:T?.logo}),r.jsx(d.Q5,{className:"bg-important font-bold text-important-foreground",children:T?.currency?.code})]}),r.jsx("span",{className:"text-sm font-bold",children:T?.currency?.code})]}),r.jsx("p",{className:"font-medium",children:V.formatVC(Number(T?.balance??0))})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,r.jsxs)(m.z,{variant:"outline",type:"button",className:"w-full md:w-auto",children:[r.jsx(y.Z,{size:16}),r.jsx("span",{children:s("Download Receipt")})]}),(0,r.jsxs)("div",{className:"flex w-full flex-wrap gap-4 sm:flex-nowrap md:w-auto md:justify-end",children:[(0,r.jsxs)(x.h_,{children:[(0,r.jsxs)(x.$F,{className:(0,p.ZP)("flex w-full items-center space-x-1.5 md:w-fit",(0,m.d)({variant:"outline"})),children:[r.jsx("span",{children:s("Menu")}),r.jsx(N.Z,{size:16})]}),(0,r.jsxs)(x.AW,{align:"start",className:"m-0",children:[(0,r.jsxs)(x.Xi,{onSelect:()=>(0,p.Fp)(L?.transactionId),className:"flex items-center gap-2 text-sm font-medium",children:[r.jsx(w.Z,{size:"20",className:"text-primary",variant:"Outline"}),s("Copy transaction ID")]}),(0,r.jsxs)(x.Xi,{onSelect:()=>D(G?.id),className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[r.jsx(_.Z,{size:"20",variant:"Outline"}),s("Bookmark receipt")]}),r.jsx(x.VD,{}),(0,r.jsxs)(x.Xi,{onSelect:()=>$.push("/services/electricity-bill"),className:"flex items-center gap-2 text-sm font-medium",children:[r.jsx(z.Z,{size:"20",variant:"Outline"}),s("Pay bill again")]})]})]}),r.jsx(m.z,{type:"button",className:"w-full md:max-w-48",asChild:!0,children:r.jsx(P.default,{href:"/services",children:s("Go to dashboard")})})]})]})]})}var J=t(2454),E=t(49547),F=t(84455),I=t(54033);function D({providerName:e,logo:s,meterNumber:t,meterType:l}){return(0,r.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,r.jsxs)(d.qE,{className:"border-2 border-border-primary",children:[r.jsx(d.F$,{src:s,alt:e}),r.jsx(d.Q5,{children:(0,I.v)(e)})]}),(0,r.jsxs)("div",{className:"flex-1 text-foreground",children:[r.jsx("h6",{className:"text-sm font-normal",children:e}),r.jsx(a.J,{condition:!!t,children:r.jsx("p",{className:"text-xs font-normal text-secondary-text",children:t})}),r.jsx(a.J,{condition:!!l,children:r.jsx("p",{className:"text-xs font-normal text-secondary-text",children:l})})]})]})}function G({onSelect:e}){let{t:s}=(0,k.$G)(),{data:t,isLoading:i,error:n}=(0,F.ZP)("/services/utility/billers",e=>(0,E.Z)(e));return n&&S.toast.error(n.message),r.jsx(J.e8,{children:(0,r.jsxs)(J.fu,{children:[r.jsx(a.J,{condition:i,children:r.jsx("div",{className:"w-full px-4 py-2.5",children:r.jsx(l.Loader,{title:s("Loading...")})})}),r.jsx(a.J,{condition:!i&&t?.data?.length!==0,children:t?.data?.map(s=>r.jsx(J.di,{value:s.name,onSelect:()=>e(s),children:r.jsx(D,{providerName:s.name,logo:""})},s.id))})]})})}var M=t(55632),B=t(54432),L=t(30811),T=t(88846),V=t(44221),A=t(44284),Q=t(17577),R=t.n(Q);function W({form:e,onNext:s,meterProvider:t,setMeterProvider:a}){let[l,i]=R().useState(!1),{t:n}=(0,k.$G)();return(0,r.jsxs)("div",{className:"flex w-full flex-col gap-4 md:gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"mb-4 font-semibold",children:n("Electricity name")}),r.jsx(M.Wi,{name:"meter_provider",control:e.control,render:({field:e})=>(0,r.jsxs)(M.xJ,{children:[r.jsx(M.NI,{children:(0,r.jsxs)(L.J2,{open:l,onOpenChange:i,children:[r.jsx(L.xo,{className:"flex h-12 w-full items-center justify-between rounded-md border border-input bg-secondary px-3",children:e.value?r.jsx("span",{children:t?.name}):r.jsx("span",{className:"font-normal text-secondary-text",children:n("Write Provider name")})}),r.jsx(L.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",children:(0,r.jsxs)(J.mY,{children:[r.jsx(J.sZ,{}),r.jsx(G,{onSelect:s=>{e.onChange(`${s?.id}`),a(s),i(!1)}})]})})]})}),r.jsx(M.zG,{})]})})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"mb-4 font-semibold",children:n("Meter Type")}),r.jsx(M.Wi,{name:"meter_type",control:e.control,render:({field:e})=>(0,r.jsxs)(M.xJ,{children:[r.jsx(M.NI,{children:(0,r.jsxs)(T.E,{defaultValue:e.value,className:"flex w-full max-w-[500px] flex-col items-center gap-4 sm:flex-row",onValueChange:e.onChange,children:[(0,r.jsxs)(M.xJ,{"data-active":"prepaid"===e.value,className:"relative flex h-[52px] w-full items-center rounded-xl border-2 border-border bg-muted px-3 text-sm font-semibold leading-5 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[r.jsx("span",{children:n("Prepaid")}),r.jsx(M.NI,{children:r.jsx(T.m,{value:"prepaid",className:"absolute inset-0 left-0 top-0 z-10 h-full w-full opacity-0"})})]}),(0,r.jsxs)(M.xJ,{"data-active":"postpaid"===e.value,className:"relative flex h-[52px] w-full items-center rounded-xl border-2 border-border bg-muted px-3 text-sm font-semibold leading-5 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[r.jsx("span",{children:n("Postpaid")}),r.jsx(M.NI,{children:r.jsx(T.m,{value:"postpaid",className:"absolute inset-0 left-0 top-0 z-10 h-full w-full opacity-0"})})]})]})}),r.jsx(M.zG,{})]})})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"mb-4 font-semibold",children:n("Meter Number")}),r.jsx(M.Wi,{name:"meter_number",control:e.control,render:({field:e})=>(0,r.jsxs)(M.xJ,{children:[r.jsx(M.NI,{children:r.jsx(B.I,{placeholder:n("Enter meter number"),...e})}),r.jsx(M.zG,{})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[r.jsx(m.z,{variant:"outline",type:"button",asChild:!0,className:"flex w-[102px] gap-0.5 rounded-lg px-4 py-2 text-base font-medium leading-[22x]",children:(0,r.jsxs)(P.default,{href:"/services",children:[r.jsx(V.Z,{size:"24"}),n("Back")]})}),(0,r.jsxs)(m.z,{type:"button",onClick:s,className:"flex w-[200px] gap-0.5 rounded-lg px-4 py-2 text-base font-medium leading-[22x]",children:[n("Next"),r.jsx(A.Z,{size:"16"})]})]})]})}var q=t(74743);function Y({form:e,onNext:s,onBack:t}){let{t:a}=(0,k.$G)();return(0,r.jsxs)("div",{className:"flex w-full flex-col gap-4 md:gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"mb-4",children:a("Select wallet")}),r.jsx(M.Wi,{name:"sender_wallet_id",control:e.control,render:({field:e})=>(0,r.jsxs)(M.xJ,{children:[r.jsx(M.NI,{children:r.jsx(q.R,{...e,value:e.value||""})}),r.jsx(M.zG,{})]})})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"mb-4",children:a("How much is the bill?")}),r.jsx(M.Wi,{control:e.control,name:"bill_amount",render:({field:e})=>(0,r.jsxs)(M.xJ,{children:[r.jsx(M.NI,{children:r.jsx(B.I,{placeholder:a("Enter payment amount"),...e,type:"number"})}),r.jsx(M.zG,{})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,r.jsxs)(m.z,{variant:"outline",type:"button",onClick:t,children:[r.jsx(V.Z,{size:15}),a("Back")]}),(0,r.jsxs)(m.z,{type:"button",onClick:()=>{let t=0;e.getValues("sender_wallet_id")||(e.setError("sender_wallet_id",{message:"Please select a wallet.",type:"custom"},{shouldFocus:!0}),t+=1),e.getValues("bill_amount")||(e.setError("bill_amount",{message:"Amount is required.",type:"custom"},{shouldFocus:!0}),t+=1),t||s()},children:[a("Next"),r.jsx(A.Z,{size:15})]})]})]})}var O=t(10734);async function X(e){try{let s=await E.Z.post("/services/utility/create",e);return(0,O.B)(s)}catch(e){return(0,O.D)(e)}}function H({formData:e,isLoading:s,onBack:t,onNext:n,meterProvider:c}){let[x,j]=R().useTransition(),[h,v]=R().useState({}),{wallets:b,getWalletByCurrencyCode:g}=(0,f.r)(),{t:y}=(0,k.$G)(),N=g(b,e.sender_wallet_id);return(0,r.jsxs)("div",{className:"mb-4",children:[r.jsx("h2",{className:"mb-8",children:y("Confirm and proceed")}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[r.jsx("h5",{className:"text-sm font-medium sm:text-base",children:y("Selected wallet")}),(0,r.jsxs)("div",{className:"flex flex-row items-center gap-2.5",children:[(0,r.jsxs)(d.qE,{className:"size-8",children:[r.jsx(d.F$,{src:N?.logo}),r.jsx(d.Q5,{className:"bg-important text-xs font-bold text-important-foreground",children:N?.currency?.code})]}),r.jsx("h6",{className:"font-bold",children:N?.currency?.code})]})]}),r.jsx(u.Z,{className:"my-8"}),x?r.jsx("div",{className:"flex justify-center",children:r.jsx(l.Loader,{})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.Y,{groupName:y("Meter details"),children:[r.jsx(i.r,{title:y("Electricity name"),value:h?.receiver?.name}),r.jsx(i.r,{title:y("Meter type"),value:r.jsx(o.C,{className:"bg-primary text-primary-foreground",children:(0,p.fl)(e?.meter_type)})}),r.jsx(i.r,{title:y("Meter number"),value:e?.meter_number})]}),r.jsx(u.Z,{className:"my-8"}),(0,r.jsxs)(i.Y,{groupName:y("Payment details"),children:[r.jsx(i.r,{title:y("Amount"),value:`${h?.sender?.sendingAmount} ${e.sender_wallet_id}`}),r.jsx(i.r,{title:y("Service charge"),value:(0,r.jsxs)(r.Fragment,{children:[r.jsx(a.J,{condition:c?.localTransactionFee===0,children:r.jsx(o.C,{className:"bg-success text-success-foreground",children:y("Free")})}),(0,r.jsxs)(a.J,{condition:c?.localTransactionFee!==0,children:[h?.sender?.fee," ",e.sender_wallet_id]})]})}),r.jsx(i.r,{title:y("Total"),value:`${Number(h?.sender?.receivingAmount)} ${e.sender_wallet_id}`}),r.jsx(i.r,{title:y("You will get"),value:`${Number(h?.receiver?.fxRate)} ${h?.receiver?.currencyCode}`})]})]}),r.jsx(u.Z,{className:"my-8"}),(0,r.jsxs)("div",{className:"mt-8 flex flex-1 justify-between gap-4",children:[(0,r.jsxs)(m.z,{variant:"outline",onClick:t,children:[r.jsx(V.Z,{size:17}),y("Back")]}),(0,r.jsxs)(m.z,{onClick:n,disabled:s,children:[(0,r.jsxs)(a.J,{condition:!s,children:[y("Pay bill"),r.jsx(A.Z,{size:17})]}),r.jsx(a.J,{condition:s,children:r.jsx(l.Loader,{title:y("Processing"),className:"text-primary-foreground"})})]})]})]})}var U=t(82548),K=t(48054),ee=t(74064),es=t(74723),et=t(27256);let er=et.z.object({meter_type:et.z.enum(["prepaid","postpaid"]).default("prepaid"),meter_provider:et.z.string().min(1,"Provider is required."),meter_number:et.z.string().min(1,"Meter number is required."),sender_wallet_id:et.z.string().optional(),bill_amount:et.z.string().optional(),phone_number:et.z.string().optional()});function ea(){let{t:e}=(0,k.$G)(),[s,t]=R().useState("meter_details"),[a,l]=R().useState(null),[i,n]=R().useTransition(),[c,d]=R().useState([{id:"meter_details",value:"meter_details",title:e("Meter Details"),complete:!1},{id:"payment_details",value:"payment_details",title:e("Payment Details"),complete:!1},{id:"review",value:"review",title:e("Review"),complete:!1},{id:"finish",value:"finish",title:e("Finish"),complete:!1}]),o=(0,es.cI)({resolver:(0,ee.F)(er),mode:"all",defaultValues:{meter_provider:"",meter_type:"prepaid",meter_number:"",sender_wallet_id:"",bill_amount:"",phone_number:""}}),m=e=>{d(s=>s.map(s=>s.id===e?{...s,complete:!0}:s))},x=()=>{let s=o.getValues();n(async()=>{let t={meterNumber:s.meter_number,amount:Number(s.bill_amount),currencyCode:s.sender_wallet_id,billerId:Number(s.meter_provider)},r=await X(t);r.status?S.toast.success(e(r.message)):S.toast.error(e(r.message))})};return r.jsx(U.Xg,{children:r.jsx(M.l0,{...o,children:r.jsx("form",{onSubmit:o.handleSubmit(x),children:r.jsx("div",{className:"w-full p-4 pb-10 md:p-12",children:r.jsx("div",{className:"mx-auto max-w-3xl",children:r.jsx(K.R,{tabs:c,value:s,onTabChange:e=>t(e),children:(0,r.jsxs)("div",{className:"p-4",children:[r.jsx(K.Q,{value:"meter_details",children:r.jsx(W,{form:o,meterProvider:a,setMeterProvider:e=>l(e),onNext:o.handleSubmit((e,s)=>{s?.preventDefault(),t("payment_details"),m("meter_details")})})}),r.jsx(K.Q,{value:"payment_details",children:r.jsx(Y,{form:o,onNext:o.handleSubmit((e,s)=>{s?.preventDefault(),t("review"),m("payment_details")}),onBack:()=>t("meter_details")})}),r.jsx(K.Q,{value:"review",children:r.jsx(H,{formData:o.getValues(),isLoading:i,meterProvider:a,onBack:()=>t("payment_details"),onNext:o.handleSubmit(x,()=>{S.toast.error(e("An error occurred. Please try again."))})})}),r.jsx(K.Q,{value:"finish",children:r.jsx($,{formData:o.getValues()})})]})})})})})})})}},82548:(e,s,t)=>{"use strict";t.d(s,{Xg:()=>j,cI:()=>u,jT:()=>h});var r=t(10326),a=t(90772),l=t(95028),i=t(77863),n=t(62047),c=t(44221),d=t(17577),o=t(70012);let m=d.createContext(null),x=()=>{let e=d.useContext(m);if(!e)throw Error("usePageLayout must be used within an PageLayoutCtx. Please ensure that your component is wrapped with an PageLayoutCtx.");return e};function u({className:e}){let{t:s}=(0,o.$G)(),{setRightSidebar:t}=x();return r.jsx("div",{className:(0,i.ZP)("flex items-center justify-end md:mb-4 xl:hidden",e),children:(0,r.jsxs)(a.z,{onClick:()=>t(e=>!e),variant:"outline",size:"sm",type:"button",className:"text-sm",children:[r.jsx(n.Z,{size:"20"}),s("Bookmarks")]})})}function j({children:e}){let[s,t]=d.useState(!1),{width:a}=(0,l.B)();d.useEffect(()=>{a>=1280&&t(!0)},[a]);let i=d.useMemo(()=>({width:a,rightSidebar:s,setRightSidebar:t}),[a,s]);return r.jsx(m.Provider,{value:i,children:e})}function h({children:e}){let{t:s}=(0,o.$G)(),{width:t,rightSidebar:l,setRightSidebar:i}=x();return(0,r.jsxs)("div",{"data-expanded":t>=1280||t<1280&&l,className:"absolute inset-y-0 right-0 top-0 w-full max-w-96 translate-x-full bg-background-body p-6 transition-all duration-300 ease-in-out data-[expanded=true]:translate-x-0 xl:relative",children:[(0,r.jsxs)(a.z,{variant:"outline",size:"sm",type:"button",onClick:()=>i(!1),className:"mb-4 gap-[2px] bg-background text-sm hover:bg-background xl:hidden",children:[r.jsx(c.Z,{size:14}),s("Hide bookmarks")]}),e]})}},45806:(e,s,t)=>{"use strict";t.d(s,{T:()=>d});var r=t(10326),a=t(90772),l=t(77863),i=t(7310),n=t(70012),c=t(85999);function d({id:e,className:s}){let{t}=(0,n.$G)();return(0,r.jsxs)("div",{className:(0,l.ZP)("inline-flex w-full items-center gap-4",s),children:[r.jsx("div",{className:"flex-1",children:t("Transaction ID")}),(0,r.jsxs)("div",{className:"inline-flex items-center gap-4",children:[r.jsx("span",{children:e}),r.jsx(a.z,{type:"button",onClick:()=>{navigator.clipboard.writeText(e).then(()=>c.toast.success("Copied to clipboard!")).catch(()=>{c.toast.error("Failed to copy!")})},variant:"outline",size:"sm",children:r.jsx(i.Z,{size:"20"})})]})]})}},80609:(e,s,t)=>{"use strict";t.d(s,{W:()=>i});var r=t(10326),a=t(77863),l=t(46226);function i({countryCode:e,className:s,url:t}){return e||t?r.jsx(l.default,{src:t??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,a.ZP)("rounded-[2px]",s)}):null}},88846:(e,s,t)=>{"use strict";t.d(s,{E:()=>c,m:()=>d});var r=t(10326),a=t(17577),l=t(18623),i=t(53982),n=t(77863);let c=a.forwardRef(({className:e,...s},t)=>r.jsx(l.fC,{className:(0,n.ZP)("grid gap-2",e),...s,ref:t}));c.displayName=l.fC.displayName;let d=a.forwardRef(({className:e,...s},t)=>r.jsx(l.ck,{ref:t,className:(0,n.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:r.jsx(l.z$,{className:"flex items-center justify-center",children:r.jsx(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));d.displayName=l.ck.displayName},30464:(e,s,t)=>{"use strict";t.d(s,{y:()=>l});var r=t(49547),a=t(10734);async function l(e,s){try{let t=await r.Z.put(`${s??"/transactions/toggle-bookmark"}/${e}`,{id:e});return(0,a.B)(t)}catch(e){return(0,a.D)(e)}}},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(40099),l=t(76609);function i({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(l.Z,{userRole:"customer"}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(a.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(19510),a=t(48413);function l(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.a,{})})}},58146:(e,s,t)=>{"use strict";function r({children:e}){return e}t.r(s),t.d(s,{default:()=>r}),t(71159)},25471:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(19510),a=t(48413);let l=function(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.a,{})})}},59304:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\services\electricity-bill\page.tsx#default`)},82338:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(19510),a=t(48413);function l(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.a,{})})}},56214:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(19510),a=t(59304);function l(){return r.jsx(a.default,{})}}};