(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9906],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},67419:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>L,default:()=>M});var s,n={};r.r(n),r.d(n,{AppRouter:()=>p.WY,ClientPageRoot:()=>p.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>p.yO,NotFoundBoundary:()=>p.O4,Postpone:()=>p.hQ,RenderFromTemplateContext:()=>p.b5,__next_app__:()=>h,actionAsyncStorage:()=>p.Wz,createDynamicallyTrackedSearchParams:()=>p.rL,createUntrackedSearchParams:()=>p.S5,decodeAction:()=>p.Hs,decodeFormState:()=>p.dH,decodeReply:()=>p.kf,originalPathname:()=>g,pages:()=>f,patchFetch:()=>p.XH,preconnect:()=>p.$P,preloadFont:()=>p.C5,preloadStyle:()=>p.oH,renderToReadableStream:()=>p.aW,requestAsyncStorage:()=>p.Fg,routeModule:()=>x,serverHooks:()=>p.GP,staticGenerationAsyncStorage:()=>p.AT,taintObjectReference:()=>p.nr,tree:()=>m}),r(67206);var a=r(79319),i=r(20518),o=r(61902),l=r(62042),c=r(44630),d=r(44828),u=r(65505),p=r(13839);let m=["",{children:["(protected)",{admin:["children",{children:["settings",{children:["plugins",{children:["[pluginId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60674)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\plugins\\[pluginId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,54204)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\plugins\\[pluginId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,2077)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\plugins\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15171)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,5897)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],f=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\plugins\\[pluginId]\\page.tsx"],g="/(protected)/@admin/settings/plugins/[pluginId]/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/settings/plugins/[pluginId]/page",pathname:"/settings/plugins/[pluginId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}});var v=r(69094),b=r(5787),E=r(90527);let y=e=>e?JSON.parse(e):void 0,S=self.__BUILD_MANIFEST,j=y(self.__REACT_LOADABLE_MANIFEST),k=null==(s=self.__RSC_MANIFEST)?void 0:s["/(protected)/@admin/settings/plugins/[pluginId]/page"],P=y(self.__RSC_SERVER_MANIFEST),N=y(self.__NEXT_FONT_MANIFEST),D=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];k&&P&&(0,b.Mo)({clientReferenceManifest:k,serverActionsManifest:P,serverModuleMap:(0,E.w)({serverActionsManifest:P,pageName:"/(protected)/@admin/settings/plugins/[pluginId]/page"})});let w=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@admin/settings/plugins/[pluginId]/page",appMod:null,pageMod:n,errorMod:null,error500Mod:null,Document:null,buildManifest:S,renderToHTML:l.f,reactLoadableManifest:j,clientReferenceManifest:k,serverActionsManifest:P,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:N,incrementalCacheHandler:null,interceptionRouteRewrites:D}),L=n;function M(e){return(0,a.C)({...e,IncrementalCache:o.k,handler:w})}},20216:(e,t,r)=>{Promise.resolve().then(r.bind(r,80312))},3734:(e,t,r)=>{Promise.resolve().then(r.bind(r,52490))},80312:(e,t,r)=>{"use strict";r.d(t,{Tabbar:()=>v});var s=r(60926),n=r(29220),a=r(58387),i=r(14579),o=r(30655),l=r(34870),c=r(20852),d=r(50201),u=r(89450),p=r(12870),m=r(81584),f=r(47020),g=r(737),h=r(64947),x=r(39228);function v(){let[e,t]=n.useState(""),r=(0,h.BT)(),v=(0,h.wm)(),b=(0,h.lr)(),{t:E}=(0,x.$G)(),y=[{title:E("Deposit Gateways"),icon:(0,s.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:E("Withdraw Methods"),icon:(0,s.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/withdraw-methods",id:"withdraw-methods"},{title:E("Plugins"),icon:(0,s.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:"/settings/plugins",id:"plugins"},{title:E("Services"),icon:(0,s.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/services",id:"services"},{title:E("Currency"),icon:(0,s.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:"/settings/currencies",id:"currencies"},{title:E("Site Settings"),icon:(0,s.jsx)(p.Z,{size:"24",variant:"Bulk"}),href:"/settings/site-settings",id:"site-settings"},{title:E("Login Sessions"),icon:(0,s.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return n.useLayoutEffect(()=>{r?t("gateways"===r?"__DEFAULT__":r):t("__DEFAULT__")},[r]),(0,s.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,s.jsx)(a.J,{condition:v.length>1,children:(0,s.jsxs)("div",{className:"line-clamp-1 inline-flex max-w-full items-center gap-2 px-0 pb-4 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,s.jsxs)(g.Z,{href:"__DEFAULT__"===e?"/settings":`/settings/${e}`,className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,s.jsx)(f.Z,{className:"size-4 sm:size-6"}),E("Back")]}),(0,s.jsxs)("span",{className:"line-clamp-1 flex items-center gap-1 whitespace-nowrap text-sm font-semibold text-secondary-text",children:["/"," ","create"===v[1]?"Create withdraw method":b.get("name")]})]})}),(0,s.jsx)(i.a,{tabs:y,defaultSegment:"gateways"})]})}},52490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>U,runtime:()=>Q});var s=r(60926),n=r(29411),a=r(36162),i=r(34451),o=r(18662),l=r(66817),c=r(26734),d=r(30417),u=r(1181),p=r(25694);async function m(e,t){try{let r=await u.Z.put(`/admin/external-plugins/${t}`,e);return(0,p.B)(r)}catch(e){return(0,p.D)(e)}}var f=r(43291),g=r(65091),h=r(15487),x=r(61394),v=r(29220),b=r(31036),E=r.n(b),y=["variant","color","size"],S=function(e){var t=e.color;return v.createElement(v.Fragment,null,v.createElement("path",{d:"M16 2H8C4.5 2 3 4 3 7v10c0 3 1.5 5 5 5h8c3.5 0 5-2 5-5V7c0-3-1.5-5-5-5ZM8 12.25h4c.41 0 .75.34.75.75s-.34.75-.75.75H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75Zm8 5.5H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75Zm2.5-8.5h-2c-1.52 0-2.75-1.23-2.75-2.75v-2c0-.41.34-.75.75-.75s.75.34.75.75v2c0 .69.56 1.25 1.25 1.25h2c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},j=function(e){var t=e.color;return v.createElement(v.Fragment,null,v.createElement("path",{d:"M3 7c0-3 1.5-5 5-5h8c3.5 0 5 2 5 5v10c0 3-1.5 5-5 5H8c-3.5 0-5-2-5-5v-5.98",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),v.createElement("path",{d:"M14.5 4.5v2c0 1.1.9 2 2 2h2M8 13h4M8 17h8",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},k=function(e){var t=e.color;return v.createElement(v.Fragment,null,v.createElement("path",{opacity:".4",d:"M21 7v10c0 3-1.5 5-5 5H8c-3.5 0-5-2-5-5V7c0-3 1.5-5 5-5h8c3.5 0 5 2 5 5Z",fill:t}),v.createElement("path",{d:"M18.5 9.25h-2c-1.52 0-2.75-1.23-2.75-2.75v-2c0-.41.34-.75.75-.75s.75.34.75.75v2c0 .69.56 1.25 1.25 1.25h2c.41 0 .75.34.75.75s-.34.75-.75.75ZM12 13.75H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h4c.41 0 .75.34.75.75s-.34.75-.75.75ZM16 17.75H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},P=function(e){var t=e.color;return v.createElement(v.Fragment,null,v.createElement("path",{d:"M22 10v5c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),v.createElement("path",{d:"M22 10h-4c-3 0-4-1-4-4V2l8 8ZM7 13h6M7 17h4",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},N=function(e){var t=e.color;return v.createElement(v.Fragment,null,v.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h5c.41 0 .75.34.75.75s-.34.75-.75.75H9C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25v-5c0-.41.34-.75.75-.75s.75.34.75.75v5c0 5.43-2.32 7.75-7.75 7.75Z",fill:t}),v.createElement("path",{d:"M22 10.748h-4c-3.42 0-4.75-1.33-4.75-4.75v-4c0-.3.18-.58.46-.69.28-.12.6-.05.82.16l8 8a.751.751 0 0 1-.53 1.28Zm-7.25-6.94v2.19c0 2.58.67 3.25 3.25 3.25h2.19l-5.44-5.44ZM13 13.75H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h6c.41 0 .75.34.75.75s-.34.75-.75.75ZM11 17.75H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h4c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},D=function(e){var t=e.color;return v.createElement(v.Fragment,null,v.createElement("path",{d:"M22 10v5c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),v.createElement("path",{d:"M22 10h-4c-3 0-4-1-4-4V2l8 8Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),v.createElement("path",{opacity:".4",d:"M7 13h6M7 17h4",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},w=function(e,t){switch(e){case"Bold":return v.createElement(S,{color:t});case"Broken":return v.createElement(j,{color:t});case"Bulk":return v.createElement(k,{color:t});case"Linear":default:return v.createElement(P,{color:t});case"Outline":return v.createElement(N,{color:t});case"TwoTone":return v.createElement(D,{color:t})}},L=(0,v.forwardRef)(function(e,t){var r=e.variant,s=e.color,n=e.size,a=(0,x._)(e,y);return v.createElement("svg",(0,x.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:n,height:n,viewBox:"0 0 24 24",fill:"none"}),w(r,s))});L.propTypes={variant:E().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:E().string,size:E().oneOfType([E().string,E().number])},L.defaultProps={variant:"Linear",color:"currentColor",size:"24"},L.displayName="DocumentText1";var M=r(14761),_=r(737),A=r(64947),I=r(45475),C=r(39228),R=r(32167),T=r(93633);let F=e=>{let t={};return e?.forEach(e=>{t[e.key]=e.required?T.z.string().min(1,{message:`${e.label} is required`}):T.z.string().optional()}),T.z.object({active:T.z.boolean().default(!1),...t})};function Z({plugin:e,onMutate:t}){let r=(0,A.UO)(),u=(0,A.lr)().get("name"),[p,x]=(0,v.useTransition)(),{data:b,isLoading:E}=(0,f.d)("/admin/external-plugins/config"),{t:y}=(0,C.$G)(),S=b?.data?.[u],j=b?.data?.[u].fields,k=F(j),P=(0,I.cI)({resolver:(0,h.F)(k),defaultValues:{active:!!e?.active}});if(E)return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(n.Loader,{})});let N=e=>e?.type==="select"?(0,s.jsx)(i.Wi,{control:P.control,name:e?.key,render:({field:t})=>(0,s.jsxs)(i.xJ,{children:[(0,s.jsx)(i.lX,{children:y(e?.label)}),(0,s.jsx)(i.NI,{children:(0,s.jsx)(c.E,{defaultValue:t.value,onValueChange:t.onChange,className:"grid-cols-12 gap-4",children:e.options.map(e=>(0,s.jsxs)(l.Z,{htmlFor:e.value,"data-active":t.value===e.value,className:"col-span-12 flex h-12 cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 text-sm font-semibold leading-5 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[(0,s.jsx)(c.m,{id:e.value,value:e.value,className:"absolute left-0 top-0 opacity-0"}),(0,s.jsx)("span",{children:y(e.label)})]},e.value))})}),(0,s.jsx)(i.zG,{})]})}):(0,s.jsx)(i.Wi,{name:e?.key,control:P.control,render:({field:t})=>(0,s.jsxs)(i.xJ,{className:"mt-2",children:[(0,s.jsx)(i.lX,{children:y(e?.label)}),(0,s.jsx)(i.NI,{children:(0,s.jsx)(o.I,{type:e?.type,placeholder:y("Enter {{label}}",{label:e?.label}),...t})}),(0,s.jsx)(i.zG,{})]})},e?.key);return(0,s.jsxs)("div",{className:"mb-4 rounded-xl border border-border bg-background px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between py-6 hover:no-underline",children:[(0,s.jsxs)("div",{className:"flex flex-col items-start",children:[(0,s.jsx)("p",{className:"mb-1 text-base font-medium leading-[22px]",children:(0,g.fl)(e?.name)}),(0,s.jsx)("p",{className:"text-sm text-secondary-text",children:S?.description})]}),(0,s.jsxs)(_.Z,{href:S?.documentation,target:"_blank",rel:"noreferrer",className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text underline-offset-4 transition duration-300 ease-out hover:text-primary hover:underline",children:[(0,s.jsx)(L,{size:20,variant:"Bulk"}),(0,s.jsx)("span",{className:"text-inherit",children:y("Read Documentation")})]})]}),(0,s.jsx)("div",{className:"gap-4 border-t py-4",children:(0,s.jsx)(i.l0,{...P,children:(0,s.jsxs)("form",{onSubmit:P.handleSubmit(e=>{x(async()=>{let s=await m(e,r?.pluginId);s.status?(t(),R.toast.success(s.message)):R.toast.error(y(s.message))})}),className:"flex flex-col gap-6 px-1",children:[j?.map(e=>N(e)),(0,s.jsx)(i.Wi,{name:"active",control:P.control,render:({field:e})=>(0,s.jsxs)(i.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,s.jsx)(l.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:y("Active")}),(0,s.jsx)(i.NI,{children:(0,s.jsx)(d.Z,{defaultChecked:e.value,onCheckedChange:e.onChange})}),(0,s.jsx)(i.zG,{})]})}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(a.z,{className:"rounded-lg",children:p?(0,s.jsx)(n.Loader,{title:y("Updating..."),className:"text-primary-foreground"}):(0,s.jsxs)(s.Fragment,{children:[y("Update plugin"),(0,s.jsx)(M.Z,{size:20})]})})})]})})})]})}var z=r(32898);let Q="edge";function U(){let e=(0,A.UO)(),{data:t,isLoading:r,mutate:a}=(0,z.ZP)(`/admin/external-plugins/${e.pluginId}`,e=>(0,u.Z)(e));if(r)return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(n.Loader,{})});let i=t?.data;return(0,s.jsx)(Z,{plugin:i,onMutate:a})}},34451:(e,t,r)=>{"use strict";r.d(t,{NI:()=>h,Wi:()=>u,l0:()=>c,lX:()=>g,xJ:()=>f,zG:()=>x});var s=r(60926),n=r(62001),a=r(29220),i=r(45475),o=r(66817),l=r(65091);let c=i.RV,d=a.createContext({}),u=({...e})=>(0,s.jsx)(d.Provider,{value:{name:e.name},children:(0,s.jsx)(i.Qr,{...e})}),p=()=>{let e=a.useContext(d),t=a.useContext(m),{getFieldState:r,formState:s}=(0,i.Gc)(),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...n}},m=a.createContext({}),f=a.forwardRef(({className:e,...t},r)=>{let n=a.useId();return(0,s.jsx)(m.Provider,{value:{id:n},children:(0,s.jsx)("div",{ref:r,className:(0,l.ZP)("space-y-2",e),...t})})});f.displayName="FormItem";let g=a.forwardRef(({className:e,required:t,...r},n)=>{let{error:a,formItemId:i}=p();return(0,s.jsx)("span",{children:(0,s.jsx)(o.Z,{ref:n,className:(0,l.ZP)(a&&"text-base font-medium text-destructive",e),htmlFor:i,...r})})});g.displayName="FormLabel";let h=a.forwardRef(({...e},t)=>{let{error:r,formItemId:a,formDescriptionId:i,formMessageId:o}=p();return(0,s.jsx)(n.g7,{ref:t,id:a,"aria-describedby":r?`${i} ${o}`:`${i}`,"aria-invalid":!!r,...e})});h.displayName="FormControl",a.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:n}=p();return(0,s.jsx)("p",{ref:r,id:n,className:(0,l.ZP)("text-sm text-muted-foreground",e),...t})}).displayName="FormDescription";let x=a.forwardRef(({className:e,children:t,...r},n)=>{let{error:a,formMessageId:i}=p(),o=a?String(a?.message):t;return o?(0,s.jsx)("p",{ref:n,id:i,className:(0,l.ZP)("text-sm font-medium text-destructive",e),...r,children:o}):null});x.displayName="FormMessage"},18662:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var s=r(60926),n=r(29220),a=r(65091);let i=n.forwardRef(({className:e,type:t,...r},n)=>(0,s.jsx)("input",{type:t,className:(0,a.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:n,...r}));i.displayName="Input"},66817:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d});var s=r(60926),n=r(11537),a=r(8206),i=r(29220),o=r(65091);let l=(0,a.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.f,{ref:r,className:(0,o.ZP)(l(),e),...t}));c.displayName=n.f.displayName;let d=c},26734:(e,t,r)=>{"use strict";r.d(t,{E:()=>l,m:()=>c});var s=r(60926),n=r(29220),a=r(40441),i=r(63608),o=r(65091);let l=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.fC,{className:(0,o.ZP)("grid gap-2",e),...t,ref:r}));l.displayName=a.fC.displayName;let c=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a.ck,{ref:r,className:(0,o.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.z$,{className:"flex items-center justify-center",children:(0,s.jsx)(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));c.displayName=a.ck.displayName},15171:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(42416);r(87908);let n=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\_components\Tabbar.tsx#Tabbar`);function a({children:e}){return(0,s.jsxs)("div",{className:"overflow-y-auto",children:[(0,s.jsx)(n,{}),(0,s.jsx)("div",{className:"p-4",children:e})]})}},5897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(42416),n=r(21237);function a(){return(0,s.jsx)("div",{className:"flex justify-center py-10",children:(0,s.jsx)(n.a,{})})}},54204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(42416),n=r(21237);function a(){return(0,s.jsx)("div",{className:"flex justify-center py-10",children:(0,s.jsx)(n.a,{})})}},60674:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,runtime:()=>n});var s=r(18264);let n=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\plugins\[pluginId]\page.tsx#runtime`),a=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\plugins\[pluginId]\page.tsx#default`)},2077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(42416),n=r(21237);function a(){return(0,s.jsx)("div",{className:"flex justify-center py-10",children:(0,s.jsx)(n.a,{})})}},40441:(e,t,r)=>{"use strict";r.d(t,{ck:()=>Z,fC:()=>F,z$:()=>z});var s=r(29220),n=r(58408),a=r(19677),i=r(16769),o=r(22316),l=r(12239),c=r(68878),d=r(3237),u=r(17526),p=r(43263),m=r(90027),f=r(60926),g="Radio",[h,x]=(0,i.b)(g),[v,b]=h(g),E=s.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:l=!1,required:c,disabled:d,value:u="on",onCheck:p,form:m,...g}=e,[h,x]=s.useState(null),b=(0,a.e)(t,e=>x(e)),E=s.useRef(!1),y=!h||m||!!h.closest("form");return(0,f.jsxs)(v,{scope:r,checked:l,disabled:d,children:[(0,f.jsx)(o.WV.button,{type:"button",role:"radio","aria-checked":l,"data-state":k(l),"data-disabled":d?"":void 0,disabled:d,value:u,...g,ref:b,onClick:(0,n.M)(e.onClick,e=>{l||p?.(),y&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),y&&(0,f.jsx)(j,{control:h,bubbles:!E.current,name:i,value:u,checked:l,required:c,disabled:d,form:m,style:{transform:"translateX(-100%)"}})]})});E.displayName=g;var y="RadioIndicator",S=s.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:s,...n}=e,a=b(y,r);return(0,f.jsx)(m.z,{present:s||a.checked,children:(0,f.jsx)(o.WV.span,{"data-state":k(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:t})})});S.displayName=y;var j=s.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:n=!0,...i},l)=>{let c=s.useRef(null),d=(0,a.e)(c,l),m=(0,p.D)(r),g=(0,u.t)(t);return s.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==r&&t){let s=new Event("click",{bubbles:n});t.call(e,r),e.dispatchEvent(s)}},[m,r,n]),(0,f.jsx)(o.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:d,style:{...i.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}j.displayName="RadioBubbleInput";var P=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],N="RadioGroup",[D,w]=(0,i.b)(N,[l.Pc,x]),L=(0,l.Pc)(),M=x(),[_,A]=D(N),I=s.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:s,defaultValue:n,value:a,required:i=!1,disabled:u=!1,orientation:p,dir:m,loop:g=!0,onValueChange:h,...x}=e,v=L(r),b=(0,d.gm)(m),[E,y]=(0,c.T)({prop:a,defaultProp:n??"",onChange:h,caller:N});return(0,f.jsx)(_,{scope:r,name:s,required:i,disabled:u,value:E,onValueChange:y,children:(0,f.jsx)(l.fC,{asChild:!0,...v,orientation:p,dir:b,loop:g,children:(0,f.jsx)(o.WV.div,{role:"radiogroup","aria-required":i,"aria-orientation":p,"data-disabled":u?"":void 0,dir:b,...x,ref:t})})})});I.displayName=N;var C="RadioGroupItem",R=s.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...o}=e,c=A(C,r),d=c.disabled||i,u=L(r),p=M(r),m=s.useRef(null),g=(0,a.e)(t,m),h=c.value===o.value,x=s.useRef(!1);return s.useEffect(()=>{let e=e=>{P.includes(e.key)&&(x.current=!0)},t=()=>x.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,f.jsx)(l.ck,{asChild:!0,...u,focusable:!d,active:h,children:(0,f.jsx)(E,{disabled:d,required:c.required,checked:h,...p,...o,name:c.name,ref:g,onCheck:()=>c.onValueChange(o.value),onKeyDown:(0,n.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,n.M)(o.onFocus,()=>{x.current&&m.current?.click()})})})});R.displayName=C;var T=s.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...s}=e,n=M(r);return(0,f.jsx)(S,{...n,...s,ref:t})});T.displayName="RadioGroupIndicator";var F=I,Z=R,z=T}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,4969,8583,7283,5089,3711],()=>t(67419));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/settings/plugins/[pluginId]/page"]=r}]);
//# sourceMappingURL=page.js.map