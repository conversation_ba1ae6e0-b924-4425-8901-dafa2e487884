exports.id=8561,exports.ids=[8561],exports.modules={26261:(e,s,t)=>{Promise.resolve().then(t.bind(t,39969))},9241:(e,s,t)=>{Promise.resolve().then(t.bind(t,39969))},39969:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>el});var a=t(10326),l=t(5158),n=t(14926),r=t(48054),i=t(55632),c=t(49547),d=t(10734);async function o(e){try{let s=await c.Z.post("/deposit-requests/create",e);return(0,d.B)(s)}catch(e){return(0,d.D)(e)}}async function x(e){try{let s=await c.Z.post("/deposits/create",e);return(0,d.B)(s)}catch(e){return(0,d.D)(e)}}var m=t(19395),u=t(74064),h=t(35047),p=t(17577),f=t.n(p),j=t(74723),g=t(70012),y=t(85999),v=t(27256),N=t(92392),w=t(46226);function b({res:e}){let{t:s}=(0,g.$G)();return(0,a.jsxs)("div",{className:"md:px-auto flex flex-col items-center justify-center px-4 md:py-10",children:[a.jsx(w.default,{src:"/phone.svg",alt:"Phone",width:168,height:168,priority:!0,quality:70}),(0,a.jsxs)("h3",{className:"my-4 flex flex-col items-center text-center",children:[s("Request is processing..."),a.jsx(N.Loader,{title:s("Please wait"),className:"mt-2.5"})]})]})}var C=t(43173);function S({res:e}){let s=(0,h.useRouter)();return(0,C.EQ)(e).with({type:"redirect"},()=>(s.push(e.redirect),null)).with({type:"post",data:{method:"perfectmoney"}},()=>a.jsx(b,{res:e})).otherwise(()=>null)}var Z=t(74743),A=t(90772),z=t(54432),$=t(44284);function k({form:e,onNext:s}){let{t}=(0,g.$G)();return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h2",{children:t("Select wallet")}),a.jsx(i.Wi,{name:"wallet",control:e.control,render:({field:e})=>(0,a.jsxs)(i.xJ,{className:"w-full",children:[a.jsx(i.NI,{children:a.jsx(Z.R,{...e})}),a.jsx(i.zG,{})]})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h2",{children:t("How much?")}),a.jsx(i.Wi,{name:"amount",control:e.control,render:({field:e})=>(0,a.jsxs)(i.xJ,{className:"w-full",children:[a.jsx(i.NI,{children:a.jsx(z.I,{type:"number",placeholder:t("Enter deposit amount"),...e,onKeyDown:e=>{"enter"===e.key&&(e.preventDefault(),e.stopPropagation(),s())}})}),a.jsx(i.zG,{})]})})]}),a.jsx("div",{className:"flex justify-end",children:(0,a.jsxs)(A.z,{type:"submit",onClick:()=>{let t=0;e.getValues("wallet")||(e.setError("wallet",{message:"Please select a wallet.",type:"custom"},{shouldFocus:!0}),t+=1),e.getValues("amount")||(e.setError("amount",{message:"Amount is required.",type:"custom"},{shouldFocus:!0}),t+=1),t||s()},className:"min-w-48",children:[a.jsx("span",{children:t("Next")}),a.jsx($.Z,{size:16})]})})]})}var P=t(61718);function I(){return(0,a.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 80 80",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{opacity:"0.4",d:"M60 62.8665H57.4666C54.8 62.8665 52.2667 63.8999 50.4 65.7665L44.6999 71.3999C42.0999 73.9665 37.8667 73.9665 35.2667 71.3999L29.5667 65.7665C27.7 63.8999 25.1333 62.8665 22.5 62.8665H20C14.4667 62.8665 10 58.4333 10 52.9666V16.5999C10 11.1332 14.4667 6.69995 20 6.69995H60C65.5333 6.69995 70 11.1332 70 16.5999V52.9666C70 58.4 65.5333 62.8665 60 62.8665Z",fill:"#01A79E"}),a.jsx("path",{d:"M40.0001 34.7C44.2895 34.7 47.7668 31.2229 47.7668 26.9335C47.7668 22.6441 44.2895 19.1667 40.0001 19.1667C35.7107 19.1667 32.2334 22.6441 32.2334 26.9335C32.2334 31.2229 35.7107 34.7 40.0001 34.7Z",fill:"#01A79E"}),a.jsx("path",{d:"M48.9333 50.2002C51.6333 50.2002 53.2 47.2002 51.7 44.9668C49.4334 41.6002 45.0333 39.3335 40 39.3335C34.9667 39.3335 30.5666 41.6002 28.3 44.9668C26.8 47.2002 28.3667 50.2002 31.0667 50.2002H48.9333Z",fill:"#01A79E"})]})}var V=t(80609),E=t(31048),J=t(88846),F=t(8281),G=t(1868),L=t(90799),R=t(61610),q=t(47237),B=t(3001),D=t(44221),W=t(63761),M=t(84455),Q=t(28758),T=t(2454),H=t(30811),Y=t(77863),K=t(54033),U=t(6216);function _({onNext:e,form:s,agent:t}){let{t:l}=(0,g.$G)(),[n,r]=(0,p.useState)(!1),[i,c]=(0,p.useState)(),d=s.getFieldState("method")?.error?.message;return(0,a.jsxs)("div",{className:"space-y-6 rounded-xl px-6 py-4 shadow-default",children:[(0,a.jsxs)("div",{className:"flex flex-col justify-between md:flex-row md:items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(Q.qE,{children:[a.jsx(Q.F$,{src:t?.profileImage}),(0,a.jsxs)(Q.Q5,{children:[" ",(0,K.v)(t?.name)," "]})]}),a.jsx("p",{className:"text-sm font-bold",children:t?.name})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-6",children:[(0,a.jsxs)("div",{className:"w-full md:w-auto",children:[a.jsx("span",{className:"mb-1 text-[10px]",children:l("Phone number")}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(w.default,{src:`https://flagcdn.com/${t?.address?.countryCode?.toLowerCase()}.svg`,alt:l("france flag"),width:64,height:64,className:"h-4 w-6"}),a.jsx("p",{className:"text-sm leading-6",children:t?.user?.customer?.phone})]})]}),(0,a.jsxs)("div",{className:"flex-1 md:flex-auto",children:[a.jsx("span",{className:"mb-1 text-[10px]",children:l("Agent ID")}),a.jsx("p",{className:"text-sm leading-6",children:t?.agentId})]}),(0,a.jsxs)("div",{className:"flex-1 md:flex-auto",children:[a.jsx("span",{className:"mb-1 text-[10px]",children:l("Charges")}),a.jsx("p",{className:"text-sm leading-6",children:t?.depositCharge?`${t?.depositCharge}%`:l("Free")})]}),(0,a.jsxs)("div",{className:"flex-1 md:flex-auto",children:[a.jsx("span",{className:"mb-1 text-[10px]",children:l("Processing time")}),(0,a.jsxs)("p",{className:"text-sm leading-6",children:[t?.processingTime," ",l("Hours")]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col justify-end gap-2 sm:flex-row",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col",children:[(0,a.jsxs)(H.J2,{open:n,onOpenChange:r,children:[(0,a.jsxs)(H.xo,{className:"flex h-10 w-full items-center justify-between gap-2 rounded bg-secondary px-4 text-sm font-medium text-secondary-foreground transition duration-300 ease-in-out hover:bg-accent",children:[i?(0,a.jsxs)("span",{children:[" ",(0,Y.fl)(i?.name)," "]}):a.jsx("span",{children:l("Select a method")}),a.jsx(U.Z,{size:16})]}),a.jsx(H.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",children:a.jsx(T.mY,{children:(0,a.jsxs)(T.e8,{children:[a.jsx(T.rb,{}),a.jsx(T.fu,{children:t?.agentMethods?t?.agentMethods?.map(e=>a.jsxs(T.di,{onSelect:()=>{c(e),s.setValue("method",e?.name?.toString()),s.setValue("agent",t?.agentId?.toString()),r(!1)},disabled:!e.active,children:[i?.id===e?.id&&a.jsx($.Z,{variant:"Bold"}),Y.fl(e?.name)]},e.id)):a.jsx("span",{children:l("No available methods")})})]})})})]}),d?a.jsx("span",{className:"mt-2 block text-sm text-destructive",children:d}):null]}),(0,a.jsxs)(A.z,{type:"button",onClick:e,className:"min-w-48",children:[a.jsx("span",{children:l("Next")}),a.jsx($.Z,{size:16})]})]})]})}function O({countryCode:e,currencyCode:s,form:t,onNext:n}){let{t:r}=(0,g.$G)(),{data:i,isLoading:d}=(0,M.ZP)(`/agents/deposit?countryCode=${e.toUpperCase()}&currencyCode=${s}`,e=>c.Z.get(e));return(0,a.jsxs)("div",{className:"mb-10 flex flex-col gap-4 px-2 md:mb-0",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-col flex-wrap gap-2 sm:flex-row sm:items-center sm:justify-between",children:[a.jsx("h2",{className:"whitespace-nowrap text-2xl font-semibold text-foreground",children:r("Select agent")}),a.jsx(W.R,{placeholder:r("Search agent"),iconPlacement:"end"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx(l.J,{condition:d,children:a.jsx(N.Loader,{})}),a.jsx(l.J,{condition:!d&&i?.data?.length===0,children:a.jsx("p",{className:"text-sm text-secondary-text",children:r("Empty data")})}),a.jsx(l.J,{condition:i?.data?.length>0,children:i?.data?.map(e=>a.jsx(_,{agent:e,form:t,onNext:n},e?.id))})]})]})}function X({form:e,changeCountry:s,onBack:t,onNext:n}){let{t:r}=(0,g.$G)(),{getCountryByCode:c}=(0,G.F)(),d=e.getValues("country"),[o,x]=(0,p.useState)(),[m,u]=(0,p.useState)(!1),{data:h,isLoading:f}=(0,L.d)(`/gateways?currency=${e.getValues("wallet")}&country=${d}`);return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 pt-6 md:gap-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 px-2 sm:px-0",children:[a.jsx("h2",{children:r("Select your preferred method")}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[o&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(V.W,{countryCode:o?.code?.cca2,className:"h-5 w-8 rounded-sm"}),a.jsx("span",{children:o?.name})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(q.Z,{variant:"Bold",size:"16",className:"text-primary"}),a.jsx("span",{children:r("Selected")})]}),(0,a.jsxs)(A.z,{variant:"link",type:"button",onClick:s,className:"h-fit w-fit gap-1 px-0 py-0 text-sm font-semibold text-secondary-text sm:ml-auto",children:[a.jsx("span",{children:r("Change country")}),a.jsx(B.Z,{size:16})]})]}),a.jsx(i.Wi,{control:e.control,name:"method",render:({field:s})=>a.jsx(i.xJ,{children:a.jsx(i.NI,{children:(0,a.jsxs)(J.E,{onValueChange:t=>{"agent"===t?(s.onChange(""),e.setValue("isAgent",!0),u(!0)):(s.onChange(t),e.setValue("isAgent",!1),u(!1))},className:`grid ${f?"grid-cols-1":"grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3"}`,children:[a.jsx(l.J,{condition:f,children:a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(N.Loader,{})})}),(0,a.jsxs)(l.J,{condition:!f,children:[(0,a.jsxs)(E.Z,{"data-active":""===s.value&&m,className:"relative col-span-1 flex cursor-pointer items-center gap-2 rounded-xl border-[3px] border-transparent bg-muted p-4 text-sm font-bold transition-all duration-300 ease-linear hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",htmlFor:"agent",children:[a.jsx(J.m,{value:"agent",id:"agent",className:"absolute left-0 top-0 opacity-0"}),a.jsx(I,{}),a.jsx("span",{children:r("By Agent")})]}),h?.data?.map(e=>new R.M(e))?.map(e=>a.jsxs(E.Z,{"data-active":s.value===e.value,className:"relative col-span-1 flex cursor-pointer items-center gap-2 rounded-xl border-[3px] border-transparent bg-muted p-4 text-sm font-bold transition-all duration-300 ease-linear hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",htmlFor:`${e.value}-${e.id}`,children:[a.jsx(J.m,{id:`${e.value}-${e.id}`,value:e.value,className:"absolute left-0 top-0 opacity-0"}),e.logoImage?a.jsx(w.default,{src:e.logoImage,alt:e.name,width:32,height:32,className:"h-8 w-8 rounded-md bg-background object-contain"}):null,a.jsx("span",{children:e.name.replace(/\//g," / ")})]},e.id))]})]})})})})]}),a.jsx(F.Z,{className:"mb-1 mt-[5px] bg-divider-secondary"}),a.jsx(l.J,{condition:m,children:a.jsx(O,{countryCode:d,currencyCode:e.getValues("wallet"),form:e,onNext:n})}),a.jsx(l.J,{condition:"agent"!==e.watch("method")&&!m,children:(0,a.jsxs)("div",{className:"mt-8 flex justify-end gap-4",children:[(0,a.jsxs)(A.z,{variant:"outline",onClick:t,type:"button",children:[a.jsx(D.Z,{size:16}),a.jsx("span",{children:r("Back")})]}),(0,a.jsxs)(A.z,{type:"submit",onClick:n,className:"min-w-48",children:[a.jsx("span",{children:r("Next")}),a.jsx($.Z,{size:16})]})]})})]})}function ee({form:e,updateTab:s,onBack:t}){let{t:n}=(0,g.$G)(),[r,c]=f().useState("details");return(0,a.jsxs)(a.Fragment,{children:[a.jsx(l.J,{condition:"details"===r,children:(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h2",{children:n("Country of deposit")}),a.jsx(i.Wi,{name:"country",control:e.control,render:({field:e})=>(0,a.jsxs)(i.xJ,{className:"w-full",children:[a.jsx(i.NI,{children:a.jsx(P.g,{defaultCountry:e.value,onSelectChange:s=>{e.onChange(s?.code?.cca2)}})}),a.jsx(i.zG,{})]})})]}),a.jsx("div",{className:"mt-8 flex justify-end",children:(0,a.jsxs)(A.z,{type:"submit",onClick:()=>{e.getValues("country")?c("agentSelection"):e.setError("country",{message:"Select a country to continue.",type:"required"})},className:"min-w-48",children:[a.jsx("span",{children:n("Next")}),a.jsx($.Z,{size:16})]})})]})}),a.jsx(l.J,{condition:"agentSelection"===r,children:a.jsx(X,{form:e,changeCountry:()=>c("details"),onBack:t,onNext:s})})]})}var es=t(12649);function et({onBack:e,onNext:s,isLoading:t,formData:n}){var r,i;let{t:c}=(0,g.$G)(),{getCountryByCode:d}=(0,G.F)(),[o,x]=f().useState(),{data:m}=(0,L.d)(n.isAgent?`/agents/deposit?countryCode=${n.country}&currencyCode=${n.wallet}`:""),{data:u}=(0,L.d)(`/deposits/preview/create?amount=${n.amount}`),{data:h}=(0,L.d)(!n?.isAgent&&n.method?`/gateways?currency=${n.wallet}&country=${n.country}`:""),p=((e,s,t)=>{if(!s)return null;let a=e.find(e=>e.agentId===s);return{...a,method:a.agentMethods.find(e=>e.name===t)}})(m?.data,n?.agent,n?.method),j=(r=h?.data,(i=n?.method)?r?.find(e=>e.value===i):null),y=new Y.F(n?.wallet);return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h2",{children:c("Select your preferred method")}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 py-2",children:[o&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(V.W,{countryCode:o?.code?.cca2,className:"h-5 w-8 rounded-sm"}),a.jsx("span",{children:o?.name})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(q.Z,{variant:"Bold",size:"16",className:"text-primary"}),a.jsx("span",{children:c("Selected")})]}),(0,a.jsxs)(A.z,{variant:"link",type:"button",onClick:e,className:"h-fit w-fit gap-1 px-0 py-0 text-sm font-semibold text-secondary-text sm:ml-auto",children:[a.jsx("span",{children:c("Change country")}),a.jsx(B.Z,{size:16})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[a.jsx(l.J,{condition:n.isAgent,children:(0,a.jsxs)("div",{className:"flex w-full items-center gap-2 rounded-xl border border-secondary-200 px-4 py-3 sm:w-auto",children:[a.jsx(l.J,{condition:p?.method?.logo,children:a.jsx(w.default,{src:(0,Y.qR)(p?.method?.logo),alt:p?.method?.name,width:64,height:64,className:"h-8 w-8"})}),a.jsx("p",{className:"text-sm",children:p?.method?.name})]})}),a.jsx(l.J,{condition:!n.isAgent&&!!n.method,children:(0,a.jsxs)("div",{className:"flex w-full items-center gap-2 rounded-xl border border-secondary-200 px-4 py-3 sm:w-auto",children:[j?.logoImage?a.jsx(w.default,{src:(0,Y.qR)(j?.logoImage),alt:j?.name,width:64,height:64,className:"h-8 w-8"}):null,a.jsx("p",{className:"text-sm",children:j?.name})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 pl-4",children:[a.jsx(q.Z,{className:"text-primary",variant:"Bold",size:"20"}),a.jsx("p",{className:"text-sm",children:c("Selected")})]}),(0,a.jsxs)("button",{type:"button",onClick:e,className:"ml-auto flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[a.jsx("span",{children:c("Change")}),a.jsx(B.Z,{size:12})]})]})]}),a.jsx(F.Z,{className:"mb-1 mt-[5px] bg-divider-secondary"}),(0,a.jsxs)(es.Y,{groupName:c("Deposit details"),children:[a.jsx(es.r,{title:c("Amount"),value:`${y.formatVC(Number(u?.data?.amount??0))}`}),a.jsx(es.r,{title:c("Service charge"),value:`${y.formatVC(u?.data?.fee??0)}`}),a.jsx(es.r,{title:c("You get"),value:`${y.formatVC(u?.data?.totalAmount??0)}`})]}),a.jsx(F.Z,{className:"mb-1 mt-[5px] bg-divider-secondary"}),(0,a.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,a.jsxs)(A.z,{variant:"outline",onClick:e,type:"button",children:[a.jsx(D.Z,{size:16}),a.jsx("span",{children:c("Back")})]}),(0,a.jsxs)(A.z,{type:"button",disabled:t,onClick:s,className:"min-w-48",children:[a.jsx(l.J,{condition:t,children:a.jsx(N.Loader,{title:c("Processing..."),className:"text-primary-foreground"})}),(0,a.jsxs)(l.J,{condition:!t,children:[a.jsx("span",{children:c("Next")}),a.jsx($.Z,{size:16})]})]})]})]})}let ea=v.z.object({wallet:v.z.string().min(1,"Wallet is required."),amount:v.z.string().min(1,"Amount is required."),agent:v.z.string().optional(),country:v.z.string().optional(),method:v.z.string().optional(),isAgent:v.z.boolean().default(!1)});function el(){let{auth:e,deviceLocation:s}=(0,m.a)(),{t}=(0,g.$G)(),[c,d]=p.useTransition(),[f,v]=p.useState(null),[N,w]=p.useState("amount"),[b,C]=p.useState([{value:"amount",title:t("Amount"),complete:!1},{value:"payment_method",title:t("Payment method"),complete:!1},{value:"review",title:t("Review"),complete:!1}]),Z=(0,h.useRouter)(),A=(0,j.cI)({resolver:(0,u.F)(ea),mode:"all",defaultValues:{wallet:"",amount:"",agent:"",country:s?.countryCode,method:"",isAgent:!1}});p.useEffect(()=>{"amount"===N&&A.reset({wallet:A.getValues("wallet"),amount:A.getValues("amount"),agent:"",country:s?.countryCode,method:"",isAgent:!1})},[N]),p.useEffect(()=>{s&&"amount"===N&&A.reset({wallet:A.getValues("wallet"),amount:A.getValues("amount"),agent:"",country:s?.countryCode,method:"",isAgent:!1})},[s]),p.useEffect(()=>()=>{A.reset({wallet:"",amount:"",agent:"",country:s?.countryCode,method:"",isAgent:!1})},[]);let z=e=>{let s=[...b];C(s?.map(s=>s.value===e?{...s,complete:!0}:s))};return e?.canMakeDeposit()?a.jsx(i.l0,{...A,children:a.jsx("form",{children:a.jsx("div",{className:"w-full p-4 pb-10 md:p-12",children:(0,a.jsxs)("div",{className:"mx-auto max-w-3xl",children:[a.jsx(r.R,{tabs:b,value:N,onTabChange:e=>w(e),children:(0,a.jsxs)("div",{className:"p-4",children:[a.jsx(r.Q,{value:"amount",children:a.jsx(k,{form:A,onNext:()=>{w("payment_method"),z("amount")}})}),a.jsx(r.Q,{value:"payment_method",children:a.jsx(ee,{form:A,updateTab:()=>{if(A.getValues("isAgent")&&!A.getValues("method")){A.setError("method",{message:t("Select a method to continue."),type:"required"});return}w("review"),z("payment_method")},onBack:()=>w("amount")})}),a.jsx(r.Q,{value:"review",children:a.jsx(et,{formData:A.getValues(),isLoading:c,onBack:()=>w("payment_method"),onNext:A.handleSubmit(e=>{d(async()=>{if(e.isAgent){let s=await o({agentId:e.agent?e.agent.toString():"",method:e.method?e.method?.toString():"",inputValue:"",amount:Number(e.amount),currencyCode:e.wallet,countryCode:e.country});s?.status?(y.toast.success(s?.message),Z.push(`/deposit/transaction-status?trxId=${s.data.trxId}`),v({res:s})):y.toast.error(s?.message)}if(!e.isAgent&&e.method){let s=await x({method:e.method?e.method?.toString():"",amount:Number(e.amount),currencyCode:e.wallet,country:e.country});s?.status?(y.toast.success(s?.message),Z.push(`/deposit/transaction-status?trxId=${s.data.trxId}`),w("confirm"),v(s)):y.toast.error(s?.message)}}),z("review")},()=>{y.toast.error(t("Something went wrong."))})})})]})}),a.jsx(l.J,{condition:"confirm"===N,children:a.jsx(S,{res:f})})]})})})}):a.jsx(n.Z,{className:"flex-1 p-10"})}},14926:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});var a=t(10326),l=t(77863),n=t(90434),r=t(70012);function i({className:e}){let{t:s}=(0,r.$G)();return a.jsx("div",{className:(0,l.ZP)("flex items-center justify-center",e),children:(0,a.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[a.jsx("h3",{className:"mb-2.5",children:s("This feature is temporarily unavailable")}),(0,a.jsxs)("p",{className:"text-sm text-secondary-text",children:[s("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),a.jsx(n.default,{href:"/contact-supports",className:"text-primary hover:underline",children:s("support")}),"."]}),a.jsx("p",{className:"mt-2 text-sm text-secondary-text",children:s("Thank you for your understanding.")})]})})}},61718:(e,s,t)=>{"use strict";t.d(s,{g:()=>u});var a=t(10326),l=t(17577),n=t(92392),r=t(80609),i=t(2454),c=t(30811),d=t(1868),o=t(77863),x=t(6216),m=t(70012);function u({allCountry:e=!1,defaultValue:s,defaultCountry:t,onSelectChange:u,disabled:h=!1,triggerClassName:p,arrowClassName:f,flagClassName:j,display:g,placeholderClassName:y,align:v="start",side:N="bottom"}){let{t:w}=(0,m.$G)(),{countries:b,getCountryByCode:C,isLoading:S}=(0,d.F)(),[Z,A]=l.useState(!1),[z,$]=l.useState(s);return l.useEffect(()=>{s&&$(s)},[s]),l.useEffect(()=>{(async()=>{t&&await C(t,e=>{e&&($(e),u(e))})})()},[t]),(0,a.jsxs)(c.J2,{open:Z,onOpenChange:A,children:[(0,a.jsxs)(c.xo,{disabled:h,className:(0,o.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",p),children:[z?a.jsx("div",{className:"flex flex-1 items-center",children:(0,a.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[a.jsx(r.W,{className:j,countryCode:z.code?.cca2==="*"?"UN":z.code?.cca2}),void 0!==g?g(z):a.jsx("span",{children:z.name})]})}):a.jsx("span",{className:(0,o.ZP)("text-placeholder",y),children:w("Select country")}),a.jsx(x.Z,{className:(0,o.ZP)("size-6",f)})]}),a.jsx(c.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:v,side:N,children:(0,a.jsxs)(i.mY,{children:[a.jsx(i.sZ,{placeholder:w("Search...")}),a.jsx(i.e8,{children:(0,a.jsxs)(i.fu,{children:[S&&a.jsx(n.Loader,{}),e&&(0,a.jsxs)(i.di,{value:w("All countries"),onSelect:()=>{$({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),u({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),A(!1)},children:[a.jsx(r.W,{countryCode:"UN"}),a.jsx("span",{className:"pl-1.5",children:w("All countries")})]}),b?.map(e=>"officially-assigned"===e.status?a.jsxs(i.di,{value:e.name,onSelect:()=>{$(e),u(e),A(!1)},children:[a.jsx(r.W,{countryCode:e.code.cca2}),a.jsxs("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},63761:(e,s,t)=>{"use strict";t.d(s,{R:()=>i});var a=t(10326);t(17577);var l=t(54432),n=t(77863),r=t(32894);function i({iconPlacement:e="start",className:s,containerClass:t,...i}){return(0,a.jsxs)("div",{className:(0,n.ZP)("relative flex items-center",t),children:[a.jsx(r.Z,{size:"20",className:(0,n.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),a.jsx(l.I,{type:"text",className:(0,n.ZP)("h-10","end"===e?"pr-10":"pl-10",s),...i})]})}},80609:(e,s,t)=>{"use strict";t.d(s,{W:()=>r});var a=t(10326),l=t(77863),n=t(46226);function r({countryCode:e,className:s,url:t}){return e||t?a.jsx(n.default,{src:t??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,l.ZP)("rounded-[2px]",s)}):null}},88846:(e,s,t)=>{"use strict";t.d(s,{E:()=>c,m:()=>d});var a=t(10326),l=t(17577),n=t(18623),r=t(53982),i=t(77863);let c=l.forwardRef(({className:e,...s},t)=>a.jsx(n.fC,{className:(0,i.ZP)("grid gap-2",e),...s,ref:t}));c.displayName=n.fC.displayName;let d=l.forwardRef(({className:e,...s},t)=>a.jsx(n.ck,{ref:t,className:(0,i.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:a.jsx(n.z$,{className:"flex items-center justify-center",children:a.jsx(r.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));d.displayName=n.ck.displayName},1868:(e,s,t)=>{"use strict";t.d(s,{F:()=>d});class a{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var l=t(44099),n=t(85999),r=t(84455);let i=l.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),c="name,cca2,ccn3,cca3,status,flag,flags";function d(){let{data:e,isLoading:s,...t}=(0,r.ZP)(`/all?fields=${c}`,e=>i.get(e)),d=e?.data,o=async(e,s)=>{try{let t=await i.get(`/alpha/${e.toLowerCase()}?fields=${c}`),l=t.data?new a(t.data):null;s(l)}catch(e){l.default.isAxiosError(e)&&n.toast.error("Failed to fetch country")}};return{countries:d?d.map(e=>new a(e)):[],isLoading:s,getCountryByCode:o,...t}}},61610:(e,s,t)=>{"use strict";t.d(s,{M:()=>a});class a{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.active=e?.active,this.activeApi=e?.activeApi,this.recommended=e?.recommended,this.variables=e?.variables,this.allowedCurrencies=e?.allowedCurrencies,this.allowedCountries=e?.allowedCountries,this.createdAt=e?.createdAt?new Date(e?.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):null}}},85049:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),l=t(9922);function n(){return a.jsx(l.default,{})}},84514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(19510),l=t(40099),n=t(76609);function r({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(n.Z,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(l.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},18406:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),l=t(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(l.a,{})})}},334:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),l=t(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(l.a,{})})}},9922:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\deposit\page.tsx#default`)},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(19510),l=t(40099),n=t(76609);function r({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(n.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(l.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),l=t(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(l.a,{})})}},14963:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),l=t(9922);function n(){return a.jsx(l.default,{})}}};