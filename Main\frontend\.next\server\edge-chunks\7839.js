(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7839],{81281:(e,t,s)=>{Promise.resolve().then(s.bind(s,44450)),Promise.resolve().then(s.bind(s,38321))},38321:(e,t,s)=>{"use strict";s.d(t,{default:()=>M});var a=s(60926),i=s(36162),n=s(74988),r=s(84607),d=s(840),o=s(43291),l=s(65091),c=s(47020),u=s(51496),m=s(32917),h=s(65694),x=s(34870),v=s(48132),f=s(55929),p=s(50201),g=s(41529),b=s(95334),y=s(28675),k=s(44788),j=s(6851),w=s(30684),N=s(92420),A=s(5147),z=s(53735),Z=s(32793),D=s(73634),C=s(28277),S=s(737),q=s(39228),I=s(28871),E=s(23065),B=s(65116),_=s(64947);function P({title:e="",nav:t}){let s=(0,_.BT)(),{setIsExpanded:i}=(0,r.q)(),{width:n}=(0,E.B)(),d=()=>{n<1024&&i(!1)};return(0,a.jsxs)("div",{className:"py-4",children:[e&&(0,a.jsx)("div",{className:"mb-2 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 text-sm font-medium tracking-wide text-secondary-600",children:e})}),(0,a.jsx)("div",{className:"flex flex-col gap-2",children:t.map(e=>void 0===e.visible||e.visible?(0,a.jsxs)(S.Z,{href:e?.link,"aria-disabled":void 0===e?.isActive||e.isActive,onClick:t=>{void 0===e?.isActive||e.isActive?d():t.preventDefault()},className:(0,B.Z)("flex w-full items-center gap-2 whitespace-nowrap rounded-2xl px-2 py-2 transition-all duration-150 ease-in-out hover:bg-secondary active:bg-important/20",s===e.key&&"bg-secondary",("(dashboard)"===s||"__DEFAULT__"===s)&&"dashboard"===e.key&&"bg-secondary",void 0!==e.isActive&&!e.isActive&&"opacity-50"),children:[e?.icon&&(0,a.jsx)("div",{"data-active":s===e.key,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon}),(0,a.jsx)("p",{className:"font-medium",children:e?.name}),void 0!==e.badge?(0,a.jsx)(I.C,{variant:e.badge?.variant??"secondary",className:(0,l.ZP)("",e.badge?.className),children:e.badge.title}):null]},e?.key):null)})]})}function M({userRole:e="customer"}){let{t}=(0,q.$G)(),{isExpanded:s,setIsExpanded:I}=(0,r.q)(),{settings:E,isLoading:B}=function(){let{data:e,...t}=(0,o.d)("/settings/global");return{settings:e?.data,...t}}(),{logo:_,siteName:M}=(0,d.T)();return(0,a.jsxs)("div",{"data-expanded":s,className:"group absolute z-[60] flex h-full min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full sm:pb-1 lg:relative lg:z-auto",children:[(0,a.jsx)(i.z,{size:"icon",variant:"outline",onClick:()=>I(!1),className:"absolute -right-5 top-4 rounded-full bg-background group-data-[expanded=false]:hidden lg:hidden",children:(0,a.jsx)(c.Z,{})}),(0,a.jsx)("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary",children:(0,a.jsx)(S.Z,{href:"/",className:"flex items-center justify-center",children:(0,a.jsx)(C.Z,{src:(0,l.qR)(_),width:160,height:40,alt:M,className:"max-h-10 object-contain"})})}),(0,a.jsxs)("div",{className:"flex w-full flex-1 flex-col overflow-y-auto overflow-x-hidden px-4",children:[(0,a.jsx)(P,{nav:[{key:"(dashboard)",name:t("Dashboard"),icon:(0,a.jsx)(u.Z,{size:"20"}),link:"/",isLoading:!1},{key:"deposit",name:t("Deposit"),icon:(0,a.jsx)(m.Z,{size:"20"}),link:"/deposit",isLoading:B,isActive:E?.deposit?.status==="on"},{key:"transfer",name:t("Transfer"),icon:(0,a.jsx)(h.Z,{size:"20"}),link:"/transfer",visible:"agent"!==e,isLoading:B,isActive:E?.transfer?.status==="on"},{key:"withdraw",name:t("Withdraw"),icon:(0,a.jsx)(x.Z,{size:"20"}),link:"/withdraw",isLoading:B,isActive:E?.withdraw?.status==="on"},{key:"exchange",name:t("Exchange"),icon:(0,a.jsx)(v.Z,{size:"20"}),link:"/exchange",isLoading:B,isActive:E?.exchange?.status==="on"},{key:"payment",name:t("Payment"),icon:(0,a.jsx)(f.Z,{size:"20"}),link:"/payment",visible:"agent"!==e,isLoading:B,isActive:E?.payment?.status==="on"},{key:"services",name:t("Services"),icon:(0,a.jsx)(p.Z,{size:"20"}),link:"/services",visible:"agent"!==e,isLoading:B,isActive:!0},{key:"cards",name:t("Cards"),icon:(0,a.jsx)(g.Z,{size:"20"}),link:"/cards",isLoading:B,isActive:E?.virtual_card?.status==="on",visible:E?.virtual_card?.status==="on"},{key:"investments",name:t("Investments"),icon:(0,a.jsx)(b.Z,{size:"20"}),link:"/investments",isLoading:B}]}),(0,a.jsx)(n.Z,{className:"bg-divider-secondary"}),(0,a.jsx)(P,{nav:[{key:"direct-deposit",name:t("Deposit to Customer"),icon:(0,a.jsx)(m.Z,{size:"20"}),link:"/direct-deposit",visible:"agent"===e,isLoading:B,isActive:!0},{key:"deposit-request",name:t("Deposit Requests"),icon:(0,a.jsx)(m.Z,{size:"20"}),link:"/deposit-request",visible:"agent"===e,isLoading:B},{key:"withdraw-request",name:t("Withdraw Requests"),icon:(0,a.jsx)(y.Z,{size:"20"}),link:"/withdraw-request",visible:"agent"===e,isLoading:B},{key:"transaction-history",name:t("Transaction History"),icon:(0,a.jsx)(k.Z,{size:"20"}),link:"/transaction-history",isLoading:B},{key:"investments-history",name:t("Investments History"),icon:(0,a.jsx)(k.Z,{size:"20"}),link:"/investments-history",isLoading:B},{key:"settlements",name:t("Settlements"),icon:(0,a.jsx)(k.Z,{size:"20"}),link:"/settlements",visible:"agent"===e,isLoading:B},{key:"merchant-transactions",name:t("Merchant Transaction"),icon:(0,a.jsx)(j.Z,{size:"20"}),link:"/merchant-transactions",visible:"merchant"===e,isLoading:B},{key:"payment-requests",name:t("Payment Requests"),icon:(0,a.jsx)(w.Z,{size:"20"}),link:"/payment-requests",visible:"merchant"===e,isLoading:B},{key:"favorites",name:t("Favorites"),icon:(0,a.jsx)(N.Z,{size:"20"}),link:"/favorites",visible:"agent"!==e,isLoading:B},{key:"contacts",name:t("Contacts"),icon:(0,a.jsx)(A.Z,{size:"20"}),link:"/contacts",visible:"agent"!==e,isLoading:B},{key:"wallets",name:t("Wallets"),icon:(0,a.jsx)(z.Z,{size:"20"}),link:"/wallets",isLoading:B},{key:"referral",name:t("Referral"),icon:(0,a.jsx)(Z.Z,{size:"20"}),link:"/referral",isLoading:B},{key:"settings",name:t("Settings"),icon:(0,a.jsx)(D.Z,{size:"20"}),link:"/settings",isLoading:B}]})]})]})}s(29220)},62797:(e,t,s)=>{"use strict";s.d(t,{z:()=>o});var a=s(60926),i=s(15185),n=s(65091),r=s(9172),d=s(90543);function o({senderName:e,senderAvatar:t,senderInfo:s,receiverName:i,receiverAvatar:r,receiverInfo:d,className:o}){return(0,a.jsxs)("div",{className:(0,n.ZP)("mb-4 flex items-start justify-around gap-1",o),children:[(0,a.jsx)(l,{name:e,avatar:t,info:s}),i&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),(0,a.jsx)(l,{name:i,avatar:r,info:d})]})]})}function l({avatar:e,name:t,info:s=[]}){let n=s.filter(Boolean);return(0,a.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,a.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,a.jsxs)(i.qE,{className:"size-10 rounded-full sm:size-14",children:[(0,a.jsx)(i.F$,{src:e,alt:t,width:56,height:56}),(0,a.jsx)(i.Q5,{className:"font-semibold",children:(0,r.v)(t)})]}),(0,a.jsx)("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:(0,a.jsx)(d.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:t}),n.length>0&&n.map((e,t)=>(0,a.jsx)("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},t))]})]})}},28871:(e,t,s)=>{"use strict";s.d(t,{C:()=>d});var a=s(60926),i=s(8206);s(29220);var n=s(65091);let r=(0,i.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,n.ZP)(r({variant:t}),e),...s})}},3632:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var a=s(73244),i=s(73146),n=s(65091);class r{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,n.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new i.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}var d=s(14455),o=s(74190);class l{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new r(e?.user),customer:e?.user?.customer?new o.O(e?.user?.customer):null,merchant:e?.user?.merchant?new o.O(e?.user?.merchant):null,agent:e?.user?.agent?new o.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,d.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,d.WU)(this.updatedAt,e):"N/A"}}},44491:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(42416),i=s(33908);let n=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\SideNav.tsx#default`);function r({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[(0,a.jsx)(n,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[(0,a.jsx)(i.Z,{}),(0,a.jsx)("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}s(87908)},20626:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),i=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(i.a,{})})}}}]);
//# sourceMappingURL=7839.js.map