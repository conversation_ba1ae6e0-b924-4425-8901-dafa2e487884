exports.id=3485,exports.ids=[3485],exports.modules={90346:(e,t,r)=>{Promise.resolve().then(r.bind(r,7076))},91711:(e,t,r)=>{Promise.resolve().then(r.bind(r,21061))},5441:(e,t,r)=>{Promise.resolve().then(r.bind(r,92779))},7076:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(10326),a=r(21061);function l(){return n.jsx(a.default,{})}},21061:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>I});var n=r(10326),a=r(5158),l=r(92392),s=r(90772),c=r(54432),o=r(31048),i=r(87673),u=r(49547),d=r(10734);async function m(e){try{let t=await u.Z.post("/users/send-support-email",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,d.B)(t)}catch(e){return(0,d.D)(e)}}var p=r(72871),f=r(52920),h=r(17577),v=r.n(h),x=r(78439),g=r.n(x),j=["variant","color","size"],E=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"m16.14 2.96-9.03 3c-6.07 2.03-6.07 5.34 0 7.36l2.68.89.89 2.68c2.02 6.07 5.34 6.07 7.36 0l3.01-9.02c1.34-4.05-.86-6.26-4.91-4.91Zm.32 5.38-3.8 3.82c-.15.15-.34.22-.53.22s-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l3.8-3.82c.29-.29.77-.29 1.06 0 .*********** 0 1.06Z",fill:t}))},b=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"M15.89 3.49c3.81-1.27 5.88.81 4.62 4.62l-2.83 8.49c-1.9 5.71-5.02 5.71-6.92 0l-.84-2.52-2.52-.84c-5.71-1.9-5.71-5.01 0-6.92L12 4.79M10.11 13.649l3.58-3.59",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},k=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{opacity:".4",d:"m7.11 5.961 9.02-3.01c4.05-1.35 6.25.86 4.91 4.91l-3.01 9.02c-2.02 6.07-5.34 6.07-7.36 0l-.89-2.68-2.68-.89c-6.06-2.01-6.06-5.32.01-7.35Z",fill:t}),v().createElement("path",{d:"m12.12 11.629 3.81-3.82ZM12.12 12.38c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l3.8-3.82c.29-.29.77-.29 1.06 0 .*********** 0 1.06l-3.8 3.82c-.15.14-.34.22-.53.22Z",fill:t}))},y=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"m7.4 6.32 8.49-2.83c3.81-1.27 5.88.81 4.62 4.62l-2.83 8.49c-1.9 5.71-5.02 5.71-6.92 0l-.84-2.52-2.52-.84c-5.71-1.9-5.71-5.01 0-6.92ZM10.11 13.65l3.58-3.59",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},w=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"M14.22 21.63c-1.18 0-2.85-.83-4.17-4.8l-.72-2.16-2.16-.72c-3.96-1.32-4.79-2.99-4.79-4.17 0-1.17.83-2.85 4.79-4.18l8.49-2.83c2.12-.71 3.89-.5 4.98.58 1.09 1.08 1.3 2.86.59 4.98l-2.83 8.49c-1.33 3.98-3 4.81-4.18 4.81ZM7.64 7.03c-2.78.93-3.77 2.03-3.77 2.75 0 .72.99 1.82 3.77 2.74l2.52.84c.22.07.4.25.47.47l.84 2.52c.92 2.78 2.03 3.77 2.75 3.77.72 0 1.82-.99 2.75-3.77l2.83-8.49c.51-1.54.42-2.8-.23-3.45-.65-.65-1.91-.73-3.44-.22L7.64 7.03Z",fill:t}),v().createElement("path",{d:"M10.11 14.4c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l3.58-3.59c.29-.29.77-.29 1.06 0 .*********** 0 1.06l-3.58 3.59c-.14.15-.34.22-.53.22Z",fill:t}))},L=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"m7.4 6.32 8.49-2.83c3.81-1.27 5.88.81 4.62 4.62l-2.83 8.49c-1.9 5.71-5.02 5.71-6.92 0l-.84-2.52-2.52-.84c-5.71-1.9-5.71-5.01 0-6.92Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),v().createElement("path",{opacity:".34",d:"m10.11 13.649 3.58-3.59",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},N=function(e,t){switch(e){case"Bold":return v().createElement(E,{color:t});case"Broken":return v().createElement(b,{color:t});case"Bulk":return v().createElement(k,{color:t});case"Linear":default:return v().createElement(y,{color:t});case"Outline":return v().createElement(w,{color:t});case"TwoTone":return v().createElement(L,{color:t})}},M=(0,h.forwardRef)(function(e,t){var r=e.variant,n=e.color,a=e.size,l=(0,f._)(e,j);return v().createElement("svg",(0,f.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),N(r,n))});M.propTypes={variant:g().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:g().string,size:g().oneOfType([g().string,g().number])},M.defaultProps={variant:"Linear",color:"currentColor",size:"24"},M.displayName="Send2";var Z=["variant","color","size"],C=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Zm.18 12.63-2.22 2.22c-.54.54-1.24.8-1.94.8s-1.41-.27-1.94-.8a2.758 2.758 0 0 1 0-3.89l1.41-1.41c.29-.29.77-.29 1.06 0 .*********** 0 1.06l-1.41 1.41a1.25 1.25 0 0 0 0 1.77c.49.49 1.28.49 1.77 0l2.22-2.22c.61-.61.95-1.43.95-2.3 0-.87-.34-1.68-.95-2.3-1.23-1.23-3.37-1.23-4.6 0L8.29 11.4a2.692 2.692 0 0 0 0 3.79c.*********** 0 1.06-.29.29-.77.29-1.06 0a4.183 4.183 0 0 1 0-5.91l2.42-2.42c.9-.9 2.09-1.39 3.36-1.39s2.46.49 3.36 1.39c.9.9 1.39 2.09 1.39 3.36s-.49 2.46-1.39 3.35Z",fill:t}))},z=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"m5.762 8.809-1.21 1.21c-2.34 2.34-2.34 6.14 0 8.49M12.33 12.152l-2.47 2.47a3.495 3.495 0 0 0 0 4.95 3.495 3.495 0 0 0 4.95 0l3.89-3.89a7.007 7.007 0 0 0 0-9.9 7.007 7.007 0 0 0-9.9 0",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},B=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{opacity:".4",d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Z",fill:t}),v().createElement("path",{d:"M12.2 17.661c-.7 0-1.41-.27-1.94-.8a2.758 2.758 0 0 1 0-3.89l1.41-1.41c.29-.29.77-.29 1.06 0 .*********** 0 1.06l-1.41 1.41a1.25 1.25 0 0 0 0 1.77c.49.49 1.28.49 1.77 0l2.22-2.22c.61-.61.95-1.43.95-2.3 0-.87-.34-1.68-.95-2.3-1.23-1.23-3.37-1.23-4.6 0l-2.42 2.42a2.692 2.692 0 0 0 0 3.79c.*********** 0 1.06-.29.29-.77.29-1.06 0a4.183 4.183 0 0 1 0-5.91l2.42-2.42c.9-.9 2.09-1.39 3.36-1.39s2.46.49 3.36 1.39c.9.9 1.39 2.09 1.39 3.36s-.49 2.46-1.39 3.36l-2.22 2.22c-.54.53-1.24.8-1.95.8Z",fill:t}))},P=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"m12.33 12.15-2.47 2.47a3.495 3.495 0 0 0 0 4.95 3.495 3.495 0 0 0 4.95 0l3.89-3.89a7.007 7.007 0 0 0 0-9.9 7.007 7.007 0 0 0-9.9 0l-4.24 4.24c-2.34 2.34-2.34 6.14 0 8.49",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},F=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"M12.33 21.34c-1.09 0-2.18-.41-3.01-1.24a4.249 4.249 0 0 1 0-6.01l2.48-2.47c.29-.29.77-.29 1.06 0 .*********** 0 1.06l-2.48 2.47a2.758 2.758 0 0 0 0 3.89 2.758 2.758 0 0 0 3.89 0l3.89-3.89a6.21 6.21 0 0 0 1.83-4.42c0-1.67-.65-3.24-1.83-4.42a6.253 6.253 0 0 0-8.84 0l-4.24 4.24a5.22 5.22 0 0 0-1.54 3.71c0 1.4.55 2.72 1.54 3.71.*********** 0 1.06-.29.29-.77.29-1.06 0a6.736 6.736 0 0 1-1.98-4.77c0-1.8.7-3.5 1.98-4.77l4.24-4.24c3.02-3.02 7.94-3.02 10.96 0a7.709 7.709 0 0 1 2.27 5.48c0 2.07-.81 4.02-2.27 5.48l-3.89 3.89c-.83.83-1.91 1.24-3 1.24Z",fill:t}))},T=function(e){var t=e.color;return v().createElement(v().Fragment,null,v().createElement("path",{d:"m12.33 12.152-2.47 2.47a3.495 3.495 0 0 0 0 4.95 3.495 3.495 0 0 0 4.95 0l3.89-3.89a7.007 7.007 0 0 0 0-9.9 7.007 7.007 0 0 0-9.9 0l-4.24 4.24c-2.34 2.34-2.34 6.14 0 8.49",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},S=function(e,t){switch(e){case"Bold":return v().createElement(C,{color:t});case"Broken":return v().createElement(z,{color:t});case"Bulk":return v().createElement(B,{color:t});case"Linear":default:return v().createElement(P,{color:t});case"Outline":return v().createElement(F,{color:t});case"TwoTone":return v().createElement(T,{color:t})}},O=(0,h.forwardRef)(function(e,t){var r=e.variant,n=e.color,a=e.size,l=(0,f._)(e,Z);return v().createElement("svg",(0,f.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),S(r,n))});O.propTypes={variant:g().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:g().string,size:g().oneOfType([g().string,g().number])},O.defaultProps={variant:"Linear",color:"currentColor",size:"24"},O.displayName="Paperclip2";var D=r(46226),W=r(70012),R=r(85999);function H(){let{t:e}=(0,W.$G)(),[t,r]=(0,h.useTransition)(),[u,d]=(0,h.useState)({subject:"",message:"",attachments:[]}),f=e=>{if(e.preventDefault(),"file"===e.target.type){let t=e.target;t.files&&t.files.length>0&&d(e=>({...e,attachments:[...e.attachments||[],...Array.from(t.files)]}))}else d(t=>({...t,[e.target.name]:e.target.value}))},v=e=>{d(t=>{let r=t.attachments?.filter(t=>t.name!==e.name||t.size!==e.size);return{...t,attachments:r}})};return(0,n.jsxs)("form",{onSubmit:e=>{e.preventDefault();let t=new FormData;t.append("subject",u.subject),t.append("message",u.message),u.attachments?.forEach(e=>{t.append("attachments[]",e)}),r(async()=>{let e=await m(t);e?.status,R.toast.success(e.message)})},className:"flex flex-col gap-4",children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[n.jsx(o.Z,{children:e("Subject")}),n.jsx(c.I,{name:"subject",type:"text",value:u.subject,onChange:f})]}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[n.jsx(o.Z,{children:e("Message")}),n.jsx(i.g,{name:"message",rows:20,value:u.message,onChange:f})]}),(0,n.jsxs)("div",{className:"relative w-full",children:[n.jsx(a.J,{condition:u?.attachments?.length!==0,children:n.jsx("div",{className:"mb-4 flex flex-col gap-3 rounded-md border bg-input p-4",children:(e=>{if(!e||e?.length===0)return null;let t=Object.groupBy(e,e=>e.type.startsWith("image/")?"images":"others");return(0,n.jsxs)(n.Fragment,{children:[Object.prototype.hasOwnProperty.call(t,"images")&&n.jsx("div",{className:"flex flex-wrap items-center gap-2 [&img]:aspect-square [&img]:max-w-28",children:t?.images?.map((e,t)=>n.jsxs("div",{className:"group relative flex",children:[n.jsx(D.default,{src:URL.createObjectURL(e),alt:e.name,width:128,height:128,loading:"lazy",className:"t-fill h-28 w-28 rounded-xl border"}),n.jsx(s.z,{type:"button",variant:"destructive",size:"icon",onClick:()=>v(e),className:"pointer-events-none invisible absolute right-1 top-1 size-7 group-hover:pointer-events-auto group-hover:visible",children:n.jsx(p.Z,{size:15})})]},t))}),Object.prototype.hasOwnProperty.call(t,"others")&&n.jsx("ul",{className:"list-inside list-disc",children:t?.others?.map(e=>n.jsxs("li",{className:"group text-sm",children:[e.name,n.jsx(s.z,{type:"button",variant:"link",size:"icon",onClick:()=>v(e),className:"pointer-events-none invisible size-7 group-hover:pointer-events-auto group-hover:visible",children:n.jsx(p.Z,{size:15})})]},e.name+e.lastModified))})]})})(u.attachments)})}),(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsxs)(s.z,{disabled:t,type:"submit",className:"w-fit min-w-32",children:[(0,n.jsxs)(a.J,{condition:!t,children:[n.jsx(M,{}),e("Send")]}),n.jsx(a.J,{condition:t,children:n.jsx(l.Loader,{title:e("Sending..."),className:"text-primary-foreground"})})]}),n.jsx(s.z,{type:"button",variant:"link",className:"cursor-pointer gap-1 text-secondary-text hover:text-foreground",asChild:!0,children:(0,n.jsxs)(o.Z,{htmlFor:"attachments",children:[n.jsx(O,{}),e("Attachment"),n.jsx(c.I,{id:"attachments",type:"file",multiple:!0,name:"attachments",onChange:f,className:"hidden"})]})})]})]})]})}var A=r(8281);function I(){let{t:e}=(0,W.$G)();return(0,n.jsxs)("div",{className:"m-4 rounded-xl bg-background p-4 md:p-10",children:[n.jsx("h2",{className:"mb-1",children:e("Need Help?")}),n.jsx("p",{className:"text-sm text-secondary-text",children:e("We're here to assist you with any questions or issues you may encounter. Please feel free to contact our support team by emailing:")}),n.jsx(A.Z,{className:"my-6"}),n.jsx("div",{className:"mb-6 rounded-md bg-info/20 p-4 text-sm",children:e("For faster assistance, please provide as much detail as possible about your issue, including screenshots or error messages if applicable. Our support hours are 24/7.")}),n.jsx(H,{})]})}},92779:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(10326),a=r(21061);function l(){return n.jsx(a.default,{})}},54432:(e,t,r)=>{"use strict";r.d(t,{I:()=>s});var n=r(10326),a=r(17577),l=r(77863);let s=a.forwardRef(({className:e,type:t,...r},a)=>n.jsx("input",{type:t,className:(0,l.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:a,...r}));s.displayName="Input"},31048:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(10326),a=r(34478),l=r(79360),s=r(17577),c=r(77863);let o=(0,l.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=s.forwardRef(({className:e,...t},r)=>n.jsx(a.f,{ref:r,className:(0,c.ZP)(o(),e),...t}));i.displayName=a.f.displayName;let u=i},87673:(e,t,r)=>{"use strict";r.d(t,{g:()=>s});var n=r(10326),a=r(17577),l=r(77863);let s=a.forwardRef(({className:e,...t},r)=>n.jsx("textarea",{className:(0,l.ZP)("placeholder:text-placeholder flex min-h-[80px] w-full rounded-md border border-input bg-input px-3 py-2 text-base ring-offset-background placeholder:font-normal focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));s.displayName="Textarea"},72871:(e,t,r)=>{"use strict";r.d(t,{Z:()=>v});var n=r(52920),a=r(17577),l=r.n(a),s=r(78439),c=r.n(s),o=["variant","color","size"],i=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21.07 5.23c-1.61-.16-3.22-.28-4.84-.37v-.01l-.22-1.3c-.15-.92-.37-2.3-2.71-2.3h-2.62c-2.33 0-2.55 1.32-2.71 2.29l-.21 1.28c-.93.06-1.86.12-2.79.21l-2.04.2c-.42.04-.72.41-.***********.4.71.82.67l2.04-.2c5.24-.52 10.52-.32 15.82.21h.08c.38 0 .71-.29.75-.68a.766.766 0 0 0-.69-.82ZM19.23 8.14c-.24-.25-.57-.39-.91-.39H5.68c-.34 0-.68.14-.91.39-.23.25-.36.59-.34.94l.62 10.26c.11 1.52.25 3.42 3.74 3.42h6.42c3.49 0 3.63-1.89 3.74-3.42l.62-10.25c.02-.36-.11-.7-.34-.95Zm-5.57 9.61h-3.33c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h3.33c.41 0 .75.34.75.75s-.34.75-.75.75Zm.84-4h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},u=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98M8.5 4.97l.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3M15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14M18.85 9.14l-.65 10.07M10.33 16.5h3.33M12.82 12.5h1.68M9.5 12.5h.83",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21.07 5.23c-1.61-.16-3.22-.28-4.84-.37v-.01l-.22-1.3c-.15-.92-.37-2.3-2.71-2.3h-2.62c-2.33 0-2.55 1.32-2.71 2.29l-.21 1.28c-.93.06-1.86.12-2.79.21l-2.04.2c-.42.04-.72.41-.***********.4.71.82.67l2.04-.2c5.24-.52 10.52-.32 15.82.21h.08c.38 0 .71-.29.75-.68a.766.766 0 0 0-.69-.82Z",fill:t}),l().createElement("path",{opacity:".399",d:"M19.23 8.14c-.24-.25-.57-.39-.91-.39H5.68c-.34 0-.68.14-.91.39-.23.25-.36.59-.34.94l.62 10.26c.11 1.52.25 3.42 3.74 3.42h6.42c3.49 0 3.63-1.89 3.74-3.42l.62-10.25c.02-.36-.11-.7-.34-.95Z",fill:t}),l().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.58 17a.75.75 0 0 1 .75-.75h3.33a.75.75 0 0 1 0 1.5h-3.33a.75.75 0 0 1-.75-.75ZM8.75 13a.75.75 0 0 1 .75-.75h5a.75.75 0 0 1 0 1.5h-5a.75.75 0 0 1-.75-.75Z",fill:t}))},m=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98M8.5 4.97l.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3M18.85 9.14l-.65 10.07C18.09 20.78 18 22 15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14M10.33 16.5h3.33M9.5 12.5h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 6.73h-.08c-5.29-.53-10.57-.73-15.8-.2l-2.04.2a.755.755 0 0 1-.83-.68c-.04-.42.26-.78.67-.82l2.04-.2c5.32-.54 10.71-.33 **********.***********.82a.74.74 0 0 1-.74.68Z",fill:t}),l().createElement("path",{d:"M8.5 5.72c-.04 0-.08 0-.13-.01a.753.753 0 0 1-.61-.86l.22-1.31c.16-.96.38-2.29 2.71-2.29h2.62c2.34 0 2.56 1.38 2.71 2.3l.22 1.3c.07.41-.21.8-.61.86-.41.07-.8-.21-.86-.61l-.22-1.3c-.14-.87-.17-1.04-1.23-1.04H10.7c-1.06 0-1.08.14-1.23 1.03l-.23 1.3a.75.75 0 0 1-.74.63ZM15.21 22.752H8.79c-3.49 0-3.63-1.93-3.74-3.49L4.4 9.192c-.03-.41.29-.77.7-.8.42-.02.77.29.8.7l.65 10.07c.11 1.52.15 2.09 2.24 2.09h6.42c2.1 0 2.14-.57 2.24-2.09l.65-10.07c.03-.41.39-.72.8-.7.41.03.73.38.7.8l-.65 10.07c-.11 1.56-.25 3.49-3.74 3.49Z",fill:t}),l().createElement("path",{d:"M13.66 17.25h-3.33c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h3.33c.41 0 .75.34.75.75s-.34.75-.75.75ZM14.5 13.25h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},f=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{opacity:".34",d:"m8.5 4.97.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{d:"m18.85 9.14-.65 10.07C18.09 20.78 18 22 15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{opacity:".34",d:"M10.33 16.5h3.33M9.5 12.5h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return l().createElement(i,{color:t});case"Broken":return l().createElement(u,{color:t});case"Bulk":return l().createElement(d,{color:t});case"Linear":default:return l().createElement(m,{color:t});case"Outline":return l().createElement(p,{color:t});case"TwoTone":return l().createElement(f,{color:t})}},v=(0,a.forwardRef)(function(e,t){var r=e.variant,a=e.color,s=e.size,c=(0,n._)(e,o);return l().createElement("svg",(0,n.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),h(r,a))});v.propTypes={variant:c().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:c().string,size:c().oneOfType([c().string,c().number])},v.defaultProps={variant:"Linear",color:"currentColor",size:"24"},v.displayName="Trash"},45679:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\contact-supports\page.tsx#default`)},84514:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(19510),a=r(40099),l=r(76609);function s({children:e}){return(0,n.jsxs)("div",{className:"flex h-screen",children:[n.jsx(l.Z,{userRole:"agent"}),(0,n.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[n.jsx(a.Z,{}),n.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},18406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(19510),a=r(48413);function l(){return n.jsx("div",{className:"flex items-center justify-center py-10",children:n.jsx(a.a,{})})}},98423:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(19510),a=r(48413);function l(){return n.jsx("div",{className:"flex items-center justify-center py-10",children:n.jsx(a.a,{})})}},1658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\contact-supports\page.tsx#default`)},88728:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(19510),a=r(40099),l=r(76609);function s({children:e}){return(0,n.jsxs)("div",{className:"flex h-screen",children:[n.jsx(l.Z,{userRole:"customer"}),(0,n.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[n.jsx(a.Z,{}),n.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},80549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(19510),a=r(48413);function l(){return n.jsx("div",{className:"flex items-center justify-center py-10",children:n.jsx(a.a,{})})}},94080:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\contact-supports\page.tsx#default`)},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>c});var n=r(17577),a=r(45226),l=r(10326),s=n.forwardRef((e,t)=>(0,l.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var c=s}};