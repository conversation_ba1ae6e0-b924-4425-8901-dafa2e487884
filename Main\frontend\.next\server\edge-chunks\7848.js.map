{"version": 3, "file": "edge-chunks/7848.js", "mappings": "4JAIAA,EAAA,2BAEAC,EAAA,SAAAC,CAAA,EACA,IAAAC,EAAAD,EAAAC,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,wLACAC,KAAAL,CACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAP,EAAAO,EAAAP,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,qGACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAC,EAAA,SAAAC,CAAA,EACA,IAAAb,EAAAa,EAAAb,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGY,QAAA,KACAV,EAAA,kDACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,yIACAC,KAAAL,CACA,GACA,EAEAe,EAAA,SAAAC,CAAA,EACA,IAAAhB,EAAAgB,EAAAhB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,6DACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAlB,EAAAkB,EAAAlB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,2UACAC,KAAAL,CACA,GACA,EAEAmB,EAAA,SAAAC,CAAA,EACA,IAAApB,EAAAoB,EAAApB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,kDACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBV,EAAAC,aAAmB,SACtCY,QAAA,KACAV,EAAA,aACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAU,EAAA,SAAAC,CAAA,CAAAtB,CAAA,EACA,OAAAsB,GACA,WACA,OAA0BrB,EAAAC,aAAmB,CAAAJ,EAAA,CAC7CE,MAAAA,CACA,EAEA,cACA,OAA0BC,EAAAC,aAAmB,CAAAI,EAAA,CAC7CN,MAAAA,CACA,EAEA,YACA,OAA0BC,EAAAC,aAAmB,CAAAU,EAAA,CAC7CZ,MAAAA,CACA,EAEA,cAeA,QAdA,OAA0BC,EAAAC,aAAmB,CAAAa,EAAA,CAC7Cf,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAe,EAAA,CAC7CjB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAiB,EAAA,CAC7CnB,MAAAA,CACA,EAMA,CACA,EAEAuB,EAAiC,GAAAtB,EAAAuB,UAAA,EAAU,SAAAC,CAAA,CAAAC,CAAA,EAC3C,IAAAJ,EAAAG,EAAAH,OAAA,CACAtB,EAAAyB,EAAAzB,KAAA,CACA2B,EAAAF,EAAAE,IAAA,CACAC,EAAa,GAAAC,EAAAC,CAAA,EAAwBL,EAAA5B,GAErC,OAAsBI,EAAAC,aAAmB,OAAQ,GAAA2B,EAAAE,CAAA,EAAQ,GAAGH,EAAA,CAC5DI,MAAA,6BACAN,IAAAA,EACAO,MAAAN,EACAO,OAAAP,EACAQ,QAAA,YACA9B,KAAA,MACA,GAAGgB,EAAAC,EAAAtB,GACH,EACAuB,CAAAA,EAAAa,SAAA,EACAd,QAAWe,IAAAC,KAAe,wDAC1BtC,MAASqC,IAAAE,MAAA,CACTZ,KAAQU,IAAAG,SAAmB,EAAEH,IAAAE,MAAA,CAAkBF,IAAAI,MAAA,CAAgB,CAC/D,EACAlB,EAAAmB,YAAA,EACApB,QAAA,SACAtB,MAAA,eACA2B,KAAA,IACA,EACAJ,EAAAoB,WAAA,iDC7HA,IAAMC,EAAYC,CAAAA,EAAAA,SAAAA,CAAAA,EAAiB,YAAa,CAAC,CAAC,OAAQ,CAAEzC,EAAG,iBAAkB0C,IAAK,QAAS,EAAE,CAAC,2MCZlG,SAAAC,EAAAC,CAAA,EAAAC,EAAAC,EAAA,EACA,OAAAC,KAAAF,GAAA,CAAAC,EAAAC,KAAAD,GAAA,CAAAD,EAAAD,GACA,0MCCAI,EAAAC,OAAAC,MAAA,EAEAC,SAAA,WACAC,OAAA,EACAvB,MAAA,EACAC,OAAA,EACAuB,QAAA,EACAC,OAAA,GACAC,SAAA,SACAC,KAAA,mBACAC,WAAA,SACAC,SAAA,QACA,EAcAC,CAZqBC,EAAAxC,UAAgB,CACrC,CAAAyC,EAAAC,IAC2B,GAAAC,EAAAC,GAAA,EACrBC,EAAAC,EAAS,CAAAC,IAAA,CACf,CACA,GAAAN,CAAA,CACAvC,IAAAwC,EACAM,MAAA,CAAiB,GAAApB,CAAA,IAAAa,EAAAO,KAAA,CACjB,IAIA7B,WAAA,CAbA,2CCYM8B,EAAY,CAAC,IAAK,QAAS,UAAW,YAAW,CACjDC,EAAiB,CAAC,IAAK,QAAO,CAM9BC,EAAc,SAGd,CAACC,EAAYC,EAAeC,EAAqB,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,EAGzDJ,GAGI,CAACK,EAAqBC,EAAiB,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,EAAmBP,EAAa,CAC/EG,EACAK,EAAAA,EAAiBA,CAClB,EACKC,EAAiBD,CAAAA,EAAAA,EAAAA,EAAAA,IAoBjB,CAACE,EAAgBC,EAAgB,CAAIN,EAAwCL,GAQ7E,CAACY,EAA6BC,EAA6B,CAC/DR,EAAqDL,GAoDjDc,EAAgC,IACpC,GAAM,CACJC,cAAAA,CAAA,CACAC,SAAAA,CAAA,CACAC,KAAMC,CAAA,CACNC,YAAAA,CAAA,CACAC,aAAAA,CAAA,CACA/C,MAAOgD,CAAA,CACPC,aAAAA,CAAA,CACAC,cAAAA,CAAA,CACAC,IAAAA,CAAA,CACAC,KAAAA,CAAA,CACAC,aAAAA,CAAA,CACAC,SAAAA,CAAA,CACAC,SAAAA,CAAA,CACAC,KAAAA,CAAA,CACF,CAAIvC,EACEwC,EAAcrB,EAAeM,GAC7B,CAACgB,EAASC,EAAU,CAAUC,EAAAA,QAAA,CAAsC,MACpE,CAACC,EAAWC,EAAY,CAAUF,EAAAA,QAAA,CAAoC,MACtE,CAACG,EAAsBC,EAAuB,CAAUJ,EAAAA,QAAA,CAAS,IACjEK,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,EAAaf,GACzB,CAACP,EAAMuB,EAAO,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB,CAC3CC,KAAMxB,EACNyB,YAAaxB,GAAe,GAC5ByB,SAAUxB,EACVyB,OAAQ7C,CACV,GACM,CAAC3B,EAAOyE,EAAQ,CAAIL,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB,CAC7CC,KAAMrB,EACNsB,YAAarB,EACbsB,SAAUrB,EACVsB,OAAQ7C,CACV,GACM+C,EAAiCd,EAAAA,MAAA,CAAwC,MAGzEe,EAAgBjB,CAAAA,GAAUF,GAAQ,CAAC,CAACE,EAAQkB,OAAA,CAAQ,QACpD,CAACC,EAAkBC,EAAmB,CAAUlB,EAAAA,QAAA,CAAS,IAAImB,KAO7DC,EAAkBC,MAAMC,IAAA,CAAKL,GAChCM,GAAA,CAAI,GAAYC,EAAOnE,KAAA,CAAMjB,KAAK,EAClCqF,IAAA,CAAK,KAER,MACEjE,CAAAA,EAAAA,EAAAA,GAAAA,EAAiBkE,EAAAA,EAAA,CAAhB,CAAsB,GAAG7B,CAAA,CACxBd,SAAA4C,CAAAA,EAAAA,EAAAA,IAAAA,EAAClD,EAAA,CACCkB,SAAAA,EACAiC,MAAO9C,EACPgB,QAAAA,EACA+B,gBAAiB9B,EACjBE,UAAAA,EACA6B,kBAAmB5B,EACnBC,qBAAAA,EACA4B,6BAA8B3B,EAC9B4B,UAAWC,CAAAA,EAAAA,EAAAA,CAAAA,IACX7F,MAAAA,EACAkD,cAAeuB,EACf7B,KAAAA,EACAG,aAAcoB,EACdhB,IAAKc,EACLS,yBAAAA,EACApB,SAAAA,EAEAX,SAAA,CAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACQ,EAAWkE,QAAA,CAAX,CAAoBN,MAAO9C,EAC1BC,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmB,EAAA,CACCiD,MAAOvE,EAAMyB,aAAA,CACbqD,kBAAyBnC,EAAAA,WAAA,CAAY,IACnCkB,EAAoB,GAAU,IAAIC,IAAIiB,GAAMC,GAAA,CAAIb,GAClD,EAAG,EAAE,EACLc,qBAA4BtC,EAAAA,WAAA,CAAY,IACtCkB,EAAoB,IAClB,IAAMqB,EAAa,IAAIpB,IAAIiB,GAE3B,OADAG,EAAWC,MAAA,CAAOhB,GACXe,CACT,EACF,EAAG,EAAE,EAEJxD,SAAAA,CAAA,EACH,GAGDgC,EACCY,CAAAA,EAAAA,EAAAA,IAAAA,EAACc,GAAA,CAEC,cAAW,GACX9C,SAAAA,EACA+C,SAAU,GACVlD,KAAAA,EACAC,aAAAA,EACArD,MAAAA,EAEAuE,SAAU,GAAWE,EAAS8B,EAAMC,MAAA,CAAOxG,KAAK,EAChDsD,SAAAA,EACAE,KAAAA,EAECb,SAAA,CAAA3C,KAAU,IAAVA,EAAsBoB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC,UAAOpB,MAAM,KAAQ,KAC5CiF,MAAMC,IAAA,CAAKL,GAAgB,EAbvBG,GAeL,OACN,EAGN,CAEAvC,CAAAA,EAAO9C,WAAA,CAAcgC,EAMrB,IAAM8E,EAAe,gBAMfC,EAAsB9C,EAAAA,UAAA,CAC1B,CAAC3C,EAAwCC,KACvC,GAAM,CAAEwB,cAAAA,CAAA,CAAeY,SAAAA,EAAW,GAAO,GAAGqD,EAAa,CAAI1F,EACvDwC,EAAcrB,EAAeM,GAC7BkE,EAAUtE,EAAiBmE,EAAc/D,GACzCmE,EAAaD,EAAQtD,QAAA,EAAYA,EACjCwD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAc0F,EAAQnB,eAAe,EACpEuB,EAAWnF,EAAca,GACzBuE,EAAuBrD,EAAAA,MAAA,CAA0C,SAEjE,CAACsD,EAAWC,EAAuBC,EAAc,CAAIC,GAAmB,IAC5E,IAAMC,EAAeN,IAAWO,MAAA,CAAO,GAAU,CAACC,EAAKlE,QAAQ,EACzDmE,EAAcH,EAAaI,IAAA,CAAK,GAAUF,EAAKxH,KAAA,GAAU4G,EAAQ5G,KAAK,EACtE2H,EAAWC,GAAaN,EAAcO,EAAQJ,EACnC,UAAbE,GACFf,EAAQ1D,aAAA,CAAcyE,EAAS3H,KAAK,CAExC,GAEM8H,EAAa,IACZjB,IACHD,EAAQ7D,YAAA,CAAa,IAErBqE,KAGEW,GACFnB,CAAAA,EAAQlC,wBAAA,CAAyBsD,OAAA,CAAU,CACzCC,EAAG9H,KAAK+H,KAAA,CAAMH,EAAaI,KAAK,EAChCC,EAAGjI,KAAK+H,KAAA,CAAMH,EAAaM,KAAK,CAClC,EAEJ,EAEA,MACEjH,CAAAA,EAAAA,EAAAA,GAAAA,EAAiBkE,EAAAA,EAAA,CAAhB,CAAuBgD,QAAO,GAAE,GAAG7E,CAAA,CAClCd,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACC,MAAA,CAAV,CACCC,KAAK,SACLC,KAAK,WACL,gBAAe9B,EAAQhB,SAAA,CACvB,gBAAegB,EAAQhE,IAAA,CACvB,gBAAegE,EAAQrD,QAAA,CACvB,oBAAkB,OAClBJ,IAAKyD,EAAQzD,GAAA,CACb,aAAYyD,EAAQhE,IAAA,CAAO,OAAS,SACpCU,SAAUuD,EACV,gBAAeA,EAAa,GAAK,OACjC,mBAAkB8B,GAAsB/B,EAAQ5G,KAAK,EAAI,GAAK,OAC7D,GAAG2G,CAAA,CACJjI,IAAKoI,EAEL8B,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBlC,EAAaiC,OAAA,CAAS,IAMlDrC,EAAMuC,aAAA,CAAcC,KAAA,GAGW,UAA3B9B,EAAee,OAAA,EACjBF,EAAWvB,EAEf,GACAyC,cAAeH,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBlC,EAAaqC,aAAA,CAAe,IAC9D/B,EAAee,OAAA,CAAUzB,EAAM0C,WAAA,CAI/B,IAAMzC,EAASD,EAAMC,MAAA,CACjBA,EAAO0C,iBAAA,CAAkB3C,EAAM4C,SAAS,GAC1C3C,EAAO4C,qBAAA,CAAsB7C,EAAM4C,SAAS,EAMzB,IAAjB5C,EAAMiC,MAAA,EAAgBjC,CAAkB,IAAlBA,EAAM8C,OAAA,EAAqB9C,UAAAA,EAAM0C,WAAA,GACzDnB,EAAWvB,GAEXA,EAAM+C,cAAA,GAEV,GACAC,UAAWV,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBlC,EAAa4C,SAAA,CAAW,IACtD,IAAMC,EAAgBtC,KAAAA,EAAUc,OAAA,CACVzB,EAAM8C,OAAA,EAAW9C,EAAMkD,MAAA,EAAUlD,EAAMmD,OAAA,EACvCnD,IAAAA,EAAMzG,GAAA,CAAI6J,MAAA,EAAcxC,EAAsBZ,EAAMzG,GAAG,EACzE0J,CAAAA,CAAAA,GAAiBjD,MAAAA,EAAMzG,GAAA,GACvB2B,EAAUmI,QAAA,CAASrD,EAAMzG,GAAG,IAC9BgI,IACAvB,EAAM+C,cAAA,GAEV,EAAC,EACH,EAGN,EAGF5C,CAAAA,EAAc/G,WAAA,CAAc8G,EAM5B,IAAMoD,EAAa,cAQbC,EAAoBlG,EAAAA,UAAA,CACxB,CAAC3C,EAAsCC,KAErC,GAAM,CAAEwB,cAAAA,CAAA,CAAeqH,UAAAA,CAAA,CAAWvI,MAAAA,CAAA,CAAOmB,SAAAA,CAAA,CAAUqH,YAAAA,EAAc,GAAI,GAAGC,EAAW,CAAIhJ,EACjF2F,EAAUtE,EAAiBuH,EAAYnH,GACvC,CAAEiD,6BAAAA,CAAA,CAA6B,CAAIiB,EACnCsD,EAAcvH,KAAa,IAAbA,EACdmE,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAc0F,EAAQlB,iBAAiB,EAM5E,MAJAyE,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,KACdxE,EAA6BuE,EAC/B,EAAG,CAACvE,EAA8BuE,EAAY,EAG5C9I,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAAChH,IAAA,CAAV,CACE,GAAG0I,CAAA,CACJvL,IAAKoI,EAGLtF,MAAO,CAAE4I,cAAe,MAAO,EAE9BzH,SAAAgG,GAAsB/B,EAAQ5G,KAAK,EAAIoB,CAAAA,EAAAA,EAAAA,GAAAA,EAAAjE,EAAAA,QAAAA,CAAA,CAAGwF,SAAAqH,CAAA,GAAkBrH,CAAA,EAGnE,EAGFmH,CAAAA,EAAYnK,WAAA,CAAckK,EAW1B,IAAMQ,EAAmBzG,EAAAA,UAAA,CACvB,CAAC3C,EAAqCC,KACpC,GAAM,CAAEwB,cAAAA,CAAA,CAAeC,SAAAA,CAAA,CAAU,GAAG2H,EAAU,CAAIrJ,EAClD,MACEG,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAAChH,IAAA,CAAV,CAAe,cAAW,GAAE,GAAG+I,CAAA,CAAW5L,IAAKwC,EAC7CyB,SAAAA,GAAY,KAGnB,EAGF0H,CAAAA,EAAW1K,WAAA,CAhBO,aAiClB,IAAM4K,EAA4C,GACzCnJ,CAAAA,EAAAA,EAAAA,GAAAA,EAACoJ,EAAAA,CAAeA,CAAf,CAAgBlC,QAAO,GAAE,GAAGrH,CAAA,EAGtCsJ,CAAAA,EAAa5K,WAAA,CAfO,eAqBpB,IAAM8K,EAAe,gBAKfC,EAAsB9G,EAAAA,UAAA,CAC1B,CAAC3C,EAAwCC,KACvC,IAAM0F,EAAUtE,EAAiBmI,EAAcxJ,EAAMyB,aAAa,EAC5D,CAACiI,EAAUC,EAAW,CAAUhH,EAAAA,QAAA,SAOtC,CAJAuG,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,KACdS,EAAY,IAAIC,iBAClB,EAAG,EAAE,EAEAjE,EAAQhE,IAAA,EAcNxB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC0J,EAAA,CAAmB,GAAG7J,CAAA,CAAOvC,IAAKwC,CAAA,GAZjC6J,EACMC,EAAAA,YAAA,CACP5J,CAAAA,EAAAA,EAAAA,GAAAA,EAAC6J,EAAA,CAAsBzF,MAAOvE,EAAMyB,aAAA,CAClCC,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACQ,EAAWsJ,IAAA,CAAX,CAAgB1F,MAAOvE,EAAMyB,aAAA,CAC5BC,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC,OAAKuB,SAAA1B,EAAM0B,QAAA,EAAS,EACvB,GANKgI,GAUT,IAIR,EAGFD,CAAAA,EAAc/K,WAAA,CAAc8K,EA2B5B,GAAM,CAACQ,EAAuBE,EAAuB,CACnDnJ,EAA+CyI,GAgC3CS,EAAOE,CAAAA,EAAAA,EAAAA,EAAAA,EAAW,8BAElBN,EAA0BlH,EAAAA,UAAA,CAC9B,CAAC3C,EAA4CC,KAC3C,GAAM,CACJwB,cAAAA,CAAA,CACAnC,SAAAA,EAAW,eACX8K,iBAAAA,CAAA,CACAC,gBAAAA,CAAA,CACAC,qBAAAA,CAAA,CAGAC,KAAAA,CAAA,CACAC,WAAAA,CAAA,CACAC,MAAAA,CAAA,CACAC,YAAAA,CAAA,CACAC,aAAAA,CAAA,CACAC,kBAAAA,CAAA,CACAC,iBAAAA,CAAA,CACAC,OAAAA,CAAA,CACAC,iBAAAA,CAAA,CACAC,gBAAAA,CAAA,CAEA,GAAGC,EACL,CAAIjL,EACE2F,EAAUtE,EAAiBmI,EAAc/H,GACzC,CAACyJ,EAASC,EAAU,CAAUxI,EAAAA,QAAA,CAA0C,MACxE,CAACyI,EAAUC,EAAW,CAAU1I,EAAAA,QAAA,CAAuC,MACvEkD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAc,GAAUkL,EAAWG,IAClE,CAACC,EAAcC,EAAe,CAAU7I,EAAAA,QAAA,CAAmC,MAC3E,CAAC8I,EAAkBC,EAAmB,CAAU/I,EAAAA,QAAA,CACpD,MAEIoD,EAAWnF,EAAca,GACzB,CAACkK,EAAcC,EAAe,CAAUjJ,EAAAA,QAAA,CAAS,IACjDkJ,EAA+BlJ,EAAAA,MAAA,CAAO,IAGtCA,EAAAA,SAAA,CAAU,KACd,GAAIuI,EAAS,MAAOY,CAAAA,EAAAA,EAAAA,EAAAA,EAAWZ,EACjC,EAAG,CAACA,EAAQ,EAIZa,CAAAA,EAAAA,EAAAA,EAAAA,IAEA,IAAMC,EAAmBrJ,EAAAA,WAAA,CACvB,IACE,GAAM,CAACsJ,EAAW,GAAGC,EAAS,CAAInG,IAAW7B,GAAA,CAAI,GAAUqC,EAAK9I,GAAA,CAAIsJ,OAAO,EACrE,CAACoF,EAAQ,CAAID,EAAUE,KAAA,CAAM,IAE7BC,EAA6BC,SAASC,aAAA,CAC5C,QAAWC,KAAaC,EAEtB,GAAID,IAAcH,IAClBG,GAAWE,eAAe,CAAEC,MAAO,SAAU,GAEzCH,IAAcP,GAAab,GAAUA,CAAAA,EAASwB,SAAA,CAAY,GAC1DJ,IAAcL,GAAYf,GAAUA,CAAAA,EAASwB,SAAA,CAAYxB,EAASyB,YAAA,EACtEL,GAAW1E,QACPwE,SAASC,aAAA,GAAkBF,GANe,MAQlD,EACA,CAACtG,EAAUqF,EAAQ,EAGf0B,EAA0BnK,EAAAA,WAAA,CAC9B,IAAMqJ,EAAW,CAACT,EAAcL,EAAQ,EACxC,CAACc,EAAYT,EAAcL,EAAO,EAK9BvI,EAAAA,SAAA,CAAU,KACVgJ,GACFmB,GAEJ,EAAG,CAACnB,EAAcmB,EAAkB,EAIpC,GAAM,CAAEhL,aAAAA,CAAA,CAAc2B,yBAAAA,CAAA,CAAyB,CAAIkC,EAC7ChD,EAAAA,SAAA,CAAU,KACd,GAAIuI,EAAS,CACX,IAAI6B,EAAmB,CAAE/F,EAAG,EAAGG,EAAG,CAAE,EAE9B6F,EAAoB,IACxBD,EAAmB,CACjB/F,EAAG9H,KAAK+N,GAAA,CAAI/N,KAAK+H,KAAA,CAAM3B,EAAM4B,KAAK,EAAKzD,CAAAA,EAAyBsD,OAAA,EAASC,GAAK,IAC9EG,EAAGjI,KAAK+N,GAAA,CAAI/N,KAAK+H,KAAA,CAAM3B,EAAM8B,KAAK,EAAK3D,CAAAA,EAAyBsD,OAAA,EAASI,GAAK,GAChF,CACF,EACM+F,EAAkB,IAElBH,EAAiB/F,CAAA,EAAK,IAAM+F,EAAiB5F,CAAA,EAAK,GACpD7B,EAAM+C,cAAA,GAGD6C,EAAQiC,QAAA,CAAS7H,EAAMC,MAAqB,GAC/CzD,EAAa,IAGjBwK,SAASc,mBAAA,CAAoB,cAAeJ,GAC5CvJ,EAAyBsD,OAAA,CAAU,IACrC,EAOA,OALyC,OAArCtD,EAAyBsD,OAAA,GAC3BuF,SAASe,gBAAA,CAAiB,cAAeL,GACzCV,SAASe,gBAAA,CAAiB,YAAaH,EAAiB,CAAEI,QAAS,GAAMC,KAAM,EAAK,IAG/E,KACLjB,SAASc,mBAAA,CAAoB,cAAeJ,GAC5CV,SAASc,mBAAA,CAAoB,YAAaF,EAAiB,CAAEI,QAAS,EAAK,EAC7E,CACF,CACF,EAAG,CAACpC,EAASpJ,EAAc2B,EAAyB,EAE9Cd,EAAAA,SAAA,CAAU,KACd,IAAM6K,EAAQ,IAAM1L,EAAa,IAGjC,OAFA2L,OAAOJ,gBAAA,CAAiB,OAAQG,GAChCC,OAAOJ,gBAAA,CAAiB,SAAUG,GAC3B,KACLC,OAAOL,mBAAA,CAAoB,OAAQI,GACnCC,OAAOL,mBAAA,CAAoB,SAAUI,EACvC,CACF,EAAG,CAAC1L,EAAa,EAEjB,GAAM,CAACmE,EAAWC,EAAqB,CAAIE,GAAmB,IAC5D,IAAMC,EAAeN,IAAWO,MAAA,CAAO,GAAU,CAACC,EAAKlE,QAAQ,EACzDmE,EAAcH,EAAaI,IAAA,CAAK,GAAUF,EAAK9I,GAAA,CAAIsJ,OAAA,GAAYuF,SAASC,aAAa,EACrF7F,EAAWC,GAAaN,EAAcO,EAAQJ,GAChDE,GAKFgH,WAAW,IAAOhH,EAASjJ,GAAA,CAAIsJ,OAAA,CAAwBe,KAAA,GAE3D,GAEM6F,EAAwBhL,EAAAA,WAAA,CAC5B,CAAC2I,EAAgCvM,EAAesD,KAC9C,IAAMuL,EAAmB,CAAC/B,EAAuB9E,OAAA,EAAW,CAAC1E,EAEzDwL,CAAAA,KADqC,IAAlBlI,EAAQ5G,KAAA,EAAuB4G,EAAQ5G,KAAA,GAAUA,GAClD6O,CAAAA,IACpBpC,EAAgBF,GACZsC,GAAkB/B,CAAAA,EAAuB9E,OAAA,CAAU,IAE3D,EACA,CAACpB,EAAQ5G,KAAK,GAEV+O,GAAwBnL,EAAAA,WAAA,CAAY,IAAMuI,GAASpD,QAAS,CAACoD,EAAQ,EACrE6C,GAA4BpL,EAAAA,WAAA,CAChC,CAAC2I,EAAoCvM,EAAesD,KAClD,IAAMuL,EAAmB,CAAC/B,EAAuB9E,OAAA,EAAW,CAAC1E,EAEzDwL,CAAAA,KADqC,IAAlBlI,EAAQ5G,KAAA,EAAuB4G,EAAQ5G,KAAA,GAAUA,GAClD6O,CAAAA,GACpBlC,EAAoBJ,EAExB,EACA,CAAC3F,EAAQ5G,KAAK,GAGViP,GAAiB1O,WAAAA,EAAwB2O,GAAuBC,EAGhEC,GACJH,KAAmBC,GACf,CACE1D,KAAAA,EACAC,WAAAA,EACAC,MAAAA,EACAC,YAAAA,EACAC,aAAAA,EACAC,kBAAAA,EACAC,iBAAAA,EACAC,OAAAA,EACAC,iBAAAA,EACAC,gBAAAA,CACF,EACA,CAAC,EAEP,MACE7K,CAAAA,EAAAA,EAAAA,GAAAA,EAAC6J,EAAA,CACCzF,MAAO9C,EACPyJ,QAAAA,EACAE,SAAAA,EACAgD,iBAAkB/C,EAClBsC,gBAAAA,EACApC,aAAAA,EACA8C,YAAaP,GACbC,oBAAAA,GACAjB,kBAAAA,EACArB,iBAAAA,EACAnM,SAAAA,EACAqM,aAAAA,EACA1F,UAAAA,EAEAvE,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmO,EAAAA,CAAYA,CAAZ,CAAaC,GAAItE,EAAMuE,eAAc,GACpC9M,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACsO,EAAAA,CAAUA,CAAV,CACCpH,QAAO,GAGPqH,QAAS/I,EAAQhE,IAAA,CACjBgN,iBAAkB,IAEhBrJ,EAAM+C,cAAA,EACR,EACAuG,mBAAoBhH,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBwC,EAAkB,IACzDzE,EAAQlD,OAAA,EAASqF,MAAM,CAAE+G,cAAe,EAAK,GAC7CvJ,EAAM+C,cAAA,EACR,GAEA3G,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC2O,EAAAA,EAAgBA,CAAhB,CACCzH,QAAO,GACP0H,4BAA2B,GAC3B1E,gBAAAA,EACAC,qBAAAA,EAGA0E,eAAgB,GAAW1J,EAAM+C,cAAA,GACjC4G,UAAW,IAAMtJ,EAAQ7D,YAAA,CAAa,IAEtCJ,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC6N,GAAA,CACCvG,KAAK,UACLyH,GAAIvJ,EAAQhB,SAAA,CACZ,aAAYgB,EAAQhE,IAAA,CAAO,OAAS,SACpCO,IAAKyD,EAAQzD,GAAA,CACbiN,cAAe,GAAW7J,EAAM+C,cAAA,GAC/B,GAAG4C,CAAA,CACH,GAAGkD,EAAA,CACJiB,SAAU,IAAMxD,EAAgB,IAChCnO,IAAKoI,EACLtF,MAAO,CAEL8O,QAAS,OACTC,cAAe,SAEfC,QAAS,OACT,GAAGtE,EAAa1K,KAAA,EAElB+H,UAAWV,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBqD,EAAa3C,SAAA,CAAW,IACtD,IAAMkH,EAAgBlK,EAAM8C,OAAA,EAAW9C,EAAMkD,MAAA,EAAUlD,EAAMmD,OAAA,CAO7D,GAJkB,QAAdnD,EAAMzG,GAAA,EAAeyG,EAAM+C,cAAA,GAE1BmH,GAAiBlK,IAAAA,EAAMzG,GAAA,CAAI6J,MAAA,EAAcxC,EAAsBZ,EAAMzG,GAAG,EAEzE,CAAC,UAAW,YAAa,OAAQ,MAAK,CAAE8J,QAAA,CAASrD,EAAMzG,GAAG,EAAG,CAE/D,IAAI4Q,EAAiBC,IADIpJ,MAAA,CAAO,GAAU,CAACC,EAAKlE,QAAQ,EAC7B6B,GAAA,CAAI,GAAUqC,EAAK9I,GAAA,CAAIsJ,OAAQ,EAK1D,GAHI,CAAC,UAAW,MAAK,CAAE4B,QAAA,CAASrD,EAAMzG,GAAG,GACvC4Q,CAAAA,EAAiBA,EAAerD,KAAA,GAAQuD,OAAA,IAEtC,CAAC,UAAW,YAAW,CAAEhH,QAAA,CAASrD,EAAMzG,GAAG,EAAG,CAChD,IAAM+Q,EAAiBtK,EAAMC,MAAA,CACvBsK,EAAeJ,EAAeK,OAAA,CAAQF,GAC5CH,EAAiBA,EAAerD,KAAA,CAAMyD,EAAe,EACvD,CAMAnC,WAAW,IAAM1B,EAAWyD,IAE5BnK,EAAM+C,cAAA,EACR,CACF,EAAC,EACH,EACF,EACF,EACF,EAGN,EAGFwB,CAAAA,EAAkBnL,WAAA,CAvTQ,oBAkU1B,IAAMwP,EAAkCvL,EAAAA,UAAA,CAGtC,CAAC3C,EAAoDC,KACrD,GAAM,CAAEwB,cAAAA,CAAA,CAAe2N,SAAAA,CAAA,CAAU,GAAGW,EAAY,CAAI/P,EAC9C2F,EAAUtE,EAAiBmI,EAAc/H,GACzCuO,EAAiB9F,EAAwBV,EAAc/H,GACvD,CAACwO,EAAgBC,EAAiB,CAAUvN,EAAAA,QAAA,CAAgC,MAC5E,CAACuI,EAASC,EAAU,CAAUxI,EAAAA,QAAA,CAAkD,MAChFkD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAc,GAAUkL,EAAWG,IAClEvF,EAAWnF,EAAca,GACzB0O,EAAgCxN,EAAAA,MAAA,CAAO,IACvCyN,EAA4BzN,EAAAA,MAAA,CAAO,IAEnC,CAAEyI,SAAAA,CAAA,CAAUG,aAAAA,CAAA,CAAcE,iBAAAA,CAAA,CAAkBqB,kBAAAA,CAAA,CAAkB,CAAIkD,EAClE1Q,EAAiBqD,EAAAA,WAAA,CAAY,KACjC,GACEgD,EAAQlD,OAAA,EACRkD,EAAQ/C,SAAA,EACRqN,GACA/E,GACAE,GACAG,GACAE,EACA,CACA,IAAM4E,EAAc1K,EAAQlD,OAAA,CAAQ6N,qBAAA,GAK9BC,EAAcrF,EAAQoF,qBAAA,GACtBE,EAAgB7K,EAAQ/C,SAAA,CAAU0N,qBAAA,GAClCG,EAAehF,EAAiB6E,qBAAA,GAEtC,GAAI3K,QAAAA,EAAQzD,GAAA,CAAe,CACzB,IAAMwO,EAAiBD,EAAaE,IAAA,CAAOJ,EAAYI,IAAA,CACjDA,EAAOH,EAAcG,IAAA,CAAOD,EAC5BE,EAAYP,EAAYM,IAAA,CAAOA,EAC/BE,EAAkBR,EAAYrS,KAAA,CAAQ4S,EACtCE,EAAe5R,KAAKD,GAAA,CAAI4R,EAAiBN,EAAYvS,KAAK,EAE1D+S,EAAcjS,EAAM6R,EAAM,CAnYjB,GA0YbzR,KAAKD,GAAA,CA1YQ,GA0YY+R,OARFC,UAAA,CAlYV,GA0YwBH,GACtC,CAEDb,CAAAA,EAAe1P,KAAA,CAAM2Q,QAAA,CAAWL,EAAkB,KAClDZ,EAAe1P,KAAA,CAAMoQ,IAAA,CAAOI,EAAc,IAC5C,KAAO,CACL,IAAML,EAAiBH,EAAYY,KAAA,CAAQV,EAAaU,KAAA,CAClDA,EAAQ1D,OAAOwD,UAAA,CAAaT,EAAcW,KAAA,CAAQT,EAClDU,EAAa3D,OAAOwD,UAAA,CAAaZ,EAAYc,KAAA,CAAQA,EACrDN,EAAkBR,EAAYrS,KAAA,CAAQoT,EACtCN,EAAe5R,KAAKD,GAAA,CAAI4R,EAAiBN,EAAYvS,KAAK,EAE1DqT,EAAevS,EAAMqS,EAAO,CAtZnB,GAwZbjS,KAAKD,GAAA,CAxZQ,GAwZYqS,OAHHL,UAAA,CArZT,GAwZuBH,GACrC,CAEDb,CAAAA,EAAe1P,KAAA,CAAM2Q,QAAA,CAAWL,EAAkB,KAClDZ,EAAe1P,KAAA,CAAM4Q,KAAA,CAAQE,EAAe,IAC9C,CAKA,IAAM3B,EAAQ3J,IACRwL,EAAkB9D,OAAO+D,WAAA,CAAcC,GACvCC,EAActG,EAASyB,YAAA,CAEvB8E,EAAgBlE,OAAOmE,gBAAA,CAAiB1G,GACxC2G,EAAwBC,SAASH,EAAcI,cAAA,CAAgB,IAC/DC,EAAoBF,SAASH,EAAcM,UAAA,CAAY,IACvDC,EAA2BJ,SAASH,EAAcQ,iBAAA,CAAmB,IAErEC,EAAoBP,EAAwBG,EAAoBN,EADzCI,SAASH,EAAcU,aAAA,CAAe,IACwCH,EACrGI,EAAmBpT,KAAKF,GAAA,CAAIuM,EAAAA,EAAagH,YAAA,CAAkBH,GAE3DI,EAAiB/E,OAAOmE,gBAAA,CAAiBxG,GACzCqH,EAAqBX,SAASU,EAAeP,UAAA,CAAY,IACzDS,EAAwBZ,SAASU,EAAeH,aAAA,CAAe,IAE/DM,EAAyBtC,EAAYuC,GAAA,CAAMvC,EAAYpS,MAAA,CAAS,EAlbrD,GAqbX4U,EAAyBtH,EAAagH,YAAA,CAAe,EAErDO,EAAyBjB,EAAwBG,EAD9BzG,CAAAA,EAAawH,SAAA,CAAYF,CAAAA,EAMlD,GAFoCC,GAA0BH,EAE7B,CAC/B,IAAMK,EACJtD,EAAMhH,MAAA,CAAS,GAAK6C,IAAiBmE,CAAA,CAAMA,EAAMhH,MAAA,CAAS,EAAC,CAAGjL,GAAA,CAAIsJ,OAAA,CACpEkJ,EAAe1P,KAAA,CAAM0S,MAAA,CAAS,MAC9B,IAAMC,EACJhI,EAAQiI,YAAA,CAAe/H,EAAS2H,SAAA,CAAY3H,EAASmH,YAAA,CAUvDtC,EAAe1P,KAAA,CAAMtC,MAAA,CAASA,EATWiB,KAAKD,GAAA,CAfdsS,EAAkBoB,EAiBhDE,EAEGG,CAAAA,EAAaN,EAAwB,GACtCQ,EACAhB,GAGmC,IACzC,KAAO,CACL,IAAMkB,EAAc1D,EAAMhH,MAAA,CAAS,GAAK6C,IAAiBmE,CAAA,CAAM,EAAC,CAAGjS,GAAA,CAAIsJ,OAAA,CACvEkJ,EAAe1P,KAAA,CAAMqS,GAAA,CAAM,MAC3B,IAAMS,EAAgCnU,KAAKD,GAAA,CACzC0T,EACAd,EACEzG,EAAS2H,SAAA,CAERK,CAAAA,EAAcX,EAAqB,GACpCI,EAGJ5C,CAAAA,EAAe1P,KAAA,CAAMtC,MAAA,CAASA,EAhCEmU,CAAAA,EAAoBU,CAAAA,EAgCb,KACvC1H,EAASwB,SAAA,CAAYkG,EAAyBH,EAAyBvH,EAAS2H,SAAA,CAGlF9C,EAAe1P,KAAA,CAAMd,MAAA,CAAS,SAC9BwQ,EAAe1P,KAAA,CAAM+S,SAAA,CAAYhB,EAAmB,KACpDrC,EAAe1P,KAAA,CAAMgT,SAAA,CAAYhC,EAAkB,KAGnDnC,MAIAoE,sBAAsB,IAAOrD,EAAwBpJ,OAAA,CAAU,GACjE,CACF,EAAG,CACDhB,EACAJ,EAAQlD,OAAA,CACRkD,EAAQ/C,SAAA,CACRqN,EACA/E,EACAE,EACAG,EACAE,EACA9F,EAAQzD,GAAA,CACRkN,EACD,EAEDlG,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,IAAM5J,IAAY,CAACA,EAAS,EAG5C,GAAM,CAACmU,EAAeC,EAAgB,CAAU/Q,EAAAA,QAAA,GAChDuG,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,KACVgC,GAASwI,EAAiBjG,OAAOmE,gBAAA,CAAiB1G,GAASyI,MAAM,CACvE,EAAG,CAACzI,EAAQ,EAMZ,IAAM0I,EAAiCjR,EAAAA,WAAA,CACrC,IACM2I,GAAQ8E,CAAgC,IAAhCA,EAAoBrJ,OAAA,GAC9BzH,IACAwN,MACAsD,EAAoBrJ,OAAA,CAAU,GAElC,EACA,CAACzH,EAAUwN,EAAiB,EAG9B,MACE3M,CAAAA,EAAAA,EAAAA,GAAAA,EAAC0T,GAAA,CACCtP,MAAO9C,EACPwO,eAAAA,EACAE,wBAAAA,EACA2D,qBAAsBF,EAEtBlS,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC,OACC1C,IAAKyS,EACL3P,MAAO,CACL8O,QAAS,OACTC,cAAe,SACfhQ,SAAU,QACVqU,OAAQF,CACV,EAEA/R,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACyM,GAAA,CAAV,CACE,GAAGhE,CAAA,CACJtS,IAAKoI,EACLtF,MAAO,CAGLyT,UAAW,aAEXT,UAAW,OACX,GAAGxD,EAAYxP,KAAA,CACjB,EACF,EACF,EAGN,EAEA2N,CAAAA,EAA0BxP,WAAA,CAvNS,4BAmOnC,IAAMuP,GAA6BtL,EAAAA,UAAA,CAGjC,CAAC3C,EAA+CC,KAChD,GAAM,CACJwB,cAAAA,CAAA,CACAgJ,MAAAA,EAAQ,QACRI,iBAAAA,EA/jBmB,EA+jBA,CACnB,GAAGkF,EACL,CAAI/P,EACEwC,EAAcrB,EAAeM,GAEnC,MACEtB,CAAAA,EAAAA,EAAAA,GAAAA,EAAiBkE,EAAAA,EAAA,CAAhB,CACE,GAAG7B,CAAA,CACH,GAAGuN,CAAA,CACJtS,IAAKwC,EACLwK,MAAAA,EACAI,iBAAAA,EACAtK,MAAO,CAELyT,UAAW,aACX,GAAGjE,EAAYxP,KAAA,CAGb,0CAA2C,uCAC3C,yCAA0C,sCAC1C,0CAA2C,uCAC3C,+BAAgC,mCAChC,gCAAiC,mCAErC,GAGN,EAEA0N,CAAAA,GAAqBvP,WAAA,CA1CQ,uBAsD7B,GAAM,CAACmV,GAAwBI,GAAwB,CACrDlT,EAAgDyI,EAAc,CAAC,GAE3D0K,GAAgB,iBAQhBC,GAAuBxR,EAAAA,UAAA,CAC3B,CAAC3C,EAAyCC,KACxC,GAAM,CAAEwB,cAAAA,CAAA,CAAe2S,MAAAA,CAAA,CAAO,GAAGC,EAAc,CAAIrU,EAC7CgQ,EAAiB9F,EAAwBgK,GAAezS,GACxD6S,EAAkBL,GAAyBC,GAAezS,GAC1DoE,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAc+P,EAAe5B,gBAAgB,EAC5EmG,EAAyB5R,EAAAA,MAAA,CAAO,GACtC,MACE2B,CAAAA,EAAAA,EAAAA,IAAAA,EAAApI,EAAAA,QAAAA,CAAA,CAEEwF,SAAA,CAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC,SACCqU,wBAAyB,CACvBC,OAAQ,2KACV,EACAL,MAAAA,CAAA,GAEFjU,CAAAA,EAAAA,EAAAA,GAAAA,EAACQ,EAAWsJ,IAAA,CAAX,CAAgB1F,MAAO9C,EACtBC,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACyM,GAAA,CAAV,CACC,6BAA2B,GAC3BtM,KAAK,eACJ,GAAG4M,CAAA,CACJ5W,IAAKoI,EACLtF,MAAO,CAILjB,SAAU,WACVoV,KAAM,EAKNhV,SAAU,cACV,GAAG2U,EAAc9T,KAAA,EAEnBoU,SAAU/M,CAAAA,EAAAA,EAAAA,CAAAA,EAAqByM,EAAcM,QAAA,CAAU,IACrD,IAAMvJ,EAAW9F,EAAMuC,aAAA,CACjB,CAAEoI,eAAAA,CAAA,CAAgBE,wBAAAA,CAAA,CAAwB,CAAImE,EACpD,GAAInE,GAAyBpJ,SAAWkJ,EAAgB,CACtD,IAAM2E,EAAa1V,KAAK+N,GAAA,CAAIsH,EAAiBxN,OAAA,CAAUqE,EAASwB,SAAS,EACzE,GAAIgI,EAAa,EAAG,CAClB,IAAMrD,EAAkB9D,OAAO+D,WAAA,CAAcC,GAGvCoD,EAAa3V,KAAKD,GAAA,CAFH6V,WAAW7E,EAAe1P,KAAA,CAAM+S,SAAS,EAC5CwB,WAAW7E,EAAe1P,KAAA,CAAMtC,MAAM,GAGxD,GAAI4W,EAAatD,EAAiB,CAChC,IAAMwD,EAAaF,EAAaD,EAC1BI,EAAoB9V,KAAKF,GAAA,CAAIuS,EAAiBwD,GAC9CE,EAAaF,EAAaC,CAEhC/E,CAAAA,EAAe1P,KAAA,CAAMtC,MAAA,CAAS+W,EAAoB,KACd,QAAhC/E,EAAe1P,KAAA,CAAM0S,MAAA,GACvB7H,EAASwB,SAAA,CAAYqI,EAAa,EAAIA,EAAa,EAEnDhF,EAAe1P,KAAA,CAAM2U,cAAA,CAAiB,WAE1C,CACF,CACF,CACAX,EAAiBxN,OAAA,CAAUqE,EAASwB,SAAA,EACrC,EACH,GACF,EAGN,EAGFuH,CAAAA,GAAezV,WAAA,CAAcwV,GAM7B,IAAMiB,GAAa,cAIb,CAACC,GAA4BC,GAAqB,CACtDtU,EAA6CoU,IAKzCG,GAAoB3S,EAAAA,UAAA,CACxB,CAAC3C,EAAsCC,KACrC,GAAM,CAAEwB,cAAAA,CAAA,CAAe,GAAG8T,EAAW,CAAIvV,EACnCwV,EAAU5Q,CAAAA,EAAAA,EAAAA,CAAAA,IAChB,MACEzE,CAAAA,EAAAA,EAAAA,GAAAA,EAACiV,GAAA,CAA2B7Q,MAAO9C,EAAeyN,GAAIsG,EACpD9T,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACyM,GAAA,CAAV,CAActM,KAAK,QAAQ,kBAAiB+N,EAAU,GAAGD,CAAA,CAAY9X,IAAKwC,CAAA,EAAc,EAG/F,EAGFqV,CAAAA,GAAY5W,WAAA,CAAcyW,GAM1B,IAAMM,GAAa,cAKbC,GAAoB/S,EAAAA,UAAA,CACxB,CAAC3C,EAAsCC,KACrC,GAAM,CAAEwB,cAAAA,CAAA,CAAe,GAAGkU,EAAW,CAAI3V,EACnC4V,EAAeP,GAAsBI,GAAYhU,GACvD,MAAOtB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACyM,GAAA,CAAV,CAAc7E,GAAI0G,EAAa1G,EAAA,CAAK,GAAGyG,CAAA,CAAYlY,IAAKwC,CAAA,EAClE,EAGFyV,CAAAA,GAAYhX,WAAA,CAAc+W,GAM1B,IAAMI,GAAY,aAUZ,CAACC,GAA2BC,GAAoB,CACpDhV,EAA4C8U,IASxCG,GAAmBrT,EAAAA,UAAA,CACvB,CAAC3C,EAAqCC,KACpC,GAAM,CACJwB,cAAAA,CAAA,CACA1C,MAAAA,CAAA,CACAsD,SAAAA,EAAW,GACX4T,UAAWC,CAAA,CACX,GAAGC,EACL,CAAInW,EACE2F,EAAUtE,EAAiBwU,GAAWpU,GACtCuO,EAAiB9F,EAAwB2L,GAAWpU,GACpD2U,EAAazQ,EAAQ5G,KAAA,GAAUA,EAC/B,CAACkX,EAAWI,EAAY,CAAU1T,EAAAA,QAAA,CAASuT,GAAiB,IAC5D,CAACI,EAAWC,EAAY,CAAU5T,EAAAA,QAAA,CAAS,IAC3CkD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAc,GACjD+P,EAAerC,eAAA,GAAkBrC,EAAMvM,EAAOsD,IAE1CmU,EAAS5R,CAAAA,EAAAA,EAAAA,CAAAA,IACToB,EAAuBrD,EAAAA,MAAA,CAA0C,SAEjE8T,EAAe,KACdpU,IACHsD,EAAQ1D,aAAA,CAAclD,GACtB4G,EAAQ7D,YAAA,CAAa,IAEzB,EAEA,GAAI/C,KAAAA,EACF,MAAM,MACJ,yLAIJ,MACEoB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC2V,GAAA,CACCvR,MAAO9C,EACP1C,MAAAA,EACAsD,SAAAA,EACAmU,OAAAA,EACAJ,WAAAA,EACAM,iBAAwB/T,EAAAA,WAAA,CAAY,IAClC0T,EAAa,GAAmBM,GAAA,CAAkBrL,GAAMsL,aAAe,IAAIC,IAAA,GAC7E,EAAG,EAAE,EAELnV,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACQ,EAAWmW,QAAA,CAAX,CACCvS,MAAO9C,EACP1C,MAAAA,EACAsD,SAAAA,EACA4T,UAAAA,EAEAvU,SAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACyM,GAAA,CAAV,CACCtM,KAAK,SACL,kBAAiB+O,EACjB,mBAAkBF,EAAY,GAAK,OAEnC,gBAAeF,GAAcE,EAC7B,aAAYF,EAAa,UAAY,YACrC,gBAAe/T,GAAY,OAC3B,gBAAeA,EAAW,GAAK,OAC/BgD,SAAUhD,EAAW,OAAY,GAChC,GAAG8T,CAAA,CACJ1Y,IAAKoI,EACLkR,QAASnP,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAUY,OAAA,CAAS,IAAMR,EAAa,KACpES,OAAQpP,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAUa,MAAA,CAAQ,IAAMT,EAAa,KAClE5O,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAUxO,OAAA,CAAS,KAEhB,UAA3B3B,EAAee,OAAA,EAAqB0P,GAC1C,GACAQ,YAAarP,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAUc,WAAA,CAAa,KAGxB,UAA3BjR,EAAee,OAAA,EAAqB0P,GAC1C,GACA1O,cAAeH,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAUpO,aAAA,CAAe,IAC3D/B,EAAee,OAAA,CAAUzB,EAAM0C,WAAA,GAEjCkP,cAAetP,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAUe,aAAA,CAAe,IAE3DlR,EAAee,OAAA,CAAUzB,EAAM0C,WAAA,CAC3B3F,EACF2N,EAAe3B,WAAA,KACqB,UAA3BrI,EAAee,OAAA,EAGxBzB,EAAMuC,aAAA,CAAcC,KAAA,CAAM,CAAE+G,cAAe,EAAK,EAEpD,GACAsI,eAAgBvP,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAUgB,cAAA,CAAgB,IACzD7R,EAAMuC,aAAA,GAAkByE,SAASC,aAAA,EACnCyD,EAAe3B,WAAA,IAEnB,GACA/F,UAAWV,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBuO,EAAU7N,SAAA,CAAW,IAC7B0H,EAAe/J,SAAA,EAAWc,UAAY,IACvCzB,MAAAA,EAAMzG,GAAA,GACvB4B,EAAekI,QAAA,CAASrD,EAAMzG,GAAG,GAAG4X,IAEtB,MAAdnR,EAAMzG,GAAA,EAAayG,EAAM+C,cAAA,GAC/B,EAAC,EACH,EACF,EAGN,EAGF2N,CAAAA,GAAWtX,WAAA,CAAcmX,GAMzB,IAAMuB,GAAiB,iBAKjBC,GAAuB1U,EAAAA,UAAA,CAC3B,CAAC3C,EAAyCC,KAExC,GAAM,CAAEwB,cAAAA,CAAA,CAAeqH,UAAAA,CAAA,CAAWvI,MAAAA,CAAA,CAAO,GAAG+W,EAAc,CAAItX,EACxD2F,EAAUtE,EAAiB+V,GAAgB3V,GAC3CuO,EAAiB9F,EAAwBkN,GAAgB3V,GACzD8V,EAAcxB,GAAqBqB,GAAgB3V,GACnD+V,EAAuBjW,EAA8B6V,GAAgB3V,GACrE,CAACgW,EAAcC,EAAe,CAAU/U,EAAAA,QAAA,CAAuC,MAC/EkD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EACnB7F,EACA,GAAUyX,EAAgBpM,GAC1BiM,EAAYb,gBAAA,CACZ,GAAU1G,EAAejC,mBAAA,GAAsBzC,EAAMiM,EAAYxY,KAAA,CAAOwY,EAAYlV,QAAQ,GAGxFuU,EAAca,GAAcb,YAC5Be,EAAqBhV,EAAAA,OAAA,CACzB,IACExC,CAAAA,EAAAA,EAAAA,GAAAA,EAAC,UAA+BpB,MAAOwY,EAAYxY,KAAA,CAAOsD,SAAUkV,EAAYlV,QAAA,CAC7EX,SAAAkV,CAAA,EADUW,EAAYxY,KAEzB,EAEF,CAACwY,EAAYlV,QAAA,CAAUkV,EAAYxY,KAAA,CAAO6X,EAAW,EAGjD,CAAE9R,kBAAAA,CAAA,CAAmBG,qBAAAA,CAAA,CAAqB,CAAIuS,EAMpD,MALAtO,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,KACdpE,EAAkB6S,GACX,IAAM1S,EAAqB0S,IACjC,CAAC7S,EAAmBG,EAAsB0S,EAAa,EAGxDrT,CAAAA,EAAAA,EAAAA,IAAAA,EAAApI,EAAAA,QAAAA,CAAA,CACEwF,SAAA,CAAAvB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAAChH,IAAA,CAAV,CAAe4O,GAAIqI,EAAYf,MAAA,CAAS,GAAGc,CAAA,CAAe7Z,IAAKoI,CAAA,GAG/D0R,EAAYnB,UAAA,EAAczQ,EAAQ/C,SAAA,EAAa,CAAC+C,EAAQ7C,oBAAA,CAC5CiH,EAAAA,YAAA,CAAauN,EAAc5V,QAAA,CAAUiE,EAAQ/C,SAAS,EAC/D,OAGV,EAGFyU,CAAAA,GAAe3Y,WAAA,CAAc0Y,GAM7B,IAAMQ,GAAsB,sBAKtBC,GAA4BlV,EAAAA,UAAA,CAChC,CAAC3C,EAA8CC,KAC7C,GAAM,CAAEwB,cAAAA,CAAA,CAAe,GAAGqW,EAAmB,CAAI9X,EAEjD,OAAOuX,GADkCK,GAAqBnW,GAC3C2U,UAAA,CACjBjW,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAAChH,IAAA,CAAV,CAAe,cAAW,GAAE,GAAGwX,CAAA,CAAoBra,IAAKwC,CAAA,GACvD,IACN,EAGF4X,CAAAA,GAAoBnZ,WAAA,CAAckZ,GAMlC,IAAMG,GAAwB,uBAKxBC,GAA6BrV,EAAAA,UAAA,CAGjC,CAAC3C,EAA+CC,KAChD,IAAM+P,EAAiB9F,EAAwB6N,GAAuB/X,EAAMyB,aAAa,EACnF6S,EAAkBL,GAAyB8D,GAAuB/X,EAAMyB,aAAa,EACrF,CAACwW,EAAaC,EAAc,CAAUvV,EAAAA,QAAA,CAAS,IAC/CkD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAcqU,EAAgBR,oBAAoB,EAevF,MAbA5K,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,KACd,GAAI8G,EAAe5E,QAAA,EAAY4E,EAAerE,YAAA,CAAc,CAE1D,IAASwM,EAAT,WAEED,EADoB9M,EAASwB,SAAA,CAAY,EAE3C,EAJMxB,EAAW4E,EAAe5E,QAAA,CAOhC,OAFA+M,IACA/M,EAASiC,gBAAA,CAAiB,SAAU8K,GAC7B,IAAM/M,EAASgC,mBAAA,CAAoB,SAAU+K,EACtD,CACF,EAAG,CAACnI,EAAe5E,QAAA,CAAU4E,EAAerE,YAAY,CAAC,EAElDsM,EACL9X,CAAAA,EAAAA,EAAAA,GAAAA,EAACiY,GAAA,CACE,GAAGpY,CAAA,CACJvC,IAAKoI,EACLwS,aAAc,KACZ,GAAM,CAAEjN,SAAAA,CAAA,CAAUG,aAAAA,CAAA,CAAa,CAAIyE,EAC/B5E,GAAYG,GACdH,CAAAA,EAASwB,SAAA,CAAYxB,EAASwB,SAAA,CAAYrB,EAAagH,YAAA,CAE3D,IAEA,IACN,EAEAyF,CAAAA,GAAqBtZ,WAAA,CAAcqZ,GAMnC,IAAMO,GAA0B,yBAK1BC,GAA+B5V,EAAAA,UAAA,CAGnC,CAAC3C,EAAiDC,KAClD,IAAM+P,EAAiB9F,EAAwBoO,GAAyBtY,EAAMyB,aAAa,EACrF6S,EAAkBL,GAAyBqE,GAAyBtY,EAAMyB,aAAa,EACvF,CAAC+W,EAAeC,EAAgB,CAAU9V,EAAAA,QAAA,CAAS,IACnDkD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAcqU,EAAgBR,oBAAoB,EAkBvF,MAhBA5K,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,KACd,GAAI8G,EAAe5E,QAAA,EAAY4E,EAAerE,YAAA,CAAc,CAE1D,IAASwM,EAAT,WACE,IAAMO,EAAYtN,EAASyB,YAAA,CAAezB,EAAS+H,YAAA,CAInDsF,EADsBvZ,KAAKyZ,IAAA,CAAKvN,EAASwB,SAAS,EAAI8L,EAExD,EAPMtN,EAAW4E,EAAe5E,QAAA,CAUhC,OAFA+M,IACA/M,EAASiC,gBAAA,CAAiB,SAAU8K,GAC7B,IAAM/M,EAASgC,mBAAA,CAAoB,SAAU+K,EACtD,CACF,EAAG,CAACnI,EAAe5E,QAAA,CAAU4E,EAAerE,YAAY,CAAC,EAElD6M,EACLrY,CAAAA,EAAAA,EAAAA,GAAAA,EAACiY,GAAA,CACE,GAAGpY,CAAA,CACJvC,IAAKoI,EACLwS,aAAc,KACZ,GAAM,CAAEjN,SAAAA,CAAA,CAAUG,aAAAA,CAAA,CAAa,CAAIyE,EAC/B5E,GAAYG,GACdH,CAAAA,EAASwB,SAAA,CAAYxB,EAASwB,SAAA,CAAYrB,EAAagH,YAAA,CAE3D,IAEA,IACN,EAEAgG,CAAAA,GAAuB7Z,WAAA,CAAc4Z,GAOrC,IAAMF,GAA+BzV,EAAAA,UAAA,CAGnC,CAAC3C,EAAiDC,KAClD,GAAM,CAAEwB,cAAAA,CAAA,CAAe4W,aAAAA,CAAA,CAAc,GAAGO,EAAqB,CAAI5Y,EAC3DgQ,EAAiB9F,EAAwB,qBAAsBzI,GAC/DoX,EAA2BlW,EAAAA,MAAA,CAAsB,MACjDoD,EAAWnF,EAAca,GAEzBqX,EAA6BnW,EAAAA,WAAA,CAAY,KACV,OAA/BkW,EAAmB9R,OAAA,GACrB0G,OAAOsL,aAAA,CAAcF,EAAmB9R,OAAO,EAC/C8R,EAAmB9R,OAAA,CAAU,KAEjC,EAAG,EAAE,EAeL,OAbMpE,EAAAA,SAAA,CAAU,IACP,IAAMmW,IACZ,CAACA,EAAqB,EAMzB5P,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB,KACd,IAAM8P,EAAajT,IAAWU,IAAA,CAAK,GAAUF,EAAK9I,GAAA,CAAIsJ,OAAA,GAAYuF,SAASC,aAAa,EACxFyM,GAAYvb,IAAIsJ,SAAS2F,eAAe,CAAEC,MAAO,SAAU,EAC7D,EAAG,CAAC5G,EAAS,EAGX5F,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACyM,GAAA,CAAV,CACC,cAAW,GACV,GAAG6E,CAAA,CACJnb,IAAKwC,EACLM,MAAO,CAAE0Y,WAAY,EAAG,GAAGL,EAAqBrY,KAAA,EAChDwH,cAAeH,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBgR,EAAqB7Q,aAAA,CAAe,KACnC,OAA/B8Q,EAAmB9R,OAAA,EACrB8R,CAAAA,EAAmB9R,OAAA,CAAU0G,OAAOyL,WAAA,CAAYb,EAAc,IAElE,GACAnB,cAAetP,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBgR,EAAqB1B,aAAA,CAAe,KACtElH,EAAe3B,WAAA,KACoB,OAA/BwK,EAAmB9R,OAAA,EACrB8R,CAAAA,EAAmB9R,OAAA,CAAU0G,OAAOyL,WAAA,CAAYb,EAAc,IAElE,GACAlB,eAAgBvP,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBgR,EAAqBzB,cAAA,CAAgB,KACxE2B,GACF,EAAC,EAGP,GAWMK,GAAwBxW,EAAAA,UAAA,CAC5B,CAAC3C,EAA0CC,KACzC,GAAM,CAAEwB,cAAAA,CAAA,CAAe,GAAG2X,EAAe,CAAIpZ,EAC7C,MAAOG,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACyM,GAAA,CAAV,CAAc,cAAW,GAAE,GAAGqF,CAAA,CAAgB3b,IAAKwC,CAAA,EAC7D,EAGFkZ,CAAAA,GAAgBza,WAAA,CAZO,kBAkBvB,IAAM2a,GAAa,aAkBnBC,CAZ0B3W,EAAAA,UAAA,CACxB,CAAC3C,EAAsCC,KACrC,GAAM,CAAEwB,cAAAA,CAAA,CAAe,GAAG8X,EAAW,CAAIvZ,EACnCwC,EAAcrB,EAAeM,GAC7BkE,EAAUtE,EAAiBgY,GAAY5X,GACvCuO,EAAiB9F,EAAwBmP,GAAY5X,GAC3D,OAAOkE,EAAQhE,IAAA,EAAQqO,WAAAA,EAAe1Q,QAAA,CACpCa,CAAAA,EAAAA,EAAAA,GAAAA,EAAiBkE,EAAAA,EAAA,CAAhB,CAAuB,GAAG7B,CAAA,CAAc,GAAG+W,CAAA,CAAY9b,IAAKwC,CAAA,GAC3D,IACN,GAGUvB,WAAA,CAAc2a,GAW1B,IAAMjU,GAA0BzC,EAAAA,UAAA,CAC9B,CAAC,CAAElB,cAAAA,CAAA,CAAe1C,MAAAA,CAAA,CAAO,GAAGiB,EAAM,CAAwCC,KACxE,IAAMxC,EAAYkF,EAAAA,MAAA,CAA0B,MACtCkD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB7F,EAAcxC,GAC7C+b,EAAYC,CAAAA,EAAAA,EAAAA,CAAAA,EAAY1a,GAgC9B,OA7BM4D,EAAAA,SAAA,CAAU,KACd,IAAM+W,EAASjc,EAAIsJ,OAAA,CACnB,GAAI,CAAC2S,EAAQ,OAOb,IAAMlW,EAAWmW,OAJSC,wBAAA,CADNnM,OAAOoM,iBAAA,CAAkBC,SAAA,CAG3C,SAE0BC,GAAA,CAC5B,GAAIP,IAAcza,GAASyE,EAAU,CACnC,IAAM8B,EAAQ,IAAI0U,MAAM,SAAU,CAAEC,QAAS,EAAK,GAClDzW,EAAS0W,IAAA,CAAKR,EAAQ3a,GACtB2a,EAAOS,aAAA,CAAc7U,EACvB,CACF,EAAG,CAACkU,EAAWza,EAAM,EAenBoB,CAAAA,EAAAA,EAAAA,GAAAA,EAACmH,EAAAA,EAASA,CAACoS,MAAA,CAAV,CACE,GAAG1Z,CAAA,CACJO,MAAO,CAAE,GAAGpB,CAAA,CAAwB,GAAGa,EAAMO,KAAA,EAC7C9C,IAAKoI,EACL7D,aAAcjD,CAAA,EAGpB,GAOF,SAAS2I,GAAsB3I,CAAA,EAC7B,MAAOA,KAAAA,GAAgBA,KAAU,IAAVA,CACzB,CAEA,SAASqH,GAAmBgU,CAAA,EAC1B,IAAMC,EAAqBC,CAAAA,EAAAA,EAAAA,CAAAA,EAAeF,GACpCnU,EAAkBtD,EAAAA,MAAA,CAAO,IACzB4X,EAAiB5X,EAAAA,MAAA,CAAO,GAExBuD,EAA8BvD,EAAAA,WAAA,CAClC,IACE,IAAMiE,EAASX,EAAUc,OAAA,CAAUlI,EACnCwb,EAAmBzT,GAElB,SAAS4T,EAAazb,CAAA,EACrBkH,EAAUc,OAAA,CAAUhI,EACpB0O,OAAOgN,YAAA,CAAaF,EAASxT,OAAO,EAEtB,KAAVhI,GAAcwb,CAAAA,EAASxT,OAAA,CAAU0G,OAAOC,UAAA,CAAW,IAAM8M,EAAa,IAAK,KACjF,EAAG5T,EACL,EACA,CAACyT,EAAkB,EAGflU,EAAuBxD,EAAAA,WAAA,CAAY,KACvCsD,EAAUc,OAAA,CAAU,GACpB0G,OAAOgN,YAAA,CAAaF,EAASxT,OAAO,CACtC,EAAG,EAAE,EAML,OAJMpE,EAAAA,SAAA,CAAU,IACP,IAAM8K,OAAOgN,YAAA,CAAaF,EAASxT,OAAO,EAChD,EAAE,EAEE,CAACd,EAAWC,EAAuBC,EAAc,CAoB1D,SAASQ,GACP+I,CAAA,CACA9I,CAAA,CACAJ,CAAA,MAkBgCkU,EAfhC,IAAMC,EAAmBC,EADClS,MAAA,CAAS,GAAK1E,MAAMC,IAAA,CAAK2C,GAAQiU,KAAA,CAAM,GAAUC,IAASlU,CAAA,CAAO,EAAE,EACvDA,CAAA,CAAO,EAAC,CAAKA,EAE/CmU,GAa4BL,EAbIxb,KAAKD,GAAA,CADhBuH,EAAckJ,EAAMI,OAAA,CAAQtJ,GAAe,GACL,GAcxDwU,EAAM9W,GAAA,CAAO,CAACrG,EAAGod,IAAUD,CAAA,EAAON,EAAaO,CAAAA,EAASD,EAAMtS,MAAM,CAAE,EAbtB,KAA5BiS,EAAiBjS,MAAA,EACpBqS,CAAAA,EAAeA,EAAazU,MAAA,CAAO,GAAO4U,IAAM1U,EAAAA,EACxE,IAAME,EAAWqU,EAAatU,IAAA,CAAK,GACjCF,EAAK0P,SAAA,CAAUkF,WAAA,GAAcC,UAAA,CAAWT,EAAiBQ,WAAA,KAE3D,OAAOzU,IAAaF,EAAcE,EAAW,MAC/C,CAxEAtB,GAAkB1G,WAAA,CApDQ,oBAsI1B,IAAM2c,GAAO7Z,EACP8Z,GAAU7V,EACV8V,GAAQ1S,EACR2S,GAAOpS,EACPqS,GAASnS,EACToS,GAAUjS,EACVkS,GAAWxH,GACXyH,GAAQtG,GACRuG,GAAQnG,GACRoG,GAAO9F,GACP+F,GAAW1E,GACX2E,GAAgBnE,GAChBoE,GAAiBjE,GACjBkE,GAAmB3D,GACnB4D,GAAYhD", "sources": ["webpack://_N_E/./node_modules/iconsax-react/dist/esm/SearchNormal1.js", "webpack://_N_E/../../../src/icons/chevron-up.ts", "webpack://_N_E/./node_modules/@radix-ui/number/dist/index.mjs", "webpack://_N_E/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "webpack://_N_E/../src/select.tsx"], "sourcesContent": ["import { _ as _objectWithoutProperties, a as _extends } from './_rollupPluginBabelHelpers-3bc641ae.js';\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\n\nvar _excluded = [\"variant\", \"color\", \"size\"];\n\nvar Bold = function Bold(_ref) {\n  var color = _ref.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z\",\n    fill: color\n  }));\n};\n\nvar Broken = function Broken(_ref2) {\n  var color = _ref2.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Bulk = function Bulk(_ref3) {\n  var color = _ref3.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z\",\n    fill: color\n  }));\n};\n\nvar Linear = function Linear(_ref4) {\n  var color = _ref4.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Outline = function Outline(_ref5) {\n  var color = _ref5.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z\",\n    fill: color\n  }));\n};\n\nvar TwoTone = function TwoTone(_ref6) {\n  var color = _ref6.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"m22 22-2-2\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar chooseVariant = function chooseVariant(variant, color) {\n  switch (variant) {\n    case 'Bold':\n      return /*#__PURE__*/React.createElement(Bold, {\n        color: color\n      });\n\n    case 'Broken':\n      return /*#__PURE__*/React.createElement(Broken, {\n        color: color\n      });\n\n    case 'Bulk':\n      return /*#__PURE__*/React.createElement(Bulk, {\n        color: color\n      });\n\n    case 'Linear':\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n\n    case 'Outline':\n      return /*#__PURE__*/React.createElement(Outline, {\n        color: color\n      });\n\n    case 'TwoTone':\n      return /*#__PURE__*/React.createElement(TwoTone, {\n        color: color\n      });\n\n    default:\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n  }\n};\n\nvar SearchNormal1 = /*#__PURE__*/forwardRef(function (_ref7, ref) {\n  var variant = _ref7.variant,\n      color = _ref7.color,\n      size = _ref7.size,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: ref,\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\"\n  }), chooseVariant(variant, color));\n});\nSearchNormal1.propTypes = {\n  variant: PropTypes.oneOf(['Linear', 'Bold', 'Broken', 'Bulk', 'Outline', 'TwoTone']),\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nSearchNormal1.defaultProps = {\n  variant: 'Linear',\n  color: 'currentColor',\n  size: '24'\n};\nSearchNormal1.displayName = 'SearchNormal1';\n\nexport { SearchNormal1 as default };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('ChevronUp', [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]]);\n\nexport default ChevronUp;\n", "// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport {\n  Root,\n  VISUALLY_HIDDEN_STYLES,\n  VisuallyHidden\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n"], "names": ["_excluded", "Bold", "_ref", "color", "react__WEBPACK_IMPORTED_MODULE_0__", "createElement", "Fragment", "d", "fill", "Broken", "_ref2", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "Bulk", "_ref3", "opacity", "Linear", "_ref4", "Outline", "_ref5", "TwoTone", "_ref6", "choose<PERSON><PERSON><PERSON>", "variant", "SearchNormal1", "forwardRef", "_ref7", "ref", "size", "rest", "_rollupPluginBabelHelpers_3bc641ae_js__WEBPACK_IMPORTED_MODULE_1__", "_", "a", "xmlns", "width", "height", "viewBox", "propTypes", "prop_types__WEBPACK_IMPORTED_MODULE_2___default", "oneOf", "string", "oneOfType", "number", "defaultProps", "displayName", "ChevronUp", "createLucideIcon", "key", "clamp", "value", "min", "max", "Math", "VISUALLY_HIDDEN_STYLES", "Object", "freeze", "position", "border", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "VisuallyHidden", "react", "props", "forwardedRef", "jsx_runtime", "jsx", "react_primitive_dist", "WV", "span", "style", "OPEN_KEYS", "SELECTION_KEYS", "SELECT_NAME", "Collection", "useCollection", "createCollectionScope", "createCollection", "createSelectContext", "createSelectScope", "createContextScope", "createPopperScope", "usePopperScope", "SelectProvider", "useSelectContext", "SelectNativeOptionsProvider", "useSelectNativeOptionsContext", "Select", "__scopeSelect", "children", "open", "openProp", "defaultOpen", "onOpenChange", "valueProp", "defaultValue", "onValueChange", "dir", "name", "autoComplete", "disabled", "required", "form", "popperScope", "trigger", "setTrigger", "React", "valueNode", "setValueNode", "valueNodeHasChildren", "setValueNodeHasChildren", "direction", "useDirection", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "onChange", "caller", "setValue", "triggerPointerDownPosRef", "isFormControl", "closest", "nativeOptionsSet", "setNativeOptionsSet", "Set", "nativeSelectKey", "Array", "from", "map", "option", "join", "PopperPrimitive", "jsxs", "scope", "onTriggerChange", "onValueNodeChange", "onValueNodeHasChildrenChange", "contentId", "useId", "Provider", "onNativeOptionAdd", "prev", "add", "onNativeOptionRemove", "optionsSet", "delete", "SelectBubbleInput", "tabIndex", "event", "target", "TRIGGER_NAME", "SelectTrigger", "triggerProps", "context", "isDisabled", "composedRefs", "useComposedRefs", "getItems", "pointerTypeRef", "searchRef", "handleTypeaheadSearch", "resetTypeahead", "useTypeaheadSearch", "enabledItems", "filter", "item", "currentItem", "find", "nextItem", "findNextItem", "search", "handleOpen", "pointerEvent", "current", "x", "round", "pageX", "y", "pageY", "<PERSON><PERSON><PERSON><PERSON>", "Primitive", "button", "type", "role", "shouldShowPlaceholder", "onClick", "composeEventHandlers", "currentTarget", "focus", "onPointerDown", "pointerType", "hasPointerCapture", "pointerId", "releasePointerCapture", "ctrl<PERSON>ey", "preventDefault", "onKeyDown", "isTypingAhead", "altKey", "metaKey", "length", "includes", "VALUE_NAME", "SelectValue", "className", "placeholder", "valueProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useLayoutEffect", "pointerEvents", "SelectIcon", "iconProps", "SelectPortal", "PortalPrimitive", "CONTENT_NAME", "SelectContent", "fragment", "setFragment", "DocumentFragment", "SelectContentImpl", "frag", "ReactDOM", "SelectContentProvider", "Slot", "useSelectContentContext", "createSlot", "onCloseAutoFocus", "onEscapeKeyDown", "onPointerDownOutside", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "collisionBoundary", "collisionPadding", "sticky", "hideWhenDetached", "avoidCollisions", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "viewport", "setViewport", "node", "selectedItem", "setSelectedItem", "selectedItemText", "setSelectedItemText", "isPositioned", "setIsPositioned", "firstValidItemFoundRef", "hideOthers", "useFocusGuards", "focusFirst", "firstItem", "restItems", "lastItem", "slice", "PREVIOUSLY_FOCUSED_ELEMENT", "document", "activeElement", "candidate", "candidates", "scrollIntoView", "block", "scrollTop", "scrollHeight", "focusSelectedItem", "pointerMoveDel<PERSON>", "handlePointerMove", "abs", "handlePointerUp", "contains", "removeEventListener", "addEventListener", "capture", "once", "close", "window", "setTimeout", "itemRefCallback", "isFirstValidItem", "isSelectedItem", "handleItemLeave", "itemTextRefCallback", "SelectPosition", "SelectPopperPosition", "SelectItemAlignedPosition", "popperContentProps", "onViewportChange", "onItemLeave", "RemoveScroll", "as", "allowPinchZoom", "FocusScope", "trapped", "onMountAutoFocus", "onUnmountAutoFocus", "preventScroll", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "disableOutsidePointerEvents", "onFocusOutside", "on<PERSON><PERSON><PERSON>", "id", "onContextMenu", "onPlaced", "display", "flexDirection", "outline", "isModifierKey", "candidateNodes", "items", "reverse", "currentElement", "currentIndex", "indexOf", "popperProps", "contentContext", "contentWrapper", "setContentWrapper", "shouldExpandOnScrollRef", "shouldRepositionRef", "triggerRect", "getBoundingClientRect", "contentRect", "valueNodeRect", "itemTextRect", "itemTextOffset", "left", "leftDelta", "minC<PERSON>nt<PERSON>id<PERSON>", "contentWidth", "clampedLeft", "rightEdge", "innerWidth", "min<PERSON><PERSON><PERSON>", "right", "<PERSON><PERSON><PERSON><PERSON>", "clampedRight", "leftEdge", "availableHeight", "innerHeight", "CONTENT_MARGIN", "itemsHeight", "contentStyles", "getComputedStyle", "contentBorderTopWidth", "parseInt", "borderTopWidth", "contentPaddingTop", "paddingTop", "contentBorderBottomWidth", "borderBottomWidth", "fullContentHeight", "paddingBottom", "minContentHeight", "offsetHeight", "viewportStyles", "viewportPaddingTop", "viewportPaddingBottom", "topEdgeToTriggerMiddle", "top", "selectedItemHalfHeight", "contentTopToItemMiddle", "offsetTop", "isLastItem", "bottom", "viewportOffsetBottom", "clientHeight", "isFirstItem", "clampedTopEdgeToTriggerMiddle", "minHeight", "maxHeight", "requestAnimationFrame", "contentZIndex", "setContentZIndex", "zIndex", "handleScrollButtonChange", "SelectViewportProvider", "onScrollButtonChange", "div", "boxSizing", "useSelectViewportContext", "VIEWPORT_NAME", "SelectViewport", "nonce", "viewportProps", "viewportContext", "prevScrollTopRef", "dangerouslySetInnerHTML", "__html", "flex", "onScroll", "scrolledBy", "prevHeight", "parseFloat", "nextHeight", "clampedNextHeight", "heightDiff", "justifyContent", "GROUP_NAME", "SelectGroupContextProvider", "useSelectGroupContext", "SelectGroup", "groupProps", "groupId", "LABEL_NAME", "SelectLabel", "labelProps", "groupContext", "ITEM_NAME", "SelectItemContextProvider", "useSelectItemContext", "SelectItem", "textValue", "textValueProp", "itemProps", "isSelected", "setTextValue", "isFocused", "setIsFocused", "textId", "handleSelect", "onItemTextChange", "prevTextValue", "textContent", "trim", "ItemSlot", "onFocus", "onBlur", "onPointerUp", "onPointerMove", "onPointerLeave", "ITEM_TEXT_NAME", "SelectItemText", "itemTextProps", "itemContext", "nativeOptionsContext", "itemTextNode", "setItemTextNode", "nativeOption", "ITEM_INDICATOR_NAME", "SelectItemIndicator", "itemIndicatorProps", "SCROLL_UP_BUTTON_NAME", "SelectScrollUpButton", "canScrollUp", "setCanScrollUp", "handleScroll", "SelectScrollButtonImpl", "onAutoScroll", "SCROLL_DOWN_BUTTON_NAME", "SelectScrollDownButton", "canScrollDown", "setCanScrollDown", "maxScroll", "ceil", "scrollIndicatorProps", "autoScrollTimerRef", "clearAutoScrollTimer", "clearInterval", "activeItem", "flexShrink", "setInterval", "SelectSeparator", "separatorProps", "ARROW_NAME", "SelectArrow", "arrowProps", "prevValue", "usePrevious", "select", "descriptor", "getOwnPropertyDescriptor", "HTMLSelectElement", "prototype", "set", "Event", "bubbles", "call", "dispatchEvent", "onSearchChange", "handleSearchChange", "useCallbackRef", "timerRef", "updateSearch", "clearTimeout", "startIndex", "normalizedSearch", "isRepeated", "every", "char", "wrappedItems", "array", "index", "v", "toLowerCase", "startsWith", "Root", "<PERSON><PERSON>", "Value", "Icon", "Portal", "Content", "Viewport", "Group", "Label", "<PERSON><PERSON>", "ItemText", "ItemIndicator", "ScrollUpButton", "ScrollDownButton", "Separator"], "sourceRoot": ""}