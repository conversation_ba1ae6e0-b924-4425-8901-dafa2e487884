(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[73230],{99714:function(e,a,n){Promise.resolve().then(n.bind(n,77190))},77190:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return f}});var t=n(57437),s=n(74667),l=n(85539),r=n(27186),c=n(85017),i=n(6512),u=n(75730),d=n(94508),o=n(99376),m=n(2265),h=n(43949);function f(){var e,a;let n=(0,o.useSearchParams)(),f=(0,o.useRouter)(),x=(0,o.usePathname)(),[p,v]=m.useState(""),{t:g}=(0,h.$G)(),{data:j,meta:w,isLoading:N,refresh:_}=(0,u.Z)("/admin/merchants?page=".concat(null!==(e=n.get("page"))&&void 0!==e?e:1,"&limit=").concat(null!==(a=n.get("limit"))&&void 0!==a?a:10));return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(l.R,{value:p,onChange:e=>{e.preventDefault();let a=(0,d.w4)(e.target.value);v(e.target.value),f.replace("".concat(x,"?").concat(a.toString()))},iconPlacement:"end",placeholder:g("Search..."),containerClass:"w-full sm:w-auto"}),(0,t.jsx)(c.k,{canFilterUser:!0,canFilterByGender:!0,canFilterByCountryCode:!0}),(0,t.jsx)(r._,{url:"/admin/merchants/export/all"})]}),(0,t.jsx)("div",{})]}),(0,t.jsx)(i.Z,{className:"my-4"}),(0,t.jsx)(s.Z,{data:j,meta:w,isLoading:N,refresh:_})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,31384,27443,83568,227,56993,85017,74667,92971,95030,1744],function(){return e(e.s=99714)}),_N_E=e.O()}]);