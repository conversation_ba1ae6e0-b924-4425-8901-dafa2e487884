"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[81766],{80114:function(t,e,n){n.d(e,{default:function(){return o}});var i=n(57437),r=n(85487),a=n(94508),s=n(43949);function o(t){let{className:e}=t,{t:n}=(0,s.$G)();return(0,i.jsx)("div",{className:(0,a.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",e),children:(0,i.jsx)(r.<PERSON>,{title:n("Loading..."),className:"text-foreground"})})}},85487:function(t,e,n){n.d(e,{Loader:function(){return s}});var i=n(57437),r=n(94508),a=n(43949);function s(t){let{title:e="Loading...",className:n}=t,{t:s}=(0,a.$G)();return(0,i.jsxs)("div",{className:(0,r.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,i.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,i.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,i.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,i.jsx)("span",{className:"text-inherit",children:s(e)})]})}},62869:function(t,e,n){n.d(e,{d:function(){return d},z:function(){return l}});var i=n(57437),r=n(37053),a=n(90535),s=n(2265),o=n(94508);let d=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((t,e)=>{let{className:n,variant:a,size:s,asChild:l=!1,...u}=t,c=l?r.g7:"button";return(0,i.jsx)(c,{className:(0,o.ZP)(d({variant:a,size:s,className:n})),ref:e,...u})});l.displayName="Button"},26110:function(t,e,n){n.d(e,{$N:function(){return p},Be:function(){return m},GG:function(){return c},Vq:function(){return d},cZ:function(){return v},fK:function(){return f},hg:function(){return l}});var i=n(57437),r=n(2265),a=n(49027),s=n(32489),o=n(94508);let d=a.fC,l=a.xz,u=a.h_,c=a.x8,h=r.forwardRef((t,e)=>{let{className:n,...r}=t;return(0,i.jsx)(a.aV,{ref:e,className:(0,o.ZP)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",n),...r})});h.displayName=a.aV.displayName;let v=r.forwardRef((t,e)=>{let{className:n,children:r,...d}=t;return(0,i.jsxs)(u,{children:[(0,i.jsx)(h,{}),(0,i.jsxs)(a.VY,{ref:e,className:(0,o.ZP)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),...d,children:[r,(0,i.jsxs)(a.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,i.jsx)(s.Z,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});v.displayName=a.VY.displayName;let f=t=>{let{className:e,...n}=t;return(0,i.jsx)("div",{className:(0,o.ZP)("flex flex-col space-y-1.5 text-center sm:text-left",e),...n})};f.displayName="DialogHeader";let p=r.forwardRef((t,e)=>{let{className:n,...r}=t;return(0,i.jsx)(a.Dx,{ref:e,className:(0,o.ZP)("text-lg font-semibold leading-none tracking-tight",n),...r})});p.displayName=a.Dx.displayName;let m=r.forwardRef((t,e)=>{let{className:n,...r}=t;return(0,i.jsx)(a.dk,{ref:e,className:(0,o.ZP)("text-sm text-muted-foreground",n),...r})});m.displayName=a.dk.displayName},6512:function(t,e,n){var i=n(57437),r=n(55156),a=n(2265),s=n(94508);let o=a.forwardRef((t,e)=>{let{className:n,orientation:a="horizontal",decorative:o=!0,...d}=t;return(0,i.jsx)(r.f,{ref:e,decorative:o,orientation:a,className:(0,s.ZP)("shrink-0 bg-divider","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",n),...d})});o.displayName=r.f.displayName,e.Z=o},1828:function(t,e,n){var i=n(57437),r=n(50721),a=n(2265),s=n(94508);let o=a.forwardRef((t,e)=>{let{className:n,...a}=t;return(0,i.jsx)(r.fC,{className:(0,s.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",n),...a,ref:e,children:(0,i.jsx)(r.bU,{className:(0,s.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});o.displayName=r.fC.displayName,e.Z=o},17062:function(t,e,n){n.d(e,{Z:function(){return p},O:function(){return f}});var i=n(57437),r=n(80114);n(83079);var a=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),s=n(31117),o=n(79981),d=n(78040),l=n(83130);class u{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(t){this.as=t.as,this.asname=t.asname,this.city=t.city,this.continent=t.continent,this.continentCode=t.continentCode,this.country=t.country,this.countryCode=t.countryCode,this.currency=t.currency,this.district=t.district,this.hosting=t.hosting,this.isp=t.isp,this.lat=t.lat,this.lon=t.lon,this.mobile=t.mobile,this.offset=t.offset,this.org=t.org,this.proxy=t.proxy,this.query=t.query,this.region=t.region,this.regionName=t.regionName,this.reverse=t.reverse,this.status=t.status,this.timezone=t.timezone,this.zip=t.zip}}var c=n(99376),h=n(2265);let v=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),f=()=>h.useContext(v);function p(t){let{children:e}=t,[n,f]=h.useState("Desktop"),[p,m]=h.useState(!1),[g,y]=h.useState(),{data:w,isLoading:x,error:b,mutate:A}=(0,s.d)("/auth/check",{revalidateOnFocus:!1}),{data:C,isLoading:k}=(0,s.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:N,isLoading:I}=(0,s.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),S=(0,c.useRouter)(),L=(0,c.usePathname)();h.useEffect(()=>{(async()=>{f((await a()).deviceType)})()},[]),h.useEffect(()=>{let t=()=>{let t=window.innerWidth;f(t<768?"Mobile":t<1024?"Tablet":"Desktop"),m(t>1024)};return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:t}=await o.Z.post("/auth/geo-location");y(new u(t))}catch(t){}})()},[]),h.useLayoutEffect(()=>{b&&!d.sp.includes(L)&&S.push("/signin")},[b]);let D=h.useMemo(()=>{var t,e,i;return{isAuthenticate:!!(null==w?void 0:null===(t=w.data)||void 0===t?void 0:t.login),auth:(null==w?void 0:null===(e=w.data)||void 0===e?void 0:e.user)?new l.n(null==w?void 0:null===(i=w.data)||void 0===i?void 0:i.user):null,isLoading:x,deviceLocation:g,refreshAuth:()=>A(w),isExpanded:p,device:n,setIsExpanded:m,branding:null==C?void 0:C.data,googleAnalytics:(null==N?void 0:N.data)?{active:null==N?void 0:N.data.active,apiKey:null==N?void 0:N.data.apiKey}:{active:!1,apiKey:""}}},[w,g,p,n]),R=!x&&!k&&!I;return(0,i.jsx)(v.Provider,{value:D,children:R?e:(0,i.jsx)(r.default,{})})}},83277:function(t,e,n){n.d(e,{f:function(){return a}});var i=n(79981),r=n(97751);async function a(t){let{cardId:e,isAdmin:n=!1}=t;try{let t=await i.Z.delete("".concat(n?"/admin/cards/":"/cards/").concat(e));return(0,r.B)(t)}catch(t){return(0,r.D)(t)}}},80167:function(t,e,n){n.d(e,{a:function(){return a}});var i=n(79981),r=n(97751);async function a(t){let{cardId:e,dataList:n,isAdmin:a=!1}=t;try{let t=await i.Z.put("".concat(a?"/admin/cards/change-status/":"/cards/change-status/").concat(e),n);return(0,r.B)(t)}catch(t){return(0,r.D)(t)}}},97751:function(t,e,n){n.d(e,{B:function(){return r},D:function(){return a}});var i=n(43577);function r(t){var e,n,i;return{...t.data,statusCode:t.status,statusText:t.statusText,status:200===t.status||201===t.status,message:null!==(i=null===(e=t.data)||void 0===e?void 0:e.message)&&void 0!==i?i:"",data:null===(n=t.data)||void 0===n?void 0:n.data}}function a(t){let e=500,n="Internal Server Error",r="An unknown error occurred";if((0,i.IZ)(t)){var a,s,o,d,l,u,c,h,v,f,p,m;e=null!==(v=null===(a=t.response)||void 0===a?void 0:a.status)&&void 0!==v?v:500,n=null!==(f=null===(s=t.response)||void 0===s?void 0:s.statusText)&&void 0!==f?f:"Internal Server Error",r=null!==(m=null!==(p=null===(u=t.response)||void 0===u?void 0:null===(l=u.data)||void 0===l?void 0:null===(d=l.messages)||void 0===d?void 0:null===(o=d[0])||void 0===o?void 0:o.message)&&void 0!==p?p:null===(h=t.response)||void 0===h?void 0:null===(c=h.data)||void 0===c?void 0:c.message)&&void 0!==m?m:t.message}else t instanceof Error&&(r=t.message);return{statusCode:e,statusText:n,status:!1,message:r,data:void 0,error:t}}},3612:function(t,e,n){n.d(e,{a:function(){return r}});var i=n(17062);let r=()=>{let t=(0,i.O)();return{isAuthenticate:t.isAuthenticate,auth:t.auth,isLoading:t.isLoading,refreshAuth:t.refreshAuth,deviceLocation:t.deviceLocation}}},21251:function(t,e,n){n.d(e,{T:function(){return r}});var i=n(17062);let r=()=>{let{branding:t}=(0,i.O)();return t}},31117:function(t,e,n){n.d(e,{d:function(){return a}});var i=n(79981),r=n(85323);let a=(t,e)=>(0,r.ZP)(t||null,t=>i.Z.get(t),{shouldRetryOnError:!1,revalidateOnFocus:!1,...e})},79981:function(t,e,n){var i=n(78040),r=n(83464);e.Z=r.default.create({baseURL:i.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(t,e,n){n.d(e,{rH:function(){return i},sp:function(){return r}});let i={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},r=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(t,e,n){n.d(e,{F:function(){return u},Fg:function(){return v},Fp:function(){return l},Qp:function(){return h},ZP:function(){return o},fl:function(){return d},qR:function(){return c},w4:function(){return f}});var i=n(78040),r=n(61994),a=n(14438),s=n(53335);function o(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return(0,s.m6)((0,r.W)(e))}function d(t){return t?t.toLowerCase().split(/[\s_-]+/).map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" "):""}let l=t=>{t&&navigator.clipboard.writeText(t).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class u{format(t,e){let{currencyCode:n,amountText:i}=this.formatter(t,e);return"".concat(i," ").concat(n)}formatVC(t,e){let{currencyCode:n,amountText:i}=this.formatter(t,e);return"".concat(i," ").concat(n," ")}constructor(t){this.formatter=(t,e)=>{var n,i;let r;let a=void 0===e?this.currencyCode:e;try{r=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(t){r=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let s=null!==(i=null===(n=r.formatToParts(t).find(t=>"currency"===t.type))||void 0===n?void 0:n.value)&&void 0!==i?i:a,o=r.format(t),d=o.substring(s.length).trim();return{currencyCode:a,currencySymbol:s,formattedAmount:o,amountText:d}},this.currencyCode=t||"USD"}}let c=t=>t?"".concat(i.rH.STATIC_URL,"/").concat(t):"",h=t=>t?"".concat(i.rH.API_URL,"/").concat(t):"",v=t=>t?(null==t?void 0:t.match(/^\+/))?t:"+".concat(t):"",f=function(t){var e,n;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",r=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(e=n.location)||void 0===e?void 0:e.search);return t?r.set(i,t):r.delete(i),r}},74539:function(t,e,n){n.d(e,{k:function(){return i}});class i{constructor(t){this.id=null==t?void 0:t.id,this.city=null==t?void 0:t.city,this.countryCode=null==t?void 0:t.countryCode,this.addressLine=null==t?void 0:t.addressLine,this.street=null==t?void 0:t.street,this.type=null==t?void 0:t.type,this.zipCode=null==t?void 0:t.zipCode,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}},83130:function(t,e,n){n.d(e,{n:function(){return d}});class i{constructor(t){this.id=null==t?void 0:t.id,this.userId=null==t?void 0:t.userId,this.agentId=null==t?void 0:t.agentId,this.name=null==t?void 0:t.name,this.email=null==t?void 0:t.email,this.occupation=null==t?void 0:t.occupation,this.status=null==t?void 0:t.status,this.isRecommended=!!(null==t?void 0:t.isRecommended),this.isSuspend=!!(null==t?void 0:t.isSuspend),this.proof=null==t?void 0:t.proof,this.depositFee=null==t?void 0:t.depositFee,this.withdrawFee=null==t?void 0:t.withdrawFee,this.withdrawCommission=null==t?void 0:t.withdrawCommission,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}var r=n(84937);class a{constructor(t){this.id=null==t?void 0:t.id,this.userId=null==t?void 0:t.userId,this.merchantId=null==t?void 0:t.merchantId,this.name=null==t?void 0:t.name,this.email=null==t?void 0:t.email,this.status=null==t?void 0:t.status,this.isSuspend=null==t?void 0:t.isSuspend,this.proof=null==t?void 0:t.proof,this.depositFee=null==t?void 0:t.depositFee,this.withdrawFee=null==t?void 0:t.withdrawFee,this.webhookUrl=null==t?void 0:t.webhookUrl,this.allowedIp=null==t?void 0:t.allowedIp,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}var s=n(66419),o=n(78040);class d{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(t){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=t.id,this.roleId=t.roleId,this.email=t.email,this.isEmailVerified=t.isEmailVerified,this.status=t.status,this.kycStatus=t.kycStatus,this.kyc=t.kyc||null,this.lastIpAddress=t.lastIpAddress,this.lastCountryName=t.lastCountryName,this.passwordUpdated=t.passwordUpdated,this.referredBy=t.referredBy,this.referralCode=t.referralCode,this.otpCode=t.otpCode,this.createdAt=new Date(t.createdAt),this.updatedAt=new Date(t.updatedAt),this.role=new s.u(t.role),this.permission=t.permission,this.customer=(null==t?void 0:t.customer)?new r.O(t.customer):void 0,this.merchant=(null==t?void 0:t.merchant)?new a(t.merchant):void 0,this.agent=(null==t?void 0:t.agent)?new i(t.agent):void 0}}},502:function(t,e,n){n.d(e,{Z:function(){return i}});class i{constructor(t){this.id=null==t?void 0:t.id,this.cardId=null==t?void 0:t.cardId,this.userId=null==t?void 0:t.userId,this.walletId=null==t?void 0:t.walletId,this.number=null==t?void 0:t.number,this.cvc=null==t?void 0:t.cvc,this.lastFour=null==t?void 0:t.lastFour,this.brand=null==t?void 0:t.brand,this.expMonth=null==t?void 0:t.expMonth,this.expYear=null==t?void 0:t.expYear,this.status=null==t?void 0:t.status,this.type=null==t?void 0:t.type,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt),this.wallet=null==t?void 0:t.wallet,this.user=null==t?void 0:t.user}}},84937:function(t,e,n){n.d(e,{O:function(){return r}});var i=n(74539);class r{constructor(t){var e,n;this.id=null==t?void 0:t.id,this.userId=null==t?void 0:t.userId,this.name=null==t?void 0:t.name,this.email=null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.email,this.phone=(null==t?void 0:null===(n=t.phone)||void 0===n?void 0:n.match(/^\+/))?t.phone:"+".concat(null==t?void 0:t.phone),this.gender=null==t?void 0:t.gender,this.dob=new Date(null==t?void 0:t.dob),this.avatar=null==t?void 0:t.profileImage,this.address=new i.k(null==t?void 0:t.address),this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}},66419:function(t,e,n){n.d(e,{u:function(){return i}});class i{constructor(t){this.id=null==t?void 0:t.id,this.name=null==t?void 0:t.name,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}}}]);