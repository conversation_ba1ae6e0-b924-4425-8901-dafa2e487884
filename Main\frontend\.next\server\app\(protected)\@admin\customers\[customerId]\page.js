(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6189],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},69743:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ComponentMod:()=>Z,default:()=>z});var s,n={};t.r(n),t.d(n,{AppRouter:()=>m.WY,ClientPageRoot:()=>m.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>m.yO,NotFoundBoundary:()=>m.O4,Postpone:()=>m.hQ,RenderFromTemplateContext:()=>m.b5,__next_app__:()=>f,actionAsyncStorage:()=>m.Wz,createDynamicallyTrackedSearchParams:()=>m.rL,createUntrackedSearchParams:()=>m.S5,decodeAction:()=>m.Hs,decodeFormState:()=>m.dH,decodeReply:()=>m.kf,originalPathname:()=>h,pages:()=>x,patchFetch:()=>m.XH,preconnect:()=>m.$P,preloadFont:()=>m.C5,preloadStyle:()=>m.oH,renderToReadableStream:()=>m.aW,requestAsyncStorage:()=>m.Fg,routeModule:()=>j,serverHooks:()=>m.GP,staticGenerationAsyncStorage:()=>m.AT,taintObjectReference:()=>m.nr,tree:()=>p}),t(67206);var a=t(79319),o=t(20518),c=t(61902),i=t(62042),l=t(44630),d=t(44828),u=t(65505),m=t(13839);let p=["",{children:["(protected)",{admin:["children",{children:["customers",{children:["[customerId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94411)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,38520)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,96104)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,78174)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,73081)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],x=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\page.tsx"],h="/(protected)/@admin/customers/[customerId]/page",f={require:t,loadChunk:()=>Promise.resolve()},j=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/customers/[customerId]/page",pathname:"/customers/[customerId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var g=t(69094),v=t(5787),C=t(90527);let b=e=>e?JSON.parse(e):void 0,y=self.__BUILD_MANIFEST,N=b(self.__REACT_LOADABLE_MANIFEST),k=null==(s=self.__RSC_MANIFEST)?void 0:s["/(protected)/@admin/customers/[customerId]/page"],E=b(self.__RSC_SERVER_MANIFEST),L=b(self.__NEXT_FONT_MANIFEST),S=b(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];k&&E&&(0,v.Mo)({clientReferenceManifest:k,serverActionsManifest:E,serverModuleMap:(0,C.w)({serverActionsManifest:E,pageName:"/(protected)/@admin/customers/[customerId]/page"})});let M=(0,o.d)({pagesType:g.s.APP,dev:!1,page:"/(protected)/@admin/customers/[customerId]/page",appMod:null,pageMod:n,errorMod:null,error500Mod:null,Document:null,buildManifest:y,renderToHTML:i.f,reactLoadableManifest:N,clientReferenceManifest:k,serverActionsManifest:E,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:L,incrementalCacheHandler:null,interceptionRouteRewrites:S}),Z=n;function z(e){return(0,a.C)({...e,IncrementalCache:c.k,handler:M})}},1212:(e,r,t)=>{Promise.resolve().then(t.bind(t,49666))},44560:(e,r,t)=>{Promise.resolve().then(t.bind(t,93448))},49666:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v,runtime:()=>g});var s=t(60926),n=t(14579),a=t(30417),o=t(89551),c=t(53042),i=t(44788),l=t(38071),d=t(28531),u=t(5764),m=t(47020),p=t(737),x=t(64947);t(29220);var h=t(39228),f=t(32167),j=t(91500);let g="edge";function v({children:e}){let r=(0,x.UO)(),t=(0,x.lr)(),g=(0,x.tv)(),v=(0,x.jD)(),{t:C}=(0,h.$G)(),b=[{title:C("Account Details"),icon:(0,s.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/customers/${r?.customerId}?${t.toString()}`,id:"__DEFAULT__"},{title:C("Transactions"),icon:(0,s.jsx)(i.Z,{size:"24",variant:"Bulk"}),href:`/customers/${r?.customerId}/transactions?${t.toString()}`,id:"transactions"},{title:C("KYC"),icon:(0,s.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/customers/${r?.customerId}/kyc?${t.toString()}`,id:"kyc"},{title:C("Permissions"),icon:(0,s.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/customers/${r?.customerId}/permissions?${t.toString()}`,id:"permissions"},{title:C("Send Email"),icon:(0,s.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:`/customers/${r?.customerId}/send-email?${t.toString()}`,id:"send-email"}],y=1===Number(t.get("active"));return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,s.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,s.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,s.jsx)("li",{children:(0,s.jsxs)(p.Z,{href:"/customers",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,s.jsx)(m.Z,{className:"size-4 sm:size-6"}),C("Back")]})}),(0,s.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",t.get("name")]}),(0,s.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",C("User")," #",r.customerId]})]}),(0,s.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,s.jsx)("span",{children:C("Active")}),(0,s.jsx)(a.Z,{className:"data-[state=unchecked]:bg-muted",defaultChecked:y,onCheckedChange:e=>{f.toast.promise((0,o.z)(r.customerId),{loading:C("Loading..."),success:s=>{if(!s.status)throw Error(s.message);let n=new URLSearchParams(t);return n.set("active",e?"1":"0"),(0,j.j)(`/admin/customers/${r.customerId}`),g.push(`${v}?${n.toString()}`),s.message},error:e=>e.message})}})]})]}),(0,s.jsx)(n.a,{tabs:b})]}),e]})}},93448:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eE});var s=t(60926),n=t(29411),a=t(59571),o=t(1181),c=t(65091),i=t(90543),l=t(66277),d=t(53735),u=t(61394),m=t(29220),p=t(31036),x=t.n(p),h=["variant","color","size"],f=function(e){var r=e.color;return m.createElement(m.Fragment,null,m.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.38c0 2.81 1.29 4.74 3.56 5.47.66.23 1.42.34 2.25.34h8.38c.83 0 1.59-.11 2.25-.34C20.71 20.93 22 19 22 16.19V7.81C22 4.17 19.83 2 16.19 2Zm4.31 14.19c0 2.14-.84 3.49-2.53 4.05-.97-1.91-3.27-3.27-5.97-3.27-2.7 0-4.99 1.35-5.97 3.27h-.01c-1.67-.54-2.52-1.9-2.52-4.04V7.81c0-2.82 1.49-4.31 4.31-4.31h8.38c2.82 0 4.31 1.49 4.31 4.31v8.38Z",fill:r}),m.createElement("path",{d:"M12.002 8c-1.98 0-3.58 1.6-3.58 3.58s1.6 3.59 3.58 3.59 3.58-1.61 3.58-3.59c0-1.98-1.6-3.58-3.58-3.58Z",fill:r}))},j=function(e){var r=e.color;return m.createElement(m.Fragment,null,m.createElement("path",{d:"M18.14 21.619c-.88.26-1.92.38-3.14.38H9c-1.22 0-2.26-.12-3.14-.38.22-2.6 2.89-4.65 6.14-4.65 3.25 0 5.92 2.05 6.14 4.65Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.createElement("path",{d:"M2 12.94V15c0 3.78 1.14 5.85 3.86 6.62.22-2.6 2.89-4.65 6.14-4.65 3.25 0 5.92 2.05 6.14 4.65C20.86 20.85 22 18.78 22 15V9c0-5-2-7-7-7H9C4 2 2 4 2 9v3.94Zm10 1.23c-1.98 0-3.58-1.61-3.58-3.59C8.42 8.6 10.02 7 12 7s3.58 1.6 3.58 3.58-1.6 3.59-3.58 3.59Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.createElement("path",{d:"M15.582 10.58c0 1.98-1.6 3.59-3.58 3.59s-3.58-1.61-3.58-3.59c0-1.98 1.6-3.58 3.58-3.58s3.58 1.6 3.58 3.58Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},g=function(e){var r=e.color;return m.createElement(m.Fragment,null,m.createElement("path",{opacity:".4",d:"M22 7.81v8.38c0 2.81-1.29 4.74-3.56 5.47-.66.23-1.42.34-2.25.34H7.81c-.83 0-1.59-.11-2.25-.34C3.29 20.93 2 19 2 16.19V7.81C2 4.17 4.17 2 7.81 2h8.38C19.83 2 22 4.17 22 7.81Z",fill:r}),m.createElement("path",{d:"M18.439 21.659c-.66.23-1.42.34-2.25.34h-8.38c-.83 0-1.59-.11-2.25-.34.35-2.64 3.11-4.69 6.44-4.69 3.33 0 6.09 2.05 6.44 4.69ZM15.582 11.58c0 1.98-1.6 3.59-3.58 3.59s-3.58-1.61-3.58-3.59c0-1.98 1.6-3.58 3.58-3.58s3.58 1.6 3.58 3.58Z",fill:r}))},v=function(e){var r=e.color;return m.createElement(m.Fragment,null,m.createElement("path",{d:"M18.14 21.62c-.88.26-1.92.38-3.14.38H9c-1.22 0-2.26-.12-3.14-.38.22-2.6 2.89-4.65 6.14-4.65 3.25 0 5.92 2.05 6.14 4.65Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.createElement("path",{d:"M15 2H9C4 2 2 4 2 9v6c0 3.78 1.14 5.85 3.86 6.62.22-2.6 2.89-4.65 6.14-4.65 3.25 0 5.92 2.05 6.14 4.65C20.86 20.85 22 18.78 22 15V9c0-5-2-7-7-7Zm-3 12.17c-1.98 0-3.58-1.61-3.58-3.59C8.42 8.6 10.02 7 12 7s3.58 1.6 3.58 3.58-1.6 3.59-3.58 3.59Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.createElement("path",{d:"M15.58 10.58c0 1.98-1.6 3.59-3.58 3.59s-3.58-1.61-3.58-3.59C8.42 8.6 10.02 7 12 7s3.58 1.6 3.58 3.58Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},C=function(e){var r=e.color;return m.createElement(m.Fragment,null,m.createElement("path",{d:"M15 22.749H9c-1.32 0-2.42-.13-3.35-.41a.767.767 0 0 1-.54-.78c.25-2.99 3.28-5.34 6.89-5.34s6.63 2.34 6.89 5.34c.03.36-.19.68-.54.78-.93.28-2.03.41-3.35.41Zm-8.28-1.69c.66.13 1.41.19 2.28.19h6c.87 0 1.62-.06 2.28-.19-.53-1.92-2.72-3.34-5.28-3.34s-4.75 1.42-5.28 3.34Z",fill:r}),m.createElement("path",{d:"M15 2H9C4 2 2 4 2 9v6c0 3.78 1.14 5.85 3.86 6.62.22-2.6 2.89-4.65 6.14-4.65 3.25 0 5.92 2.05 6.14 4.65C20.86 20.85 22 18.78 22 15V9c0-5-2-7-7-7Zm-3 12.17c-1.98 0-3.58-1.61-3.58-3.59C8.42 8.6 10.02 7 12 7s3.58 1.6 3.58 3.58-1.6 3.59-3.58 3.59Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.createElement("path",{d:"M12.002 14.92a4.34 4.34 0 0 1-4.33-4.34c0-2.39 1.94-4.33 4.33-4.33s4.33 1.94 4.33 4.33a4.34 4.34 0 0 1-4.33 4.34Zm0-7.17a2.836 2.836 0 0 0 0 5.67 2.836 2.836 0 0 0 0-5.67Z",fill:r}))},b=function(e){var r=e.color;return m.createElement(m.Fragment,null,m.createElement("path",{opacity:".4",d:"M18.14 21.619c-.88.26-1.92.38-3.14.38H9c-1.22 0-2.26-.12-3.14-.38.22-2.6 2.89-4.65 6.14-4.65 3.25 0 5.92 2.05 6.14 4.65Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.createElement("path",{d:"M22 9v6c0 3.78-1.14 5.85-3.86 6.62-.88.26-1.92.38-3.14.38H9c-1.22 0-2.26-.12-3.14-.38C3.14 20.85 2 18.78 2 15V9c0-5 2-7 7-7h6c5 0 7 2 7 7Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.createElement("path",{opacity:".4",d:"M15.582 10.58c0 1.98-1.6 3.59-3.58 3.59s-3.58-1.61-3.58-3.59c0-1.98 1.6-3.58 3.58-3.58s3.58 1.6 3.58 3.58Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},y=function(e,r){switch(e){case"Bold":return m.createElement(f,{color:r});case"Broken":return m.createElement(j,{color:r});case"Bulk":return m.createElement(g,{color:r});case"Linear":default:return m.createElement(v,{color:r});case"Outline":return m.createElement(C,{color:r});case"TwoTone":return m.createElement(b,{color:r})}},N=(0,m.forwardRef)(function(e,r){var t=e.variant,s=e.color,n=e.size,a=(0,u._)(e,h);return m.createElement("svg",(0,u.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:n,height:n,viewBox:"0 0 24 24",fill:"none"}),y(t,s))});N.propTypes={variant:x().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:x().string,size:x().oneOfType([x().string,x().number])},N.defaultProps={variant:"Linear",color:"currentColor",size:"24"},N.displayName="UserSquare";var k=t(64947),E=t(39228),L=t(32898),S=t(58387),M=t(73806),Z=t(36162),z=t(34451),w=t(18662),I=t(66817),P=t(85430),A=t(92773),_=t(15487),F=t(14761),D=t(45475),T=t(32167),B=t(93633);let q=B.z.object({street:B.z.string({required_error:"Street is required."}),country:B.z.string({required_error:"Country is required."}),city:B.z.string({required_error:"city is required."}),zipCode:B.z.string({required_error:"Zip code is required."})});function W({customer:e,onMutate:r}){let[t,o]=m.useTransition(),[c,i]=m.useState(),{getCountryByCode:l}=(0,A.F)(),{t:d}=(0,E.$G)(),u=(0,D.cI)({resolver:(0,_.F)(q),defaultValues:{street:"",city:"",country:"",zipCode:""}});return(0,s.jsx)(z.l0,{...u,children:(0,s.jsx)("form",{onSubmit:u.handleSubmit(t=>{o(async()=>{let s=await (0,P.H)(t,e.id);s?.status?(r(),T.toast.success(s.message)):T.toast.error(d(s.message))})}),className:"rounded-xl border border-border bg-background",children:(0,s.jsxs)(a.Qd,{value:"ADDRESS_INFORMATION",className:"border-none px-4 py-0",children:[(0,s.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:d("Address")})}),(0,s.jsxs)(a.vF,{className:"flex flex-col gap-2 border-t px-1 pt-4",children:[(0,s.jsx)(I.Z,{children:d("Full mailing address")}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-2.5",children:[(0,s.jsx)(z.Wi,{control:u.control,name:"street",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:d("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:u.control,name:"country",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(M.g,{defaultValue:c,onSelectChange:r=>e.onChange(r.code.cca2)})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:u.control,name:"city",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12 md:col-span-6",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:d("City"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:u.control,name:"zipCode",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12 md:col-span-6",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:d("Zip code"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,s.jsx)(z.zG,{})]})})]}),(0,s.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,s.jsxs)(Z.z,{disabled:t,children:[(0,s.jsxs)(S.J,{condition:!t,children:[d("Save"),(0,s.jsx)(F.Z,{size:20})]}),(0,s.jsx)(S.J,{condition:t,children:(0,s.jsx)(n.Loader,{title:d("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}var R=t(48673),O=t(68870),H=t(47436),G=t(74988),V=t(86079),Q=t(28029),U=t(50684);function $({wallets:e,onMutate:r}){let{t}=(0,E.$G)();return(0,s.jsxs)(a.Qd,{value:"BALANCE",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,s.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:t("Balance")})}),(0,s.jsx)(a.vF,{className:"grid grid-cols-12 gap-4 border-t pt-4",children:e?.map(e=>s.jsx(J,{item:e,onMutate:r},e.id))})]})}function J({item:e,onMutate:r}){let{t}=(0,E.$G)();return(0,s.jsxs)("div",{className:"relative col-span-12 flex flex-col gap-2 rounded-xl border border-border bg-accent p-6 text-accent-foreground sm:col-span-6 md:col-span-4 lg:col-span-3",children:[(0,s.jsx)("div",{className:"absolute right-1 top-1 flex items-center gap-1",children:(0,s.jsxs)(H.h_,{children:[(0,s.jsx)(H.$F,{asChild:!0,children:(0,s.jsx)(Z.z,{variant:"ghost",size:"icon",className:"size-8 hover:bg-secondary-text/30 active:bg-secondary-text/50",children:(0,s.jsx)(U.Z,{strokeWidth:3,size:17})})}),(0,s.jsxs)(H.AW,{className:"flex flex-col rounded-sm",align:"end",children:[(0,s.jsx)(Y,{wallet:e,userId:e?.userId,onMutate:r}),(0,s.jsx)(X,{wallet:e,userId:e?.userId,onMutate:r}),(0,s.jsx)(K,{wallet:e,onMutate:r})]})]})}),(0,s.jsx)("span",{className:"text-xs font-normal leading-4",children:e.currency.code}),(0,s.jsxs)("h6",{className:"text-sm font-semibold leading-5",children:[e.balance," ",e.currency.code]}),e?.dailyTransferAmount?(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsxs)("span",{className:"text-xs font-normal leading-4",children:[t("Daily transfer limit"),":"]}),(0,s.jsxs)("h6",{className:"text-xs font-normal leading-4",children:[e?.dailyTransferAmount," ",e.currency.code]})]}):null]})}function Y({userId:e,wallet:r,onMutate:t}){let[a,o]=m.useState(!1),[c,i]=m.useState(!1),{t:l}=(0,E.$G)(),[d,u]=m.useState({amount:"0",currencyCode:r?.currency.code,userId:e,keepRecords:!0}),p=()=>{u({amount:"0",currencyCode:r?.currency.code,userId:e,keepRecords:!0})},x=async e=>{e.preventDefault(),i(!0);let r=await (0,V.y)({amount:Number(d.amount),currencyCode:d.currencyCode,userId:d.userId,keepRecords:d.keepRecords},"add");r.status?(T.toast.success(r.message),t(),i(!1),o(!1)):(T.toast.error(r.message),i(!1))};return(0,s.jsxs)(O.Vq,{open:a,onOpenChange:e=>{o(e),p()},children:[(0,s.jsx)(O.hg,{asChild:!0,children:(0,s.jsx)(Z.z,{variant:"ghost",className:"relative flex h-8 cursor-pointer select-none items-center justify-start rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",children:l("Add balance")})}),(0,s.jsxs)(O.cZ,{children:[(0,s.jsxs)(O.fK,{children:[(0,s.jsx)(O.$N,{className:"text-semibold",children:l("Add Balance")}),(0,s.jsx)(O.Be,{className:"hidden"})]}),(0,s.jsx)(G.Z,{}),(0,s.jsx)("div",{children:(0,s.jsxs)("form",{onSubmit:x,className:"flex flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsxs)(I.Z,{className:"text-sm",children:[" ",l("Balance")," "]}),(0,s.jsx)(w.I,{type:"number",value:d.amount,min:0,onChange:e=>u(r=>({...r,amount:e.target.value}))})]}),(0,s.jsxs)(I.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,s.jsx)(R.X,{checked:d.keepRecords,onCheckedChange:e=>u(r=>({...r,keepRecords:e}))}),(0,s.jsx)("span",{children:l("Keep in record")})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,s.jsx)(O.GG,{asChild:!0,children:(0,s.jsx)(Z.z,{type:"button",variant:"ghost",children:"Cancel"})}),(0,s.jsx)(Z.z,{disabled:c,children:c?(0,s.jsx)(n.Loader,{title:l("Uploading..."),className:"text-primary-foreground"}):l("Update")})]})]})})]})]})}function X({userId:e,wallet:r,onMutate:t}){let[a,o]=m.useState(!1),[c,i]=m.useState(!1),{t:l}=(0,E.$G)(),[d,u]=m.useState({amount:"0",currencyCode:r?.currency.code,userId:e,keepRecords:!0}),p=()=>{u({amount:"0",currencyCode:r?.currency.code,userId:e,keepRecords:!0})},x=async e=>{e.preventDefault(),o(!0);let r=await (0,V.y)({amount:Number(d.amount),currencyCode:d.currencyCode,userId:d.userId,keepRecords:d.keepRecords},"remove");r.status?(p(),t(),i(!1),o(!1),T.toast.success(r.status)):(o(!1),T.toast.error(r.status))};return(0,s.jsxs)(O.Vq,{open:c,onOpenChange:e=>{i(e),p()},children:[(0,s.jsx)(O.hg,{asChild:!0,children:(0,s.jsx)(Z.z,{variant:"ghost",className:"relative flex h-8 cursor-pointer select-none items-center justify-start rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",children:l("Remove balance")})}),(0,s.jsxs)(O.cZ,{children:[(0,s.jsxs)(O.fK,{children:[(0,s.jsx)(O.$N,{className:"text-semibold",children:l("Remove Balance")}),(0,s.jsx)(O.Be,{className:"hidden"})]}),(0,s.jsx)(G.Z,{}),(0,s.jsx)("div",{children:(0,s.jsxs)("form",{onSubmit:x,className:"flex flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsxs)(I.Z,{className:"text-sm",children:[" ",l("Balance")," "]}),(0,s.jsx)(w.I,{type:"number",value:d.amount,min:0,onChange:e=>u(r=>({...r,amount:e.target.value}))})]}),(0,s.jsxs)(I.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,s.jsx)(R.X,{checked:d.keepRecords,onCheckedChange:e=>u(r=>({...r,keepRecords:e}))}),(0,s.jsx)("span",{children:l("Keep in record")})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,s.jsx)(O.GG,{asChild:!0,children:(0,s.jsx)(Z.z,{type:"button",variant:"ghost",children:l("Cancel")})}),(0,s.jsx)(Z.z,{disabled:a,children:a?(0,s.jsx)(n.Loader,{title:l("Uploading..."),className:"text-primary-foreground"}):l("Update")})]})]})})]})]})}function K({wallet:e,onMutate:r}){let[t,a]=m.useState(!1),[o,c]=m.useState(!1),{t:i}=(0,E.$G)(),[l,d]=m.useState(e?.dailyTransferAmount),u=()=>{d(l||0)},p=async t=>{t.preventDefault(),a(!0);let s={dailyTransferAmount:Number(l)},n=await (0,Q.I)(s,e?.id);n.status?(u(),r(),c(!1),a(!1),r(),T.toast.success(n.status)):(a(!1),T.toast.error(n.status))};return(0,s.jsxs)(O.Vq,{open:o,onOpenChange:e=>{c(e),u()},children:[(0,s.jsx)(O.hg,{asChild:!0,children:(0,s.jsx)(Z.z,{variant:"ghost",className:"relative flex h-8 cursor-pointer select-none items-center justify-start rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",children:i("Transfer limit")})}),(0,s.jsxs)(O.cZ,{children:[(0,s.jsxs)(O.fK,{children:[(0,s.jsx)(O.$N,{className:"text-semibold flex items-center gap-4",children:i("Transfer amount limit")}),(0,s.jsx)(O.Be,{className:"hidden"})]}),(0,s.jsx)(G.Z,{}),(0,s.jsx)("div",{children:(0,s.jsxs)("form",{onSubmit:p,className:"flex flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsxs)(I.Z,{className:"text-sm",children:[" ",i("Daily transfer amount")," "]}),(0,s.jsx)(w.I,{type:"string",value:l,min:0,onChange:e=>d(e.target.value)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,s.jsx)(O.GG,{asChild:!0,children:(0,s.jsx)(Z.z,{type:"button",variant:"ghost",children:i("Cancel")})}),(0,s.jsx)(Z.z,{disabled:t,children:t?(0,s.jsx)(n.Loader,{title:i("Uploading..."),className:"text-primary-foreground"}):i("Update")})]})]})})]})]})}var ee=t(80317);function er({className:e}){return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"37",height:"37",viewBox:"0 0 37 37",fill:"none",className:(0,c.ZP)("fill-[#E04242]",e),children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M28.1625 18.712L20.0806 13.7707C19.5225 13.4222 18.8592 13.2824 18.208 13.376C17.5568 13.4696 16.9597 13.7907 16.5224 14.2823L14.976 15.9878C14.8404 16.1373 14.6701 16.2511 14.4802 16.3191C14.2902 16.3872 14.0864 16.4074 13.8868 16.378C13.6872 16.3486 13.4978 16.2704 13.3356 16.1505C13.1733 16.0306 13.0431 15.8725 12.9564 15.6903C12.9233 15.6204 12.8903 15.5462 12.8574 15.4677L15.9665 11.7763C16.1683 11.5269 16.425 11.3276 16.7165 11.1939C17.0081 11.0601 17.3266 10.9956 17.6473 11.0053L25.3485 11.087L29.3086 17.9455L28.1628 18.712H28.1625ZM28.5711 21.6074C28.7221 21.3579 28.7686 21.0589 28.7002 20.7754C28.6318 20.4919 28.4542 20.2469 28.2061 20.0937L19.4775 14.757C19.1484 14.5515 18.7572 14.469 18.3731 14.5242C17.9891 14.5794 17.6369 14.7687 17.3791 15.0587L15.8326 16.7647C15.5694 17.0549 15.2389 17.2758 14.8701 17.4079C14.5013 17.54 14.1056 17.5792 13.718 17.5221C13.3305 17.4649 12.963 17.3132 12.648 17.0802C12.333 16.8473 12.0803 16.5404 11.9121 16.1866C11.8513 16.0583 11.7911 15.9192 11.7333 15.7723C11.6698 15.615 11.6507 15.4432 11.6781 15.2758C11.7055 15.1084 11.7784 14.9517 11.8888 14.8229L13.4522 12.9666L11.2064 11.8475L7.39269 18.4526L8.33308 18.9592L8.53065 18.7614C8.81909 18.4726 9.18046 18.2675 9.57628 18.1679C9.97209 18.0683 10.3875 18.0779 10.7783 18.1958C11.169 18.3137 11.5205 18.5353 11.7952 18.8372C12.0699 19.1391 12.2576 19.5098 12.3383 19.9099C12.7562 19.7218 13.2214 19.6651 13.6723 19.7474C14.1232 19.8297 14.5384 20.0471 14.8629 20.3707C15.0729 20.58 15.2392 20.8289 15.3524 21.1029C15.4655 21.377 15.5232 21.6707 15.5221 21.9672C15.522 22.0931 15.5114 22.2188 15.4905 22.343C15.8096 22.3574 16.122 22.4395 16.407 22.5838C16.6919 22.7281 16.943 22.9313 17.1435 23.18C17.344 23.4287 17.4893 23.7171 17.5698 24.0262C17.6504 24.3353 17.6643 24.658 17.6107 24.9729C17.6652 24.9756 17.7197 24.9807 17.7741 24.9874C17.7852 24.8876 17.822 24.7925 17.881 24.7114C17.94 24.6302 18.0192 24.5658 18.1106 24.5245C18.2021 24.4831 18.3027 24.4663 18.4026 24.4757C18.5025 24.4851 18.5983 24.5203 18.6804 24.5779L20.7682 26.0403L21.3722 26.4074C21.5291 26.5059 21.7102 26.5592 21.8955 26.5613C22.0808 26.5635 22.2631 26.5144 22.4223 26.4196C22.822 26.2021 23.108 25.7736 23.1179 25.3775C23.1265 25.0366 22.9359 24.7441 22.5513 24.5093C22.5513 24.5093 22.5513 24.5093 22.5506 24.5083L19.4643 22.6214C19.3992 22.582 19.3425 22.5301 19.2974 22.4687C19.2524 22.4073 19.22 22.3376 19.202 22.2636C19.184 22.1896 19.1808 22.1128 19.1925 22.0376C19.2043 21.9624 19.2308 21.8902 19.2705 21.8252C19.3102 21.7603 19.3624 21.7038 19.424 21.6591C19.4856 21.6143 19.5554 21.5822 19.6295 21.5645C19.7035 21.5468 19.7803 21.544 19.8555 21.5561C19.9307 21.5681 20.0027 21.595 20.0675 21.635L24.2558 24.1961C24.505 24.3485 24.8044 24.3957 25.0884 24.3272C25.3723 24.2588 25.6174 24.0804 25.7698 23.8312C25.9221 23.582 25.9693 23.2826 25.9008 22.9986C25.8324 22.7147 25.654 22.4696 25.4048 22.3172L20.7769 19.4877C20.6476 19.4071 20.5555 19.2786 20.5204 19.1302C20.4854 18.9819 20.5103 18.8258 20.5898 18.6957C20.6693 18.5657 20.7969 18.4723 20.9449 18.4359C21.0929 18.3995 21.2493 18.423 21.3801 18.5013L26.0068 21.3298L26.0076 21.3305C26.0082 21.3305 26.0087 21.3315 26.009 21.3315L27.0573 21.9725C27.3068 22.1236 27.6058 22.17 27.8894 22.1016C28.1729 22.0332 28.4179 21.8556 28.5711 21.6074ZM20.2492 28.3281C20.1313 28.5205 19.9572 28.6721 19.7505 28.7623C19.5437 28.8526 19.3141 28.8771 19.0929 28.8326L19.1022 28.8231C19.3122 28.6138 19.4786 28.3649 19.5917 28.0909C19.7049 27.8169 19.7625 27.5231 19.7614 27.2267C19.7614 27.0507 19.741 26.8754 19.7008 26.7041L20.082 26.9709C20.2565 27.1464 20.3677 27.3751 20.398 27.6208C20.4283 27.8666 20.3759 28.1154 20.2492 28.3281ZM16.0749 29.7569C15.8571 29.7577 15.644 29.6936 15.4627 29.573C15.2814 29.4524 15.14 29.2806 15.0566 29.0794C14.9731 28.8783 14.9513 28.6569 14.994 28.4433C15.0366 28.2297 15.1418 28.0337 15.2962 27.88L15.2968 27.879L16.7271 26.4492C16.934 26.2449 17.2134 26.1308 17.5041 26.1317C17.7949 26.1327 18.0735 26.2486 18.2791 26.4542C18.4847 26.6598 18.6007 26.9384 18.6016 27.2291C18.6026 27.5199 18.4885 27.7993 18.2842 28.0062L16.8533 29.4374C16.7512 29.5396 16.6297 29.6205 16.4961 29.6754C16.3624 29.7303 16.2192 29.7582 16.0747 29.7574L16.0749 29.7569ZM12.6664 27.3166C12.4603 27.11 12.3444 26.8301 12.3444 26.5383C12.3443 26.2465 12.4599 25.9665 12.666 25.7598L14.607 23.819H14.6075C14.7097 23.7167 14.831 23.6356 14.9645 23.5803C15.0981 23.5249 15.2412 23.4964 15.3858 23.4964C15.5303 23.4964 15.6735 23.5248 15.807 23.5801C15.9406 23.6354 16.062 23.7164 16.1642 23.8186C16.2665 23.9208 16.3476 24.0422 16.4029 24.1757C16.4583 24.3092 16.4868 24.4524 16.4868 24.5969C16.4868 24.7415 16.4584 24.8846 16.4031 25.0182C16.3478 25.1518 16.2668 25.2731 16.1646 25.3754L15.9096 25.6305L14.2237 27.3166C14.0171 27.5229 13.737 27.6388 13.445 27.6388C13.1531 27.6388 12.873 27.5229 12.6664 27.3166ZM10.0365 25.1969C9.83033 24.9904 9.71452 24.7104 9.71452 24.4186C9.71452 24.1267 9.83033 23.8468 10.0365 23.6402L10.7939 22.8821C10.7966 22.8801 10.7994 22.8771 10.802 22.8749C10.8034 22.8735 10.8044 22.8718 10.8058 22.8705L11.7183 21.9577C11.7197 21.9563 11.7214 21.9557 11.7227 21.9543C11.7254 21.9513 11.728 21.9482 11.7308 21.9455L12.4878 21.1882C12.6947 20.9834 12.9743 20.8689 13.2654 20.8697C13.5564 20.8705 13.8353 20.9865 14.0411 21.1923C14.2469 21.3982 14.3629 21.6771 14.3636 21.9682C14.3643 22.2593 14.2497 22.5388 14.045 22.7456L11.5936 25.1968C11.387 25.4029 11.107 25.5187 10.8151 25.5187C10.5232 25.5187 10.2432 25.4029 10.0365 25.1968V25.1969ZM8.42724 20.4997L9.34791 19.5791C9.55381 19.3721 9.8335 19.2554 10.1255 19.2546C10.4174 19.2538 10.6977 19.3691 10.9047 19.575C11.1117 19.7809 11.2284 20.0606 11.2292 20.3525C11.23 20.6445 11.1147 20.9248 10.9088 21.1318L9.97987 22.0617C9.7728 22.2676 9.49243 22.3828 9.20043 22.3819C8.90844 22.3811 8.62874 22.2643 8.42287 22.0572C8.217 21.8501 8.10182 21.5697 8.10266 21.2778C8.10351 20.9858 8.22032 20.7061 8.42739 20.5002L8.42724 20.4997ZM31.5329 6.37782C31.4562 6.24504 31.33 6.14814 31.1819 6.10843C31.0338 6.06872 30.876 6.08946 30.7432 6.16609L25.067 9.4434C24.9818 9.49223 24.9105 9.56211 24.8601 9.64635C24.8096 9.7306 24.7816 9.82639 24.7788 9.92455L17.6595 9.84925C17.1682 9.83704 16.6806 9.93723 16.2338 10.1422C15.7871 10.3471 15.3931 10.6514 15.0818 11.0317L14.2175 12.0579C14.2103 12.0538 14.2027 12.0491 14.1948 12.0453L11.7854 10.8445L12.145 10.2216C12.2216 10.0888 12.2423 9.931 12.2026 9.78293C12.1629 9.63486 12.066 9.50863 11.9333 9.43199L6.25703 6.15452C6.19126 6.11657 6.11865 6.09195 6.04335 6.08207C5.96806 6.07218 5.89156 6.07723 5.81821 6.09692C5.74487 6.11661 5.67613 6.15056 5.6159 6.19682C5.55568 6.24308 5.50517 6.30076 5.46724 6.36655L0.0775274 15.7017C0.000998188 15.8344 -0.0197106 15.9921 0.0199506 16.1401C0.0596118 16.2881 0.156399 16.4143 0.289049 16.491L5.96522 19.7683C6.03098 19.8062 6.10357 19.8309 6.17885 19.8408C6.25413 19.8507 6.33062 19.8457 6.40396 19.826C6.4773 19.8064 6.54605 19.7725 6.60629 19.7262C6.66652 19.68 6.71706 19.6224 6.75502 19.5566L6.81384 19.4545L7.48634 19.8168C7.11961 20.2491 6.92869 20.8035 6.95145 21.3699C6.9742 21.9363 7.20897 22.4736 7.6092 22.8751C7.92237 23.1896 8.32218 23.4036 8.75757 23.4899C8.60201 23.8335 8.53516 24.2107 8.56315 24.5869C8.59113 24.963 8.71306 25.3261 8.91776 25.643C9.12246 25.9598 9.40339 26.2202 9.73481 26.4003C10.0662 26.5804 10.4376 26.6745 10.8148 26.6739C10.9407 26.6735 11.0663 26.663 11.1905 26.6424C11.2051 26.9615 11.2873 27.2738 11.4317 27.5587C11.5762 27.8436 11.7795 28.0946 12.0282 28.2949C12.2769 28.4953 12.5654 28.6406 12.8745 28.721C13.1836 28.8015 13.5063 28.8153 13.8211 28.7617C13.8418 29.1984 13.9889 29.6197 14.2444 29.9745C14.4999 30.3293 14.853 30.6023 15.2606 30.7603C15.6683 30.9183 16.1131 30.9546 16.541 30.8647C16.9689 30.7748 17.3615 30.5626 17.6711 30.254L18.2043 29.7206C18.5402 29.911 18.9198 30.0112 19.306 30.0114C19.6931 30.0118 20.0739 29.9127 20.4118 29.7236C20.7496 29.5346 21.0333 29.2619 21.2355 28.9317C21.4628 28.5588 21.5759 28.1274 21.5607 27.6908C22.0473 27.7667 22.5454 27.6769 22.9749 27.4357C23.7321 27.0231 24.2371 26.244 24.2714 25.4426C24.6074 25.5288 24.9587 25.5364 25.298 25.4647C25.6374 25.393 25.9556 25.244 26.228 25.0292C26.5003 24.8144 26.7194 24.5396 26.8682 24.2263C27.017 23.913 27.0915 23.5696 27.0859 23.2228C27.262 23.2667 27.4428 23.2893 27.6244 23.29C27.805 23.2897 27.985 23.2681 28.1605 23.2255C28.5531 23.1303 28.913 22.9315 29.2026 22.6498C29.4922 22.3681 29.701 22.0139 29.8071 21.6241C29.9132 21.2343 29.9128 20.8231 29.8059 20.4335C29.6991 20.0439 29.4896 19.6901 29.1995 19.409L29.8878 18.9489L30.2451 19.5678C30.3218 19.7006 30.4481 19.7974 30.5962 19.8371C30.7443 19.8768 30.9021 19.8561 31.035 19.7796L36.7109 16.5027C36.7767 16.4648 36.8343 16.4142 36.8805 16.354C36.9267 16.2937 36.9606 16.2249 36.9802 16.1516C36.9998 16.0782 37.0048 16.0018 36.9949 15.9265C36.985 15.8512 36.9603 15.7786 36.9223 15.7129L31.5329 6.37782Z"})})}var et=t(25694);async function es(e,r){try{let t=await o.Z.put(`/admin/customers/convert-account/${r}`,e);return(0,et.B)(t)}catch(e){return(0,et.D)(e)}}var en=t(47020);let ea=B.z.object({roleId:B.z.number().optional(),name:B.z.string({required_error:"Agent name is required."}),occupation:B.z.string({required_error:"Occupation is required."}),whatsapp:B.z.string({required_error:"Whatsapp number/link is required."})});function eo({customer:e}){let[r,t]=m.useTransition(),[a,o]=m.useState(!1),{t:c}=(0,E.$G)(),i=(0,D.cI)({resolver:(0,_.F)(ea),defaultValues:{roleId:4,name:"",occupation:"",whatsapp:""}});return(0,s.jsxs)("div",{className:"flex flex-1 flex-col items-center rounded-xl border border-border bg-background px-6 py-4 sm:w-[300px]",children:[(0,s.jsx)("div",{className:"mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-spacial-red-foreground/50",children:(0,s.jsx)(er,{})}),(0,s.jsx)(G.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,s.jsx)("div",{className:"mt-2 px-2",children:(0,s.jsxs)(O.Vq,{open:a,onOpenChange:o,children:[(0,s.jsx)(O.hg,{disabled:e?.roleId===4,asChild:!0,children:(0,s.jsxs)(Z.z,{className:"rounded-xl",children:[c("Convert to Agent"),(0,s.jsx)(F.Z,{size:16})]})}),(0,s.jsxs)(O.cZ,{className:"flex max-w-[716px] flex-col gap-6 p-16",children:[(0,s.jsxs)(O.fK,{className:"p-0",children:[(0,s.jsx)(O.$N,{className:"text-[32px] font-medium leading-10",children:c("Add agent information")}),(0,s.jsx)(O.Be,{className:"hidden","aria-hidden":!0,children:c("dialog description")})]}),(0,s.jsx)(G.Z,{}),(0,s.jsx)(z.l0,{...i,children:(0,s.jsxs)("form",{onSubmit:i.handleSubmit(r=>{t(async()=>{let t=await es({roleId:r.roleId,agent:r},e.id);t?.status?(o(!1),T.toast.success(t.message)):T.toast.error(c(t.message))})}),className:"flex flex-col gap-y-6",children:[(0,s.jsx)(z.Wi,{name:"name",control:i.control,render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(w.I,{type:"hidden","aria-hidden":!0,placeholder:c("Enter agent name"),...e}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{name:"name",control:i.control,render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:"Agent Name"}),(0,s.jsx)(w.I,{type:"text",placeholder:c("Enter agent name"),...e}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{name:"occupation",control:i.control,render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:c("Job/Occupation")}),(0,s.jsx)(w.I,{type:"text",placeholder:c("Enter your job/occupation"),...e}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{name:"whatsapp",control:i.control,render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:c("WhatsApp number/link")}),(0,s.jsx)(w.I,{type:"text",placeholder:c("Enter your WhatsApp account number or link"),...e}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(Z.z,{onClick:()=>o(!1),type:"button",variant:"outline",children:[(0,s.jsx)(en.Z,{}),c("Back")]}),(0,s.jsxs)(Z.z,{className:"w-[286px]",disabled:r,children:[(0,s.jsxs)(S.J,{condition:!r,children:[c("Convert"),(0,s.jsx)(F.Z,{})]}),(0,s.jsx)(S.J,{condition:r,children:(0,s.jsx)(n.Loader,{title:c("Converting..."),className:"text-primary-foreground"})})]})]})]})})]})]})})]})}var ec=t(18001);function ei(){let{t:e}=(0,E.$G)();return(0,s.jsxs)("div",{className:"flex flex-1 flex-col items-center rounded-xl border border-border bg-background px-6 py-4",children:[(0,s.jsx)("div",{className:"mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-selected text-primary",children:(0,s.jsx)(ec.Z,{variant:"Bold",size:32})}),(0,s.jsx)(G.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,s.jsx)("div",{className:"mt-2 px-2",children:(0,s.jsxs)(Z.z,{variant:"secondary",disabled:!0,className:"rounded-xl",children:[e("Convert to Customer"),(0,s.jsx)(F.Z,{size:16})]})})]})}function el({className:e}){return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"34",height:"34",viewBox:"0 0 34 34",fill:"none",className:(0,c.ZP)("fill-[#09A7FF]",e),children:(0,s.jsx)("path",{d:"M26.8317 25.452H13.1851L12.3417 22.0782H30.1758L34 6.78136H8.51752L7.17339 1.40479H0V3.40753H5.60966L11.1408 25.5319C9.53061 25.8777 8.31969 27.3116 8.31969 29.0236C8.31969 30.9929 9.92187 32.5951 11.8913 32.5951C13.8607 32.5951 15.4629 30.9928 15.4629 29.0236C15.4629 28.461 15.3317 27.9286 15.099 27.4547H23.624C23.3912 27.9286 23.2601 28.461 23.2601 29.0236C23.2601 30.9929 24.8623 32.5951 26.8317 32.5951C28.801 32.5951 30.4032 30.9928 30.4032 29.0236C30.4032 27.0542 28.8009 25.452 26.8317 25.452Z"})})}var ed=t(5670);B.z.object({firstName:B.z.string().min(1,"First name is required."),lastName:B.z.string().min(1,"Last name is required."),email:B.z.string().email({message:"Invalid email address."}),phone:B.z.string().min(1,"Phone number is required."),password:B.z.string({required_error:"Password is required"}).min(8,"Your password must be at least 8 characters long"),confirmPassword:B.z.string({required_error:"Confirm password is required"}).min(8,"Password is required."),referralCode:B.z.string().optional(),termAndCondition:B.z.literal(!0,{errorMap:()=>({message:"You must accept our terms & conditions"})})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),B.z.object({title:B.z.string().min(1,"Title is required."),dateOfBirth:B.z.date({required_error:"A date of birth is required."}),street:B.z.string().min(1,"Street is required."),country:B.z.string().min(1,"Country is required."),city:B.z.string().min(1,"City is required."),zipCode:B.z.string().min(1,"Zip code is required.")});let eu=B.z.object({name:B.z.string({required_error:"Full name is required."}).min(1,"Full name is required."),email:B.z.string({required_error:"Email address is required."}).email({message:"Invalid email address."}),license:B.z.string().min(1,"Merchant license is required."),street:B.z.string({required_error:"Street is required"}).min(1,"Street is required."),country:B.z.string({required_error:"Country is required"}).min(1,"Country is required."),city:B.z.string({required_error:"City is required"}).min(1,"City is required."),zipCode:B.z.string({required_error:"Zip code is required"}).min(1,"Zip code is required.")});B.z.object({name:B.z.string({required_error:"Full name is required."}).min(1,"Full name is required."),occupation:B.z.string({required_error:"Occupation is required."}).min(1,"Occupation is required."),whatsapp:B.z.string({required_error:"WhatsApp link is required."}).min(1,"WhatsApp link is required.")});var em=t(66697);function ep({onPrev:e,onSubmit:r,nextButtonLabel:t,isLoading:a=!1}){let{t:o}=(0,E.$G)(),c=(0,D.cI)({resolver:(0,_.F)(eu),defaultValues:{name:"",email:"",license:"",street:"",country:"",city:"",zipCode:""}});return(0,s.jsx)(z.l0,{...c,children:(0,s.jsx)("form",{onSubmit:c.handleSubmit(r),children:(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsx)(z.Wi,{control:c.control,name:"name",render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:o("Merchant name")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",className:"placeholder:font-normal",placeholder:o("Enter merchant name"),...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:c.control,name:"email",render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:o("Merchant email")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"email",placeholder:o("Enter your merchant email address"),...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:c.control,name:"license",render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsxs)(z.lX,{className:"flex items-center justify-between gap-4",children:[o("Merchant proof"),(0,s.jsxs)(O.Vq,{children:[(0,s.jsx)(O.hg,{className:"inline-flex items-center gap-1",asChild:!0,children:(0,s.jsxs)(Z.z,{type:"button",variant:"ghost",size:"sm",children:[o("Help"),(0,s.jsx)(em.Z,{size:16})]})}),(0,s.jsxs)(O.cZ,{children:[(0,s.jsxs)(O.fK,{children:[(0,s.jsx)(O.$N,{children:o("Dialog title")}),(0,s.jsx)(O.Be,{children:o("Make changes to your profile here. Click save when youre done.")})]}),(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"flex aspect-video w-full items-center justify-center rounded-lg bg-neutral-200",children:(0,s.jsx)(ed.X,{})})})]})]})]}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:o("Enter merchant license or register number"),...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,s.jsx)(I.Z,{className:"col-span-12",children:o("Merchant address")}),(0,s.jsx)(z.Wi,{control:c.control,name:"street",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:o("Address Line"),...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:c.control,name:"country",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(M.g,{onSelectChange:r=>e.onChange(r.code.cca2)})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:c.control,name:"city",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-6",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:o("City"),...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:c.control,name:"zipCode",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-6",children:[(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:o("Zip Code"),...e})}),(0,s.jsx)(z.zG,{})]})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,s.jsxs)(Z.z,{className:"p-4 text-base leading-[22px]",variant:"outline",type:"button",onClick:e,children:[(0,s.jsx)(en.Z,{size:24}),o("Back")]}),(0,s.jsxs)(Z.z,{type:"submit",disabled:a,className:"w-[286px] p-4 text-base leading-[22px]",children:[(0,s.jsxs)(S.J,{condition:!a,children:[t,(0,s.jsx)(F.Z,{size:16})]}),(0,s.jsx)(S.J,{condition:a,children:(0,s.jsx)(n.Loader,{className:"text-background"})})]})]})]})})})}function ex({customer:e}){let[r,t]=m.useTransition(),[n,a]=m.useState(!1),{t:o}=(0,E.$G)();return(0,s.jsxs)("div",{className:"flex flex-1 flex-col items-center rounded-xl border border-border bg-background px-6 py-4",children:[(0,s.jsx)("div",{className:"mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-spacial-blue-foreground/50",children:(0,s.jsx)(el,{})}),(0,s.jsx)(G.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,s.jsx)("div",{className:"mt-2 px-2",children:(0,s.jsxs)(O.Vq,{open:n,onOpenChange:a,children:[(0,s.jsx)(O.hg,{asChild:!0,children:(0,s.jsxs)(Z.z,{className:"rounded-xl",children:[o("Convert to Merchant"),(0,s.jsx)(F.Z,{size:16})]})}),(0,s.jsxs)(O.cZ,{className:"flex max-h-[90%] max-w-[716px] flex-col gap-6 p-0",children:[(0,s.jsx)(O.fK,{className:"px-16 pb-0 pt-16",children:(0,s.jsx)(O.$N,{className:"text-[32px] font-medium leading-10",children:o("Add merchant information")})}),(0,s.jsx)(G.Z,{className:"mx-16"}),(0,s.jsx)("div",{className:"h-auto overflow-y-auto px-16 pb-16 pt-0",children:(0,s.jsx)(ep,{onPrev:()=>{a(!1)},isLoading:r,onSubmit:r=>{t(async()=>{let t=await es({roleId:3,merchant:{name:r.name,email:r.email,proof:r.license,addressLine:r.street,zipCode:r.zipCode,countryCode:r.country,city:r.city}},e.id);t?.status?(a(!1),T.toast.success(t.message)):T.toast.error(o(t.message))})},nextButtonLabel:"Convert"})})]})]})})]})}function eh({customer:e}){let{t:r}=(0,E.$G)();return(0,s.jsxs)(a.Qd,{value:"ConvertAccountType",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,s.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:r("Convert account type")})}),(0,s.jsxs)(a.vF,{className:"flex flex-col gap-4 border-t p-[1px] py-4",children:[(0,s.jsxs)(ee.bZ,{className:"border-none bg-transparent shadow-default",children:[(0,s.jsx)(N,{color:"#0B6A0B",variant:"Bulk",className:"-mt-1"}),(0,s.jsx)(ee.Cd,{className:"pl-2 text-sm font-semibold leading-5",children:r("This is a Customer Account")}),(0,s.jsx)(ee.X,{className:"pl-2 text-sm font-normal",children:r("You will need to add additional information to convert this account into a Merchant of Agent.")})]}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-y-4 sm:gap-4",children:[(0,s.jsx)(ei,{}),(0,s.jsx)(eo,{customer:e}),(0,s.jsx)(ex,{customer:e})]})]})]})}var ef=t(93739),ej=t(7602),eg=t(82944),ev=t(26734),eC=t(12403),eb=t(72382);let ey=B.z.object({profile:eb.K,firstName:B.z.string({required_error:"Full name is required."}),lastName:B.z.string({required_error:"Full name is required."}),email:B.z.string({required_error:"Email is required."}),phone:B.z.string({required_error:"Phone is required."}),dateOfBirth:B.z.date({required_error:"Date of Birth is required."}),gender:B.z.string({required_error:"Gender is required"})});function eN({customer:e,onMutate:r,isLoading:t=!1}){let[o,i]=(0,m.useTransition)(),{t:l}=(0,E.$G)(),d=(0,D.cI)({resolver:(0,_.F)(ey),defaultValues:{profile:"",firstName:"",lastName:"",email:"",phone:"",dateOfBirth:void 0,gender:""}});return(0,m.useCallback)(()=>{e&&d.reset({firstName:e?.firstName,lastName:e?.lastName,email:e?.user?.email,phone:e?.phone,dateOfBirth:new Date(e?.dob),gender:e.gender})},[t]),(0,s.jsx)(z.l0,{...d,children:(0,s.jsx)("form",{onSubmit:d.handleSubmit(t=>{i(async()=>{let s=await (0,eC.n)(t,e.id);s?.status?(r(),T.toast.success(s.message)):T.toast.error(l(s.message))})}),className:"rounded-xl border border-border bg-background",children:(0,s.jsxs)(a.Qd,{value:"PROFILE_INFORMATION",className:"border-none px-4 py-0",children:[(0,s.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:l("Profile")})}),(0,s.jsxs)(a.vF,{className:"flex flex-col gap-6 border-t px-1 py-4",children:[(0,s.jsx)(z.Wi,{control:d.control,name:"profile",render:({field:r})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:l("Profile picture")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(ej.S,{id:"documentFrontSideFile",defaultValue:(0,c.qR)(e?.profileImage),onChange:e=>{r.onChange(e)},className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,s.jsx)(ed.X,{}),(0,s.jsx)("p",{className:"text-sm font-normal text-primary",children:l("Upload photo")})]})})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,s.jsx)(z.Wi,{control:d.control,name:"firstName",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(z.lX,{children:l("First name")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:l("First name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:d.control,name:"lastName",render:({field:e})=>(0,s.jsxs)(z.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(z.lX,{children:l("Last name")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"text",placeholder:l("Last name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,s.jsx)(z.zG,{})]})})]}),(0,s.jsx)(z.Wi,{control:d.control,name:"email",render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:l("Email")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(w.I,{type:"email",placeholder:l("Enter your email"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:d.control,name:"phone",render:({field:r})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:l("Phone")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(eg.E,{value:e?.phone,onChange:r.onChange,inputClassName:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",onBlur:e=>{e?d.setError("phone",{type:"custom",message:l(e)}):d.clearErrors("phone")}})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:d.control,name:"dateOfBirth",render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:l("Date of birth")}),(0,s.jsx)(z.NI,{children:(0,s.jsx)(ef.M,{...e})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)(z.Wi,{control:d.control,name:"gender",render:({field:e})=>(0,s.jsxs)(z.xJ,{children:[(0,s.jsx)(z.lX,{children:l("Gender")}),(0,s.jsx)(z.NI,{children:(0,s.jsxs)(ev.E,{defaultValue:e.value,onValueChange:e.onChange,className:"flex",children:[(0,s.jsxs)(I.Z,{htmlFor:"GenderMale","data-selected":"male"===e.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,s.jsx)(ev.m,{id:"GenderMale",value:"male",className:"absolute opacity-0"}),(0,s.jsx)("span",{children:l("Male")})]}),(0,s.jsxs)(I.Z,{htmlFor:"GenderFemale","data-selected":"female"===e.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,s.jsx)(ev.m,{id:"GenderFemale",value:"female",className:"absolute opacity-0"}),(0,s.jsx)("span",{children:l("Female")})]})]})}),(0,s.jsx)(z.zG,{})]})}),(0,s.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,s.jsxs)(Z.z,{disabled:o,children:[(0,s.jsx)(S.J,{condition:o,children:(0,s.jsx)(n.Loader,{className:"text-primary-foreground"})}),(0,s.jsxs)(S.J,{condition:!o,children:[l("Save"),(0,s.jsx)(F.Z,{size:20})]})]})})]})]})})})}function ek({title:e,status:r,icon:t,iconClass:n,statusClass:a,className:o}){return(0,s.jsxs)("div",{className:(0,c.ZP)("inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default",o),children:[(0,s.jsx)("div",{className:(0,c.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full",n),children:t({size:34,variant:"Bulk"})}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,s.jsxs)("span",{className:"block text-xs font-normal leading-4",children:[e," "]}),(0,s.jsx)("h6",{className:(0,c.ZP)("text-sm font-semibold leading-5",a),children:r})]})]})}function eE(){let e=(0,k.UO)(),{t:r}=(0,E.$G)(),{data:t,isLoading:u,mutate:m}=(0,L.ZP)(`/admin/customers/${e.customerId}`,e=>(0,o.Z)(e));if(u)return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(n.Loader,{})});let p=t?.data,x=p?.user?.wallets?.find(e=>e.default);return(0,s.jsx)(a.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","ADDRESS_INFORMATION","BALANCE","ConvertAccountType"],children:(0,s.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,s.jsxs)("div",{className:"grid w-full grid-cols-12 gap-4",children:[(0,s.jsx)(ek,{title:r("Account Status"),icon:e=>(0,s.jsx)(i.Z,{...e,variant:"Outline"}),statusClass:p?.user?.status?"text-success":"",status:p?.user?.status?"Active":"Inactive",iconClass:"bg-success/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,s.jsx)(ek,{title:r("KYC Status"),icon:e=>(0,s.jsx)(l.Z,{className:(0,c.ZP)(e.className,"text-primary"),...e}),statusClass:"text-primary",status:r(p?.user?.kycStatus?"Verified":"Pending Verification"),iconClass:"bg-primary/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,s.jsx)(ek,{title:r("Default Wallet"),icon:e=>(0,s.jsx)(d.Z,{...e}),statusClass:"text-spacial-blue",status:`${x?.balance} ${x?.currency?.code}`,iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,s.jsx)(ek,{title:r("Account type"),icon:e=>(0,s.jsx)(N,{...e}),statusClass:"text-spacial-blue",status:"Customer",iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"})]}),(0,s.jsx)($,{wallets:p?.user?.wallets,onMutate:()=>m(t)}),(0,s.jsx)(eN,{isLoading:u,customer:p,onMutate:()=>m(t)}),(0,s.jsx)(W,{customer:p,onMutate:()=>m(t)}),(0,s.jsx)(eh,{customer:p})]})})}},80317:(e,r,t)=>{"use strict";t.d(r,{Cd:()=>l,X:()=>d,bZ:()=>i});var s=t(60926),n=t(8206),a=t(29220),o=t(65091);let c=(0,n.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=a.forwardRef(({className:e,variant:r,...t},n)=>(0,s.jsx)("div",{ref:n,role:"alert",className:(0,o.ZP)(c({variant:r}),e),...t}));i.displayName="Alert";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,o.ZP)("mb-1 font-medium leading-none tracking-tight",e),...r}));l.displayName="AlertTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.ZP)("text-sm [&_p]:leading-relaxed",e),...r}));d.displayName="AlertDescription"},28029:(e,r,t)=>{"use strict";t.d(r,{I:()=>a});var s=t(1181),n=t(25694);async function a(e,r){try{let t=await s.Z.put(`/admin/wallets/transfer-limit/${r}`,e);return(0,n.B)(t)}catch(e){return(0,n.D)(e)}}},50684:(e,r,t)=>{"use strict";t.d(r,{Z:()=>h});var s=t(61394),n=t(29220),a=t(31036),o=t.n(a),c=["variant","color","size"],i=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7 13.31c-.72 0-1.31-.59-1.31-1.31 0-.72.59-1.31 1.31-1.31.72 0 1.31.59 1.31 1.31 0 .72-.59 1.31-1.31 1.31Zm5 0c-.72 0-1.31-.59-1.31-1.31 0-.72.59-1.31 1.31-1.31.72 0 1.31.59 1.31 1.31 0 .72-.59 1.31-1.31 1.31Zm5 0c-.72 0-1.31-.59-1.31-1.31 0-.72.59-1.31 1.31-1.31.72 0 1.31.59 1.31 1.31 0 .72-.59 1.31-1.31 1.31Z",fill:r}))},l=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M5 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM19 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:r,strokeWidth:"1.5"}),n.createElement("path",{d:"M10 12c0 1.1.9 2 2 2s2-.9 2-2-.9-2-2-2",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Z",fill:r}),n.createElement("path",{d:"M12 10.691c-.72 0-1.31.59-1.31 1.31 0 .72.59 1.31 1.31 1.31.72 0 1.31-.59 1.31-1.31 0-.72-.59-1.31-1.31-1.31ZM7 10.691c-.72 0-1.31.59-1.31 1.31 0 .72.59 1.31 1.31 1.31.72 0 1.31-.59 1.31-1.31 0-.72-.59-1.31-1.31-1.31ZM17 10.691c-.72 0-1.31.59-1.31 1.31 0 .72.59 1.31 1.31 1.31.72 0 1.31-.59 1.31-1.31 0-.72-.59-1.31-1.31-1.31Z",fill:r}))},u=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M5 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM19 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM12 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:r,strokeWidth:"1.5"}))},m=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M5 14.75c-1.52 0-2.75-1.23-2.75-2.75S3.48 9.25 5 9.25 7.75 10.48 7.75 12 6.52 14.75 5 14.75Zm0-4a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5ZM19 14.75c-1.52 0-2.75-1.23-2.75-2.75S17.48 9.25 19 9.25s2.75 1.23 2.75 2.75-1.23 2.75-2.75 2.75Zm0-4a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5ZM12 14.75c-1.52 0-2.75-1.23-2.75-2.75S10.48 9.25 12 9.25s2.75 1.23 2.75 2.75-1.23 2.75-2.75 2.75Zm0-4a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5Z",fill:r}))},p=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M5 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2ZM19 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:r,strokeWidth:"1.5"}),n.createElement("path",{opacity:".4",d:"M12 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2Z",stroke:r,strokeWidth:"1.5"}))},x=function(e,r){switch(e){case"Bold":return n.createElement(i,{color:r});case"Broken":return n.createElement(l,{color:r});case"Bulk":return n.createElement(d,{color:r});case"Linear":default:return n.createElement(u,{color:r});case"Outline":return n.createElement(m,{color:r});case"TwoTone":return n.createElement(p,{color:r})}},h=(0,n.forwardRef)(function(e,r){var t=e.variant,a=e.color,o=e.size,i=(0,s._)(e,c);return n.createElement("svg",(0,s.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:o,height:o,viewBox:"0 0 24 24",fill:"none"}),x(t,a))});h.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="More"},18001:(e,r,t)=>{"use strict";t.d(r,{Z:()=>h});var s=t(61394),n=t(29220),a=t(31036),o=t.n(a),c=["variant","color","size"],i=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM12 14.5c-5.01 0-9.09 3.36-9.09 7.5 0 .28.22.5.5.5h17.18c.28 0 .5-.22.5-.5 0-4.14-4.08-7.5-9.09-7.5Z",fill:r}))},l=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M15.02 3.01A4.944 4.944 0 0 0 12 2C9.24 2 7 4.24 7 7s2.24 5 5 5 5-2.24 5-5M20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z",fill:r}),n.createElement("path",{d:"M12 14.5c-5.01 0-9.09 3.36-9.09 7.5 0 .28.22.5.5.5h17.18c.28 0 .5-.22.5-.5 0-4.14-4.08-7.5-9.09-7.5Z",fill:r}))},u=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 12.75c-3.17 0-5.75-2.58-5.75-5.75S8.83 1.25 12 1.25 17.75 3.83 17.75 7s-2.58 5.75-5.75 5.75Zm0-10A4.26 4.26 0 0 0 7.75 7 4.26 4.26 0 0 0 12 11.25 4.26 4.26 0 0 0 16.25 7 4.26 4.26 0 0 0 12 2.75ZM20.59 22.75c-.41 0-.75-.34-.75-.75 0-3.45-3.52-6.25-7.84-6.25S4.16 18.55 4.16 22c0 .41-.34.75-.75.75s-.75-.34-.75-.75c0-4.27 4.19-7.75 9.34-7.75 5.15 0 9.34 3.48 9.34 7.75 0 .41-.34.75-.75.75Z",fill:r}))},p=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{opacity:".4",d:"M20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},x=function(e,r){switch(e){case"Bold":return n.createElement(i,{color:r});case"Broken":return n.createElement(l,{color:r});case"Bulk":return n.createElement(d,{color:r});case"Linear":default:return n.createElement(u,{color:r});case"Outline":return n.createElement(m,{color:r});case"TwoTone":return n.createElement(p,{color:r})}},h=(0,n.forwardRef)(function(e,r){var t=e.variant,a=e.color,o=e.size,i=(0,s._)(e,c);return n.createElement("svg",(0,s.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:o,height:o,viewBox:"0 0 24 24",fill:"none"}),x(t,a))});h.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="User"},53735:(e,r,t)=>{"use strict";t.d(r,{Z:()=>h});var s=t(61394),n=t(29220),a=t(31036),o=t.n(a),c=["variant","color","size"],i=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M22 10.97v2.06c0 .55-.44 1-1 1.02h-1.96c-1.08 0-2.07-.79-2.16-1.87-.06-.63.18-1.22.6-1.63.37-.38.88-.6 1.44-.6H21c.56.02 1 .47 1 1.02Z",fill:r}),n.createElement("path",{d:"M20.47 15.55h-1.43c-1.9 0-3.5-1.43-3.66-3.25-.09-1.04.29-2.08 1.05-2.82.64-.66 1.53-1.03 2.49-1.03h1.55c.29 0 .53-.24.5-.53-.22-2.43-1.83-4.09-4.22-4.37-.24-.04-.49-.05-.75-.05H7c-.28 0-.55.02-.81.06C3.64 3.88 2 5.78 2 8.5v7c0 2.76 2.24 5 5 5h9c2.8 0 4.73-1.75 4.97-4.42a.49.49 0 0 0-.5-.53ZM13 9.75H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h6c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:r}))},l=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M13 9H7M22 10.97v2.06c0 .55-.44 1-1 1.02h-1.96c-1.08 0-2.07-.79-2.16-1.87-.06-.63.18-1.22.6-1.63.37-.38.88-.6 1.44-.6H21c.56.02 1 .47 1 1.02Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M2 8.5c0-2.72 1.64-4.62 4.19-4.94.26-.04.53-.06.81-.06h9c.26 0 .51.01.75.05C19.33 3.85 21 5.76 21 8.5v1.45h-2.08c-.56 0-1.07.22-1.44.6-.42.41-.66 1-.6 1.63.09 1.08 1.08 1.87 2.16 1.87H21v1.45c0 3-2 5-5 5H7c-3 0-5-2-5-5v-3.24",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M22 10.97v2.06c0 .55-.44 1-1 1.02h-1.96c-1.08 0-2.07-.79-2.16-1.87-.06-.63.18-1.22.6-1.63.37-.38.88-.6 1.44-.6H21c.56.02 1 .47 1 1.02Z",fill:r}),n.createElement("path",{opacity:".4",d:"M17.48 10.55c-.42.41-.66 1-.6 1.63.09 1.08 1.08 1.87 2.16 1.87H21v1.45c0 3-2 5-5 5H7c-3 0-5-2-5-5v-7c0-2.72 1.64-4.62 4.19-4.94.26-.04.53-.06.81-.06h9c.26 0 .51.01.75.05C19.33 3.85 21 5.76 21 8.5v1.45h-2.08c-.56 0-1.07.22-1.44.6Z",fill:r}),n.createElement("path",{d:"M13 9.75H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h6c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:r}))},u=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M13 9H7M22 10.97v2.06c0 .55-.44 1-1 1.02h-1.96c-1.08 0-2.07-.79-2.16-1.87-.06-.63.18-1.22.6-1.63.37-.38.88-.6 1.44-.6H21c.56.02 1 .47 1 1.02Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M17.48 10.55c-.42.41-.66 1-.6 1.63.09 1.08 1.08 1.87 2.16 1.87H21v1.45c0 3-2 5-5 5H7c-3 0-5-2-5-5v-7c0-2.72 1.64-4.62 4.19-4.94.26-.04.53-.06.81-.06h9c.26 0 .51.01.75.05C19.33 3.85 21 5.76 21 8.5v1.45h-2.08c-.56 0-1.07.22-1.44.6Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M13 9.75H7c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h6c.41 0 .75.34.75.75s-.34.75-.75.75ZM19.04 14.8c-1.51 0-2.79-1.12-2.91-2.56-.08-.83.22-1.64.82-2.23.5-.52 1.21-.81 1.96-.81H21c.99.03 1.75.81 1.75 1.77v2.06c0 .96-.76 1.74-1.72 1.77h-1.99Zm1.93-4.1h-2.05c-.35 0-.67.13-.9.37-.29.28-.43.66-.39 1.04.05.66.69 1.19 1.41 1.19H21c.13 0 .25-.12.25-.27v-2.06c0-.15-.12-.26-.28-.27Z",fill:r}),n.createElement("path",{d:"M16 21.25H7c-3.44 0-5.75-2.31-5.75-5.75v-7c0-3.08 1.9-5.31 4.85-5.68.27-.04.58-.07.9-.07h9c.24 0 .55.01.87.06 2.95.34 4.88 2.58 4.88 5.69v1.45c0 .41-.34.75-.75.75h-2.08c-.35 0-.67.13-.9.37l-.01.01c-.28.27-.41.64-.38 1.02.05.66.69 1.19 1.41 1.19H21c.41 0 .75.34.75.75v1.45c0 3.45-2.31 5.76-5.75 5.76Zm-9-17c-.24 0-.47.02-.7.05-2.2.28-3.55 1.88-3.55 4.2v7c0 2.58 1.67 4.25 4.25 4.25h9c2.58 0 4.25-1.67 4.25-4.25v-.7h-1.21c-1.51 0-2.79-1.12-2.91-2.56-.08-.82.22-1.64.82-2.22.52-.53 1.22-.82 1.97-.82h1.33v-.7c0-2.34-1.37-3.95-3.59-4.21-.24-.04-.45-.04-.66-.04H7Z",fill:r}))},p=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M13 9H7",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M22 10.97v2.06c0 .55-.44 1-1 1.02h-1.96c-1.08 0-2.07-.79-2.16-1.87-.06-.63.18-1.22.6-1.63.37-.38.88-.6 1.44-.6H21c.56.02 1 .47 1 1.02Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M17.48 10.55c-.42.41-.66 1-.6 1.63.09 1.08 1.08 1.87 2.16 1.87H21v1.45c0 3-2 5-5 5H7c-3 0-5-2-5-5v-7c0-2.72 1.64-4.62 4.19-4.94.26-.04.53-.06.81-.06h9c.26 0 .51.01.75.05C19.33 3.85 21 5.76 21 8.5v1.45h-2.08c-.56 0-1.07.22-1.44.6Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},x=function(e,r){switch(e){case"Bold":return n.createElement(i,{color:r});case"Broken":return n.createElement(l,{color:r});case"Bulk":return n.createElement(d,{color:r});case"Linear":default:return n.createElement(u,{color:r});case"Outline":return n.createElement(m,{color:r});case"TwoTone":return n.createElement(p,{color:r})}},h=(0,n.forwardRef)(function(e,r){var t=e.variant,a=e.color,o=e.size,i=(0,s._)(e,c);return n.createElement("svg",(0,s.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:o,height:o,viewBox:"0 0 24 24",fill:"none"}),x(t,a))});h.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="Wallet2"},66697:(e,r,t)=>{"use strict";t.d(r,{Z:()=>h});var s=t(61394),n=t(29220),a=t(31036),o=t.n(a),c=["variant","color","size"],i=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"m19.51 5.85-5.94-3.43c-.97-.56-2.17-.56-3.15 0L4.49 5.85a3.15 3.15 0 0 0-1.57 2.73v6.84c0 1.12.6 2.16 1.57 2.73l5.94 3.43c.97.56 2.17.56 3.15 0l5.94-3.43a3.15 3.15 0 0 0 1.57-2.73V8.58a3.192 3.192 0 0 0-1.58-2.73Zm-8.26 1.9c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75s-.75-.34-.75-.75V7.75Zm1.67 8.88c-.05.12-.12.23-.21.33a.99.99 0 0 1-1.09.21c-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38Z",fill:r}))},l=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 7.75V13M2.92 8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.56 1.57 1.6 1.57 2.73v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73v-2.76",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M12 16.2v.1",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",fill:r}),n.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.249c-.13 0-.26-.03-.38-.08-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:r}))},u=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 7.75V13M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M12 16.2v.1",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.25a.99.99 0 0 1-.71-.29c-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.11-.52.29-.71.37-.37 1.05-.37 1.42 0 .18.19.29.45.29.71 0 .13-.03.26-.08.38s-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:r}),n.createElement("path",{d:"M12 22.751c-.67 0-1.35-.17-1.95-.52l-5.94-3.43c-1.2-.7-1.95-1.99-1.95-3.38v-6.84c0-1.39.75-2.68 1.95-3.38l5.94-3.43c1.2-.7 2.69-.7 3.9 0l5.94 3.43c1.2.7 1.95 1.99 1.95 3.38v6.84c0 1.39-.75 2.68-1.95 3.38l-5.94 3.43c-.6.35-1.28.52-1.95.52Zm0-20c-.41 0-.83.11-1.2.32l-5.94 3.43c-.74.43-1.2 1.22-1.2 2.08v6.84c0 .85.46 1.65 1.2 2.08l5.94 3.43c.74.43 1.66.43 2.39 0l5.94-3.43c.74-.43 1.2-1.22 1.2-2.08v-6.84c0-.85-.46-1.65-1.2-2.08l-5.94-3.43c-.36-.21-.78-.32-1.19-.32Z",fill:r}))},p=function(e){var r=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{opacity:".4",d:"M12 7.75V13",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{opacity:".4",d:"M12 16.2v.1",stroke:r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},x=function(e,r){switch(e){case"Bold":return n.createElement(i,{color:r});case"Broken":return n.createElement(l,{color:r});case"Bulk":return n.createElement(d,{color:r});case"Linear":default:return n.createElement(u,{color:r});case"Outline":return n.createElement(m,{color:r});case"TwoTone":return n.createElement(p,{color:r})}},h=(0,n.forwardRef)(function(e,r){var t=e.variant,a=e.color,o=e.size,i=(0,s._)(e,c);return n.createElement("svg",(0,s.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:o,height:o,viewBox:"0 0 24 24",fill:"none"}),x(t,a))});h.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="Warning2"},38520:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,runtime:()=>n});var s=t(18264);let n=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\layout.tsx#runtime`),a=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\layout.tsx#default`)},96104:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(42416),n=t(21237);function a(){return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(n.a,{})})}},94411:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\page.tsx#default`)},78174:(e,r,t)=>{"use strict";function s({children:e}){return e}t.r(r),t.d(r,{default:()=>s}),t(87908)},73081:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(42416),n=t(21237);function a(){return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(n.a,{})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[529,6578,3390,6165,4969,4774,870,1474,3099,7283,5089,3711,3214],()=>r(69743));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/customers/[customerId]/page"]=t}]);
//# sourceMappingURL=page.js.map