(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9255],{77128:function(e,n,l){Promise.resolve().then(l.bind(l,12198))},12198:function(e,n,l){"use strict";l.r(n),l.d(n,{default:function(){return f}});var s=l(57437),u=l(45702),r=l(68332),d=l(61149),i=l(6596),t=l(79981),a=l(74539),o=l(70517),c=l(85323);function f(){let{data:e,isLoading:n,error:l}=(0,c.ZP)("/customers/detail",e=>t.Z.get(e)),f=null==e?void 0:e.data;return(0,s.jsx)(i.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","PASSWORD_INFORMATION","ADDRESS_INFORMATION"],children:(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)(d.O,{user:f?new o.n({...f,...null==f?void 0:f.user,address:null==f?void 0:f.address,avatar:null==f?void 0:f.profileImage}):null,isLoading:n,error:l}),(0,s.jsx)(u.h,{address:new a.k(null==f?void 0:f.address)}),(0,s.jsx)(r.G,{})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,31384,60627,85598,77317,227,45967,92971,95030,1744],function(){return e(e.s=77128)}),_N_E=e.O()}]);