"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[58410],{36887:function(e,r,t){t.d(r,{Z:function(){return v}});var n=t(74677),o=t(2265),a=t(40718),l=t.n(a),u=["variant","color","size"],i=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:r,d:"M17.919 8.18H6.079c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},c=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M16.01 12.85l-2.62 2.62c-.77.77-2.03.77-2.8 0L4.08 8.95M19.92 8.95l-1.04 1.04"}))},s=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:r,d:"M15.48 13.23l-3.79-5.05H6.08c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97z",opacity:".4"}),o.createElement("path",{fill:r,d:"M17.92 8.18h-6.23l3.79 5.05 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},d=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},p=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:r,d:"M12 16.8c-.7 0-1.4-.27-1.93-.8L3.55 9.48a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.48.48 1.26.48 1.74 0l6.52-6.52c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L13.93 16c-.53.53-1.23.8-1.93.8z"}))},f=function(e){var r=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},h=function(e,r){switch(e){case"Bold":return o.createElement(i,{color:r});case"Broken":return o.createElement(c,{color:r});case"Bulk":return o.createElement(s,{color:r});case"Linear":default:return o.createElement(d,{color:r});case"Outline":return o.createElement(p,{color:r});case"TwoTone":return o.createElement(f,{color:r})}},v=(0,o.forwardRef)(function(e,r){var t=e.variant,a=e.color,l=e.size,i=(0,n._)(e,u);return o.createElement("svg",(0,n.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:r,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),h(t,a))});v.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},v.defaultProps={variant:"Linear",color:"currentColor",size:"24"},v.displayName="ArrowDown2"},21047:function(e,r,t){t.d(r,{Z:function(){return n}});let n=(0,t(79205).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},99397:function(e,r,t){t.d(r,{Z:function(){return n}});let n=(0,t(79205).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99376:function(e,r,t){var n=t(35475);t.o(n,"permanentRedirect")&&t.d(r,{permanentRedirect:function(){return n.permanentRedirect}}),t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}}),t.o(n,"useSelectedLayoutSegment")&&t.d(r,{useSelectedLayoutSegment:function(){return n.useSelectedLayoutSegment}}),t.o(n,"useSelectedLayoutSegments")&&t.d(r,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})},27312:function(e,r,t){t.d(r,{VY:function(){return U},fC:function(){return Z},h_:function(){return H},xz:function(){return V}});var n=t(2265),o=t(6741),a=t(98575),l=t(73966),u=t(15278),i=t(86097),c=t(99103),s=t(99255),d=t(26008),p=t(83832),f=t(71599),h=t(66840),v=t(37053),m=t(80886),g=t(5478),k=t(87922),w=t(57437),y="Popover",[x,P]=(0,l.b)(y,[d.D7]),b=(0,d.D7)(),[E,C]=x(y),R=e=>{let{__scopePopover:r,children:t,open:o,defaultOpen:a,onOpenChange:l,modal:u=!1}=e,i=b(r),c=n.useRef(null),[p,f]=n.useState(!1),[h,v]=(0,m.T)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:y});return(0,w.jsx)(d.fC,{...i,children:(0,w.jsx)(E,{scope:r,contentId:(0,s.M)(),triggerRef:c,open:h,onOpenChange:v,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:p,onCustomAnchorAdd:n.useCallback(()=>f(!0),[]),onCustomAnchorRemove:n.useCallback(()=>f(!1),[]),modal:u,children:t})})};R.displayName=y;var j="PopoverAnchor";n.forwardRef((e,r)=>{let{__scopePopover:t,...o}=e,a=C(j,t),l=b(t),{onCustomAnchorAdd:u,onCustomAnchorRemove:i}=a;return n.useEffect(()=>(u(),()=>i()),[u,i]),(0,w.jsx)(d.ee,{...l,...o,ref:r})}).displayName=j;var M="PopoverTrigger",S=n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,l=C(M,t),u=b(t),i=(0,a.e)(r,l.triggerRef),c=(0,w.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...n,ref:i,onClick:(0,o.M)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?c:(0,w.jsx)(d.ee,{asChild:!0,...u,children:c})});S.displayName=M;var L="PopoverPortal",[O,F]=x(L,{forceMount:void 0}),D=e=>{let{__scopePopover:r,forceMount:t,children:n,container:o}=e,a=C(L,r);return(0,w.jsx)(O,{scope:r,forceMount:t,children:(0,w.jsx)(f.z,{present:t||a.open,children:(0,w.jsx)(p.h,{asChild:!0,container:o,children:n})})})};D.displayName=L;var N="PopoverContent",_=n.forwardRef((e,r)=>{let t=F(N,e.__scopePopover),{forceMount:n=t.forceMount,...o}=e,a=C(N,e.__scopePopover);return(0,w.jsx)(f.z,{present:n||a.open,children:a.modal?(0,w.jsx)(A,{...o,ref:r}):(0,w.jsx)(T,{...o,ref:r})})});_.displayName=N;var z=(0,v.Z8)("PopoverContent.RemoveScroll"),A=n.forwardRef((e,r)=>{let t=C(N,e.__scopePopover),l=n.useRef(null),u=(0,a.e)(r,l),i=n.useRef(!1);return n.useEffect(()=>{let e=l.current;if(e)return(0,g.Ry)(e)},[]),(0,w.jsx)(k.Z,{as:z,allowPinchZoom:!0,children:(0,w.jsx)(B,{...e,ref:u,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var r;e.preventDefault(),i.current||null===(r=t.triggerRef.current)||void 0===r||r.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;i.current=n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),T=n.forwardRef((e,r)=>{let t=C(N,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,w.jsx)(B,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,r),r.defaultPrevented||(o.current||null===(l=t.triggerRef.current)||void 0===l||l.focus(),r.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:r=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,r),r.defaultPrevented||(o.current=!0,"pointerdown"!==r.detail.originalEvent.type||(a.current=!0));let u=r.target;(null===(l=t.triggerRef.current)||void 0===l?void 0:l.contains(u))&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&a.current&&r.preventDefault()}})}),B=n.forwardRef((e,r)=>{let{__scopePopover:t,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEscapeKeyDown:s,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:h,...v}=e,m=C(N,t),g=b(t);return(0,i.EW)(),(0,w.jsx)(c.M,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,w.jsx)(u.XB,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:h,onEscapeKeyDown:s,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>m.onOpenChange(!1),children:(0,w.jsx)(d.VY,{"data-state":W(m.open),role:"dialog",id:m.contentId,...g,...v,ref:r,style:{...v.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),I="PopoverClose";function W(e){return e?"open":"closed"}n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,a=C(I,t);return(0,w.jsx)(h.WV.button,{type:"button",...n,ref:r,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=I,n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,o=b(t);return(0,w.jsx)(d.Eh,{...o,...n,ref:r})}).displayName="PopoverArrow";var Z=R,V=S,H=D,U=_},55156:function(e,r,t){t.d(r,{f:function(){return c}});var n=t(2265),o=t(66840),a=t(57437),l="horizontal",u=["horizontal","vertical"],i=n.forwardRef((e,r)=>{let{decorative:t,orientation:n=l,...i}=e,c=u.includes(n)?n:l;return(0,a.jsx)(o.WV.div,{"data-orientation":c,...t?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...i,ref:r})});i.displayName="Separator";var c=i},50721:function(e,r,t){t.d(r,{bU:function(){return b},fC:function(){return P}});var n=t(2265),o=t(6741),a=t(98575),l=t(73966),u=t(80886),i=t(6718),c=t(90420),s=t(66840),d=t(57437),p="Switch",[f,h]=(0,l.b)(p),[v,m]=f(p),g=n.forwardRef((e,r)=>{let{__scopeSwitch:t,name:l,checked:i,defaultChecked:c,required:f,disabled:h,value:m="on",onCheckedChange:g,form:k,...w}=e,[P,b]=n.useState(null),E=(0,a.e)(r,e=>b(e)),C=n.useRef(!1),R=!P||k||!!P.closest("form"),[j,M]=(0,u.T)({prop:i,defaultProp:null!=c&&c,onChange:g,caller:p});return(0,d.jsxs)(v,{scope:t,checked:j,disabled:h,children:[(0,d.jsx)(s.WV.button,{type:"button",role:"switch","aria-checked":j,"aria-required":f,"data-state":x(j),"data-disabled":h?"":void 0,disabled:h,value:m,...w,ref:E,onClick:(0,o.M)(e.onClick,e=>{M(e=>!e),R&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),R&&(0,d.jsx)(y,{control:P,bubbles:!C.current,name:l,value:m,checked:j,required:f,disabled:h,form:k,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var k="SwitchThumb",w=n.forwardRef((e,r)=>{let{__scopeSwitch:t,...n}=e,o=m(k,t);return(0,d.jsx)(s.WV.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:r})});w.displayName=k;var y=n.forwardRef((e,r)=>{let{__scopeSwitch:t,control:o,checked:l,bubbles:u=!0,...s}=e,p=n.useRef(null),f=(0,a.e)(p,r),h=(0,i.D)(l),v=(0,c.t)(o);return n.useEffect(()=>{let e=p.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==l&&r){let t=new Event("click",{bubbles:u});r.call(e,l),e.dispatchEvent(t)}},[h,l,u]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...s,tabIndex:-1,ref:f,style:{...s.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var P=g,b=w},43577:function(e,r,t){t.d(r,{IZ:function(){return d}});let{Axios:n,AxiosError:o,CanceledError:a,isCancel:l,CancelToken:u,VERSION:i,all:c,Cancel:s,isAxiosError:d,spread:p,toFormData:f,AxiosHeaders:h,HttpStatusCode:v,formToJSON:m,getAdapter:g,mergeConfig:k}=t(83464).default}}]);