(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[52253],{47670:function(e,t,r){Promise.resolve().then(r.bind(r,25294))},25294:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return h}});var n=r(57437),a=r(25833),o=r(85539),i=r(16831),s=r(35974),l=r(62869),c=r(66070),d=r(75730),u=r(94508),f=r(59532),m=r(58926),g=r(27648),p=r(99376),v=r(2265),x=r(43949);function h(){var e;let{t}=(0,x.$G)(),r=(0,p.useSearchParams)(),[h,b]=v.useState([]),[y,N]=v.useState(null!==(e=r.get("search"))&&void 0!==e?e:""),w=(0,p.useRouter)(),j=(0,p.usePathname)(),{data:P,meta:S,isLoading:R}=(0,d.Z)("/admin/gateways?".concat(r.toString()));return(0,n.jsxs)(c.Zb,{className:"rounded-xl",children:[(0,n.jsxs)(c.Ol,{className:"flex w-full flex-wrap items-start justify-between py-4 sm:flex-row sm:items-center",children:[(0,n.jsx)(c.ll,{className:"flex-1 text-base font-medium leading-[22px]",children:t("Deposit/Payment Gateways")}),(0,n.jsx)(o.R,{value:y,onChange:e=>{e.preventDefault();let t=(0,u.w4)(e.target.value);N(e.target.value),w.replace("".concat(j,"?").concat(t.toString()))},iconPlacement:"end",placeholder:t("Search...")})]}),(0,n.jsx)(c.aY,{className:"border-t border-divider py-4",children:(0,n.jsx)("div",{className:"flex flex-col gap-4",children:(0,n.jsx)(a.Z,{data:P,sorting:h,setSorting:b,isLoading:R,pagination:{total:null==S?void 0:S.total,page:null==S?void 0:S.currentPage,limit:null==S?void 0:S.perPage},structure:[{id:"name",header:t("Name"),cell:e=>{var t,r,a,o,s;let{row:l}=e;return(0,n.jsxs)(g.default,{href:"/settings/gateways/".concat(null===(t=l.original)||void 0===t?void 0:t.id,"?name=").concat(null===(r=l.original)||void 0===r?void 0:r.value),className:"flex items-center gap-2",children:[(0,n.jsxs)(i.qE,{children:[(0,n.jsx)(i.F$,{src:(0,u.qR)(null===(a=l.original)||void 0===a?void 0:a.logoImage)}),(0,n.jsx)(i.Q5,{className:"font-semibold",children:(0,f.v)(null===(o=l.original)||void 0===o?void 0:o.name)})]}),(0,n.jsx)("span",{className:"text-secondary-text hover:text-primary hover:underline",children:null===(s=l.original)||void 0===s?void 0:s.name})]})}},{id:"status",header:t("Status"),cell:e=>{var r,a;let{row:o}=e;return(0,n.jsx)(s.C,{variant:(null===(r=o.original)||void 0===r?void 0:r.active)?"success":"secondary",children:(null===(a=o.original)||void 0===a?void 0:a.active)?t("Active"):t("Inactive")})}},{id:"recommended",header:t("Recommended"),cell:e=>{var r,a;let{row:o}=e;return(0,n.jsx)(s.C,{variant:(null===(r=o.original)||void 0===r?void 0:r.recommended)?"important":"secondary",children:(null===(a=o.original)||void 0===a?void 0:a.recommended)?t("Yes"):t("No")})}},{id:"menu",header:t("Menu"),cell:e=>{var t,r;let{row:a}=e;return(0,n.jsx)(l.z,{size:"icon",variant:"outline",className:"size-8 bg-background p-1.5 text-primary",children:(0,n.jsx)(g.default,{href:"/settings/gateways/".concat(null===(t=a.original)||void 0===t?void 0:t.id,"?name=").concat(null===(r=a.original)||void 0===r?void 0:r.value),children:(0,n.jsx)(m.Z,{size:"20",className:"text-primary"})})})}}]})})})]})}},25833:function(e,t,r){"use strict";r.d(t,{Z:function(){return x}});var n=r(57437),a=r(94508),o=r(71594),i=r(24525),s=r(73490),l=r(36887),c=r(64394),d=r(61756),u=r(99376),f=r(4751),m=r(2265),g=r(43949),p=r(62869),v=r(73578);function x(e){let{data:t,isLoading:r=!1,structure:x,sorting:h,setSorting:b,padding:y=!1,className:N,onRefresh:w,pagination:j}=e,P=(0,m.useMemo)(()=>x,[x]),S=(0,u.useRouter)(),R=(0,u.usePathname)(),Z=(0,u.useSearchParams)(),{t:C}=(0,g.$G)(),T=(0,o.b7)({data:t||[],columns:P,state:{sorting:h,onRefresh:w},onSortingChange:b,getCoreRowModel:(0,i.sC)(),getSortedRowModel:(0,i.tj)(),debugTable:!1});return r?(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:C("Loading...")})}):(null==t?void 0:t.length)?(0,n.jsxs)("div",{className:(0,a.ZP)("".concat(y?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),N),children:[(0,n.jsxs)(v.iA,{children:[(0,n.jsx)(v.xD,{children:T.getHeaderGroups().map(e=>(0,n.jsx)(v.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,r,i,s;return(0,n.jsx)(v.ss,{className:(0,a.ZP)("",null==e?void 0:null===(i=e.column)||void 0===i?void 0:null===(r=i.columnDef)||void 0===r?void 0:null===(t=r.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,n.jsxs)(p.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[C((0,o.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(s=({asc:(0,n.jsx)(l.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,n.jsx)(l.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==s?s:(0,n.jsx)(l.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,n.jsx)(v.RM,{children:T.getRowModel().rows.map(e=>(0,n.jsx)(v.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,n.jsx)(v.pj,{className:"py-3 text-sm font-semibold",children:(0,o.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),j&&j.total>10&&(0,n.jsx)("div",{className:"pb-2 pt-6",children:(0,n.jsx)(f.Z,{showTotal:(e,t)=>C("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==j?void 0:j.page,total:null==j?void 0:j.total,pageSize:null==j?void 0:j.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(Z);t.set("page",e.toString()),S.push("".concat(R,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(c.Z,{size:"18"})}),nextIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(d.Z,{size:"18"})})})})]}):(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,n.jsx)(s.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),C("No data found!")]})})}},85539:function(e,t,r){"use strict";r.d(t,{R:function(){return s}});var n=r(57437);r(2265);var a=r(95186),o=r(94508),i=r(48674);function s(e){let{iconPlacement:t="start",className:r,containerClass:s,...l}=e;return(0,n.jsxs)("div",{className:(0,o.ZP)("relative flex items-center",s),children:[(0,n.jsx)(i.Z,{size:"20",className:(0,o.ZP)("absolute top-1/2 -translate-y-1/2","end"===t?"right-2.5":"left-2.5")}),(0,n.jsx)(a.I,{type:"text",className:(0,o.ZP)("h-10","end"===t?"pr-10":"pl-10",r),...l})]})}},16831:function(e,t,r){"use strict";r.d(t,{F$:function(){return l},Q5:function(){return c},qE:function(){return s}});var n=r(57437),a=r(2265),o=r(61146),i=r(94508);let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.fC,{ref:t,className:(0,i.ZP)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...a})});s.displayName=o.fC.displayName;let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.Ee,{ref:t,className:(0,i.ZP)("aspect-square h-full w-full",r),...a})});l.displayName=o.Ee.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.NY,{ref:t,className:(0,i.ZP)("flex h-full w-full items-center justify-center rounded-full bg-muted",r),...a})});c.displayName=o.NY.displayName},35974:function(e,t,r){"use strict";r.d(t,{C:function(){return s}});var n=r(57437),a=r(90535);r(2265);var o=r(94508);let i=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function s(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{className:(0,o.ZP)(i({variant:r}),t),...a})}},62869:function(e,t,r){"use strict";r.d(t,{d:function(){return l},z:function(){return c}});var n=r(57437),a=r(37053),o=r(90535),i=r(2265),s=r(94508);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:o,size:i,asChild:c=!1,...d}=e,u=c?a.g7:"button";return(0,n.jsx)(u,{className:(0,s.ZP)(l({variant:o,size:i,className:r})),ref:t,...d})});c.displayName="Button"},66070:function(e,t,r){"use strict";r.d(t,{Ol:function(){return s},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var n=r(57437),a=r(2265),o=r(94508);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("flex flex-col space-y-1.5 p-6",r),...a})});s.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,o.ZP)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,o.ZP)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var n=r(57437),a=r(2265),o=r(94508);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,n.jsx)("input",{type:a,className:(0,o.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...i})});i.displayName="Input"},73578:function(e,t,r){"use strict";r.d(t,{RM:function(){return l},SC:function(){return c},iA:function(){return i},pj:function(){return u},ss:function(){return d},xD:function(){return s}});var n=r(57437),a=r(2265),o=r(94508);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,o.ZP)("w-full caption-bottom text-sm",r),...a})})});i.displayName="Table";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("thead",{ref:t,className:(0,o.ZP)("",r),...a})});s.displayName="TableHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,o.ZP)("[&_tr:last-child]:border-0",r),...a})});l.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,o.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tr",{ref:t,className:(0,o.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});c.displayName="TableRow";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("th",{ref:t,className:(0,o.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});d.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("td",{ref:t,className:(0,o.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("caption",{ref:t,className:(0,o.ZP)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},31117:function(e,t,r){"use strict";r.d(t,{d:function(){return o}});var n=r(79981),a=r(85323);let o=(e,t)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},75730:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(31117),a=r(99376);function o(e,t){var r,o,i;let s=(0,a.usePathname)(),l=(0,a.useSearchParams)(),c=(0,a.useRouter)(),[d,u]=e.split("?"),f=new URLSearchParams(u);f.has("page")||f.set("page","1"),f.has("limit")||f.set("limit","10");let m="".concat(d,"?").concat(f.toString()),{data:g,error:p,isLoading:v,mutate:x,...h}=(0,n.d)(m,t);return{refresh:()=>x(g),data:null!==(i=null==g?void 0:null===(r=g.data)||void 0===r?void 0:r.data)&&void 0!==i?i:[],meta:null==g?void 0:null===(o=g.data)||void 0===o?void 0:o.meta,filter:(e,t,r)=>{let n=new URLSearchParams(l.toString());t?n.set(e,t.toString()):n.delete(e),c.replace("".concat(s,"?").concat(n.toString())),null==r||r()},isLoading:v,error:p,...h}}},79981:function(e,t,r){"use strict";var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return d},Fg:function(){return m},Fp:function(){return c},Qp:function(){return f},ZP:function(){return s},fl:function(){return l},qR:function(){return u},w4:function(){return g}});var n=r(78040),a=r(61994),o=r(14438),i=r(53335);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,a.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>o.toast.success("Copied to clipboard!")).catch(()=>{o.toast.error("Failed to copy!")})};class d{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let o=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:o,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:o,s=a.format(e),l=s.substring(i.length).trim();return{currencyCode:o,currencySymbol:i,formattedAmount:s,amountText:l}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",g=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}},59532:function(e,t,r){"use strict";function n(e){if(!e)return"";let t=e.split(" ");return(t.length>2?t[0].length>3?t[0][0]+t[t.length-1][0]:t[1][0]+t[t.length-1][0]:2===t.length?t[0][0]+t[1][0]:t[0][0]).toUpperCase()}r.d(t,{v:function(){return n}})}},function(e){e.O(0,[14438,31304,83464,2602,85323,27648,85210,64672,92971,95030,1744],function(){return e(e.s=47670)}),_N_E=e.O()}]);