"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[93909],{58068:function(e,t,r){r.d(t,{B:function(){return i}});var l=r(2265),n=r(73966),o=r(98575),u=r(37053),c=r(57437);function i(e){let t=e+"CollectionProvider",[r,i]=(0,n.b)(t),[f,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:r}=e,n=l.useRef(null),o=l.useRef(new Map).current;return(0,c.jsx)(f,{scope:t,itemMap:o,collectionRef:n,children:r})};s.displayName=t;let d=e+"CollectionSlot",m=(0,u.Z8)(d),p=l.forwardRef((e,t)=>{let{scope:r,children:l}=e,n=a(d,r),u=(0,o.e)(t,n.collectionRef);return(0,c.jsx)(m,{ref:u,children:l})});p.displayName=d;let C=e+"CollectionItemSlot",R="data-radix-collection-item",v=(0,u.Z8)(C),x=l.forwardRef((e,t)=>{let{scope:r,children:n,...u}=e,i=l.useRef(null),f=(0,o.e)(t,i),s=a(C,r);return l.useEffect(()=>(s.itemMap.set(i,{ref:i,...u}),()=>void s.itemMap.delete(i))),(0,c.jsx)(v,{[R]:"",ref:f,children:n})});return x.displayName=C,[{Provider:s,Slot:p,ItemSlot:x},function(t){let r=a(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(R,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},i]}},29114:function(e,t,r){r.d(t,{gm:function(){return o}});var l=r(2265);r(57437);var n=l.createContext(void 0);function o(e){let t=l.useContext(n);return e||t||"ltr"}}}]);