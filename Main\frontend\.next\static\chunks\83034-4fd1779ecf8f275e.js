(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[83034],{83034:function(e,i,n){Promise.resolve().then(n.bind(n,22990)),Promise.resolve().then(n.bind(n,9329))},9329:function(e,i,n){"use strict";n.d(i,{default:function(){return L}});var t=n(57437),s=n(62869),a=n(6512),r=n(64612),o=n(21251),l=n(17897),d=n(94508),c=n(90433),u=n(93596),v=n(8877),m=n(61756),x=n(89340),f=n(3512),g=n(91238),b=n(83068),h=n(3124),p=n(69961),y=n(65939),k=n(59607),j=n(35839),w=n(55036),Z=n(8400),z=n(15869),N=n(98491),A=n(39508),q=n(15084),_=n(33145),C=n(27648),D=n(43949),P=n(35974),R=n(28152),S=n(61994),T=n(99376);function E(e){let{title:i="",nav:n}=e,s=(0,T.useSelectedLayoutSegment)(),{setIsExpanded:a}=(0,r.q)(),{width:o}=(0,R.B)(),l=()=>{o<1024&&a(!1)};return(0,t.jsxs)("div",{className:"py-4",children:[i&&(0,t.jsx)("div",{className:"mb-2 whitespace-nowrap",children:(0,t.jsx)("span",{className:"px-2 text-sm font-medium tracking-wide text-secondary-600",children:i})}),(0,t.jsx)("div",{className:"flex flex-col gap-2",children:n.map(e=>{var i,n,a;return void 0===e.visible||e.visible?(0,t.jsxs)(C.default,{href:null==e?void 0:e.link,"aria-disabled":void 0===(null==e?void 0:e.isActive)||e.isActive,onClick:i=>{void 0===(null==e?void 0:e.isActive)||e.isActive?l():i.preventDefault()},className:(0,S.Z)("flex w-full items-center gap-2 whitespace-nowrap rounded-2xl px-2 py-2 transition-all duration-150 ease-in-out hover:bg-secondary active:bg-important/20",s===e.key&&"bg-secondary",("(dashboard)"===s||"__DEFAULT__"===s)&&"dashboard"===e.key&&"bg-secondary",void 0!==e.isActive&&!e.isActive&&"opacity-50"),children:[(null==e?void 0:e.icon)&&(0,t.jsx)("div",{"data-active":s===e.key,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:null==e?void 0:e.icon}),(0,t.jsx)("p",{className:"font-medium",children:null==e?void 0:e.name}),void 0!==e.badge?(0,t.jsx)(P.C,{variant:null!==(a=null===(i=e.badge)||void 0===i?void 0:i.variant)&&void 0!==a?a:"secondary",className:(0,d.ZP)("",null===(n=e.badge)||void 0===n?void 0:n.className),children:e.badge.title}):null]},null==e?void 0:e.key):null})})]})}function L(e){var i,n,P,R,S,T,L;let{userRole:W="customer"}=e,{t:F}=(0,D.$G)(),{isExpanded:H,setIsExpanded:I}=(0,r.q)(),{settings:B,isLoading:G}=(0,l.h)(),{logo:M,siteName:U}=(0,o.T)();return(0,t.jsxs)("div",{"data-expanded":H,className:"group absolute z-[60] flex h-full min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full sm:pb-1 lg:relative lg:z-auto",children:[(0,t.jsx)(s.z,{size:"icon",variant:"outline",onClick:()=>I(!1),className:"absolute -right-5 top-4 rounded-full bg-background group-data-[expanded=false]:hidden lg:hidden",children:(0,t.jsx)(c.Z,{})}),(0,t.jsx)("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary",children:(0,t.jsx)(C.default,{href:"/",className:"flex items-center justify-center",children:(0,t.jsx)(_.default,{src:(0,d.qR)(M),width:160,height:40,alt:U,className:"max-h-10 object-contain"})})}),(0,t.jsxs)("div",{className:"flex w-full flex-1 flex-col overflow-y-auto overflow-x-hidden px-4",children:[(0,t.jsx)(E,{nav:[{key:"(dashboard)",name:F("Dashboard"),icon:(0,t.jsx)(u.Z,{size:"20"}),link:"/",isLoading:!1},{key:"deposit",name:F("Deposit"),icon:(0,t.jsx)(v.Z,{size:"20"}),link:"/deposit",isLoading:G,isActive:(null==B?void 0:null===(i=B.deposit)||void 0===i?void 0:i.status)==="on"},{key:"transfer",name:F("Transfer"),icon:(0,t.jsx)(m.Z,{size:"20"}),link:"/transfer",visible:"agent"!==W,isLoading:G,isActive:(null==B?void 0:null===(n=B.transfer)||void 0===n?void 0:n.status)==="on"},{key:"withdraw",name:F("Withdraw"),icon:(0,t.jsx)(x.Z,{size:"20"}),link:"/withdraw",isLoading:G,isActive:(null==B?void 0:null===(P=B.withdraw)||void 0===P?void 0:P.status)==="on"},{key:"exchange",name:F("Exchange"),icon:(0,t.jsx)(f.Z,{size:"20"}),link:"/exchange",isLoading:G,isActive:(null==B?void 0:null===(R=B.exchange)||void 0===R?void 0:R.status)==="on"},{key:"payment",name:F("Payment"),icon:(0,t.jsx)(g.Z,{size:"20"}),link:"/payment",visible:"agent"!==W,isLoading:G,isActive:(null==B?void 0:null===(S=B.payment)||void 0===S?void 0:S.status)==="on"},{key:"services",name:F("Services"),icon:(0,t.jsx)(b.Z,{size:"20"}),link:"/services",visible:"agent"!==W,isLoading:G,isActive:!0},{key:"cards",name:F("Cards"),icon:(0,t.jsx)(h.Z,{size:"20"}),link:"/cards",isLoading:G,isActive:(null==B?void 0:null===(T=B.virtual_card)||void 0===T?void 0:T.status)==="on",visible:(null==B?void 0:null===(L=B.virtual_card)||void 0===L?void 0:L.status)==="on"},{key:"investments",name:F("Investments"),icon:(0,t.jsx)(p.Z,{size:"20"}),link:"/investments",isLoading:G}]}),(0,t.jsx)(a.Z,{className:"bg-divider-secondary"}),(0,t.jsx)(E,{nav:[{key:"direct-deposit",name:F("Deposit to Customer"),icon:(0,t.jsx)(v.Z,{size:"20"}),link:"/direct-deposit",visible:"agent"===W,isLoading:G,isActive:!0},{key:"deposit-request",name:F("Deposit Requests"),icon:(0,t.jsx)(v.Z,{size:"20"}),link:"/deposit-request",visible:"agent"===W,isLoading:G},{key:"withdraw-request",name:F("Withdraw Requests"),icon:(0,t.jsx)(y.Z,{size:"20"}),link:"/withdraw-request",visible:"agent"===W,isLoading:G},{key:"transaction-history",name:F("Transaction History"),icon:(0,t.jsx)(k.Z,{size:"20"}),link:"/transaction-history",isLoading:G},{key:"investments-history",name:F("Investments History"),icon:(0,t.jsx)(k.Z,{size:"20"}),link:"/investments-history",isLoading:G},{key:"settlements",name:F("Settlements"),icon:(0,t.jsx)(k.Z,{size:"20"}),link:"/settlements",visible:"agent"===W,isLoading:G},{key:"merchant-transactions",name:F("Merchant Transaction"),icon:(0,t.jsx)(j.Z,{size:"20"}),link:"/merchant-transactions",visible:"merchant"===W,isLoading:G},{key:"payment-requests",name:F("Payment Requests"),icon:(0,t.jsx)(w.Z,{size:"20"}),link:"/payment-requests",visible:"merchant"===W,isLoading:G},{key:"favorites",name:F("Favorites"),icon:(0,t.jsx)(Z.Z,{size:"20"}),link:"/favorites",visible:"agent"!==W,isLoading:G},{key:"contacts",name:F("Contacts"),icon:(0,t.jsx)(z.Z,{size:"20"}),link:"/contacts",visible:"agent"!==W,isLoading:G},{key:"wallets",name:F("Wallets"),icon:(0,t.jsx)(N.Z,{size:"20"}),link:"/wallets",isLoading:G},{key:"referral",name:F("Referral"),icon:(0,t.jsx)(A.Z,{size:"20"}),link:"/referral",isLoading:G},{key:"settings",name:F("Settings"),icon:(0,t.jsx)(q.Z,{size:"20"}),link:"/settings",isLoading:G}]})]})]})}n(2265)},35974:function(e,i,n){"use strict";n.d(i,{C:function(){return o}});var t=n(57437),s=n(90535);n(2265);var a=n(94508);let r=(0,s.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:i,variant:n,...s}=e;return(0,t.jsx)("div",{className:(0,a.ZP)(r({variant:n}),i),...s})}},17897:function(e,i,n){"use strict";n.d(i,{h:function(){return s}});var t=n(31117);function s(){let{data:e,...i}=(0,t.d)("/settings/global");return{settings:null==e?void 0:e.data,...i}}}}]);