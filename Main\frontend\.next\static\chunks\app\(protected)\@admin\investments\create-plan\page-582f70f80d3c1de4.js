(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[63926],{17370:function(e,r,t){Promise.resolve().then(t.bind(t,56320))},56320:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return v}});var i=t(57437),n=t(94097),s=t(83898),u=t(15681),a=t(79981),o=t(97751);async function d(e){try{let r={...e,isActive:e.isActive?1:0,isFeatured:e.isFeatured?1:0},t=await a.Z.post("/admin/investment-plans",r);return(0,o.B)(t)}catch(e){return(0,o.D)(e)}}var c=t(13590),l=t(99376),m=t(2265),f=t(29501),q=t(14438),p=t(31229);let g=p.z.object({name:p.z.string({required_error:"Name is required"}),description:p.z.string({required_error:"Description is required"}),isActive:p.z.boolean().default(!0),isFeatured:p.z.boolean().default(!1),isRange:p.z.string({required_error:"Investment type is required"}),minAmount:p.z.string({required_error:"Minimum amount is required"}),maxAmount:p.z.string({required_error:"Maximum amount is required"}),currency:p.z.string({required_error:"Currency is required"}),interestRate:p.z.string({required_error:"Interest rate is required"}),duration:p.z.string({required_error:"Duration is required"}),durationType:p.z.string({required_error:"Duration type is required"}),withdrawAfterMatured:p.z.string({required_error:"Withdraw after matured is required"})});function v(){let[e,r]=(0,m.useState)(1),t=(0,l.useRouter)(),[a,o]=(0,m.useTransition)(),p=(0,f.cI)({resolver:(0,c.F)(g),defaultValues:{name:"",description:"",isActive:!0,isFeatured:!1,isRange:"1",minAmount:"0",maxAmount:"0",currency:"",interestRate:"",duration:"",durationType:"daily",withdrawAfterMatured:"1"}});return(0,i.jsx)("div",{className:"h-[calc(100vh-157px)] overflow-y-auto bg-background p-4",children:(0,i.jsx)(u.l0,{...p,children:(0,i.jsxs)("form",{onSubmit:p.handleSubmit(e=>{o(async()=>{let r=await d(e);(null==r?void 0:r.status)?(q.toast.success("Investment plan created successfully"),t.push("/investments/manage-plans")):q.toast.error(null==r?void 0:r.message)})}),children:[1===e&&(0,i.jsx)(s.Z,{form:p,setStep:r}),2===e&&(0,i.jsx)(n.Z,{form:p,setStep:r,isPending:a})]})})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,38658,58939,80080,29355,92971,95030,1744],function(){return e(e.s=17370)}),_N_E=e.O()}]);