"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[49027],{49027:function(e,n,t){t.d(n,{Dx:function(){return er},VY:function(){return et},aV:function(){return en},dk:function(){return eo},fC:function(){return Q},h_:function(){return ee},jm:function(){return X},p8:function(){return R},x8:function(){return ei},xz:function(){return $}});var r=t(2265),o=t(6741),i=t(98575),l=t(73966),a=t(99255),u=t(80886),s=t(15278),d=t(99103),c=t(83832),f=t(71599),p=t(66840),m=t(86097),g=t(87922),v=t(5478),N=t(37053),y=t(57437),D="Dialog",[O,R]=(0,l.b)(D),[h,M]=O(D),b=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:i,onOpenChange:l,modal:s=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:D});return(0,y.jsx)(h,{scope:n,triggerRef:d,contentRef:c,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:t})};b.displayName=D;var w="DialogTrigger",x=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,l=M(w,t),a=(0,i.e)(n,l.triggerRef);return(0,y.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...r,ref:a,onClick:(0,o.M)(e.onClick,l.onOpenToggle)})});x.displayName=w;var I="DialogPortal",[j,E]=O(I,{forceMount:void 0}),C=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:i}=e,l=M(I,n);return(0,y.jsx)(j,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,y.jsx)(f.z,{present:t||l.open,children:(0,y.jsx)(c.h,{asChild:!0,container:i,children:e})}))})};C.displayName=I;var _="DialogOverlay",T=r.forwardRef((e,n)=>{let t=E(_,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,i=M(_,e.__scopeDialog);return i.modal?(0,y.jsx)(f.z,{present:r||i.open,children:(0,y.jsx)(F,{...o,ref:n})}):null});T.displayName=_;var A=(0,N.Z8)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=M(_,t);return(0,y.jsx)(g.Z,{as:A,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.WV.div,{"data-state":H(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",W=r.forwardRef((e,n)=>{let t=E(P,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,i=M(P,e.__scopeDialog);return(0,y.jsx)(f.z,{present:r||i.open,children:i.modal?(0,y.jsx)(k,{...o,ref:n}):(0,y.jsx)(U,{...o,ref:n})})});W.displayName=P;var k=r.forwardRef((e,n)=>{let t=M(P,e.__scopeDialog),l=r.useRef(null),a=(0,i.e)(n,t.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Ry)(e)},[]),(0,y.jsx)(V,{...e,ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null===(n=t.triggerRef.current)||void 0===n||n.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),U=r.forwardRef((e,n)=>{let t=M(P,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(V,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,l;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current||null===(l=t.triggerRef.current)||void 0===l||l.focus(),n.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:n=>{var r,l;null===(r=e.onInteractOutside)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"!==n.detail.originalEvent.type||(i.current=!0));let a=n.target;(null===(l=t.triggerRef.current)||void 0===l?void 0:l.contains(a))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&i.current&&n.preventDefault()}})}),V=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...u}=e,c=M(P,t),f=r.useRef(null),p=(0,i.e)(n,f);return(0,m.EW)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,y.jsx)(s.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...u,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(G,{titleId:c.titleId}),(0,y.jsx)(J,{contentRef:f,descriptionId:c.descriptionId})]})]})}),S="DialogTitle",L=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=M(S,t);return(0,y.jsx)(p.WV.h2,{id:o.titleId,...r,ref:n})});L.displayName=S;var z="DialogDescription",B=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=M(z,t);return(0,y.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:n})});B.displayName=z;var Z="DialogClose",q=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,i=M(Z,t);return(0,y.jsx)(p.WV.button,{type:"button",...r,ref:n,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function H(e){return e?"open":"closed"}q.displayName=Z;var K="DialogTitleWarning",[X,Y]=(0,l.k)(K,{contentName:P,titleName:S,docsSlug:"dialog"}),G=e=>{let{titleId:n}=e,t=Y(K),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&!document.getElementById(n)&&console.error(o)},[o,n]),null},J=e=>{let{contentRef:n,descriptionId:t}=e,o=Y("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=n.current)||void 0===e?void 0:e.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(i)},[i,n,t]),null},Q=b,$=x,ee=C,en=T,et=W,er=L,eo=B,ei=q},71599:function(e,n,t){t.d(n,{z:function(){return l}});var r=t(2265),o=t(98575),i=t(61188),l=e=>{var n,t;let l,u;let{present:s,children:d}=e,c=function(e){var n,t;let[o,l]=r.useState(),u=r.useRef(null),s=r.useRef(e),d=r.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=a(u.current);d.current="mounted"===c?e:"none"},[c]),(0,i.b)(()=>{let n=u.current,t=s.current;if(t!==e){let r=d.current,o=a(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.b)(()=>{if(o){var e;let n;let t=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(d.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(s),f="function"==typeof d?d({present:c.isPresent}):r.Children.only(d),p=(0,o.e)(c.ref,(l=null===(n=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in l&&l.isReactWarning?f.ref:(l=null===(t=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in l&&l.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof d||c.isPresent?r.cloneElement(f,{ref:p}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"}}]);