"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[65236],{8877:function(e,t,r){r.d(t,{Z:function(){return f}});var n=r(74677),o=r(2265),c=r(40718),a=r.n(c),l=["variant","color","size"],i=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM18 12.75h-5.25V18c0 .41-.34.75-.75.75s-.75-.34-.75-.75v-5.25H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5.25V6c0-.41.34-.75.75-.75s.75.34.75.75v5.25H18c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 18V6M16 12h2M6 12h5.66M12 18V6",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{opacity:".4",d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Z",fill:t}),o.createElement("path",{d:"M18 11.25h-5.25V6c0-.41-.34-.75-.75-.75s-.75.34-.75.75v5.25H6c-.41 0-.75.34-.75.75s.34.75.75.75h5.25V18c0 .41.34.75.75.75s.75-.34.75-.75v-5.25H18c.41 0 .75-.34.75-.75s-.34-.75-.75-.75Z",fill:t}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M6 12h12M12 18V6",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M18 12.75H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h12c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}),o.createElement("path",{d:"M12 18.75c-.41 0-.75-.34-.75-.75V6c0-.41.34-.75.75-.75s.75.34.75.75v12c0 .41-.34.75-.75.75Z",fill:t}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{opacity:".4",d:"M6 12h12",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M12 18V6",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return o.createElement(i,{color:t});case"Broken":return o.createElement(u,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(h,{color:t});case"TwoTone":return o.createElement(m,{color:t})}},f=(0,o.forwardRef)(function(e,t){var r=e.variant,c=e.color,a=e.size,i=(0,n._)(e,l);return o.createElement("svg",(0,n.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),p(r,c))});f.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="Add"},47480:function(e,t,r){r.d(t,{Z:function(){return f}});var n=r(74677),o=r(2265),c=r(40718),a=r.n(c),l=["variant","color","size"],i=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"m19.27 3.648-5.1 5.1H3.2c-.66 0-1.2-.54-1.2-1.2v-.01c0-2.29 1.85-4.14 4.14-4.14h11.71c.5 0 .98.09 1.42.25ZM12.67 10.25l-5.5 5.5H6c-.41 0-.75.34-.75.75 0 .31.2.59.48.69l-2.35 2.35A4.087 4.087 0 0 1 2 16.46v-5.01c0-.66.54-1.2 1.2-1.2h9.47ZM21.999 11v5.46c0 2.29-1.85 4.14-4.14 4.14h-9.05c-.89 0-1.34-1.08-.71-1.71l1.35-1.35a.99.99 0 0 1 .71-.29h4.34c.41 0 .75-.34.75-.75s-.34-.75-.75-.75h-.84c-.89 0-1.34-1.08-.71-1.71l3.46-3.46c.38-.37.89-.58 1.42-.58h3.17c.55 0 1 .45 1 1Z",fill:t}),o.createElement("path",{d:"M21.769 2.229c-.3-.3-.79-.3-1.09 0l-18.45 18.46c-.3.3-.3.79 0 1.09a.758.758 0 0 0 1.08-.01l18.46-18.46c.31-.3.31-.78 0-1.08Z",fill:t}),o.createElement("path",{d:"m21.769 3.309-1.16 1.16-4.28 4.28-13.02 13.02c-.15.15-.34.23-.54.23s-.39-.08-.54-.23a.743.743 0 0 1 0-1.08l1.15-1.15 2.35-2.35 13.54-13.54 1.42-1.42c.3-.31.78-.31 1.08 0 .31.3.31.78 0 1.08ZM22 7.41c0-.89-1.08-1.34-1.71-.71L20 7l-.29.29c-.63.63-.19 1.71.7 1.71H21c.55 0 1-.45 1-1v-.59Z",fill:t}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M2 8.5h13.24M6 16.5h1.29M11 16.5h3.5",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M7.98 20.5h9.58c3.56 0 4.44-.88 4.44-4.39V6.89M2 14.969v1.14c0 2.34.39 3.51 1.71 4.03M19.99 3.75c-.62-.18-1.42-.25-2.43-.25H6.44C2.89 3.5 2 4.38 2 7.89v3.05M22 2 2 22",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{opacity:".4",d:"M21.998 9v7.46c0 2.29-1.86 4.14-4.15 4.14H6.398l3.57-3.57 1.28-1.28L17.998 9h4ZM15 9 4 20c-1.2-.72-2-2.04-2-3.54V9h13Z",fill:t}),o.createElement("path",{d:"M22 7.541v1.46h-4l3.47-3.47c.34.59.53 1.28.53 2.01ZM20 3.998l-5 5H2v-1.46c0-2.29 1.86-4.14 4.15-4.14h11.7c.79 0 1.52.22 2.15.6ZM8.21 15.79l-1.46 1.46H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h2c.07 0 .14.01.21.04ZM15.249 16.5c0 .41-.34.75-.75.75h-4c-.2 0-.39-.09-.53-.22l1.28-1.28h3.25c.41 0 .75.34.75.75Z",fill:t}),o.createElement("path",{d:"M21.769 2.229c-.3-.3-.79-.3-1.09 0l-18.45 18.46c-.3.3-.3.79 0 1.09a.758.758 0 0 0 1.08-.01l18.46-18.46c.31-.3.31-.78 0-1.08Z",fill:t}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M2 8.5h13.24M6 16.5h1.29M11 16.5h3.5",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M7.98 20.5h9.58c3.56 0 4.44-.88 4.44-4.39V6.89M19.99 3.75c-.62-.18-1.42-.25-2.43-.25H6.44C2.89 3.5 2 4.38 2 7.89v8.21c0 2.34.39 3.51 1.71 4.03M22 2 2 22",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M15.24 9.25H2c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h13.24c.41 0 .75.34.75.75s-.34.75-.75.75ZM7.29 17.25H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h1.29a.749.749 0 1 1 0 1.5ZM14.5 17.25H11c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h3.5c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}),o.createElement("path",{d:"M17.56 21.25H7.98c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h9.58c3.14 0 3.69-.54 3.69-3.64V6.89c0-.41.34-.75.75-.75s.75.34.75.75v9.21c0 3.95-1.21 5.15-5.19 5.15ZM3.71 20.89c-.09 0-.19-.02-.28-.05-1.81-.72-2.18-2.41-2.18-4.73V7.89c0-3.94 1.21-5.14 5.19-5.14h11.11c1.14 0 1.98.09 2.64.29.4.12.62.53.51.93-.12.4-.54.62-.93.51-.52-.15-1.22-.22-2.22-.22H6.44c-3.14 0-3.69.54-3.69 3.64v8.21c0 2.39.43 3.02 1.24 3.34a.745.745 0 0 1-.28 1.44Z",fill:t}),o.createElement("path",{d:"M2.001 22.751c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l20-20c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-20 20c-.15.15-.34.22-.53.22Z",fill:t}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M2 8.5h13.24M6 16.5h1.29",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{opacity:".4",d:"M11 16.5h3.5",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{opacity:".4",d:"M7.98 20.5h9.58c3.56 0 4.44-.88 4.44-4.39V6.89",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M19.99 3.75c-.62-.18-1.42-.25-2.43-.25H6.44C2.89 3.5 2 4.38 2 7.89v8.21c0 2.34.39 3.51 1.71 4.03M22 2 2 22",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return o.createElement(i,{color:t});case"Broken":return o.createElement(u,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(h,{color:t});case"TwoTone":return o.createElement(m,{color:t})}},f=(0,o.forwardRef)(function(e,t){var r=e.variant,c=e.color,a=e.size,i=(0,n._)(e,l);return o.createElement("svg",(0,n.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),p(r,c))});f.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="CardSlash"},54882:function(e,t,r){r.d(t,{Z:function(){return f}});var n=r(74677),o=r(2265),c=r(40718),a=r.n(c),l=["variant","color","size"],i=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M16 12.9v4.2c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1v-4.2C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9Z",fill:t}),o.createElement("path",{d:"M17.1 2h-4.2C9.817 2 8.37 3.094 8.07 5.739c-.064.553.395 1.011.952 1.011H11.1c4.2 0 6.15 1.95 6.15 6.15v2.078c0 .557.457 1.015 1.01.952C20.907 15.63 22 14.183 22 11.1V6.9C22 3.4 20.6 2 17.1 2Z",fill:t}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M2 12.9C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9v4.2c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M22 11.1c0 3.5-1.4 4.9-4.9 4.9H16v-3.1C16 9.4 14.6 8 11.1 8H8V6.9C8 3.4 9.4 2 12.9 2h4.2C20.6 2 22 3.4 22 6.9",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M16 12.9v4.2c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1v-4.2C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9Z",fill:t}),o.createElement("path",{opacity:".4",d:"M17.1 2h-4.2C9.45 2 8.05 3.37 8.01 6.75h3.09c4.2 0 6.15 1.95 6.15 6.15v3.09c3.38-.04 4.75-1.44 4.75-4.89V6.9C22 3.4 20.6 2 17.1 2Z",fill:t}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M16 12.9v4.2c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1v-4.2C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9z"}),o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M22 6.9v4.2c0 3.5-1.4 4.9-4.9 4.9H16v-3.1C16 9.4 14.6 8 11.1 8H8V6.9C8 3.4 9.4 2 12.9 2h4.2C20.6 2 22 3.4 22 6.9z"}))},h=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M11.1 22.75H6.9c-3.91 0-5.65-1.74-5.65-5.65v-4.2c0-3.91 1.74-5.65 5.65-5.65h4.2c3.91 0 5.65 1.74 5.65 5.65v4.2c0 3.91-1.74 5.65-5.65 5.65Zm-4.2-14c-3.1 0-4.15 1.05-4.15 4.15v4.2c0 3.1 1.05 4.15 4.15 4.15h4.2c3.1 0 4.15-1.05 4.15-4.15v-4.2c0-3.1-1.05-4.15-4.15-4.15H6.9Z",fill:t}),o.createElement("path",{d:"M17.1 16.75H16c-.41 0-.75-.34-.75-.75v-3.1c0-3.1-1.05-4.15-4.15-4.15H8c-.41 0-.75-.34-.75-.75V6.9c0-3.91 1.74-5.65 5.65-5.65h4.2c3.91 0 5.65 1.74 5.65 5.65v4.2c0 3.91-1.74 5.65-5.65 5.65Zm-.35-1.5h.35c3.1 0 4.15-1.05 4.15-4.15V6.9c0-3.1-1.05-4.15-4.15-4.15h-4.2c-3.1 0-4.15 1.05-4.15 4.15v.35h2.35c3.91 0 5.65 1.74 5.65 5.65v2.35Z",fill:t}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M16 12.9v4.2c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1v-4.2C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{opacity:".4",d:"M22 6.9v4.2c0 3.5-1.4 4.9-4.9 4.9H16v-3.1C16 9.4 14.6 8 11.1 8H8V6.9C8 3.4 9.4 2 12.9 2h4.2C20.6 2 22 3.4 22 6.9Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return o.createElement(i,{color:t});case"Broken":return o.createElement(u,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(h,{color:t});case"TwoTone":return o.createElement(m,{color:t})}},f=(0,o.forwardRef)(function(e,t){var r=e.variant,c=e.color,a=e.size,i=(0,n._)(e,l);return o.createElement("svg",(0,n.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),p(r,c))});f.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="Copy"},32489:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},99376:function(e,t,r){var n=r(35475);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useSelectedLayoutSegment")&&r.d(t,{useSelectedLayoutSegment:function(){return n.useSelectedLayoutSegment}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})},12119:function(e,t,r){Object.defineProperty(t,"$",{enumerable:!0,get:function(){return o}});let n=r(83079);function o(e){let{createServerReference:t}=r(6671);return t(e,n.callServer)}},55156:function(e,t,r){r.d(t,{f:function(){return u}});var n=r(2265),o=r(66840),c=r(57437),a="horizontal",l=["horizontal","vertical"],i=n.forwardRef((e,t)=>{let{decorative:r,orientation:n=a,...i}=e,u=l.includes(n)?n:a;return(0,c.jsx)(o.WV.div,{"data-orientation":u,...r?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...i,ref:t})});i.displayName="Separator";var u=i},50721:function(e,t,r){r.d(t,{bU:function(){return w},fC:function(){return C}});var n=r(2265),o=r(6741),c=r(98575),a=r(73966),l=r(80886),i=r(6718),u=r(90420),s=r(66840),d=r(57437),h="Switch",[m,p]=(0,a.b)(h),[f,v]=m(h),k=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:a,checked:i,defaultChecked:u,required:m,disabled:p,value:v="on",onCheckedChange:k,form:E,...M}=e,[C,w]=n.useState(null),Z=(0,c.e)(t,e=>w(e)),y=n.useRef(!1),H=!C||E||!!C.closest("form"),[V,b]=(0,l.T)({prop:i,defaultProp:null!=u&&u,onChange:k,caller:h});return(0,d.jsxs)(f,{scope:r,checked:V,disabled:p,children:[(0,d.jsx)(s.WV.button,{type:"button",role:"switch","aria-checked":V,"aria-required":m,"data-state":g(V),"data-disabled":p?"":void 0,disabled:p,value:v,...M,ref:Z,onClick:(0,o.M)(e.onClick,e=>{b(e=>!e),H&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),H&&(0,d.jsx)(L,{control:C,bubbles:!y.current,name:a,value:v,checked:V,required:m,disabled:p,form:E,style:{transform:"translateX(-100%)"}})]})});k.displayName=h;var E="SwitchThumb",M=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=v(E,r);return(0,d.jsx)(s.WV.span,{"data-state":g(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});M.displayName=E;var L=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:a,bubbles:l=!0,...s}=e,h=n.useRef(null),m=(0,c.e)(h,t),p=(0,i.D)(a),f=(0,u.t)(o);return n.useEffect(()=>{let e=h.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&t){let r=new Event("click",{bubbles:l});t.call(e,a),e.dispatchEvent(r)}},[p,a,l]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...s,tabIndex:-1,ref:m,style:{...s.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function g(e){return e?"checked":"unchecked"}L.displayName="SwitchBubbleInput";var C=k,w=M}}]);