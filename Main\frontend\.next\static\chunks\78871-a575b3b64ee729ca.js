"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[78871],{15054:function(e,t,s){s.d(t,{h:function(){return r}});var n=s(57437);function r(e){let{title:t,subTitle:s}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"mb-2.5 text-sm text-secondary-text",children:s}),(0,n.jsx)("h1",{className:"text-[28px] font-medium leading-10 md:text-[32px]",children:t})]})}},21318:function(e,t,s){s.d(t,{Z:function(){return b}});var n=s(57437),r=s(2265),i=s(15054),a=s(41709),o=s(52323),l=s(39785),d=s(85487),u=s(62869),c=s(15681),h=s(95186),v=s(26815),m=s(74991),p=s(6512),x=s(71792),f=s(13590),g=s(90433),j=s(22291),y=s(29501),w=s(43949);function b(e){let{onPrev:t,onSubmit:s,nextButtonLabel:b,title:C,subTitle:N,isLoading:I=!1,formData:A}=e,{t:z}=(0,w.$G)(),k=(0,y.cI)({resolver:(0,f.F)(x.Zg),defaultValues:{title:"",dateOfBirth:void 0,street:"",country:"",city:"",zipCode:""}});return r.useEffect(()=>{A&&k.reset({...A})},[]),(0,n.jsx)(c.l0,{...k,children:(0,n.jsxs)("form",{onSubmit:k.handleSubmit(s),children:[(0,n.jsx)(i.h,{title:C,subTitle:N}),(0,n.jsx)("div",{className:"mt-6 flex h-[5px] items-center",children:(0,n.jsx)(p.Z,{className:"bg-divider"})}),(0,n.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)(c.lX,{required:!0,children:z("Select your gender")}),(0,n.jsx)("div",{className:"grid grid-cols-12 gap-4",children:(0,n.jsx)(c.Wi,{control:k.control,name:"title",render:e=>{let{field:t}=e;return(0,n.jsxs)(c.xJ,{className:"col-span-12",children:[(0,n.jsx)(c.NI,{children:(0,n.jsxs)(m.E,{defaultValue:t.value,onValueChange:t.onChange,className:"grid-cols-12 gap-4",children:[(0,n.jsxs)(v.Z,{htmlFor:"male","data-active":"male"===t.value,className:"col-span-12 flex h-12 w-full cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[(0,n.jsx)(m.m,{id:"male",value:"male",className:"absolute left-0 top-0 opacity-0"}),(0,n.jsx)("span",{children:z("Male")})]}),(0,n.jsxs)(v.Z,{htmlFor:"female","data-active":"female"===t.value,className:"col-span-12 flex h-12 cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[(0,n.jsx)(m.m,{id:"female",value:"female",className:"absolute left-0 top-0 opacity-0"}),(0,n.jsx)("span",{children:z("Female")})]})]})}),(0,n.jsx)(c.zG,{})]})}})})]}),(0,n.jsx)(c.Wi,{control:k.control,name:"dateOfBirth",render:e=>{let{field:t}=e;return(0,n.jsxs)(c.xJ,{children:[(0,n.jsx)(c.lX,{children:z("Select your birth date")}),(0,n.jsx)(c.NI,{children:(0,n.jsx)(l.M,{value:t.value,onChange:t.onChange})}),(0,n.jsx)(c.zG,{})]})}}),(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,n.jsx)(v.Z,{className:"col-span-12",children:z("Your full mailing address")}),(0,n.jsx)(c.Wi,{control:k.control,name:"street",render:e=>{let{field:t}=e;return(0,n.jsxs)(c.xJ,{className:"col-span-12",children:[(0,n.jsx)(c.NI,{children:(0,n.jsx)(h.I,{type:"text",placeholder:z("Address Line"),...t})}),(0,n.jsx)(c.zG,{})]})}}),(0,n.jsx)(c.Wi,{control:k.control,name:"country",render:e=>{let{field:t}=e;return(0,n.jsxs)(c.xJ,{className:"col-span-12",children:[(0,n.jsx)(c.NI,{children:(0,n.jsx)(o.g,{defaultCountry:t.value,onSelectChange:e=>t.onChange(e.code.cca2)})}),(0,n.jsx)(c.zG,{})]})}}),(0,n.jsx)(c.Wi,{control:k.control,name:"city",render:e=>{let{field:t}=e;return(0,n.jsxs)(c.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(c.NI,{children:(0,n.jsx)(h.I,{type:"text",placeholder:z("City"),...t})}),(0,n.jsx)(c.zG,{})]})}}),(0,n.jsx)(c.Wi,{control:k.control,name:"zipCode",render:e=>{let{field:t}=e;return(0,n.jsxs)(c.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(c.NI,{children:(0,n.jsx)(h.I,{type:"text",placeholder:z("Zip Code"),...t})}),(0,n.jsx)(c.zG,{})]})}})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,n.jsxs)(u.z,{className:"border-btn-outline-border p-4 text-base font-medium leading-[22px]",variant:"outline",type:"button",onClick:t,children:[(0,n.jsx)(g.Z,{size:24}),z("Back")]}),(0,n.jsxs)(u.z,{disabled:I,className:"w-[286px] p-4 text-base font-medium leading-[22px]",children:[(0,n.jsxs)(a.J,{condition:!I,children:[b,(0,n.jsx)(j.Z,{size:16})]}),(0,n.jsx)(a.J,{condition:I,children:(0,n.jsx)(d.Loader,{className:"text-background"})})]})]})]})]})})}},99723:function(e,t,s){s.d(t,{Z:function(){return b}});var n=s(57437),r=s(15054),i=s(18629),a=s(56353),o=s(62869),l=s(19060),d=s(15681),u=s(95186),c=s(26815),h=s(6512),v=s(3612),m=s(71792),p=s(13590),x=s(90433),f=s(22291),g=s(99376),j=s(2265),y=s(29501),w=s(43949);function b(e){let{onPrev:t,onSubmit:s,title:b,subTitle:C,formData:N}=e,{t:I}=(0,w.$G)(),A=(0,g.useSearchParams)(),{deviceLocation:z}=(0,v.a)(),k=(0,y.cI)({resolver:(0,p.F)(m.cN),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:"",phone:"",referralCode:"",termAndCondition:void 0}});return j.useEffect(()=>{if(N&&k.reset({...N}),A.get("referral")){var e;k.setValue("referralCode",null!==(e=A.get("referral"))&&void 0!==e?e:"")}},[]),(0,n.jsx)(d.l0,{...k,children:(0,n.jsxs)("form",{onSubmit:k.handleSubmit(s),children:[(0,n.jsx)(r.h,{title:b,subTitle:C}),(0,n.jsx)("div",{className:"my-6 flex h-[5px] items-center",children:(0,n.jsx)(h.Z,{className:"bg-divider"})}),(0,n.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,n.jsx)(d.Wi,{control:k.control,name:"firstName",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,n.jsx)(d.lX,{children:I("First Name")}),(0,n.jsx)(d.NI,{children:(0,n.jsx)(u.I,{type:"text",placeholder:I("Enter first name"),...t})}),(0,n.jsx)(d.zG,{})]})}}),(0,n.jsx)(d.Wi,{control:k.control,name:"lastName",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,n.jsx)(d.lX,{children:I("Last Name")}),(0,n.jsx)(d.NI,{children:(0,n.jsx)(u.I,{type:"text",placeholder:I("Enter last name"),...t})}),(0,n.jsx)(d.zG,{})]})}})]}),(0,n.jsx)(d.Wi,{control:k.control,name:"email",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{children:[(0,n.jsx)(d.lX,{children:I("Email")}),(0,n.jsx)(d.NI,{children:(0,n.jsx)(u.I,{type:"email",placeholder:I("Enter your email address"),...t})}),(0,n.jsx)(d.zG,{})]})}}),(0,n.jsx)(d.Wi,{control:k.control,name:"phone",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{className:"w-full",children:[(0,n.jsx)(d.lX,{children:I("Phone")}),(0,n.jsx)(d.NI,{children:(0,n.jsx)(i.E,{onChange:t.onChange,onBlur:e=>{e?k.setError("phone",{type:"custom",message:e}):k.clearErrors("phone")},options:{initialCountry:null==z?void 0:z.countryCode}})}),(0,n.jsx)(d.zG,{})]})}}),(0,n.jsx)(d.Wi,{control:k.control,name:"password",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{children:[(0,n.jsx)(d.lX,{children:I("Password")}),(0,n.jsx)(d.NI,{children:(0,n.jsx)(a.W,{placeholder:I("Create a strong password"),...t})}),(0,n.jsx)(d.zG,{})]})}}),(0,n.jsx)(d.Wi,{control:k.control,name:"confirmPassword",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{children:[(0,n.jsx)(d.lX,{children:I("Confirm Password")}),(0,n.jsx)(d.NI,{children:(0,n.jsx)(a.W,{placeholder:I("Enter the password again"),...t})}),(0,n.jsx)(d.zG,{})]})}}),(0,n.jsx)(d.Wi,{control:k.control,name:"referralCode",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{children:[(0,n.jsx)(d.lX,{children:I("Referral (optional)")}),(0,n.jsx)(d.NI,{children:(0,n.jsx)(u.I,{placeholder:I("Enter referral code (if applicable)"),...t})}),(0,n.jsx)(d.zG,{})]})}}),(0,n.jsx)(d.Wi,{control:k.control,name:"termAndCondition",render:e=>{let{field:t}=e;return(0,n.jsxs)(d.xJ,{children:[(0,n.jsx)(d.NI,{children:(0,n.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,n.jsx)(l.X,{id:"termAndCondition",checked:t.value,onCheckedChange:t.onChange}),(0,n.jsx)(c.Z,{className:"text-sm font-normal leading-5 text-foreground",htmlFor:"termAndCondition",children:I("I read and accept the general terms & conditions of use")})]})}),(0,n.jsx)(d.zG,{})]})}}),(0,n.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,n.jsxs)(o.z,{variant:"outline",type:"button",onClick:t,className:"h-10 w-[102px] text-base font-medium leading-[22px] text-foreground",children:[(0,n.jsx)(x.Z,{size:"24"}),I("Back")]}),(0,n.jsxs)(o.z,{type:"submit",className:"w-[286px] rounded-[8px] px-4 py-2 text-base font-medium leading-[22px]",children:[I("Next"),(0,n.jsx)(f.Z,{size:"16"})]})]})]})]})})}},80114:function(e,t,s){s.d(t,{default:function(){return o}});var n=s(57437),r=s(85487),i=s(94508),a=s(43949);function o(e){let{className:t}=e,{t:s}=(0,a.$G)();return(0,n.jsx)("div",{className:(0,i.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,n.jsx)(r.Loader,{title:s("Loading..."),className:"text-foreground"})})}},56353:function(e,t,s){s.d(t,{W:function(){return u}});var n=s(57437),r=s(2265),i=s(62869),a=s(95186),o=s(94508),l=s(93824),d=s(32706);let u=r.forwardRef((e,t)=>{let{className:s,type:u,...c}=e,[h,v]=r.useState(!1);return(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(a.I,{type:h?"text":"password",className:(0,o.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...c}),(0,n.jsx)(i.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),v(e=>!e)},children:h?(0,n.jsx)(l.Z,{}):(0,n.jsx)(d.Z,{})})]})});u.displayName="PasswordInput"},17062:function(e,t,s){s.d(t,{Z:function(){return p},O:function(){return m}});var n=s(57437),r=s(80114);s(83079);var i=(0,s(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=s(31117),o=s(79981),l=s(78040),d=s(83130);class u{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var c=s(99376),h=s(2265);let v=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),m=()=>h.useContext(v);function p(e){let{children:t}=e,[s,m]=h.useState("Desktop"),[p,x]=h.useState(!1),[f,g]=h.useState(),{data:j,isLoading:y,error:w,mutate:b}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:C,isLoading:N}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:I,isLoading:A}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),z=(0,c.useRouter)(),k=(0,c.usePathname)();h.useEffect(()=>{(async()=>{m((await i()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;m(e<768?"Mobile":e<1024?"Tablet":"Desktop"),x(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");g(new u(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{w&&!l.sp.includes(k)&&z.push("/signin")},[w]);let E=h.useMemo(()=>{var e,t,n;return{isAuthenticate:!!(null==j?void 0:null===(e=j.data)||void 0===e?void 0:e.login),auth:(null==j?void 0:null===(t=j.data)||void 0===t?void 0:t.user)?new d.n(null==j?void 0:null===(n=j.data)||void 0===n?void 0:n.user):null,isLoading:y,deviceLocation:f,refreshAuth:()=>b(j),isExpanded:p,device:s,setIsExpanded:x,branding:null==C?void 0:C.data,googleAnalytics:(null==I?void 0:I.data)?{active:null==I?void 0:I.data.active,apiKey:null==I?void 0:I.data.apiKey}:{active:!1,apiKey:""}}},[j,f,p,s]),L=!y&&!N&&!A;return(0,n.jsx)(v.Provider,{value:E,children:L?t:(0,n.jsx)(r.default,{})})}},56766:function(e,t,s){s.d(t,{Pq:function(){return d},Pr:function(){return c},jd:function(){return l},o8:function(){return u},s8:function(){return h}});var n=s(2901),r=s(79981),i=s(78040),a=s(43577);let o=e=>{let t={...e,email:e.email,password:e.password,passwordConfirmation:e.confirmPassword,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,firstName:e.firstName,lastName:e.lastName,phone:e.phone,gender:e.title.toLowerCase(),dob:(0,n.WU)(e.dateOfBirth,"yyyy-MM-dd"),roleId:e.accountType,acceptTermsCondition:e.termAndCondition};if(void 0!==e.merchant)return{...t,merchant:{...e.merchant,name:e.merchant.name,email:e.merchant.email,proof:e.merchant.license,addressLine:e.merchant.street,zipCode:e.merchant.zipCode,countryCode:e.merchant.country,city:e.merchant.city}};if(void 0!==e.agent){var s,r,i;return{...t,agent:{...e.agent,proof:"agent",occupation:e.agent.occupation,email:e.email,name:e.agent.name,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,whatsapp:e.agent.whatsapp,agreeFundingCustomer:(null===(s=e.agent.fundingByAgentAccount)||void 0===s?void 0:s.toLowerCase())==="yes",agreeHonest:(null===(r=e.agent.honestyAgreement)||void 0===r?void 0:r.toLowerCase())==="yes",agreeRechargeCustomer:(null===(i=e.agent.rechargeAgreement)||void 0===i?void 0:i.toLowerCase())==="yes"}}}return t};async function l(e){var t,s,n,l,d,u,c,h,v,m,p,x,f,g;try{let n=await r.Z.post("".concat(i.rH.API_URL,"/auth/register"),o(e));return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(s=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==s?s:"",data:{email:e.email}}}catch(r){let e=500,t="Internal Server Error",s="An unknown error occurred";return(0,a.IZ)(r)&&(e=null!==(p=null===(n=r.response)||void 0===n?void 0:n.status)&&void 0!==p?p:500,t=null!==(x=null===(l=r.response)||void 0===l?void 0:l.statusText)&&void 0!==x?x:"Internal Server Error",s=null!==(g=null!==(f=null===(u=r.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.message)&&void 0!==f?f:null===(m=r.response)||void 0===m?void 0:null===(v=m.data)||void 0===v?void 0:null===(h=v.messages)||void 0===h?void 0:null===(c=h[0])||void 0===c?void 0:c.message)&&void 0!==g?g:r.message),{statusCode:e,statusText:t,status:!1,message:s}}}async function d(e){var t,s,n,l,d,u,c,h,v;try{let n=await r.Z.post("".concat(i.rH.API_URL,"/auth/register"),o(e));return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(s=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==s?s:"",data:{email:e.email}}}catch(r){let e=500,t="Internal Server Error",s="An unknown error occurred";return(0,a.IZ)(r)?(e=null!==(c=null===(n=r.response)||void 0===n?void 0:n.status)&&void 0!==c?c:500,t=null!==(h=null===(l=r.response)||void 0===l?void 0:l.statusText)&&void 0!==h?h:"Internal Server Error",s=null!==(v=null===(u=r.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.message)&&void 0!==v?v:r.message):r instanceof Error&&(s=r.message),{statusCode:e,statusText:t,status:!1,message:s}}}async function u(e){var t,s,n,l,d,u,c,h,v;try{let n=await r.Z.post("".concat(i.rH.API_URL,"/auth/register"),o(e));return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(s=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==s?s:"",data:{email:e.email}}}catch(r){let e=500,t="Internal Server Error",s="An unknown error occurred";return(0,a.IZ)(r)?(e=null!==(c=null===(n=r.response)||void 0===n?void 0:n.status)&&void 0!==c?c:500,t=null!==(h=null===(l=r.response)||void 0===l?void 0:l.statusText)&&void 0!==h?h:"Internal Server Error",s=null!==(v=null===(u=r.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.message)&&void 0!==v?v:r.message):r instanceof Error&&(s=r.message),{statusCode:e,statusText:t,status:!1,message:s}}}async function c(e){var t,s,n,o,l,d,u,c,h;try{let n=await r.Z.post("".concat(i.rH.API_URL,"/auth/resend-verify-email"),{email:e});return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(s=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==s?s:""}}catch(r){let e=500,t="Internal Server Error",s="An unknown error occurred";return(0,a.IZ)(r)?(e=null!==(u=null===(n=r.response)||void 0===n?void 0:n.status)&&void 0!==u?u:500,t=null!==(c=null===(o=r.response)||void 0===o?void 0:o.statusText)&&void 0!==c?c:"Internal Server Error",s=null!==(h=null===(d=r.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.message)&&void 0!==h?h:r.message):r instanceof Error&&(s=r.message),{statusCode:e,statusText:t,status:!1,message:s}}}async function h(e){var t,s,n,o,l,d,u,c,h;let{token:v}=e;try{let e=await r.Z.post("".concat(i.rH.API_URL,"/auth/verify-email"),{token:v});return{statusCode:e.status,statusText:e.statusText,status:201===e.status||200===e.status,message:null!==(s=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==s?s:""}}catch(r){let e=500,t="Internal Server Error",s="An unknown error occurred";return(0,a.IZ)(r)?(e=null!==(u=null===(n=r.response)||void 0===n?void 0:n.status)&&void 0!==u?u:500,t=null!==(c=null===(o=r.response)||void 0===o?void 0:o.statusText)&&void 0!==c?c:"Internal Server Error",s=null!==(h=null===(d=r.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.message)&&void 0!==h?h:r.message):r instanceof Error&&(s=r.message),{statusCode:e,statusText:t,status:!1,message:s}}}},3612:function(e,t,s){s.d(t,{a:function(){return r}});var n=s(17062);let r=()=>{let e=(0,n.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},21251:function(e,t,s){s.d(t,{T:function(){return r}});var n=s(17062);let r=()=>{let{branding:e}=(0,n.O)();return e}},31117:function(e,t,s){s.d(t,{d:function(){return i}});var n=s(79981),r=s(85323);let i=(e,t)=>(0,r.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},74539:function(e,t,s){s.d(t,{k:function(){return n}});class n{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,s){s.d(t,{n:function(){return l}});class n{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var r=s(84937);class i{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=s(66419),o=s(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new r.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new i(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new n(e.agent):void 0}}},84937:function(e,t,s){s.d(t,{O:function(){return r}});var n=s(74539);class r{constructor(e){var t,s;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(s=e.phone)||void 0===s?void 0:s.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new n.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,s){s.d(t,{u:function(){return n}});class n{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}}]);