"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[12042],{41709:function(e,r,t){function n(e){let{condition:r,children:t}=e;return r?t:null}t.d(r,{J:function(){return n}}),t(2265)},52323:function(e,r,t){t.d(r,{g:function(){return f}});var n=t(57437),a=t(2265),i=t(85487),s=t(41062),o=t(23518),l=t(57054),d=t(40593),c=t(94508),u=t(36887),m=t(43949);function f(e){var r,t;let{allCountry:f=!1,defaultValue:p,defaultCountry:x,onSelectChange:h,disabled:g=!1,triggerClassName:y,arrowClassName:v,flagClassName:b,display:j,placeholderClassName:N,align:w="start",side:C="bottom"}=e,{t:_}=(0,m.$G)(),{countries:q,getCountryByCode:z,isLoading:Z}=(0,d.F)(),[P,S]=a.useState(!1),[k,F]=a.useState(p);return a.useEffect(()=>{p&&F(p)},[p]),a.useEffect(()=>{(async()=>{x&&await z(x,e=>{e&&(F(e),h(e))})})()},[x]),(0,n.jsxs)(l.J2,{open:P,onOpenChange:S,children:[(0,n.jsxs)(l.xo,{disabled:g,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",y),children:[k?(0,n.jsx)("div",{className:"flex flex-1 items-center",children:(0,n.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,n.jsx)(s.W,{className:b,countryCode:(null===(r=k.code)||void 0===r?void 0:r.cca2)==="*"?"UN":null===(t=k.code)||void 0===t?void 0:t.cca2}),void 0!==j?j(k):(0,n.jsx)("span",{children:k.name})]})}):(0,n.jsx)("span",{className:(0,c.ZP)("text-placeholder",N),children:_("Select country")}),(0,n.jsx)(u.Z,{className:(0,c.ZP)("size-6",v)})]}),(0,n.jsx)(l.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:w,side:C,children:(0,n.jsxs)(o.mY,{children:[(0,n.jsx)(o.sZ,{placeholder:_("Search...")}),(0,n.jsx)(o.e8,{children:(0,n.jsxs)(o.fu,{children:[Z&&(0,n.jsx)(i.Loader,{}),f&&(0,n.jsxs)(o.di,{value:_("All countries"),onSelect:()=>{F({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),h({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),S(!1)},children:[(0,n.jsx)(s.W,{countryCode:"UN"}),(0,n.jsx)("span",{className:"pl-1.5",children:_("All countries")})]}),null==q?void 0:q.map(e=>"officially-assigned"===e.status?(0,n.jsxs)(o.di,{value:e.name,onSelect:()=>{F(e),h(e),S(!1)},children:[(0,n.jsx)(s.W,{countryCode:e.code.cca2}),(0,n.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},39785:function(e,r,t){t.d(r,{M:function(){return u}});var n=t(57437),a=t(1098),i=t(57054),s=t(94508),o=t(2901),l=t(76534),d=t(2265),c=t(43949);let u=d.forwardRef((e,r)=>{let{value:t,onChange:u,className:m,placeholderClassName:f,options:p}=e,{t:x}=(0,c.$G)(),[h,g]=d.useState(!1);return(0,n.jsxs)(i.J2,{open:h,onOpenChange:g,children:[(0,n.jsxs)(i.xo,{disabled:!!(null==p?void 0:p.disabled),className:(0,s.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",m),children:[(0,n.jsx)("div",{ref:r,className:"flex flex-1 items-center",children:(0,n.jsx)("div",{className:"flex flex-1 items-center gap-2 text-left",children:t?(0,o.WU)(t,"dd/MM/yyyy"):(0,n.jsx)("span",{className:(0,s.ZP)("text-placeholder",f),children:x("Pick a Date")})})}),(0,n.jsx)(l.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),(0,n.jsx)(i.yk,{className:"w-auto p-0",align:"start",children:(0,n.jsx)(a.f,{...p,mode:"single",initialFocus:!0,selected:null!=t?t:void 0,onSelect:e=>{u(e),g(!1)}})})]})})},18629:function(e,r,t){t.d(r,{E:function(){return _}});var n=t(57437),a=t(85487),i=t(41062),s=t(23518),o=t(95186),l=t(57054),d=t(40593),c=t(94508),u=t(95550),m=t(36887),f=t(58414),p=t(78286),x=t(19368),h=t(68953),g=t(56555),y=t(5874),v=t(19615),b=t(93781),j=t(83057),N=t(43949),w=t(2265);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function _(e){let{value:r,defaultValue:t="",onChange:a,onBlur:i,disabled:s,inputClassName:l,options:d}=e,[u,m]=(0,w.useState)(null!=t?t:""),[b,N]=(0,w.useState)(""),[_,z]=(0,w.useState)(null==d?void 0:d.initialCountry),Z=e=>{if(e)try{let r=f.S(e,_);r?(z(r.country),N("+".concat(r.countryCallingCode)),m(r.formatNational())):m(e)}catch(r){m(e)}else m(e)};(0,w.useEffect)(()=>{r&&Z(r)},[r]);let P=p.L(_||(null==d?void 0:d.initialCountry)||"US",j.Z);return(0,n.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(q,{country:_,disabled:s,initialCountry:null==d?void 0:d.initialCountry,onSelect:e=>{var r;let t=null===(r=e.code.cca2)||void 0===r?void 0:r.toUpperCase(),n=x.G(t);N("+".concat(n)),z(t)}}),(0,n.jsx)("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:b||"+".concat(null==P?void 0:P.countryCallingCode)})]}),(0,n.jsx)(o.I,{type:"tel",className:(0,c.ZP)("rounded-l-none pl-2",l),value:u,onChange:e=>{let{value:r}=e.target,t=f.S(r,_);null==i||i(""),t&&h.t(r,_)&&g.q(r,_)?(z(t.country),N("+".concat(t.countryCallingCode)),null==a||a(t.number),m(r)):(t?m(t.nationalNumber):m(r),null==a||a(r))},onPaste:e=>{let r=e.clipboardData.getData("Text"),t=f.S(r);if(t&&h.t(r))Z(t.formatNational()),z(t.country),N("+".concat(t.countryCallingCode)),null==a||a(t.number),null==i||i("");else{let e=f.S(r,_);e&&h.t(r,_)&&(Z(e.formatNational()),null==a||a(e.number),null==i||i(""))}},onBlur:()=>{if(u&&!y.y(u,_)){let e=v.d(u,_);e&&(null==i||i(C[e]))}},placeholder:null==P?void 0:P.formatNational(),disabled:s})]})}function q(e){let{initialCountry:r,country:t,onSelect:a,disabled:s}=e,[o,d]=(0,w.useState)(!1);return(0,n.jsxs)(l.J2,{open:o,onOpenChange:d,children:[(0,n.jsxs)(l.xo,{disabled:s,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[r||t?(0,n.jsx)(i.W,{countryCode:t||r,className:"aspect-auto h-[18px] w-7 flex-1"}):(0,n.jsx)(u.Z,{}),(0,n.jsx)(m.Z,{variant:"Bold",size:16})]}),(0,n.jsx)(l.yk,{align:"start",className:"h-fit p-0",children:(0,n.jsx)(z,{defaultValue:t||r,onSelect:e=>{a(e),d(!1)}})})]})}function z(e){var r;let{defaultValue:t,onSelect:o}=e,{countries:l,isLoading:c}=(0,d.F)(),{t:u}=(0,N.$G)();return(0,n.jsxs)(s.mY,{children:[(0,n.jsx)(s.sZ,{placeholder:u("Search country by name"),className:"placeholder:text-input-placeholder"}),(0,n.jsx)(s.e8,{children:(0,n.jsx)(s.fu,{children:c?(0,n.jsx)(s.di,{children:(0,n.jsx)(a.Loader,{})}):null===(r=l.filter(e=>{var r;let t=null===(r=e.code.cca2)||void 0===r?void 0:r.toUpperCase();return b.o().includes(t)}))||void 0===r?void 0:r.map(e=>(0,n.jsxs)(s.di,{value:e.name,"data-active":e.code.cca2===t,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>o(e),children:[(0,n.jsx)(i.W,{countryCode:e.code.cca2}),e.name]},e.code.ccn3))})})]})}},1098:function(e,r,t){t.d(r,{f:function(){return c}});var n=t(57437),a=t(92451),i=t(10407),s=t(40875);t(2265);var o=t(90827),l=t(62869),d=t(94508);function c(e){let{className:r,classNames:t,showOutsideDays:c=!0,...u}=e;return(0,n.jsx)(o._W,{showOutsideDays:c,className:(0,d.ZP)("p-3",r),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,d.ZP)((0,l.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.ZP)((0,l.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:e=>{let{...r}=e;return(0,n.jsx)(a.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...r}=e;return(0,n.jsx)(i.Z,{className:"h-4 w-4"})},Dropdown:e=>{let{...r}=e;return(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("select",{...r,style:{opacity:0,position:"absolute"}}),(0,n.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[(0,n.jsx)("span",{className:"text-sm",children:r.caption}),(0,n.jsx)(s.Z,{className:"size-3"})]})]})}},...u})}c.displayName="Calendar"},19060:function(e,r,t){t.d(r,{X:function(){return l}});var n=t(57437),a=t(9270),i=t(30401),s=t(2265),o=t(94508);let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.fC,{ref:r,className:(0,o.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...s,children:(0,n.jsx)(a.z$,{className:(0,o.ZP)("flex items-center justify-center text-current"),children:(0,n.jsx)(i.Z,{className:"h-4 w-4"})})})});l.displayName=a.fC.displayName},15681:function(e,r,t){t.d(r,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return x},xJ:function(){return p},zG:function(){return g}});var n=t(57437),a=t(37053),i=t(2265),s=t(29501),o=t(26815),l=t(94508);let d=s.RV,c=i.createContext({}),u=e=>{let{...r}=e;return(0,n.jsx)(c.Provider,{value:{name:r.name},children:(0,n.jsx)(s.Qr,{...r})})},m=()=>{let e=i.useContext(c),r=i.useContext(f),{getFieldState:t,formState:n}=(0,s.Gc)(),a=t(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...a}},f=i.createContext({}),p=i.forwardRef((e,r)=>{let{className:t,...a}=e,s=i.useId();return(0,n.jsx)(f.Provider,{value:{id:s},children:(0,n.jsx)("div",{ref:r,className:(0,l.ZP)("space-y-2",t),...a})})});p.displayName="FormItem";let x=i.forwardRef((e,r)=>{let{className:t,required:a,...i}=e,{error:s,formItemId:d}=m();return(0,n.jsx)("span",{children:(0,n.jsx)(o.Z,{ref:r,className:(0,l.ZP)(s&&"text-base font-medium text-destructive",t),htmlFor:d,...i})})});x.displayName="FormLabel";let h=i.forwardRef((e,r)=>{let{...t}=e,{error:i,formItemId:s,formDescriptionId:o,formMessageId:l}=m();return(0,n.jsx)(a.g7,{ref:r,id:s,"aria-describedby":i?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!i,...t})});h.displayName="FormControl",i.forwardRef((e,r)=>{let{className:t,...a}=e,{formDescriptionId:i}=m();return(0,n.jsx)("p",{ref:r,id:i,className:(0,l.ZP)("text-sm text-muted-foreground",t),...a})}).displayName="FormDescription";let g=i.forwardRef((e,r)=>{let{className:t,children:a,...i}=e,{error:s,formMessageId:o}=m(),d=s?String(null==s?void 0:s.message):a;return d?(0,n.jsx)("p",{ref:r,id:o,className:(0,l.ZP)("text-sm font-medium text-destructive",t),...i,children:d}):null});g.displayName="FormMessage"},57054:function(e,r,t){t.d(r,{J2:function(){return o},xo:function(){return l},yk:function(){return d}});var n=t(57437),a=t(2265),i=t(27312),s=t(94508);let o=i.fC,l=i.xz,d=a.forwardRef((e,r)=>{let{className:t,align:a="center",sideOffset:o=4,...l}=e;return(0,n.jsx)(i.h_,{children:(0,n.jsx)(i.VY,{ref:r,align:a,sideOffset:o,className:(0,s.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})})});d.displayName=i.VY.displayName},74991:function(e,r,t){t.d(r,{E:function(){return l},m:function(){return d}});var n=t(57437),a=t(2265),i=t(42325),s=t(40519),o=t(94508);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)(i.fC,{className:(0,o.ZP)("grid gap-2",t),...a,ref:r})});l.displayName=i.fC.displayName;let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)(i.ck,{ref:r,className:(0,o.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,n.jsx)(i.z$,{className:"flex items-center justify-center",children:(0,n.jsx)(s.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=i.ck.displayName},6512:function(e,r,t){var n=t(57437),a=t(55156),i=t(2265),s=t(94508);let o=i.forwardRef((e,r)=>{let{className:t,orientation:i="horizontal",decorative:o=!0,...l}=e;return(0,n.jsx)(a.f,{ref:r,decorative:o,orientation:i,className:(0,s.ZP)("shrink-0 bg-divider","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",t),...l})});o.displayName=a.f.displayName,r.Z=o},71792:function(e,r,t){t.d(r,{Zg:function(){return i},cN:function(){return a},yC:function(){return s},z1:function(){return o}});var n=t(31229);let a=n.z.object({firstName:n.z.string().min(1,"First name is required."),lastName:n.z.string().min(1,"Last name is required."),email:n.z.string().email({message:"Invalid email address."}),phone:n.z.string().min(1,"Phone number is required."),password:n.z.string({required_error:"Password is required"}).min(8,"Your password must be at least 8 characters long"),confirmPassword:n.z.string({required_error:"Confirm password is required"}).min(8,"Password is required."),referralCode:n.z.string().optional(),termAndCondition:n.z.literal(!0,{errorMap:()=>({message:"You must accept our terms & conditions"})})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),i=n.z.object({title:n.z.string().min(1,"Title is required."),dateOfBirth:n.z.date({required_error:"A date of birth is required."}),street:n.z.string().min(1,"Street is required."),country:n.z.string().min(1,"Country is required."),city:n.z.string().min(1,"City is required."),zipCode:n.z.string().min(1,"Zip code is required.")}),s=n.z.object({name:n.z.string({required_error:"Full name is required."}).min(1,"Full name is required."),email:n.z.string({required_error:"Email address is required."}).email({message:"Invalid email address."}),license:n.z.string().min(1,"Merchant license is required."),street:n.z.string({required_error:"Street is required"}).min(1,"Street is required."),country:n.z.string({required_error:"Country is required"}).min(1,"Country is required."),city:n.z.string({required_error:"City is required"}).min(1,"City is required."),zipCode:n.z.string({required_error:"Zip code is required"}).min(1,"Zip code is required.")}),o=n.z.object({name:n.z.string({required_error:"Full name is required."}).min(1,"Full name is required."),occupation:n.z.string({required_error:"Occupation is required."}).min(1,"Occupation is required."),whatsapp:n.z.string({required_error:"WhatsApp link is required."}).min(1,"WhatsApp link is required.")})}}]);