(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4786],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},53525:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>P,default:()=>A});var n,s={};r.r(s),r.d(s,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>f,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>g,pages:()=>p,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>x,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>h}),r(67206);var a=r(79319),i=r(20518),o=r(61902),l=r(62042),c=r(44630),d=r(44828),m=r(65505),u=r(13839);let h=["",{children:["(protected)",{admin:["children",{children:["exchanges",{children:["[exchangeId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80067)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\exchanges\\[exchangeId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71450)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\exchanges\\[exchangeId]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,27735)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\exchanges\\[exchangeId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,87620)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\exchanges\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],p=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\exchanges\\[exchangeId]\\page.tsx"],g="/(protected)/@admin/exchanges/[exchangeId]/page",f={require:r,loadChunk:()=>Promise.resolve()},x=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/exchanges/[exchangeId]/page",pathname:"/exchanges/[exchangeId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}});var k=r(69094),v=r(5787),y=r(90527);let E=e=>e?JSON.parse(e):void 0,b=self.__BUILD_MANIFEST,j=E(self.__REACT_LOADABLE_MANIFEST),S=null==(n=self.__RSC_MANIFEST)?void 0:n["/(protected)/@admin/exchanges/[exchangeId]/page"],w=E(self.__RSC_SERVER_MANIFEST),N=E(self.__NEXT_FONT_MANIFEST),M=E(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];S&&w&&(0,v.Mo)({clientReferenceManifest:S,serverActionsManifest:w,serverModuleMap:(0,y.w)({serverActionsManifest:w,pageName:"/(protected)/@admin/exchanges/[exchangeId]/page"})});let L=(0,i.d)({pagesType:k.s.APP,dev:!1,page:"/(protected)/@admin/exchanges/[exchangeId]/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:b,renderToHTML:l.f,reactLoadableManifest:j,clientReferenceManifest:S,serverActionsManifest:w,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:N,incrementalCacheHandler:null,interceptionRouteRewrites:M}),P=s;function A(e){return(0,a.C)({...e,IncrementalCache:o.k,handler:L})}},72726:(e,t,r)=>{Promise.resolve().then(r.bind(r,41071))},26807:(e,t,r)=>{Promise.resolve().then(r.bind(r,44450)),Promise.resolve().then(r.bind(r,40098))},35303:()=>{},41071:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>Q});var n=r(60926),s=r(58387),a=r(29411),i=r(15185),o=r(28871),l=r(36162),c=r(18662),d=r(74988),m=r(1181),u=r(25694);async function h(e){try{let t=await m.Z.put(`/admin/exchanges/accept/${e}`,{});return(0,u.B)(t)}catch(e){return(0,u.D)(e)}}async function p(e,t){try{let r=await m.Z.put(`/admin/exchanges/${e}`,{exchangeRate:t});return(0,u.B)(r)}catch(e){return(0,u.D)(e)}}async function g(e){try{let t=await m.Z.put(`/admin/exchanges/decline/${e}`,{});return(0,u.B)(t)}catch(e){return(0,u.D)(e)}}var f=r(43291),x=r(65091),k=r(3632),v=r(9172),y=r(37988),E=r(90543),b=r(61394),j=r(29220),S=r(31036),w=r.n(S),N=["variant","color","size"],M=function(e){var t=e.color;return j.createElement(j.Fragment,null,j.createElement("path",{d:"M21 22H3c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h18c.41 0 .75.34.75.75s-.34.75-.75.75ZM19.02 3.482c-1.94-1.94-3.84-1.99-5.83 0l-1.21 1.21c-.1.1-.14.26-.1.4a8.129 8.129 0 0 0 5.53 5.53.4.4 0 0 0 .41-.1l1.2-1.21c.99-.98 1.47-1.93 1.47-2.89.01-.99-.47-1.95-1.47-2.94ZM15.61 11.53c-.29-.14-.57-.28-.84-.44a8.8 8.8 0 0 1-.64-.42c-.17-.11-.37-.27-.56-.43a1.22 1.22 0 0 1-.17-.15c-.33-.28-.7-.64-1.03-1.04-.03-.02-.08-.09-.15-.18-.1-.12-.27-.32-.42-.55a5.49 5.49 0 0 1-.39-.59c-.16-.27-.3-.54-.44-.82a6.88 6.88 0 0 1-.061-.135c-.148-.333-.583-.43-.84-.173L4.34 12.331c-.13.13-.25.38-.28.55l-.54 3.83c-.1.68.09 1.32.51 1.75.36.35.86.54 1.4.54.12 0 .24-.01.36-.03l3.84-.54c.18-.03.43-.15.55-.28l5.722-5.721c.26-.26.161-.705-.176-.85a26.852 26.852 0 0 1-.116-.05Z",fill:t}))},L=function(e){var t=e.color;return j.createElement(j.Fragment,null,j.createElement("path",{d:"m17.37 10.171 1.34-1.42c1.42-1.5 2.06-3.21-.15-5.3-2.21-2.08-3.88-1.35-5.3.15l-8.21 8.69c-.31.33-.61.98-.67 1.43l-.37 3.24c-.13 1.17.71 1.97 1.87 1.77l3.22-.55c.45-.08 1.08-.41 1.39-.75l3.95-4.18",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),j.createElement("path",{d:"M11.89 5.05a6.126 6.126 0 0 0 5.45 5.15M3 22h11M18 22h3",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},P=function(e){var t=e.color;return j.createElement(j.Fragment,null,j.createElement("path",{opacity:".4",d:"M21 22H3c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h18c.41 0 .75.34.75.75s-.34.75-.75.75ZM19.02 3.482c-1.94-1.94-3.84-1.99-5.83 0l-1.21 1.21c-.1.1-.14.26-.1.4a8.129 8.129 0 0 0 5.53 5.53.4.4 0 0 0 .41-.1l1.2-1.21c.99-.98 1.47-1.93 1.47-2.89.01-.99-.47-1.95-1.47-2.94Z",fill:t}),j.createElement("path",{d:"M15.61 11.53c-.29-.14-.57-.28-.84-.44a8.8 8.8 0 0 1-.64-.42c-.17-.11-.37-.27-.56-.43a1.22 1.22 0 0 1-.17-.15c-.33-.28-.7-.64-1.03-1.04-.03-.02-.08-.09-.15-.18-.1-.12-.27-.32-.42-.55a5.49 5.49 0 0 1-.39-.59c-.16-.27-.3-.54-.44-.82-.14-.3-.25-.59-.35-.86l-6.28 6.28c-.13.13-.25.38-.28.55l-.54 3.83c-.1.68.09 1.32.51 1.75.36.35.86.54 1.4.54.12 0 .24-.01.36-.03l3.84-.54c.18-.03.43-.15.55-.28l6.28-6.28c-.28-.1-.55-.21-.85-.34Z",fill:t}))},A=function(e){var t=e.color;return j.createElement(j.Fragment,null,j.createElement("path",{d:"m13.26 3.6-8.21 8.69c-.31.33-.61.98-.67 1.43l-.37 3.24c-.13 1.17.71 1.97 1.87 1.77l3.22-.55c.45-.08 1.08-.41 1.39-.75l8.21-8.69c1.42-1.5 2.06-3.21-.15-5.3-2.2-2.07-3.87-1.34-5.29.16Z",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),j.createElement("path",{d:"M11.89 5.05a6.126 6.126 0 0 0 5.45 5.15M3 22h18",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},D=function(e){var t=e.color;return j.createElement(j.Fragment,null,j.createElement("path",{d:"M5.54 19.52c-.61 0-1.18-.21-1.59-.6-.52-.49-.77-1.23-.68-2.03l.37-3.24c.07-.61.44-1.42.87-1.86l8.21-8.69c2.05-2.17 4.19-2.23 6.36-.18s2.23 4.19.18 6.36l-8.21 8.69c-.42.45-1.2.87-1.81.97l-3.22.55c-.17.01-.32.03-.48.03ZM15.93 2.91c-.77 0-1.44.48-2.12 1.2l-8.21 8.7c-.2.21-.43.71-.47 1l-.37 3.24c-.04.33.04.6.22.77.18.17.45.23.78.18l3.22-.55c.29-.05.77-.31.97-.52l8.21-8.69C19.4 6.92 19.85 5.7 18.04 4c-.8-.77-1.49-1.09-2.11-1.09Z",fill:t}),j.createElement("path",{d:"M17.34 10.949h-.07a6.86 6.86 0 0 1-6.11-5.78c-.06-.41.22-.79.63-.86.41-.06.79.22.86.63a5.372 5.372 0 0 0 4.78 4.52c.41.04.71.41.67.82-.05.38-.38.67-.76.67ZM21 22.75H3c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h18c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},C=function(e){var t=e.color;return j.createElement(j.Fragment,null,j.createElement("path",{d:"m13.26 3.6-8.21 8.69c-.31.33-.61.98-.67 1.43l-.37 3.24c-.13 1.17.71 1.97 1.87 1.77l3.22-.55c.45-.08 1.08-.41 1.39-.75l8.21-8.69c1.42-1.5 2.06-3.21-.15-5.3-2.2-2.07-3.87-1.34-5.29.16Z",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),j.createElement("path",{opacity:".4",d:"M11.89 5.05a6.126 6.126 0 0 0 5.45 5.15M3 22h18",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},Z=function(e,t){switch(e){case"Bold":return j.createElement(M,{color:t});case"Broken":return j.createElement(L,{color:t});case"Bulk":return j.createElement(P,{color:t});case"Linear":default:return j.createElement(A,{color:t});case"Outline":return j.createElement(D,{color:t});case"TwoTone":return j.createElement(C,{color:t})}},I=(0,j.forwardRef)(function(e,t){var r=e.variant,n=e.color,s=e.size,a=(0,b._)(e,N);return j.createElement("svg",(0,b.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),Z(r,n))});I.propTypes={variant:w().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:w().string,size:w().oneOfType([w().string,w().number])},I.defaultProps={variant:"Linear",color:"currentColor",size:"24"},I.displayName="Edit2";var T=r(51018),_=r(4432),z=r(55506),F=r(64947),B=r(39228),R=r(32167);let O=new x.F;function Q(){let e=(0,F.UO)(),[t,r]=j.useState(!1),{data:m,isLoading:u,mutate:b}=(0,f.d)(`/exchanges/${e.exchangeId}`),[S,w]=(0,j.useState)(""),N=j.useRef(null),{t:M}=(0,B.$G)(),L=(0,j.useMemo)(()=>m?.data?new k.C(m?.data):null,[m?.data]),P=()=>{r(!0),N.current?.focus();let e=document.createRange();e.selectNodeContents(N.current);let t=window.getSelection();t?.removeAllRanges(),t?.addRange(e)},A=t=>{if(t){if(r(!1),Number.isNaN(t)){R.toast.error(M("Please enter a numeric value.")),P();return}L?.metaData?.exchangeRate?.toString()!==t?.toString()&&R.toast.promise(p(e.exchangeId,Number(t)),{loading:"Processing...",success:e=>{if(!e?.status)throw Error(e.message);return b(m),M(e.message)},error:e=>(P(),M(e.message))})}};return u?(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(a.Loader,{})}):L?(0,n.jsx)("div",{className:"p-4",children:(0,n.jsxs)("div",{className:"flex w-full max-w-[616px] flex-col gap-4 rounded-xl bg-card px-2 py-14",children:[(0,n.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,n.jsx)(E.Z,{variant:"Bulk",size:32,className:"text-success"}),(0,n.jsxs)("h2",{className:"font-semibold",children:[M("Exchange")," #",L?.id]})]}),(0,n.jsxs)("div",{className:"flex flex-col items-center rounded-full",children:[(0,n.jsxs)(i.qE,{className:"mb-1",children:[(0,n.jsx)(i.F$,{src:L?.user?.customer?.avatar,alt:L?.user?.customer?.name}),(0,n.jsx)(i.Q5,{className:"font-bold",children:(0,v.v)(L?.user?.customer?.name)})]}),(0,n.jsx)("h5",{className:"text-sm font-medium sm:text-base",children:L?.user?.customer?.name}),(0,n.jsx)("p",{className:"text-xs text-secondary-text sm:text-sm",children:L?.user?.email}),(0,n.jsx)("p",{className:"text-xs text-secondary-text sm:text-sm",children:L?.user?.customer?.phone})]}),(0,n.jsx)(d.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,n.jsx)("div",{className:"flex flex-col",children:(0,n.jsx)("table",{className:"w-full table-auto border-collapse",children:(0,n.jsxs)("tbody",{children:[(0,n.jsxs)("tr",{className:"odd:bg-accent",children:[(0,n.jsx)("td",{className:"col-span-6 w-48 px-6 py-3 text-sm font-normal sm:text-base",children:M("Exchange from")}),(0,n.jsx)("td",{className:"pl-2.5 text-sm font-medium sm:text-base",children:O.formatVC(L.amount,L.metaData?.currencyFrom)})]}),(0,n.jsxs)("tr",{className:"odd:bg-accent",children:[(0,n.jsx)("td",{className:"col-span-6 w-48 px-6 py-3 text-sm font-normal sm:text-base",children:M("Exchanged to")}),(0,n.jsx)("td",{className:"pl-2.5 text-sm font-medium sm:text-base",children:O.formatVC(L.total,L.metaData?.currencyTo)})]}),(0,n.jsxs)("tr",{className:"odd:bg-accent",children:[(0,n.jsx)("td",{className:"col-span-6 w-48 px-6 py-3 text-sm font-normal sm:text-base",children:M("Exchange rate")}),(0,n.jsx)("td",{className:"flex items-center text-sm font-medium sm:text-base",children:t?(0,n.jsxs)("form",{onSubmit:()=>A(S),className:"flex items-center",children:[(0,n.jsx)(c.I,{value:S,autoFocus:!0,onChange:e=>w(e.target.value),"data-mode":t?"edit":"",className:"h-fit w-52 rounded-md border border-transparent px-1.5 py-1 outline-none data-[mode=edit]:focus:border-foreground/50"}),"USD",(0,n.jsxs)("div",{className:"ml-1.5 flex items-center gap-2",children:[(0,n.jsx)(l.z,{variant:"ghost",size:"icon",className:"w-fit hover:text-green-500",onClick:()=>{A(N?.current?.textContent)},children:(0,n.jsx)(_.Z,{size:14})}),(0,n.jsx)(l.z,{variant:"ghost",size:"icon",type:"button",className:"w-fit hover:text-red-500",onClick:()=>{r(!1),w(L?.metaData?.exchangeRate)},children:(0,n.jsx)(z.Z,{size:14})})]})]}):(0,n.jsxs)("div",{className:"flex items-center gap-1.5 pl-2.5",children:[S,(0,n.jsx)(l.z,{variant:"ghost",size:"icon",className:"w-8 hover:text-primary",onClick:()=>r(!0),children:(0,n.jsx)(I,{size:14})})]})})]}),(0,n.jsxs)("tr",{className:"odd:bg-accent",children:[(0,n.jsx)("td",{className:"col-span-6 w-48 px-6 py-3 text-sm font-normal sm:text-base",children:M("Fee")}),(0,n.jsx)("td",{className:"pl-2.5 text-sm font-medium sm:text-base",children:O.formatVC(L.fee,L.metaData?.currencyFrom)})]}),(0,n.jsxs)("tr",{className:"odd:bg-accent",children:[(0,n.jsx)("td",{className:"col-span-6 w-48 px-6 py-3 text-sm font-normal sm:text-base",children:M("User gets")}),(0,n.jsx)("td",{className:"pl-2.5 text-sm font-semibold sm:text-base",children:O.formatVC(L.total,L.metaData?.currencyTo)})]}),(0,n.jsxs)("tr",{className:"odd:bg-accent",children:[(0,n.jsx)("td",{className:"col-span-6 w-48 px-6 py-3 text-sm font-normal sm:text-base",children:M("Status")}),(0,n.jsxs)("td",{className:"pl-2.5 text-sm font-semibold sm:text-base",children:[(0,n.jsx)(s.J,{condition:L?.status==="complete",children:(0,n.jsx)(o.C,{variant:"success",children:(0,x.fl)(L?.status)})}),(0,n.jsx)(s.J,{condition:L?.status==="pending",children:(0,n.jsx)(o.C,{variant:"secondary",children:(0,x.fl)(L?.status)})}),(0,n.jsx)(s.J,{condition:L?.status==="failed",children:(0,n.jsx)(o.C,{variant:"destructive",children:(0,x.fl)(L?.status)})})]})]})]})})}),(0,n.jsx)(d.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,n.jsx)(s.J,{condition:L?.status==="pending",children:(0,n.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,n.jsxs)(l.z,{type:"button",onClick:()=>{R.toast.promise(h(L?.id),{loading:M("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return b(m),M(e.message)},error:e=>M(e.message)})},className:"gap-1 rounded-lg bg-spacial-green px-4 py-2 font-medium text-background hover:bg-[#219621] hover:text-background",children:[(0,n.jsx)(E.Z,{}),M("Approve")]}),(0,n.jsxs)(l.z,{type:"button",onClick:()=>{R.toast.promise(g(L?.id),{loading:M("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return b(m),M(e.message)},error:e=>M(e.message)})},className:"gap-1 rounded-lg bg-[#D13438] px-4 py-2 font-medium text-white hover:bg-[#a5272b] hover:text-white",children:[(0,n.jsx)(T.Z,{}),M("Reject")]})]})})]})}):(0,n.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,n.jsx)(y.Z,{}),M("No data found")]})}},40098:(e,t,r)=>{"use strict";r.d(t,{default:()=>D});var n=r(60926),s=r(58387),a=r(36162),i=r(84607),o=r(86059),l=r(737),c=r(64947),d=r(29220);function m({sidebarItem:e}){let[t,r]=d.useState("(dashboard)"),[m,u]=d.useState(!1),{setIsExpanded:h,device:p}=(0,i.q)(),g=(0,c.BT)();return d.useEffect(()=>{r(g)},[]),d.useEffect(()=>{u(e.segment===g)},[g,e.segment]),(0,n.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,n.jsxs)(l.Z,{href:e.link,onClick:()=>{r(e.segment),e.children?.length||"Desktop"===p||h(!1)},"data-active":g===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[(0,n.jsx)(s.J,{condition:!!e.icon,children:(0,n.jsx)("div",{"data-active":g===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),(0,n.jsx)("span",{className:"flex-1",children:e.name}),(0,n.jsx)(s.J,{condition:!!e.children?.length,children:(0,n.jsx)(a.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:(0,n.jsx)(o.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),(0,n.jsx)(s.J,{condition:!!e.children?.length,children:(0,n.jsx)("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>n.jsx("li",{children:n.jsxs(l.Z,{href:e.link,"data-active":t===e.segment,onClick:()=>{r(e.segment),"Desktop"!==p&&h(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[n.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=r(74988),h=r(840),p=r(65091),g=r(51496),f=r(32917),x=r(65694),k=r(34870),v=r(48132),y=r(55929),E=r(41529),b=r(95334),j=r(5147),S=r(76409),w=r(24112),N=r(69628),M=r(73634),L=r(47020),P=r(28277),A=r(39228);function D(){let{t:e}=(0,A.$G)(),{isExpanded:t,setIsExpanded:r}=(0,i.q)(),{logo:s,siteName:o}=(0,h.T)(),c=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:(0,n.jsx)(g.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:(0,n.jsx)(f.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:(0,n.jsx)(x.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:(0,n.jsx)(k.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:(0,n.jsx)(v.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:(0,n.jsx)(y.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:(0,n.jsx)(E.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:(0,n.jsx)(b.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:(0,n.jsx)(j.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:(0,n.jsx)(S.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:(0,n.jsx)(w.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:(0,n.jsx)(N.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:(0,n.jsx)(M.Z,{size:"20"}),link:"/settings"}]}];return(0,n.jsxs)("div",{"data-expanded":t,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[(0,n.jsx)(a.z,{size:"icon",variant:"outline",onClick:()=>r(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${t?"":"hidden"} lg:hidden`,children:(0,n.jsx)(L.Z,{})}),(0,n.jsx)("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:(0,n.jsx)(l.Z,{href:"/",className:"flex items-center justify-center",children:(0,n.jsx)(P.Z,{src:(0,p.qR)(s),width:160,height:40,alt:o,className:"max-h-10 object-contain"})})}),(0,n.jsx)("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:c.map(e=>(0,n.jsxs)("div",{children:[""!==e.title?(0,n.jsx)("div",{children:(0,n.jsx)(u.Z,{className:"my-4"})}):null,(0,n.jsx)("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>n.jsx("li",{children:n.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},28871:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(60926),s=r(8206);r(29220);var a=r(65091);let i=(0,s.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,a.ZP)(i({variant:t}),e),...r})}},18662:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=r(60926),s=r(29220),a=r(65091);let i=s.forwardRef(({className:e,type:t,...r},s)=>(0,n.jsx)("input",{type:t,className:(0,a.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...r}));i.displayName="Input"},51018:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm3.36 12.3c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-2.3-2.3-2.3 2.3c-.15.15-.34.22-.53.22s-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l2.3-2.3-2.3-2.3a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 2.3-2.3c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-2.3 2.3 2.3 2.3Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"m13.99 10.012.84-.84M9.17 14.828l2.75-2.75M14.83 14.832l-5.66-5.66M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),s.createElement("path",{d:"m13.06 12 2.3-2.3c.29-.29.29-.77 0-1.06a.754.754 0 0 0-1.06 0l-2.3 2.3-2.3-2.3a.754.754 0 0 0-1.06 0c-.29.29-.29.77 0 1.06l2.3 2.3-2.3 2.3c-.29.29-.29.77 0 1.06.15.15.34.22.53.22s.38-.07.53-.22l2.3-2.3 2.3 2.3c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77 0-1.06l-2.3-2.3Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10ZM9.17 14.83l5.66-5.66M14.83 14.83 9.17 9.17",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),s.createElement("path",{d:"M9.17 15.58c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l5.66-5.66c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.7 15.36c-.14.15-.34.22-.53.22Z",fill:t}),s.createElement("path",{d:"M14.83 15.58c-.19 0-.38-.07-.53-.22L8.64 9.7a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l5.66 5.66c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},h=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("g",{opacity:".4",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},s.createElement("path",{d:"m9.17 14.832 5.66-5.66M14.83 14.832l-5.66-5.66"})))},p=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(h,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(r,a))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="CloseCircle"},37988:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"m19.53 5.53-14 14c-.02.02-.03.03-.05.04-.38-.32-.73-.67-1.05-1.05A9.903 9.903 0 0 1 2 12C2 6.48 6.48 2 12 2c2.49 0 4.77.91 6.52 2.43.38.32.73.67 1.05 1.05-.01.02-.02.03-.04.05ZM22 12c0 5.49-4.51 10-10 10-1.5 0-2.92-.33-4.2-.93-.62-.29-.74-1.12-.26-1.61L19.46 7.54c.48-.48 1.32-.36 1.61.26.6 1.27.93 2.7.93 4.2Z",fill:t}),s.createElement("path",{d:"M21.77 2.229c-.3-.3-.79-.3-1.09 0L2.23 20.689c-.3.3-.3.79 0 1.09a.758.758 0 0 0 1.08-.01l18.46-18.46c.31-.3.31-.78 0-1.08Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"m18.9 5-14 14",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"m19.53 5.53-14 14c-.02.02-.03.03-.05.04-.38-.32-.73-.67-1.05-1.05A9.903 9.903 0 0 1 2 12C2 6.48 6.48 2 12 2c2.49 0 4.77.91 6.52 2.43.38.32.73.67 1.05 1.05-.01.02-.02.03-.04.05ZM22 12.002c0 5.52-4.48 10-10 10-1.99 0-3.84-.58-5.4-1.6l13.8-13.8a9.815 9.815 0 0 1 1.6 5.4Z",fill:t}),s.createElement("path",{d:"M21.77 2.229c-.3-.3-.79-.3-1.09 0L2.23 20.689c-.3.3-.3.79 0 1.09a.758.758 0 0 0 1.08-.01l18.46-18.46c.31-.3.31-.78 0-1.08Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.52 0 10-4.48 10-10S17.52 2 12 2 2 6.48 2 12s4.48 10 10 10ZM18.9 5l-14 14",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),s.createElement("path",{d:"M4.9 19.751c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l14-14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-14 14c-.15.15-.34.22-.53.22Z",fill:t}))},h=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.52 0 10-4.48 10-10S17.52 2 12 2 2 6.48 2 12s4.48 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".34",d:"m18.9 5-14 14",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(h,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(r,a))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="Slash"},90543:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm4.78 7.7-5.67 5.67a.75.75 0 0 1-1.06 0l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.76 0 1.06Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85M15 10.38l1.12-1.13",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"m7.88 12 2.74 2.75 2.55-2.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),s.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"m7.75 12 2.83 2.83 5.67-5.66",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),s.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:t}))},h=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".34",d:"m7.75 12.002 2.83 2.83 5.67-5.66",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return s.createElement(l,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(h,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,l=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(r,a))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="TickCircle"},3632:(e,t,r)=>{"use strict";r.d(t,{C:()=>c});var n=r(73244),s=r(73146),a=r(65091);class i{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,a.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new s.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new n.k(e?.address):null}}var o=r(14455),l=r(74190);class c{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new i(e?.user),customer:e?.user?.customer?new l.O(e?.user?.customer):null,merchant:e?.user?.merchant?new l.O(e?.user?.merchant):null,agent:e?.user?.agent?new l.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,o.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,o.WU)(this.updatedAt,e):"N/A"}}},71450:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,runtime:()=>n});let n="edge";function s({children:e}){return e}},27735:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}},80067:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\exchanges\[exchangeId]\page.tsx#default`)},87620:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}},73391:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(42416),s=r(33908);let a=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function i({children:e}){return(0,n.jsxs)("div",{className:"flex h-screen",children:[(0,n.jsx)(a,{}),(0,n.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[(0,n.jsx)(s.Z,{}),(0,n.jsx)("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(87908)},50517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,7283,5089],()=>t(53525));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/exchanges/[exchangeId]/page"]=r}]);
//# sourceMappingURL=page.js.map