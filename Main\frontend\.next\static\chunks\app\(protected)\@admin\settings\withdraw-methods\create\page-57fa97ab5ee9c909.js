(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[82853],{16246:function(e,n,t){Promise.resolve().then(t.bind(t,81927))},81927:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return C}});var r=t(57437),a=t(81330),o=t(85487),d=t(62869),i=t(15681),u=t(79981),s=t(97751);async function l(e){try{var n,t,r,a,o,d,i,l;let c=new FormData;c.append("name",e.name),c.append("countryCode",e.countryCode),c.append("currencyCode",e.currencyCode),c.append("active",e.active.toString()),c.append("recommended",e.recommended.toString()),c.append("minAmount",null!==(o=null===(n=e.minAmount)||void 0===n?void 0:n.toString())&&void 0!==o?o:""),c.append("maxAmount",null!==(d=null===(t=e.maxAmount)||void 0===t?void 0:t.toString())&&void 0!==d?d:""),c.append("fixedCharge",null!==(i=null===(r=e.fixedCharge)||void 0===r?void 0:r.toString())&&void 0!==i?i:""),c.append("percentageCharge",null!==(l=null===(a=e.percentageCharge)||void 0===a?void 0:a.toString())&&void 0!==l?l:""),c.append("inputParams",JSON.stringify(e.params)),e.uploadLogo&&c.append("uploadLogo",e.uploadLogo);let m=await u.Z.post("/admin/methods",c,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.B)(m)}catch(e){return(0,s.D)(e)}}var c=t(30412),m=t(13590),p=t(22291),g=t(99376),h=t(2265),f=t(29501),v=t(43949),x=t(14438);function C(){let[e,n]=(0,h.useTransition)(),{t}=(0,v.$G)(),u=(0,g.useRouter)(),s=(0,f.cI)({resolver:(0,m.F)(c.L),defaultValues:{uploadLogo:"",name:"",countryCode:"",currencyCode:"",active:!0,recommended:!1,minAmount:"0",maxAmount:"0",fixedCharge:"0",percentageCharge:"0",params:[{name:"",label:"",type:"text",required:!1}]}});return(0,r.jsx)("div",{className:"mb-4 rounded-xl border border-border bg-background p-4",children:(0,r.jsx)(i.l0,{...s,children:(0,r.jsxs)("form",{onSubmit:s.handleSubmit(e=>{n(async()=>{let n=await l(e);(null==n?void 0:n.status)?(x.toast.success(t("Withdraw method created successfully")),u.push("/settings/withdraw-methods")):x.toast.error(t(null==n?void 0:n.message))})}),className:"flex flex-col gap-6 px-1",children:[(0,r.jsx)(a.Z,{form:s}),(0,r.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,r.jsx)(d.z,{children:e?(0,r.jsx)(o.Loader,{title:t("Creating..."),className:"text-primary-foreground"}):(0,r.jsxs)(r.Fragment,{children:[t("Create method"),(0,r.jsx)(p.Z,{size:20})]})})})]})})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,38658,42592,58939,85598,58410,227,4471,92971,95030,1744],function(){return e(e.s=16246)}),_N_E=e.O()}]);