"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[15269],{85633:function(e,t,n){n.r(t),n.d(t,{default:function(){return ef}});var r=n(57437),i=n(41709),a=n(85487),l=n(27300),s=n(31026),o=n(70880),c=n(16831),d=n(15681),u=n(6512),m=n(79981),f=n(97751);async function v(e){try{let t=await m.Z.post("/transfers/create",e);return(0,f.B)(t)}catch(e){return(0,f.D)(e)}}var x=n(11564),h=n(3612),p=n(94508),g=n(59532),j=n(13590),y=n(66574),N=n(99376),b=n(2265),w=n(29501),k=n(43949),E=n(14438),C=n(31229),Z=n(45932),L=n(62869),M=n(95186),T=n(22291);function S(e){let{form:t,isCheckingUser:n,onNext:l}=e,{t:s}=(0,k.$G)();return(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h2",{children:s("Add recipient")}),(0,r.jsx)(d.Wi,{name:"email",control:t.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(d.xJ,{className:"w-full",children:[(0,r.jsx)(d.NI,{children:(0,r.jsx)(M.I,{type:"text",placeholder:s("Enter recipient’s email"),...t})}),(0,r.jsx)(d.zG,{})]})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h2",{children:s("Select wallet")}),(0,r.jsx)(d.Wi,{name:"currencyCode",control:t.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(d.xJ,{children:[(0,r.jsx)(d.NI,{children:(0,r.jsx)(Z.R,{...t})}),(0,r.jsx)(d.zG,{})]})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h2",{children:s("Add amount")}),(0,r.jsx)(d.Wi,{name:"amount",control:t.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(d.xJ,{children:[(0,r.jsx)(d.NI,{children:(0,r.jsx)(M.I,{type:"number",min:0,placeholder:s("Enter transfer amount"),...t})}),(0,r.jsx)(d.zG,{})]})}})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(L.z,{type:"submit",onClick:l,className:"min-w-48",disabled:n,children:[(0,r.jsx)(i.J,{condition:n,children:(0,r.jsx)(a.Loader,{title:s("Checking..."),className:"text-primary-foreground"})}),(0,r.jsxs)(i.J,{condition:!n,children:[(0,r.jsx)("span",{children:s("Next")}),(0,r.jsx)(T.Z,{size:16})]})]})})]})}var A=n(37781),z=n(25318),I=n(3697),F=n(87806),R=n(35974),_=n(84190),P=n(64863),O=n(70569),U=n(48358),B=n(19571),D=n(83504),W=n(43271),q=n(74677),V=n(40718),$=n.n(V),G=["variant","color","size"],J=function(e){var t=e.color;return b.createElement(b.Fragment,null,b.createElement("path",{d:"M21.97 2.33A3.944 3.944 0 0 0 19 1a3.995 3.995 0 0 0-4 4c0 .75.21 1.46.58 2.06.2.34.46.65.76.91C17.04 8.61 17.97 9 19 9c.44 0 .86-.07 1.25-.21.92-.29 1.69-.92 2.17-1.73.21-.34.37-.73.46-1.13.08-.3.12-.61.12-.93 0-1.02-.39-1.96-1.03-2.67Zm-1.48 3.4h-.74v.78c0 .41-.34.75-.75.75s-.75-.34-.75-.75v-.78h-.74c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h.74v-.71c0-.41.34-.75.75-.75s.75.34.75.75v.71h.74a.749.749 0 1 1 0 1.5Z",fill:t}),b.createElement("path",{d:"M22 12c0-1.31-.25-2.57-.72-3.72-.31.22-.66.39-1.03.51-.11.04-.22.07-.34.1a8.48 8.48 0 0 1-1.87 9.08c-.29-.37-.66-.71-1.1-1-2.71-1.82-7.15-1.82-9.88 0-.44.29-.8.63-1.1 1A8.48 8.48 0 0 1 3.5 12c0-4.69 3.81-8.5 8.5-8.5 1.09 0 2.14.21 3.1.59.03-.12.06-.23.1-.35.12-.37.29-.71.52-1.02A9.82 9.82 0 0 0 12 2C6.49 2 2 6.49 2 12c0 2.9 1.25 5.51 3.23 7.34 0 .01 0 .01-.01.02.1.1.22.18.32.27.06.05.11.1.17.14.18.15.38.29.57.43l.2.14c.19.13.39.25.6.36.07.04.15.09.22.13.2.11.41.21.63.3.08.04.16.08.24.11.22.09.44.17.66.24.08.03.16.06.24.08.24.07.48.13.72.19.07.02.14.04.22.05.28.06.56.1.85.13.04 0 .08.01.12.02.34.03.68.05 1.02.05.34 0 .68-.02 1.01-.05.04 0 .08-.01.12-.02.29-.03.57-.07.85-.13.07-.01.14-.04.22-.05.24-.06.49-.11.72-.19.08-.03.16-.06.24-.08.22-.08.45-.15.66-.24.08-.03.16-.07.24-.11.21-.09.42-.19.63-.3.08-.04.15-.09.22-.13.2-.12.4-.23.6-.36.07-.04.13-.09.2-.14.2-.14.39-.28.57-.43.06-.05.11-.1.17-.14.11-.09.22-.18.32-.27 0-.01 0-.01-.01-.02C20.75 17.51 22 14.9 22 12Z",fill:t}),b.createElement("path",{d:"M12 6.93c-2.07 0-3.75 1.68-3.75 3.75 0 2.03 1.59 3.68 3.7 3.74h.18a3.743 3.743 0 0 0 3.62-3.74c0-2.07-1.68-3.75-3.75-3.75Z",fill:t}))},H=function(e){var t=e.color;return b.createElement(b.Fragment,null,b.createElement("path",{d:"M11.458 13.73a2.81 2.81 0 1 0 0-5.62 2.81 2.81 0 0 0 0 5.62ZM16.65 20.199c0-2.33-2.32-4.23-5.19-4.23-2.87 0-5.19 1.89-5.19 4.23",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),b.createElement("path",{d:"M11.5 3c1.31 0 2.56.26 3.7.74-.13.4-.2.82-.2 1.26 0 .75.21 1.46.58 2.06.2.34.46.65.76.91C17.04 8.61 17.97 9 19 9c.44 0 .86-.07 1.25-.21.48 1.14.75 2.4.75 3.71 0 5.25-4.25 9.5-9.5 9.5S2 17.75 2 12.5c0-3.84 2.28-7.15 5.56-8.65",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),b.createElement("path",{d:"M23 5c0 .32-.04.63-.12.93-.09.4-.25.79-.46 1.13-.48.81-1.25 1.44-2.17 1.73-.39.14-.81.21-1.25.21a3.92 3.92 0 0 1-2.66-1.03c-.3-.26-.56-.57-.76-.91A3.92 3.92 0 0 1 15 5c0-.44.07-.86.2-1.26A3.995 3.995 0 0 1 19 1c1.18 0 2.25.51 2.97 1.33C22.61 3.04 23 3.98 23 5ZM20.492 4.98h-2.98M19 3.52v2.99",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},Q=function(e){var t=e.color;return b.createElement(b.Fragment,null,b.createElement("path",{d:"M16.65 20.199v.29a9.513 9.513 0 0 1-10.38-.06v-.23c0-2.33 2.33-4.23 5.19-4.23 2.87 0 5.19 1.9 5.19 4.23Z",fill:t}),b.createElement("path",{opacity:".4",d:"M21 12.5c0 3.35-1.73 6.29-4.35 7.99v-.29c0-2.33-2.32-4.23-5.19-4.23-2.86 0-5.19 1.9-5.19 4.23v.23A9.487 9.487 0 0 1 2 12.5C2 7.25 6.25 3 11.5 3c1.31 0 2.56.26 3.7.74-.13.4-.2.82-.2 1.26 0 .75.21 1.46.58 2.06.2.34.46.65.76.91C17.04 8.61 17.97 9 19 9c.44 0 .86-.07 1.25-.21.48 1.14.75 2.4.75 3.71Z",fill:t}),b.createElement("path",{d:"M21.97 2.33A3.944 3.944 0 0 0 19 1a3.995 3.995 0 0 0-4 4c0 .75.21 1.46.58 2.06.2.34.46.65.76.91C17.04 8.61 17.97 9 19 9c.44 0 .86-.07 1.25-.21.92-.29 1.69-.92 2.17-1.73.21-.34.37-.73.46-1.13.08-.3.12-.61.12-.93 0-1.02-.39-1.96-1.03-2.67Zm-1.48 3.4h-.74v.78c0 .41-.34.75-.75.75s-.75-.34-.75-.75v-.78h-.74c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h.74v-.71c0-.41.34-.75.75-.75s.75.34.75.75v.71h.74a.749.749 0 1 1 0 1.5ZM11.458 14.73a2.81 2.81 0 1 0 0-5.62 2.81 2.81 0 0 0 0 5.62Z",fill:t}))},X=function(e){var t=e.color;return b.createElement(b.Fragment,null,b.createElement("path",{d:"M11.46 13.73a2.81 2.81 0 1 0 0-5.62 2.81 2.81 0 0 0 0 5.62ZM16.65 20.2c0-2.33-2.32-4.23-5.19-4.23-2.87 0-5.19 1.89-5.19 4.23",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),b.createElement("path",{d:"M21 12.5c0 5.25-4.25 9.5-9.5 9.5S2 17.75 2 12.5 6.25 3 11.5 3c1.31 0 2.56.26 3.7.74-.13.4-.2.82-.2 1.26 0 .75.21 1.46.58 2.06.2.34.46.65.76.91C17.04 8.61 17.97 9 19 9c.44 0 .86-.07 1.25-.21.48 1.14.75 2.4.75 3.71Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),b.createElement("path",{d:"M23 5c0 .32-.04.63-.12.93-.09.4-.25.79-.46 1.13-.48.81-1.25 1.44-2.17 1.73-.39.14-.81.21-1.25.21a3.92 3.92 0 0 1-2.66-1.03c-.3-.26-.56-.57-.76-.91A3.92 3.92 0 0 1 15 5c0-.44.07-.86.2-1.26A3.995 3.995 0 0 1 19 1c1.18 0 2.25.51 2.97 1.33C22.61 3.04 23 3.98 23 5ZM20.49 4.98h-2.98M19 3.52v2.99",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},Y=function(e){var t=e.color;return b.createElement(b.Fragment,null,b.createElement("path",{d:"M11.458 14.48c-1.96 0-3.56-1.6-3.56-3.56s1.6-3.56 3.56-3.56 3.56 1.6 3.56 3.56-1.6 3.56-3.56 3.56Zm0-5.61c-1.13 0-2.06.92-2.06 2.06 0 1.14.92 2.06 2.06 2.06 1.14 0 2.06-.92 2.06-2.06 0-1.14-.92-2.06-2.06-2.06ZM16.65 20.949c-.41 0-.75-.34-.75-.75 0-1.92-1.99-3.48-4.44-3.48s-4.44 1.56-4.44 3.48c0 .41-.34.75-.75.75s-.75-.34-.75-.75c0-2.74 2.66-4.98 5.94-4.98 3.28 0 5.94 2.23 5.94 4.98 0 .41-.34.75-.75.75Z",fill:t}),b.createElement("path",{d:"M11.5 22.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 2.25 11.5 2.25c1.39 0 2.73.27 3.99.8.36.15.54.55.42.92-.11.33-.16.68-.16 1.03 0 .59.16 1.17.47 1.67.16.28.37.53.61.74.87.79 2.16 1.04 3.17.68.37-.14.79.05.94.42.54 1.27.81 2.62.81 4 0 5.64-4.6 10.24-10.25 10.24Zm0-19c-4.82 0-8.75 3.92-8.75 8.75s3.93 8.75 8.75 8.75 8.75-3.92 8.75-8.75c0-.96-.16-1.91-.46-2.82a4.75 4.75 0 0 1-3.95-1.16c-.35-.3-.66-.67-.9-1.08a4.695 4.695 0 0 1-.61-3.23c-.91-.31-1.86-.46-2.83-.46Z",fill:t}),b.createElement("path",{d:"M19 9.75a4.7 4.7 0 0 1-3.17-1.23c-.35-.3-.66-.67-.9-1.08-.44-.72-.68-1.57-.68-2.44 0-.51.08-1.01.24-1.49.22-.68.6-1.31 1.11-1.82.9-.92 2.11-1.44 3.41-1.44 1.36 0 2.65.58 3.53 1.58A4.74 4.74 0 0 1 23.76 5c0 .38-.05.76-.15 1.12-.1.45-.29.92-.55 1.33a4.61 4.61 0 0 1-2.58 2.05c-.45.17-.95.25-1.48.25Zm0-8c-.89 0-1.72.35-2.33.98-.35.36-.6.77-.75 1.24-.11.33-.16.68-.16 1.03 0 .59.16 1.17.47 1.67.16.28.37.53.61.74.87.79 2.16 1.04 3.17.68.76-.24 1.38-.74 1.78-1.41.18-.29.3-.6.37-.91.07-.26.1-.51.1-.77 0-.8-.3-1.57-.84-2.17A3.22 3.22 0 0 0 19 1.75Z",fill:t}),b.createElement("path",{d:"M20.49 5.73H17.5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h2.99a.749.749 0 1 1 0 1.5Z",fill:t}),b.createElement("path",{d:"M19 7.26c-.41 0-.75-.34-.75-.75V3.52c0-.41.34-.75.75-.75s.75.34.75.75v2.99c0 .42-.34.75-.75.75Z",fill:t}))},K=function(e){var t=e.color;return b.createElement(b.Fragment,null,b.createElement("path",{opacity:".4",d:"M11.458 13.73a2.81 2.81 0 1 0 0-5.62 2.81 2.81 0 0 0 0 5.62ZM16.65 20.199c0-2.33-2.32-4.23-5.19-4.23-2.87 0-5.19 1.89-5.19 4.23",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),b.createElement("path",{d:"M21 12.5c0 5.25-4.25 9.5-9.5 9.5S2 17.75 2 12.5 6.25 3 11.5 3c1.31 0 2.56.26 3.7.74-.13.4-.2.82-.2 1.26 0 .75.21 1.46.58 2.06.2.34.46.65.76.91C17.04 8.61 17.97 9 19 9c.44 0 .86-.07 1.25-.21.48 1.14.75 2.4.75 3.71Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),b.createElement("path",{d:"M23 5c0 .32-.04.63-.12.93-.09.4-.25.79-.46 1.13-.48.81-1.25 1.44-2.17 1.73-.39.14-.81.21-1.25.21a3.92 3.92 0 0 1-2.66-1.03c-.3-.26-.56-.57-.76-.91A3.92 3.92 0 0 1 15 5c0-.44.07-.86.2-1.26A3.995 3.995 0 0 1 19 1c1.18 0 2.25.51 2.97 1.33C22.61 3.04 23 3.98 23 5ZM20.492 4.98h-2.98M19 3.52v2.99",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},ee=function(e,t){switch(e){case"Bold":return b.createElement(J,{color:t});case"Broken":return b.createElement(H,{color:t});case"Bulk":return b.createElement(Q,{color:t});case"Linear":default:return b.createElement(X,{color:t});case"Outline":return b.createElement(Y,{color:t});case"TwoTone":return b.createElement(K,{color:t})}},et=(0,b.forwardRef)(function(e,t){var n=e.variant,r=e.color,i=e.size,a=(0,q._)(e,G);return b.createElement("svg",(0,q.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),ee(n,r))});et.propTypes={variant:$().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:$().string,size:$().oneOfType([$().string,$().number])},et.defaultProps={variant:"Linear",color:"currentColor",size:"24"},et.displayName="UserCirlceAdd";var en=n(8400),er=n(61703),ei=n(7836),ea=n(27648),el=n(2602);function es(e){var t,n,a,l,s,o,d,m,f,v,x,h;let{res:g,user:j,onTransferAgain:y}=e,{t:N}=(0,k.$G)(),b=null==g?void 0:g.data,{getWalletByCurrencyCode:w,wallets:C}=(0,U.r)(),Z=w(C,null==b?void 0:null===(t=b.from)||void 0===t?void 0:t.currency),M=e=>{E.toast.promise((0,O.y)("".concat(e)),{loading:N("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return e.message},error:e=>e.message})};return(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,r.jsxs)("h2",{className:"mb-1 flex items-center justify-center gap-2 text-2xl font-semibold text-foreground",children:[(0,r.jsx)(B.Z,{size:"32",color:"#13A10E",variant:"Bold"}),(0,r.jsx)("span",{children:N("Transfer successful")})]}),(0,r.jsx)(u.Z,{className:"mb-1 mt-[5px] bg-divider"}),(0,r.jsx)(F.z,{senderName:null==b?void 0:null===(n=b.from)||void 0===n?void 0:n.label,senderAvatar:(0,p.qR)(null==b?void 0:null===(a=b.from)||void 0===a?void 0:a.image),receiverName:null==b?void 0:null===(l=b.to)||void 0===l?void 0:l.label,receiverAvatar:(0,p.qR)(null==b?void 0:null===(s=b.to)||void 0===s?void 0:s.image)}),(0,r.jsxs)(z.Y,{groupName:N("Transfer details"),children:[(0,r.jsx)(z.r,{title:"".concat(null==b?void 0:null===(o=b.to)||void 0===o?void 0:o.label," ").concat(N("will get")),value:"".concat(null==b?void 0:b.total," ").concat(null==b?void 0:null===(d=b.from)||void 0===d?void 0:d.currency)}),(0,r.jsx)(z.r,{title:N("Service charge"),value:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.J,{condition:null==b?void 0:b.fee,children:"".concat(null==b?void 0:b.fee," ").concat(null==b?void 0:null===(m=b.from)||void 0===m?void 0:m.currency)}),(0,r.jsx)(i.J,{condition:0===Number(null==b?void 0:b.fee),children:(0,r.jsx)(R.C,{variant:"success",children:N("Free")})})]})}),(0,r.jsx)(z.r,{title:N("Total"),value:"".concat(null==b?void 0:b.amount," ").concat(null==b?void 0:null===(f=b.from)||void 0===f?void 0:f.currency),valueClassName:"text-xl sm:text-2xl font-semibold"})]}),(0,r.jsx)(u.Z,{className:"mb-1 mt-[5px] bg-divider"}),(0,r.jsx)(I.T,{id:null==b?void 0:b.trxId,className:"mb-4 text-sm sm:text-base"}),(0,r.jsxs)("div",{className:"mb-8 space-y-4 text-sm sm:text-base",children:[(0,r.jsx)("h4",{className:"text-base font-medium sm:text-lg",children:N("New balance")}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(c.qE,{children:[(0,r.jsx)(c.F$,{src:null==Z?void 0:Z.logo}),(0,r.jsx)(c.Q5,{className:"bg-important text-important-foreground",children:null==Z?void 0:null===(v=Z.currency)||void 0===v?void 0:v.code})]}),(0,r.jsx)("span",{className:"text-sm font-bold",children:null==Z?void 0:null===(x=Z.currency)||void 0===x?void 0:x.code})]}),(0,r.jsx)("p",{className:"font-medium",children:"".concat(null==Z?void 0:Z.balance," ").concat(null==Z?void 0:null===(h=Z.currency)||void 0===h?void 0:h.code)})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,r.jsx)(A.T,{trxId:null==b?void 0:b.trxId,className:"w-full md:w-auto"}),(0,r.jsxs)("div",{className:"flex w-full flex-wrap gap-4 md:w-auto md:justify-end",children:[(0,r.jsxs)(_.h_,{children:[(0,r.jsxs)(_.$F,{className:(0,p.ZP)("flex w-full items-center space-x-1.5 md:w-fit",(0,L.d)({variant:"outline"})),children:[(0,r.jsx)("span",{children:N("Menu")}),(0,r.jsx)(D.Z,{size:16})]}),(0,r.jsxs)(_.AW,{align:"start",className:"m-0",children:[(0,r.jsxs)(_.Xi,{onSelect:()=>(0,p.Fp)(null==b?void 0:b.trxId),className:"flex items-center gap-2 text-sm font-medium focus:text-primary [&>svg]:hover:text-primary",children:[(0,r.jsx)(W.Z,{size:"20",variant:"Outline"}),N("Copy transaction ID")]}),(0,r.jsxs)(_.Xi,{onSelect:()=>{E.toast.promise((0,P.Az)({contactId:null==j?void 0:j.id}),{loading:N("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return(0,el.j)("/contacts"),e.message},error:e=>e.message})},className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[(0,r.jsx)(et,{size:"20",variant:"Outline"}),N("Add to contact")]}),(0,r.jsxs)(_.Xi,{onSelect:()=>M(null==b?void 0:b.id),className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[(0,r.jsx)(en.Z,{size:"20",variant:"Outline"}),N("Bookmark receipt")]}),(0,r.jsx)(_.VD,{}),(0,r.jsxs)(_.Xi,{onSelect:y,className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[(0,r.jsx)(er.Z,{size:"20",variant:"Outline"}),N("Transfer again")]})]})]}),(0,r.jsx)(L.z,{type:"button",className:"w-full md:max-w-48",asChild:!0,children:(0,r.jsxs)(ea.default,{href:"/",children:[(0,r.jsx)("span",{children:N("Go to dashboard")}),(0,r.jsx)(ei.Z,{size:16})]})})]})]})]})}var eo=n(93022),ec=n(31117),ed=n(90433);function eu(e){var t,n,l,s,o,d;let{onNext:m,onPrev:f,nextButtonLabel:v,isLoading:x=!1,formData:h,user:j}=e,{t:y}=(0,k.$G)(),{data:N,isLoading:b}=(0,ec.d)("/transfers/preview/create?amount=".concat(h.amount)),{wallets:w,isLoading:E,getWalletByCurrencyCode:C}=(0,U.r)(),Z=new p.F(h.currencyCode),M=C(w,h.currencyCode);return(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,r.jsx)("h2",{children:y("Confirm and proceed")}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h5",{className:"text-sm font-medium sm:text-base",children:y("Selected wallet")}),(0,r.jsx)("div",{className:"flex flex-row items-center gap-2.5",children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eo.O,{className:"size-8 rounded-full"}),(0,r.jsx)(eo.O,{className:"h-4 w-20 rounded-full"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(c.qE,{className:"size-8",children:[(0,r.jsx)(c.F$,{src:null==M?void 0:M.logo}),(0,r.jsx)(c.Q5,{className:"bg-important text-xs font-bold text-important-foreground",children:null==M?void 0:null===(t=M.currency)||void 0===t?void 0:t.code})]}),(0,r.jsx)("h6",{className:"font-bold",children:null==M?void 0:null===(n=M.currency)||void 0===n?void 0:n.code})]})})]}),(0,r.jsx)(u.Z,{className:"mb-1 mt-[5px] bg-divider"}),(0,r.jsxs)(z.Y,{groupName:y("Transfer details"),children:[(0,r.jsx)(z.r,{title:"".concat(null==j?void 0:j.name," ").concat(y("will get")),value:Z.formatVC(null==N?void 0:null===(l=N.data)||void 0===l?void 0:l.totalAmount),isLoading:b}),(0,r.jsx)(z.r,{title:y("Service charge"),isLoading:b,value:(null==N?void 0:null===(s=N.data)||void 0===s?void 0:s.fee)>0?Z.formatVC(null==N?void 0:null===(o=N.data)||void 0===o?void 0:o.fee):(0,r.jsx)(R.C,{variant:"success",children:y("Free")})}),(0,r.jsx)(z.r,{title:y("Total"),value:Z.formatVC(null==N?void 0:null===(d=N.data)||void 0===d?void 0:d.formatedAmount),isLoading:b})]}),(0,r.jsx)(u.Z,{className:"mb-1 mt-[5px] bg-divider"}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h5",{className:"text-sm font-medium sm:text-base",children:y("Recipient")}),(0,r.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,r.jsxs)(c.qE,{children:[(0,r.jsx)(c.F$,{src:(0,p.qR)(null==j?void 0:j.avatar)}),(0,r.jsx)(c.Q5,{children:(0,g.v)(null==j?void 0:j.name)})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-0.5",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:null==j?void 0:j.name}),(0,r.jsx)("p",{className:"text-xs text-secondary-text",children:null==j?void 0:j.email})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 flex justify-end gap-4",children:[(0,r.jsxs)(L.z,{variant:"outline",onClick:f,type:"button",children:[(0,r.jsx)(ed.Z,{size:16}),(0,r.jsx)("span",{children:y("Back")})]}),(0,r.jsxs)(L.z,{type:"submit",onClick:m,disabled:x,className:"min-w-48",children:[(0,r.jsxs)(i.J,{condition:!x,children:[(0,r.jsx)("span",{children:v}),(0,r.jsx)(T.Z,{size:16})]}),(0,r.jsx)(i.J,{condition:x,children:(0,r.jsx)(a.Loader,{title:y("Processing..."),className:"text-primary-foreground"})})]})]})]})}let em=C.z.object({email:C.z.string().min(1,"Recipient email is required."),amount:C.z.string().min(1,"Transfer amount is required."),currencyCode:C.z.string({required_error:"Currency is required."})});function ef(){let{auth:e}=(0,h.a)(),{t}=(0,k.$G)(),n=(0,N.useSearchParams)(),[f,C]=(0,b.useState)("transfer_details"),[Z,L]=(0,b.useState)(null),[M,T]=(0,b.useTransition)(),[A,z]=(0,b.useState)(null),[I,F]=(0,b.useState)(!1),{contacts:R,isLoading:_}=(0,x.t)(),P=n.get("email"),[O,U]=(0,b.useState)([{id:"transfer_details",value:"transfer_details",title:t("Transfer details"),complete:!1},{id:"review",value:"review",title:t("Review"),complete:!1},{id:"finish",value:"finish",title:t("Finish"),complete:!1}]),B=(0,w.cI)({resolver:(0,j.F)(em),defaultValues:{email:"",amount:"",currencyCode:""},mode:"all"});(0,b.useEffect)(()=>{P&&z({id:"",avatar:"",name:"",email:P})},[P]),(0,b.useEffect)(()=>{B.setValue("email",null==A?void 0:A.email)},[null==A?void 0:A.email]);let D=e=>{U(t=>t.map(t=>t.id===e?{...t,complete:!0}:t))};if(!(null==e?void 0:e.canMakeTransfer()))return(0,r.jsx)(l.Z,{className:"flex-1 p-10"});let W=async()=>{let e=B.getValues("email");F(!0);try{let t=await m.Z.get("/users/check/?email=".concat(e));if(t&&t.status){F(!1);let e=null==t?void 0:t.data;z({id:null==e?void 0:e.id,avatar:null==e?void 0:e.profileImage,name:null==e?void 0:e.name,email:null==e?void 0:e.email}),C("review"),D("transfer_details")}}catch(e){F(!1),E.toast.error(t("User not found.")),B.setError("email",{message:t("User not found."),type:"required"})}};return(0,r.jsx)(s.Xg,{children:(0,r.jsx)(d.l0,{...B,children:(0,r.jsx)("form",{className:"md:h-full",children:(0,r.jsxs)("div",{className:"relative flex md:h-full",children:[(0,r.jsx)("div",{className:"w-full p-4 pb-10 md:h-full md:p-12",children:(0,r.jsxs)("div",{className:"mx-auto max-w-3xl",children:[(0,r.jsx)(i.J,{condition:"transfer_details"===f,children:(0,r.jsx)(s.cI,{})}),(0,r.jsx)(o.R,{tabs:O,value:f,onTabChange:e=>C(e),children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)(o.Q,{value:"transfer_details",children:(0,r.jsx)(S,{form:B,isCheckingUser:I,onNext:B.handleSubmit((e,t)=>{null==t||t.preventDefault(),W()})})}),(0,r.jsx)(o.Q,{value:"review",children:(0,r.jsx)(eu,{onPrev:()=>C("transfer_details"),onNext:B.handleSubmit(e=>{if(Z){C("finish");return}T(async()=>{let n=await v(e);n&&n.status?(C("finish"),D("review"),D("finish"),L(n),E.toast.success(n.message)):E.toast.error(t(n.message))})}),nextButtonLabel:t("Transfer"),isLoading:M,formData:B.getValues(),user:A})}),(0,r.jsx)(o.Q,{value:"finish",children:(0,r.jsx)(es,{res:Z,user:A,onTransferAgain:()=>{U([{id:"transfer_details",value:"transfer_details",title:t("Transfer details"),complete:!1},{id:"review",value:"review",title:t("Review"),complete:!1},{id:"finish",value:"finish",title:t("Finish"),complete:!1}]),B.reset(),C("transfer_details"),L(null),z(null)}})})]})})]})}),(0,r.jsx)(i.J,{condition:"transfer_details"===f,children:(0,r.jsx)(s.jT,{children:(0,r.jsxs)("div",{className:"mb-4 rounded-xl bg-background p-6 shadow-default",children:[(0,r.jsxs)("div",{className:"mb-6 border-b border-divider-secondary pb-6",children:[(0,r.jsx)("p",{className:"mb-2 font-medium text-foreground",children:t("Contacts")}),(0,r.jsx)("p",{className:"text-xs text-secondary-text",children:t("Click to autofill recipient")})]}),(0,r.jsxs)("div",{className:"flex h-full max-h-72 flex-col overflow-y-auto",children:[!_&&(!R||(null==R?void 0:R.length)===0)&&(0,r.jsx)("p",{className:"text-sm font-medium text-foreground/50",children:t("No data found")}),_?(0,r.jsx)(a.Loader,{}):null==R?void 0:R.map(e=>{var t,n,i,a,l,s,o,d,m,f;return(0,r.jsxs)(b.Fragment,{children:[(0,r.jsx)("div",{role:"presentation",onClick:()=>{z({id:e.contact.id,avatar:e.contact.customer.profileImage,email:e.contact.email,name:e.contact.customer.name}),B.setValue("email",e.email)},className:"flex items-center justify-between py-2 first:pt-0 hover:cursor-pointer",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(c.qE,{className:"size-8",children:[(0,r.jsx)(c.F$,{src:(0,p.qR)(null==e?void 0:null===(n=e.contact)||void 0===n?void 0:null===(t=n.customer)||void 0===t?void 0:t.profileImage)}),(0,r.jsx)(c.Q5,{className:"font-semibold",children:(0,g.v)(null==e?void 0:null===(a=e.contact)||void 0===a?void 0:null===(i=a.customer)||void 0===i?void 0:i.name)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-normal leading-5 text-foreground",children:null==e?void 0:null===(s=e.contact)||void 0===s?void 0:null===(l=s.customer)||void 0===l?void 0:l.name}),(0,r.jsx)("p",{className:"text-xs font-normal leading-4 text-secondary-text",children:(null==e?void 0:null===(d=e.contact)||void 0===d?void 0:null===(o=d.customer)||void 0===o?void 0:o.phone)&&(0,y.h)((0,p.Fg)(null==e?void 0:null===(f=e.contact)||void 0===f?void 0:null===(m=f.customer)||void 0===m?void 0:m.phone)).formatInternational()})]})]})}),(0,r.jsx)(u.Z,{className:"mb-1 mt-[5px]"})]},e.id)})]})]})})})]})})})})}},85487:function(e,t,n){n.d(t,{Loader:function(){return l}});var r=n(57437),i=n(94508),a=n(43949);function l(e){let{title:t="Loading...",className:n}=e,{t:l}=(0,a.$G)();return(0,r.jsxs)("div",{className:(0,i.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-inherit",children:l(t)})]})}},31026:function(e,t,n){n.d(t,{Xg:function(){return v},cI:function(){return f},jT:function(){return x}});var r=n(57437),i=n(62869),a=n(28152),l=n(94508),s=n(8400),o=n(90433),c=n(2265),d=n(43949);let u=c.createContext(null),m=()=>{let e=c.useContext(u);if(!e)throw Error("usePageLayout must be used within an PageLayoutCtx. Please ensure that your component is wrapped with an PageLayoutCtx.");return e};function f(e){let{className:t}=e,{t:n}=(0,d.$G)(),{setRightSidebar:a}=m();return(0,r.jsx)("div",{className:(0,l.ZP)("flex items-center justify-end md:mb-4 xl:hidden",t),children:(0,r.jsxs)(i.z,{onClick:()=>a(e=>!e),variant:"outline",size:"sm",type:"button",className:"text-sm",children:[(0,r.jsx)(s.Z,{size:"20"}),n("Bookmarks")]})})}function v(e){let{children:t}=e,[n,i]=c.useState(!1),{width:l}=(0,a.B)();c.useEffect(()=>{l>=1280&&i(!0)},[l]);let s=c.useMemo(()=>({width:l,rightSidebar:n,setRightSidebar:i}),[l,n]);return(0,r.jsx)(u.Provider,{value:s,children:t})}function x(e){let{children:t}=e,{t:n}=(0,d.$G)(),{width:a,rightSidebar:l,setRightSidebar:s}=m();return(0,r.jsxs)("div",{"data-expanded":a>=1280||a<1280&&l,className:"absolute inset-y-0 right-0 top-0 w-full max-w-96 translate-x-full bg-background-body p-6 transition-all duration-300 ease-in-out data-[expanded=true]:translate-x-0 xl:relative",children:[(0,r.jsxs)(i.z,{variant:"outline",size:"sm",type:"button",onClick:()=>s(!1),className:"mb-4 gap-[2px] bg-background text-sm hover:bg-background xl:hidden",children:[(0,r.jsx)(o.Z,{size:14}),n("Hide bookmarks")]}),t]})}},62869:function(e,t,n){n.d(t,{d:function(){return o},z:function(){return c}});var r=n(57437),i=n(37053),a=n(90535),l=n(2265),s=n(94508);let o=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,t)=>{let{className:n,variant:a,size:l,asChild:c=!1,...d}=e,u=c?i.g7:"button";return(0,r.jsx)(u,{className:(0,s.ZP)(o({variant:a,size:l,className:n})),ref:t,...d})});c.displayName="Button"},95186:function(e,t,n){n.d(t,{I:function(){return l}});var r=n(57437),i=n(2265),a=n(94508);let l=i.forwardRef((e,t)=>{let{className:n,type:i,...l}=e;return(0,r.jsx)("input",{type:i,className:(0,a.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",n),ref:t,...l})});l.displayName="Input"},26815:function(e,t,n){var r=n(57437),i=n(6394),a=n(90535),l=n(2265),s=n(94508);let o=(0,a.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(i.f,{ref:t,className:(0,s.ZP)(o(),n),...a})});c.displayName=i.f.displayName,t.Z=c},64863:function(e,t,n){n.d(t,{Az:function(){return s},Ch:function(){return l},hs:function(){return a}});var r=n(79981),i=n(97751);async function a(e){try{let t=await r.Z.post("/merchants/save",e);return(0,i.B)(t)}catch(e){return(0,i.D)(e)}}async function l(e){try{let t=await r.Z.post("/services/phone/save",e);return(0,i.B)(t)}catch(e){return(0,i.D)(e)}}async function s(e){try{if(!e.contactId)throw Error("contact id not found");let t=await r.Z.post("/contacts/create",e);return(0,i.B)(t)}catch(e){return(0,i.D)(e)}}},11564:function(e,t,n){n.d(t,{t:function(){return a}});var r=n(85323),i=n(79981);function a(e){var t;let{data:n,isLoading:a,error:l,mutate:s,...o}=(0,r.ZP)(e||"/contacts",e=>i.Z.get(e));return{contacts:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],isLoading:a,error:l,mutate:()=>s(n),...o}}},28152:function(e,t,n){n.d(t,{B:function(){return i}});var r=n(2265);let i=()=>{let[e,t]=r.useState(0),[n,i]=r.useState(0);function a(){window&&(t(window.innerWidth),i(window.innerHeight))}return r.useEffect(()=>(a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)),[]),{width:e,height:n}}},79981:function(e,t,n){var r=n(78040),i=n(83464);t.Z=i.default.create({baseURL:r.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,n){n.d(t,{rH:function(){return r},sp:function(){return i}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},i=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){n.d(t,{F:function(){return d},Fg:function(){return f},Fp:function(){return c},Qp:function(){return m},ZP:function(){return s},fl:function(){return o},qR:function(){return u},w4:function(){return v}});var r=n(78040),i=n(61994),a=n(14438),l=n(53335);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,l.m6)((0,i.W)(t))}function o(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class d{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let i;let a=void 0===t?this.currencyCode:t;try{i=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){i=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let l=null!==(r=null===(n=i.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:a,s=i.format(e),o=s.substring(l.length).trim();return{currencyCode:a,currencySymbol:l,formattedAmount:s,amountText:o}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",v=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",i=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?i.set(r,e):i.delete(r),i}},71599:function(e,t,n){n.d(t,{z:function(){return l}});var r=n(2265),i=n(98575),a=n(61188),l=e=>{var t,n;let l,o;let{present:c,children:d}=e,u=function(e){var t,n;let[i,l]=r.useState(),o=r.useRef(null),c=r.useRef(e),d=r.useRef("none"),[u,m]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=s(o.current);d.current="mounted"===u?e:"none"},[u]),(0,a.b)(()=>{let t=o.current,n=c.current;if(n!==e){let r=d.current,i=s(t);e?m("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?m("UNMOUNT"):n&&r!==i?m("ANIMATION_OUT"):m("UNMOUNT"),c.current=e}},[e,m]),(0,a.b)(()=>{if(i){var e;let t;let n=null!==(e=i.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=s(o.current).includes(e.animationName);if(e.target===i&&r&&(m("ANIMATION_END"),!c.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},a=e=>{e.target===i&&(d.current=s(o.current))};return i.addEventListener("animationstart",a),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",a),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}m("ANIMATION_END")},[i,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:r.useCallback(e=>{o.current=e?getComputedStyle(e):null,l(e)},[])}}(c),m="function"==typeof d?d({present:u.isPresent}):r.Children.only(d),f=(0,i.e)(u.ref,(l=null===(t=Object.getOwnPropertyDescriptor(m.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in l&&l.isReactWarning?m.ref:(l=null===(n=Object.getOwnPropertyDescriptor(m,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in l&&l.isReactWarning?m.props.ref:m.props.ref||m.ref);return"function"==typeof d||u.isPresent?r.cloneElement(m,{ref:f}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},66574:function(e,t,n){n.d(t,{h:function(){return s}});var r=n(39863),i=n(34181),a=n(24633);function l(){var e=(0,a.Z)(arguments),t=e.text,n=e.options,r=e.metadata;return(0,i.Z)(t,n,r)}function s(){return(0,r.Z)(l,arguments)}}}]);