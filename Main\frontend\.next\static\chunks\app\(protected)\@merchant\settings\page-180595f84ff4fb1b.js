(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[98657],{70129:function(e,n,r){Promise.resolve().then(r.bind(r,40571))},40571:function(e,n,r){"use strict";r.r(n),r.d(n,{default:function(){return o}});var t=r(57437),s=r(85487),l=r(45702),u=r(68332),i=r(61149),a=r(6596),d=r(68338),c=r(43949);function o(){let{t:e}=(0,c.$G)(),{data:n,user:r,address:o,isLoading:f,error:h}=(0,d.h)();return!n&&h?(0,t.jsx)("div",{className:"w-full bg-danger py-2.5 text-danger-foreground",children:(0,t.jsx)("p",{children:e("We encountered an issue while retrieving the requested data. Please try again later or contact support if the problem persists.")})}):!n&&f?(0,t.jsx)("div",{className:"flex w-full items-center justify-center py-10",children:(0,t.jsx)(s.Loader,{})}):(0,t.jsx)(a.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","PASSWORD_INFORMATION","ADDRESS_INFORMATION"],children:(0,t.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,t.jsx)(i.O,{user:r(n),isLoading:f,error:h}),(0,t.jsx)(l.h,{address:o(n)}),(0,t.jsx)(u.G,{})]})})}},68338:function(e,n,r){"use strict";r.d(n,{h:function(){return i}});var t=r(79981),s=r(74539),l=r(70517),u=r(85323);function i(){let{data:e,error:n,isLoading:r}=(0,u.ZP)("/customers/detail",e=>t.Z.get(e));return{data:null==e?void 0:e.data,user:e=>e?new l.n({...e,...null==e?void 0:e.user,avatar:null==e?void 0:e.profileImage}):null,address:e=>e?new s.k(null==e?void 0:e.address):null,isLoading:r,error:n}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,31384,60627,85598,77317,227,45967,92971,95030,1744],function(){return e(e.s=70129)}),_N_E=e.O()}]);