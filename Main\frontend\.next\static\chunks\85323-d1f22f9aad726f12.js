"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[85323],{24369:function(e,t,n){var r=n(2265),u="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function o(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!u(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),u=r[0].inst,c=r[1];return l(function(){u.value=n,u.getSnapshot=t,o(u)&&c({inst:u})},[e,n,t]),a(function(){return o(u)&&c({inst:u}),e(function(){o(u)&&c({inst:u})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},82558:function(e,t,n){e.exports=n(24369)},58034:function(e,t,n){n.d(t,{U:function(){return r}});let r="$inf$"},62827:function(e,t,n){n.d(t,{kY:function(){return o},ko:function(){return f},s6:function(){return d},xD:function(){return g}});var r=n(2602),u=n(58034),i=n(2265);let a=r.i&&window.__SWR_DEVTOOLS_USE__,l=a?window.__SWR_DEVTOOLS_USE__:[],s=e=>(0,r.a)(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],o=()=>(0,r.m)(r.d,(0,i.useContext)(r.S)),c=l.concat(e=>(t,n,i)=>{let a=n&&((...e)=>{let[i]=(0,r.s)(t),[,,,a]=r.b.get(r.c);if(i.startsWith(u.U))return n(...e);let l=a[i];return(0,r.e)(l)?n(...e):(delete a[i],l)});return e(t,a,i)}),d=e=>function(...t){let n=o(),[u,i,a]=s(t),l=(0,r.f)(n,a),d=e,{use:f}=l,g=(f||[]).concat(c);for(let e=g.length;e--;)d=g[e](d);return d(u,i||l.fetcher||null,l)},f=(e,t,n)=>{let r=t[e]||(t[e]=[]);return r.push(n),()=>{let e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}},g=(e,t)=>(...n)=>{let[r,u,i]=s(n),a=(i.use||[]).concat(t);return e(r,u,{...i,use:a})};a&&(window.__SWR_DEVTOOLS_REACT__=i)},85323:function(e,t,n){n.d(t,{ZP:function(){return d}});var r=n(2265),u=n(82558),i=n(2602),a=n(98183),l=n(62827);let s=()=>{};s(),new WeakMap;let o=r.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),c={dedupe:!0};i.O.defineProperty(i.g,"defaultValue",{value:i.d});let d=(0,l.s6)((e,t,n)=>{let{cache:s,compare:d,suspense:f,fallbackData:g,revalidateOnMount:w,revalidateIfStale:S,refreshInterval:h,refreshWhenHidden:v,refreshWhenOffline:_,keepPreviousData:p}=n,[E,b,O,V]=i.b.get(s),[k,R]=(0,i.s)(e),y=(0,r.useRef)(!1),L=(0,r.useRef)(!1),m=(0,r.useRef)(k),T=(0,r.useRef)(t),D=(0,r.useRef)(n),C=()=>D.current,U=()=>C().isVisible()&&C().isOnline(),[x,P,I,W]=(0,i.z)(s,k),j=(0,r.useRef)({}).current,F=(0,i.e)(g)?(0,i.e)(n.fallback)?i.U:n.fallback[k]:g,N=(e,t)=>{for(let n in j)if("data"===n){if(!d(e[n],t[n])&&(!(0,i.e)(e[n])||!d(Z,t[n])))return!1}else if(t[n]!==e[n])return!1;return!0},M=(0,r.useMemo)(()=>{let e=!!k&&!!t&&((0,i.e)(w)?!C().isPaused()&&!f&&!1!==S:w),n=t=>{let n=(0,i.m)(t);return(delete n._k,e)?{isValidating:!0,isLoading:!0,...n}:n},r=x(),u=W(),a=n(r),l=r===u?a:n(u),s=a;return[()=>{let e=n(x());return N(e,s)?(s.data=e.data,s.isLoading=e.isLoading,s.isValidating=e.isValidating,s.error=e.error,s):(s=e,e)},()=>l]},[s,k]),Q=(0,u.useSyncExternalStore)((0,r.useCallback)(e=>I(k,(t,n)=>{N(n,t)||e()}),[s,k]),M[0],M[1]),$=!y.current,q=E[k]&&E[k].length>0,z=Q.data,A=(0,i.e)(z)?F&&(0,i.B)(F)?o(F):F:z,B=Q.error,Y=(0,r.useRef)(A),Z=p?(0,i.e)(z)?(0,i.e)(Y.current)?A:Y.current:z:A,G=(!q||!!(0,i.e)(B))&&($&&!(0,i.e)(w)?w:!C().isPaused()&&(f?!(0,i.e)(A)&&S:(0,i.e)(A)||S)),H=!!(k&&t&&$&&G),J=(0,i.e)(Q.isValidating)?H:Q.isValidating,K=(0,i.e)(Q.isLoading)?H:Q.isLoading,X=(0,r.useCallback)(async e=>{let t,r;let u=T.current;if(!k||!u||L.current||C().isPaused())return!1;let l=!0,s=e||{},o=!O[k]||!s.dedupe,c=()=>i.I?!L.current&&k===m.current&&y.current:k===m.current,f={isValidating:!1,isLoading:!1},g=()=>{P(f)},w=()=>{let e=O[k];e&&e[1]===r&&delete O[k]},S={isValidating:!0};(0,i.e)(x().data)&&(S.isLoading=!0);try{if(o&&(P(S),n.loadingTimeout&&(0,i.e)(x().data)&&setTimeout(()=>{l&&c()&&C().onLoadingSlow(k,n)},n.loadingTimeout),O[k]=[u(R),(0,i.o)()]),[t,r]=O[k],t=await t,o&&setTimeout(w,n.dedupingInterval),!O[k]||O[k][1]!==r)return o&&c()&&C().onDiscarded(k),!1;f.error=i.U;let e=b[k];if(!(0,i.e)(e)&&(r<=e[0]||r<=e[1]||0===e[1]))return g(),o&&c()&&C().onDiscarded(k),!1;let a=x().data;f.data=d(a,t)?a:t,o&&c()&&C().onSuccess(t,k,n)}catch(n){w();let e=C(),{shouldRetryOnError:t}=e;!e.isPaused()&&(f.error=n,o&&c()&&(e.onError(n,k,e),(!0===t||(0,i.a)(t)&&t(n))&&(!C().revalidateOnFocus||!C().revalidateOnReconnect||U())&&e.onErrorRetry(n,k,e,e=>{let t=E[k];t&&t[0]&&t[0](a.aU,e)},{retryCount:(s.retryCount||0)+1,dedupe:!0})))}return l=!1,g(),!0},[k,s]),ee=(0,r.useCallback)((...e)=>(0,i.n)(s,m.current,...e),[]);if((0,i.u)(()=>{T.current=t,D.current=n,(0,i.e)(z)||(Y.current=z)}),(0,i.u)(()=>{if(!k)return;let e=X.bind(i.U,c),t=0;C().revalidateOnFocus&&(t=Date.now()+C().focusThrottleInterval);let n=(0,l.ko)(k,E,(n,r={})=>{if(n==a.N4){let n=Date.now();C().revalidateOnFocus&&n>t&&U()&&(t=n+C().focusThrottleInterval,e())}else if(n==a.l2)C().revalidateOnReconnect&&U()&&e();else if(n==a.QQ)return X();else if(n==a.aU)return X(r)});return L.current=!1,m.current=k,y.current=!0,P({_k:R}),G&&((0,i.e)(A)||i.r?e():(0,i.t)(e)),()=>{L.current=!0,n()}},[k]),(0,i.u)(()=>{let e;function t(){let t=(0,i.a)(h)?h(x().data):h;t&&-1!==e&&(e=setTimeout(n,t))}function n(){!x().error&&(v||C().isVisible())&&(_||C().isOnline())?X(c).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[h,v,_,k]),(0,r.useDebugValue)(Z),f&&(0,i.e)(A)&&k){if(!i.I&&i.r)throw Error("Fallback data is required when using Suspense in SSR.");T.current=t,D.current=n,L.current=!1;let e=V[k];if((0,i.e)(e)||o(ee(e)),(0,i.e)(B)){let e=X(c);(0,i.e)(Z)||(e.status="fulfilled",e.value=!0),o(e)}else throw B}return{mutate:ee,get data(){return j.data=!0,Z},get error(){return j.error=!0,B},get isValidating(){return j.isValidating=!0,J},get isLoading(){return j.isLoading=!0,K}}})}}]);