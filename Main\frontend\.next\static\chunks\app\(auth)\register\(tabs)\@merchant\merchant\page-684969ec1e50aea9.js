(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22403],{55748:function(e,t,n){Promise.resolve().then(n.bind(n,23774))},23774:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return L}});var r=n(57437),l=n(99376),a=n(2265),o=n(15054),i=n(41709),c=n(52323),s=n(85487),u=n(62869),d=n(15681),m=n(95186),h=n(26815),x=n(6512),f=n(71792),p=n(13590),v=n(90433),j=n(22291),g=n(29501),k=n(43949);function E(e){let{title:t,subTitle:n,onPrev:l,onSubmit:E,nextButtonLabel:b,isLoading:N=!1,formData:w}=e,{t:y}=(0,k.$G)(),L=(0,g.cI)({resolver:(0,p.F)(f.yC),defaultValues:{name:"",email:"",license:"",street:"",country:"",city:"",zipCode:""}});a.useEffect(()=>{(null==w?void 0:w.merchant)&&L.reset({...w.merchant})},[]);let z=a.useCallback(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"text",l=arguments.length>3?arguments[3]:void 0;return(0,r.jsx)(d.Wi,{control:L.control,name:e,render:e=>{let{field:a}=e;return(0,r.jsxs)(d.xJ,{children:[t?(0,r.jsx)(d.lX,{children:t}):null,(0,r.jsx)(d.NI,{children:(0,r.jsx)(m.I,{type:n,placeholder:l||y("Enter ".concat(t.toLowerCase())),...a})}),(0,r.jsx)(d.zG,{})]})}})},[L.control,y]);return(0,r.jsx)(d.l0,{...L,children:(0,r.jsxs)("form",{onSubmit:L.handleSubmit(E),children:[(0,r.jsx)(o.h,{title:t,subTitle:n}),(0,r.jsx)("div",{className:"my-6 flex h-[5px] items-center",children:(0,r.jsx)(x.Z,{className:"bg-divider"})}),(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[z("name",y("Merchant name")),z("email",y("Merchant email"),"email"),z("license",y("Merchant proof"),"text",y("Enter merchant license or register number")),(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,r.jsx)(h.Z,{className:"col-span-12",children:y("Merchant address")}),(0,r.jsx)("div",{className:"col-span-12",children:z("street","","text",y("Address line"))}),(0,r.jsx)("div",{className:"col-span-12",children:(0,r.jsx)(d.Wi,{control:L.control,name:"country",render:e=>{let{field:t}=e;return(0,r.jsxs)(d.xJ,{children:[(0,r.jsx)(d.NI,{children:(0,r.jsx)(c.g,{onSelectChange:e=>t.onChange(e.code.cca2)})}),(0,r.jsx)(d.zG,{})]})}})}),(0,r.jsx)("div",{className:"col-span-12 md:col-span-6",children:z("city","","text",y("City"))}),(0,r.jsx)("div",{className:"col-span-12 md:col-span-6",children:z("zipCode","","text",y("Zip Code"))})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,r.jsxs)(u.z,{className:"p-4 text-base leading-[22px]",variant:"outline",type:"button",onClick:l,children:[(0,r.jsx)(v.Z,{size:24}),y("Back")]}),(0,r.jsxs)(u.z,{type:"submit",disabled:N,className:"w-[286px] p-4 text-base leading-[22px]",children:[(0,r.jsxs)(i.J,{condition:!N,children:[b,(0,r.jsx)(j.Z,{size:16})]}),(0,r.jsx)(i.J,{condition:N,children:(0,r.jsx)(s.Loader,{className:"text-background"})})]})]})]})]})})}var b=n(56766),N=n(21251),w=n(21318),y=n(99723);function L(){let[e,t]=(0,a.useTransition)(),[n,o]=a.useState({accountType:3}),[c,s]=a.useState("register"),u=(0,l.useRouter)(),{t:d}=(0,k.$G)(),{siteName:m}=(0,N.T)(),h=e=>{t(async()=>{let t=await (0,b.Pq)(e);if(t&&t.status){var n;u.push("/register/email-verification-message?email=".concat(null===(n=t.data)||void 0===n?void 0:n.email))}})};return(0,r.jsxs)("div",{className:"container mb-10 max-w-3xl",children:[(0,r.jsx)(i.J,{condition:"register"===c,children:(0,r.jsx)(y.Z,{formData:n,title:d("Create an account"),subTitle:d("Welcome to {{siteName}}, let's get start",{siteName:m}),onPrev:()=>u.push("/register"),onSubmit:e=>{o(t=>({...t,...e})),s("personalInformation")}})}),(0,r.jsx)(i.J,{condition:"personalInformation"===c,children:(0,r.jsx)(w.Z,{formData:n,title:d("Add personal information"),subTitle:d("Welcome to {{siteName}}, let's get start",{siteName:m}),nextButtonLabel:d("Next"),onPrev:()=>s("register"),onSubmit:e=>{o(t=>({...t,...e})),s("merchantInformation")}})}),(0,r.jsx)(i.J,{condition:"merchantInformation"===c,children:(0,r.jsx)(E,{formData:n,title:d("Add merchant information"),subTitle:d("Welcome to {{siteName}}, let's get start",{siteName:m}),nextButtonLabel:d("Create account"),onPrev:()=>s("personalInformation"),isLoading:e,onSubmit:e=>{o(t=>({...t,merchant:e})),h({...n,merchant:e})}})})]})}},22291:function(e,t,n){"use strict";n.d(t,{Z:function(){return f}});var r=n(74677),l=n(2265),a=n(40718),o=n.n(a),i=["variant","color","size"],c=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},s=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},u=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),l.createElement("path",{fill:t,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},d=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},m=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},h=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},x=function(e,t){switch(e){case"Bold":return l.createElement(c,{color:t});case"Broken":return l.createElement(s,{color:t});case"Bulk":return l.createElement(u,{color:t});case"Linear":default:return l.createElement(d,{color:t});case"Outline":return l.createElement(m,{color:t});case"TwoTone":return l.createElement(h,{color:t})}},f=(0,l.forwardRef)(function(e,t){var n=e.variant,a=e.color,o=e.size,c=(0,r._)(e,i);return l.createElement("svg",(0,r.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:o,height:o,viewBox:"0 0 24 24",fill:"none"}),x(n,a))});f.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="ArrowRight2"},40875:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6718:function(e,t,n){"use strict";n.d(t,{D:function(){return l}});var r=n(2265);function l(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,31384,60627,31284,93355,227,12042,78871,92971,95030,1744],function(){return e(e.s=55748)}),_N_E=e.O()}]);