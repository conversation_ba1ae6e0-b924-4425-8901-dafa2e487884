(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2506],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},48340:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>w,default:()=>_});var a,s={};r.r(s),r.d(s,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>x,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>h,pages:()=>f,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>g,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),r(67206);var n=r(79319),i=r(20518),o=r(61902),l=r(62042),d=r(44630),c=r(44828),m=r(65505),u=r(13839);let p=["",{children:["(protected)",{admin:["children",{children:["merchants",{children:["[userId]",{children:["[merchantId]",{children:["fees",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,18380)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\fees\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,75680)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\fees\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,26105)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,73722)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,76667)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,94626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],f=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\fees\\page.tsx"],h="/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page",x={require:r,loadChunk:()=>Promise.resolve()},g=new d.AppPageRouteModule({definition:{kind:c.x.APP_PAGE,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page",pathname:"/merchants/[userId]/[merchantId]/fees",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var b=r(69094),v=r(5787),S=r(90527);let y=e=>e?JSON.parse(e):void 0,I=self.__BUILD_MANIFEST,j=y(self.__REACT_LOADABLE_MANIFEST),P=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page"],E=y(self.__RSC_SERVER_MANIFEST),N=y(self.__NEXT_FONT_MANIFEST),D=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];P&&E&&(0,v.Mo)({clientReferenceManifest:P,serverActionsManifest:E,serverModuleMap:(0,S.w)({serverActionsManifest:E,pageName:"/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page"})});let A=(0,i.d)({pagesType:b.s.APP,dev:!1,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:I,renderToHTML:l.f,reactLoadableManifest:j,clientReferenceManifest:P,serverActionsManifest:E,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:N,incrementalCacheHandler:null,interceptionRouteRewrites:D}),w=s;function _(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:A})}},82146:(e,t,r)=>{Promise.resolve().then(r.bind(r,72713))},31591:(e,t,r)=>{Promise.resolve().then(r.bind(r,13600))},35303:()=>{},72713:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(60926),s=r(58387),n=r(29411),i=r(59571),o=r(36162),l=r(34451),d=r(18662),c=r(1181),m=r(25694);async function u(e,t){try{let r=await c.Z.put(`/admin/merchants/update-fees/${t}`,e);return(0,m.B)(r)}catch(e){return(0,m.D)(e)}}var p=r(43291),f=r(15487),h=r(14761),x=r(64947),g=r(29220),b=r(45475),v=r(39228),S=r(32167),y=r(93633);let I=y.z.object({depositFee:y.z.string().optional(),withdrawalFee:y.z.string().optional(),exchangeFee:y.z.string().optional(),transferFee:y.z.string().optional(),paymentFee:y.z.string().optional()});function j(){let e=(0,x.UO)(),[t,r]=(0,g.useTransition)(),{t:c}=(0,v.$G)(),{data:m,isLoading:y,mutate:j}=(0,p.d)(`/admin/merchants/${e.merchantId}`),P=(0,b.cI)({resolver:(0,f.F)(I),defaultValues:{depositFee:"",withdrawalFee:"",exchangeFee:"",transferFee:"",paymentFee:""}});if(y)return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(n.Loader,{})});let E=[{name:"depositFee",label:c("Deposit Fee"),placeholder:"0.00%"},{name:"withdrawalFee",label:c("Withdrawal Fee"),placeholder:"0.00%"},{name:"exchangeFee",label:c("Exchange Fee"),placeholder:"0.00%"},{name:"transferFee",label:c("Transfer Fee"),placeholder:"0.00%"},{name:"paymentFee",label:c("Payment Fee"),placeholder:"0.00%"}];return(0,a.jsx)(i.UQ,{type:"multiple",defaultValue:["ServicesSettings"],children:(0,a.jsx)("div",{className:"flex flex-col gap-4 p-4",children:(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)(i.Qd,{value:"ServicesSettings",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,a.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:c("Fees")})}),(0,a.jsx)(i.vF,{className:"flex items-center gap-4 border-t pt-4",children:(0,a.jsx)(l.l0,{...P,children:(0,a.jsxs)("form",{onSubmit:P.handleSubmit(t=>{r(async()=>{let r=await u(t,e.userId);r.status?(S.toast.success(r.message),j()):S.toast.error(c(r.message))})}),className:"w-full",children:[(0,a.jsx)("div",{className:"flex w-full flex-col gap-y-6 px-1",children:E.map(e=>(0,a.jsx)(l.Wi,{control:P.control,name:e.name,render:({field:t})=>(0,a.jsxs)(l.xJ,{className:"w-full space-y-2.5",children:[(0,a.jsx)(l.lX,{children:e.label}),(0,a.jsx)(d.I,{type:"text",placeholder:e.placeholder,className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t}),(0,a.jsx)(l.zG,{})]})},e.name))}),(0,a.jsx)("div",{className:"mt-4 flex flex-row items-center justify-end gap-4",children:(0,a.jsxs)(o.z,{disabled:t,children:[(0,a.jsx)(s.J,{condition:t,children:(0,a.jsx)(n.Loader,{className:"text-primary-foreground"})}),(0,a.jsxs)(s.J,{condition:!t,children:[c("Save"),(0,a.jsx)(h.Z,{size:20})]})]})})]})})})]})})})})}},13600:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,runtime:()=>b});var a=r(60926),s=r(14579),n=r(30417),i=r(89551),o=r(53042),l=r(44788),d=r(38071),c=r(28531),m=r(5764),u=r(47020),p=r(737),f=r(64947);r(29220);var h=r(39228),x=r(32167),g=r(91500);let b="edge";function v({children:e}){let t=(0,f.UO)(),r=(0,f.lr)(),b=(0,f.tv)(),v=(0,f.jD)(),{t:S}=(0,h.$G)(),y=[{title:S("Account Details"),icon:(0,a.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}?${r.toString()}`,id:"__DEFAULT__"},{title:S("Transactions"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/transactions?${r.toString()}`,id:"transactions"},{title:S("KYC"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/kyc?${r.toString()}`,id:"kyc"},{title:S("Fees"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/fees?${r.toString()}`,id:"fees"},{title:S("Permissions"),icon:(0,a.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/permissions?${r.toString()}`,id:"permissions"},{title:S("Send Email"),icon:(0,a.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/send-email?${r.toString()}`,id:"send-email"}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,a.jsx)("li",{children:(0,a.jsxs)(p.Z,{href:"/merchants/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,a.jsx)(u.Z,{}),S("Back")]})}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",r.get("name")]}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",S("Merchant")," #",t.merchantId]})]}),(0,a.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,a.jsx)("span",{children:S("Active")}),(0,a.jsx)(n.Z,{defaultChecked:"1"===r.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:e=>{x.toast.promise((0,i.z)(t.userId),{loading:S("Loading..."),success:a=>{if(!a.status)throw Error(a.message);let s=new URLSearchParams(r);return(0,g.j)(`/admin/merchants/${t.merchantId}`),s.set("active",e?"1":"0"),b.push(`${v}?${s.toString()}`),a.message},error:e=>e.message})}})]})]}),(0,a.jsx)(s.a,{tabs:y})]}),e]})}},59571:(e,t,r)=>{"use strict";r.d(t,{Qd:()=>d,UQ:()=>l,o4:()=>c,vF:()=>m});var a=r(60926),s=r(73837),n=r(29220),i=r(65091),o=r(86059);let l=s.fC,d=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(s.ck,{ref:r,className:(0,i.ZP)("border-b",e),...t}));d.displayName="AccordionItem";let c=n.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsx)(s.h4,{className:"flex",children:(0,a.jsxs)(s.xz,{ref:n,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...r,children:[t,(0,a.jsx)(o.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));c.displayName=s.xz.displayName;let m=n.forwardRef(({className:e,children:t,...r},n)=>(0,a.jsx)(s.VY,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:(0,a.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",e),children:t})}));m.displayName=s.VY.displayName},34451:(e,t,r)=>{"use strict";r.d(t,{NI:()=>x,Wi:()=>m,l0:()=>d,lX:()=>h,xJ:()=>f,zG:()=>g});var a=r(60926),s=r(62001),n=r(29220),i=r(45475),o=r(66817),l=r(65091);let d=i.RV,c=n.createContext({}),m=({...e})=>(0,a.jsx)(c.Provider,{value:{name:e.name},children:(0,a.jsx)(i.Qr,{...e})}),u=()=>{let e=n.useContext(c),t=n.useContext(p),{getFieldState:r,formState:a}=(0,i.Gc)(),s=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...s}},p=n.createContext({}),f=n.forwardRef(({className:e,...t},r)=>{let s=n.useId();return(0,a.jsx)(p.Provider,{value:{id:s},children:(0,a.jsx)("div",{ref:r,className:(0,l.ZP)("space-y-2",e),...t})})});f.displayName="FormItem";let h=n.forwardRef(({className:e,required:t,...r},s)=>{let{error:n,formItemId:i}=u();return(0,a.jsx)("span",{children:(0,a.jsx)(o.Z,{ref:s,className:(0,l.ZP)(n&&"text-base font-medium text-destructive",e),htmlFor:i,...r})})});h.displayName="FormLabel";let x=n.forwardRef(({...e},t)=>{let{error:r,formItemId:n,formDescriptionId:i,formMessageId:o}=u();return(0,a.jsx)(s.g7,{ref:t,id:n,"aria-describedby":r?`${i} ${o}`:`${i}`,"aria-invalid":!!r,...e})});x.displayName="FormControl",n.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:s}=u();return(0,a.jsx)("p",{ref:r,id:s,className:(0,l.ZP)("text-sm text-muted-foreground",e),...t})}).displayName="FormDescription";let g=n.forwardRef(({className:e,children:t,...r},s)=>{let{error:n,formMessageId:i}=u(),o=n?String(n?.message):t;return o?(0,a.jsx)("p",{ref:s,id:i,className:(0,l.ZP)("text-sm font-medium text-destructive",e),...r,children:o}):null});g.displayName="FormMessage"},18662:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var a=r(60926),s=r(29220),n=r(65091);let i=s.forwardRef(({className:e,type:t,...r},s)=>(0,a.jsx)("input",{type:t,className:(0,n.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...r}));i.displayName="Input"},66817:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var a=r(60926),s=r(11537),n=r(8206),i=r(29220),o=r(65091);let l=(0,n.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef(({className:e,...t},r)=>(0,a.jsx)(s.f,{ref:r,className:(0,o.ZP)(l(),e),...t}));d.displayName=s.f.displayName;let c=d},89551:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var a=r(1181),s=r(25694);async function n(e){try{let t=await a.Z.put(`/admin/users/toggle-active/${e}`,{});return(0,s.B)(t)}catch(e){return(0,s.D)(e)}}},75680:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(s.a,{})})}},18380:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\fees\page.tsx#default`)},26105:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,runtime:()=>s});var a=r(18264);let s=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#runtime`),n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#default`)},73722:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(s.a,{})})}},76667:(e,t,r)=>{"use strict";function a({children:e}){return e}r.r(t),r.d(t,{default:()=>a}),r(87908)},94626:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(s.a,{})})}},73837:(e,t,r)=>{"use strict";r.d(t,{VY:()=>ei,h4:()=>es,ck:()=>ea,fC:()=>er,xz:()=>en});var a=r(29220),s=r(16769),n=r(56556),i=r(19677),o=r(58408),l=r(68878),d=r(22316),c=r(57730),m=r(90027),u=r(72814),p=r(60926),f="Collapsible",[h,x]=(0,s.b)(f),[g,b]=h(f),v=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:s,defaultOpen:n,disabled:i,onOpenChange:o,...c}=e,[m,h]=(0,l.T)({prop:s,defaultProp:n??!1,onChange:o,caller:f});return(0,p.jsx)(g,{scope:r,disabled:i,contentId:(0,u.M)(),open:m,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),children:(0,p.jsx)(d.WV.div,{"data-state":E(m),"data-disabled":i?"":void 0,...c,ref:t})})});v.displayName=f;var S="CollapsibleTrigger",y=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,...a}=e,s=b(S,r);return(0,p.jsx)(d.WV.button,{type:"button","aria-controls":s.contentId,"aria-expanded":s.open||!1,"data-state":E(s.open),"data-disabled":s.disabled?"":void 0,disabled:s.disabled,...a,ref:t,onClick:(0,o.M)(e.onClick,s.onOpenToggle)})});y.displayName=S;var I="CollapsibleContent",j=a.forwardRef((e,t)=>{let{forceMount:r,...a}=e,s=b(I,e.__scopeCollapsible);return(0,p.jsx)(m.z,{present:r||s.open,children:({present:e})=>(0,p.jsx)(P,{...a,ref:t,present:e})})});j.displayName=I;var P=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:s,children:n,...o}=e,l=b(I,r),[m,u]=a.useState(s),f=a.useRef(null),h=(0,i.e)(t,f),x=a.useRef(0),g=x.current,v=a.useRef(0),S=v.current,y=l.open||m,j=a.useRef(y),P=a.useRef(void 0);return a.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,c.b)(()=>{let e=f.current;if(e){P.current=P.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();x.current=t.height,v.current=t.width,j.current||(e.style.transitionDuration=P.current.transitionDuration,e.style.animationName=P.current.animationName),u(s)}},[l.open,s]),(0,p.jsx)(d.WV.div,{"data-state":E(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!y,...o,ref:h,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":S?`${S}px`:void 0,...e.style},children:y&&n})});function E(e){return e?"open":"closed"}var N=r(3237),D="Accordion",A=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[w,_,k]=(0,n.B)(D),[C,F]=(0,s.b)(D,[k,x]),R=x(),L=a.forwardRef((e,t)=>{let{type:r,...a}=e;return(0,p.jsx)(w.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,p.jsx)($,{...a,ref:t}):(0,p.jsx)(U,{...a,ref:t})})});L.displayName=D;var[M,T]=C(D),[Q,z]=C(D,{collapsible:!1}),U=a.forwardRef((e,t)=>{let{value:r,defaultValue:s,onValueChange:n=()=>{},collapsible:i=!1,...o}=e,[d,c]=(0,l.T)({prop:r,defaultProp:s??"",onChange:n,caller:D});return(0,p.jsx)(M,{scope:e.__scopeAccordion,value:a.useMemo(()=>d?[d]:[],[d]),onItemOpen:c,onItemClose:a.useCallback(()=>i&&c(""),[i,c]),children:(0,p.jsx)(Q,{scope:e.__scopeAccordion,collapsible:i,children:(0,p.jsx)(Y,{...o,ref:t})})})}),$=a.forwardRef((e,t)=>{let{value:r,defaultValue:s,onValueChange:n=()=>{},...i}=e,[o,d]=(0,l.T)({prop:r,defaultProp:s??[],onChange:n,caller:D}),c=a.useCallback(e=>d((t=[])=>[...t,e]),[d]),m=a.useCallback(e=>d((t=[])=>t.filter(t=>t!==e)),[d]);return(0,p.jsx)(M,{scope:e.__scopeAccordion,value:o,onItemOpen:c,onItemClose:m,children:(0,p.jsx)(Q,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(Y,{...i,ref:t})})})}),[B,Z]=C(D),Y=a.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:s,dir:n,orientation:l="vertical",...c}=e,m=a.useRef(null),u=(0,i.e)(m,t),f=_(r),h="ltr"===(0,N.gm)(n),x=(0,o.M)(e.onKeyDown,e=>{if(!A.includes(e.key))return;let t=e.target,r=f().filter(e=>!e.ref.current?.disabled),a=r.findIndex(e=>e.ref.current===t),s=r.length;if(-1===a)return;e.preventDefault();let n=a,i=s-1,o=()=>{(n=a+1)>i&&(n=0)},d=()=>{(n=a-1)<0&&(n=i)};switch(e.key){case"Home":n=0;break;case"End":n=i;break;case"ArrowRight":"horizontal"===l&&(h?o():d());break;case"ArrowDown":"vertical"===l&&o();break;case"ArrowLeft":"horizontal"===l&&(h?d():o());break;case"ArrowUp":"vertical"===l&&d()}let c=n%s;r[c].ref.current?.focus()});return(0,p.jsx)(B,{scope:r,disabled:s,direction:n,orientation:l,children:(0,p.jsx)(w.Slot,{scope:r,children:(0,p.jsx)(d.WV.div,{...c,"data-orientation":l,ref:u,onKeyDown:s?void 0:x})})})}),O="AccordionItem",[W,V]=C(O),G=a.forwardRef((e,t)=>{let{__scopeAccordion:r,value:a,...s}=e,n=Z(O,r),i=T(O,r),o=R(r),l=(0,u.M)(),d=a&&i.value.includes(a)||!1,c=n.disabled||e.disabled;return(0,p.jsx)(W,{scope:r,open:d,disabled:c,triggerId:l,children:(0,p.jsx)(v,{"data-orientation":n.orientation,"data-state":et(d),...o,...s,ref:t,disabled:c,open:d,onOpenChange:e=>{e?i.onItemOpen(a):i.onItemClose(a)}})})});G.displayName=O;var H="AccordionHeader",q=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,s=Z(D,r),n=V(H,r);return(0,p.jsx)(d.WV.h3,{"data-orientation":s.orientation,"data-state":et(n.open),"data-disabled":n.disabled?"":void 0,...a,ref:t})});q.displayName=H;var J="AccordionTrigger",X=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,s=Z(D,r),n=V(J,r),i=z(J,r),o=R(r);return(0,p.jsx)(w.ItemSlot,{scope:r,children:(0,p.jsx)(y,{"aria-disabled":n.open&&!i.collapsible||void 0,"data-orientation":s.orientation,id:n.triggerId,...o,...a,ref:t})})});X.displayName=J;var K="AccordionContent",ee=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,s=Z(D,r),n=V(K,r),i=R(r);return(0,p.jsx)(j,{role:"region","aria-labelledby":n.triggerId,"data-orientation":s.orientation,...i,...a,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=K;var er=L,ea=G,es=q,en=X,ei=ee}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4969,7283,5089,3711],()=>t(48340));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page"]=r}]);
//# sourceMappingURL=page.js.map