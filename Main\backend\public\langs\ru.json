{"Welcome to {{siteName}}": "Добро пожаловать на {{siteName}}", "Welcome to {{siteName}}, let's get start": "Добро пожаловать на {{siteName}}, давайте начнем", "Cards": "Карты", "No more": "Больше нет", "Completed": "Завершено", "Deposit gateway": "Шлюз для депозита", "Withdraw method": "Метод вывода", "Agent method": "Метод агента", "Done": "Готово", "Plugins": "Плагины", "Site Settings": "Настройки сайта", "Disbursements": "Выплаты", "Investments History": "История инвестиций", "Favorites": "Избранное", "Deposit to Customer": "Депозит для клиента", "Share this referral link to your friends and earn money.": "Поделитесь этой реферальной ссылкой с друзьями и зарабатывайте деньги.", "No favorite item": "Нет избранных элементов", "Are you sure you want to issue a card for this wallet?": "Вы уверены, что хотите выпустить карту для этого кошелька?", "Card Not Available": "Карта недоступна", "Issue Card": "Выпустить карту", "Confirm Your Card": "Подтвердите вашу карту", "By Agent": "Че<PERSON>ез агента", "Hours": "<PERSON>а<PERSON>ы", "Charges": "Комиссии", "Pick a date": "Выберите дату", "Account Settings": "Настройки аккаунта", "Charges/Commissions": "Комиссии/Комиссионные", "Sex": "Пол", "Charges and commissions": "Комиссии и комиссионные", "Agent withdrawal charge (Long distance)": "Комиссия за вывод через агента (Дальнее расстояние)", "Agent deposit charge (Long distance)": "Комиссия за депозит через агента (Дальнее расстояние)", "Withdraw details": "Детали вывода", "Withdraw again": "Вывести снова", "Your QR Code": "Ваш QR-код", "Total merchant payments received": "Всего получено платежей от мерчантов", "Received": "Получено", "Total payments today": "Всего платежей сегодня", "Customers can scan this QR code to make payments.": "Клиенты могут сканировать этот QR-код для совершения платежей.", "To make payment, scan the QR Code.": "Для совершения платежа отсканируйте QR-код.", "Total Earnings": "Общий доход", "Total bonus": "Об<PERSON>ий бонус", "Bulk withdraw": "Массовый вывод", "Disburse Now": "Выплатить сейчас", "MPay API": "MPay API", "Other name": "Другое имя", "Write required field name": "Напишите название обязательного поля", "Enter method value": "Введите значение метода", "Enter method name": "Введите название метода", "Enter currency": "Введите валюту", "Your account is not yet verified. Please complete the KYC process by submitting the required documents.": "Ваш аккаунт еще не проверен. Пожалуйста, завершите процесс KYC, отправив необходимые документы.", "Thank you for submitting your documents! Your KYC verification is currently under review. Our team is working to process your submission as quickly as possible.": "Спасибо за отправку документов! Ваша проверка KYC находится на рассмотрении. Наша команда работает над обработкой вашей заявки как можно быстрее.", "Your account has been successfully verified. ": "<PERSON>аш аккаунт успешно проверен.", "Your account has been successfully verified. If you have any questions feel free to reach out to our support team.": "Ваш аккаунт успешно проверен. Если у вас есть вопросы, не стесняйтесь обращаться в нашу службу поддержки.", "Card ID": "ID карты", "Issue date": "Дата выпуска", "Exp. Date": "Срок действия", "Payment Cards": "Платежные карты", "CVV": "CVV", "Interest Rate": "Процентная ставка", "Amount Invested": "Сумма инвестиций", "Duration Type": "Тип срока", "Duration": "Срок", "Profit": "Прибыль", "Matured Withdraw": "Вывод по истечении срока", "Merchant Transactions": "Транзакции мерчантов", "Electricity bill": "Счет за электричество", "No cards available": "Нет доступных карт", "My Investments": "Мои инвестиции", "Available Plans": "Доступные планы", "No investments available!": "Нет доступных инвестиций!", "Range": "Диа<PERSON>азон", "Profit Adjust": "Корректировка прибыли", "Withdraw After Matured": "Вывод после истечения срока", "Featured": "Рекомендуемые", "Days": "<PERSON><PERSON>и", "Max Amount": "Максимальная сумма", "Invest Now": "Инвестировать сейчас", "No description": "Нет описания", "Required Amount": "Необходимая сумма", "View details": "Просмотреть детали", "Invest": "Инвестировать", "Fixed": "Фиксированная", "Min Amount": "Минимальная сумма", "No cards found!": "Карты не найдены!", "Favorite phone numbers": "Избранные номера телефонов", "Enter recharge amount": "Введите сумму пополнения", "Meter Details": "Детали счетчика", "Click to autofill recipient": "Нажмите, чтобы автоматически заполнить получателя", "from dashboard": "с панели управления", "Document type": "Тип документа", "Card Management": "Управление картами", "Profit rate": "Процентная ставка", "Opening date": "Дата открытия", "Edit investment plan": "Редактировать инвестиционный план", "Term": "Срок", "Plan": "<PERSON><PERSON><PERSON><PERSON>", "Manage Plans": "Управление планами", "Are you sure you want to delete this customer? This action cannot be undone, and all associated data will be permanently removed.": "Вы уверены, что хотите удалить этого клиента? Это действие нельзя отменить, и все связанные данные будут удалены навсегда.", "Delete Customer Confirmation": "Подтверждение удаления клиента", "Continue": "Продолжить", "Pending Verification": "Ожидание проверки", "Site Info": "Информация о сайте", "Site Logo": "Логотип сайта", "Upload logo": "Загрузить логотип", "Favicon": "Фавикон", "Card Background": "<PERSON>он карты", "Upload favicon": "Загрузить фавикон", "Sign In Page Banner": "Баннер страницы входа", "Upload background": "Загрузить фон", "Upload banner": "Загрузить баннер", "Site Name": "Название сайта", "Enter Site Name": "Введите название сайта", "Site Url": "URL сайта", "Enter Site Url": "Введите URL сайта", "Enter API Url": "Введите URL API", "API Url": "URL API", "Referral Bonus Amount": "Сумма реферального бонуса", "Apply Referral": "Применить реферальную программу", "First deposit": "Первый депозит", "Referrer": "Реферер", "Verify email": "Подтвердить email", "Default Currency": "Валюта по умолчанию", "Select referral": "Выберите реферала", "Both": "Оба", "Referral Bonus Receiver": "Получатель реферального бонуса", "Customer Registration": "Регистрация клиента", "Default Language": "Язык по умолчанию", "Select language": "Выберите язык", "Agent Registration": "Регистрация агента", "Enter referral bonus amount": "Введите сумму реферального бонуса", "Merchant Registration": "Регистрация мерчанта", "Deposit Commission": "Комиссия за депозит", "Default fee": "Комиссия по умолчанию", "Withdraw Commission": "Комиссия за вывод", "Virtual Card": "Виртуальная карта", "Select card provider": "Выберите провайдера карты", "Card Provider": "Провайдер карты", "Min. Amount": "Мин. сумма", "Edit": "Редактировать", "Kyc Limit": "<PERSON>и<PERSON>ит KYC", "Crypto": "Криптовалюта", "Max. Amount": "Макс. сумма", "USD Rate": "Курс USD", "times": "раз", "Edit Currency": "Редактировать валюту", "Checking...": "Проверка...", "Enter recipient’s email": "Введите email получателя", "Favorite merchants": "Избранные мерчанты", "Click to autofill merchant": "Нажмите, чтобы автоматически заполнить мерчанта", "Enter merchant account": "Введите аккаунт мерчанта", "Contact number": "Контактный номер", "Last Name": "Фамилия", "First Name": "Имя", "Zip Code": "Почтовый индекс", "Deactivated": "Деак<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>н", "Daily transfer limit": "Лимит ежедневного перевода", "Verification Successful": "Проверка успешна", "Select your gender": "Выберите ваш пол", "Select your birth date": "Выберите дату рождения", "Your full mailing address": "Ваш полный почтовый адрес", "URL is not defined": "URL не определен", "Add to contact": "Добавить в контакты", "You can add customers to the block list to prevent them from using the platform.": "Вы можете добавить клиентов в черный список, чтобы предотвратить их использование платформы.", "Gateway logo": "Логотип шлюза", "Enter {{label}}": "Введите {{label}}", "Public Key": "Публичный ключ", "Master Key": "<PERSON>аст<PERSON><PERSON>-кл<PERSON><PERSON>", "Private Key": "Приват<PERSON><PERSON>й ключ", "Token": "Токен", "Webhook Id": "ID вебхука", "Secret Key": "Секретный ключ", "Live": "Режим реального времени", "Mode": "Режим", "Sandbox": "Песочница", "Add to blacklist": "Добавить в черный список", "Id": "ID", "Deposit Request": "Запрос на депозит", "Withdraw Request": "Запрос на вывод", "Amount Sent": "Отправленная сумма", "Processing Time (Hours)": "Время обработки (часы)", "Agent name": "Имя агента", "Enter processing time": "Введите время обработки", "Amount received": "Полученная сумма", "The inputType field must be defined": "Поле inputType должно быть определено", "Close Card": "Закрыть карту", "Expiry Date": "Срок действия", "Card Details": "Детали карты", "Expiry date": "Срок действия", "Card holder name": "Имя держателя карты", "View Card": "Просмотреть карту", "Deposit Money": "Депозит", "Copy Number": "Копировать номер", "Card Type": "<PERSON>и<PERSON> карты", "Contact Supports": "Поддержка", "Account Number": "Номер счета", "Account Name": "Имя счета", "Bank Name": "Название банка", "IBAN": "IBAN", "Enter exchange amount": "Введите сумму обмена", "Investment return": "Возврат инвестиций", "Referral bonus": "Реферальный бонус", "No methods available for this country.": "Для этой страны нет доступных методов.", "Fixed charge": "Фиксированная комиссия", "Name (Unique)": "Имя (уникальное)", "Method logo": "Логотип метода", "Method Fields": "Поля метода", "Method name": "Название метода", "Field type": "Тип поля", "Method details": "Детали метода", "Blacklist": "Черный список", "payload.uploadLogo.move is not a function": "payload.uploadLogo.move не является функцией", "Creating...": "Создание...", "Select a payment gateway": "Выберите платежный шлюз", "Select a method to continue.": "Выберите метод для продолжения.", "The params field must be a string": "Поле params должно быть строкой", "Receiver will get": "Получатель получит", "You are not allowed to deposit more than 250 USD": "Вам не разрешено вносить более 250 USD", "Balance insufficiant": "Недостаточный баланс", "The countryCode field must be defined": "Поле countryCode должно быть определено", "No agent available for this country.": "Для этой страны нет доступных агентов.", "Withdraw approved": "Вывод одобрен", "Accept withdraw": "Принять вывод", "Additional info": "Дополнительная информация", "Reject withdraw": "Отклонить вывод", "The params field must be an array": "Поле params должно быть массивом", "Invalid file extension svg. Only jpg, png, jpeg, webp are allowed": "Недопустимое расширение файла svg. Разрешены только jpg, png, jpeg, webp", "I read and accept the general terms & conditions of use": "Я прочитал и принимаю общие условия использования", "We provide funding for your AGENT account, you must be able to pay 50% of that amount to fund the customers, do you agree?": "Мы предоставляем финансирование для вашего аккаунта АГЕНТА, вы должны быть готовы оплатить 50% этой суммы для финансирования клиентов, вы согласны?", "You must be always available to recharge account for our customers, do you agree?": "Вы должны быть всегда доступны для пополнения счетов наших клиентов, вы согласны?", "Other": "Другое", "How much money do you want to start with? (USD)": "С какой суммы вы хотите начать? (USD)", "You must be honest, do you agree?": "Вы должны быть честны, вы согласны?", "Transfer limit": "Лимит перевода", "Add balance": "Добавить баланс", "Daily transfer amount": "Сумма ежедневного перевода", "Remove balance": "Удалить баланс", "Transfer amount limit": "Лимит суммы перевода", "Enter merchant license or register number": "Введите лицензию мерчанта или регистрационный номер", "Merchant proof": "Доказательство мерчанта", "Enter merchant name": "Введите имя мерчанта", "Address line": "Адресная строка", "Enter merchant email": "Введите email мерчанта", "Invalid file extension JPEG. Only jpg, png, jpeg, webp are allowed": "Недопустимое расширение файла JPEG. Разрешены только jpg, png, jpeg, webp", "Please provide all required field.": "Пожалуйста, заполните все обязательные поля.", "Please wait": "Пожалуйста, подождите", "Request is processing...": "Запрос обрабатывается...", "Currency must be different.": "Валюта должна быть другой.", "Notification limit (Optional)": "Лимит уведомлений (необязательно)", "Enter kyc limit": "Введите лимит KYC", "Enter daily transfer limit": "Введите лимит ежедневного перевода", "Enter daily transfer amount": "Введите сумму ежедневного перевода", "Enter USD rate": "Введите курс USD", "Create Currency": "Создать валюту", "Enter notification limit": "Введите лимит уведомлений", "Is Crypto": "Это криптовалюта", "Kyc limit (Optional)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (необязательно)", "Accept API rate": "Принять курс API", "Show less": "Показать меньше", "User not found.": "Пользователь не найден.", "Pay Now": "Оплатить сейчас", "Add to favorites": "Добавить в избранное", "Payment details": "Детали платежа", "This merchant is not at service": "Этот мерчант не обслуживается", "Loading...": "Загрузка...", "Withdraw Requests": "Запросы на вывод", "Deposit": "Депозит", "Transfer": "Перевод", "Exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Withdraw": "Вывод", "Contacts": "Контакты", "Referral": "Реферальная программа", "Services": "Услуги", "Deposit Requests": "Запросы на депозит", "Settlements": "Расчеты", "Transaction History": "История транзакций", "Merchant Transaction": "Транзакция мерчанта", "Log out": "Выйти", "Investments": "Инвестиции", "Settings": "Настройки", "Support": "Поддержка", "Wallets": "Кошельки", "Stay updated with alerts and offers. Customize your notifications.": "Будьте в курсе с уведомлениями и предложениями. Настройте свои уведомления.", "Payment Requests": "Запросы на оплату", "Mark all as read": "Отметить все как прочитанные", "Notifications": "Уведомления", "Select up to 5 contacts to add them in the Quick Send list.": "Выберите до 5 контактов, чтобы добавить их в список быстрой отправки.", "Copy link": "Копировать ссылку", "Refer a friend": "Рекомендовать друга", "Search...": "Поиск...", "Quick Send": "Быстрая отправка", "See all": "Смотреть все", "After Processing": "После обработки", "Amount": "Сумма", "Fee": "Комиссия", "Status": "Статус", "Payment": "Пла<PERSON><PERSON><PERSON>", "Dashboard": "Панель управления", "Type": "Тип", "Show all wallets": "Показать все кошельки", "Cancel": "Отмена", "Balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Deposits": "Депозиты", "Withdraws": "Выводы", "Pending": "Ожидание", "Exchanges": "Обмены", "Payments": "Плате<PERSON>и", "Customers": "Клиенты", "Customer List": "Список клиентов", "Bulk Email": "Массовая рассылка", "Agents": "Агенты", "Merchant List": "Список мерчантов", "Merchants": "Мер<PERSON>анты", "Transfers": "Переводы", "Pending Kyc": "Ожидание KYC", "Staffs": "Сотрудники", "Payment Request": "Запрос на оплату", "Total Withdraws": "Всего выводов", "Total Deposits": "Всего депозитов", "Agent List": "Список агентов", "Recently registered": "Недавно зарегистрированные", "Total Transfers": "Всего переводов", "Merchant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Customer": "Кли<PERSON><PERSON>т", "History": "История", "Total Exchanges": "Всего обменов", "Recent transactions": "Недавние транзакции", "Active": "Активный", "Failed": "Неудачно", "Gender": "Пол", "Clear Filter": "Очистить фильтр", "Export": "Экспорт", "Agent": "Агент", "Filter": "Фильтр", "Country": "Страна", "Male": "Мужской", "Inactive": "Неактивный", "Method": "Метод", "Date": "Дата", "User": "Пользователь", "View": "Просмотр", "Showing {{start}}-{{end}} of {{total}}": "Показано {{start}}-{{end}} из {{total}}", "Trx ID": "ID транзакции", "Female": "Женский", "Transaction ID": "ID транзакции", "User gets": "Пользователь получает", "Reject deposit": "Отклонить депозит", "Service charge": "Комиссия за услугу", "Deposit failed": "Депозит не удался", "Accept deposit": "Принять депозит", "Deposit approved": "Депозит одобрен", "Method used": "Использованный метод", "Method info": "Информация о методе", "Wallet": "Кошелек", "Received by": "Получено", "Send by": "Отправлено", "No data found!": "Данные не найдены!", "Send": "Отправить", "Withdraw failed": "Вывод не удался", "Number": "Номер", "Orange Money CIV fee": "Комиссия Orange Money CIV", "Payment method": "Метод оплаты", "Review": "Обзор", "Enter deposit amount": "Введите сумму депозита", "Next": "Далее", "Show more": "Показать больше", "Your Balance": "<PERSON>а<PERSON> баланс", "Select wallet": "Выберите кошелек", "How much?": "Сколько?", "Enter transfer amount": "Введите сумму перевода", "Add recipient": "Добавить получателя", "Finish": "Завершить", "Transfer details": "Детали перевода", "Bookmarks": "Закладки", "Add amount": "Добавить сумму", "Hide bookmarks": "Скрыть закладки", "Regular withdraw": "Обычный вывод", "Enter withdraw amount": "Введите сумму вывода", "Agent Selection": "Выбор агента", "Withdraw through an agent": "Вывод через агента", "Payment & Review": "Оплата и обзор", "From": "От", "To": "Кому", "No results found.": "Результатов не найдено.", "Add Merchant": "Добавить мерчанта", "Selected": "Выбрано", "Change": "Изменить", "Enter payment amount": "Введите сумму платежа", "MerchantID": "ID мерчанта", "Top-up": "Пополнение", "Number/ID": "Номер/ID", "Name": "Имя", "Website": "Веб-сайт", "Menu": "<PERSON>е<PERSON><PERSON>", "Email": "Email", "Recipient": "Получатель", "Processing...": "Обработка...", "will get": "получит", "Free": "Бесплатно", "Total": "Всего", "Confirm and proceed": "Подтвердить и продолжить", "Back": "Назад", "Selected wallet": "Выбранный кошелек", "Bookmark receipt": "Закладка квитанции", "New balance": "Новый баланс", "Go to dashboard": "Перейти к панели управления", "Transfer successful": "Перевод успешен", "Copy transaction ID": "Копировать ID транзакции", "Transfer again": "Перевести снова", "Download Receipt": "Скачать квитанцию", "Search agent...": "Поиск агента...", "Select country": "Выберите страну", "Select agent": "Выберите агента", "Select a country first": "Сначала выберите страну", "Change country": "Изменить страну", "Investment Amount": "Сумма инвестиции", "Exchange amount": "Сумма обмена", "Exchange rate": "Курс обмена", "You get": "Вы получаете", "Exchange details": "Детали обмена", "Exchange again": "Обменять снова", "Exchange successful": "Об<PERSON>ен успешен", "Payment again": "Оплатить снова", "Enter phone number": "Введите номер телефона", "Confirm and Recharge": "Подтвердить и пополнить", "Search country by name": "Поиск страны по названию", "Deposit Gateways": "Шлюзы для депозита", "Login Sessions": "Сессии входа", "Currency": "Валюта", "Withdraw Methods": "Методы вывода", "Currencies": "Валюты", "Currency Name": "Название валюты", "Code": "<PERSON>од", "Add Currency": "Добавить валюту", "More": "Еще", "No currency found": "Валюта не найдена", "Save": "Сохранить", "Services and Status": "Услуги и статус", "KYC": "KYC", "Select currency": "Выберите валюту", "Full name": "Полное имя", "Profile picture": "Фото профиля", "Upload photo": "Загрузить фото", "Profile": "Профиль", "Phone": "Телефон", "Address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Date of birth": "Дата рождения", "Privacy & Security": "Конфиденциальность и безопасность", "City name": "Название города", "Full mailing address": "Полный почтовый адрес", "Enter your email": "Введите ваш email", "Two-factor authentication is on.": "Двухфакторная аутентификация включена.", "Edit Password": "Изменить пароль", "To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.": "Для обеспечения максимальной безопасности двухфакторная аутентификация всегда включена по умолчанию. Вам нужно будет подтверждать ваш email при каждом входе.", "Password": "Пароль", "KYC Verification": "Проверка KYC", "Actions": "Действия", "End date": "Дата окончания", "Don’t have an account?": "Нет аккаунта?", "Sign up": "Зарегистрироваться", "Have an account?": "Есть аккаунт?", "Enter your password": "Введите ваш пароль", "Forgot password?": "Забыли пароль?", "Sign in": "Войти", "Enter your email address": "Введите ваш email", "Investment ID": "ID инвестиции", "Create new plan": "Создать новый план", "Description": "Описание", "Yes": "Да", "Edit plan": "Редактировать план", "Invalid user credentials": "Неверные учетные данные пользователя", "Remember this device": "Запомнить это устройство", "Two-factor authentication": "Двухфакторная аутентификация", "Resend code": "Отправить код снова", "Verify": "Подтвердить", "Please verify it's you": "Пожалуйста, подтвердите, что это вы", "We’ve sent you a 4 digit code to the email address you provided, please enter the code to verify it’s you.": "Мы отправили вам 4-значный код на указанный вами email, пожалуйста, введите код, чтобы подтвердить, что это вы.", "Download": "Скачать", "Awaiting KYC verification": "Ожидание проверки KYC", "Total Payments": "Всего платежей", "Submit Documents": "Отправить документы", "Personal": "Личное", "Investment type": "Тип инвестиции", "Enter name for the investment plan": "Введите название инвестиционного плана", "Enter investment amount": "Введите сумму инвестиции", "Plan name": "Название плана", "Duration (Days)": "Срок (дни)", "Investment amount (Fixed)": "Сумма инвестиции (фиксированная)", "Enter investment duration": "Введите срок инвестиции", "Enter a profit rate": "Введите процентную ставку", "Profit rate (Percentage)": "Процентная ставка (проценты)", "daily": "ежедневно", "Select portfolio adjust": "Выберите корректировку портфеля", "weekly": "еженедельно", "yearly": "ежегодно", "monthly": "ежемесячно", "Min": "<PERSON><PERSON><PERSON>", "Investment amount (min-max)": "Сумма инвестиции (мин-макс)", "No": "Нет", "Max": "<PERSON>а<PERSON><PERSON>", "No data found": "Данные не найдены", "Last logged in": "Последний вход", "Add Staff": "Добавить сотрудника", "Agent is not permitted for deposit": "Агент не имеет разрешения на депозит", "Deposit/Payment Gateways": "Шлюзы для депозита/оплаты", "Recommended": "Рекомендуется", "Meter Type": "Тип счетчика", "Electricity name": "Название электричества", "Write Provider name": "Напишите название провайдера", "Meter Number": "Номер счетчика", "Prepaid": "Предоплата", "Postpaid": "Постоплата", "Enter meter number": "Введите номер счетчика", "Agent ID": "ID агента", "Send an email to": "Отправить email на", "Write a message here...": "Напишите сообщение здесь...", "Select users to send mail": "Выберите пользователей для отправки письма", "CC": "Копия", "Message": "Сообщение", "Subject of your mail...": "Тема вашего письма...", "Subject": "Тема", "How much is the bill?": "Сколько составляет счет?", "Confirm password": "Подтвердите пароль", "Create New Staff": "Создать нового сотрудника", "Create": "Создать", "Processing": "Обработка", "Email address": "Адрес электронной почты", "Pay bill": "Оплатить счет", "You will get": "Вы получите", "City": "Город", "Cannot read properties of undefined (reading 'transaction')": "Не удалось прочитать свойства undefined (чтение 'transaction')", "Investment successful!": "Инвестиция успешна!", "Insufficient balance": "Недостаточный баланс", "withdrawn": "выведено", "Delete Investment": "Удалить инвестицию", "Are you sure you want to delete this investment? This action cannot be undone, and all associated data will be permanently removed.": "Вы уверены, что хотите удалить эту инвестицию? Это действие нельзя отменить, и все связанные данные будут удалены навсегда.", "on_hold": "на удержании", "Sending...": "Отправка...", "Update": "Обновить", "Total Profit": "Общая прибыль", "Are you sure you want to proceed? This action is irreversible and will permanently withdraw your investment.": "Вы уверены, что хотите продолжить? Это действие необратимо и навсегда выведет вашу инвестицию.", "Confirm Withdrawal": "Подтвердить вывод", "Start Date": "Дата начала", "Invested": "Инвестировано", "All Transactions": "Все транзакции", "Show bookmarks": "Показать закладки", "Select what you want to see": "Выберите, что вы хотите увидеть", "Action": "Действие", "Delete Staff": "Удалить сотрудника", "Are you sure you want to delete this staff? This action cannot be undone, and all associated data will be permanently removed.": "Вы уверены, что хотите удалить этого сотрудника? Это действие нельзя отменить, и все связанные данные будут удалены навсегда.", "Set as default": "Установить по умолчанию", "Unpin": "Открепить", "Default": "По умолчанию", "Create a New Wallet": "Создать новый кошелек", "No data...": "Нет данных...", "Add Wallet": "Добавить кошелек", "Currency Already in Use": "Валюта уже используется", "Available Currency": "Доступная валюта", "Added": "Добавлено", "Add": "Добавить", "Quickly and securely set up your new digital wallet by following the steps.": "Быстро и безопасно настройте ваш новый цифровой кошелек, следуя шагам.", "Referrals": "Рефералы", "Verified": "Проверено", "KYC Status": "Статус KYC", "Documents": "Документы", "Your account is verified": "<PERSON>аш аккаунт проверен", "Attach selfie": "Прикрепить селфи", "You have not submitted documents yet.": "Вы еще не отправили документы.", "Attach pictures": "Прикрепить фотографии", "Select document type": "Выберите тип документа", "Front Side": "Лицевая сторона", "Drag and drop file here or upload": "Перетащите файл сюда или загрузите", "Selfie": "Селфи", "Upload": "Загрузить", "Awaiting submission": "Ожидание отправки", "Back Side": "Обратная сторона", "IP Address": "IP-адрес", "Device": "Устройство", "Logged in": "Вход выполнен", "Logout from all device": "Выйти со всех устройств", "We're here to assist you with any questions or issues you may encounter. Please feel free to contact our support team by emailing:": "Мы здесь, чтобы помочь вам с любыми вопросами или проблемами, с которыми вы можете столкнуться. Пожалуйста, не стесняйтесь обращаться в нашу службу поддержки по email:", "Need Help?": "Нужна помощь?", "For faster assistance, please provide as much detail as possible about your issue, including screenshots or error messages if applicable. Our support hours are 24/7.": "Для более быстрой помощи, пожалуйста, предоставьте как можно больше деталей о вашей проблеме, включая скриншоты или сообщения об ошибках, если это применимо. Наша служба поддержки работает 24/7.", "Attachment": "Вложение", "Successfully logout.": "Успешный выход.", "Total Commission": "Общая комиссия", "Commission": "Комиссия", "Tip": "Совет", "Fee by customer": "Комиссия клиента", "Request payment": "Запрос на оплату", "Enter recipient's address": "Введите адрес получателя", "Enter recipient's email address": "Введите email получателя", "When you request payment, the recipient will receive an automated email to pay you.": "Когда вы запрашиваете оплату, получатель получит автоматическое письмо с запросом на оплату.", "Enter request amount": "Введите сумму запроса", "Enter recipient's name": "Введите имя получателя", "Close": "Закрыть", "The recipient will get an email with a payment link. You can also download and share the QR code. This request is valid for 15 minutes.": "Получатель получит письмо с ссылкой на оплату. Вы также можете скачать и поделиться QR-кодом. Этот запрос действителен в течение 15 минут.", "Payment Request Sent": "Запрос на оплату отправлен", "Merchant Settings": "Настройки мерчанта", "Webhook URL": "URL вебхука", "Store profile picture": "Фото профиля магазина", "Enter Merchant ID": "Введите ID мерчанта", "Store Profile": "Профиль магазина", "Merchant address": "Адрес мерчанта", "Merchant email": "Email мерчанта", "Merchant ID": "ID мерчанта", "Merchant name": "Имя мерчанта", "Generating...": "Генерация...", "API Key": "<PERSON> ключ", "Introducing MPay API": "Представляем MPay API", "Create Payment": "Создать платеж", "Re-generate key": "Перегенерировать ключ", "Copy": "Копировать", "API Documentation": "Документация API", "Introduction": "Введение", "Check Payment Status": "Проверить статус платежа", "Delete Key": "Удалить ключ", "Generate an API Key to start implementing our gateway. See documentation below for more.": "Сгенерируйте API ключ, чтобы начать внедрение нашего шлюза. Смотрите документацию ниже для получения дополнительной информации.", "Show Key": "Показать ключ", "Learn how to integrate the Merchant Payment API to receive payments from your customer in your own platform/system.": "Узнайте, как интегрировать Merchant Payment API для получения платежей от ваших клиентов на вашей платформе/системе.", "Hide menu": "Скрыть меню", "amount to be received": "сумма к получению", "a value compatible with {{name}} supported currencies": "значение, совместимое с поддерживаемыми валютами {{name}}", "An URL to redirect the customer after payment is cancelled/failed.": "URL для перенаправления клиента после отмены/неудачи платежа.", "An URL to receive webhook callback about payment status.": "URL для получения обратного вызова вебхука о статусе платежа.", "An URL to redirect the customer after payment is completed.": "URL для перенаправления клиента после завершения платежа.", "An URL to your logo. It'll be shown on the payment page.": "URL вашего логотипа. Он будет отображаться на странице оплаты.", "Customer email for identification purpose.": "Email клиента для идентификации.", "A custom object where you can add your custom data like customer email or transaction ID to verify the payment from your side. This field will be sent during webhook callbacks.": "Пользовательский объект, в который вы можете добавить свои данные, такие как email клиента или ID транзакции, чтобы проверить платеж с вашей стороны. Это поле будет отправлено во время обратных вызовов вебхука.", "If set to \"true\" the payment will take the fee from the customer directly.": "Если установлено значение \"true\", комиссия будет взиматься непосредственно с клиента.", "400 Bad Request": "400 Неверный запрос", "500 Internal Server Error": "500 Внутренняя ошибка сервера", "This operation is used to take payments directly from your customers in your account.": "Эта операция используется для получения платежей непосредственно от ваших клиентов на ваш счет.", "Request URL": "URL запроса", "Set it to \"true\" to test payment integration during development phase. Otherwise keep it \"false\" to take real payments.": "Установите значение \"true\" для тестирования интеграции платежей во время фазы разработки. В противном случае оставьте значение \"false\" для получения реальных платежей.", "Value": "Значение", "Bearer API_KEY (replace API_KEY with your actual API key).": "Bearer API_KEY (замените API_KEY на ваш фактический API ключ).", "Mandatory": "Обязательно", "Headers": "Заголовки", "Redirect your customer to": "Перенаправьте вашего клиента на", "Responses": "Ответы", "Authorization": "Авторизация", "Webhook (POST)": "Вебхук (POST)", "Body Parameters": "Параметры тела", "Example": "Пример", "401 Unauthorized": "401 Неавторизован", "This operation is used to get the status of a payment request. TrxId is the id received after request to payment has been successful.": "Эта операция используется для получения статуса запроса на оплату. TrxId — это ID, полученный после успешного запроса на оплату.", "Request Body": "Тело запроса", "Response Body": "Тело ответа", "Webhooks": "Вебхуки", "Total transaction /": "Всего транзакций /", "Deposit details": "Детали депозита", "Saved phone numbers": "Сохраненные номера телефонов", "Add recipient email": "Добавить email получателя", "Click to autofill phone number": "Нажмите, чтобы автоматически заполнить номер телефона", "Direct Deposit": "Прямой депозит", "Reject request": "Отклонить запрос", "Payment Status": "Статус платежа", "Approve request": "Одобрить запрос", "Preview": "Предпросмотр", "Complete": "Завершить", "Methods": "Методы", "Job/Occupation": "Работа/Профессия", "Enter your WhatsApp account number or link": "Введите номер вашего аккаунта WhatsApp или ссылку", "Enter your job": "Введите вашу работу", "WhatsApp number/link": "Номер/ссылка WhatsApp", "Agent profile": "Профиль агента", "Available methods": "Доступные методы", "Required": "Обязательно", "Input name": "Имя ввода", "Create method": "Создать метод", "Country code": "Код страны", "Add New Method": "Добавить новый метод", "Currency code": "Код валюты", "Input type": "Тип ввода", "Update method": "Обновить метод", "Agent withdrawal commission": "Комиссия за вывод агента", "Agent deposit commission": "Комиссия за депозит агента", "Required additional field": "Дополнительное обязательное поле", "Allow Deposit": "Разрешить депозит", "Allow Withdraw": "Разрешить вывод", "Select input type": "Выберите тип ввода", "Exchange from": "Обмен из", "Exchanged to": "Обмен на", "Approve": "Одобрить", "Reject": "Отклонить", "Transactions": "Транзакции", "Transaction": "Транзакция", "Role": "Роль", "Account Details": "Детали аккаунта", "Send Email": "Отправить email", "Permissions": "Разрешения", "Account Status": "Статус аккаунта", "Default Wallet": "Кошелек по умолчанию", "Account type": "Тип аккаунта", "Convert account type": "Конвертировать тип аккаунта", "This is a Customer Account": "Это аккаунт клиента", "Convert to Agent": "Конвертировать в агента", "You will need to add additional information to convert this account into a Merchant of Agent.": "Вам нужно будет добавить дополнительную информацию, чтобы конвертировать этот аккаунт в мерчанта или агента.", "Converting...": "Конвертация...", "Convert": "Конвертировать", "Convert to Customer": "Конвертировать в клиента", "Add merchant information": "Добавить информацию о мерчанте", "Add agent information": "Добавить информацию об агенте", "Convert to Merchant": "Конвертировать в мерчанта", "dialog description": "описание диалога", "Total Deposit": "Всего депозитов", "Total Withdraw": "Всего выводов", "Total Exchange": "Всего обменов", "Rejected": "Отклонено", "KYC Document Rejected": "Документ KYC отклонен", "The submitted KYC document has been rejected. Please review the document for discrepancies or invalid details and request the user to submit accurate information for verification.": "Предоставленный документ KYC был отклонен. Пожалуйста, проверьте документ на наличие несоответствий или неверных данных и запросите у пользователя предоставление точной информации для проверки.", "User have not submitted documents yet": "Пользователь еще не отправил документы", "KYC Documents not submitted yet": "Документы KYC еще не отправлены", "Permission": "Разрешение", "Blacklisted Methods": "Заблокированные методы", "Logo": "Лого<PERSON>ип", "Blacklisted Gateways": "Заблокированные шлюзы", "Permitted Actions": "Разрешенные действия", "Add account": "Добавить аккаунт", "User services": "Услуги пользователя", "Add/Remove balance": "Добавить/Удалить баланс", "Withdraw money": "Вывести деньги", "No Data": "Нет данных", "Keep in record": "Сохранить в записи", "Fees": "Комиссии", "Suspended": "Приостановлено", "Merchant access": "Доступ мерчанта", "Merchant status": "Статус мерчанта", "Access granted": "Доступ предоставлен", "Grant Access": "Предоставить доступ", "Awaiting Status": "Ожидание статуса", "Payment Fee": "Комиссия за платеж", "Exchange Fee": "Комиссия за обмен", "Withdrawal Fee": "Комиссия за вывод", "Deposit Fee": "Комиссия за депозит", "Transfer Fee": "Комиссия за перевод", "Block List": "Черный список", "Add Customer": "Добавить клиента", "Supported Currencies": "Поддерживаемые валюты", "Update gateway": "Обновить шлюз", "Active API": "Активный API", "Charge": "Комиссия", "Limit": "<PERSON>и<PERSON><PERSON><PERSON>", "How would you like to pay?": "Как вы хотите оплатить?", "For any issues please contact at": "По любым вопросам, пожалуйста, свяжитесь по адресу", "Pay": "Оплатить", "Go back": "Вернуться", "You can go back to the home page.": "Вы можете вернуться на главную страницу.", "This page isn't here anymore.": "Этой страницы больше нет.", "Select your preferred method": "Выберите предпочтительный метод", "Country of deposit": "Страна депозита", "Deposit pending": "Депозит в ожидании", "Deposit successful": "Депозит успешен", "Deposit again": "Депозит снова", "Amount not allowed (Min:) 200": "Сумма не разрешена (Мин:) 200", "Wallet ID": "ID кошелька", "Withdraw submitted": "Вывод отправлен", "Withdraw successful": "Вывод успешен", "Investment status updated successfully": "Статус инвестиции успешно обновлен", "Are you sure you want to close this card?": "Вы уверены, что хотите закрыть эту карту?", "Create new investment plan": "Создать новый инвестиционный план", "Currency is required": "Валюта обязательна", "Minimum amount is required": "Минимальная сумма обязательна", "Name is required": "Имя обязательно", "Interest rate is required": "Процентная ставка обязательна", "Duration is required": "Срок обязателен", "Withdraw after matured is required": "Вывод после истечения срока обязателен", "Duration type is required": "Тип срока обязателен", "Write description here...": "Напишите описание здесь...", "Remove": "Удалить", "All countries": "Все страны", "Maximum amount": "Максимальная сумма", "Label": "Метка", "Minimum amount": "Минимальная сумма", "Percentage charge (%)": "Процентная комиссия (%)", "Webhook Secret (Optional)": "Секрет вебхука (необязательно)", "Update plugin": "Обновить плагин", "Read Documentation": "Прочитать документацию", "Enter minimum transaction amount": "Введите минимальную сумму транзакции", "Enter currency name": "Введите название валюты", "Update Currency": "Обновить валюту", "Updating...": "Обновление...", "Enter maximum transaction amount": "Введите максимальную сумму транзакции", "Enter currency code": "Введите код валюты", "Front image": "Лицевое изображение", "Back image": "Обратное изображение", "Agent access": "Доступ агента", "Granted": "Предоставлено", "Enter your name": "Введите ваше имя", "Agent status": "Статус агента", "Use default instead": "Использовать по умолчанию", "Pay Commission": "Оплатить комиссию", "Phone number": "Номер телефона", "No preview": "Нет предпросмотра", "Investment updated successfully": "Инвестиция успешно обновлена"}