(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3377],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},59440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>C,default:()=>w});var a,s={};r.r(s),r.d(s,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>x,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>f,pages:()=>h,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>g,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),r(67206);var n=r(79319),i=r(20518),o=r(61902),c=r(62042),d=r(44630),l=r(44828),m=r(65505),u=r(13839);let p=["",{children:["(protected)",{admin:["children",{children:["merchants",{children:["[userId]",{children:["[merchantId]",{children:["permissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68080)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\permissions\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,44939)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\permissions\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,26105)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,73722)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,76667)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,94626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\permissions\\page.tsx"],f="/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page",x={require:r,loadChunk:()=>Promise.resolve()},g=new d.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page",pathname:"/merchants/[userId]/[merchantId]/permissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=r(69094),j=r(5787),b=r(90527);let S=e=>e?JSON.parse(e):void 0,y=self.__BUILD_MANIFEST,E=S(self.__REACT_LOADABLE_MANIFEST),N=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page"],k=S(self.__RSC_SERVER_MANIFEST),P=S(self.__NEXT_FONT_MANIFEST),A=S(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];N&&k&&(0,j.Mo)({clientReferenceManifest:N,serverActionsManifest:k,serverModuleMap:(0,b.w)({serverActionsManifest:k,pageName:"/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page"})});let I=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:y,renderToHTML:c.f,reactLoadableManifest:E,clientReferenceManifest:N,serverActionsManifest:k,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:P,incrementalCacheHandler:null,interceptionRouteRewrites:A}),C=s;function w(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:I})}},31591:(e,t,r)=>{Promise.resolve().then(r.bind(r,13600))},64260:(e,t,r)=>{Promise.resolve().then(r.bind(r,1771))},35303:()=>{},13600:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j,runtime:()=>v});var a=r(60926),s=r(14579),n=r(30417),i=r(89551),o=r(53042),c=r(44788),d=r(38071),l=r(28531),m=r(5764),u=r(47020),p=r(737),h=r(64947);r(29220);var f=r(39228),x=r(32167),g=r(91500);let v="edge";function j({children:e}){let t=(0,h.UO)(),r=(0,h.lr)(),v=(0,h.tv)(),j=(0,h.jD)(),{t:b}=(0,f.$G)(),S=[{title:b("Account Details"),icon:(0,a.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}?${r.toString()}`,id:"__DEFAULT__"},{title:b("Transactions"),icon:(0,a.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/transactions?${r.toString()}`,id:"transactions"},{title:b("KYC"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/kyc?${r.toString()}`,id:"kyc"},{title:b("Fees"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/fees?${r.toString()}`,id:"fees"},{title:b("Permissions"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/permissions?${r.toString()}`,id:"permissions"},{title:b("Send Email"),icon:(0,a.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/send-email?${r.toString()}`,id:"send-email"}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,a.jsx)("li",{children:(0,a.jsxs)(p.Z,{href:"/merchants/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,a.jsx)(u.Z,{}),b("Back")]})}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",r.get("name")]}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",b("Merchant")," #",t.merchantId]})]}),(0,a.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,a.jsx)("span",{children:b("Active")}),(0,a.jsx)(n.Z,{defaultChecked:"1"===r.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:e=>{x.toast.promise((0,i.z)(t.userId),{loading:b("Loading..."),success:a=>{if(!a.status)throw Error(a.message);let s=new URLSearchParams(r);return(0,g.j)(`/admin/merchants/${t.merchantId}`),s.set("active",e?"1":"0"),v.push(`${j}?${s.toString()}`),a.message},error:e=>e.message})}})]})]}),(0,a.jsx)(s.a,{tabs:S})]}),e]})}},1771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(60926),s=r(78133),n=r(29411),i=r(28871),o=r(87198),c=r(30417),d=r(92207),l=r(41911),m=r(43291),u=r(75785),p=r(81379),h=r(18001),f=r(64947),x=r(29220),g=r(39228),v=r(32167);function j(){let e=(0,f.UO)(),[t,r]=x.useState(""),[j,S]=x.useState(""),{t:y}=(0,g.$G)(),{data:E,isLoading:N}=(0,m.d)(`/admin/users/permission/${e.merchantId}`),{data:k,isLoading:P}=(0,m.d)(`/admin/users/blacklisted-methods/${e.merchantId}&search=${t}`),{data:A,isLoading:I}=(0,m.d)(`/admin/users/blacklisted-gateways/${e.merchantId}&search=${j}`),C=(e,t)=>{v.toast.promise((0,l.Y)(e,t),{loading:y("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return e.message},error:e=>e.message})};return(0,a.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsx)("div",{className:"flex items-center gap-1",children:(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:y("Permitted Actions")})})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsx)("div",{className:"max-w-[900px]",children:(0,a.jsxs)(d.iA,{children:[(0,a.jsx)(d.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(d.SC,{children:[(0,a.jsx)(d.ss,{children:y("Actions")}),(0,a.jsx)(d.ss,{children:y("Permission")})]})}),(0,a.jsx)(d.RM,{children:N?Array.from({length:8}).map((e,t)=>(0,a.jsxs)(d.SC,{children:[(0,a.jsx)(d.pj,{className:"w-full",children:(0,a.jsx)(o.O,{className:"h-4 w-2/3"})}),(0,a.jsx)(d.pj,{children:(0,a.jsx)(o.O,{className:"h-5 w-16"})})]},t)):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b,{title:y("Deposit money"),type:"deposit",defaultStatus:!!E?.data?.permission?.deposit,onChange:e=>C(e,E?.data?.permission?.id)}),(0,a.jsx)(b,{title:y("Withdraw money"),type:"withdraw",defaultStatus:!!E?.data?.permission?.withdraw,onChange:e=>C(e,E?.data?.permission?.id)}),(0,a.jsx)(b,{title:y("Payment"),type:"payment",defaultStatus:!!E?.data?.permission?.payment,onChange:e=>C(e,E?.data?.permission?.id)}),(0,a.jsx)(b,{title:y("Exchange"),type:"exchange",defaultStatus:!!E?.data?.permission?.exchange,onChange:e=>C(e,E?.data?.permission?.id)}),(0,a.jsx)(b,{title:y("Transfer"),type:"transfer",defaultStatus:!!E?.data?.permission?.transfer,onChange:e=>C(e,E?.data?.permission?.id)}),(0,a.jsx)(b,{title:y("Add account"),type:"addAccount",defaultStatus:!!E?.data?.permission?.addAccount,onChange:e=>C(e,E?.data?.permission?.id)}),(0,a.jsx)(b,{title:y("Add/Remove balance"),type:"addRemoveBalance",defaultStatus:!!E?.data?.permission?.addRemoveBalance,onChange:e=>C(e,E?.data?.permission?.id)}),(0,a.jsx)(b,{title:y("User services"),type:"services",defaultStatus:!!E?.data?.permission?.services,onChange:e=>C(e,E?.data?.permission?.id)})]})})]})})})]})}),(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center justify-between gap-1 gap-y-2 sm:gap-y-0",children:[(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:y("Blacklisted Gateways")}),(0,a.jsx)(s.R,{value:t,onChange:e=>r(e.target.value),placeholder:y("Search"),iconPlacement:"end"})]})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsxs)(d.iA,{children:[(0,a.jsx)(d.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(d.SC,{children:[(0,a.jsx)(d.ss,{children:y("Logo")}),(0,a.jsx)(d.ss,{children:y("Name")}),(0,a.jsx)(d.ss,{children:y("Status")}),(0,a.jsx)(d.ss,{children:y("Recommended")}),(0,a.jsx)(d.ss,{children:y("Permission")})]})}),(0,a.jsx)(d.RM,{children:P?(0,a.jsx)(d.SC,{children:(0,a.jsx)(d.pj,{colSpan:5,children:(0,a.jsx)(n.Loader,{})})}):k?.data?.blackListedMethods?.length===0?(0,a.jsx)(d.SC,{children:(0,a.jsx)(d.pj,{colSpan:5,className:"bg-accent/50",children:y("No Data")})}):k?.data?.blackListedMethods.map(e=>new p.n(e))?.map(e=>a.jsxs(d.SC,{className:"odd:bg-accent",children:[a.jsx(d.pj,{children:a.jsx("div",{className:"flex size-10 items-center justify-center rounded-full bg-muted",children:a.jsx(h.Z,{size:20})})}),a.jsx(d.pj,{className:"w-[420px]",children:e.name}),a.jsx(d.pj,{children:e.active?a.jsx(i.C,{variant:"success",children:y("Active")}):a.jsx(i.C,{variant:"secondary",children:y("Inactive")})}),a.jsx(d.pj,{children:e.recommended?a.jsx(i.C,{variant:"important",children:y("Yes")}):a.jsx(i.C,{variant:"secondary",children:y("No")})}),a.jsx(d.pj,{children:a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx("span",{children:y("No")}),a.jsx(c.Z,{defaultChecked:!1,disabled:!0,className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]},e.id))})]})})]})}),(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center justify-between gap-1 gap-y-2 sm:gap-y-0",children:[(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:y("Blacklisted Methods")}),(0,a.jsx)(s.R,{value:j,onChange:e=>S(e.target.value),placeholder:y("Search"),iconPlacement:"end"})]})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsxs)(d.iA,{children:[(0,a.jsx)(d.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(d.SC,{children:[(0,a.jsx)(d.ss,{className:"w-2/5",children:y("Name")}),(0,a.jsx)(d.ss,{children:y("Status")}),(0,a.jsx)(d.ss,{children:y("Recommended")}),(0,a.jsx)(d.ss,{children:y("Permission")})]})}),(0,a.jsx)(d.RM,{children:I?(0,a.jsx)(d.SC,{children:(0,a.jsx)(d.pj,{colSpan:5,children:(0,a.jsx)(n.Loader,{})})}):A?.data?.blackListedGateways?.length===0?(0,a.jsx)(d.SC,{children:(0,a.jsx)(d.pj,{colSpan:5,className:"bg-accent/50",children:y("No Data")})}):A?.data?.blackListedGateways?.map(e=>new u.M(e))?.map(e=>a.jsxs(d.SC,{className:"odd:bg-accent",children:[a.jsx(d.pj,{className:"w-2/5",children:e?.name}),a.jsx(d.pj,{children:e.active?a.jsx(i.C,{variant:"success",children:y("Active")}):a.jsx(i.C,{variant:"secondary",children:y("Inactive")})}),a.jsx(d.pj,{children:e.recommended?a.jsx(i.C,{variant:"important",children:y("Yes")}):a.jsx(i.C,{variant:"secondary",children:y("No")})}),a.jsx(d.pj,{children:a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx("span",{children:y("No")}),a.jsx(c.Z,{defaultChecked:!1,disabled:!0,className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]},e?.id))})]})})]})})]})}function b({title:e,type:t,defaultStatus:r,onChange:s}){let{t:n}=(0,g.$G)();return(0,a.jsxs)(d.SC,{className:"odd:bg-accent",children:[(0,a.jsx)(d.pj,{children:n(e)}),(0,a.jsx)(d.pj,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("span",{children:n(r?"Yes":"No")}),(0,a.jsx)(c.Z,{defaultChecked:r,onCheckedChange:e=>s({permission:t,status:e}),className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]})}},78133:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var a=r(60926);r(29220);var s=r(18662),n=r(65091),i=r(51670);function o({iconPlacement:e="start",className:t,containerClass:r,...o}){return(0,a.jsxs)("div",{className:(0,n.ZP)("relative flex items-center",r),children:[(0,a.jsx)(i.Z,{size:"20",className:(0,n.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),(0,a.jsx)(s.I,{type:"text",className:(0,n.ZP)("h-10","end"===e?"pr-10":"pl-10",t),...o})]})}},28871:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var a=r(60926),s=r(8206);r(29220);var n=r(65091);let i=(0,s.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,n.ZP)(i({variant:t}),e),...r})}},18662:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var a=r(60926),s=r(29220),n=r(65091);let i=s.forwardRef(({className:e,type:t,...r},s)=>(0,a.jsx)("input",{type:t,className:(0,n.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...r}));i.displayName="Input"},87198:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});var a=r(60926),s=r(65091);function n({className:e,...t}){return(0,a.jsx)("div",{className:(0,s.ZP)("animate-pulse rounded-md bg-muted",e),...t})}},92207:(e,t,r)=>{"use strict";r.d(t,{RM:()=>c,SC:()=>d,iA:()=>i,pj:()=>m,ss:()=>l,xD:()=>o});var a=r(60926),s=r(29220),n=r(65091);let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,n.ZP)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("thead",{ref:r,className:(0,n.ZP)("",e),...t}));o.displayName="TableHeader";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tbody",{ref:r,className:(0,n.ZP)("[&_tr:last-child]:border-0",e),...t}));c.displayName="TableBody",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tfoot",{ref:r,className:(0,n.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tr",{ref:r,className:(0,n.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("th",{ref:r,className:(0,n.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));l.displayName="TableHead";let m=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("td",{ref:r,className:(0,n.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));m.displayName="TableCell",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("caption",{ref:r,className:(0,n.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},89551:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var a=r(1181),s=r(25694);async function n(e){try{let t=await a.Z.put(`/admin/users/toggle-active/${e}`,{});return(0,s.B)(t)}catch(e){return(0,s.D)(e)}}},41911:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});var a=r(1181),s=r(25694);async function n(e,t){try{let r=await a.Z.put(`/admin/users/permission/${t}`,e);return(0,s.B)(r)}catch(e){return(0,s.D)(e)}}},51670:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var a=r(61394),s=r(29220),n=r(31036),i=r.n(n),o=["variant","color","size"],c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",fill:t}),s.createElement("path",{d:"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"m22 22-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(c,{color:t});case"Broken":return s.createElement(d,{color:t});case"Bulk":return s.createElement(l,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,n=e.color,i=e.size,c=(0,a._)(e,o);return s.createElement("svg",(0,a.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,n))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="SearchNormal1"},18001:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var a=r(61394),s=r(29220),n=r(31036),i=r.n(n),o=["variant","color","size"],c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM12 14.5c-5.01 0-9.09 3.36-9.09 7.5 0 .28.22.5.5.5h17.18c.28 0 .5-.22.5-.5 0-4.14-4.08-7.5-9.09-7.5Z",fill:t}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M15.02 3.01A4.944 4.944 0 0 0 12 2C9.24 2 7 4.24 7 7s2.24 5 5 5 5-2.24 5-5M20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z",fill:t}),s.createElement("path",{d:"M12 14.5c-5.01 0-9.09 3.36-9.09 7.5 0 .28.22.5.5.5h17.18c.28 0 .5-.22.5-.5 0-4.14-4.08-7.5-9.09-7.5Z",fill:t}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12.75c-3.17 0-5.75-2.58-5.75-5.75S8.83 1.25 12 1.25 17.75 3.83 17.75 7s-2.58 5.75-5.75 5.75Zm0-10A4.26 4.26 0 0 0 7.75 7 4.26 4.26 0 0 0 12 11.25 4.26 4.26 0 0 0 16.25 7 4.26 4.26 0 0 0 12 2.75ZM20.59 22.75c-.41 0-.75-.34-.75-.75 0-3.45-3.52-6.25-7.84-6.25S4.16 18.55 4.16 22c0 .41-.34.75-.75.75s-.75-.34-.75-.75c0-4.27 4.19-7.75 9.34-7.75 5.15 0 9.34 3.48 9.34 7.75 0 .41-.34.75-.75.75Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"M20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(c,{color:t});case"Broken":return s.createElement(d,{color:t});case"Bulk":return s.createElement(l,{color:t});case"Linear":default:return s.createElement(m,{color:t});case"Outline":return s.createElement(u,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,n=e.color,i=e.size,c=(0,a._)(e,o);return s.createElement("svg",(0,a.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,n))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="User"},75785:(e,t,r)=>{"use strict";r.d(t,{M:()=>a});class a{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.active=e?.active,this.activeApi=e?.activeApi,this.recommended=e?.recommended,this.variables=e?.variables,this.allowedCurrencies=e?.allowedCurrencies,this.allowedCountries=e?.allowedCountries,this.createdAt=e?.createdAt?new Date(e?.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):null}}},81379:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});class a{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.params=e?.params?JSON.parse(e?.params):null,this.currencyCode=e?.currencyCode,this.countryCode=e?.countryCode,this.active=!!e?.active,this.activeApi=!!e?.activeApi,this.recommended=!!e?.recommended,this.minAmount=e?.minAmount??0,this.maxAmount=e?.maxAmount??0,this.fixedCharge=e?.fixedCharge??0,this.percentageCharge=e?.percentageCharge,this.createdAt=e?.createdAt?new Date(e.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e.updatedAt):null}}},26105:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,runtime:()=>s});var a=r(18264);let s=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#runtime`),n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#default`)},73722:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(s.a,{})})}},44939:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(s.a,{})})}},68080:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\permissions\page.tsx#default`)},76667:(e,t,r)=>{"use strict";function a({children:e}){return e}r.r(t),r.d(t,{default:()=>a}),r(87908)},94626:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(s.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,7283,5089,3711],()=>t(59440));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page"]=r}]);
//# sourceMappingURL=page.js.map