(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72255],{57366:function(e,a,s){Promise.resolve().then(s.bind(s,26080))},26080:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return h}});var t=s(57437),n=s(39241),r=s(85539),l=s(27186),c=s(85017),u=s(6512),i=s(75730),o=s(94508),d=s(99376),m=s(2265),f=s(43949);function h(){var e;let a=(0,d.useSearchParams)(),[s,h]=m.useState(null!==(e=a.get("search"))&&void 0!==e?e:""),x=(0,d.useRouter)(),p=(0,d.usePathname)(),{t:v}=(0,f.$G)(),{data:g,meta:j,isLoading:N,refresh:w}=(0,i.Z)("/admin/customers?".concat(a.toString()),{keepPreviousData:!0});return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsx)("div",{className:"flex items-center sm:h-12",children:(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(r.R,{value:s,onChange:e=>{e.preventDefault();let a=(0,o.w4)(e.target.value);h(e.target.value),x.replace("".concat(p,"?").concat(a.toString()))},iconPlacement:"end",placeholder:v("Search..."),className:"w-full sm:w-auto"}),(0,t.jsx)(c.k,{canFilterUser:!0,canFilterByGender:!0,canFilterByCountryCode:!0}),(0,t.jsx)(l._,{url:"/admin/customers/export/all"})]})}),(0,t.jsx)(u.Z,{className:"my-4"}),(0,t.jsx)(n.Z,{data:g,meta:j,isLoading:N,refresh:w})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,83568,227,56993,85017,39241,92971,95030,1744],function(){return e(e.s=57366)}),_N_E=e.O()}]);