<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>
            Cashfree Checkout
        </title>
        <script src="https://sdk.cashfree.com/js/v3/cashfree.js">
            
        </script>
        <style>
            body {
                display: flex;
                height: 100vh;
                align-items: center;
                justify-content: center;
            }
            
            .loader {
                border: 16px solid #f3f3f3;
                /* Light grey */
                border-top: 16px solid #3498db;
                /* Blue */
                border-radius: 50%;
                width: 80px;
                height: 80px;
                animation: spin 2s linear infinite;
            }
            
            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }
            
                100% {
                    transform: rotate(360deg);
                }
            }
        </style>
    </head>
    <body>
        <div class="loader">
        </div>
        <script>
            const cashfree = Cashfree({
                mode: "{{mode}}"
            });
            
            document.addEventListener("DOMContentLoaded", () => {
                let checkoutOptions = {
                    paymentSessionId: "{{sessionId}}",
                    redirectTarget: "_self"
                };
                cashfree.checkout(checkoutOptions);
            });
        </script>
    </body>
</html>
