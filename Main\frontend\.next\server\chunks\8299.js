"use strict";exports.id=8299,exports.ids=[8299],exports.modules={59168:(e,s,t)=>{t.d(s,{Z:()=>Z});var a=t(10326),n=t(90772),r=t(62288),i=t(8281),l=t(77863),d=t(70012);function c({item:e}){let{t:s}=(0,d.$G)();return(0,a.jsxs)(r.Vq,{children:[a.jsx(r.hg,{asChild:!0,children:a.jsx(n.z,{type:"button",variant:"outline",className:"w-full",children:s("View details")})}),(0,a.jsxs)(r.cZ,{className:"sm:max-w-[625px]",children:[a.jsx(r.fK,{children:a.jsx(r.$N,{children:e?.name})}),(0,a.jsxs)("div",{className:"rounded-xl bg-secondary p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-xs",children:[(0,l.fl)(e?.durationType)," ",s("Profit")]}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[e?.interestRate,"%"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"text-xs",children:s("Profit Adjust")}),a.jsx("p",{className:"font-bold text-primary",children:(0,l.fl)(e?.durationType)})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-xs",children:s(e?.isRange?"Min Amount":"Required Amount")}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[e?.minAmount," ",e?.currency?.toUpperCase()]})]}),e?.isRange?(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"text-xs",children:s("Max Amount")}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[e?.maxAmount," ",e?.currency?.toUpperCase()]})]}):null]})]}),a.jsx(i.Z,{className:"border-b bg-transparent"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:s("Profit Adjust")}),a.jsx("span",{className:"text-sm font-semibold",children:(0,l.fl)(e?.durationType)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:s("Withdraw After Matured")}),a.jsx("span",{className:"text-sm font-semibold",children:s(e?.withdrawAfterMatured?"Yes":"No")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:s("Type")}),a.jsx("span",{className:"text-sm font-semibold",children:s(e?.isRange?"Range":"Fixed")})]})]}),a.jsx(i.Z,{className:"border-b bg-transparent"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"mb-1 text-sm",children:s("Description")}),a.jsx("p",{className:"text-sm text-gray-500",children:e?.description||s("No description")})]})]})]})}var m=t(5158),o=t(92392),x=t(55632),u=t(54432),f=t(49547),p=t(10734);async function h(e){try{let s=await f.Z.post("/investments",e);return(0,p.B)(s)}catch(e){return(0,p.D)(e)}}var j=t(74064),N=t(44284),b=t(17577),v=t(74723),y=t(85999),g=t(27256);let w=g.z.object({amountInvested:g.z.coerce.number({required_error:"Amount invested is required"}),minAmount:g.z.coerce.number({required_error:"Minimum amount is required"}),maxAmount:g.z.coerce.number({required_error:"Maximum amount is required"})}).refine(e=>e.amountInvested>=e.minAmount,{message:"Amount invested must be at least the minimum amount",path:["amountInvested"]}).refine(e=>e.amountInvested<=e.maxAmount,{message:"Amount must be at most the maximum amount",path:["amountInvested"]});function A({item:e,onMutate:s}){let{t}=(0,d.$G)(),[i,c]=(0,b.useTransition)(),[f,p]=(0,b.useState)(!1),g=(0,v.cI)({resolver:(0,j.F)(w),defaultValues:{amountInvested:Number(e.minAmount),minAmount:Number(e.minAmount),maxAmount:e?.isRange?Number(e.maxAmount):Number(e.minAmount)}});return(0,a.jsxs)(r.Vq,{open:f,onOpenChange:p,children:[a.jsx(r.hg,{asChild:!0,children:(0,a.jsxs)(n.z,{type:"button",variant:"default",disabled:!e.isActive,className:"w-full",children:[t("Invest Now"),a.jsx(N.Z,{size:20})]})}),(0,a.jsxs)(r.cZ,{className:"sm:max-w-[425px]",children:[a.jsx(r.fK,{children:a.jsx(r.$N,{children:t("Invest")})}),(0,a.jsxs)("div",{className:"rounded-xl bg-primary-selected p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-xs",children:[(0,l.fl)(e?.durationType)," ",t("Profit")]}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[e?.interestRate,"%"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"text-xs",children:t("Profit Adjust")}),a.jsx("p",{className:"font-bold text-primary",children:(0,l.fl)(e?.durationType)})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-xs",children:e?.isRange?t("Min Amount"):t("Required Amount")}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[e?.minAmount," ",e?.currency]})]}),e?.isRange?(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"text-xs",children:t("Max Amount")}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[e?.maxAmount," ",e?.currency]})]}):null]})]}),a.jsx(x.l0,{...g,children:(0,a.jsxs)("form",{onSubmit:g.handleSubmit(a=>{c(async()=>{let n=await h({amountInvested:a.amountInvested,investmentPlanId:e.id});n?.status?(y.toast.success(t("Investment successful!")),s(),p(!1)):y.toast.error(t(n?.message))})}),children:[a.jsx(x.Wi,{control:g.control,name:"amountInvested",render:({field:s})=>(0,a.jsxs)(x.xJ,{className:"col-span-12 lg:col-span-6",children:[a.jsx(x.lX,{children:`${t("Investment Amount")} (${e?.currency})`}),a.jsx(x.NI,{children:a.jsx(u.I,{type:"text",placeholder:e.isRange?`${e.minAmount} ${e.currency} - ${e.maxAmount} ${e.currency}`:`${e.minAmount} ${e.currency}`,className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...s})}),a.jsx(x.zG,{})]})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-end gap-2",children:[a.jsx(n.z,{variant:"outline",type:"button",onClick:()=>p(!1),children:t("Cancel")}),(0,a.jsxs)(n.z,{type:"submit",children:[a.jsx(m.J,{condition:i,children:a.jsx(o.Loader,{title:t("Processing..."),className:"text-primary-foreground"})}),a.jsx(m.J,{condition:!i,children:t("Invest Now")})]})]})]})})]})]})}var R=t(567),C=t(33071),I=t(35047);function Z({isAdmin:e=!1,item:s,onMutate:t}){let{t:r}=(0,d.$G)(),m=(0,I.useRouter)();return(0,a.jsxs)(C.Zb,{className:`w-[350px] overflow-hidden rounded-xl border-2 shadow-featured ${s.isFeatured?"border-primary":"border-transparent"}`,children:[(0,a.jsxs)(C.aY,{className:"space-y-4 px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("p",{className:"font-medium",children:s?.name}),s?.isFeatured?a.jsx(R.C,{variant:"success",children:r("Featured")}):null]}),a.jsx(i.Z,{className:"border-b bg-transparent"}),(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-xl bg-secondary p-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-xs",children:[(0,l.fl)(s?.durationType)," ",r("Profit")]}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[s?.interestRate,"%"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"text-xs",children:r("Duration")}),(0,a.jsxs)("p",{className:"font-bold text-primary",children:[s?.duration," ",r(s?.duration>1?"Days":"Day")]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-xs",children:r(s?.isRange?"Min Amount":"Required Amount")}),(0,a.jsxs)("p",{className:"text-sm font-semibold",children:[s?.minAmount," ",s?.currency?.toUpperCase()]})]}),s?.isRange?(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:"text-xs",children:r("Max Amount")}),(0,a.jsxs)("p",{className:"text-sm font-semibold",children:[s?.maxAmount," ",s?.currency?.toUpperCase()]})]}):null]}),a.jsx(i.Z,{className:"border-b bg-transparent"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:r("Profit Adjust")}),a.jsx("span",{className:"text-sm font-semibold",children:(0,l.fl)(s?.durationType)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:r("Withdraw After Matured")}),a.jsx("span",{className:"text-sm font-semibold",children:r(s?.withdrawAfterMatured?"Yes":"No")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:r("Type")}),a.jsx("span",{className:"text-sm font-semibold",children:r(s?.isRange?"Range":"Fixed")})]})]})]}),(0,a.jsxs)(C.eW,{className:"flex flex-col gap-2 p-4",children:[e?(0,a.jsxs)(n.z,{type:"button",onClick:()=>m.push(`/investments/edit-plan/${s?.id}`),variant:"default",className:"w-full",children:[r("Edit plan"),a.jsx(N.Z,{size:20})]}):a.jsx(A,{item:s,onMutate:t}),a.jsx(c,{item:s})]})]},s?.id)}},33646:(e,s,t)=>{t.d(s,{a:()=>x});var a=t(10326),n=t(90772),r=t(60097),i=t(6216),l=t(90434),d=t(35047),c=t(17577),m=t.n(c),o=t(70012);function x({tabs:e,fullWidth:s=!0,defaultSegment:t}){let[n,r]=(0,c.useState)(()=>e.map(e=>({...e,placeTo:"nav"}))),[i,l]=(0,c.useState)("");(0,d.useSelectedLayoutSegment)();let m=(0,c.useCallback)((e,s)=>{r(t=>t.map(t=>t.id===e?{...t,placeTo:s}:t))},[]);return(0,a.jsxs)("div",{className:`inline-flex h-12 items-center rounded-lg bg-accent p-1 text-muted-foreground ${s?"w-full":""}`,children:[n.map(e=>"nav"===e.placeTo?a.jsx(u,{...e,isActive:i===e.id,onClick:()=>l(e.id),updateTabPlace:m},e.id):null),a.jsx("div",{className:"ml-auto",children:a.jsx(f,{navItems:n,activeTabId:i})})]})}function u({title:e,id:s,icon:t,href:n,isActive:r,onClick:i,updateTabPlace:d}){let m=(0,c.useRef)(null);return(0,c.useCallback)(()=>{let e=m.current?.getBoundingClientRect(),t=window?.innerWidth;e&&t<e.right+150?d(s,"menu"):d(s,"nav")},[s,d]),(0,a.jsxs)(l.default,{href:n,"data-state":r?"active":"",onClick:i,prefetch:!1,ref:m,className:"inline-flex h-10 w-56 items-center justify-center gap-1 whitespace-nowrap rounded-md px-4 py-1.5 text-sm font-medium text-secondary-text ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[t,a.jsx("span",{children:e})]})}function f({navItems:e,activeTabId:s}){let[t,d]=m().useState(!1),c=e.filter(e=>"menu"===e.placeTo),{t:x}=(0,o.$G)();return 0===c.length?null:(0,a.jsxs)(r.h_,{open:t,onOpenChange:d,children:[a.jsx(r.$F,{asChild:!0,children:(0,a.jsxs)(n.z,{variant:"outline",className:"h-10 text-sm font-medium",children:[x("More"),a.jsx(i.Z,{size:16})]})}),a.jsx(r.AW,{children:c.map(e=>a.jsx(r.Xi,{"data-active":s===e.id,className:"data-[active=true]:bg-accent",children:(0,a.jsxs)(l.default,{href:e.href,prefetch:!1,onClick:()=>d(!1),className:"flex h-full w-full items-center gap-2",children:[e.icon,a.jsx("span",{children:e.title})]})},e.id))})]})}},33071:(e,s,t)=>{t.d(s,{Ol:()=>l,SZ:()=>c,Zb:()=>i,aY:()=>m,eW:()=>o,ll:()=>d});var a=t(10326),n=t(17577),r=t(77863);let i=n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let l=n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.ZP)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=n.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,r.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=n.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,r.ZP)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let m=n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.ZP)("p-6 pt-0",e),...s}));m.displayName="CardContent";let o=n.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,r.ZP)("flex items-center p-6 pt-0",e),...s}));o.displayName="CardFooter"},55632:(e,s,t)=>{t.d(s,{NI:()=>h,Wi:()=>o,l0:()=>c,lX:()=>p,xJ:()=>f,zG:()=>j});var a=t(10326),n=t(34214),r=t(17577),i=t(74723),l=t(31048),d=t(77863);let c=i.RV,m=r.createContext({}),o=({...e})=>a.jsx(m.Provider,{value:{name:e.name},children:a.jsx(i.Qr,{...e})}),x=()=>{let e=r.useContext(m),s=r.useContext(u),{getFieldState:t,formState:a}=(0,i.Gc)(),n=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},u=r.createContext({}),f=r.forwardRef(({className:e,...s},t)=>{let n=r.useId();return a.jsx(u.Provider,{value:{id:n},children:a.jsx("div",{ref:t,className:(0,d.ZP)("space-y-2",e),...s})})});f.displayName="FormItem";let p=r.forwardRef(({className:e,required:s,...t},n)=>{let{error:r,formItemId:i}=x();return a.jsx("span",{children:a.jsx(l.Z,{ref:n,className:(0,d.ZP)(r&&"text-base font-medium text-destructive",e),htmlFor:i,...t})})});p.displayName="FormLabel";let h=r.forwardRef(({...e},s)=>{let{error:t,formItemId:r,formDescriptionId:i,formMessageId:l}=x();return a.jsx(n.g7,{ref:s,id:r,"aria-describedby":t?`${i} ${l}`:`${i}`,"aria-invalid":!!t,...e})});h.displayName="FormControl",r.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:n}=x();return a.jsx("p",{ref:t,id:n,className:(0,d.ZP)("text-sm text-muted-foreground",e),...s})}).displayName="FormDescription";let j=r.forwardRef(({className:e,children:s,...t},n)=>{let{error:r,formMessageId:i}=x(),l=r?String(r?.message):s;return l?a.jsx("p",{ref:n,id:i,className:(0,d.ZP)("text-sm font-medium text-destructive",e),...t,children:l}):null});j.displayName="FormMessage"},54432:(e,s,t)=>{t.d(s,{I:()=>i});var a=t(10326),n=t(17577),r=t(77863);let i=n.forwardRef(({className:e,type:s,...t},n)=>a.jsx("input",{type:s,className:(0,r.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:n,...t}));i.displayName="Input"},31048:(e,s,t)=>{t.d(s,{Z:()=>m});var a=t(10326),n=t(34478),r=t(79360),i=t(17577),l=t(77863);let d=(0,r.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...s},t)=>a.jsx(n.f,{ref:t,className:(0,l.ZP)(d(),e),...s}));c.displayName=n.f.displayName;let m=c}};