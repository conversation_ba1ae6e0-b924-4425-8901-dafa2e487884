(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7089],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},85170:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>N,default:()=>k});var r,i={};s.r(i),s.d(i,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>g,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>S,pages:()=>f,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>x,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),s(67206);var a=s(79319),n=s(20518),o=s(61902),c=s(62042),l=s(44630),d=s(44828),m=s(65505),u=s(13839);let p=["",{children:["(protected)",{admin:["children",{children:["customers",{children:["[customerId]",{children:["send-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5887)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\send-email\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,55012)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\send-email\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,38520)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,96104)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,78174)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,73081)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],f=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\customers\\[customerId]\\send-email\\page.tsx"],S="/(protected)/@admin/customers/[customerId]/send-email/page",g={require:s,loadChunk:()=>Promise.resolve()},x=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/customers/[customerId]/send-email/page",pathname:"/customers/[customerId]/send-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var h=s(69094),E=s(5787),P=s(90527);let v=e=>e?JSON.parse(e):void 0,D=self.__BUILD_MANIFEST,b=v(self.__REACT_LOADABLE_MANIFEST),A=null==(r=self.__RSC_MANIFEST)?void 0:r["/(protected)/@admin/customers/[customerId]/send-email/page"],j=v(self.__RSC_SERVER_MANIFEST),I=v(self.__NEXT_FONT_MANIFEST),_=v(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];A&&j&&(0,E.Mo)({clientReferenceManifest:A,serverActionsManifest:j,serverModuleMap:(0,P.w)({serverActionsManifest:j,pageName:"/(protected)/@admin/customers/[customerId]/send-email/page"})});let y=(0,n.d)({pagesType:h.s.APP,dev:!1,page:"/(protected)/@admin/customers/[customerId]/send-email/page",appMod:null,pageMod:i,errorMod:null,error500Mod:null,Document:null,buildManifest:D,renderToHTML:c.f,reactLoadableManifest:b,clientReferenceManifest:A,serverActionsManifest:j,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:I,incrementalCacheHandler:null,interceptionRouteRewrites:_}),N=i;function k(e){return(0,a.C)({...e,IncrementalCache:o.k,handler:y})}},1212:(e,t,s)=>{Promise.resolve().then(s.bind(s,49666))},64795:(e,t,s)=>{Promise.resolve().then(s.bind(s,43012))},49666:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E,runtime:()=>h});var r=s(60926),i=s(14579),a=s(30417),n=s(89551),o=s(53042),c=s(44788),l=s(38071),d=s(28531),m=s(5764),u=s(47020),p=s(737),f=s(64947);s(29220);var S=s(39228),g=s(32167),x=s(91500);let h="edge";function E({children:e}){let t=(0,f.UO)(),s=(0,f.lr)(),h=(0,f.tv)(),E=(0,f.jD)(),{t:P}=(0,S.$G)(),v=[{title:P("Account Details"),icon:(0,r.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}?${s.toString()}`,id:"__DEFAULT__"},{title:P("Transactions"),icon:(0,r.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/transactions?${s.toString()}`,id:"transactions"},{title:P("KYC"),icon:(0,r.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/kyc?${s.toString()}`,id:"kyc"},{title:P("Permissions"),icon:(0,r.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/permissions?${s.toString()}`,id:"permissions"},{title:P("Send Email"),icon:(0,r.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/customers/${t?.customerId}/send-email?${s.toString()}`,id:"send-email"}],D=1===Number(s.get("active"));return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,r.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,r.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,r.jsx)("li",{children:(0,r.jsxs)(p.Z,{href:"/customers",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,r.jsx)(u.Z,{className:"size-4 sm:size-6"}),P("Back")]})}),(0,r.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",s.get("name")]}),(0,r.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",P("User")," #",t.customerId]})]}),(0,r.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,r.jsx)("span",{children:P("Active")}),(0,r.jsx)(a.Z,{className:"data-[state=unchecked]:bg-muted",defaultChecked:D,onCheckedChange:e=>{g.toast.promise((0,n.z)(t.customerId),{loading:P("Loading..."),success:r=>{if(!r.status)throw Error(r.message);let i=new URLSearchParams(s);return i.set("active",e?"1":"0"),(0,x.j)(`/admin/customers/${t.customerId}`),h.push(`${E}?${i.toString()}`),r.message},error:e=>e.message})}})]})]}),(0,r.jsx)(i.a,{tabs:v})]}),e]})}},43012:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var r=s(60926),i=s(29411),a=s(59571),n=s(36162),o=s(34451),c=s(18662),l=s(23009),d=s(52419),m=s(15487),u=s(14761),p=s(64947),f=s(29220),S=s(45475),g=s(39228),x=s(32167),h=s(93633);let E=h.z.object({subject:h.z.string({required_error:"User Key is required"}),message:h.z.string({required_error:"User Secret is required"})});function P(){let[e,t]=(0,f.useTransition)(),s=(0,p.lr)(),h=(0,p.UO)(),{t:P}=(0,g.$G)(),v=(0,S.cI)({resolver:(0,m.F)(E),defaultValues:{subject:"",message:""}});return(0,r.jsx)(a.UQ,{type:"multiple",defaultValue:["API"],children:(0,r.jsx)("div",{className:"flex flex-col gap-4 p-4",children:(0,r.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(a.Qd,{value:"API",className:"border-none px-4 py-0",children:[(0,r.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("div",{className:"flex items-center gap-1",children:(0,r.jsxs)("p",{className:"text-base font-medium leading-[22px]",children:[P("Send an email to")," ",s.get("name")]})})}),(0,r.jsx)(a.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:(0,r.jsx)(o.l0,{...v,children:(0,r.jsxs)("form",{onSubmit:v.handleSubmit(e=>{t(async()=>{let t=await (0,d.Y)(e,h.customerId);t.status?x.toast.success(t.message):x.toast.error(P(t.message))})}),className:"flex flex-col gap-4",children:[(0,r.jsx)(o.Wi,{name:"subject",control:v.control,render:({field:e})=>(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.lX,{children:P("Subject")}),(0,r.jsx)(c.I,{type:"text",placeholder:P("Subject of your mail..."),...e}),(0,r.jsx)(o.zG,{})]})}),(0,r.jsx)(o.Wi,{name:"message",control:v.control,render:({field:e})=>(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.lX,{children:P("Message")}),(0,r.jsx)(l.g,{placeholder:P("Write a message here..."),rows:10,...e}),(0,r.jsx)(o.zG,{})]})}),(0,r.jsx)("div",{className:"flex items-center justify-end gap-4",children:(0,r.jsx)(n.z,{disabled:e,className:"rounded-xl",children:e?(0,r.jsx)(i.Loader,{title:P("Sending.."),className:"text-primary-foreground"}):(0,r.jsxs)(r.Fragment,{children:[P("Send"),(0,r.jsx)(u.Z,{size:16})]})})})]})})})]})})})})}},38520:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a,runtime:()=>i});var r=s(18264);let i=(0,r.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\layout.tsx#runtime`),a=(0,r.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\layout.tsx#default`)},96104:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(42416),i=s(21237);function a(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(i.a,{})})}},55012:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(42416),i=s(21237);function a(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(i.a,{})})}},5887:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\customers\[customerId]\send-email\page.tsx#default`)},78174:(e,t,s)=>{"use strict";function r({children:e}){return e}s.r(t),s.d(t,{default:()=>r}),s(87908)},73081:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(42416),i=s(21237);function a(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(i.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4969,7283,5089,3711,7066],()=>t(85170));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/customers/[customerId]/send-email/page"]=s}]);
//# sourceMappingURL=page.js.map