(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4746],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},5649:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>w,default:()=>I});var a,n={};s.r(n),s.d(n,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>g,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>x,pages:()=>h,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>f,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),s(67206);var r=s(79319),i=s(20518),o=s(61902),l=s(62042),c=s(44630),d=s(44828),m=s(65505),u=s(13839);let p=["",{children:["(protected)",{admin:["children",{children:["transactions",{children:["[trxId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,38384)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\transactions\\[trxId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,97796)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\transactions\\[trxId]\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,27626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\transactions\\[trxId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\transactions\\[trxId]\\page.tsx"],x="/(protected)/@admin/transactions/[trxId]/page",g={require:s,loadChunk:()=>Promise.resolve()},f=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/transactions/[trxId]/page",pathname:"/transactions/[trxId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=s(69094),k=s(5787),y=s(90527);let b=e=>e?JSON.parse(e):void 0,j=self.__BUILD_MANIFEST,S=b(self.__REACT_LOADABLE_MANIFEST),E=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/transactions/[trxId]/page"],N=b(self.__RSC_SERVER_MANIFEST),P=b(self.__NEXT_FONT_MANIFEST),A=b(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];E&&N&&(0,k.Mo)({clientReferenceManifest:E,serverActionsManifest:N,serverModuleMap:(0,y.w)({serverActionsManifest:N,pageName:"/(protected)/@admin/transactions/[trxId]/page"})});let D=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@admin/transactions/[trxId]/page",appMod:null,pageMod:n,errorMod:null,error500Mod:null,Document:null,buildManifest:j,renderToHTML:l.f,reactLoadableManifest:S,clientReferenceManifest:E,serverActionsManifest:N,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:P,incrementalCacheHandler:null,interceptionRouteRewrites:A}),w=n;function I(e){return(0,r.C)({...e,IncrementalCache:o.k,handler:D})}},21388:(e,t,s)=>{Promise.resolve().then(s.bind(s,27628))},26807:(e,t,s)=>{Promise.resolve().then(s.bind(s,44450)),Promise.resolve().then(s.bind(s,40098))},35303:()=>{},27628:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j,runtime:()=>b});var a=s(60926),n=s(58387),r=s(29411),i=s(62797),o=s(36162),l=s(74988),c=s(43291),d=s(65091),m=s(3632),u=s(14455),p=s(37988),h=s(36086),x=s(90543),g=s(51018),f=s(30684),v=s(31949),k=s(64947),y=s(39228);let b="edge";function j(){let{t:e}=(0,y.$G)(),t=(0,k.UO)(),s=(0,k.tv)(),{data:b,isLoading:j}=(0,c.d)(`/transactions/trx/${t.trxId}`);if(j)return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.Loader,{})});let S=b?.data?new m.C(b?.data):null,E=new d.F;return S?(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)("div",{className:"grid grid-cols-12 gap-4",children:(0,a.jsx)("div",{className:"col-span-12 lg:col-span-7",children:(0,a.jsxs)("div",{className:"relative flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,a.jsx)(o.z,{type:"button",variant:"outline",size:"icon",onClick:s.back,className:"absolute left-4 top-4",children:(0,a.jsx)(h.Z,{})}),(0,a.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,a.jsx)(n.J,{condition:"completed"===S.status,children:(0,a.jsx)(x.Z,{variant:"Bulk",size:32,className:"text-success"})}),(0,a.jsx)(n.J,{condition:"failed"===S.status,children:(0,a.jsx)(g.Z,{variant:"Bulk",size:32,className:"text-destructive"})}),(0,a.jsx)(n.J,{condition:"pending"===S.status,children:(0,a.jsx)(f.Z,{variant:"Bulk",size:32,className:"text-primary"})}),(0,a.jsxs)("h2",{className:"font-semibold",children:[e("Transaction")," #",t.trxId]})]}),(0,a.jsx)(i.z,{senderAvatar:(0,d.qR)(S.from.image),senderName:S.from.label,senderInfo:[S.from?.email,S?.from?.phone],receiverAvatar:(0,d.qR)(S?.to?.image),receiverName:S?.to?.label,receiverInfo:[S?.to?.email,S?.to?.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:"Date"}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:S?.createdAt?(0,u.WU)(S.createdAt,"dd MMM yyyy; hh:mm a"):""})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:"Amount"}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:E.formatVC(S.amount,S.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Service charge")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:E.formatVC(S.fee,S.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("User gets")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-semibold sm:text-base",children:E.formatVC(S.total,S.metaData.currency)})]})]}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Transaction ID")}),(0,a.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[S.trxId,(0,a.jsx)(o.z,{type:"button",onClick:()=>(0,d.Fp)(S.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,a.jsx)(v.Z,{size:"20"})})]})]})}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"})]})})})}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,a.jsx)(p.Z,{}),e("No data found")]})}},62797:(e,t,s)=>{"use strict";s.d(t,{z:()=>l});var a=s(60926),n=s(15185),r=s(65091),i=s(9172),o=s(90543);function l({senderName:e,senderAvatar:t,senderInfo:s,receiverName:n,receiverAvatar:i,receiverInfo:o,className:l}){return(0,a.jsxs)("div",{className:(0,r.ZP)("mb-4 flex items-start justify-around gap-1",l),children:[(0,a.jsx)(c,{name:e,avatar:t,info:s}),n&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),(0,a.jsx)(c,{name:n,avatar:i,info:o})]})]})}function c({avatar:e,name:t,info:s=[]}){let r=s.filter(Boolean);return(0,a.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,a.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,a.jsxs)(n.qE,{className:"size-10 rounded-full sm:size-14",children:[(0,a.jsx)(n.F$,{src:e,alt:t,width:56,height:56}),(0,a.jsx)(n.Q5,{className:"font-semibold",children:(0,i.v)(t)})]}),(0,a.jsx)("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:(0,a.jsx)(o.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:t}),r.length>0&&r.map((e,t)=>(0,a.jsx)("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},t))]})]})}},40098:(e,t,s)=>{"use strict";s.d(t,{default:()=>M});var a=s(60926),n=s(58387),r=s(36162),i=s(84607),o=s(86059),l=s(737),c=s(64947),d=s(29220);function m({sidebarItem:e}){let[t,s]=d.useState("(dashboard)"),[m,u]=d.useState(!1),{setIsExpanded:p,device:h}=(0,i.q)(),x=(0,c.BT)();return d.useEffect(()=>{s(x)},[]),d.useEffect(()=>{u(e.segment===x)},[x,e.segment]),(0,a.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,a.jsxs)(l.Z,{href:e.link,onClick:()=>{s(e.segment),e.children?.length||"Desktop"===h||p(!1)},"data-active":x===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[(0,a.jsx)(n.J,{condition:!!e.icon,children:(0,a.jsx)("div",{"data-active":x===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),(0,a.jsx)("span",{className:"flex-1",children:e.name}),(0,a.jsx)(n.J,{condition:!!e.children?.length,children:(0,a.jsx)(r.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:(0,a.jsx)(o.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),(0,a.jsx)(n.J,{condition:!!e.children?.length,children:(0,a.jsx)("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>a.jsx("li",{children:a.jsxs(l.Z,{href:e.link,"data-active":t===e.segment,onClick:()=>{s(e.segment),"Desktop"!==h&&p(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[a.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=s(74988),p=s(840),h=s(65091),x=s(51496),g=s(32917),f=s(65694),v=s(34870),k=s(48132),y=s(55929),b=s(41529),j=s(95334),S=s(5147),E=s(76409),N=s(24112),P=s(69628),A=s(73634),D=s(47020),w=s(28277),I=s(39228);function M(){let{t:e}=(0,I.$G)(),{isExpanded:t,setIsExpanded:s}=(0,i.q)(),{logo:n,siteName:o}=(0,p.T)(),c=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:(0,a.jsx)(x.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:(0,a.jsx)(g.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:(0,a.jsx)(f.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:(0,a.jsx)(v.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:(0,a.jsx)(k.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:(0,a.jsx)(y.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:(0,a.jsx)(b.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:(0,a.jsx)(j.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:(0,a.jsx)(S.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:(0,a.jsx)(E.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:(0,a.jsx)(N.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:(0,a.jsx)(P.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:(0,a.jsx)(A.Z,{size:"20"}),link:"/settings"}]}];return(0,a.jsxs)("div",{"data-expanded":t,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[(0,a.jsx)(r.z,{size:"icon",variant:"outline",onClick:()=>s(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${t?"":"hidden"} lg:hidden`,children:(0,a.jsx)(D.Z,{})}),(0,a.jsx)("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:(0,a.jsx)(l.Z,{href:"/",className:"flex items-center justify-center",children:(0,a.jsx)(w.Z,{src:(0,h.qR)(n),width:160,height:40,alt:o,className:"max-h-10 object-contain"})})}),(0,a.jsx)("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:c.map(e=>(0,a.jsxs)("div",{children:[""!==e.title?(0,a.jsx)("div",{children:(0,a.jsx)(u.Z,{className:"my-4"})}):null,(0,a.jsx)("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>a.jsx("li",{children:a.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},36086:(e,t,s)=>{"use strict";s.d(t,{Z:()=>x});var a=s(61394),n=s(29220),r=s(31036),i=s.n(r),o=["variant","color","size"],l=function(e){var t=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2zM18 12.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},c=function(e){var t=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M12.82 12H3.5M20.33 12h-3.48"}))},d=function(e){var t=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{fill:t,d:"M7.81 2h8.37C19.83 2 22 4.17 22 7.81v8.37c0 3.64-2.17 5.81-5.81 5.81H7.81C4.17 22 2 19.83 2 16.19V7.81C2 4.17 4.17 2 7.81 2z",opacity:".4"}),n.createElement("path",{fill:t,d:"M5.47 11.47l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06z"}))},m=function(e){var t=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M20.5 12H3.67"}))},u=function(e){var t=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{fill:t,d:"M9.57 18.82c-.19 0-.38-.07-.53-.22l-6.07-6.07a.754.754 0 010-1.06L9.04 5.4c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L4.56 12l5.54 5.54c.29.29.29.77 0 1.06-.14.15-.34.22-.53.22z"}),n.createElement("path",{fill:t,d:"M20.5 12.75H3.67c-.41 0-.75-.34-.75-.75s.34-.75.75-.75H20.5c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},p=function(e){var t=e.color;return n.createElement(n.Fragment,null,n.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07"}),n.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M20.5 12H3.67",opacity:".4"}))},h=function(e,t){switch(e){case"Bold":return n.createElement(l,{color:t});case"Broken":return n.createElement(c,{color:t});case"Bulk":return n.createElement(d,{color:t});case"Linear":default:return n.createElement(m,{color:t});case"Outline":return n.createElement(u,{color:t});case"TwoTone":return n.createElement(p,{color:t})}},x=(0,n.forwardRef)(function(e,t){var s=e.variant,r=e.color,i=e.size,l=(0,a._)(e,o);return n.createElement("svg",(0,a.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(s,r))});x.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="ArrowLeft"},3632:(e,t,s)=>{"use strict";s.d(t,{C:()=>c});var a=s(73244),n=s(73146),r=s(65091);class i{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,r.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new n.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}var o=s(14455),l=s(74190);class c{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new i(e?.user),customer:e?.user?.customer?new l.O(e?.user?.customer):null,merchant:e?.user?.merchant?new l.O(e?.user?.merchant):null,agent:e?.user?.agent?new l.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,o.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,o.WU)(this.updatedAt,e):"N/A"}}},73391:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(42416),n=s(33908);let r=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function i({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[(0,a.jsx)(r,{}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[(0,a.jsx)(n.Z,{}),(0,a.jsx)("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}s(87908)},50517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(42416),n=s(21237);function r(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(n.a,{})})}},97796:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,runtime:()=>a});let a="edge";function n({children:e}){return e}},27626:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(42416),n=s(21237);function r(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(n.a,{})})}},38384:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r,runtime:()=>n});var a=s(18264);let n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\transactions\[trxId]\page.tsx#runtime`),r=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\transactions\[trxId]\page.tsx#default`)}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,2682,7283,5089],()=>t(5649));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/transactions/[trxId]/page"]=s}]);
//# sourceMappingURL=page.js.map