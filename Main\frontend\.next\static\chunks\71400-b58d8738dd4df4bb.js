"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[71400],{8142:function(e,t,n){n.d(t,{Z:function(){return f}});var s=n(57437),a=n(52323),r=n(39785),i=n(18629),l=n(56353),o=n(15681),d=n(95186),c=n(26815),u=n(74991),h=n(3612),m=n(99376),p=n(43949);function f(e){let{form:t,defaultCountry:n,defaultPhone:f}=e,{t:x}=(0,p.$G)(),v=(0,m.useParams)(),{deviceLocation:g}=(0,h.a)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,s.jsx)(o.Wi,{control:t.control,name:"firstName",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(o.lX,{children:x("First name")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(d.I,{type:"text",placeholder:x("First name"),...t})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:t.control,name:"lastName",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(o.lX,{children:x("Last name")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(d.I,{type:"text",placeholder:x("Last name"),...t})}),(0,s.jsx)(o.zG,{})]})}})]}),(0,s.jsx)(o.Wi,{control:t.control,name:"gender",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsxs)(o.lX,{children:[" ",x("Gender")," "]}),(0,s.jsx)(o.NI,{children:(0,s.jsxs)(u.E,{defaultValue:t.value,onValueChange:t.onChange,className:"grid grid-cols-12 gap-4",children:[(0,s.jsxs)(c.Z,{"data-selected":"male"===t.value,className:"relative col-span-12 flex h-12 items-center space-x-2.5 rounded-md border-2 border-accent bg-accent px-4 py-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected sm:col-span-6",children:[(0,s.jsx)(u.m,{value:"male",className:"absolute opacity-0"}),(0,s.jsx)("span",{children:x("Male")})]}),(0,s.jsxs)(c.Z,{"data-selected":"female"===t.value,className:"relative col-span-12 flex h-12 items-center space-x-2.5 rounded-md border-2 border-accent bg-accent px-4 py-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected sm:col-span-6",children:[(0,s.jsx)(u.m,{value:"female",className:"absolute opacity-0"}),(0,s.jsx)("span",{children:x("Female")})]})]})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:t.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:x("Email")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(d.I,{type:"email",placeholder:x("Email address"),...t})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:t.control,name:"dob",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:x("Date of birth")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(r.M,{...t})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:t.control,name:"phone",render:e=>{let{field:n}=e;return(0,s.jsxs)(o.xJ,{className:"w-full",children:[(0,s.jsx)(o.lX,{children:x("Phone")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(i.E,{value:f,onChange:n.onChange,onBlur:e=>{e?t.setError("phone",{type:"custom",message:e}):t.clearErrors("phone")},options:{initialCountry:null==g?void 0:g.countryCode}})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,s.jsx)(o.Wi,{control:t.control,name:v.staffId?"newPassword":"password",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{className:"col-span-12",children:[(0,s.jsxs)(o.lX,{children:[" ",x("Password")," "]}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(l.W,{placeholder:x("Password"),...t})}),(0,s.jsx)(o.zG,{})]})}}),!v.staffId&&(0,s.jsx)(o.Wi,{control:t.control,name:"passwordConfirmation",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{className:"col-span-12",children:[(0,s.jsxs)(o.lX,{children:[" ",x("Confirm password")," "]}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(l.W,{placeholder:x("Password"),...t})}),(0,s.jsx)(o.zG,{})]})}})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,s.jsx)(o.Wi,{control:t.control,name:"addressLine",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:x("Address")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(d.I,{type:"text",placeholder:x("Address line"),...t})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:t.control,name:"countryCode",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.NI,{children:(0,s.jsx)(a.g,{defaultValue:n,onSelectChange:e=>{t.onChange(e.code.cca2)}})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,s.jsx)(o.Wi,{control:t.control,name:"city",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(o.NI,{children:(0,s.jsx)(d.I,{type:"text",placeholder:x("City"),...t})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:t.control,name:"zipCode",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(o.NI,{children:(0,s.jsx)(d.I,{type:"text",placeholder:x("Zip code"),...t})}),(0,s.jsx)(o.zG,{})]})}})]})]})]})}},80114:function(e,t,n){n.d(t,{default:function(){return l}});var s=n(57437),a=n(85487),r=n(94508),i=n(43949);function l(e){let{className:t}=e,{t:n}=(0,i.$G)();return(0,s.jsx)("div",{className:(0,r.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,s.jsx)(a.Loader,{title:n("Loading..."),className:"text-foreground"})})}},52323:function(e,t,n){n.d(t,{g:function(){return m}});var s=n(57437),a=n(2265),r=n(85487),i=n(41062),l=n(23518),o=n(57054),d=n(40593),c=n(94508),u=n(36887),h=n(43949);function m(e){var t,n;let{allCountry:m=!1,defaultValue:p,defaultCountry:f,onSelectChange:x,disabled:v=!1,triggerClassName:g,arrowClassName:y,flagClassName:j,display:b,placeholderClassName:w,align:N="start",side:C="bottom"}=e,{t:I}=(0,h.$G)(),{countries:A,getCountryByCode:S,isLoading:Z}=(0,d.F)(),[k,P]=a.useState(!1),[z,_]=a.useState(p);return a.useEffect(()=>{p&&_(p)},[p]),a.useEffect(()=>{(async()=>{f&&await S(f,e=>{e&&(_(e),x(e))})})()},[f]),(0,s.jsxs)(o.J2,{open:k,onOpenChange:P,children:[(0,s.jsxs)(o.xo,{disabled:v,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",g),children:[z?(0,s.jsx)("div",{className:"flex flex-1 items-center",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,s.jsx)(i.W,{className:j,countryCode:(null===(t=z.code)||void 0===t?void 0:t.cca2)==="*"?"UN":null===(n=z.code)||void 0===n?void 0:n.cca2}),void 0!==b?b(z):(0,s.jsx)("span",{children:z.name})]})}):(0,s.jsx)("span",{className:(0,c.ZP)("text-placeholder",w),children:I("Select country")}),(0,s.jsx)(u.Z,{className:(0,c.ZP)("size-6",y)})]}),(0,s.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:N,side:C,children:(0,s.jsxs)(l.mY,{children:[(0,s.jsx)(l.sZ,{placeholder:I("Search...")}),(0,s.jsx)(l.e8,{children:(0,s.jsxs)(l.fu,{children:[Z&&(0,s.jsx)(r.Loader,{}),m&&(0,s.jsxs)(l.di,{value:I("All countries"),onSelect:()=>{_({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),x({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),P(!1)},children:[(0,s.jsx)(i.W,{countryCode:"UN"}),(0,s.jsx)("span",{className:"pl-1.5",children:I("All countries")})]}),null==A?void 0:A.map(e=>"officially-assigned"===e.status?(0,s.jsxs)(l.di,{value:e.name,onSelect:()=>{_(e),x(e),P(!1)},children:[(0,s.jsx)(i.W,{countryCode:e.code.cca2}),(0,s.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},39785:function(e,t,n){n.d(t,{M:function(){return u}});var s=n(57437),a=n(1098),r=n(57054),i=n(94508),l=n(2901),o=n(76534),d=n(2265),c=n(43949);let u=d.forwardRef((e,t)=>{let{value:n,onChange:u,className:h,placeholderClassName:m,options:p}=e,{t:f}=(0,c.$G)(),[x,v]=d.useState(!1);return(0,s.jsxs)(r.J2,{open:x,onOpenChange:v,children:[(0,s.jsxs)(r.xo,{disabled:!!(null==p?void 0:p.disabled),className:(0,i.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",h),children:[(0,s.jsx)("div",{ref:t,className:"flex flex-1 items-center",children:(0,s.jsx)("div",{className:"flex flex-1 items-center gap-2 text-left",children:n?(0,l.WU)(n,"dd/MM/yyyy"):(0,s.jsx)("span",{className:(0,i.ZP)("text-placeholder",m),children:f("Pick a Date")})})}),(0,s.jsx)(o.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),(0,s.jsx)(r.yk,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(a.f,{...p,mode:"single",initialFocus:!0,selected:null!=n?n:void 0,onSelect:e=>{u(e),v(!1)}})})]})})},18629:function(e,t,n){n.d(t,{E:function(){return I}});var s=n(57437),a=n(85487),r=n(41062),i=n(23518),l=n(95186),o=n(57054),d=n(40593),c=n(94508),u=n(95550),h=n(36887),m=n(58414),p=n(78286),f=n(19368),x=n(68953),v=n(56555),g=n(5874),y=n(19615),j=n(93781),b=n(83057),w=n(43949),N=n(2265);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function I(e){let{value:t,defaultValue:n="",onChange:a,onBlur:r,disabled:i,inputClassName:o,options:d}=e,[u,h]=(0,N.useState)(null!=n?n:""),[j,w]=(0,N.useState)(""),[I,S]=(0,N.useState)(null==d?void 0:d.initialCountry),Z=e=>{if(e)try{let t=m.S(e,I);t?(S(t.country),w("+".concat(t.countryCallingCode)),h(t.formatNational())):h(e)}catch(t){h(e)}else h(e)};(0,N.useEffect)(()=>{t&&Z(t)},[t]);let k=p.L(I||(null==d?void 0:d.initialCountry)||"US",b.Z);return(0,s.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(A,{country:I,disabled:i,initialCountry:null==d?void 0:d.initialCountry,onSelect:e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase(),s=f.G(n);w("+".concat(s)),S(n)}}),(0,s.jsx)("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:j||"+".concat(null==k?void 0:k.countryCallingCode)})]}),(0,s.jsx)(l.I,{type:"tel",className:(0,c.ZP)("rounded-l-none pl-2",o),value:u,onChange:e=>{let{value:t}=e.target,n=m.S(t,I);null==r||r(""),n&&x.t(t,I)&&v.q(t,I)?(S(n.country),w("+".concat(n.countryCallingCode)),null==a||a(n.number),h(t)):(n?h(n.nationalNumber):h(t),null==a||a(t))},onPaste:e=>{let t=e.clipboardData.getData("Text"),n=m.S(t);if(n&&x.t(t))Z(n.formatNational()),S(n.country),w("+".concat(n.countryCallingCode)),null==a||a(n.number),null==r||r("");else{let e=m.S(t,I);e&&x.t(t,I)&&(Z(e.formatNational()),null==a||a(e.number),null==r||r(""))}},onBlur:()=>{if(u&&!g.y(u,I)){let e=y.d(u,I);e&&(null==r||r(C[e]))}},placeholder:null==k?void 0:k.formatNational(),disabled:i})]})}function A(e){let{initialCountry:t,country:n,onSelect:a,disabled:i}=e,[l,d]=(0,N.useState)(!1);return(0,s.jsxs)(o.J2,{open:l,onOpenChange:d,children:[(0,s.jsxs)(o.xo,{disabled:i,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[t||n?(0,s.jsx)(r.W,{countryCode:n||t,className:"aspect-auto h-[18px] w-7 flex-1"}):(0,s.jsx)(u.Z,{}),(0,s.jsx)(h.Z,{variant:"Bold",size:16})]}),(0,s.jsx)(o.yk,{align:"start",className:"h-fit p-0",children:(0,s.jsx)(S,{defaultValue:n||t,onSelect:e=>{a(e),d(!1)}})})]})}function S(e){var t;let{defaultValue:n,onSelect:l}=e,{countries:o,isLoading:c}=(0,d.F)(),{t:u}=(0,w.$G)();return(0,s.jsxs)(i.mY,{children:[(0,s.jsx)(i.sZ,{placeholder:u("Search country by name"),className:"placeholder:text-input-placeholder"}),(0,s.jsx)(i.e8,{children:(0,s.jsx)(i.fu,{children:c?(0,s.jsx)(i.di,{children:(0,s.jsx)(a.Loader,{})}):null===(t=o.filter(e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase();return j.o().includes(n)}))||void 0===t?void 0:t.map(e=>(0,s.jsxs)(i.di,{value:e.name,"data-active":e.code.cca2===n,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>l(e),children:[(0,s.jsx)(r.W,{countryCode:e.code.cca2}),e.name]},e.code.ccn3))})})]})}},56353:function(e,t,n){n.d(t,{W:function(){return c}});var s=n(57437),a=n(2265),r=n(62869),i=n(95186),l=n(94508),o=n(93824),d=n(32706);let c=a.forwardRef((e,t)=>{let{className:n,type:c,...u}=e,[h,m]=a.useState(!1);return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.I,{type:h?"text":"password",className:(0,l.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),ref:t,...u}),(0,s.jsx)(r.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),m(e=>!e)},children:h?(0,s.jsx)(o.Z,{}):(0,s.jsx)(d.Z,{})})]})});c.displayName="PasswordInput"},1098:function(e,t,n){n.d(t,{f:function(){return c}});var s=n(57437),a=n(92451),r=n(10407),i=n(40875);n(2265);var l=n(90827),o=n(62869),d=n(94508);function c(e){let{className:t,classNames:n,showOutsideDays:c=!0,...u}=e;return(0,s.jsx)(l._W,{showOutsideDays:c,className:(0,d.ZP)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,d.ZP)((0,o.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.ZP)((0,o.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...n},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:e=>{let{...t}=e;return(0,s.jsx)(a.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...t}=e;return(0,s.jsx)(r.Z,{className:"h-4 w-4"})},Dropdown:e=>{let{...t}=e;return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("select",{...t,style:{opacity:0,position:"absolute"}}),(0,s.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm",children:t.caption}),(0,s.jsx)(i.Z,{className:"size-3"})]})]})}},...u})}c.displayName="Calendar"},15681:function(e,t,n){n.d(t,{NI:function(){return x},Wi:function(){return u},l0:function(){return d},lX:function(){return f},xJ:function(){return p},zG:function(){return v}});var s=n(57437),a=n(37053),r=n(2265),i=n(29501),l=n(26815),o=n(94508);let d=i.RV,c=r.createContext({}),u=e=>{let{...t}=e;return(0,s.jsx)(c.Provider,{value:{name:t.name},children:(0,s.jsx)(i.Qr,{...t})})},h=()=>{let e=r.useContext(c),t=r.useContext(m),{getFieldState:n,formState:s}=(0,i.Gc)(),a=n(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...a}},m=r.createContext({}),p=r.forwardRef((e,t)=>{let{className:n,...a}=e,i=r.useId();return(0,s.jsx)(m.Provider,{value:{id:i},children:(0,s.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",n),...a})})});p.displayName="FormItem";let f=r.forwardRef((e,t)=>{let{className:n,required:a,...r}=e,{error:i,formItemId:d}=h();return(0,s.jsx)("span",{children:(0,s.jsx)(l.Z,{ref:t,className:(0,o.ZP)(i&&"text-base font-medium text-destructive",n),htmlFor:d,...r})})});f.displayName="FormLabel";let x=r.forwardRef((e,t)=>{let{...n}=e,{error:r,formItemId:i,formDescriptionId:l,formMessageId:o}=h();return(0,s.jsx)(a.g7,{ref:t,id:i,"aria-describedby":r?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!r,...n})});x.displayName="FormControl",r.forwardRef((e,t)=>{let{className:n,...a}=e,{formDescriptionId:r}=h();return(0,s.jsx)("p",{ref:t,id:r,className:(0,o.ZP)("text-sm text-muted-foreground",n),...a})}).displayName="FormDescription";let v=r.forwardRef((e,t)=>{let{className:n,children:a,...r}=e,{error:i,formMessageId:l}=h(),d=i?String(null==i?void 0:i.message):a;return d?(0,s.jsx)("p",{ref:t,id:l,className:(0,o.ZP)("text-sm font-medium text-destructive",n),...r,children:d}):null});v.displayName="FormMessage"},57054:function(e,t,n){n.d(t,{J2:function(){return l},xo:function(){return o},yk:function(){return d}});var s=n(57437),a=n(2265),r=n(27312),i=n(94508);let l=r.fC,o=r.xz,d=a.forwardRef((e,t)=>{let{className:n,align:a="center",sideOffset:l=4,...o}=e;return(0,s.jsx)(r.h_,{children:(0,s.jsx)(r.VY,{ref:t,align:a,sideOffset:l,className:(0,i.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...o})})});d.displayName=r.VY.displayName},74991:function(e,t,n){n.d(t,{E:function(){return o},m:function(){return d}});var s=n(57437),a=n(2265),r=n(42325),i=n(40519),l=n(94508);let o=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,s.jsx)(r.fC,{className:(0,l.ZP)("grid gap-2",n),...a,ref:t})});o.displayName=r.fC.displayName;let d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,s.jsx)(r.ck,{ref:t,className:(0,l.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),...a,children:(0,s.jsx)(r.z$,{className:"flex items-center justify-center",children:(0,s.jsx)(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=r.ck.displayName},17062:function(e,t,n){n.d(t,{Z:function(){return f},O:function(){return p}});var s=n(57437),a=n(80114);n(83079);var r=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),i=n(31117),l=n(79981),o=n(78040),d=n(83130);class c{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var u=n(99376),h=n(2265);let m=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),p=()=>h.useContext(m);function f(e){let{children:t}=e,[n,p]=h.useState("Desktop"),[f,x]=h.useState(!1),[v,g]=h.useState(),{data:y,isLoading:j,error:b,mutate:w}=(0,i.d)("/auth/check",{revalidateOnFocus:!1}),{data:N,isLoading:C}=(0,i.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:I,isLoading:A}=(0,i.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),S=(0,u.useRouter)(),Z=(0,u.usePathname)();h.useEffect(()=>{(async()=>{p((await r()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;p(e<768?"Mobile":e<1024?"Tablet":"Desktop"),x(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await l.Z.post("/auth/geo-location");g(new c(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{b&&!o.sp.includes(Z)&&S.push("/signin")},[b]);let k=h.useMemo(()=>{var e,t,s;return{isAuthenticate:!!(null==y?void 0:null===(e=y.data)||void 0===e?void 0:e.login),auth:(null==y?void 0:null===(t=y.data)||void 0===t?void 0:t.user)?new d.n(null==y?void 0:null===(s=y.data)||void 0===s?void 0:s.user):null,isLoading:j,deviceLocation:v,refreshAuth:()=>w(y),isExpanded:f,device:n,setIsExpanded:x,branding:null==N?void 0:N.data,googleAnalytics:(null==I?void 0:I.data)?{active:null==I?void 0:I.data.active,apiKey:null==I?void 0:I.data.apiKey}:{active:!1,apiKey:""}}},[y,v,f,n]),P=!j&&!C&&!A;return(0,s.jsx)(m.Provider,{value:k,children:P?t:(0,s.jsx)(a.default,{})})}},97751:function(e,t,n){n.d(t,{B:function(){return a},D:function(){return r}});var s=n(43577);function a(e){var t,n,s;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(s=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==s?s:"",data:null===(n=e.data)||void 0===n?void 0:n.data}}function r(e){let t=500,n="Internal Server Error",a="An unknown error occurred";if((0,s.IZ)(e)){var r,i,l,o,d,c,u,h,m,p,f,x;t=null!==(m=null===(r=e.response)||void 0===r?void 0:r.status)&&void 0!==m?m:500,n=null!==(p=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==p?p:"Internal Server Error",a=null!==(x=null!==(f=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(l=o[0])||void 0===l?void 0:l.message)&&void 0!==f?f:null===(h=e.response)||void 0===h?void 0:null===(u=h.data)||void 0===u?void 0:u.message)&&void 0!==x?x:e.message}else e instanceof Error&&(a=e.message);return{statusCode:t,statusText:n,status:!1,message:a,data:void 0,error:e}}},3612:function(e,t,n){n.d(t,{a:function(){return a}});var s=n(17062);let a=()=>{let e=(0,s.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},31117:function(e,t,n){n.d(t,{d:function(){return r}});var s=n(79981),a=n(85323);let r=(e,t)=>(0,a.ZP)(e||null,e=>s.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},74539:function(e,t,n){n.d(t,{k:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){n.d(t,{n:function(){return o}});class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=n(84937);class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var i=n(66419),l=n(78040);class o{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(l.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new i.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new a.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new r(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new s(e.agent):void 0}}},84937:function(e,t,n){n.d(t,{O:function(){return a}});var s=n(74539);class a{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new s.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){n.d(t,{u:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}}]);