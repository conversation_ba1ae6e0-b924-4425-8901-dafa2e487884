(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1225],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},36275:(e,s,t)=>{"use strict";t.r(s),t.d(s,{ComponentMod:()=>D,default:()=>I});var a,i={};t.r(i),t.d(i,{AppRouter:()=>h.WY,ClientPageRoot:()=>h.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>h.yO,NotFoundBoundary:()=>h.O4,Postpone:()=>h.hQ,RenderFromTemplateContext:()=>h.b5,__next_app__:()=>g,actionAsyncStorage:()=>h.Wz,createDynamicallyTrackedSearchParams:()=>h.rL,createUntrackedSearchParams:()=>h.S5,decodeAction:()=>h.Hs,decodeFormState:()=>h.dH,decodeReply:()=>h.kf,originalPathname:()=>x,pages:()=>u,patchFetch:()=>h.XH,preconnect:()=>h.$P,preloadFont:()=>h.C5,preloadStyle:()=>h.oH,renderToReadableStream:()=>h.aW,requestAsyncStorage:()=>h.Fg,routeModule:()=>f,serverHooks:()=>h.GP,staticGenerationAsyncStorage:()=>h.AT,taintObjectReference:()=>h.nr,tree:()=>p}),t(67206);var n=t(79319),r=t(20518),d=t(61902),l=t(62042),o=t(44630),c=t(44828),m=t(65505),h=t(13839);let p=["",{children:["(protected)",{admin:["children",{children:["withdraws",{children:["[withdrawId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,24444)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\withdraws\\[withdrawId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,52384)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\withdraws\\[withdrawId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,63674)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\withdraws\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],u=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\withdraws\\[withdrawId]\\page.tsx"],x="/(protected)/@admin/withdraws/[withdrawId]/page",g={require:t,loadChunk:()=>Promise.resolve()},f=new o.AppPageRouteModule({definition:{kind:c.x.APP_PAGE,page:"/(protected)/@admin/withdraws/[withdrawId]/page",pathname:"/withdraws/[withdrawId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=t(69094),y=t(5787),b=t(90527);let j=e=>e?JSON.parse(e):void 0,w=self.__BUILD_MANIFEST,k=j(self.__REACT_LOADABLE_MANIFEST),N=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/withdraws/[withdrawId]/page"],S=j(self.__RSC_SERVER_MANIFEST),A=j(self.__NEXT_FONT_MANIFEST),E=j(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];N&&S&&(0,y.Mo)({clientReferenceManifest:N,serverActionsManifest:S,serverModuleMap:(0,b.w)({serverActionsManifest:S,pageName:"/(protected)/@admin/withdraws/[withdrawId]/page"})});let P=(0,r.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@admin/withdraws/[withdrawId]/page",appMod:null,pageMod:i,errorMod:null,error500Mod:null,Document:null,buildManifest:w,renderToHTML:l.f,reactLoadableManifest:k,clientReferenceManifest:N,serverActionsManifest:S,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:A,incrementalCacheHandler:null,interceptionRouteRewrites:E}),D=i;function I(e){return(0,n.C)({...e,IncrementalCache:d.k,handler:P})}},66505:(e,s,t)=>{Promise.resolve().then(t.bind(t,67522))},26807:(e,s,t)=>{Promise.resolve().then(t.bind(t,44450)),Promise.resolve().then(t.bind(t,40098))},67522:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S,runtime:()=>N});var a=t(60926),i=t(58387),n=t(29411),r=t(62797),d=t(36162),l=t(74988),o=t(1181),c=t(25694);async function m(e,s){try{let t=await o.Z.put(`/admin/withdraws/${s}/${e}`,{id:e});return(0,c.B)(t)}catch(e){return(0,c.D)(e)}}var h=t(43291),p=t(65091),u=t(3632),x=t(14455),g=t(37988),f=t(90543),v=t(51018),y=t(30684),b=t(31949),j=t(64947),w=t(39228),k=t(32167);let N="edge";function S(){let{t:e}=(0,w.$G)(),s=(0,j.UO)(),{data:t,isLoading:o,mutate:c}=(0,h.d)(`/admin/withdraws/${s.withdrawId}`);if(o)return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(n.Loader,{})});let N=t?.data?new u.C(t?.data):null,S=new p.F;if(!N)return(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,a.jsx)(g.Z,{}),e("No data found")]});let A=N?.metaData?.params?Object.entries(N.metaData?.params).filter(([e])=>"trxSecret"!==e).map(([e,s])=>({key:e,value:s})):[],E=e=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());return(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,a.jsxs)("div",{className:"col-span-12 lg:col-span-7",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,a.jsx)(i.J,{condition:"completed"===N.status,children:(0,a.jsx)(f.Z,{variant:"Bulk",size:32,className:"text-success"})}),(0,a.jsx)(i.J,{condition:"failed"===N.status,children:(0,a.jsx)(v.Z,{variant:"Bulk",size:32,className:"text-destructive"})}),(0,a.jsx)(i.J,{condition:"pending"===N.status,children:(0,a.jsx)(y.Z,{variant:"Bulk",size:32,className:"text-primary"})}),(0,a.jsxs)("h2",{className:"font-semibold",children:[e("Withdraw")," #",s.withdrawId]})]}),(0,a.jsx)(r.z,{senderAvatar:(0,p.qR)(N.from.image),senderName:N.from.label,senderInfo:[N.from?.email,N?.from?.phone],receiverAvatar:(0,p.qR)(N?.to?.image),receiverName:N?.to?.label,receiverInfo:[N?.to?.email,N?.to?.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Date")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:N?.createdAt?(0,x.WU)(N.createdAt,"dd MMM yyyy; hh:mm a"):""})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Amount")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:S.formatVC(N.amount,N.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Service charge")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:S.formatVC(N.fee,N.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("User gets")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-semibold sm:text-base",children:S.formatVC(N.total,N.metaData.currency)})]})]}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Transaction ID")}),(0,a.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[N.trxId,(0,a.jsx)(d.z,{type:"button",onClick:()=>(0,p.Fp)(N.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,a.jsx)(b.Z,{size:"20"})})]})]})}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4 rounded-xl bg-card px-4 py-6",children:[(0,a.jsx)("h4",{children:e("Withdraw request")}),(0,a.jsx)(i.J,{condition:N?.status==="completed",children:(0,a.jsx)("p",{children:e("Withdraw approved")})}),(0,a.jsx)(i.J,{condition:N?.status==="failed",children:(0,a.jsx)("p",{children:e("Withdraw failed")})}),(0,a.jsx)(i.J,{condition:N?.status==="pending",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,a.jsxs)(d.z,{type:"button",className:"bg-[#0B6A0B] text-white hover:bg-[#149014]",onClick:()=>{k.toast.promise(m(t?.data?.id,"accept"),{loading:e("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return c(t),e.message},error:e=>e.message})},children:[(0,a.jsx)(f.Z,{}),e("Accept withdraw")]}),(0,a.jsxs)(d.z,{type:"button",className:"bg-[#D13438] text-white hover:bg-[#b42328]",onClick:()=>{k.toast.promise(m(t?.data?.id,"decline"),{loading:e("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return c(t),e.message},error:e=>e.message})},children:[(0,a.jsx)(v.Z,{}),e("Reject withdraw")]})]})})]})]}),(0,a.jsxs)("div",{className:"col-span-12 lg:col-span-5",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("h2",{children:e("Method info")})}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Method used")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:N?.method})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Currency")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:N?.metaData?.currency??"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("h2",{children:e("Additional info")})}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsx)("div",{className:"flex flex-col",children:A.map((e,s)=>(0,a.jsxs)("div",{className:`grid grid-cols-12 px-6 py-3 ${s%2==0?"bg-accent":""}`,children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:E(e.key)}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:e.value})]},e.key))})]})]})]})})}},62797:(e,s,t)=>{"use strict";t.d(s,{z:()=>l});var a=t(60926),i=t(15185),n=t(65091),r=t(9172),d=t(90543);function l({senderName:e,senderAvatar:s,senderInfo:t,receiverName:i,receiverAvatar:r,receiverInfo:d,className:l}){return(0,a.jsxs)("div",{className:(0,n.ZP)("mb-4 flex items-start justify-around gap-1",l),children:[(0,a.jsx)(o,{name:e,avatar:s,info:t}),i&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),(0,a.jsx)(o,{name:i,avatar:r,info:d})]})]})}function o({avatar:e,name:s,info:t=[]}){let n=t.filter(Boolean);return(0,a.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,a.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,a.jsxs)(i.qE,{className:"size-10 rounded-full sm:size-14",children:[(0,a.jsx)(i.F$,{src:e,alt:s,width:56,height:56}),(0,a.jsx)(i.Q5,{className:"font-semibold",children:(0,r.v)(s)})]}),(0,a.jsx)("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:(0,a.jsx)(d.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:s}),n.length>0&&n.map((e,s)=>(0,a.jsx)("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},s))]})]})}},40098:(e,s,t)=>{"use strict";t.d(s,{default:()=>C});var a=t(60926),i=t(58387),n=t(36162),r=t(84607),d=t(86059),l=t(737),o=t(64947),c=t(29220);function m({sidebarItem:e}){let[s,t]=c.useState("(dashboard)"),[m,h]=c.useState(!1),{setIsExpanded:p,device:u}=(0,r.q)(),x=(0,o.BT)();return c.useEffect(()=>{t(x)},[]),c.useEffect(()=>{h(e.segment===x)},[x,e.segment]),(0,a.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,a.jsxs)(l.Z,{href:e.link,onClick:()=>{t(e.segment),e.children?.length||"Desktop"===u||p(!1)},"data-active":x===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[(0,a.jsx)(i.J,{condition:!!e.icon,children:(0,a.jsx)("div",{"data-active":x===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),(0,a.jsx)("span",{className:"flex-1",children:e.name}),(0,a.jsx)(i.J,{condition:!!e.children?.length,children:(0,a.jsx)(n.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),h(!m)},children:(0,a.jsx)(d.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),(0,a.jsx)(i.J,{condition:!!e.children?.length,children:(0,a.jsx)("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>a.jsx("li",{children:a.jsxs(l.Z,{href:e.link,"data-active":s===e.segment,onClick:()=>{t(e.segment),"Desktop"!==u&&p(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[a.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var h=t(74988),p=t(840),u=t(65091),x=t(51496),g=t(32917),f=t(65694),v=t(34870),y=t(48132),b=t(55929),j=t(41529),w=t(95334),k=t(5147),N=t(76409),S=t(24112),A=t(69628),E=t(73634),P=t(47020),D=t(28277),I=t(39228);function C(){let{t:e}=(0,I.$G)(),{isExpanded:s,setIsExpanded:t}=(0,r.q)(),{logo:i,siteName:d}=(0,p.T)(),o=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:(0,a.jsx)(x.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:(0,a.jsx)(g.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:(0,a.jsx)(f.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:(0,a.jsx)(v.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:(0,a.jsx)(y.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:(0,a.jsx)(b.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:(0,a.jsx)(j.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:(0,a.jsx)(w.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:(0,a.jsx)(k.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:(0,a.jsx)(N.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:(0,a.jsx)(S.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:(0,a.jsx)(A.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:(0,a.jsx)(E.Z,{size:"20"}),link:"/settings"}]}];return(0,a.jsxs)("div",{"data-expanded":s,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[(0,a.jsx)(n.z,{size:"icon",variant:"outline",onClick:()=>t(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${s?"":"hidden"} lg:hidden`,children:(0,a.jsx)(P.Z,{})}),(0,a.jsx)("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:(0,a.jsx)(l.Z,{href:"/",className:"flex items-center justify-center",children:(0,a.jsx)(D.Z,{src:(0,u.qR)(i),width:160,height:40,alt:d,className:"max-h-10 object-contain"})})}),(0,a.jsx)("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:o.map(e=>(0,a.jsxs)("div",{children:[""!==e.title?(0,a.jsx)("div",{children:(0,a.jsx)(h.Z,{className:"my-4"})}):null,(0,a.jsx)("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>a.jsx("li",{children:a.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},3632:(e,s,t)=>{"use strict";t.d(s,{C:()=>o});var a=t(73244),i=t(73146),n=t(65091);class r{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,n.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new i.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}var d=t(14455),l=t(74190);class o{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new r(e?.user),customer:e?.user?.customer?new l.O(e?.user?.customer):null,merchant:e?.user?.merchant?new l.O(e?.user?.merchant):null,agent:e?.user?.agent?new l.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,d.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,d.WU)(this.updatedAt,e):"N/A"}}},73391:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(42416),i=t(33908);let n=(0,t(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function r({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[(0,a.jsx)(n,{}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[(0,a.jsx)(i.Z,{}),(0,a.jsx)("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(87908)},50517:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(42416),i=t(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(i.a,{})})}},52384:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(42416),i=t(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(i.a,{})})}},24444:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,runtime:()=>i});var a=t(18264);let i=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\withdraws\[withdrawId]\page.tsx#runtime`),n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\withdraws\[withdrawId]\page.tsx#default`)},63674:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(42416),i=t(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(i.a,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[529,6578,3390,2682,7283,5089],()=>s(36275));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/withdraws/[withdrawId]/page"]=t}]);
//# sourceMappingURL=page.js.map