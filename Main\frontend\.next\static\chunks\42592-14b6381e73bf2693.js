"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[42592],{73247:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},32489:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},46343:function(e,t,r){r.d(t,{mY:function(){return D}});var n=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,a=/[\s-]/,u=/[\s-]/g;function i(e){return e.toLowerCase().replace(u," ")}var c=r(49027),o=r(2265),d=r(66840),s=r(99255),f=r(98575),v='[cmdk-group=""]',m='[cmdk-group-items=""]',p='[cmdk-item=""]',h="".concat(p,':not([aria-disabled="true"])'),g="cmdk-item-select",b="data-value",E=(e,t,r)=>{var c;return c=e,function e(t,r,i,c,o,d,s){if(d===r.length)return o===t.length?1:.99;var f=`${o},${d}`;if(void 0!==s[f])return s[f];for(var v,m,p,h,g=c.charAt(d),b=i.indexOf(g,o),E=0;b>=0;)(v=e(t,r,i,c,b+1,d+1,s))>E&&(b===o?v*=1:n.test(t.charAt(b-1))?(v*=.8,(p=t.slice(o,b-1).match(l))&&o>0&&(v*=Math.pow(.999,p.length))):a.test(t.charAt(b-1))?(v*=.9,(h=t.slice(o,b-1).match(u))&&o>0&&(v*=Math.pow(.999,h.length))):(v*=.17,o>0&&(v*=Math.pow(.999,b-o))),t.charAt(b)!==r.charAt(d)&&(v*=.9999)),(v<.1&&i.charAt(b-1)===c.charAt(d+1)||c.charAt(d+1)===c.charAt(d)&&i.charAt(b-1)!==c.charAt(d))&&.1*(m=e(t,r,i,c,b+1,d+2,s))>v&&(v=.1*m),v>E&&(E=v),b=i.indexOf(g,b+1);return s[f]=E,E}(c=r&&r.length>0?`${c+" "+r.join(" ")}`:c,t,i(c),i(t),0,0,{})},k=o.createContext(void 0),w=()=>o.useContext(k),y=o.createContext(void 0),S=()=>o.useContext(y),C=o.createContext(void 0),I=o.forwardRef((e,t)=>{let r=q(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=q(()=>new Set),l=q(()=>new Map),a=q(()=>new Map),u=q(()=>new Set),i=P(e),{label:c,children:f,value:w,onValueChange:S,filter:C,shouldFilter:I,loop:A,disablePointerSelection:x=!1,vimBindings:M=!0,...R}=e,V=(0,s.M)(),F=(0,s.M)(),D=(0,s.M)(),K=o.useRef(null),L=O();W(()=>{if(void 0!==w){let e=w.trim();r.current.value=e,N.emit()}},[w]),W(()=>{L(6,U)},[]);let N=o.useMemo(()=>({subscribe:e=>(u.current.add(e),()=>u.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var l,a,u,c;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)H(),B(),L(1,$);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(D);e?e.focus():null==(l=document.getElementById(V))||l.focus()}if(L(7,()=>{var e;r.current.selectedItemId=null==(e=Y())?void 0:e.id,N.emit()}),n||L(5,U),(null==(a=i.current)?void 0:a.value)!==void 0){null==(c=(u=i.current).onValueChange)||c.call(u,null!=t?t:"");return}}N.emit()}},emit:()=>{u.current.forEach(e=>e())}}),[]),Z=o.useMemo(()=>({value:(e,t,n)=>{var l;t!==(null==(l=a.current.get(e))?void 0:l.value)&&(a.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,z(t,n)),L(2,()=>{B(),N.emit()}))},item:(e,t)=>(n.current.add(e),t&&(l.current.has(t)?l.current.get(t).add(e):l.current.set(t,new Set([e]))),L(3,()=>{H(),B(),r.current.value||$(),N.emit()}),()=>{a.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=Y();L(4,()=>{H(),(null==t?void 0:t.getAttribute("id"))===e&&$(),N.emit()})}),group:e=>(l.current.has(e)||l.current.set(e,new Set),()=>{a.current.delete(e),l.current.delete(e)}),filter:()=>i.current.shouldFilter,label:c||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:V,inputId:D,labelId:F,listInnerRef:K}),[]);function z(e,t){var n,l;let a=null!=(l=null==(n=i.current)?void 0:n.filter)?l:E;return e?a(e,r.current.search,t):0}function B(){if(!r.current.search||!1===i.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=l.current.get(r),a=0;n.forEach(t=>{a=Math.max(e.get(t),a)}),t.push([r,a])});let n=K.current;G().sort((t,r)=>{var n,l;let a=t.getAttribute("id"),u=r.getAttribute("id");return(null!=(n=e.get(u))?n:0)-(null!=(l=e.get(a))?l:0)}).forEach(e=>{let t=e.closest(m);t?t.appendChild(e.parentElement===t?e:e.closest("".concat(m," > *"))):n.appendChild(e.parentElement===n?e:e.closest("".concat(m," > *")))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=K.current)?void 0:t.querySelector("".concat(v,"[").concat(b,'="').concat(encodeURIComponent(e[0]),'"]'));null==r||r.parentElement.appendChild(r)})}function $(){let e=G().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(b);N.setState("value",t||void 0)}function H(){var e,t,u,c;if(!r.current.search||!1===i.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let o=0;for(let l of n.current){let n=z(null!=(t=null==(e=a.current.get(l))?void 0:e.value)?t:"",null!=(c=null==(u=a.current.get(l))?void 0:u.keywords)?c:[]);r.current.filtered.items.set(l,n),n>0&&o++}for(let[e,t]of l.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=o}function U(){var e,t,r;let n=Y();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(v))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function Y(){var e;return null==(e=K.current)?void 0:e.querySelector("".concat(p,'[aria-selected="true"]'))}function G(){var e;return Array.from((null==(e=K.current)?void 0:e.querySelectorAll(h))||[])}function X(e){let t=G()[e];t&&N.setState("value",t.getAttribute(b))}function J(e){var t;let r=Y(),n=G(),l=n.findIndex(e=>e===r),a=n[l+e];null!=(t=i.current)&&t.loop&&(a=l+e<0?n[n.length-1]:l+e===n.length?n[0]:n[l+e]),a&&N.setState("value",a.getAttribute(b))}function Q(e){let t=Y(),r=null==t?void 0:t.closest(v),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,v):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,v))?void 0:r.querySelector(h);n?N.setState("value",n.getAttribute(b)):J(e)}let T=()=>X(G().length-1),ee=e=>{e.preventDefault(),e.metaKey?T():e.altKey?Q(1):J(1)},et=e=>{e.preventDefault(),e.metaKey?X(0):e.altKey?Q(-1):J(-1)};return o.createElement(d.WV.div,{ref:t,tabIndex:-1,...R,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=R.onKeyDown)||t.call(R,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":M&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":M&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),X(0);break;case"End":e.preventDefault(),T();break;case"Enter":{e.preventDefault();let t=Y();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},o.createElement("label",{"cmdk-label":"",htmlFor:Z.inputId,id:Z.labelId,style:j},c),_(e,e=>o.createElement(y.Provider,{value:N},o.createElement(k.Provider,{value:Z},e))))}),A=o.forwardRef((e,t)=>{var r,n;let l=(0,s.M)(),a=o.useRef(null),u=o.useContext(C),i=w(),c=P(e),v=null!=(n=null==(r=c.current)?void 0:r.forceMount)?n:null==u?void 0:u.forceMount;W(()=>{if(!v)return i.item(l,null==u?void 0:u.id)},[v]);let m=L(l,a,[e.value,e.children,a],e.keywords),p=S(),h=K(e=>e.value&&e.value===m.current),b=K(e=>!!v||!1===i.filter()||!e.search||e.filtered.items.get(l)>0);function E(){var e,t;k(),null==(t=(e=c.current).onSelect)||t.call(e,m.current)}function k(){p.setState("value",m.current,!0)}if(o.useEffect(()=>{let t=a.current;if(!(!t||e.disabled))return t.addEventListener(g,E),()=>t.removeEventListener(g,E)},[b,e.onSelect,e.disabled]),!b)return null;let{disabled:y,value:I,onSelect:A,forceMount:x,keywords:M,...R}=e;return o.createElement(d.WV.div,{ref:(0,f.F)(a,t),...R,id:l,"cmdk-item":"",role:"option","aria-disabled":!!y,"aria-selected":!!h,"data-disabled":!!y,"data-selected":!!h,onPointerMove:y||i.getDisablePointerSelection()?void 0:k,onClick:y?void 0:E},e.children)}),x=o.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:l,...a}=e,u=(0,s.M)(),i=o.useRef(null),c=o.useRef(null),v=(0,s.M)(),m=w(),p=K(e=>!!l||!1===m.filter()||!e.search||e.filtered.groups.has(u));W(()=>m.group(u),[]),L(u,i,[e.value,e.heading,c]);let h=o.useMemo(()=>({id:u,forceMount:l}),[l]);return o.createElement(d.WV.div,{ref:(0,f.F)(i,t),...a,"cmdk-group":"",role:"presentation",hidden:!p||void 0},r&&o.createElement("div",{ref:c,"cmdk-group-heading":"","aria-hidden":!0,id:v},r),_(e,e=>o.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?v:void 0},o.createElement(C.Provider,{value:h},e))))}),M=o.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,l=o.useRef(null),a=K(e=>!e.search);return r||a?o.createElement(d.WV.div,{ref:(0,f.F)(l,t),...n,"cmdk-separator":"",role:"separator"}):null}),R=o.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,l=null!=e.value,a=S(),u=K(e=>e.search),i=K(e=>e.selectedItemId),c=w();return o.useEffect(()=>{null!=e.value&&a.setState("search",e.value)},[e.value]),o.createElement(d.WV.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.listId,"aria-labelledby":c.labelId,"aria-activedescendant":i,id:c.inputId,type:"text",value:l?e.value:u,onChange:e=>{l||a.setState("search",e.target.value),null==r||r(e.target.value)}})}),V=o.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...l}=e,a=o.useRef(null),u=o.useRef(null),i=K(e=>e.selectedItemId),c=w();return o.useEffect(()=>{if(u.current&&a.current){let e=u.current,t=a.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),o.createElement(d.WV.div,{ref:(0,f.F)(a,t),...l,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":i,"aria-label":n,id:c.listId},_(e,e=>o.createElement("div",{ref:(0,f.F)(u,c.listInnerRef),"cmdk-list-sizer":""},e)))}),F=o.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:l,contentClassName:a,container:u,...i}=e;return o.createElement(c.fC,{open:r,onOpenChange:n},o.createElement(c.h_,{container:u},o.createElement(c.aV,{"cmdk-overlay":"",className:l}),o.createElement(c.VY,{"aria-label":e.label,"cmdk-dialog":"",className:a},o.createElement(I,{ref:t,...i}))))}),D=Object.assign(I,{List:V,Item:A,Input:R,Group:x,Separator:M,Dialog:F,Empty:o.forwardRef((e,t)=>K(e=>0===e.filtered.count)?o.createElement(d.WV.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:o.forwardRef((e,t)=>{let{progress:r,children:n,label:l="Loading...",...a}=e;return o.createElement(d.WV.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":l},_(e,e=>o.createElement("div",{"aria-hidden":!0},e)))})});function P(e){let t=o.useRef(e);return W(()=>{t.current=e}),t}var W="undefined"==typeof window?o.useEffect:o.useLayoutEffect;function q(e){let t=o.useRef();return void 0===t.current&&(t.current=e()),t}function K(e){let t=S(),r=()=>e(t.snapshot());return o.useSyncExternalStore(t.subscribe,r,r)}function L(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],l=o.useRef(),a=w();return W(()=>{var u;let i=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():l.current}})(),c=n.map(e=>e.trim());a.value(e,i,c),null==(u=t.current)||u.setAttribute(b,i),l.current=i}),l}var O=()=>{let[e,t]=o.useState(),r=q(()=>new Map);return W(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function _(e,t){let r,{asChild:n,children:l}=e;return n&&o.isValidElement(l)?o.cloneElement("function"==typeof(r=l.type)?r(l.props):"render"in r?r.render(l.props):l,{ref:l.ref},t(l.props.children)):t(l)}var j={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}}}]);