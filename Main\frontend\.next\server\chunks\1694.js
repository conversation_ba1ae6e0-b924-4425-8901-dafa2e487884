exports.id=1694,exports.ids=[1694],exports.modules={7193:(e,t,r)=>{Promise.resolve().then(r.bind(r,96072))},43245:(e,t,r)=>{Promise.resolve().then(r.bind(r,96072))},96072:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var a=r(10326),l=r(56140),n=r(28758),s=r(567),o=r(77863);class c{constructor(e){this.id=e?.id,this.userId=e?.userId,this.type=e?.type,this.info=e?.info?JSON.parse(e.info):null,this.value=e?.value,this.metadata=e?.metaData?JSON.parse(e.metaData):null,this.isBookmarked=!!e?.isBookmarked,this.createdAt=e?.createdAt?new Date(e.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e.updatedAt):null}getInfo(){return{...this.info,image:this.info?.image,label:"wallet"===this.type?this.metadata?.currency:this.info?.label}}}var i=r(54033),d=r(64561),u=r(17577),m=r.n(u),h=r(70012),f=r(90772),p=r(49547),g=r(10734);async function x(e){try{let t=await p.Z.put(`/saves/toggle-bookmark/${e}`,{id:e});return(0,g.B)(t)}catch(e){return(0,g.D)(e)}}var v=r(72871),j=r(35047),b=r(85999),N=r(7291);function w({row:e}){let{mutate:t}=(0,N.kY)(),r=(0,j.useSearchParams)(),{t:l}=(0,h.$G)(),n=e=>{b.toast.promise(x(e.toString()),{loading:l("Processing..."),success:e=>{if(!e.status)throw Error(e.message);return t(`/saves?page=${r.get("page")??1}&limit=${r.get("limit")??10}`),e.message},error:e=>e.message})};return a.jsx("div",{className:"flex items-center gap-2",children:a.jsx(f.z,{onClick:()=>n(e?.id),size:"icon",className:"h-8 w-8 bg-[#D13438] hover:bg-[#c1262b]",children:a.jsx(v.Z,{size:"20",color:"#fff"})})})}function k({data:e,meta:t,isLoading:r}){let[u,f]=m().useState([]),{t:p}=(0,h.$G)();return a.jsx(l.Z,{data:e?.length?[...e.map(e=>new c(e))]:[],sorting:u,isLoading:r,setSorting:f,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"name",header:p("Name"),cell:({row:e})=>{let t=e.original;return t.info?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(n.qE,{className:"size-8",children:[a.jsx(n.F$,{src:t.info?.avatar,alt:t.info?.label}),a.jsx(n.Q5,{children:(0,i.v)(t.info?.label)})]}),a.jsx("span",{className:"font-normal",children:t.info?.label})]}):a.jsx("span",{children:" N/A "})}},{id:"id",header:p("Number/ID"),cell:({row:e})=>{let t=e.original;if(!t?.value)return a.jsx("span",{className:"text-sm font-normal",children:" N/A "});if(Number.isNaN(Number(t?.value)))return a.jsx("span",{className:"block min-w-28 font-normal",children:t?.value});let r=(0,d.h)((0,o.Fg)(t?.value));return a.jsx("span",{className:"block min-w-28 font-normal",children:r.formatInternational()})}},{id:"email",header:p("Email"),cell:({row:e})=>{let t=e.original;return t?.info?.email?a.jsx("span",{className:"font-normal",children:t?.info?.email}):a.jsx("span",{className:"text-sm font-normal",children:" N/A "})}},{id:"website",header:p("Website"),cell:({row:e})=>{let t=e.original;return t?.website?a.jsx("span",{className:"font-normal",children:t?.website}):a.jsx("span",{className:"text-sm font-normal",children:" N/A "})}},{id:"type",header:p("Type"),cell:({row:e})=>a.jsx(s.C,{variant:"secondary",className:"bg-status-secondary font-normal text-status-secondary-foreground",children:(0,o.fl)(e.original.type)??a.jsx("span",{className:"text-sm font-normal",children:" N/A "})})},{id:"menu",header:p("Menu"),cell:({row:e})=>a.jsx(w,{row:e?.original})}]})}var Z=r(63761),y=r(75584);function E(){let{t:e}=(0,h.$G)(),t=(0,j.useSearchParams)(),[r,l]=u.useState(t.get("search")??""),n=(0,j.useRouter)(),s=(0,j.usePathname)(),{data:c,meta:i,isLoading:d}=(0,y.Z)(`/saves?${t.toString()}`);return a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[a.jsx("div",{className:"flex w-full flex-col gap-4 md:flex-row md:justify-end xl:h-12 xl:items-center",children:a.jsx("div",{className:"flex w-full items-center gap-4 sm:w-fit",children:a.jsx(Z.R,{value:r,onChange:e=>{e.preventDefault();let t=(0,o.w4)(e.target.value);l(e.target.value),n.replace(`${s}?${t.toString()}`)},iconPlacement:"end",placeholder:e("Search..."),className:"h-10 rounded-lg",containerClass:"w-full sm:w-[280px]"})})}),a.jsx(k,{data:c,isLoading:d,meta:i})]})})}},56140:(e,t,r)=>{"use strict";r.d(t,{Z:()=>w});var a=r(10326),l=r(77863),n=r(86508),s=r(11798),o=r(77132),c=r(6216),i=r(75817),d=r(40420),u=r(35047),m=r(93327),h=r(17577),f=r(70012),p=r(90772);let g=h.forwardRef(({className:e,...t},r)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:r,className:(0,l.ZP)("w-full caption-bottom text-sm",e),...t})}));g.displayName="Table";let x=h.forwardRef(({className:e,...t},r)=>a.jsx("thead",{ref:r,className:(0,l.ZP)("",e),...t}));x.displayName="TableHeader";let v=h.forwardRef(({className:e,...t},r)=>a.jsx("tbody",{ref:r,className:(0,l.ZP)("[&_tr:last-child]:border-0",e),...t}));v.displayName="TableBody",h.forwardRef(({className:e,...t},r)=>a.jsx("tfoot",{ref:r,className:(0,l.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let j=h.forwardRef(({className:e,...t},r)=>a.jsx("tr",{ref:r,className:(0,l.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));j.displayName="TableRow";let b=h.forwardRef(({className:e,...t},r)=>a.jsx("th",{ref:r,className:(0,l.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));b.displayName="TableHead";let N=h.forwardRef(({className:e,...t},r)=>a.jsx("td",{ref:r,className:(0,l.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));function w({data:e,isLoading:t=!1,structure:r,sorting:w,setSorting:k,padding:Z=!1,className:y,onRefresh:E,pagination:M}){let L=(0,h.useMemo)(()=>r,[r]),S=(0,u.useRouter)(),P=(0,u.usePathname)(),C=(0,u.useSearchParams)(),{t:R}=(0,f.$G)(),T=(0,n.b7)({data:e||[],columns:L,state:{sorting:w,onRefresh:E},onSortingChange:k,getCoreRowModel:(0,s.sC)(),getSortedRowModel:(0,s.tj)(),debugTable:!1});return t?a.jsx("div",{className:"rounded-md bg-background p-10",children:a.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:R("Loading...")})}):e?.length?(0,a.jsxs)("div",{className:(0,l.ZP)(`${Z?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,y),children:[(0,a.jsxs)(g,{children:[a.jsx(x,{children:T.getHeaderGroups().map(e=>a.jsx(j,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>a.jsx(b,{className:(0,l.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,a.jsxs)(p.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[R((0,n.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:a.jsx(c.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:a.jsx(c.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??a.jsx(c.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),a.jsx(v,{children:T.getRowModel().rows.map(e=>a.jsx(j,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>a.jsx(N,{className:"py-3 text-sm font-semibold",children:(0,n.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),M&&M.total>10&&a.jsx("div",{className:"pb-2 pt-6",children:a.jsx(m.Z,{showTotal:(e,t)=>R("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:M?.page,total:M?.total,pageSize:M?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(C);t.set("page",e.toString()),S.push(`${P}?${t.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>a.jsx("a",{...e,children:a.jsx(i.Z,{size:"18"})}),nextIcon:e=>a.jsx("a",{...e,children:a.jsx(d.Z,{size:"18"})})})})]}):a.jsx("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(o.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),R("No data found!")]})})}N.displayName="TableCell",h.forwardRef(({className:e,...t},r)=>a.jsx("caption",{ref:r,className:(0,l.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},63761:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var a=r(10326);r(17577);var l=r(54432),n=r(77863),s=r(32894);function o({iconPlacement:e="start",className:t,containerClass:r,...o}){return(0,a.jsxs)("div",{className:(0,n.ZP)("relative flex items-center",r),children:[a.jsx(s.Z,{size:"20",className:(0,n.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),a.jsx(l.I,{type:"text",className:(0,n.ZP)("h-10","end"===e?"pr-10":"pl-10",t),...o})]})}},54432:(e,t,r)=>{"use strict";r.d(t,{I:()=>s});var a=r(10326),l=r(17577),n=r(77863);let s=l.forwardRef(({className:e,type:t,...r},l)=>a.jsx("input",{type:t,className:(0,n.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:l,...r}));s.displayName="Input"},75584:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var a=r(90799),l=r(35047);function n(e,t){let r=(0,l.usePathname)(),n=(0,l.useSearchParams)(),s=(0,l.useRouter)(),[o,c]=e.split("?"),i=new URLSearchParams(c);i.has("page")||i.set("page","1"),i.has("limit")||i.set("limit","10");let d=`${o}?${i.toString()}`,{data:u,error:m,isLoading:h,mutate:f,...p}=(0,a.d)(d,t);return{refresh:()=>f(u),data:u?.data?.data??[],meta:u?.data?.meta,filter:(e,t,a)=>{let l=new URLSearchParams(n.toString());t?l.set(e,t.toString()):l.delete(e),s.replace(`${r}?${l.toString()}`),a?.()},isLoading:h,error:m,...p}}},32894:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var a=r(52920),l=r(17577),n=r.n(l),s=r(78439),o=r.n(s),c=["variant","color","size"],i=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},d=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{opacity:".4",d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",fill:t}),n().createElement("path",{d:"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},m=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},f=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n().createElement("path",{opacity:".4",d:"m22 22-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return n().createElement(i,{color:t});case"Broken":return n().createElement(d,{color:t});case"Bulk":return n().createElement(u,{color:t});case"Linear":default:return n().createElement(m,{color:t});case"Outline":return n().createElement(h,{color:t});case"TwoTone":return n().createElement(f,{color:t})}},g=(0,l.forwardRef)(function(e,t){var r=e.variant,l=e.color,s=e.size,o=(0,a._)(e,c);return n().createElement("svg",(0,a.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(r,l))});g.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="SearchNormal1"},72871:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var a=r(52920),l=r(17577),n=r.n(l),s=r(78439),o=r.n(s),c=["variant","color","size"],i=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M21.07 5.23c-1.61-.16-3.22-.28-4.84-.37v-.01l-.22-1.3c-.15-.92-.37-2.3-2.71-2.3h-2.62c-2.33 0-2.55 1.32-2.71 2.29l-.21 1.28c-.93.06-1.86.12-2.79.21l-2.04.2c-.42.04-.72.41-.***********.4.71.82.67l2.04-.2c5.24-.52 10.52-.32 15.82.21h.08c.38 0 .71-.29.75-.68a.766.766 0 0 0-.69-.82ZM19.23 8.14c-.24-.25-.57-.39-.91-.39H5.68c-.34 0-.68.14-.91.39-.23.25-.36.59-.34.94l.62 10.26c.11 1.52.25 3.42 3.74 3.42h6.42c3.49 0 3.63-1.89 3.74-3.42l.62-10.25c.02-.36-.11-.7-.34-.95Zm-5.57 9.61h-3.33c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h3.33c.41 0 .75.34.75.75s-.34.75-.75.75Zm.84-4h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},d=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98M8.5 4.97l.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3M15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14M18.85 9.14l-.65 10.07M10.33 16.5h3.33M12.82 12.5h1.68M9.5 12.5h.83",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M21.07 5.23c-1.61-.16-3.22-.28-4.84-.37v-.01l-.22-1.3c-.15-.92-.37-2.3-2.71-2.3h-2.62c-2.33 0-2.55 1.32-2.71 2.29l-.21 1.28c-.93.06-1.86.12-2.79.21l-2.04.2c-.42.04-.72.41-.***********.4.71.82.67l2.04-.2c5.24-.52 10.52-.32 15.82.21h.08c.38 0 .71-.29.75-.68a.766.766 0 0 0-.69-.82Z",fill:t}),n().createElement("path",{opacity:".399",d:"M19.23 8.14c-.24-.25-.57-.39-.91-.39H5.68c-.34 0-.68.14-.91.39-.23.25-.36.59-.34.94l.62 10.26c.11 1.52.25 3.42 3.74 3.42h6.42c3.49 0 3.63-1.89 3.74-3.42l.62-10.25c.02-.36-.11-.7-.34-.95Z",fill:t}),n().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.58 17a.75.75 0 0 1 .75-.75h3.33a.75.75 0 0 1 0 1.5h-3.33a.75.75 0 0 1-.75-.75ZM8.75 13a.75.75 0 0 1 .75-.75h5a.75.75 0 0 1 0 1.5h-5a.75.75 0 0 1-.75-.75Z",fill:t}))},m=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98M8.5 4.97l.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3M18.85 9.14l-.65 10.07C18.09 20.78 18 22 15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14M10.33 16.5h3.33M9.5 12.5h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M21 6.73h-.08c-5.29-.53-10.57-.73-15.8-.2l-2.04.2a.755.755 0 0 1-.83-.68c-.04-.42.26-.78.67-.82l2.04-.2c5.32-.54 10.71-.33 **********.***********.82a.74.74 0 0 1-.74.68Z",fill:t}),n().createElement("path",{d:"M8.5 5.72c-.04 0-.08 0-.13-.01a.753.753 0 0 1-.61-.86l.22-1.31c.16-.96.38-2.29 2.71-2.29h2.62c2.34 0 2.56 1.38 2.71 2.3l.22 1.3c.07.41-.21.8-.61.86-.41.07-.8-.21-.86-.61l-.22-1.3c-.14-.87-.17-1.04-1.23-1.04H10.7c-1.06 0-1.08.14-1.23 1.03l-.23 1.3a.75.75 0 0 1-.74.63ZM15.21 22.752H8.79c-3.49 0-3.63-1.93-3.74-3.49L4.4 9.192c-.03-.41.29-.77.7-.8.42-.02.77.29.8.7l.65 10.07c.11 1.52.15 2.09 2.24 2.09h6.42c2.1 0 2.14-.57 2.24-2.09l.65-10.07c.03-.41.39-.72.8-.7.41.03.73.38.7.8l-.65 10.07c-.11 1.56-.25 3.49-3.74 3.49Z",fill:t}),n().createElement("path",{d:"M13.66 17.25h-3.33c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h3.33c.41 0 .75.34.75.75s-.34.75-.75.75ZM14.5 13.25h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},f=function(e){var t=e.color;return n().createElement(n().Fragment,null,n().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n().createElement("path",{opacity:".34",d:"m8.5 4.97.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n().createElement("path",{d:"m18.85 9.14-.65 10.07C18.09 20.78 18 22 15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),n().createElement("path",{opacity:".34",d:"M10.33 16.5h3.33M9.5 12.5h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return n().createElement(i,{color:t});case"Broken":return n().createElement(d,{color:t});case"Bulk":return n().createElement(u,{color:t});case"Linear":default:return n().createElement(m,{color:t});case"Outline":return n().createElement(h,{color:t});case"TwoTone":return n().createElement(f,{color:t})}},g=(0,l.forwardRef)(function(e,t){var r=e.variant,l=e.color,s=e.size,o=(0,a._)(e,c);return n().createElement("svg",(0,a.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(r,l))});g.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="Trash"},79766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(19510),l=r(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(l.a,{})})}},61011:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\favorites\page.tsx#default`)},88728:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(19510),l=r(40099),n=r(76609);function s({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(n.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(l.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},80549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(19510),l=r(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(l.a,{})})}},74200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(19510),l=r(61011);function n(){return a.jsx(l.default,{})}},64561:(e,t,r)=>{"use strict";r.d(t,{h:()=>o});var a=r(34878),l=r(89637),n=r(36159);function s(){var e=(0,n.Z)(arguments),t=e.text,r=e.options,a=e.metadata;return(0,l.Z)(t,r,a)}function o(){return(0,a.Z)(s,arguments)}}};