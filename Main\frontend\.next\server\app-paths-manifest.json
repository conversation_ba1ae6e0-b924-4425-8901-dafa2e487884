{"/_not-found/page": "app/_not-found/page.js", "/(auth)/forgot-password/page": "app/(auth)/forgot-password/page.js", "/(auth)/forgot-password/mail-send/page": "app/(auth)/forgot-password/mail-send/page.js", "/(auth)/register/email-verification-message/page": "app/(auth)/register/email-verification-message/page.js", "/(auth)/reset-password/page": "app/(auth)/reset-password/page.js", "/(auth)/signin/2fa/page": "app/(auth)/signin/2fa/page.js", "/(auth)/signin/page": "app/(auth)/signin/page.js", "/mpay/confirm-payment/page": "app/mpay/confirm-payment/page.js", "/mpay/otp-pay/page": "app/mpay/otp-pay/page.js", "/mpay/page": "app/mpay/page.js", "/mpay/qrform/page": "app/mpay/qrform/page.js", "/mpay/review/page": "app/mpay/review/page.js", "/(auth)/register/(tabs)/@customer/customer/page": "app/(auth)/register/(tabs)/@customer/customer/page.js", "/(auth)/register/(tabs)/@agent/agent/page": "app/(auth)/register/(tabs)/@agent/agent/page.js", "/(auth)/register/(tabs)/@merchant/merchant/page": "app/(auth)/register/(tabs)/@merchant/merchant/page.js", "/(auth)/register/(tabs)/page": "app/(auth)/register/(tabs)/page.js", "/(protected)/@admin/(dashboard)/page": "app/(protected)/@admin/(dashboard)/page.js", "/(protected)/@admin/account-settings/page": "app/(protected)/@admin/account-settings/page.js", "/(protected)/@admin/cards/page": "app/(protected)/@admin/cards/page.js", "/(protected)/@admin/deposits/history/page": "app/(protected)/@admin/deposits/history/page.js", "/(protected)/@admin/deposits/page": "app/(protected)/@admin/deposits/page.js", "/(protected)/@admin/exchanges/history/page": "app/(protected)/@admin/exchanges/history/page.js", "/(protected)/@admin/exchanges/page": "app/(protected)/@admin/exchanges/page.js", "/(protected)/@admin/payments/page": "app/(protected)/@admin/payments/page.js", "/(protected)/@admin/staffs/create/page": "app/(protected)/@admin/staffs/create/page.js", "/(protected)/@admin/staffs/edit/[staffId]/page": "app/(protected)/@admin/staffs/edit/[staffId]/page.js", "/(protected)/@admin/staffs/page": "app/(protected)/@admin/staffs/page.js", "/(protected)/@admin/transfers/history/page": "app/(protected)/@admin/transfers/history/page.js", "/(protected)/@admin/transfers/page": "app/(protected)/@admin/transfers/page.js", "/(protected)/@admin/withdraws/history/page": "app/(protected)/@admin/withdraws/history/page.js", "/(protected)/@admin/withdraws/page": "app/(protected)/@admin/withdraws/page.js", "/(protected)/@agent/(dashboard)/page": "app/(protected)/@agent/(dashboard)/page.js", "/(protected)/@agent/cards/page": "app/(protected)/@agent/cards/page.js", "/(protected)/@agent/contact-supports/page": "app/(protected)/@agent/contact-supports/page.js", "/(protected)/@agent/deposit-request/page": "app/(protected)/@agent/deposit-request/page.js", "/(protected)/@agent/deposit/page": "app/(protected)/@agent/deposit/page.js", "/(protected)/@agent/deposit/transaction-status/page": "app/(protected)/@agent/deposit/transaction-status/page.js", "/(protected)/@agent/direct-deposit/page": "app/(protected)/@agent/direct-deposit/page.js", "/(protected)/@agent/exchange/page": "app/(protected)/@agent/exchange/page.js", "/(protected)/@agent/investments-history/page": "app/(protected)/@agent/investments-history/page.js", "/(protected)/@agent/referral/page": "app/(protected)/@agent/referral/page.js", "/(protected)/@agent/settlements/page": "app/(protected)/@agent/settlements/page.js", "/(protected)/@agent/transaction-history/page": "app/(protected)/@agent/transaction-history/page.js", "/(protected)/@agent/wallets/page": "app/(protected)/@agent/wallets/page.js", "/(protected)/@agent/withdraw-request/page": "app/(protected)/@agent/withdraw-request/page.js", "/(protected)/@agent/withdraw/page": "app/(protected)/@agent/withdraw/page.js", "/(protected)/@agent/withdraw/transaction-status/page": "app/(protected)/@agent/withdraw/transaction-status/page.js", "/(protected)/@customer/cards/page": "app/(protected)/@customer/cards/page.js", "/(protected)/@customer/contact-supports/page": "app/(protected)/@customer/contact-supports/page.js", "/(protected)/@customer/contacts/page": "app/(protected)/@customer/contacts/page.js", "/(protected)/@customer/deposit/page": "app/(protected)/@customer/deposit/page.js", "/(protected)/@customer/deposit/transaction-status/page": "app/(protected)/@customer/deposit/transaction-status/page.js", "/(protected)/@customer/favorites/page": "app/(protected)/@customer/favorites/page.js", "/(protected)/@customer/investments-history/page": "app/(protected)/@customer/investments-history/page.js", "/(protected)/@customer/exchange/page": "app/(protected)/@customer/exchange/page.js", "/(protected)/@customer/payment/page": "app/(protected)/@customer/payment/page.js", "/(protected)/@customer/referral/page": "app/(protected)/@customer/referral/page.js", "/(protected)/@customer/services/page": "app/(protected)/@customer/services/page.js", "/(protected)/@customer/transaction-history/page": "app/(protected)/@customer/transaction-history/page.js", "/(protected)/@customer/transfer/page": "app/(protected)/@customer/transfer/page.js", "/(protected)/@customer/wallets/page": "app/(protected)/@customer/wallets/page.js", "/(protected)/@merchant/cards/page": "app/(protected)/@merchant/cards/page.js", "/(protected)/@merchant/contact-supports/page": "app/(protected)/@merchant/contact-supports/page.js", "/(protected)/@merchant/contacts/page": "app/(protected)/@merchant/contacts/page.js", "/(protected)/@merchant/deposit/page": "app/(protected)/@merchant/deposit/page.js", "/(protected)/@merchant/exchange/page": "app/(protected)/@merchant/exchange/page.js", "/(protected)/@merchant/favorites/page": "app/(protected)/@merchant/favorites/page.js", "/(protected)/@merchant/deposit/transaction-status/page": "app/(protected)/@merchant/deposit/transaction-status/page.js", "/(protected)/@merchant/investments-history/page": "app/(protected)/@merchant/investments-history/page.js", "/(protected)/@merchant/merchant-transactions/page": "app/(protected)/@merchant/merchant-transactions/page.js", "/(protected)/@merchant/payment-requests/page": "app/(protected)/@merchant/payment-requests/page.js", "/(protected)/@merchant/payment-requests/create/page": "app/(protected)/@merchant/payment-requests/create/page.js", "/(protected)/@merchant/payment/page": "app/(protected)/@merchant/payment/page.js", "/(protected)/@merchant/referral/page": "app/(protected)/@merchant/referral/page.js", "/(protected)/@merchant/services/electricity-bill/page": "app/(protected)/@merchant/services/electricity-bill/page.js", "/(protected)/@merchant/services/top-up/page": "app/(protected)/@merchant/services/top-up/page.js", "/(protected)/@merchant/services/page": "app/(protected)/@merchant/services/page.js", "/(protected)/@merchant/services/top-up/success/page": "app/(protected)/@merchant/services/top-up/success/page.js", "/(protected)/@merchant/transaction-history/page": "app/(protected)/@merchant/transaction-history/page.js", "/(protected)/@merchant/transfer/page": "app/(protected)/@merchant/transfer/page.js", "/(protected)/@merchant/wallets/page": "app/(protected)/@merchant/wallets/page.js", "/(protected)/@merchant/withdraw/page": "app/(protected)/@merchant/withdraw/page.js", "/(protected)/@merchant/withdraw/transaction-status/page": "app/(protected)/@merchant/withdraw/transaction-status/page.js", "/(protected)/@admin/agents/bulk-email/page": "app/(protected)/@admin/agents/bulk-email/page.js", "/(protected)/@admin/customers/bulk-email/page": "app/(protected)/@admin/customers/bulk-email/page.js", "/(protected)/@admin/investments/create-plan/page": "app/(protected)/@admin/investments/create-plan/page.js", "/(protected)/@admin/investments/edit-plan/[investmentId]/page": "app/(protected)/@admin/investments/edit-plan/[investmentId]/page.js", "/(protected)/@admin/investments/history/page": "app/(protected)/@admin/investments/history/page.js", "/(protected)/@admin/investments/manage-plans/page": "app/(protected)/@admin/investments/manage-plans/page.js", "/(protected)/@admin/investments/page": "app/(protected)/@admin/investments/page.js", "/(protected)/@admin/settings/currencies/page": "app/(protected)/@admin/settings/currencies/page.js", "/(protected)/@admin/settings/login-sessions/page": "app/(protected)/@admin/settings/login-sessions/page.js", "/(protected)/@admin/settings/page": "app/(protected)/@admin/settings/page.js", "/(protected)/@admin/merchants/bulk-email/page": "app/(protected)/@admin/merchants/bulk-email/page.js", "/(protected)/@admin/settings/plugins/page": "app/(protected)/@admin/settings/plugins/page.js", "/(protected)/@admin/settings/services/page": "app/(protected)/@admin/settings/services/page.js", "/(protected)/@admin/settings/site-settings/page": "app/(protected)/@admin/settings/site-settings/page.js", "/(protected)/@admin/settings/withdraw-methods/create/page": "app/(protected)/@admin/settings/withdraw-methods/create/page.js", "/(protected)/@admin/settings/withdraw-methods/page": "app/(protected)/@admin/settings/withdraw-methods/page.js", "/(protected)/@agent/investments/available-plans/page": "app/(protected)/@agent/investments/available-plans/page.js", "/(protected)/@agent/investments/page": "app/(protected)/@agent/investments/page.js", "/(protected)/@agent/settings/kyc-verification-settings/page": "app/(protected)/@agent/settings/kyc-verification-settings/page.js", "/(protected)/@agent/settings/login-sessions/page": "app/(protected)/@agent/settings/login-sessions/page.js", "/(protected)/@agent/settings/fees-commissions/page": "app/(protected)/@agent/settings/fees-commissions/page.js", "/(protected)/@agent/settings/methods/page": "app/(protected)/@agent/settings/methods/page.js", "/(protected)/@agent/settings/page": "app/(protected)/@agent/settings/page.js", "/(protected)/@customer/(dashboard)/@tableSlot/page": "app/(protected)/@customer/(dashboard)/@tableSlot/page.js", "/(protected)/@customer/investments/available-plans/page": "app/(protected)/@customer/investments/available-plans/page.js", "/(protected)/@customer/services/electricity-bill/page": "app/(protected)/@customer/services/electricity-bill/page.js", "/(protected)/@customer/investments/page": "app/(protected)/@customer/investments/page.js", "/(protected)/@customer/services/top-up/page": "app/(protected)/@customer/services/top-up/page.js", "/(protected)/@customer/services/top-up/success/page": "app/(protected)/@customer/services/top-up/success/page.js", "/(protected)/@customer/settings/login-sessions/page": "app/(protected)/@customer/settings/login-sessions/page.js", "/(protected)/@customer/settings/kyc-verification-settings/page": "app/(protected)/@customer/settings/kyc-verification-settings/page.js", "/(protected)/@customer/withdraw/page": "app/(protected)/@customer/withdraw/page.js", "/(protected)/@customer/settings/page": "app/(protected)/@customer/settings/page.js", "/(protected)/@customer/withdraw/transaction-status/page": "app/(protected)/@customer/withdraw/transaction-status/page.js", "/(protected)/@merchant/investments/available-plans/page": "app/(protected)/@merchant/investments/available-plans/page.js", "/(protected)/@merchant/(dashboard)/@tableSlot/page": "app/(protected)/@merchant/(dashboard)/@tableSlot/page.js", "/(protected)/@merchant/investments/page": "app/(protected)/@merchant/investments/page.js", "/(protected)/@merchant/settings/kyc-verification-settings/page": "app/(protected)/@merchant/settings/kyc-verification-settings/page.js", "/(protected)/@merchant/settings/login-sessions/page": "app/(protected)/@merchant/settings/login-sessions/page.js", "/(protected)/@merchant/settings/merchant-settings/page": "app/(protected)/@merchant/settings/merchant-settings/page.js", "/(protected)/@merchant/settings/mpay-api/page": "app/(protected)/@merchant/settings/mpay-api/page.js", "/(protected)/@merchant/settings/page": "app/(protected)/@merchant/settings/page.js", "/(protected)/@merchant/settings/webhook-url-settings/page": "app/(protected)/@merchant/settings/webhook-url-settings/page.js", "/(protected)/@admin/agents/(list)/list/page": "app/(protected)/@admin/agents/(list)/list/page.js", "/(protected)/@admin/agents/(list)/page": "app/(protected)/@admin/agents/(list)/page.js", "/(protected)/@admin/customers/(list)/list/page": "app/(protected)/@admin/customers/(list)/list/page.js", "/(protected)/@admin/customers/(list)/page": "app/(protected)/@admin/customers/(list)/page.js", "/(protected)/@admin/merchants/(list)/list/page": "app/(protected)/@admin/merchants/(list)/list/page.js", "/(protected)/@admin/merchants/(list)/payment-request/page": "app/(protected)/@admin/merchants/(list)/payment-request/page.js", "/(protected)/@admin/merchants/(list)/page": "app/(protected)/@admin/merchants/(list)/page.js", "/(auth)/register/email-verification-status/page": "app/(auth)/register/email-verification-status/page.js", "/(protected)/@admin/deposits/[depositId]/page": "app/(protected)/@admin/deposits/[depositId]/page.js", "/(protected)/@admin/withdraws/[withdrawId]/page": "app/(protected)/@admin/withdraws/[withdrawId]/page.js", "/(protected)/@agent/deposit-request/[trxId]/page": "app/(protected)/@agent/deposit-request/[trxId]/page.js", "/(protected)/@agent/withdraw-request/[trxId]/page": "app/(protected)/@agent/withdraw-request/[trxId]/page.js", "/(protected)/@admin/exchanges/[exchangeId]/page": "app/(protected)/@admin/exchanges/[exchangeId]/page.js", "/(protected)/@admin/settings/gateways/[gatewayId]/page": "app/(protected)/@admin/settings/gateways/[gatewayId]/page.js", "/(protected)/@admin/settings/plugins/[pluginId]/page": "app/(protected)/@admin/settings/plugins/[pluginId]/page.js", "/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page": "app/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page.js", "/(protected)/@admin/transfers/[transferId]/page": "app/(protected)/@admin/transfers/[transferId]/page.js", "/(protected)/@admin/transactions/[trxId]/page": "app/(protected)/@admin/transactions/[trxId]/page.js", "/(protected)/@admin/agents/[userId]/[agentId]/commissions/page": "app/(protected)/@admin/agents/[userId]/[agentId]/commissions/page.js", "/(protected)/@admin/agents/[userId]/[agentId]/fees/page": "app/(protected)/@admin/agents/[userId]/[agentId]/fees/page.js", "/(protected)/@admin/agents/[userId]/[agentId]/kyc/page": "app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page.js", "/(protected)/@admin/agents/[userId]/[agentId]/page": "app/(protected)/@admin/agents/[userId]/[agentId]/page.js", "/(protected)/@admin/agents/[userId]/[agentId]/permissions/page": "app/(protected)/@admin/agents/[userId]/[agentId]/permissions/page.js", "/(protected)/@admin/agents/[userId]/[agentId]/send-email/page": "app/(protected)/@admin/agents/[userId]/[agentId]/send-email/page.js", "/(protected)/@admin/agents/[userId]/[agentId]/transactions/page": "app/(protected)/@admin/agents/[userId]/[agentId]/transactions/page.js", "/(protected)/@admin/customers/[customerId]/kyc/page": "app/(protected)/@admin/customers/[customerId]/kyc/page.js", "/(protected)/@admin/customers/[customerId]/page": "app/(protected)/@admin/customers/[customerId]/page.js", "/(protected)/@admin/customers/[customerId]/permissions/page": "app/(protected)/@admin/customers/[customerId]/permissions/page.js", "/(protected)/@admin/customers/[customerId]/send-email/page": "app/(protected)/@admin/customers/[customerId]/send-email/page.js", "/(protected)/@admin/customers/[customerId]/transactions/page": "app/(protected)/@admin/customers/[customerId]/transactions/page.js", "/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page": "app/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page.js", "/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page": "app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page.js", "/(protected)/@admin/merchants/[userId]/[merchantId]/page": "app/(protected)/@admin/merchants/[userId]/[merchantId]/page.js", "/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page": "app/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page.js", "/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page": "app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page.js", "/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page": "app/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page.js"}