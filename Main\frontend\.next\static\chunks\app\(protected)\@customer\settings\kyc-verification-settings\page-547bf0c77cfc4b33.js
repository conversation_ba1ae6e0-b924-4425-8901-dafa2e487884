(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[70090],{54077:function(e,n,t){Promise.resolve().then(t.bind(t,57177))},57177:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return f}});var r=t(57437),a=t(70093),l=t(32211),s=t(6596),i=t(79981),u=t(85323);function f(){let{data:e,isLoading:n,mutate:t}=(0,u.ZP)("/kycs/detail",e=>i.Z.get(e),{refreshInterval:0,revalidateIfStale:!1,revalidateOnFocus:!1,refreshWhenHidden:!1,shouldRetryOnError:!1});return(0,r.jsx)(s.UQ,{type:"multiple",defaultValue:["KYC_STATUS","DOCUMENT_INFORMATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)(l.Z,{fetchData:null==e?void 0:e.data,isLoading:n}),(0,r.jsx)(a.u,{fetchData:e,isLoading:n,refresh:t})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,33145,38658,58939,85598,21564,41543,19813,92971,95030,1744],function(){return e(e.s=54077)}),_N_E=e.O()}]);