"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[56025],{84764:function(e,t,n){n.d(t,{ZP:function(){return ep}});var r,o,a=n(5853),i="4.6.2";function c(e,t){return new Promise(function(n){return setTimeout(n,e,t)})}function l(e){return!!e&&"function"==typeof e.then}function u(e,t){try{var n=e();l(n)?n.then(function(e){return t(!0,e)},function(e){return t(!1,e)}):t(!0,n)}catch(e){t(!1,e)}}function s(e,t,n){return void 0===n&&(n=16),(0,a.mG)(this,void 0,void 0,function(){var r,o,i,c;return(0,a.Jh)(this,function(a){switch(a.label){case 0:r=Array(e.length),o=Date.now(),i=0,a.label=1;case 1:if(!(i<e.length))return[3,4];if(r[i]=t(e[i],i),!((c=Date.now())>=o+n))return[3,3];return o=c,[4,new Promise(function(e){var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(null)})];case 2:a.sent(),a.label=3;case 3:return++i,[3,1];case 4:return[2,r]}})})}function d(e){return e.then(void 0,function(){}),e}function f(e){return parseInt(e)}function m(e){return parseFloat(e)}function p(e,t){return"number"==typeof e&&isNaN(e)?t:e}function h(e){return e.reduce(function(e,t){return e+(t?1:0)},0)}function v(e,t){if(void 0===t&&(t=1),Math.abs(t)>=1)return Math.round(e/t)*t;var n=1/t;return Math.round(e*n)/n}function g(e,t){var n,r,o=e[0]>>>16,a=65535&e[0],i=e[1]>>>16,c=65535&e[1],l=t[0]>>>16,u=65535&t[0],s=t[1]>>>16,d=65535&t[1],f=0,m=0;n=0+((r=0+(c+d))>>>16),r&=65535,n+=i+s,m+=n>>>16,n&=65535,m+=a+u,f+=m>>>16,m&=65535,f+=o+l,f&=65535,e[0]=f<<16|m,e[1]=n<<16|r}function y(e,t){var n,r,o=e[0]>>>16,a=65535&e[0],i=e[1]>>>16,c=65535&e[1],l=t[0]>>>16,u=65535&t[0],s=t[1]>>>16,d=65535&t[1],f=0,m=0;n=0+((r=0+c*d)>>>16),r&=65535,n+=i*d,m+=n>>>16,n&=65535,n+=c*s,m+=n>>>16,n&=65535,m+=a*d,f+=m>>>16,m&=65535,m+=i*s,f+=m>>>16,m&=65535,m+=c*u,f+=m>>>16,m&=65535,f+=o*d+a*s+i*u+c*l,f&=65535,e[0]=f<<16|m,e[1]=n<<16|r}function b(e,t){var n=e[0];32==(t%=64)?(e[0]=e[1],e[1]=n):t<32?(e[0]=n<<t|e[1]>>>32-t,e[1]=e[1]<<t|n>>>32-t):(t-=32,e[0]=e[1]<<t|n>>>32-t,e[1]=n<<t|e[1]>>>32-t)}function w(e,t){0!=(t%=64)&&(t<32?(e[0]=e[1]>>>32-t,e[1]=e[1]<<t):(e[0]=e[1]<<t-32,e[1]=0))}function k(e,t){e[0]^=t[0],e[1]^=t[1]}var L=[4283543511,3981806797],S=[3301882366,444984403];function E(e){var t=[0,e[0]>>>1];k(e,t),y(e,L),t[1]=e[0]>>>1,k(e,t),y(e,S),t[1]=e[0]>>>1,k(e,t)}var M=[2277735313,289559509],x=[1291169091,658871167],W=[0,5],V=[0,1390208809],R=[0,944331445];function C(){var e=window,t=navigator;return h(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in t,"msPointerEnabled"in t])>=4}function P(){var e=window,t=navigator;return h(["webkitPersistentStorage"in t,"webkitTemporaryStorage"in t,0===(t.vendor||"").indexOf("Google"),"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])>=5}function j(){var e=window;return h(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,0===navigator.vendor.indexOf("Apple"),"RGBColor"in e,"WebKitMediaKeys"in e])>=4}function Z(){var e=window,t=e.HTMLElement,n=e.Document;return h(["safari"in e,!("ongestureend"in e),!("TouchEvent"in e),!("orientation"in e),t&&!("autocapitalize"in t.prototype),n&&"pointerLockElement"in n.prototype])>=4}function I(){var e,t=window;return e=t.print,/^function\s.*?\{\s*\[native code]\s*}$/.test(String(e))&&"[object WebPageNamespace]"===String(t.browser)}function F(){var e,t,n=window;return h(["buildID"in navigator,"MozAppearance"in(null!==(t=null===(e=document.documentElement)||void 0===e?void 0:e.style)&&void 0!==t?t:{}),"onmozfullscreenchange"in n,"mozInnerScreenX"in n,"CSSMozDocumentRule"in n,"CanvasCaptureMediaStream"in n])>=4}function T(){var e=window,t=navigator,n=e.CSS,r=e.HTMLButtonElement;return h([!("getStorageUpdates"in t),r&&"popover"in r.prototype,"CSSCounterStyleRule"in e,n.supports("font-size-adjust: ex-height 0.5"),n.supports("text-transform: full-width")])>=4}function G(){var e=P(),t=F(),n=window,r=navigator,o="connection";return e?h([!("SharedWorker"in n),r[o]&&"ontypechange"in r[o],!("sinkId"in new Audio)])>=2:!!t&&h(["onorientationchange"in n,"orientation"in n,/android/i.test(r.appVersion)])>=2}function N(e){var t=Error(e);return t.name=e,t}function O(e,t,n){var r,o,i;return void 0===n&&(n=50),(0,a.mG)(this,void 0,void 0,function(){var l,u;return(0,a.Jh)(this,function(a){switch(a.label){case 0:l=document,a.label=1;case 1:if(l.body)return[3,3];return[4,c(n)];case 2:return a.sent(),[3,1];case 3:u=l.createElement("iframe"),a.label=4;case 4:return a.trys.push([4,,10,11]),[4,new Promise(function(e,n){var r=!1,o=function(){r=!0,e()};u.onload=o,u.onerror=function(e){r=!0,n(e)};var a=u.style;a.setProperty("display","block","important"),a.position="absolute",a.top="0",a.left="0",a.visibility="hidden",t&&"srcdoc"in u?u.srcdoc=t:u.src="about:blank",l.body.appendChild(u);var i=function(){var e,t;r||((null===(t=null===(e=u.contentWindow)||void 0===e?void 0:e.document)||void 0===t?void 0:t.readyState)==="complete"?o():setTimeout(i,10))};i()})];case 5:a.sent(),a.label=6;case 6:if(null===(o=null===(r=u.contentWindow)||void 0===r?void 0:r.document)||void 0===o?void 0:o.body)return[3,8];return[4,c(n)];case 7:return a.sent(),[3,6];case 8:return[4,e(u,u.contentWindow)];case 9:return[2,a.sent()];case 10:return null===(i=u.parentNode)||void 0===i||i.removeChild(u),[7];case 11:return[2]}})})}var A=["monospace","sans-serif","serif"],D=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];function H(e){return e.toDataURL()}function z(){var e=screen;return[p(m(e.availTop),null),p(m(e.width)-m(e.availWidth)-p(m(e.availLeft),0),null),p(m(e.height)-m(e.availHeight)-p(m(e.availTop),0),null),p(m(e.availLeft),null)]}function Y(e){for(var t=0;t<4;++t)if(e[t])return!1;return!0}function X(e){e.style.setProperty("visibility","hidden","important"),e.style.setProperty("display","block","important")}function B(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}function _(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}function J(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}function U(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}function Q(e){return matchMedia("(prefers-reduced-transparency: ".concat(e,")")).matches}function K(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}var $=Math,q=function(){return 0},ee={default:[],apple:[{font:"-apple-system-body"}],serif:[{fontFamily:"serif"}],sans:[{fontFamily:"sans-serif"}],mono:[{fontFamily:"monospace"}],min:[{fontSize:"1px"}],system:[{fontFamily:"system-ui"}]},et=function(){for(var e=window;;){var t=e.parent;if(!t||t===e)return!1;try{if(t.location.origin!==e.location.origin)return!0}catch(e){if(e instanceof Error&&"SecurityError"===e.name)return!0;throw e}e=t}},en=new Set([10752,2849,2884,2885,2886,2928,2929,2930,2931,2932,2960,2961,2962,2963,2964,2965,2966,2967,2968,2978,3024,3042,3088,3089,3106,3107,32773,32777,32777,32823,32824,32936,32937,32938,32939,32968,32969,32970,32971,3317,33170,3333,3379,3386,33901,33902,34016,34024,34076,3408,3410,3411,3412,3413,3414,3415,34467,34816,34817,34818,34819,34877,34921,34930,35660,35661,35724,35738,35739,36003,36004,36005,36347,36348,36349,37440,37441,37443,7936,7937,7938]),er=new Set([34047,35723,36063,34852,34853,34854,34229,36392,36795,38449]),eo=["FRAGMENT_SHADER","VERTEX_SHADER"],ea=["LOW_FLOAT","MEDIUM_FLOAT","HIGH_FLOAT","LOW_INT","MEDIUM_INT","HIGH_INT"],ei="WEBGL_debug_renderer_info";function ec(e){if(e.webgl)return e.webgl.context;var t,n=document.createElement("canvas");n.addEventListener("webglCreateContextError",function(){return t=void 0});for(var r=0,o=["webgl","experimental-webgl"];r<o.length;r++){var a=o[r];try{t=n.getContext(a)}catch(e){}if(t)break}return e.webgl={context:t},t}function el(e){return Object.keys(e.__proto__).filter(eu)}function eu(e){return"string"==typeof e&&!e.match(/[^A-Z0-9_x]/)}function es(e){return"function"==typeof e.getParameter}var ed={fonts:function(){var e=this;return O(function(t,n){var r=n.document;return(0,a.mG)(e,void 0,void 0,function(){var e,t,n,o,i,c,l,u,s,d,f,m;return(0,a.Jh)(this,function(a){for((e=r.body).style.fontSize="48px",(t=r.createElement("div")).style.setProperty("visibility","hidden","important"),n={},o={},i=function(e){var n=r.createElement("span"),o=n.style;return o.position="absolute",o.top="0",o.left="0",o.fontFamily=e,n.textContent="mmMwWLliI0O&1",t.appendChild(n),n},c=function(e,t){return i("'".concat(e,"',").concat(t))},l=function(){return A.map(i)},u=function(){for(var e={},t=function(t){e[t]=A.map(function(e){return c(t,e)})},n=0;n<D.length;n++)t(D[n]);return e},s=function(e){return A.some(function(t,r){return e[r].offsetWidth!==n[t]||e[r].offsetHeight!==o[t]})},d=l(),f=u(),e.appendChild(t),m=0;m<A.length;m++)n[A[m]]=d[m].offsetWidth,o[A[m]]=d[m].offsetHeight;return[2,D.filter(function(e){return s(f[e])})]})})})},domBlockers:function(e){var t=(void 0===e?{}:e).debug;return(0,a.mG)(this,void 0,void 0,function(){var e,n,r,o,i;return(0,a.Jh)(this,function(l){switch(l.label){case 0:var u;if(!(j()||G()))return[2,void 0];return n=Object.keys(e={abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",'[title="ALIENBOLA" i]',(u=atob)("I0JveC1CYW5uZXItYWRz")],abpvn:[".quangcao","#mobileCatfish",u("LmNsb3NlLWFkcw=="),'[id^="bn_bottom_fixed_"]',"#pmadv"],adBlockFinland:[".mainostila",u("LnNwb25zb3JpdA=="),".ylamainos",u("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),u("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi",u("YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd")],adBlockWarningRemoval:["#adblock-honeypot",".adblocker-root",".wp_adblock_detect",u("LmhlYWRlci1ibG9ja2VkLWFk"),u("I2FkX2Jsb2NrZXI=")],adGuardAnnoyances:[".hs-sosyal","#cookieconsentdiv",'div[class^="app_gdpr"]',".as-oil",'[data-cypress="soft-push-notification-modal"]'],adGuardBase:[".BetterJsPopOverlay",u("I2FkXzMwMFgyNTA="),u("I2Jhbm5lcmZsb2F0MjI="),u("I2NhbXBhaWduLWJhbm5lcg=="),u("I0FkLUNvbnRlbnQ=")],adGuardChinese:[u("LlppX2FkX2FfSA=="),u("YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd"),"#widget-quan",u("YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd"),u("YVtocmVmKj0iLjE5NTZobC5jb20vIl0=")],adGuardFrench:["#pavePub",u("LmFkLWRlc2t0b3AtcmVjdGFuZ2xl"),".mobile_adhesion",".widgetadv",u("LmFkc19iYW4=")],adGuardGerman:['aside[data-portal-id="leaderboard"]'],adGuardJapanese:["#kauli_yad_1",u("YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0="),u("Ll9wb3BJbl9pbmZpbml0ZV9hZA=="),u("LmFkZ29vZ2xl"),u("Ll9faXNib29zdFJldHVybkFk")],adGuardMobile:[u("YW1wLWF1dG8tYWRz"),u("LmFtcF9hZA=="),'amp-embed[type="24smi"]',"#mgid_iframe1",u("I2FkX2ludmlld19hcmVh")],adGuardRussian:[u("YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0="),u("LnJlY2xhbWE="),'div[id^="smi2adblock"]',u("ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd"),"#psyduckpockeball"],adGuardSocial:[u("YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0="),u("YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0="),".etsy-tweet","#inlineShare",".popup-social"],adGuardSpanishPortuguese:["#barraPublicidade","#Publicidade","#publiEspecial","#queTooltip",".cnt-publi"],adGuardTrackingProtection:["#qoo-counter",u("YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=="),u("YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0="),u("YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=="),"#top100counter"],adGuardTurkish:["#backkapat",u("I3Jla2xhbWk="),u("YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0="),u("YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd"),u("YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ==")],bulgarian:[u("dGQjZnJlZW5ldF90YWJsZV9hZHM="),"#ea_intext_div",".lapni-pop-over","#xenium_hot_offers"],easyList:[".yb-floorad",u("LndpZGdldF9wb19hZHNfd2lkZ2V0"),u("LnRyYWZmaWNqdW5reS1hZA=="),".textad_headline",u("LnNwb25zb3JlZC10ZXh0LWxpbmtz")],easyListChina:[u("LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=="),u("LmZyb250cGFnZUFkdk0="),"#taotaole","#aafoot.top_box",".cfa_popup"],easyListCookie:[".ezmob-footer",".cc-CookieWarning","[data-cookie-number]",u("LmF3LWNvb2tpZS1iYW5uZXI="),".sygnal24-gdpr-modal-wrap"],easyListCzechSlovak:["#onlajny-stickers",u("I3Jla2xhbW5pLWJveA=="),u("LnJla2xhbWEtbWVnYWJvYXJk"),".sklik",u("W2lkXj0ic2tsaWtSZWtsYW1hIl0=")],easyListDutch:[u("I2FkdmVydGVudGll"),u("I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=="),".adstekst",u("YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0="),"#semilo-lrectangle"],easyListGermany:["#SSpotIMPopSlider",u("LnNwb25zb3JsaW5rZ3J1ZW4="),u("I3dlcmJ1bmdza3k="),u("I3Jla2xhbWUtcmVjaHRzLW1pdHRl"),u("YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0=")],easyListItaly:[u("LmJveF9hZHZfYW5udW5jaQ=="),".sb-box-pubbliredazionale",u("YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd"),u("YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd"),u("YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ==")],easyListLithuania:[u("LnJla2xhbW9zX3RhcnBhcw=="),u("LnJla2xhbW9zX251b3JvZG9z"),u("aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd"),u("aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd"),u("aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd")],estonian:[u("QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==")],fanboyAnnoyances:["#ac-lre-player",".navigate-to-top","#subscribe_popup",".newsletter_holder","#back-top"],fanboyAntiFacebook:[".util-bar-module-firefly-visible"],fanboyEnhancedTrackers:[".open.pushModal","#issuem-leaky-paywall-articles-zero-remaining-nag","#sovrn_container",'div[class$="-hide"][zoompage-fontsize][style="display: block;"]',".BlockNag__Card"],fanboySocial:["#FollowUs","#meteored_share","#social_follow",".article-sharer",".community__social-desc"],frellwitSwedish:[u("YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=="),u("YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=="),"article.category-samarbete",u("ZGl2LmhvbGlkQWRz"),"ul.adsmodern"],greekAdBlock:[u("QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd"),u("QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=="),u("QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd"),"DIV.agores300","TABLE.advright"],hungarian:["#cemp_doboz",".optimonk-iframe-container",u("LmFkX19tYWlu"),u("W2NsYXNzKj0iR29vZ2xlQWRzIl0="),"#hirdetesek_box"],iDontCareAboutCookies:['.alert-info[data-block-track*="CookieNotice"]',".ModuleTemplateCookieIndicator",".o--cookies--container","#cookies-policy-sticky","#stickyCookieBar"],icelandicAbp:[u("QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==")],latvian:[u("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0OiA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0="),u("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[u("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),u("I2xpdmVyZUFkV3JhcHBlcg=="),u("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),u("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[u("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",u("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),u("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),u("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[u("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),u("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),u("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",u("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),u("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),u("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),u("ZGl2I3NrYXBpZWNfYWQ=")],ro:[u("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),u("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),u("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),u("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd"),'a[href^="/url/"]'],ruAd:[u("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),u("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),u("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",u("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),u("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",u("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]}),[4,function(e){var t;return(0,a.mG)(this,void 0,void 0,function(){var n,r,o,i,l,u,s;return(0,a.Jh)(this,function(a){switch(a.label){case 0:for(r=(n=document).createElement("div"),o=Array(e.length),i={},X(r),l=0;l<e.length;++l)"DIALOG"===(u=function(e){for(var t=function(e){for(var t,n,r="Unexpected syntax '".concat(e,"'"),o=/^\s*([a-z-]*)(.*)$/i.exec(e),a=o[1]||void 0,i={},c=/([.:#][\w-]+|\[.+?\])/gi,l=function(e,t){i[e]=i[e]||[],i[e].push(t)};;){var u=c.exec(o[2]);if(!u)break;var s=u[0];switch(s[0]){case".":l("class",s.slice(1));break;case"#":l("id",s.slice(1));break;case"[":var d=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(s);if(d)l(d[1],null!==(n=null!==(t=d[4])&&void 0!==t?t:d[5])&&void 0!==n?n:"");else throw Error(r);break;default:throw Error(r)}}return[a,i]}(e),n=t[0],r=t[1],o=document.createElement(null!=n?n:"div"),a=0,i=Object.keys(r);a<i.length;a++){var c=i[a],l=r[c].join(" ");"style"===c?function(e,t){for(var n=0,r=t.split(";");n<r.length;n++){var o=r[n],a=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(o);if(a){var i=a[1],c=a[2],l=a[4];e.setProperty(i,c,l||"")}}}(o.style,l):o.setAttribute(c,l)}return o}(e[l])).tagName&&u.show(),X(s=n.createElement("div")),s.appendChild(u),r.appendChild(s),o[l]=u;a.label=1;case 1:if(n.body)return[3,3];return[4,c(50)];case 2:return a.sent(),[3,1];case 3:n.body.appendChild(r);try{for(l=0;l<e.length;++l)o[l].offsetParent||(i[e[l]]=!0)}finally{null===(t=r.parentNode)||void 0===t||t.removeChild(r)}return[2,i]}})})}((i=[]).concat.apply(i,n.map(function(t){return e[t]})))];case 1:return r=l.sent(),t&&function(e,t){for(var n="DOM blockers debug:\n```",r=0,o=Object.keys(e);r<o.length;r++){var a=o[r];n+="\n".concat(a,":");for(var i=0,c=e[a];i<c.length;i++){var l=c[i];n+="\n  ".concat(t[l]?"\uD83D\uDEAB":"➡️"," ").concat(l)}}console.log("".concat(n,"\n```"))}(e,r),(o=n.filter(function(t){var n=e[t];return h(n.map(function(e){return r[e]}))>.6*n.length})).sort(),[2,o]}})})},fontPreferences:function(){var e,t;return e=function(e,t){for(var n={},r={},o=0,a=Object.keys(ee);o<a.length;o++){var i=a[o],c=ee[i],l=c[0],u=void 0===l?{}:l,s=c[1],d=void 0===s?"mmMwWLliI0fiflO&1":s,f=e.createElement("span");f.textContent=d,f.style.whiteSpace="nowrap";for(var m=0,p=Object.keys(u);m<p.length;m++){var h=p[m],v=u[h];void 0!==v&&(f.style[h]=v)}n[i]=f,t.append(e.createElement("br"),f)}for(var g=0,y=Object.keys(ee);g<y.length;g++){var i=y[g];r[i]=n[i].getBoundingClientRect().width}return r},void 0===t&&(t=4e3),O(function(n,r){var o=r.document,i=o.body,c=i.style;c.width="".concat(t,"px"),c.webkitTextSizeAdjust=c.textSizeAdjust="none",P()?i.style.zoom="".concat(1/r.devicePixelRatio):j()&&(i.style.zoom="reset");var l=o.createElement("div");return l.textContent=(0,a.ev)([],Array(t/20<<0),!0).map(function(){return"word"}).join(" "),i.appendChild(l),e(o,i)},'<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">')},audio:function(){var e,t,n,r,o,a;return j()&&T()&&I()||P()&&(e=navigator,t=window,h(["srLatency"in(n=Audio.prototype),"srChannelCount"in n,"devicePosture"in e,(r=t.visualViewport)&&"segments"in r,"getTextInformation"in Image.prototype])>=3)&&(a=(o=window).URLPattern,h(["union"in Set.prototype,"Iterator"in o,a&&"hasRegExpGroups"in a.prototype,"RGB8"in WebGLRenderingContext.prototype])>=3)?-4:function(){var e,t,n=window,r=n.OfflineAudioContext||n.webkitOfflineAudioContext;if(!r)return -2;if(j()&&!Z()&&!(h(["DOMRectList"in(e=window),"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])>=3))return -1;var o=new r(1,5e3,44100),a=o.createOscillator();a.type="triangle",a.frequency.value=1e4;var i=o.createDynamicsCompressor();i.threshold.value=-50,i.knee.value=40,i.ratio.value=12,i.attack.value=0,i.release.value=.25,a.connect(i),i.connect(o.destination),a.start(0);var c=(t=function(){},[new Promise(function(e,n){var r=!1,a=0,i=0;o.oncomplete=function(t){return e(t.renderedBuffer)};var c=function(){setTimeout(function(){return n(N("timeout"))},Math.min(500,i+5e3-Date.now()))},u=function(){try{var e=o.startRendering();switch(l(e)&&d(e),o.state){case"running":i=Date.now(),r&&c();break;case"suspended":!document.hidden&&a++,r&&a>=3?n(N("suspended")):setTimeout(u,500)}}catch(e){n(e)}};u(),t=function(){!r&&(r=!0,i>0&&c())}}),t]),u=c[0],s=c[1],f=d(u.then(function(e){return function(e){for(var t=0,n=0;n<e.length;++n)t+=Math.abs(e[n]);return t}(e.getChannelData(0).subarray(4500))},function(e){if("timeout"===e.name||"suspended"===e.name)return -3;throw e}));return function(){return s(),f}}()},screenFrame:function(){var e=this;if(j()&&T()&&I())return function(){return Promise.resolve(void 0)};var t=function(){var e=this;return!function(){if(void 0===o){var e=function(){var t=z();Y(t)?o=setTimeout(e,2500):(r=t,o=void 0)};e()}}(),function(){return(0,a.mG)(e,void 0,void 0,function(){var e;return(0,a.Jh)(this,function(t){switch(t.label){case 0:var n,o;if(!Y(e=z()))return[3,2];if(r)return[2,(0,a.ev)([],r,!0)];if(!((n=document).fullscreenElement||n.msFullscreenElement||n.mozFullScreenElement||n.webkitFullscreenElement))return[3,2];return[4,((o=document).exitFullscreen||o.msExitFullscreen||o.mozCancelFullScreen||o.webkitExitFullscreen).call(o)];case 1:t.sent(),e=z(),t.label=2;case 2:return Y(e)||(r=e),[2,e]}})})}}();return function(){return(0,a.mG)(e,void 0,void 0,function(){var e,n;return(0,a.Jh)(this,function(r){switch(r.label){case 0:return[4,t()];case 1:return e=r.sent(),[2,[(n=function(e){return null===e?null:v(e,10)})(e[0]),n(e[1]),n(e[2]),n(e[3])]]}})})}},canvas:function(){var e,t,n,r,o,a,i,c,l,u,s;return e=j()&&T()&&I(),a=!1,c=((t=document.createElement("canvas")).width=1,t.height=1,i=[t,t.getContext("2d")])[0],(l=i[1])&&c.toDataURL?(l.rect(0,0,10,10),l.rect(2,2,6,6),a=!l.isPointInPath(5,5,"evenodd"),e?r=o="skipped":(r=(c.width=240,c.height=60,l.textBaseline="alphabetic",l.fillStyle="#f60",l.fillRect(100,1,62,20),l.fillStyle="#069",l.font='11pt "Times New Roman"',u="Cwm fjordbank gly ".concat(String.fromCharCode(55357,56835)),l.fillText(u,2,15),l.fillStyle="rgba(102, 204, 0, 0.2)",l.font="18pt Arial",l.fillText(u,4,45),n=(s=H(c))!==H(c)?["unstable","unstable"]:(function(e,t){e.width=122,e.height=110,t.globalCompositeOperation="multiply";for(var n=0,r=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];n<r.length;n++){var o=r[n],a=o[0],i=o[1],c=o[2];t.fillStyle=a,t.beginPath(),t.arc(i,c,40,0,2*Math.PI,!0),t.closePath(),t.fill()}t.fillStyle="#f9c",t.arc(60,60,60,0,2*Math.PI,!0),t.arc(60,60,20,0,2*Math.PI,!0),t.fill("evenodd")}(c,l),[H(c),s]))[0],o=n[1])):r=o="unsupported",{winding:a,geometry:r,text:o}},osCpu:function(){return navigator.oscpu},languages:function(){var e,t=navigator,n=[],r=t.language||t.userLanguage||t.browserLanguage||t.systemLanguage;if(void 0!==r&&n.push([r]),Array.isArray(t.languages))P()&&h([!("MediaSettingsRange"in(e=window)),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])>=3||n.push(t.languages);else if("string"==typeof t.languages){var o=t.languages;o&&n.push(o.split(","))}return n},colorDepth:function(){return window.screen.colorDepth},deviceMemory:function(){return p(m(navigator.deviceMemory),void 0)},screenResolution:function(){if(!(j()&&T()&&I())){var e,t,n;return e=screen,(n=[(t=function(e){return p(f(e),null)})(e.width),t(e.height)]).sort().reverse(),n}},hardwareConcurrency:function(){return p(f(navigator.hardwareConcurrency),void 0)},timezone:function(){var e,t,n=null===(t=window.Intl)||void 0===t?void 0:t.DateTimeFormat;if(n){var r=new n().resolvedOptions().timeZone;if(r)return r}var o=-Math.max(m(new Date(e=new Date().getFullYear(),0,1).getTimezoneOffset()),m(new Date(e,6,1).getTimezoneOffset()));return"UTC".concat(o>=0?"+":"").concat(o)},sessionStorage:function(){try{return!!window.sessionStorage}catch(e){return!0}},localStorage:function(){try{return!!window.localStorage}catch(e){return!0}},indexedDB:function(){var e,t;if(!(C()||h(["msWriteProfilerMark"in(e=window),"MSStream"in e,"msLaunchUri"in(t=navigator),"msSaveBlob"in t])>=3&&!C()))try{return!!window.indexedDB}catch(e){return!0}},openDatabase:function(){return!!window.openDatabase},cpuClass:function(){return navigator.cpuClass},platform:function(){var e=navigator.platform;return"MacIntel"===e&&j()&&!Z()?!function(){if("iPad"===navigator.platform)return!0;var e=screen,t=e.width/e.height;return h(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,t>.65&&t<1.53])>=2}()?"iPhone":"iPad":e},plugins:function(){var e=navigator.plugins;if(e){for(var t=[],n=0;n<e.length;++n){var r=e[n];if(r){for(var o=[],a=0;a<r.length;++a){var i=r[a];o.push({type:i.type,suffixes:i.suffixes})}t.push({name:r.name,description:r.description,mimeTypes:o})}}return t}},touchSupport:function(){var e,t=navigator,n=0;void 0!==t.maxTouchPoints?n=f(t.maxTouchPoints):void 0!==t.msMaxTouchPoints&&(n=t.msMaxTouchPoints);try{document.createEvent("TouchEvent"),e=!0}catch(t){e=!1}return{maxTouchPoints:n,touchEvent:e,touchStart:"ontouchstart"in window}},vendor:function(){return navigator.vendor||""},vendorFlavors:function(){for(var e=[],t=0,n=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];t<n.length;t++){var r=n[t],o=window[r];o&&"object"==typeof o&&e.push(r)}return e.sort()},cookiesEnabled:function(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var t=-1!==e.cookie.indexOf("cookietest=");return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",t}catch(e){return!1}},colorGamut:function(){for(var e=0,t=["rec2020","p3","srgb"];e<t.length;e++){var n=t[e];if(matchMedia("(color-gamut: ".concat(n,")")).matches)return n}},invertedColors:function(){return!!B("inverted")||!B("none")&&void 0},forcedColors:function(){return!!_("active")||!_("none")&&void 0},monochrome:function(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=100;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw Error("Too high value")}},contrast:function(){return J("no-preference")?0:J("high")||J("more")?1:J("low")||J("less")?-1:J("forced")?10:void 0},reducedMotion:function(){return!!U("reduce")||!U("no-preference")&&void 0},reducedTransparency:function(){return!!Q("reduce")||!Q("no-preference")&&void 0},hdr:function(){return!!K("high")||!K("standard")&&void 0},math:function(){var e=$.acos||q,t=$.acosh||q,n=$.asin||q,r=$.asinh||q,o=$.atanh||q,a=$.atan||q,i=$.sin||q,c=$.sinh||q,l=$.cos||q,u=$.cosh||q,s=$.tan||q,d=$.tanh||q,f=$.exp||q,m=$.expm1||q,p=$.log1p||q;return{acos:e(.12312423423423424),acosh:t(1e308),acoshPf:$.log(1e154+$.sqrt(1e154*1e154-1)),asin:n(.12312423423423424),asinh:r(1),asinhPf:$.log(1+$.sqrt(2)),atanh:o(.5),atanhPf:$.log(3)/2,atan:a(.5),sin:i(-1e300),sinh:c(1),sinhPf:$.exp(1)-1/$.exp(1)/2,cos:l(10.000000000123),cosh:u(1),coshPf:($.exp(1)+1/$.exp(1))/2,tan:s(-1e300),tanh:d(1),tanhPf:($.exp(2)-1)/($.exp(2)+1),exp:f(1),expm1:m(1),expm1Pf:$.exp(1)-1,log1p:p(10),log1pPf:$.log(11),powPI:$.pow($.PI,-100)}},pdfViewerEnabled:function(){return navigator.pdfViewerEnabled},architecture:function(){var e=new Float32Array(1),t=new Uint8Array(e.buffer);return e[0]=1/0,e[0]=e[0]-e[0],t[3]},applePay:function(){var e=window.ApplePaySession;if("function"!=typeof(null==e?void 0:e.canMakePayments))return -1;if(et())return -3;try{return e.canMakePayments()?1:0}catch(e){return function(e){if(e instanceof Error&&"InvalidAccessError"===e.name&&/\bfrom\b.*\binsecure\b/i.test(e.message))return -2;throw e}(e)}},privateClickMeasurement:function(){var e,t=document.createElement("a"),n=null!==(e=t.attributionSourceId)&&void 0!==e?e:t.attributionsourceid;return void 0===n?void 0:String(n)},audioBaseLatency:function(){if(!(G()||j()))return -2;if(!window.AudioContext)return -1;var e=new AudioContext().baseLatency;return null==e?-1:isFinite(e)?e:-3},dateTimeLocale:function(){if(!window.Intl)return -1;var e=window.Intl.DateTimeFormat;if(!e)return -2;var t=e().resolvedOptions().locale;return t||""===t?t:-3},webGlBasics:function(e){var t,n,r,o,a,i,c=ec(e.cache);if(!c)return -1;if(!es(c))return -2;var l=F()?null:c.getExtension(ei);return{version:(null===(t=c.getParameter(c.VERSION))||void 0===t?void 0:t.toString())||"",vendor:(null===(n=c.getParameter(c.VENDOR))||void 0===n?void 0:n.toString())||"",vendorUnmasked:l?null===(r=c.getParameter(l.UNMASKED_VENDOR_WEBGL))||void 0===r?void 0:r.toString():"",renderer:(null===(o=c.getParameter(c.RENDERER))||void 0===o?void 0:o.toString())||"",rendererUnmasked:l?null===(a=c.getParameter(l.UNMASKED_RENDERER_WEBGL))||void 0===a?void 0:a.toString():"",shadingLanguageVersion:(null===(i=c.getParameter(c.SHADING_LANGUAGE_VERSION))||void 0===i?void 0:i.toString())||""}},webGlExtensions:function(e){var t=ec(e.cache);if(!t)return -1;if(!es(t))return -2;var n=t.getSupportedExtensions(),r=t.getContextAttributes(),o=[],a=[],i=[],c=[],l=[];if(r)for(var u=0,s=Object.keys(r);u<s.length;u++){var d=s[u];a.push("".concat(d,"=").concat(r[d]))}for(var f=el(t),m=0;m<f.length;m++){var p=f[m],h=t[p];i.push("".concat(p,"=").concat(h).concat(en.has(h)?"=".concat(t.getParameter(h)):""))}if(n)for(var v=0;v<n.length;v++){var g=n[v];if(!(g===ei&&F()||"WEBGL_polygon_mode"===g&&(P()||j()))){var y=t.getExtension(g);if(!y){o.push(g);continue}for(var b=0,w=el(y);b<w.length;b++){var p=w[b],h=y[p];c.push("".concat(p,"=").concat(h).concat(er.has(h)?"=".concat(t.getParameter(h)):""))}}}for(var k=0;k<eo.length;k++)for(var L=eo[k],S=0;S<ea.length;S++){var E=ea[S],M=function(e,t,n){var r=e.getShaderPrecisionFormat(e[t],e[n]);return r?[r.rangeMin,r.rangeMax,r.precision]:[]}(t,L,E);l.push("".concat(L,".").concat(E,"=").concat(M.join(",")))}return c.sort(),i.sort(),{contextAttributes:a,parameters:i,shaderPrecisions:l,extensions:n,extensionParameters:c,unsupportedExtensions:o}}};function ef(e){return JSON.stringify(e,function(e,t){if(t instanceof Error){var n;return(0,a.pi)({name:t.name,message:t.message,stack:null===(n=t.stack)||void 0===n?void 0:n.split("\n")},t)}return t},2)}function em(e){return function(e,t){var n,r=function(e){for(var t=new Uint8Array(e.length),n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r>127)return new TextEncoder().encode(e);t[n]=r}return t}(e);t=t||0;var o=[0,r.length],a=o[1]%16,i=o[1]-a,c=[0,t],l=[0,t],u=[0,0],s=[0,0];for(n=0;n<i;n+=16)u[0]=r[n+4]|r[n+5]<<8|r[n+6]<<16|r[n+7]<<24,u[1]=r[n]|r[n+1]<<8|r[n+2]<<16|r[n+3]<<24,s[0]=r[n+12]|r[n+13]<<8|r[n+14]<<16|r[n+15]<<24,s[1]=r[n+8]|r[n+9]<<8|r[n+10]<<16|r[n+11]<<24,y(u,M),b(u,31),y(u,x),k(c,u),b(c,27),g(c,l),y(c,W),g(c,V),y(s,x),b(s,33),y(s,M),k(l,s),b(l,31),g(l,c),y(l,W),g(l,R);u[0]=0,u[1]=0,s[0]=0,s[1]=0;var d=[0,0];switch(a){case 15:d[1]=r[n+14],w(d,48),k(s,d);case 14:d[1]=r[n+13],w(d,40),k(s,d);case 13:d[1]=r[n+12],w(d,32),k(s,d);case 12:d[1]=r[n+11],w(d,24),k(s,d);case 11:d[1]=r[n+10],w(d,16),k(s,d);case 10:d[1]=r[n+9],w(d,8),k(s,d);case 9:d[1]=r[n+8],k(s,d),y(s,x),b(s,33),y(s,M),k(l,s);case 8:d[1]=r[n+7],w(d,56),k(u,d);case 7:d[1]=r[n+6],w(d,48),k(u,d);case 6:d[1]=r[n+5],w(d,40),k(u,d);case 5:d[1]=r[n+4],w(d,32),k(u,d);case 4:d[1]=r[n+3],w(d,24),k(u,d);case 3:d[1]=r[n+2],w(d,16),k(u,d);case 2:d[1]=r[n+1],w(d,8),k(u,d);case 1:d[1]=r[n],k(u,d),y(u,M),b(u,31),y(u,x),k(c,u)}return k(c,o),k(l,o),g(c,l),g(l,c),E(c),E(l),g(c,l),g(l,c),("00000000"+(c[0]>>>0).toString(16)).slice(-8)+("00000000"+(c[1]>>>0).toString(16)).slice(-8)+("00000000"+(l[0]>>>0).toString(16)).slice(-8)+("00000000"+(l[1]>>>0).toString(16)).slice(-8)}(function(e){for(var t="",n=0,r=Object.keys(e).sort();n<r.length;n++){var o=r[n],a=e[o],i="error"in a?"error":JSON.stringify(a.value);t+="".concat(t?"|":"").concat(o.replace(/([:|\\])/g,"\\$1"),":").concat(i)}return t}(e))}var ep={load:function(e){var t;return void 0===e&&(e={}),(0,a.mG)(this,void 0,void 0,function(){var n,r;return(0,a.Jh)(this,function(o){var l,f,m,p,h,g,y,b,w,k,L,S;switch(o.label){case 0:return(null===(t=e.monitoring)||void 0===t||t)&&function(){if(!(window.__fpjs_d_m||Math.random()>=.001))try{var e=new XMLHttpRequest;e.open("get","https://m1.openfpcdn.io/fingerprintjs/v".concat(i,"/npm-monitoring"),!0),e.send()}catch(e){console.error(e)}}(),n=e.delayFallback,r=e.debug,[4,(void 0===(l=n)&&(l=50),f=l,m=2*l,(p=window.requestIdleCallback)?new Promise(function(e){return p.call(window,function(){return e()},{timeout:m})}):c(Math.min(f,m)))];case 1:return o.sent(),h={cache:{},debug:r},g=[],w=d(s(b=Object.keys(ed).filter(function(e){return!function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return!0;return!1}(g,e)}),function(e){var t,n;return t=ed[e],n=d(new Promise(function(e){var n=Date.now();u(t.bind(null,h),function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o=Date.now()-n;if(!t[0])return e(function(){return{error:t[1],duration:o}});var a=t[1];if("function"!=typeof a)return e(function(){return{value:a,duration:o}});e(function(){return new Promise(function(e){var t=Date.now();u(a,function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=o+Date.now()-t;if(!n[0])return e({error:n[1],duration:a});e({value:n[1],duration:a})})})})})})),function(){return n.then(function(e){return e()})}},void 0)),[2,(k=function(){return(0,a.mG)(this,void 0,void 0,function(){var e,t,n;return(0,a.Jh)(this,function(r){switch(r.label){case 0:return[4,w];case 1:return[4,s(r.sent(),function(e){return d(e())},y)];case 2:return[4,Promise.all(r.sent())];case 3:for(n=0,e=r.sent(),t={};n<b.length;++n)t[b[n]]=e[n];return[2,t]}})})},L=r,S=Date.now(),{get:function(e){return(0,a.mG)(this,void 0,void 0,function(){var t,n,r;return(0,a.Jh)(this,function(o){switch(o.label){case 0:return t=Date.now(),[4,k()];case 1:var a,c,l,u;return r={get visitorId(){return void 0===u&&(u=em(this.components)),u},set visitorId(visitorId){u=visitorId},confidence:(l=v(.99+.01*(c=function(e){if(G())return .4;if(j())return Z()&&!(T()&&I())?.5:.3;var t="value"in e.platform?e.platform.value:"";return/^Win/.test(t)?.6:/^Mac/.test(t)?.5:.7}(a=n=o.sent())),1e-4),{score:c,comment:"$ if upgrade to Pro: https://fpjs.dev/pro".replace(/\$/g,"".concat(l))}),components:a,version:i},(L||(null==e?void 0:e.debug))&&console.log("Copy the text below to get the debug data:\n\n```\nversion: ".concat(r.version,"\nuserAgent: ").concat(navigator.userAgent,"\ntimeBetweenLoadAndGet: ").concat(t-S,"\nvisitorId: ").concat(r.visitorId,"\ncomponents: ").concat(ef(n),"\n```")),[2,r]}})})}})]}})})},hashComponents:em,componentsToDebugString:ef}},27168:function(e,t,n){n.d(t,{Z:function(){return h}});var r=n(74677),o=n(2265),a=n(40718),i=n.n(a),c=["variant","color","size"],l=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M14.55 22.42c-.33 0-.64-.22-.73-.56-.1-.4.13-.81.53-.92a9.256 9.256 0 006.89-8.95c0-5.1-4.15-9.25-9.25-9.25-4.33 0-7.17 2.53-8.5 4.06h2.94c.41 0 .75.34.75.75s-.32.76-.74.76H2.01c-.07 0-.14-.01-.21-.03a.899.899 0 01-.24-.12.659.659 0 01-.21-.23c-.05-.1-.09-.2-.1-.31V3c0-.41.34-.75.75-.75s.75.34.75.75v2.39C4.38 3.64 7.45 1.25 12 1.25c5.93 0 10.75 4.82 10.75 10.75 0 4.88-3.29 9.16-8.01 10.4-.06.01-.13.02-.19.02zM11.29 22.73c-.02 0-.05 0-.07-.01-1.07-.07-2.12-.31-3.12-.7a.751.751 0 01-.43-.97c.15-.38.59-.58.97-.43.86.34 1.77.54 2.69.61h.01c.4.02.7.36.7.76v.04a.76.76 0 01-.75.7zm-5.51-2.15c-.17 0-.33-.05-.47-.16-.84-.67-1.57-1.46-2.18-2.35a.73.73 0 01.19-1.04.76.76 0 011.04.19c.53.77 1.16 1.45 1.89 2.02.17.14.28.35.28.58 0 .17-.05.34-.16.48-.14.18-.36.28-.59.28zM2.44 15.7c-.33 0-.62-.21-.71-.52-.32-1.03-.48-2.1-.48-3.18 0-.41.34-.75.75-.75s.75.34.75.75c0 .93.14 1.85.41 2.73.02.07.03.15.03.23 0 .33-.21.61-.52.71-.08.02-.15.03-.23.03z"}),o.createElement("path",{fill:t,d:"M12 16a4 4 0 100-8 4 4 0 000 8z"}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M14.55 21.67C18.84 20.54 22 16.64 22 12c0-5.52-4.44-10-10-10C5.33 2 2 7.56 2 7.56m0 0V3m0 4.56H6.44"}),o.createElement("path",{stroke:t,strokeDasharray:"3 3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M2 12c0 5.52 4.48 10 10 10"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M14.55 22.42c-.33 0-.64-.22-.73-.56-.1-.4.13-.81.53-.92a9.256 9.256 0 006.89-8.95c0-5.1-4.15-9.25-9.25-9.25-4.33 0-7.17 2.53-8.5 4.06h2.94c.41 0 .75.34.75.75s-.32.76-.74.76H2.01c-.07 0-.14-.01-.21-.03a.899.899 0 01-.24-.12.659.659 0 01-.21-.23c-.05-.1-.09-.2-.1-.31V3c0-.41.34-.75.75-.75s.75.34.75.75v2.39C4.38 3.64 7.45 1.25 12 1.25c5.93 0 10.75 4.82 10.75 10.75 0 4.88-3.29 9.16-8.01 10.4-.06.01-.13.02-.19.02z",opacity:".4"}),o.createElement("path",{fill:t,d:"M11.29 22.73c-.02 0-.05 0-.07-.01-1.07-.07-2.12-.31-3.12-.7a.751.751 0 01-.43-.97c.15-.38.59-.58.97-.43.86.34 1.77.54 2.69.61h.01c.4.02.7.36.7.76v.04a.76.76 0 01-.75.7zm-5.51-2.15c-.17 0-.33-.05-.47-.16-.84-.67-1.57-1.46-2.18-2.35a.73.73 0 01.19-1.04.76.76 0 011.04.19c.53.77 1.16 1.45 1.89 2.02.17.14.28.35.28.58 0 .17-.05.34-.16.48-.14.18-.36.28-.59.28zM2.44 15.7c-.33 0-.62-.21-.71-.52-.32-1.03-.48-2.1-.48-3.18 0-.41.34-.75.75-.75s.75.34.75.75c0 .93.14 1.85.41 2.73.02.07.03.15.03.23 0 .33-.21.61-.52.71-.08.02-.15.03-.23.03z"}),o.createElement("path",{fill:t,d:"M12 16a4 4 0 100-8 4 4 0 000 8z",opacity:".4"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M14.55 21.67C18.84 20.54 22 16.64 22 12c0-5.52-4.44-10-10-10C5.33 2 2 7.56 2 7.56m0 0V3m0 4.56H6.44"}),o.createElement("path",{stroke:t,strokeDasharray:"3 3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M2 12c0 5.52 4.48 10 10 10"}))},f=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M14.55 22.42c-.33 0-.64-.22-.73-.56-.11-.4.13-.81.54-.92a9.256 9.256 0 006.89-8.95c0-5.1-4.15-9.25-9.25-9.25-4.33 0-7.17 2.53-8.5 4.06h2.94a.755.755 0 010 1.51H2.01c-.05 0-.14-.01-.21-.03a.899.899 0 01-.24-.12.659.659 0 01-.21-.23.808.808 0 01-.1-.31V3c0-.41.34-.75.75-.75s.75.34.75.75v2.39C4.38 3.64 7.45 1.25 12 1.25c5.93 0 10.75 4.82 10.75 10.75 0 4.88-3.29 9.16-8.01 10.4-.06.01-.13.02-.19.02zM11.29 22.73c-.02 0-.04-.01-.05-.01-1.08-.07-2.14-.31-3.14-.7a.747.747 0 01-.43-.97c.15-.38.6-.57.97-.43.87.34 1.78.54 2.7.61.39.02.7.36.7.76l-.01.04c-.02.39-.35.7-.74.7zm-5.51-2.15c-.17 0-.33-.06-.47-.16-.84-.68-1.58-1.47-2.18-2.35a.73.73 0 01.19-1.04.77.77 0 011.04.18v.01c.01.01.02.03.03.04a9.21 9.21 0 001.86 1.98c.17.14.28.35.28.58 0 .17-.05.34-.16.48-.15.18-.36.28-.59.28zM2.44 15.7c-.33 0-.62-.21-.71-.52-.32-1.03-.48-2.1-.48-3.18v-.01c.01-.41.34-.74.75-.74s.75.34.75.75c0 .94.14 1.86.41 2.73.02.08.03.15.03.23a.747.747 0 01-.75.74z"}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M14.55 21.67C18.84 20.54 22 16.64 22 12c0-5.52-4.44-10-10-10C5.33 2 2 7.56 2 7.56m0 0V3m0 4.56H6.44"}),o.createElement("path",{stroke:t,strokeDasharray:"3 3",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M2 12c0 5.52 4.48 10 10 10",opacity:".4"}))},p=function(e,t){switch(e){case"Bold":return o.createElement(l,{color:t});case"Broken":return o.createElement(u,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(f,{color:t});case"TwoTone":return o.createElement(m,{color:t})}},h=(0,o.forwardRef)(function(e,t){var n=e.variant,a=e.color,i=e.size,l=(0,r._)(e,c);return o.createElement("svg",(0,r.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(n,a))});h.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="Refresh2"},19571:function(e,t,n){n.d(t,{Z:function(){return h}});var r=n(74677),o=n(2265),a=n(40718),i=n.n(a),c=["variant","color","size"],l=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm4.78 7.7-5.67 5.67a.75.75 0 0 1-1.06 0l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.76 0 1.06Z",fill:t}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85M15 10.38l1.12-1.13",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"m7.88 12 2.74 2.75 2.55-2.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),o.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:t}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"m7.75 12 2.83 2.83 5.67-5.66",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),o.createElement("path",{d:"M10.58 15.582a.75.75 0 0 1-.53-.22l-2.83-2.83a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 5.14-5.14c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-5.67 5.67a.75.75 0 0 1-.53.22Z",fill:t}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{opacity:".34",d:"m7.75 12.002 2.83 2.83 5.67-5.66",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return o.createElement(l,{color:t});case"Broken":return o.createElement(u,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(f,{color:t});case"TwoTone":return o.createElement(m,{color:t})}},h=(0,o.forwardRef)(function(e,t){var n=e.variant,a=e.color,i=e.size,l=(0,r._)(e,c);return o.createElement("svg",(0,r.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),p(n,a))});h.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="TickCircle"},79205:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(2265);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:c=2,absoluteStrokeWidth:l,className:u="",children:s,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:n,strokeWidth:l?24*Number(c)/Number(o):c,className:a("lucide",u),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:l,...u}=n;return(0,r.createElement)(c,{ref:i,iconNode:t,className:a("lucide-".concat(o(e)),l),...u})});return n.displayName="".concat(e),n}},30401:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},10407:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},62319:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]])},99376:function(e,t,n){var r=n(35475);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useSelectedLayoutSegment")&&n.d(t,{useSelectedLayoutSegment:function(){return r.useSelectedLayoutSegment}}),n.o(r,"useSelectedLayoutSegments")&&n.d(t,{useSelectedLayoutSegments:function(){return r.useSelectedLayoutSegments}})},12119:function(e,t,n){Object.defineProperty(t,"$",{enumerable:!0,get:function(){return o}});let r=n(83079);function o(e){let{createServerReference:t}=n(6671);return t(e,r.callServer)}},9270:function(e,t,n){n.d(t,{fC:function(){return E},z$:function(){return M}});var r=n(2265),o=n(98575),a=n(73966),i=n(6741),c=n(80886),l=n(6718),u=n(90420),s=n(71599),d=n(66840),f=n(57437),m="Checkbox",[p,h]=(0,a.b)(m),[v,g]=p(m),y=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:a,checked:l,defaultChecked:u,required:s,disabled:p,value:h="on",onCheckedChange:g,form:y,...b}=e,[w,E]=r.useState(null),M=(0,o.e)(t,e=>E(e)),x=r.useRef(!1),W=!w||y||!!w.closest("form"),[V,R]=(0,c.T)({prop:l,defaultProp:null!=u&&u,onChange:g,caller:m}),C=r.useRef(V);return r.useEffect(()=>{let e=null==w?void 0:w.form;if(e){let t=()=>R(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[w,R]),(0,f.jsxs)(v,{scope:n,state:V,disabled:p,children:[(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":L(V)?"mixed":V,"aria-required":s,"data-state":S(V),"data-disabled":p?"":void 0,disabled:p,value:h,...b,ref:M,onKeyDown:(0,i.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(e.onClick,e=>{R(e=>!!L(e)||!e),W&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),W&&(0,f.jsx)(k,{control:w,bubbles:!x.current,name:a,value:h,checked:V,required:s,disabled:p,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!L(u)&&u})]})});y.displayName=m;var b="CheckboxIndicator",w=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,a=g(b,n);return(0,f.jsx)(s.z,{present:r||L(a.state)||!0===a.state,children:(0,f.jsx)(d.WV.span,{"data-state":S(a.state),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,control:a,checked:i,bubbles:c=!0,defaultChecked:s,...m}=e,p=r.useRef(null),h=(0,o.e)(p,t),v=(0,l.D)(i),g=(0,u.t)(a);r.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==i&&t){let n=new Event("click",{bubbles:c});e.indeterminate=L(i),t.call(e,!L(i)&&i),e.dispatchEvent(n)}},[v,i,c]);let y=r.useRef(!L(i)&&i);return(0,f.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=s?s:y.current,...m,tabIndex:-1,ref:h,style:{...m.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function L(e){return"indeterminate"===e}function S(e){return L(e)?"indeterminate":e?"checked":"unchecked"}k.displayName="CheckboxBubbleInput";var E=y,M=w},71599:function(e,t,n){n.d(t,{z:function(){return i}});var r=n(2265),o=n(98575),a=n(61188),i=e=>{var t,n;let i,l;let{present:u,children:s}=e,d=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef(null),u=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=c(l.current);s.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let t=l.current,n=u.current;if(n!==e){let r=s.current,o=c(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,a.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=c(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(s.current=c(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(u),f="function"==typeof s?s({present:d.isPresent}):r.Children.only(s),m=(0,o.e)(d.ref,(i=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in i&&i.isReactWarning?f.ref:(i=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in i&&i.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?r.cloneElement(f,{ref:m}):null};function c(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},55156:function(e,t,n){n.d(t,{f:function(){return u}});var r=n(2265),o=n(66840),a=n(57437),i="horizontal",c=["horizontal","vertical"],l=r.forwardRef((e,t)=>{let{decorative:n,orientation:r=i,...l}=e,u=c.includes(r)?r:i;return(0,a.jsx)(o.WV.div,{"data-orientation":u,...n?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...l,ref:t})});l.displayName="Separator";var u=l},6718:function(e,t,n){n.d(t,{D:function(){return o}});var r=n(2265);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90420:function(e,t,n){n.d(t,{t:function(){return a}});var r=n(2265),o=n(61188);function a(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},35934:function(e,t,n){n.d(t,{VM:function(){return p},uZ:function(){return h}});var r=n(2265),o=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,c=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))l.call(t,n)&&s(e,n,t[n]);if(c)for(var n of c(t))u.call(t,n)&&s(e,n,t[n]);return e},f=(e,t)=>a(e,i(t)),m=(e,t)=>{var n={};for(var r in e)l.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&c)for(var r of c(e))0>t.indexOf(r)&&u.call(e,r)&&(n[r]=e[r]);return n},p=r.createContext({}),h=r.forwardRef((e,t)=>{let n;var o,a,i,c,l,{value:u,onChange:s,maxLength:h,textAlign:y="left",pattern:b,placeholder:w,inputMode:k="numeric",onComplete:L,pushPasswordManagerStrategy:S="increase-width",pasteTransformer:E,containerClassName:M,noScriptCSSFallback:x=g,render:W,children:V}=e,R=m(e,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]);let[C,P]=r.useState("string"==typeof R.defaultValue?R.defaultValue:""),j=null!=u?u:C,Z=(n=r.useRef(),r.useEffect(()=>{n.current=j}),n.current),I=r.useCallback(e=>{null==s||s(e),P(e)},[s]),F=r.useMemo(()=>b?"string"==typeof b?new RegExp(b):b:null,[b]),T=r.useRef(null),G=r.useRef(null),N=r.useRef({value:j,onChange:I,isIOS:"undefined"!=typeof window&&(null==(a=null==(o=null==window?void 0:window.CSS)?void 0:o.supports)?void 0:a.call(o,"-webkit-touch-callout","none"))}),O=r.useRef({prev:[null==(i=T.current)?void 0:i.selectionStart,null==(c=T.current)?void 0:c.selectionEnd,null==(l=T.current)?void 0:l.selectionDirection]});r.useImperativeHandle(t,()=>T.current,[]),r.useEffect(()=>{let e=T.current,t=G.current;if(!e||!t)return;function n(){if(document.activeElement!==e){X(null),_(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,o=e.maxLength,a=e.value,i=O.current.prev,c=-1,l=-1,u;if(0!==a.length&&null!==t&&null!==n){let e=t===n,r=t===a.length&&a.length<o;if(e&&!r){if(0===t)c=0,l=1,u="forward";else if(t===o)c=t-1,l=t,u="backward";else if(o>1&&a.length>1){let e=0;if(null!==i[0]&&null!==i[1]){u=t<i[1]?"backward":"forward";let n=i[0]===i[1]&&i[0]<o;"backward"!==u||n||(e=-1)}c=e+t,l=e+t+1}}-1!==c&&-1!==l&&c!==l&&T.current.setSelectionRange(c,l,u)}let s=-1!==c?c:t,d=-1!==l?l:n,f=null!=u?u:r;X(s),_(d),O.current.prev=[s,d,f]}if(N.current.value!==e.value&&N.current.onChange(e.value),O.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&z(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";v(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),v(e.sheet,`[data-input-otp]:autofill { ${t} }`),v(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),v(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),v(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let o=new ResizeObserver(r);return o.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),o.disconnect()}},[]);let[A,D]=r.useState(!1),[H,z]=r.useState(!1),[Y,X]=r.useState(null),[B,_]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=T.current)||e.dispatchEvent(new Event("input"));let o=null==(t=T.current)?void 0:t.selectionStart,a=null==(n=T.current)?void 0:n.selectionEnd,i=null==(r=T.current)?void 0:r.selectionDirection;null!==o&&null!==a&&(X(o),_(a),O.current.prev=[o,a,i])},0),setTimeout(e,10),setTimeout(e,50)},[j,H]),r.useEffect(()=>{void 0!==Z&&j!==Z&&Z.length<h&&j.length===h&&(null==L||L(j))},[h,L,Z,j]);let J=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:o}){let[a,i]=r.useState(!1),[c,l]=r.useState(!1),[u,s]=r.useState(!1),d=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&a&&c,[a,c,n]),f=r.useCallback(()=>{let r=e.current,o=t.current;if(!r||!o||u||"none"===n)return;let a=r.getBoundingClientRect().left+r.offsetWidth,c=r.getBoundingClientRect().top+r.offsetHeight/2;0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(a-18,c)===r||(i(!0),s(!0))},[e,t,u,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){l(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let o=setInterval(r,1e3);return()=>{clearInterval(o)}},[e,n]),r.useEffect(()=>{let e=o||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),a=setTimeout(f,2e3),i=setTimeout(f,5e3),c=setTimeout(()=>{s(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(a),clearTimeout(i),clearTimeout(c)}},[t,o,n,f]),{hasPWMBadge:a,willPushPWMBadge:d,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:G,inputRef:T,pushPasswordManagerStrategy:S,isFocused:H}),U=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,h);if(t.length>0&&F&&!F.test(t)){e.preventDefault();return}"string"==typeof Z&&t.length<Z.length&&document.dispatchEvent(new Event("selectionchange")),I(t)},[h,I,Z,F]),Q=r.useCallback(()=>{var e;if(T.current){let t=Math.min(T.current.value.length,h-1),n=T.current.value.length;null==(e=T.current)||e.setSelectionRange(t,n),X(t),_(n)}z(!0)},[h]),K=r.useCallback(e=>{var t,n;let r=T.current;if(!E&&(!N.current.isIOS||!e.clipboardData||!r))return;let o=e.clipboardData.getData("text/plain"),a=E?E(o):o;e.preventDefault();let i=null==(t=T.current)?void 0:t.selectionStart,c=null==(n=T.current)?void 0:n.selectionEnd,l=(i!==c?j.slice(0,i)+a+j.slice(c):j.slice(0,i)+a+j.slice(i)).slice(0,h);if(l.length>0&&F&&!F.test(l))return;r.value=l,I(l);let u=Math.min(l.length,h-1),s=l.length;r.setSelectionRange(u,s),X(u),_(s)},[h,I,F,j]),$=r.useMemo(()=>({position:"relative",cursor:R.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[R.disabled]),q=r.useMemo(()=>({position:"absolute",inset:0,width:J.willPushPWMBadge?`calc(100% + ${J.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:J.willPushPWMBadge?`inset(0 ${J.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:y,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[J.PWM_BADGE_SPACE_WIDTH,J.willPushPWMBadge,y]),ee=r.useMemo(()=>r.createElement("input",f(d({autoComplete:R.autoComplete||"one-time-code"},R),{"data-input-otp":!0,"data-input-otp-placeholder-shown":0===j.length||void 0,"data-input-otp-mss":Y,"data-input-otp-mse":B,inputMode:k,pattern:null==F?void 0:F.source,"aria-placeholder":w,style:q,maxLength:h,value:j,ref:T,onPaste:e=>{var t;K(e),null==(t=R.onPaste)||t.call(R,e)},onChange:U,onMouseOver:e=>{var t;D(!0),null==(t=R.onMouseOver)||t.call(R,e)},onMouseLeave:e=>{var t;D(!1),null==(t=R.onMouseLeave)||t.call(R,e)},onFocus:e=>{var t;Q(),null==(t=R.onFocus)||t.call(R,e)},onBlur:e=>{var t;z(!1),null==(t=R.onBlur)||t.call(R,e)}})),[U,Q,K,k,q,h,B,Y,R,null==F?void 0:F.source,j]),et=r.useMemo(()=>({slots:Array.from({length:h}).map((e,t)=>{var n;let r=H&&null!==Y&&null!==B&&(Y===B&&t===Y||t>=Y&&t<B),o=void 0!==j[t]?j[t]:null;return{char:o,placeholderChar:void 0!==j[0]?null:null!=(n=null==w?void 0:w[t])?n:null,isActive:r,hasFakeCaret:r&&null===o}}),isFocused:H,isHovering:!R.disabled&&A}),[H,A,h,B,Y,R.disabled,j]),en=r.useMemo(()=>W?W(et):r.createElement(p.Provider,{value:et},V),[V,et,W]);return r.createElement(r.Fragment,null,null!==x&&r.createElement("noscript",null,r.createElement("style",null,x)),r.createElement("div",{ref:G,"data-input-otp-container":!0,style:$,className:M},en,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},ee)))});function v(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}h.displayName="Input";var g=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`},55988:function(e,t,n){n.d(t,{EQ:function(){return S}});let r=Symbol.for("@ts-pattern/matcher"),o=Symbol.for("@ts-pattern/isVariadic"),a="@ts-pattern/anonymous-select-key",i=e=>!!(e&&"object"==typeof e),c=e=>e&&!!e[r],l=(e,t,n)=>{if(c(e)){let{matched:o,selections:a}=e[r]().match(t);return o&&a&&Object.keys(a).forEach(e=>n(e,a[e])),o}if(i(e)){if(!i(t))return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let r=[],a=[],i=[];for(let t of e.keys()){let n=e[t];c(n)&&n[o]?i.push(n):i.length?a.push(n):r.push(n)}if(i.length){if(i.length>1)throw Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<r.length+a.length)return!1;let e=t.slice(0,r.length),o=0===a.length?[]:t.slice(-a.length),c=t.slice(r.length,0===a.length?1/0:-a.length);return r.every((t,r)=>l(t,e[r],n))&&a.every((e,t)=>l(e,o[t],n))&&(0===i.length||l(i[0],c,n))}return e.length===t.length&&e.every((e,r)=>l(e,t[r],n))}return Reflect.ownKeys(e).every(o=>{let a=e[o];return(o in t||c(a)&&"optional"===a[r]().matcherType)&&l(a,t[o],n)})}return Object.is(t,e)},u=e=>{var t,n,o;return i(e)?c(e)?null!=(t=null==(n=(o=e[r]()).getSelectionKeys)?void 0:n.call(o))?t:[]:Array.isArray(e)?s(e,u):s(Object.values(e),u):[]},s=(e,t)=>e.reduce((e,n)=>e.concat(t(n)),[]);function d(e){return Object.assign(e,{optional:()=>d({[r]:()=>({match:t=>{let n={},r=(e,t)=>{n[e]=t};return void 0===t?(u(e).forEach(e=>r(e,void 0)),{matched:!0,selections:n}):{matched:l(e,t,r),selections:n}},getSelectionKeys:()=>u(e),matcherType:"optional"})}),and:t=>f(e,t),or:t=>(function(...e){return d({[r]:()=>({match:t=>{let n={},r=(e,t)=>{n[e]=t};return s(e,u).forEach(e=>r(e,void 0)),{matched:e.some(e=>l(e,t,r)),selections:n}},getSelectionKeys:()=>s(e,u),matcherType:"or"})})})(e,t),select:t=>void 0===t?p(e):p(t,e)})}function f(...e){return d({[r]:()=>({match:t=>{let n={},r=(e,t)=>{n[e]=t};return{matched:e.every(e=>l(e,t,r)),selections:n}},getSelectionKeys:()=>s(e,u),matcherType:"and"})})}function m(e){return{[r]:()=>({match:t=>({matched:!!e(t)})})}}function p(...e){let t="string"==typeof e[0]?e[0]:void 0,n=2===e.length?e[1]:"string"==typeof e[0]?void 0:e[0];return d({[r]:()=>({match:e=>{let r={[null!=t?t:a]:e};return{matched:void 0===n||l(n,e,(e,t)=>{r[e]=t}),selections:r}},getSelectionKeys:()=>[null!=t?t:a].concat(void 0===n?[]:u(n))})})}function h(e){return"number"==typeof e}function v(e){return"string"==typeof e}function g(e){return"bigint"==typeof e}d(m(function(e){return!0}));let y=e=>Object.assign(d(e),{startsWith:t=>y(f(e,m(e=>v(e)&&e.startsWith(t)))),endsWith:t=>y(f(e,m(e=>v(e)&&e.endsWith(t)))),minLength:t=>y(f(e,m(e=>v(e)&&e.length>=t))),length:t=>y(f(e,m(e=>v(e)&&e.length===t))),maxLength:t=>y(f(e,m(e=>v(e)&&e.length<=t))),includes:t=>y(f(e,m(e=>v(e)&&e.includes(t)))),regex:t=>y(f(e,m(e=>v(e)&&!!e.match(t))))}),b=(y(m(v)),e=>Object.assign(d(e),{between:(t,n)=>b(f(e,m(e=>h(e)&&t<=e&&n>=e))),lt:t=>b(f(e,m(e=>h(e)&&e<t))),gt:t=>b(f(e,m(e=>h(e)&&e>t))),lte:t=>b(f(e,m(e=>h(e)&&e<=t))),gte:t=>b(f(e,m(e=>h(e)&&e>=t))),int:()=>b(f(e,m(e=>h(e)&&Number.isInteger(e)))),finite:()=>b(f(e,m(e=>h(e)&&Number.isFinite(e)))),positive:()=>b(f(e,m(e=>h(e)&&e>0))),negative:()=>b(f(e,m(e=>h(e)&&e<0)))})),w=(b(m(h)),e=>Object.assign(d(e),{between:(t,n)=>w(f(e,m(e=>g(e)&&t<=e&&n>=e))),lt:t=>w(f(e,m(e=>g(e)&&e<t))),gt:t=>w(f(e,m(e=>g(e)&&e>t))),lte:t=>w(f(e,m(e=>g(e)&&e<=t))),gte:t=>w(f(e,m(e=>g(e)&&e>=t))),positive:()=>w(f(e,m(e=>g(e)&&e>0))),negative:()=>w(f(e,m(e=>g(e)&&e<0)))}));w(m(g)),d(m(function(e){return"boolean"==typeof e})),d(m(function(e){return"symbol"==typeof e})),d(m(function(e){return null==e})),d(m(function(e){return null!=e}));class k extends Error{constructor(e){let t;try{t=JSON.stringify(e)}catch(n){t=e}super(`Pattern matching error: no pattern matches value ${t}`),this.input=void 0,this.input=e}}let L={matched:!1,value:void 0};function S(e){return new E(e,L)}class E{constructor(e,t){this.input=void 0,this.state=void 0,this.input=e,this.state=t}with(...e){let t;if(this.state.matched)return this;let n=e[e.length-1],r=[e[0]];3===e.length&&"function"==typeof e[1]?t=e[1]:e.length>2&&r.push(...e.slice(1,e.length-1));let o=!1,i={},c=(e,t)=>{o=!0,i[e]=t},u=r.some(e=>l(e,this.input,c))&&(!t||t(this.input))?{matched:!0,value:n(o?a in i?i[a]:i:this.input,this.input)}:L;return new E(this.input,u)}when(e,t){if(this.state.matched)return this;let n=!!e(this.input);return new E(this.input,n?{matched:!0,value:t(this.input,this.input)}:L)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(e=M){return this.state.matched?this.state.value:e(this.input)}run(){return this.exhaustive()}returnType(){return this}}function M(e){throw new k(e)}},5853:function(e,t,n){n.d(t,{Jh:function(){return i},_T:function(){return o},ev:function(){return c},mG:function(){return a},pi:function(){return r}});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function a(e,t,n,r){return new(n||(n=Promise))(function(o,a){function i(e){try{l(r.next(e))}catch(e){a(e)}}function c(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(i,c)}l((r=r.apply(e,t||[])).next())})}function i(e,t){var n,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=c(0),i.throw=c(1),i.return=c(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(l){return function(c){if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&c[0]?r.return:c[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,c[1])).done)return o;switch(r=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===c[0]||2===c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=t.call(e,a)}catch(e){c=[6,e],r=0}finally{n=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}}function c(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError}}]);