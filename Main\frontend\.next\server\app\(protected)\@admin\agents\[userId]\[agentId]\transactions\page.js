(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[47],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},11206:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>D,default:()=>A});var a,r={};s.r(r),s.d(r,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>f,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>h,pages:()=>x,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>g,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),s(67206);var n=s(79319),i=s(20518),o=s(61902),l=s(62042),c=s(44630),d=s(44828),m=s(65505),u=s(13839);let p=["",{children:["(protected)",{admin:["children",{children:["agents",{children:["[userId]",{children:["[agentId]",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,63897)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\transactions\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,76694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\transactions\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,12771)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,12885)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,29670)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,74030)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],x=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\transactions\\page.tsx"],h="/(protected)/@admin/agents/[userId]/[agentId]/transactions/page",f={require:s,loadChunk:()=>Promise.resolve()},g=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/agents/[userId]/[agentId]/transactions/page",pathname:"/agents/[userId]/[agentId]/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=s(69094),y=s(5787),S=s(90527);let b=e=>e?JSON.parse(e):void 0,j=self.__BUILD_MANIFEST,E=b(self.__REACT_LOADABLE_MANIFEST),k=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/agents/[userId]/[agentId]/transactions/page"],N=b(self.__RSC_SERVER_MANIFEST),P=b(self.__NEXT_FONT_MANIFEST),w=b(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];k&&N&&(0,y.Mo)({clientReferenceManifest:k,serverActionsManifest:N,serverModuleMap:(0,S.w)({serverActionsManifest:N,pageName:"/(protected)/@admin/agents/[userId]/[agentId]/transactions/page"})});let I=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@admin/agents/[userId]/[agentId]/transactions/page",appMod:null,pageMod:r,errorMod:null,error500Mod:null,Document:null,buildManifest:j,renderToHTML:l.f,reactLoadableManifest:E,clientReferenceManifest:k,serverActionsManifest:N,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:P,incrementalCacheHandler:null,interceptionRouteRewrites:w}),D=r;function A(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:I})}},98254:(e,t,s)=>{Promise.resolve().then(s.bind(s,29431))},19701:(e,t,s)=>{Promise.resolve().then(s.bind(s,61600))},29431:(e,t,s)=>{"use strict";s.d(t,{Tabbar:()=>y});var a=s(60926),r=s(14579),n=s(30417),i=s(89551),o=s(53042),l=s(23181),c=s(44788),d=s(38071),m=s(28531),u=s(5764),p=s(47020),x=s(737),h=s(64947),f=s(39228),g=s(32167),v=s(91500);function y(){let e=(0,h.UO)(),t=(0,h.jD)(),s=(0,h.tv)(),y=(0,h.lr)(),{t:S}=(0,f.$G)(),b=[{title:S("Account Details"),icon:(0,a.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}?${y.toString()}`,id:"__DEFAULT__"},{title:S("Charges/Commissions"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/commissions?${y.toString()}`,id:"commissions"},{title:S("Fees"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/fees?${y.toString()}`,id:"fees"},{title:S("Transactions"),icon:(0,a.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/transactions?${y.toString()}`,id:"transactions"},{title:S("KYC"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/kyc?${y.toString()}`,id:"kyc"},{title:S("Permissions"),icon:(0,a.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/permissions?${y.toString()}`,id:"permissions"},{title:S("Send Email"),icon:(0,a.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/send-email?${y.toString()}`,id:"send-email"}];return(0,a.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,a.jsx)("li",{children:(0,a.jsxs)(x.Z,{href:"/agents/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,a.jsx)(p.Z,{className:"size-4 sm:size-6"}),S("Back")]})}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",y.get("name")," "]}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",S("Agents")," #",e.agentId]})]}),(0,a.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,a.jsx)("span",{children:S("Active")}),(0,a.jsx)(n.Z,{defaultChecked:"1"===y.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:a=>{g.toast.promise((0,i.z)(e.userId),{loading:S("Loading..."),success:r=>{if(!r.status)throw Error(r.message);let n=new URLSearchParams(y);return(0,v.j)(`/admin/agents/${e.agentId}`),n.set("active",a?"1":"0"),s.push(`${t}?${n.toString()}`),r.message},error:e=>e.message})}})]})]}),(0,a.jsx)(r.a,{tabs:b})]})}},61600:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(60926),r=s(44335),n=s(78133),i=s(44942),o=s(69226),l=s(20293),c=s(58904),d=s(15185),m=s(28871),u=s(48673),p=s(66817),x=s(74988),h=s(43291),f=s(98903),g=s(65091),v=s(3632),y=s(9172),S=s(32917),b=s(34870),j=s(737),E=s(64947),k=s(29220),N=s(39228),P=s(56999);let w=new g.F;function I(){let e=(0,E.UO)(),t=(0,E.lr)(),[s,I]=k.useState(t.get("search")??""),D=(0,E.tv)(),A=(0,E.jD)(),[C,M]=k.useState([]),{t:L}=(0,N.$G)(),{data:_,meta:z,isLoading:Z,refresh:T,filter:F}=(0,f.Z)(`/admin/transactions/${e.userId}?${t.toString()}`,{keepPreviousData:!0}),{data:R,isLoading:Q}=(0,h.d)(`/admin/transactions/counts/${e.userId}`);return(0,a.jsxs)("div",{className:"h-full p-4",children:[(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-12 gap-4",children:[(0,a.jsx)(i.x,{value:Q?0:R?.data?.deposit,title:L("Total Deposit"),icon:e=>(0,a.jsx)(S.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6",statusClass:"text-spacial-green",iconClass:"bg-spacial-green-foreground"}),(0,a.jsx)(i.x,{value:Q?0:R?.data?.withdraw,title:L("Total Withdraw"),icon:e=>(0,a.jsx)(b.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6",iconClass:"bg-spacial-red-foreground text-spacial-red",statusClass:"text-spacial-red"})]}),(0,a.jsxs)("div",{className:"h-fit w-full overflow-y-auto overflow-x-hidden bg-background p-6 pb-16 shadow-default sm:rounded-xl sm:pb-4",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,a.jsxs)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:[(0,a.jsx)(c.Z,{filter:F}),(0,a.jsxs)("div",{className:"flex h-full w-full items-center gap-2.5 px-4 py-2.5 sm:w-40 sm:px-0",children:[(0,a.jsx)(u.X,{id:"bookmark",defaultChecked:"true"===t.get("bookmark"),onCheckedChange:e=>F("bookmark",e.toString()),className:"border-foreground/40 data-[state=checked]:border-primary"}),(0,a.jsx)(p.Z,{htmlFor:"bookmark",className:"text-sm font-normal hover:cursor-pointer",children:L("Show bookmarks")})]})]}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center gap-4 md:flex-nowrap xl:justify-end",children:[(0,a.jsx)(n.R,{value:s,onChange:e=>{e.preventDefault();let t=(0,g.w4)(e.target.value);I(e.target.value),D.replace(`${A}?${t.toString()}`)},iconPlacement:"end",placeholder:L("Search..."),containerClass:"w-full sm:w-auto"}),(0,a.jsx)(l.k,{canFilterByMethod:!0,canFilterByGateway:!0}),(0,a.jsx)(o._,{url:`/admin/transactions/export/${e.userId}`})]}),(0,a.jsx)("div",{})]}),(0,a.jsx)(x.Z,{className:"my-4"}),(0,a.jsx)(r.Z,{data:_?_?.map(e=>new v.C(e)):[],isLoading:Z,onRefresh:T,sorting:C,setSorting:M,pagination:{total:z?.total,page:z?.currentPage,limit:z?.perPage},structure:[{id:"createdAt",header:L("Date"),cell:({row:e})=>(0,a.jsx)("span",{className:"text-sm font-normal leading-5 text-secondary-text",children:e.original.getCreatedAt()})},{id:"to",header:L("To"),cell:({row:e})=>(0,a.jsxs)("div",{className:"text-sm font-normal leading-5 text-foreground",children:[(0,a.jsxs)(d.qE,{className:"size-7 border-2 border-primary p-1 font-semibold",children:[(0,a.jsx)(d.F$,{src:e.original?.to?.image,alt:e.original?.to?.label}),(0,a.jsxs)(d.Q5,{children:[(0,y.v)(e.original?.to?.label)," "]})]}),(0,a.jsx)("span",{children:e.original.to.label})]})},{id:"status",header:L("Status"),cell:({row:e})=>e.original?.status==="completed"?(0,a.jsx)(m.C,{variant:"success",children:L((0,g.fl)(e.original?.status))}):e.original?.status==="failed"?(0,a.jsx)(m.C,{variant:"destructive",children:L((0,g.fl)(e.original?.status))}):(0,a.jsx)(m.C,{variant:"secondary",children:L((0,g.fl)(e.original?.status))})},{id:"amount",header:L("Amount sent"),cell:({row:e})=>{let t=e.original;return(0,a.jsx)("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,P.EQ)(t).with({type:"exchange"},()=>w.format(t?.metaData?.amountFrom,t?.metaData?.currencyFrom)).with({type:"deposit"},()=>w.format(t.amount,t?.metaData?.currency)).otherwise(()=>w.format(t.amount,t?.from?.currency))})}},{id:"fee",header:L("Fee"),cell:({row:e})=>{let t=e.original;return(0,a.jsx)("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,P.EQ)(t).with({type:"exchange"},()=>w.format(t?.fee,t.metaData?.currency)).with({type:"deposit"},()=>w.format(t.fee,t.metaData?.currency)).otherwise(()=>w.format(t.fee,t.from?.currency))})}},{id:"total",header:L("Amount received"),cell:({row:e})=>{let t=e.original;return(0,a.jsx)("span",{className:"leading-20 whitespace-nowrap text-sm font-semibold text-foreground",children:(0,P.EQ)(t).with({type:"exchange"},()=>w.format(t.total,t.metaData?.currencyTo)).with({type:"deposit"},()=>w.format(t.total,t.metaData?.currency)).otherwise(()=>w.format(t.total,t.to?.currency))})}},{id:"metaData",header:L("Phone number"),cell:({row:e})=>(0,a.jsx)("span",{className:"text-xs font-normal text-secondary-text",children:e.original?.metaData?.phone??"N/A"})},{id:"trxId",header:L("Trx ID"),cell:({row:e})=>e.original?.trxId?(0,a.jsx)(j.Z,{href:`/transactions/${e.original?.trxId}`,className:"text-xs font-normal text-foreground hover:underline",children:e.original?.trxId}):(0,a.jsx)("span",{className:"text-sm font-normal",children:"N/A"})}]})]})]})}},44942:(e,t,s)=>{"use strict";s.d(t,{x:()=>i});var a=s(60926),r=s(87198),n=s(65091);function i({title:e,value:t,status:s,icon:i,iconClass:o,statusClass:l,className:c,isLoading:d}){return d?(0,a.jsx)(r.O,{className:(0,n.ZP)("",c)}):(0,a.jsxs)("div",{className:(0,n.ZP)("inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default",c),children:[(0,a.jsx)("div",{className:(0,n.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full bg-muted",o),children:i({size:34,variant:"Outline"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)("h1",{children:t}),(0,a.jsxs)("span",{className:"block text-xs font-normal leading-4",children:[e," "]}),(0,a.jsx)("h6",{className:(0,n.ZP)("text-sm font-semibold leading-5",l),children:s})]})]})}s(29220)},58904:(e,t,s)=>{"use strict";s.d(t,{Z:()=>S});var a=s(60926),r=s(29220),n=s(98019),i=s(23183),o=s(65091),l=s(86059),c=s(32917),d=s(65694),m=s(34870),u=s(48132),p=s(55929),x=s(50201),h=s(95334),f=s(32793),g=s(36926),v=s(64947),y=s(39228);function S({filter:e}){let t=(0,v.lr)(),{t:s}=(0,y.$G)(),[S,b]=r.useState(!1);return(0,a.jsxs)(i.J2,{open:S,onOpenChange:b,children:[(0,a.jsxs)(i.xo,{className:"flex h-10 w-full items-center gap-2 rounded-sm bg-background px-3 text-foreground shadow-defaultLite sm:w-72",children:[(0,a.jsx)("span",{className:"line-clamp-1 flex-1 text-left font-medium leading-[22px] text-foreground",children:t.get("type")?(0,o.fl)(t.get("type")):s("All Transactions")}),(0,a.jsx)(l.Z,{size:"24",strokeWidth:1.5,className:"text-secondary-text"})]}),(0,a.jsx)(i.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:(0,a.jsx)(n.mY,{className:"p-1",children:(0,a.jsx)(n.e8,{className:"max-h-[450px]",children:(0,a.jsxs)(n.fu,{className:"p-0",children:[(0,a.jsx)("div",{className:"px-2 py-1.5",children:(0,a.jsx)("p",{className:"text-[10px] font-normal leading-4 text-secondary-text",children:s("Select what you want to see")})}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(g.Z,{size:24}),s("All Transactions")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","deposit",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(c.Z,{size:24}),s("Deposits")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","transfer",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(d.Z,{size:24}),s("Transfer")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","withdraw",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(m.Z,{size:"24"}),s("Withdraws")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","exchange",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(u.Z,{size:"24"}),s("Exchange")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","payment",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(p.Z,{size:"24"}),s("Payment")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","service",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(x.Z,{size:"24"}),s("Services")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","investment",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(h.Z,{size:"24"}),s("Investments")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","investment_return",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(h.Z,{size:"24"}),s("Investment return")]}),(0,a.jsx)(n.zz,{className:"mb-1 mt-[5px]"}),(0,a.jsxs)(n.di,{onSelect:()=>e("type","referral_bonus",()=>b(!1)),className:"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]",children:[(0,a.jsx)(f.Z,{size:"24"}),s("Referral bonus")]})]})})})})]})}},48673:(e,t,s)=>{"use strict";s.d(t,{X:()=>l});var a=s(60926),r=s(20021),n=s(4432),i=s(29220),o=s(65091);let l=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.fC,{ref:s,className:(0,o.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:(0,a.jsx)(r.z$,{className:(0,o.ZP)("flex items-center justify-center text-current"),children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})}));l.displayName=r.fC.displayName},87198:(e,t,s)=>{"use strict";s.d(t,{O:()=>n});var a=s(60926),r=s(65091);function n({className:e,...t}){return(0,a.jsx)("div",{className:(0,r.ZP)("animate-pulse rounded-md bg-muted",e),...t})}},23181:(e,t,s)=>{"use strict";s.d(t,{Z:()=>h});var a=s(61394),r=s(29220),n=s(31036),i=s.n(n),o=["variant","color","size"],l=function(e){var t=e.color;return r.createElement(r.Fragment,null,r.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7.75 9.14c0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48 0 .81-.66 1.48-1.48 1.48a1.49 1.49 0 0 1-1.48-1.48Zm1.07 6.88c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.35 15.8c-.15.15-.34.22-.53.22Zm6.95.32c-.81 0-1.48-.66-1.48-1.48 0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48s-.66 1.48-1.48 1.48Z",fill:t}))},c=function(e){var t=e.color;return r.createElement(r.Fragment,null,r.createElement("path",{d:"M2 13.05V15c0 5 2 7 7 7h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9M8.32 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("path",{d:"M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return r.createElement(r.Fragment,null,r.createElement("path",{opacity:".4",d:"M16.192 2h-8.37c-3.64 0-5.81 2.17-5.81 5.81v8.37c0 3.64 2.17 5.81 5.81 5.81h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81c0-3.64-2.17-5.81-5.81-5.81Z",fill:t}),r.createElement("path",{d:"M8.822 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM9.23 7.66c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48a1.49 1.49 0 0 0-1.48-1.48ZM15.769 13.39c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48s-.66-1.48-1.48-1.48Z",fill:t}))},m=function(e){var t=e.color;return r.createElement(r.Fragment,null,r.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7ZM8.57 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("path",{d:"M8.98 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.52 16.09a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return r.createElement(r.Fragment,null,r.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h6c5.43 0 7.75 2.32 7.75 7.75v6c0 5.43-2.32 7.75-7.75 7.75Zm-6-20C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25V9c0-4.61-1.64-6.25-6.25-6.25H9Z",fill:t}),r.createElement("path",{d:"M8.568 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM8.98 11.108c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48a.48.48 0 0 0-.48-.48ZM15.519 16.839c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48-.21-.48-.48-.48Z",fill:t}))},p=function(e){var t=e.color;return r.createElement(r.Fragment,null,r.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("path",{opacity:".4",d:"m8.32 15.27 6.54-6.54M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},x=function(e,t){switch(e){case"Bold":return r.createElement(l,{color:t});case"Broken":return r.createElement(c,{color:t});case"Bulk":return r.createElement(d,{color:t});case"Linear":default:return r.createElement(m,{color:t});case"Outline":return r.createElement(u,{color:t});case"TwoTone":return r.createElement(p,{color:t})}},h=(0,r.forwardRef)(function(e,t){var s=e.variant,n=e.color,i=e.size,l=(0,a._)(e,o);return r.createElement("svg",(0,a.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),x(s,n))});h.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="PercentageSquare"},3632:(e,t,s)=>{"use strict";s.d(t,{C:()=>c});var a=s(73244),r=s(73146),n=s(65091);class i{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,n.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new r.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}var o=s(14455),l=s(74190);class c{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new i(e?.user),customer:e?.user?.customer?new l.O(e?.user?.customer):null,merchant:e?.user?.merchant?new l.O(e?.user?.merchant):null,agent:e?.user?.agent?new l.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,o.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,o.WU)(this.updatedAt,e):"N/A"}}},12771:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,runtime:()=>n});var a=s(42416);s(87908);let r=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\_components\Tabbar.tsx#Tabbar`),n="edge";function i({children:e}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r,{}),e]})}},12885:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}},76694:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex justify-center py-10",children:(0,a.jsx)(r.a,{})})}},63897:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\transactions\page.tsx#default`)},29670:(e,t,s)=>{"use strict";function a({children:e}){return e}s.r(t),s.d(t,{default:()=>a}),s(87908)},74030:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}},20021:(e,t,s)=>{"use strict";s.d(t,{fC:()=>k,z$:()=>N});var a=s(29220),r=s(19677),n=s(16769),i=s(58408),o=s(68878),l=s(43263),c=s(17526),d=s(90027),m=s(22316),u=s(60926),p="Checkbox",[x,h]=(0,n.b)(p),[f,g]=x(p),v=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:n,checked:l,defaultChecked:c,required:d,disabled:x,value:h="on",onCheckedChange:g,form:v,...y}=e,[S,k]=a.useState(null),N=(0,r.e)(t,e=>k(e)),P=a.useRef(!1),w=!S||v||!!S.closest("form"),[I,D]=(0,o.T)({prop:l,defaultProp:c??!1,onChange:g,caller:p}),A=a.useRef(I);return a.useEffect(()=>{let e=S?.form;if(e){let t=()=>D(A.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[S,D]),(0,u.jsxs)(f,{scope:s,state:I,disabled:x,children:[(0,u.jsx)(m.WV.button,{type:"button",role:"checkbox","aria-checked":j(I)?"mixed":I,"aria-required":d,"data-state":E(I),"data-disabled":x?"":void 0,disabled:x,value:h,...y,ref:N,onKeyDown:(0,i.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(e.onClick,e=>{D(e=>!!j(e)||!e),w&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),w&&(0,u.jsx)(b,{control:S,bubbles:!P.current,name:n,value:h,checked:I,required:d,disabled:x,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!j(c)&&c})]})});v.displayName=p;var y="CheckboxIndicator",S=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:a,...r}=e,n=g(y,s);return(0,u.jsx)(d.z,{present:a||j(n.state)||!0===n.state,children:(0,u.jsx)(m.WV.span,{"data-state":E(n.state),"data-disabled":n.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});S.displayName=y;var b=a.forwardRef(({__scopeCheckbox:e,control:t,checked:s,bubbles:n=!0,defaultChecked:i,...o},d)=>{let p=a.useRef(null),x=(0,r.e)(p,d),h=(0,l.D)(s),f=(0,c.t)(t);a.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==s&&t){let a=new Event("click",{bubbles:n});e.indeterminate=j(s),t.call(e,!j(s)&&s),e.dispatchEvent(a)}},[h,s,n]);let g=a.useRef(!j(s)&&s);return(0,u.jsx)(m.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:i??g.current,...o,tabIndex:-1,ref:x,style:{...o.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return"indeterminate"===e}function E(e){return j(e)?"indeterminate":e?"checked":"unchecked"}b.displayName="CheckboxBubbleInput";var k=v,N=S}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4774,7848,6147,1991,7283,5089,3711,4656,8748],()=>t(11206));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/agents/[userId]/[agentId]/transactions/page"]=s}]);
//# sourceMappingURL=page.js.map