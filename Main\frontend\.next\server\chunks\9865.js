exports.id=9865,exports.ids=[9865],exports.modules={26807:(e,t,r)=>{Promise.resolve().then(r.bind(r,73e3)),Promise.resolve().then(r.bind(r,75285))},92475:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var n=r(10326),s=r(56038),a=r(56140),i=r(567),l=r(90772),o=r(77863),d=r(66114),c=r(9489),m=r(90434),u=r(17577),h=r.n(u),g=r(70012);let f=new o.F;function x({data:e,meta:t,isLoading:r,refresh:u}){let[x,p]=h().useState([]),{t:y}=(0,g.$G)();return n.jsx(a.Z,{data:e?e?.map(e=>new d.C(e)):null,isLoading:r,sorting:x,setSorting:p,onRefresh:u,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"trxId",header:y("Trx ID"),cell:({row:e})=>n.jsx(m.default,{href:`/withdraws/${e.original?.id}`,className:"text-xs font-normal text-foreground hover:underline",children:e.original?.trxId})},{id:"createdAt",header:y("Date"),cell:({row:e})=>(0,n.jsxs)("div",{children:[n.jsx("span",{className:"block min-w-24 text-sm font-normal leading-5 text-foreground",children:e.original.getCreatedAt("dd MMM yyyy;")}),n.jsx("span",{className:"block min-w-24 text-sm font-normal leading-5 text-foreground",children:e.original.getCreatedAt("hh:mm a")})]})},{id:"status",header:y("Status"),cell:({row:e})=>e.original?.status==="completed"?n.jsx(i.C,{variant:"success",children:y((0,o.fl)(e.original?.status))}):e.original?.status==="pending"?n.jsx(i.C,{variant:"secondary",className:"bg-muted",children:y((0,o.fl)(e.original?.status))}):e.original?.status==="failed"?n.jsx(i.C,{variant:"destructive",children:y((0,o.fl)(e.original?.status))}):n.jsx(i.C,{variant:"secondary",className:"bg-muted",children:y("Pending")})},{id:"amount",header:y("Amount"),cell:({row:e})=>{let t;return t="exchange"===e.original.type?e.original?.metaData?.currencyFrom:"deposit"===e.original.type?e.original?.metaData?.currency:e.original?.from?.currency,n.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:f.format(e.original.amount,t)})}},{id:"fee",header:y("Fee"),cell:({row:e})=>{let t;return t="exchange"===e.original.type?e.original?.metaData?.currencyFrom:"deposit"===e.original.type?e.original?.metaData?.currency:e.original?.from?.currency,n.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:f.format(e.original.fee,t)})}},{id:"after_processing",header:y("After Processing"),cell:({row:e})=>{let t;return t="exchange"===e.original.type?e.original?.metaData?.currencyFrom:"deposit"===e.original.type?e.original?.metaData?.currency:e.original?.from?.currency,n.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:f.format(e.original.total,t)})}},{id:"method",header:y("Method"),cell:({row:e})=>e.original?.method?n.jsx("span",{className:"line-clamp-2 w-[100px] text-sm font-normal text-foreground",children:e.original?.method}):n.jsx("span",{className:"text-sm font-normal",children:"N/A"})},{id:"customer",header:y("Customer"),cell:({row:e})=>n.jsx(s.Z,{row:e})},{id:"view",header:y("View"),cell:({row:e})=>n.jsx("div",{className:"flex items-center gap-1",children:n.jsx(l.z,{type:"button",variant:"ghost",size:"icon",className:"hover:bg-muted",asChild:!0,children:n.jsx(m.default,{href:`/withdraws/${e.original?.id}`,prefetch:!1,children:n.jsx(c.Z,{})})})})}]})}},56038:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(10326),s=r(28758),a=r(54033),i=r(90434);let l=function({row:e}){return(0,n.jsxs)(i.default,{href:(()=>{switch(e.original?.user?.roleId){case 1:return`/staffs/edit/${e.original?.user?.id}`;case 2:default:return`/customers/${e.original?.user?.customer?.id}?name=${e.original?.user?.customer?.name}&active=${e?.original?.user?.status}`;case 3:return`/merchants/${e.original?.userId}/${e.original?.user?.merchant?.id}?name=${e.original.user?.customer?.name}&active=${e?.original?.user?.status}`;case 4:return`/agents/${e.original?.userId}/${e.original?.user?.agent?.id}?name=${e.original.user?.customer?.name}&active=${e?.original?.user?.status}`}})(),className:"flex min-w-[80px] items-center gap-2 font-normal text-secondary-text hover:text-foreground",children:[(0,n.jsxs)(s.qE,{children:[n.jsx(s.F$,{src:e.original.user.customer.profileImage}),n.jsx(s.Q5,{children:(0,a.v)(e.original.user.customer.name)})]}),(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("p",{className:"text-sm font-normal",children:e.original.user.customer.name}),e.original?.user.email?n.jsx("p",{className:"text-xs font-normal",children:e.original?.user.email}):null]})]})}},75285:(e,t,r)=>{"use strict";r.d(t,{default:()=>D});var n=r(10326),s=r(5158),a=r(90772),i=r(81638),l=r(6216),o=r(90434),d=r(35047),c=r(17577);function m({sidebarItem:e}){let[t,r]=c.useState("(dashboard)"),[m,u]=c.useState(!1),{setIsExpanded:h,device:g}=(0,i.q)(),f=(0,d.useSelectedLayoutSegment)();return c.useEffect(()=>{r(f)},[]),c.useEffect(()=>{u(e.segment===f)},[f,e.segment]),(0,n.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,n.jsxs)(o.default,{href:e.link,onClick:()=>{r(e.segment),e.children?.length||"Desktop"===g||h(!1)},"data-active":f===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[n.jsx(s.J,{condition:!!e.icon,children:n.jsx("div",{"data-active":f===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),n.jsx("span",{className:"flex-1",children:e.name}),n.jsx(s.J,{condition:!!e.children?.length,children:n.jsx(a.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:n.jsx(l.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),n.jsx(s.J,{condition:!!e.children?.length,children:n.jsx("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>n.jsx("li",{children:n.jsxs(o.default,{href:e.link,"data-active":t===e.segment,onClick:()=>{r(e.segment),"Desktop"!==g&&h(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[n.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=r(8281),h=r(4066),g=r(77863),f=r(1178),x=r(29169),p=r(40420),y=r(78564),k=r(53105),v=r(81770),j=r(45922),b=r(29764),w=r(26920),N=r(9155),E=r(41334),Z=r(73686),A=r(75073),z=r(44221),C=r(46226),M=r(70012);function D(){let{t:e}=(0,M.$G)(),{isExpanded:t,setIsExpanded:r}=(0,i.q)(),{logo:s,siteName:l}=(0,h.T)(),d=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:n.jsx(f.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:n.jsx(x.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:n.jsx(p.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:n.jsx(y.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:n.jsx(k.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:n.jsx(v.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:n.jsx(j.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:n.jsx(b.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:n.jsx(w.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:n.jsx(N.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:n.jsx(E.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:n.jsx(Z.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:n.jsx(A.Z,{size:"20"}),link:"/settings"}]}];return(0,n.jsxs)("div",{"data-expanded":t,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[n.jsx(a.z,{size:"icon",variant:"outline",onClick:()=>r(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${t?"":"hidden"} lg:hidden`,children:n.jsx(z.Z,{})}),n.jsx("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:n.jsx(o.default,{href:"/",className:"flex items-center justify-center",children:n.jsx(C.default,{src:(0,g.qR)(s),width:160,height:40,alt:l,className:"max-h-10 object-contain"})})}),n.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:d.map(e=>(0,n.jsxs)("div",{children:[""!==e.title?n.jsx("div",{children:n.jsx(u.Z,{className:"my-4"})}):null,n.jsx("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>n.jsx("li",{children:n.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>l});var n=r(10326),s=r(79360);r(17577);var a=r(77863);let i=(0,s.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return n.jsx("div",{className:(0,a.ZP)(i({variant:t}),e),...r})}},9489:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var n=r(52920),s=r(17577),a=r.n(s),i=r(78439),l=r.n(i),o=["variant","color","size"],d=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M21.25 9.15C18.94 5.52 15.56 3.43 12 3.43c-1.78 0-3.51.52-5.09 1.49-1.58.98-3 2.41-4.16 4.23-1 1.57-1 4.12 0 5.69 2.31 3.64 5.69 5.72 9.25 5.72 1.78 0 3.51-.52 5.09-1.49 1.58-.98 3-2.41 4.16-4.23 1-1.56 1-4.12 0-5.69ZM12 16.04c-2.24 0-4.04-1.81-4.04-4.04S9.76 7.96 12 7.96s4.04 1.81 4.04 4.04-1.8 4.04-4.04 4.04Z",fill:t}),a().createElement("path",{d:"M11.998 9.14a2.855 2.855 0 0 0 0 5.71c1.57 0 2.86-1.28 2.86-2.85s-1.29-2.86-2.86-2.86Z",fill:t}))},c=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M9.032 14.002c-.39-.57-.61-1.26-.61-2 0-1.98 1.6-3.58 3.58-3.58s3.58 1.6 3.58 3.58-1.6 3.58-3.58 3.58",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a().createElement("path",{d:"M17.56 5.58c-1.69-1.2-3.59-1.85-5.56-1.85-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68 3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{opacity:".4",d:"M21.25 9.15C18.94 5.52 15.56 3.43 12 3.43c-1.78 0-3.51.52-5.09 1.49-1.58.98-3 2.41-4.16 4.23-1 1.57-1 4.12 0 5.69 2.31 3.64 5.69 5.72 9.25 5.72 1.78 0 3.51-.52 5.09-1.49 1.58-.98 3-2.41 4.16-4.23 1-1.56 1-4.12 0-5.69ZM12 16.04c-2.24 0-4.04-1.81-4.04-4.04S9.76 7.96 12 7.96s4.04 1.81 4.04 4.04-1.8 4.04-4.04 4.04Z",fill:t}),a().createElement("path",{d:"M11.998 9.14a2.855 2.855 0 0 0 0 5.71c1.57 0 2.86-1.28 2.86-2.85s-1.29-2.86-2.86-2.86Z",fill:t}))},u=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M15.58 12c0 1.98-1.6 3.58-3.58 3.58S8.42 13.98 8.42 12s1.6-3.58 3.58-3.58 3.58 1.6 3.58 3.58Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a().createElement("path",{d:"M12 20.27c3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19-2.29-3.6-5.58-5.68-9.11-5.68-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M12.002 16.332c-2.39 0-4.33-1.94-4.33-4.33s1.94-4.33 4.33-4.33 4.33 1.94 4.33 4.33-1.94 4.33-4.33 4.33Zm0-7.16c-1.56 0-2.83 1.27-2.83 2.83s1.27 2.83 2.83 2.83 2.83-1.27 2.83-2.83-1.27-2.83-2.83-2.83Z",fill:t}),a().createElement("path",{d:"M11.998 21.02c-3.76 0-7.31-2.2-9.75-6.02-1.06-1.65-1.06-4.34 0-6 2.45-3.82 6-6.02 9.75-6.02s7.3 2.2 9.74 6.02c1.06 1.65 1.06 4.34 0 6-2.44 3.82-5.99 6.02-9.74 6.02Zm0-16.54c-3.23 0-6.32 1.94-8.48 5.33-.75 1.17-.75 3.21 0 4.38 2.16 3.39 5.25 5.33 8.48 5.33 3.23 0 6.32-1.94 8.48-5.33.75-1.17.75-3.21 0-4.38-2.16-3.39-5.25-5.33-8.48-5.33Z",fill:t}))},g=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{opacity:".4",d:"M15.582 12.002c0 1.98-1.6 3.58-3.58 3.58s-3.58-1.6-3.58-3.58 1.6-3.58 3.58-3.58 3.58 1.6 3.58 3.58Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a().createElement("path",{d:"M12 20.269c3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19-2.29-3.6-5.58-5.68-9.11-5.68-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e,t){switch(e){case"Bold":return a().createElement(d,{color:t});case"Broken":return a().createElement(c,{color:t});case"Bulk":return a().createElement(m,{color:t});case"Linear":default:return a().createElement(u,{color:t});case"Outline":return a().createElement(h,{color:t});case"TwoTone":return a().createElement(g,{color:t})}},x=(0,s.forwardRef)(function(e,t){var r=e.variant,s=e.color,i=e.size,l=(0,n._)(e,o);return a().createElement("svg",(0,n.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),f(r,s))});x.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="Eye"},66114:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var n=r(72450),s=r(71305),a=r(79308);class i{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new n.n(e?.user),customer:e?.user?.customer?new a.O(e?.user?.customer):null,merchant:e?.user?.merchant?new a.O(e?.user?.merchant):null,agent:e?.user?.agent?new a.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,s.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,s.WU)(this.updatedAt,e):"N/A"}}},72450:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var n=r(13263),s=r(13573),a=r(77863);class i{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,a.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new s.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new n.k(e?.address):null}}},11840:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(19510),s=r(40099);let a=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function i({children:e}){return(0,n.jsxs)("div",{className:"flex h-screen",children:[n.jsx(a,{}),(0,n.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[n.jsx(s.Z,{}),n.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},33661:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(19510),s=r(48413);function a(){return n.jsx("div",{className:"flex items-center justify-center py-10",children:n.jsx(s.a,{})})}},85878:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(19510),s=r(48413);function a(){return n.jsx("div",{className:"flex items-center justify-center py-10",children:n.jsx(s.a,{})})}}};