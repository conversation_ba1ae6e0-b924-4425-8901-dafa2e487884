(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72880],{84429:function(a,s,e){Promise.resolve().then(e.bind(e,56522))},56522:function(a,s,e){"use strict";e.r(s),e.d(s,{default:function(){return b}});var l=e(57437),t=e(40506),n=e(85539),c=e(27186),o=e(85017),i=e(18384),d=e(6512),r=e(31117),u=e(75730),x=e(94508),p=e(8877),m=e(89340),f=e(61756),g=e(3512),v=e(99376),h=e(2265),j=e(43949),w=e(66465);function b(){var a,s,e,b,N;let C=(0,v.useSearchParams)(),Z=(0,v.useRouter)(),_=(0,v.useParams)(),k=(0,v.usePathname)(),{t:y}=(0,j.$G)(),[L,P]=h.useState(null!==(N=C.get("search"))&&void 0!==N?N:""),{data:S,meta:T,isLoading:E,filter:B}=(0,u.Z)("/admin/transactions/".concat(_.userId,"?").concat(C.toString())),{data:F,isLoading:I}=(0,r.d)("/admin/transactions/counts/".concat(_.userId));return(0,l.jsxs)("div",{className:"h-full p-4",children:[(0,l.jsxs)("div",{className:"mb-4 grid grid-cols-12 gap-4",children:[(0,l.jsx)(w.x,{value:null==F?void 0:null===(a=F.data)||void 0===a?void 0:a.deposit,title:y("Total Deposit"),icon:a=>(0,l.jsx)(p.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",statusClass:"text-spacial-green",iconClass:"bg-spacial-green-foreground",isLoading:I}),(0,l.jsx)(w.x,{value:null==F?void 0:null===(s=F.data)||void 0===s?void 0:s.withdraw,title:y("Total Withdraw"),icon:a=>(0,l.jsx)(m.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-red-foreground text-spacial-red",statusClass:"text-spacial-red",isLoading:I}),(0,l.jsx)(w.x,{value:null==F?void 0:null===(e=F.data)||void 0===e?void 0:e.transfer,title:y("Total Transfers"),icon:a=>(0,l.jsx)(f.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-blue-foreground text-spacial-blue",statusClass:"text-spacial-blue",isLoading:I}),(0,l.jsx)(w.x,{value:null==F?void 0:null===(b=F.data)||void 0===b?void 0:b.exchange,title:y("Total Exchange"),icon:a=>(0,l.jsx)(g.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",isLoading:I})]}),(0,l.jsxs)("div",{className:"h-fit w-full overflow-auto rounded-xl bg-background p-6 shadow-default",children:[(0,l.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,l.jsx)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:(0,l.jsx)(i.Z,{filter:B})}),(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,l.jsx)(n.R,{value:L,onChange:a=>{a.preventDefault();let s=(0,x.w4)(a.target.value);P(a.target.value),Z.replace("".concat(k,"?").concat(s.toString()))},iconPlacement:"end",placeholder:y("Search...")}),(0,l.jsx)(o.k,{canFilterByAgent:!0,canFilterByMethod:!0,canFilterByGateway:!0}),(0,l.jsx)(c._,{url:"/admin/transactions/export/".concat(_.userId)})]})]}),(0,l.jsx)(d.Z,{className:"my-4"}),(0,l.jsx)(t.Z,{data:S,meta:T,isLoading:E})]})]})}}},function(a){a.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,64009,93396,227,56993,85017,18628,92971,95030,1744],function(){return a(a.s=84429)}),_N_E=a.O()}]);