exports.id=221,exports.ids=[221],exports.modules={94973:(e,s,t)=>{Promise.resolve().then(t.bind(t,48263))},96808:(e,s,t)=>{Promise.resolve().then(t.bind(t,19218))},15260:(e,s,t)=>{Promise.resolve().then(t.bind(t,27337))},90131:(e,s,t)=>{Promise.resolve().then(t.bind(t,77973))},20376:(e,s,t)=>{Promise.resolve().then(t.bind(t,66539))},73152:(e,s,t)=>{Promise.resolve().then(t.bind(t,84532))},48263:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>u});var r=t(10326),a=t(33646),i=t(53844),n=t(88589),l=t(40508),d=t(20187),o=t(50493),c=t(70012);function u(){let{t:e}=(0,c.$G)(),s=[{title:e("Account Settings"),icon:r.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:e("Charges/Commissions"),icon:r.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/fees-commissions",id:"fees-commissions"},{title:e("KYC Verification"),icon:r.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Methods"),icon:r.jsx(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/methods",id:"methods"},{title:e("Login Sessions"),icon:r.jsx(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return r.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:r.jsx(a.a,{tabs:s})})}},19218:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(10326),a=t(90210),i=t(11969),n=t(56672),l=t(27719);function d(){let{data:e,isLoading:s,refresh:t}=(0,l.E)();return r.jsx(n.UQ,{type:"multiple",defaultValue:["KYC_STATUS","DOCUMENT_INFORMATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[r.jsx(i.Z,{fetchData:e,isLoading:s}),r.jsx(a.u,{fetchData:e,isLoading:s,refresh:t})]})})}},27337:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>o});var r=t(10326),a=t(33646),i=t(53844),n=t(40508),l=t(50493),d=t(70012);function o(){let{t:e}=(0,d.$G)(),s=[{title:e("Account Settings"),icon:r.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings/",id:"__DEFAULT__"},{title:e("KYC Verification"),icon:r.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Login Sessions"),icon:r.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return r.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-black/[8%] bg-white p-4",children:r.jsx(a.a,{tabs:s})})}},77973:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(10326),a=t(90210),i=t(11969),n=t(56672),l=t(49547),d=t(84455);function o(){let{data:e,isLoading:s,mutate:t}=(0,d.ZP)("/kycs/detail",e=>l.Z.get(e),{refreshInterval:0,revalidateIfStale:!1,revalidateOnFocus:!1,refreshWhenHidden:!1,shouldRetryOnError:!1});return r.jsx(n.UQ,{type:"multiple",defaultValue:["KYC_STATUS","DOCUMENT_INFORMATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[r.jsx(i.Z,{fetchData:e?.data,isLoading:s}),r.jsx(a.u,{fetchData:e,isLoading:s,refresh:t})]})})}},66539:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>f});var r=t(10326),a=t(33646),i=t(53844),n=t(40508),l=t(9155),d=t(418),o=t(26138),c=t(50493),u=t(70012);function f(){let{t:e}=(0,u.$G)(),s=[{title:e("Account Settings"),icon:r.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:e("KYC Verification"),icon:r.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Merchant Settings"),icon:r.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/merchant-settings",id:"merchant-settings"},{title:e("MPay API"),icon:r.jsx(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/mpay-api",id:"mpay-api"},{title:e("Webhook URL"),icon:r.jsx(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/webhook-url-settings",id:"webhook-url-settings"},{title:e("Login Sessions"),icon:r.jsx(c.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return r.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:r.jsx(a.a,{tabs:s})})}},84532:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(10326),a=t(90210),i=t(11969),n=t(56672),l=t(27719);function d(){let{data:e,isLoading:s,refresh:t}=(0,l.E)();return r.jsx(n.UQ,{type:"multiple",defaultValue:["KYC_STATUS","DOCUMENT_INFORMATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[r.jsx(i.Z,{fetchData:e,isLoading:s}),r.jsx(a.u,{fetchData:e,isLoading:s,refresh:t})]})})}},12104:(e,s,t)=>{"use strict";t.d(s,{S:()=>o});var r=t(10326),a=t(77863),i=t(46226),n=t(17577),l=t.n(n),d=t(99447);function o({defaultValue:e,onChange:s,className:t,children:n,disabled:o=!1,id:c}){let[u,f]=l().useState(e),{getRootProps:m,getInputProps:x}=(0,d.uI)({onDrop:e=>{let t=e?.[0];t&&(s(t),f(URL.createObjectURL(t)))},disabled:o});return(0,r.jsxs)("div",{...m({className:(0,a.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t)}),children:[!!u&&r.jsx(i.default,{src:u,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),r.jsx("input",{id:c,...x()}),!u&&r.jsx("div",{children:n})]})}},33646:(e,s,t)=>{"use strict";t.d(s,{a:()=>f});var r=t(10326),a=t(90772),i=t(60097),n=t(6216),l=t(90434),d=t(35047),o=t(17577),c=t.n(o),u=t(70012);function f({tabs:e,fullWidth:s=!0,defaultSegment:t}){let[a,i]=(0,o.useState)(()=>e.map(e=>({...e,placeTo:"nav"}))),[n,l]=(0,o.useState)("");(0,d.useSelectedLayoutSegment)();let c=(0,o.useCallback)((e,s)=>{i(t=>t.map(t=>t.id===e?{...t,placeTo:s}:t))},[]);return(0,r.jsxs)("div",{className:`inline-flex h-12 items-center rounded-lg bg-accent p-1 text-muted-foreground ${s?"w-full":""}`,children:[a.map(e=>"nav"===e.placeTo?r.jsx(m,{...e,isActive:n===e.id,onClick:()=>l(e.id),updateTabPlace:c},e.id):null),r.jsx("div",{className:"ml-auto",children:r.jsx(x,{navItems:a,activeTabId:n})})]})}function m({title:e,id:s,icon:t,href:a,isActive:i,onClick:n,updateTabPlace:d}){let c=(0,o.useRef)(null);return(0,o.useCallback)(()=>{let e=c.current?.getBoundingClientRect(),t=window?.innerWidth;e&&t<e.right+150?d(s,"menu"):d(s,"nav")},[s,d]),(0,r.jsxs)(l.default,{href:a,"data-state":i?"active":"",onClick:n,prefetch:!1,ref:c,className:"inline-flex h-10 w-56 items-center justify-center gap-1 whitespace-nowrap rounded-md px-4 py-1.5 text-sm font-medium text-secondary-text ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[t,r.jsx("span",{children:e})]})}function x({navItems:e,activeTabId:s}){let[t,d]=c().useState(!1),o=e.filter(e=>"menu"===e.placeTo),{t:f}=(0,u.$G)();return 0===o.length?null:(0,r.jsxs)(i.h_,{open:t,onOpenChange:d,children:[r.jsx(i.$F,{asChild:!0,children:(0,r.jsxs)(a.z,{variant:"outline",className:"h-10 text-sm font-medium",children:[f("More"),r.jsx(n.Z,{size:16})]})}),r.jsx(i.AW,{children:o.map(e=>r.jsx(i.Xi,{"data-active":s===e.id,className:"data-[active=true]:bg-accent",children:(0,r.jsxs)(l.default,{href:e.href,prefetch:!1,onClick:()=>d(!1),className:"flex h-full w-full items-center gap-2",children:[e.icon,r.jsx("span",{children:e.title})]})},e.id))})]})}},31810:(e,s,t)=>{"use strict";t.d(s,{X:()=>i});var r=t(10326),a=t(77863);function i({className:e}){return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,a.ZP)("fill-primary",e),children:[r.jsx("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),r.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},90210:(e,s,t)=>{"use strict";t.d(s,{u:()=>D});var r=t(10326),a=t(5158),i=t(31810),n=t(56672),l=t(90772),d=t(55632),o=t(31048),c=t(8281),u=t(74064),f=t(17577),m=t(74723),x=t(12104),p=t(92392),h=t(34474),g=t(49547),j=t(50833);async function v(e){try{let s=new FormData;s.append("documentType",e.documentType),s.append("front",e.documentFrontSide),s.append("back",e.documentBackSide),s.append("selfie",e.selfie);let t=await g.Z.post("/kycs/submit",s,{headers:{"Content-Type":"multipart/form-data"}});return{statusCode:t.status,statusText:t.statusText,status:200===t.status||201===t.status,message:t.data?.message??""}}catch(r){let e=500,s="Internal Server Error",t="An unknown error occurred";return(0,j.IZ)(r)?(e=r.response?.status??500,s=r.response?.statusText??"Internal Server Error",t=r.response?.data?.message??r.message):r instanceof Error&&(t=r.message),{statusCode:e,statusText:s,status:!1,message:t,error:r}}}var b=t(77863),y=t(27256);let N=["image/png","image/jpeg","image/jpg","image/webp"],w=y.z.any().optional().refine(e=>!e||e.size<=3145728,"File size must be less than 3MB").refine(e=>!e||N.includes(e.type),"File must be a PNG,JPEG,JPG,WEBP"),C=y.z.object({documentType:y.z.string({required_error:"Document type is required."}),documentFrontSide:w,documentBackSide:w,selfie:w});var k=t(983),S=t(44284),Z=t(70012),P=t(85999),z=t(43173);let T=["NID","Passport","Driving"];function D({fetchData:e,isLoading:s,refresh:t}){let[g,y]=(0,f.useTransition)(),{t:N}=(0,Z.$G)(),w=(0,m.cI)({mode:"all",resolver:(0,u.F)(C),defaultValues:{documentType:e?.documentType??"",documentFrontSide:"",documentBackSide:"",selfie:""}});return r.jsx(d.l0,{...w,children:r.jsx("form",{onSubmit:w.handleSubmit(e=>{y(async()=>{let s=await v(e);if(s&&s.status)t(),P.toast.success(s.message);else if(422===s.statusCode){if((0,j.IZ)(s.error)&&s.error?.response?.data?.messages&&Array.isArray(s.error?.response?.data?.messages)){let e=s.error?.response?.data?.messages;e?.forEach(e=>{z.EQ(e).with({field:"front"},()=>w.setError("documentFrontSide",{message:N(e?.message),type:"custom"})).with({field:"back"},()=>w.setError("documentBackSide",{message:N(e?.message),type:"custom"})).with({field:"selfie"},()=>w.setError("selfie",{message:N(e?.message),type:"custom"})).otherwise(()=>null)}),P.toast.error(N("Please provide all required field."))}}else P.toast.error(N(s.message))})}),"data-loading":s,className:"rounded-xl border border-border bg-background data-[loading=true]:pointer-events-none data-[loading=true]:opacity-50",children:(0,r.jsxs)(n.Qd,{value:"DOCUMENT_INFORMATION",className:"border-none px-4 py-0",children:[r.jsx(n.o4,{className:"py-6 hover:no-underline",children:(0,r.jsxs)("div",{className:"flex items-center gap-10 text-base font-medium leading-[22px]",children:[N("Documents"),s&&r.jsx(p.Loader,{title:N("Loading...")})]})}),(0,r.jsxs)(n.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[r.jsx(d.Wi,{control:w.control,name:"documentType",render:({field:s})=>(0,r.jsxs)(d.xJ,{children:[r.jsx(d.lX,{children:N("Document Type")}),r.jsx(d.NI,{children:(0,r.jsxs)(h.Ph,{disabled:e?.status==="verified",value:s.value,onValueChange:s.onChange,children:[r.jsx(h.i4,{className:"data-[placeholder]:text-placeholder w-full max-w-[337px] bg-accent disabled:opacity-100 data-[placeholder]:text-base [&>svg>path]:stroke-primary [&>svg]:size-6 [&>svg]:opacity-100",children:r.jsx(h.ki,{placeholder:N("Select document type")})}),r.jsx(h.Bw,{align:"start",children:T.map(e=>r.jsx(h.Ql,{value:e.toLowerCase(),children:e},e))})]})}),r.jsx(d.zG,{})]})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h5",{className:"mb-4 font-medium",children:[" ",N("Attach pictures")," "]}),(0,r.jsxs)("div",{className:"flex w-full flex-wrap items-center gap-2.5 gap-y-4",children:[r.jsx(d.Wi,{control:w.control,name:"documentFrontSide",render:({field:s})=>(0,r.jsxs)(d.xJ,{className:"w-full md:w-auto",children:[r.jsx(d.lX,{className:"text-base font-medium text-secondary-text",children:N("Front Side")}),r.jsx(d.NI,{children:(0,r.jsxs)("div",{className:"flex w-full flex-col gap-2",children:[r.jsx(x.S,{id:"documentFrontSideFile",disabled:e?.status==="verified",defaultValue:(0,b.qR)(e?.front),onChange:e=>s.onChange(e),className:"flex h-[270px] w-full items-center justify-center border-dashed border-primary bg-transparent sm:w-[400px]",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[r.jsx(i.X,{}),(0,r.jsxs)("p",{className:"text-sm font-normal text-primary",children:[N("Drag and drop file here or upload")," "]})]})}),e?.status==="verified"?null:r.jsx(l.z,{asChild:!0,type:"button",className:"h-8 px-[12px] py-[5px] hover:cursor-pointer disabled:pointer-events-none",children:(0,r.jsxs)(o.Z,{htmlFor:"documentFrontSideFile",children:[r.jsx(k.Z,{size:20}),r.jsx("span",{className:"text-sm font-semibold",children:N("Upload")})]})})]})}),r.jsx(d.zG,{})]})}),r.jsx(a.J,{condition:"passport"!==w.watch("documentType"),children:r.jsx(d.Wi,{control:w.control,name:"documentBackSide",render:({field:s})=>(0,r.jsxs)(d.xJ,{className:"w-full md:w-auto",children:[r.jsx(d.lX,{className:"text-base font-medium text-secondary-text",children:N("Back Side")}),r.jsx(d.NI,{children:(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[r.jsx(x.S,{id:"documentBackSideFile",disabled:e?.status==="verified",defaultValue:(0,b.qR)(e?.back),onChange:e=>s.onChange(e),className:"flex h-[270px] w-full items-center justify-center border-dashed border-primary bg-transparent sm:w-[400px]",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[r.jsx(i.X,{}),(0,r.jsxs)("p",{className:"text-sm font-normal text-primary",children:[N("Drag and drop file here or upload")," "]})]})}),e?.status==="verified"?null:r.jsx(l.z,{asChild:!0,type:"button",className:"h-8 px-[12px] py-[5px] hover:cursor-pointer",children:(0,r.jsxs)(o.Z,{htmlFor:"documentBackSideFile",children:[r.jsx(k.Z,{size:20}),r.jsx("span",{className:"text-sm font-semibold",children:N("Upload")})]})})]})}),r.jsx(d.zG,{})]})})})]})]}),r.jsx(c.Z,{className:"border-b border-dashed bg-transparent"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h5",{className:"mb-4 font-medium",children:[" ",N("Attach selfie")," "]}),r.jsx(d.Wi,{control:w.control,name:"selfie",render:({field:s})=>(0,r.jsxs)(d.xJ,{className:"w-full md:w-auto",children:[r.jsx(d.lX,{className:"text-base font-medium text-secondary-text",children:N("Selfie")}),r.jsx(d.NI,{children:(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[r.jsx(x.S,{disabled:e?.status==="verified",defaultValue:(0,b.qR)(e?.selfie),id:"selfieFile",onChange:e=>s.onChange(e),className:"flex h-[270px] w-full items-center justify-center border-dashed border-primary bg-transparent sm:w-[400px]",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[r.jsx(i.X,{}),r.jsx("p",{className:"text-sm font-normal text-primary",children:N("Drag and drop file here or upload")})]})}),e?.status==="verified"?null:r.jsx(l.z,{asChild:!0,type:"button",className:"h-8 max-w-[400px] px-[12px] py-[5px] hover:cursor-pointer",children:(0,r.jsxs)(o.Z,{htmlFor:"selfieFile",children:[r.jsx(k.Z,{size:20}),r.jsx("span",{className:"text-sm font-semibold",children:N("Upload")})]})})]})}),r.jsx(d.zG,{})]})})]}),e?.status==="verified"?null:r.jsx("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,r.jsxs)(l.z,{disabled:g,children:[(0,r.jsxs)(a.J,{condition:!g,children:[N("Save"),r.jsx(S.Z,{size:20})]}),r.jsx(a.J,{condition:g,children:r.jsx(p.Loader,{title:N("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}},11969:(e,s,t)=>{"use strict";t.d(s,{Z:()=>m});var r=t(10326),a=t(5158),i=t(92392),n=t(56672),l=t(43273),d=t(567),o=t(9169),c=t(71282),u=t(34612),f=t(70012);function m({fetchData:e,isLoading:s}){let{t}=(0,f.$G)();return r.jsx("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(n.Qd,{value:"KYC_STATUS",className:"border-none px-4 py-0",children:[r.jsx(n.o4,{className:"py-6 hover:no-underline",children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx("p",{className:"text-base font-medium leading-[22px]",children:t("KYC Status")}),r.jsx(a.J,{condition:e?.status==="verified",children:r.jsx(d.C,{className:"h-5 bg-spacial-green text-[10px] text-spacial-green-foreground",children:t("Verified")})}),r.jsx(a.J,{condition:e?.status==="pending",children:r.jsx(d.C,{className:"h-5 bg-primary text-[10px] text-primary-foreground",children:t("Pending")})}),r.jsx(a.J,{condition:!["pending","verified","failed"].includes(e?.status),children:r.jsx(d.C,{className:"h-5 bg-foreground text-[10px] text-background",children:t("Awaiting submission")})})]})}),r.jsx(a.J,{condition:s,children:r.jsx(n.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:r.jsx(i.Loader,{})})}),r.jsx(a.J,{condition:!s,children:(0,r.jsxs)(n.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[r.jsx(a.J,{condition:e?.status==="verified",children:(0,r.jsxs)(l.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-spacial-green",children:[r.jsx(o.Z,{size:"32",variant:"Bulk"}),r.jsx(l.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:t("Your account is verified")}),r.jsx(l.X,{className:"ml-2.5 text-sm font-normal",children:t("Your account has been successfully verified. If you have any questions feel free to reach out to our support team.")})]})}),r.jsx(a.J,{condition:e?.status==="pending",children:(0,r.jsxs)(l.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-primary",children:[r.jsx(c.Z,{size:"32",variant:"Bulk"}),r.jsx(l.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:t("Pending verification")}),r.jsx(l.X,{className:"ml-2.5 text-sm font-normal",children:t("Thank you for submitting your documents! Your KYC verification is currently under review. Our team is working to process your submission as quickly as possible.")})]})}),r.jsx(a.J,{condition:!["pending","verified","failed"].includes(e?.status),children:(0,r.jsxs)(l.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-foreground",children:[r.jsx(u.Z,{size:"32",variant:"Bulk"}),r.jsx(l.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:t("You have not submitted documents yet.")}),r.jsx(l.X,{className:"ml-2.5 text-sm font-normal",children:t("Your account is not yet verified. Please complete the KYC process by submitting the required documents.")})]})})]})})]})})}},56672:(e,s,t)=>{"use strict";t.d(s,{Qd:()=>o,UQ:()=>d,o4:()=>c,vF:()=>u});var r=t(10326),a=t(57793),i=t(17577),n=t(77863),l=t(6216);let d=a.fC,o=i.forwardRef(({className:e,...s},t)=>r.jsx(a.ck,{ref:t,className:(0,n.ZP)("border-b",e),...s}));o.displayName="AccordionItem";let c=i.forwardRef(({className:e,children:s,...t},i)=>r.jsx(a.h4,{className:"flex",children:(0,r.jsxs)(a.xz,{ref:i,className:(0,n.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...t,children:[s,r.jsx(l.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));c.displayName=a.xz.displayName;let u=i.forwardRef(({className:e,children:s,...t},i)=>r.jsx(a.VY,{ref:i,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...t,children:r.jsx("div",{className:(0,n.ZP)("pb-4 pt-0",e),children:s})}));u.displayName=a.VY.displayName},43273:(e,s,t)=>{"use strict";t.d(s,{Cd:()=>o,X:()=>c,bZ:()=>d});var r=t(10326),a=t(79360),i=t(17577),n=t(77863);let l=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=i.forwardRef(({className:e,variant:s,...t},a)=>r.jsx("div",{ref:a,role:"alert",className:(0,n.ZP)(l({variant:s}),e),...t}));d.displayName="Alert";let o=i.forwardRef(({className:e,...s},t)=>r.jsx("h5",{ref:t,className:(0,n.ZP)("mb-1 font-medium leading-none tracking-tight",e),...s}));o.displayName="AlertTitle";let c=i.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.ZP)("text-sm [&_p]:leading-relaxed",e),...s}));c.displayName="AlertDescription"},55632:(e,s,t)=>{"use strict";t.d(s,{NI:()=>h,Wi:()=>u,l0:()=>o,lX:()=>p,xJ:()=>x,zG:()=>g});var r=t(10326),a=t(34214),i=t(17577),n=t(74723),l=t(31048),d=t(77863);let o=n.RV,c=i.createContext({}),u=({...e})=>r.jsx(c.Provider,{value:{name:e.name},children:r.jsx(n.Qr,{...e})}),f=()=>{let e=i.useContext(c),s=i.useContext(m),{getFieldState:t,formState:r}=(0,n.Gc)(),a=t(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...a}},m=i.createContext({}),x=i.forwardRef(({className:e,...s},t)=>{let a=i.useId();return r.jsx(m.Provider,{value:{id:a},children:r.jsx("div",{ref:t,className:(0,d.ZP)("space-y-2",e),...s})})});x.displayName="FormItem";let p=i.forwardRef(({className:e,required:s,...t},a)=>{let{error:i,formItemId:n}=f();return r.jsx("span",{children:r.jsx(l.Z,{ref:a,className:(0,d.ZP)(i&&"text-base font-medium text-destructive",e),htmlFor:n,...t})})});p.displayName="FormLabel";let h=i.forwardRef(({...e},s)=>{let{error:t,formItemId:i,formDescriptionId:n,formMessageId:l}=f();return r.jsx(a.g7,{ref:s,id:i,"aria-describedby":t?`${n} ${l}`:`${n}`,"aria-invalid":!!t,...e})});h.displayName="FormControl",i.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:a}=f();return r.jsx("p",{ref:t,id:a,className:(0,d.ZP)("text-sm text-muted-foreground",e),...s})}).displayName="FormDescription";let g=i.forwardRef(({className:e,children:s,...t},a)=>{let{error:i,formMessageId:n}=f(),l=i?String(i?.message):s;return l?r.jsx("p",{ref:a,id:n,className:(0,d.ZP)("text-sm font-medium text-destructive",e),...t,children:l}):null});g.displayName="FormMessage"},31048:(e,s,t)=>{"use strict";t.d(s,{Z:()=>c});var r=t(10326),a=t(34478),i=t(79360),n=t(17577),l=t(77863);let d=(0,i.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=n.forwardRef(({className:e,...s},t)=>r.jsx(a.f,{ref:t,className:(0,l.ZP)(d(),e),...s}));o.displayName=a.f.displayName;let c=o},34474:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>h,Ph:()=>u,Ql:()=>g,i4:()=>m,ki:()=>f});var r=t(10326),a=t(13869),i=t(96633),n=t(941),l=t(17577),d=t(77863),o=t(6216),c=t(44284);let u=a.fC;a.ZA;let f=a.B4,m=l.forwardRef(({className:e,children:s,...t},i)=>(0,r.jsxs)(a.xz,{ref:i,className:(0,d.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,r.jsx(a.JO,{asChild:!0,children:r.jsx(o.Z,{size:"24",color:"#292D32"})})]}));m.displayName=a.xz.displayName;let x=l.forwardRef(({className:e,...s},t)=>r.jsx(a.u_,{ref:t,className:(0,d.ZP)("flex cursor-default items-center justify-center py-1",e),...s,children:r.jsx(i.Z,{className:"h-4 w-4"})}));x.displayName=a.u_.displayName;let p=l.forwardRef(({className:e,...s},t)=>r.jsx(a.$G,{ref:t,className:(0,d.ZP)("flex cursor-default items-center justify-center py-1",e),...s,children:r.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=a.$G.displayName;let h=l.forwardRef(({className:e,children:s,position:t="popper",...i},n)=>r.jsx(a.h_,{children:(0,r.jsxs)(a.VY,{ref:n,className:(0,d.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...i,children:[r.jsx(x,{}),r.jsx(a.l_,{className:(0,d.ZP)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),r.jsx(p,{})]})}));h.displayName=a.VY.displayName,l.forwardRef(({className:e,...s},t)=>r.jsx(a.__,{ref:t,className:(0,d.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=a.__.displayName;let g=l.forwardRef(({className:e,children:s,...t},i)=>(0,r.jsxs)(a.ck,{ref:i,className:(0,d.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(a.wU,{children:r.jsx(c.Z,{variant:"Bold",className:"h-4 w-4"})})}),r.jsx(a.eT,{children:s})]}));g.displayName=a.ck.displayName,l.forwardRef(({className:e,...s},t)=>r.jsx(a.Z0,{ref:t,className:(0,d.ZP)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=a.Z0.displayName},27719:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var r=t(49547),a=t(84455);function i(){let{data:e,isLoading:s,error:t,mutate:i}=(0,a.ZP)("/kycs/detail",e=>r.Z.get(e),{refreshInterval:0,revalidateOnFocus:!1,revalidateOnReconnect:!1,shouldRetryOnError:!1});return{data:e?.data,isLoading:s,error:t,refresh:i}}},84514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(40099),i=t(76609);function n({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(i.Z,{userRole:"agent"}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(a.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},18406:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.a,{})})}},67054:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex justify-center py-10",children:r.jsx(a.a,{})})}},51827:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\settings\kyc-verification-settings\page.tsx#default`)},40903:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510);t(71159);let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,r.jsxs)("div",{className:"",children:[r.jsx(a,{}),r.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},285:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex justify-center",children:r.jsx(a.a,{})})}},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(40099),i=t(76609);function n({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(i.Z,{userRole:"customer"}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(a.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.a,{})})}},15629:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex justify-center py-10",children:r.jsx(a.a,{})})}},95410:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\settings\kyc-verification-settings\page.tsx#default`)},48218:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510);t(71159);let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,r.jsxs)("div",{children:[r.jsx(a,{}),r.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},80161:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex justify-center py-10",children:r.jsx(a.a,{})})}},76235:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex justify-center py-10",children:r.jsx(a.a,{})})}},28566:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\settings\kyc-verification-settings\page.tsx#default`)},80482:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510);t(71159);let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,r.jsxs)("div",{children:[r.jsx(a,{}),r.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},7016:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(19510),a=t(48413);function i(){return r.jsx("div",{className:"flex justify-center",children:r.jsx(a.a,{})})}}};