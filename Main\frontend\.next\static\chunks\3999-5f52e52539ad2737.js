"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3999],{64394:function(e,t,r){r.d(t,{Z:function(){return h}});var n=r(74677),o=r(2265),a=r(40718),l=r.n(a),i=["variant","color","size"],c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2zM18 12.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M12.82 12H3.5M20.33 12h-3.48"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M7.81 2h8.37C19.83 2 22 4.17 22 7.81v8.37c0 3.64-2.17 5.81-5.81 5.81H7.81C4.17 22 2 19.83 2 16.19V7.81C2 4.17 4.17 2 7.81 2z",opacity:".4"}),o.createElement("path",{fill:t,d:"M5.47 11.47l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06z"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M20.5 12H3.67"}))},f=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M9.57 18.82c-.19 0-.38-.07-.53-.22l-6.07-6.07a.754.754 0 010-1.06L9.04 5.4c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L4.56 12l5.54 5.54c.29.29.29.77 0 1.06-.14.15-.34.22-.53.22z"}),o.createElement("path",{fill:t,d:"M20.5 12.75H3.67c-.41 0-.75-.34-.75-.75s.34-.75.75-.75H20.5c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07"}),o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M20.5 12H3.67",opacity:".4"}))},p=function(e,t){switch(e){case"Bold":return o.createElement(c,{color:t});case"Broken":return o.createElement(u,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(f,{color:t});case"TwoTone":return o.createElement(m,{color:t})}},h=(0,o.forwardRef)(function(e,t){var r=e.variant,a=e.color,l=e.size,c=(0,n._)(e,i);return o.createElement("svg",(0,n.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),p(r,a))});h.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="ArrowLeft"},22291:function(e,t,r){r.d(t,{Z:function(){return h}});var n=r(74677),o=r(2265),a=r(40718),l=r.n(a),i=["variant","color","size"],c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),o.createElement("path",{fill:t,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},f=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},m=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},p=function(e,t){switch(e){case"Bold":return o.createElement(c,{color:t});case"Broken":return o.createElement(u,{color:t});case"Bulk":return o.createElement(s,{color:t});case"Linear":default:return o.createElement(d,{color:t});case"Outline":return o.createElement(f,{color:t});case"TwoTone":return o.createElement(m,{color:t})}},h=(0,o.forwardRef)(function(e,t){var r=e.variant,a=e.color,l=e.size,c=(0,n._)(e,i);return o.createElement("svg",(0,n.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:l,height:l,viewBox:"0 0 24 24",fill:"none"}),p(r,a))});h.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="ArrowRight2"},79205:function(e,t,r){r.d(t,{Z:function(){return c}});var n=r(2265);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:u="",children:s,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:c?24*Number(i)/Number(o):i,className:a("lucide",u),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),c=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:c,...u}=r;return(0,n.createElement)(i,{ref:l,iconNode:t,className:a("lucide-".concat(o(e)),c),...u})});return r.displayName="".concat(e),r}},30401:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},99376:function(e,t,r){var n=r(35475);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useSelectedLayoutSegment")&&r.d(t,{useSelectedLayoutSegment:function(){return n.useSelectedLayoutSegment}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})},61146:function(e,t,r){r.d(t,{F$:function(){return E},NY:function(){return M},Ee:function(){return b},fC:function(){return y}});var n=r(2265),o=r(73966),a=r(26606),l=r(61188),i=r(66840),c=r(82558);function u(){return()=>{}}var s=r(57437),d="Avatar",[f,m]=(0,o.b)(d),[p,h]=f(d),v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,s.jsx)(p,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,s.jsx)(i.WV.span,{...o,ref:t})})});v.displayName=d;var k="AvatarImage",E=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...f}=e,m=h(k,r),p=function(e,t){let{referrerPolicy:r,crossOrigin:o}=t,a=(0,c.useSyncExternalStore)(u,()=>!0,()=>!1),i=n.useRef(null),s=a?(i.current||(i.current=new window.Image),i.current):null,[d,f]=n.useState(()=>L(s,e));return(0,l.b)(()=>{f(L(s,e))},[s,e]),(0,l.b)(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),n=e("error");return s.addEventListener("load",t),s.addEventListener("error",n),r&&(s.referrerPolicy=r),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",t),s.removeEventListener("error",n)}},[s,o,r]),d}(o,f),v=(0,a.W)(e=>{d(e),m.onImageLoadingStatusChange(e)});return(0,l.b)(()=>{"idle"!==p&&v(p)},[p,v]),"loaded"===p?(0,s.jsx)(i.WV.img,{...f,ref:t,src:o}):null});E.displayName=k;var g="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=h(g,r),[c,u]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(i.WV.span,{...a,ref:t}):null});function L(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=g;var y=v,b=E,M=w},9270:function(e,t,r){r.d(t,{fC:function(){return M},z$:function(){return S}});var n=r(2265),o=r(98575),a=r(73966),l=r(6741),i=r(80886),c=r(6718),u=r(90420),s=r(71599),d=r(66840),f=r(57437),m="Checkbox",[p,h]=(0,a.b)(m),[v,k]=p(m),E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:c,defaultChecked:u,required:s,disabled:p,value:h="on",onCheckedChange:k,form:E,...g}=e,[w,M]=n.useState(null),S=(0,o.e)(t,e=>M(e)),x=n.useRef(!1),z=!w||E||!!w.closest("form"),[C,R]=(0,i.T)({prop:c,defaultProp:null!=u&&u,onChange:k,caller:m}),j=n.useRef(C);return n.useEffect(()=>{let e=null==w?void 0:w.form;if(e){let t=()=>R(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[w,R]),(0,f.jsxs)(v,{scope:r,state:C,disabled:p,children:[(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":y(C)?"mixed":C,"aria-required":s,"data-state":b(C),"data-disabled":p?"":void 0,disabled:p,value:h,...g,ref:S,onKeyDown:(0,l.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(e.onClick,e=>{R(e=>!!y(e)||!e),z&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),z&&(0,f.jsx)(L,{control:w,bubbles:!x.current,name:a,value:h,checked:C,required:s,disabled:p,form:E,style:{transform:"translateX(-100%)"},defaultChecked:!y(u)&&u})]})});E.displayName=m;var g="CheckboxIndicator",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=k(g,r);return(0,f.jsx)(s.z,{present:n||y(a.state)||!0===a.state,children:(0,f.jsx)(d.WV.span,{"data-state":b(a.state),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=g;var L=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,control:a,checked:l,bubbles:i=!0,defaultChecked:s,...m}=e,p=n.useRef(null),h=(0,o.e)(p,t),v=(0,c.D)(l),k=(0,u.t)(a);n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==l&&t){let r=new Event("click",{bubbles:i});e.indeterminate=y(l),t.call(e,!y(l)&&l),e.dispatchEvent(r)}},[v,l,i]);let E=n.useRef(!y(l)&&l);return(0,f.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=s?s:E.current,...m,tabIndex:-1,ref:h,style:{...m.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return"indeterminate"===e}function b(e){return y(e)?"indeterminate":e?"checked":"unchecked"}L.displayName="CheckboxBubbleInput";var M=E,S=w},6718:function(e,t,r){r.d(t,{D:function(){return o}});var n=r(2265);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90420:function(e,t,r){r.d(t,{t:function(){return a}});var n=r(2265),o=r(61188);function a(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);