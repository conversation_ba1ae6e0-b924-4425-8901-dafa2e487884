exports.id=3352,exports.ids=[3352],exports.modules={26807:(e,t,s)=>{Promise.resolve().then(s.bind(s,73e3)),Promise.resolve().then(s.bind(s,75285))},15830:(e,t,s)=>{"use strict";s.d(t,{Z:()=>I});var a=s(10326),n=s(56038),r=s(56140),i=s(567),l=s(77863),d=s(66114),o=s(90434),c=s(17577),m=s.n(c),u=s(70012),g=s(90772),h=s(60097),x=s(49547),f=s(10734);async function p(e){try{let t=await x.Z.put(`/admin/exchanges/accept/${e}`,{});return(0,f.B)(t)}catch(e){return(0,f.D)(e)}}async function y(e){try{let t=await x.<PERSON>.put(`/admin/exchanges/decline/${e}`,{});return(0,f.B)(t)}catch(e){return(0,f.D)(e)}}var k=s(9489),j=s(31112),v=s(47237),b=s(25896),w=s(35047),N=s(85999);function A({row:e,...t}){let{t:s}=(0,u.$G)(),n=(0,w.useRouter)(),r=()=>{N.toast.promise(p(e.original.id),{loading:s("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return t.table.getState().onRefresh?.(),e.message},error:e=>e.message})},i=()=>{N.toast.promise(y(e.original.id),{loading:s("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return t.table.getState().onRefresh?.(),e.message},error:e=>e.message})};return(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(g.z,{type:"button",variant:"ghost",size:"icon",className:"hover:bg-muted",onClick:()=>n.push(`/exchanges/${e.original.id}`),children:a.jsx(k.Z,{})}),(0,a.jsxs)(h.h_,{children:[a.jsx(h.$F,{asChild:!0,children:a.jsx(g.z,{size:"icon",variant:"outline",className:"h-8 w-8 rounded-md bg-background text-primary hover:bg-background",children:a.jsx(j.Z,{size:"20",className:"text-primary"})})}),(0,a.jsxs)(h.AW,{className:"flex flex-col gap-1.5 rounded-sm p-1",align:"end",children:[(0,a.jsxs)(h.Xi,{onSelect:()=>r(),className:"my-0 flex h-full items-center gap-1 px-4 py-2 text-base font-medium leading-[22px] data-[highlighted]:bg-success data-[highlighted]:text-success-foreground",children:[a.jsx(v.Z,{}),s("Approve request")]}),(0,a.jsxs)(h.Xi,{onSelect:()=>i(),className:"my-0 flex h-full items-center gap-1 px-4 py-2 text-base font-medium leading-[22px] data-[highlighted]:bg-danger data-[highlighted]:text-danger-foreground",children:[a.jsx(b.Z,{}),s("Reject request")]})]})]})]})}let C=new l.F;function I({data:e,meta:t,isLoading:s,refresh:c}){let[g,h]=m().useState([]),{t:x}=(0,u.$G)();return a.jsx(r.Z,{data:e?e?.map(e=>new d.C(e)):null,isLoading:s,sorting:g,setSorting:h,onRefresh:c,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"trxId",header:x("Trx ID"),cell:({row:e})=>a.jsx(o.default,{href:`/exchanges/${e.original?.id}`,className:"text-xs font-normal text-foreground hover:underline",children:e.original?.trxId})},{id:"createdAt",header:x("Date"),cell:({row:e})=>(0,a.jsxs)("div",{children:[a.jsx("span",{className:"block min-w-24 text-sm font-normal leading-5 text-foreground",children:e.original.getCreatedAt("dd MMM yyyy;")}),a.jsx("span",{className:"block min-w-24 text-sm font-normal leading-5 text-foreground",children:e.original.getCreatedAt("hh:mm a")})]})},{id:"status",header:x("Status"),cell:({row:e})=>e.original?.trxId==="completed"?a.jsx(i.C,{variant:"success",children:(0,l.fl)(e.original?.trxId)}):e.original?.trxId==="pending"?a.jsx(i.C,{variant:"secondary",className:"bg-muted",children:(0,l.fl)(e.original?.trxId)}):e.original?.trxId==="failed"?a.jsx(i.C,{variant:"destructive",children:(0,l.fl)(e.original?.trxId)}):a.jsx(i.C,{variant:"secondary",className:"bg-muted",children:x("Pending")})},{id:"from",header:x("From"),cell:({row:e})=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:C.formatVC(e.original.metaData.amountFrom,e.original.metaData.currencyFrom)})},{id:"to",header:x("To"),cell:({row:e})=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:C.formatVC(e.original.amount,e.original.metaData.currencyTo)})},{id:"fee",header:x("Fee"),cell:({row:e})=>a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:C.formatVC(e.original.fee,e.original.metaData.currencyTo)})},{id:"after_processing",header:x("After Processing"),cell:({row:e})=>{let t;return t="exchange"===e.original.type?e.original?.metaData?.currencyTo:"deposit"===e.original.type?e.original?.metaData?.currency:e.original?.from?.currency,a.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:C.formatVC(e.original.total,t)})}},{id:"user",header:x("User"),cell:({row:e})=>a.jsx(n.Z,{row:e})},{id:"menu",header:x("Menu"),cell:e=>a.jsx(A,{...e})}]})}},56038:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var a=s(10326),n=s(28758),r=s(54033),i=s(90434);let l=function({row:e}){return(0,a.jsxs)(i.default,{href:(()=>{switch(e.original?.user?.roleId){case 1:return`/staffs/edit/${e.original?.user?.id}`;case 2:default:return`/customers/${e.original?.user?.customer?.id}?name=${e.original?.user?.customer?.name}&active=${e?.original?.user?.status}`;case 3:return`/merchants/${e.original?.userId}/${e.original?.user?.merchant?.id}?name=${e.original.user?.customer?.name}&active=${e?.original?.user?.status}`;case 4:return`/agents/${e.original?.userId}/${e.original?.user?.agent?.id}?name=${e.original.user?.customer?.name}&active=${e?.original?.user?.status}`}})(),className:"flex min-w-[80px] items-center gap-2 font-normal text-secondary-text hover:text-foreground",children:[(0,a.jsxs)(n.qE,{children:[a.jsx(n.F$,{src:e.original.user.customer.profileImage}),a.jsx(n.Q5,{children:(0,r.v)(e.original.user.customer.name)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-normal",children:e.original.user.customer.name}),e.original?.user.email?a.jsx("p",{className:"text-xs font-normal",children:e.original?.user.email}):null]})]})}},75285:(e,t,s)=>{"use strict";s.d(t,{default:()=>S});var a=s(10326),n=s(5158),r=s(90772),i=s(81638),l=s(6216),d=s(90434),o=s(35047),c=s(17577);function m({sidebarItem:e}){let[t,s]=c.useState("(dashboard)"),[m,u]=c.useState(!1),{setIsExpanded:g,device:h}=(0,i.q)(),x=(0,o.useSelectedLayoutSegment)();return c.useEffect(()=>{s(x)},[]),c.useEffect(()=>{u(e.segment===x)},[x,e.segment]),(0,a.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,a.jsxs)(d.default,{href:e.link,onClick:()=>{s(e.segment),e.children?.length||"Desktop"===h||g(!1)},"data-active":x===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[a.jsx(n.J,{condition:!!e.icon,children:a.jsx("div",{"data-active":x===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),a.jsx("span",{className:"flex-1",children:e.name}),a.jsx(n.J,{condition:!!e.children?.length,children:a.jsx(r.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:a.jsx(l.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),a.jsx(n.J,{condition:!!e.children?.length,children:a.jsx("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>a.jsx("li",{children:a.jsxs(d.default,{href:e.link,"data-active":t===e.segment,onClick:()=>{s(e.segment),"Desktop"!==h&&g(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[a.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=s(8281),g=s(4066),h=s(77863),x=s(1178),f=s(29169),p=s(40420),y=s(78564),k=s(53105),j=s(81770),v=s(45922),b=s(29764),w=s(26920),N=s(9155),A=s(41334),C=s(73686),I=s(75073),Z=s(44221),z=s(46226),D=s(70012);function S(){let{t:e}=(0,D.$G)(),{isExpanded:t,setIsExpanded:s}=(0,i.q)(),{logo:n,siteName:l}=(0,g.T)(),o=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:a.jsx(x.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:a.jsx(f.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:a.jsx(p.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:a.jsx(y.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:a.jsx(k.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:a.jsx(j.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:a.jsx(v.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:a.jsx(b.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:a.jsx(w.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:a.jsx(N.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:a.jsx(A.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:a.jsx(C.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:a.jsx(I.Z,{size:"20"}),link:"/settings"}]}];return(0,a.jsxs)("div",{"data-expanded":t,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[a.jsx(r.z,{size:"icon",variant:"outline",onClick:()=>s(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${t?"":"hidden"} lg:hidden`,children:a.jsx(Z.Z,{})}),a.jsx("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:a.jsx(d.default,{href:"/",className:"flex items-center justify-center",children:a.jsx(z.default,{src:(0,h.qR)(n),width:160,height:40,alt:l,className:"max-h-10 object-contain"})})}),a.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:o.map(e=>(0,a.jsxs)("div",{children:[""!==e.title?a.jsx("div",{children:a.jsx(u.Z,{className:"my-4"})}):null,a.jsx("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>a.jsx("li",{children:a.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},567:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var a=s(10326),n=s(79360);s(17577);var r=s(77863);let i=(0,n.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return a.jsx("div",{className:(0,r.ZP)(i({variant:t}),e),...s})}},66114:(e,t,s)=>{"use strict";s.d(t,{C:()=>i});var a=s(72450),n=s(71305),r=s(79308);class i{constructor(e){this.amount=0,this.fee=0,this.total=0,this.method=null,this.isBookmarked=!1,this.userId=3,this.id=e?.id,this.trxId=e.trxId,this.type=e?.type,this.from=e?.from?JSON.parse(e.from):null,this.to=e?.to?JSON.parse(e.to):null,this.amount=e?.amount,this.fee=e?.fee,this.total=e?.total,this.status=e?.status,this.method=e?.method,this.currency=e?.currency,this.isBookmarked=!!e?.isBookmarked,this.metaData=e?.metaData?JSON.parse(e.metaData):null,this.userId=e?.userId,this.createdAt=e?.createdAt?new Date(e.createdAt):void 0,this.updatedAt=e.updatedAt?new Date(e.updatedAt):void 0,this.user={...new a.n(e?.user),customer:e?.user?.customer?new r.O(e?.user?.customer):null,merchant:e?.user?.merchant?new r.O(e?.user?.merchant):null,agent:e?.user?.agent?new r.O(e?.user?.agent):null}}getCreatedAt(e="dd MMM yyyy"){return this.createdAt?(0,n.WU)(this.createdAt,e):"N/A"}getUpdatedAt(e="dd MMM yyyy"){return this.updatedAt?(0,n.WU)(this.updatedAt,e):"N/A"}}},72450:(e,t,s)=>{"use strict";s.d(t,{n:()=>i});var a=s(13263),n=s(13573),r=s(77863);class i{constructor(e){this.id=e?.id,this.name=e?.name,this.firstName=e?.firstName,this.lastName=e?.lastName,this.avatar=e?.avatar,this.roleId=e?.roleId,this.phone=(0,r.Fg)(e?.phone),this.email=e?.email,this.isEmailVerified=e?.isEmailVerified,this.status=e?.status,this.kycStatus=e?.kycStatus,this.lastIpAddress=e?.lastIpAddress,this.lastCountryName=e?.lastCountryName,this.passwordUpdated=e?.passwordUpdated,this.referredBy=e?.referredBy,this.otpCode=e?.otpCode,this.createdAt=e?.createdAt?new Date(e?.createdAt):void 0,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):void 0,this.role=new n.u(e?.role),this.dateOfBirth=e?.dob?new Date(e?.dob):void 0,this.gender=e?.gender,this.address=e?.address?new a.k(e?.address):null}}},42621:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(19510),n=s(48413);function r(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(n.a,{})})}},11840:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(19510),n=s(40099);let r=(0,s(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function i({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(r,{}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(n.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}s(71159)},33661:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(19510),n=s(48413);function r(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(n.a,{})})}}};