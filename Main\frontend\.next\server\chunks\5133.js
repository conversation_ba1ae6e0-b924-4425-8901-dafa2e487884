exports.id=5133,exports.ids=[5133],exports.modules={23560:(e,s,t)=>{Promise.resolve().then(t.bind(t,31321))},26807:(e,s,t)=>{Promise.resolve().then(t.bind(t,73e3)),Promise.resolve().then(t.bind(t,75285))},31321:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>k});var n=t(10326),i=t(17577),a=t(5158),r=t(33646),l=t(54943),d=t(78564),c=t(66786),o=t(57900),m=t(54747),u=t(92801),h=t(50493),g=t(44221),x=t(90434),f=t(35047),p=t(70012);function k(){let[e,s]=i.useState(""),t=(0,f.useSelectedLayoutSegment)(),k=(0,f.useSelectedLayoutSegments)(),y=(0,f.useSearchParams)(),{t:v}=(0,p.$G)(),j=[{title:v("Deposit Gateways"),icon:n.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:v("Withdraw Methods"),icon:n.jsx(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/withdraw-methods",id:"withdraw-methods"},{title:v("Plugins"),icon:n.jsx(c.Z,{size:"24",variant:"Bulk"}),href:"/settings/plugins",id:"plugins"},{title:v("Services"),icon:n.jsx(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/services",id:"services"},{title:v("Currency"),icon:n.jsx(m.Z,{size:"24",variant:"Bulk"}),href:"/settings/currencies",id:"currencies"},{title:v("Site Settings"),icon:n.jsx(u.Z,{size:"24",variant:"Bulk"}),href:"/settings/site-settings",id:"site-settings"},{title:v("Login Sessions"),icon:n.jsx(h.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return i.useLayoutEffect(()=>{t?s("gateways"===t?"__DEFAULT__":t):s("__DEFAULT__")},[t]),(0,n.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[n.jsx(a.J,{condition:k.length>1,children:(0,n.jsxs)("div",{className:"line-clamp-1 inline-flex max-w-full items-center gap-2 px-0 pb-4 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,n.jsxs)(x.default,{href:"__DEFAULT__"===e?"/settings":`/settings/${e}`,className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[n.jsx(g.Z,{className:"size-4 sm:size-6"}),v("Back")]}),(0,n.jsxs)("span",{className:"line-clamp-1 flex items-center gap-1 whitespace-nowrap text-sm font-semibold text-secondary-text",children:["/"," ","create"===k[1]?"Create withdraw method":y.get("name")]})]})}),n.jsx(r.a,{tabs:j,defaultSegment:"gateways"})]})}},75285:(e,s,t)=>{"use strict";t.d(s,{default:()=>L});var n=t(10326),i=t(5158),a=t(90772),r=t(81638),l=t(6216),d=t(90434),c=t(35047),o=t(17577);function m({sidebarItem:e}){let[s,t]=o.useState("(dashboard)"),[m,u]=o.useState(!1),{setIsExpanded:h,device:g}=(0,r.q)(),x=(0,c.useSelectedLayoutSegment)();return o.useEffect(()=>{t(x)},[]),o.useEffect(()=>{u(e.segment===x)},[x,e.segment]),(0,n.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,n.jsxs)(d.default,{href:e.link,onClick:()=>{t(e.segment),e.children?.length||"Desktop"===g||h(!1)},"data-active":x===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[n.jsx(i.J,{condition:!!e.icon,children:n.jsx("div",{"data-active":x===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),n.jsx("span",{className:"flex-1",children:e.name}),n.jsx(i.J,{condition:!!e.children?.length,children:n.jsx(a.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:n.jsx(l.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),n.jsx(i.J,{condition:!!e.children?.length,children:n.jsx("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>n.jsx("li",{children:n.jsxs(d.default,{href:e.link,"data-active":s===e.segment,onClick:()=>{t(e.segment),"Desktop"!==g&&h(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[n.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=t(8281),h=t(4066),g=t(77863),x=t(1178),f=t(29169),p=t(40420),k=t(78564),y=t(53105),v=t(81770),j=t(45922),b=t(29764),w=t(26920),N=t(9155),z=t(41334),S=t(73686),Z=t(75073),P=t(44221),C=t(46226),E=t(70012);function L(){let{t:e}=(0,E.$G)(),{isExpanded:s,setIsExpanded:t}=(0,r.q)(),{logo:i,siteName:l}=(0,h.T)(),c=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:n.jsx(x.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:n.jsx(f.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:n.jsx(p.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:n.jsx(k.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:n.jsx(y.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:n.jsx(v.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:n.jsx(j.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:n.jsx(b.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:n.jsx(w.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:n.jsx(N.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:n.jsx(z.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:n.jsx(S.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:n.jsx(Z.Z,{size:"20"}),link:"/settings"}]}];return(0,n.jsxs)("div",{"data-expanded":s,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[n.jsx(a.z,{size:"icon",variant:"outline",onClick:()=>t(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${s?"":"hidden"} lg:hidden`,children:n.jsx(P.Z,{})}),n.jsx("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:n.jsx(d.default,{href:"/",className:"flex items-center justify-center",children:n.jsx(C.default,{src:(0,g.qR)(i),width:160,height:40,alt:l,className:"max-h-10 object-contain"})})}),n.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:c.map(e=>(0,n.jsxs)("div",{children:[""!==e.title?n.jsx("div",{children:n.jsx(u.Z,{className:"my-4"})}):null,n.jsx("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>n.jsx("li",{children:n.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},33646:(e,s,t)=>{"use strict";t.d(s,{a:()=>u});var n=t(10326),i=t(90772),a=t(60097),r=t(6216),l=t(90434),d=t(35047),c=t(17577),o=t.n(c),m=t(70012);function u({tabs:e,fullWidth:s=!0,defaultSegment:t}){let[i,a]=(0,c.useState)(()=>e.map(e=>({...e,placeTo:"nav"}))),[r,l]=(0,c.useState)("");(0,d.useSelectedLayoutSegment)();let o=(0,c.useCallback)((e,s)=>{a(t=>t.map(t=>t.id===e?{...t,placeTo:s}:t))},[]);return(0,n.jsxs)("div",{className:`inline-flex h-12 items-center rounded-lg bg-accent p-1 text-muted-foreground ${s?"w-full":""}`,children:[i.map(e=>"nav"===e.placeTo?n.jsx(h,{...e,isActive:r===e.id,onClick:()=>l(e.id),updateTabPlace:o},e.id):null),n.jsx("div",{className:"ml-auto",children:n.jsx(g,{navItems:i,activeTabId:r})})]})}function h({title:e,id:s,icon:t,href:i,isActive:a,onClick:r,updateTabPlace:d}){let o=(0,c.useRef)(null);return(0,c.useCallback)(()=>{let e=o.current?.getBoundingClientRect(),t=window?.innerWidth;e&&t<e.right+150?d(s,"menu"):d(s,"nav")},[s,d]),(0,n.jsxs)(l.default,{href:i,"data-state":a?"active":"",onClick:r,prefetch:!1,ref:o,className:"inline-flex h-10 w-56 items-center justify-center gap-1 whitespace-nowrap rounded-md px-4 py-1.5 text-sm font-medium text-secondary-text ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary",children:[t,n.jsx("span",{children:e})]})}function g({navItems:e,activeTabId:s}){let[t,d]=o().useState(!1),c=e.filter(e=>"menu"===e.placeTo),{t:u}=(0,m.$G)();return 0===c.length?null:(0,n.jsxs)(a.h_,{open:t,onOpenChange:d,children:[n.jsx(a.$F,{asChild:!0,children:(0,n.jsxs)(i.z,{variant:"outline",className:"h-10 text-sm font-medium",children:[u("More"),n.jsx(r.Z,{size:16})]})}),n.jsx(a.AW,{children:c.map(e=>n.jsx(a.Xi,{"data-active":s===e.id,className:"data-[active=true]:bg-accent",children:(0,n.jsxs)(l.default,{href:e.href,prefetch:!1,onClick:()=>d(!1),className:"flex h-full w-full items-center gap-2",children:[e.icon,n.jsx("span",{children:e.title})]})},e.id))})]})}},11840:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var n=t(19510),i=t(40099);let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function r({children:e}){return(0,n.jsxs)("div",{className:"flex h-screen",children:[n.jsx(a,{}),(0,n.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[n.jsx(i.Z,{}),n.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},33661:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var n=t(19510),i=t(48413);function a(){return n.jsx("div",{className:"flex items-center justify-center py-10",children:n.jsx(i.a,{})})}},25590:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var n=t(19510);t(71159);let i=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\_components\Tabbar.tsx#Tabbar`);function a({children:e}){return(0,n.jsxs)("div",{className:"overflow-y-auto",children:[n.jsx(i,{}),n.jsx("div",{className:"p-4",children:e})]})}},64689:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var n=t(19510),i=t(48413);function a(){return n.jsx("div",{className:"flex justify-center py-10",children:n.jsx(i.a,{})})}}};