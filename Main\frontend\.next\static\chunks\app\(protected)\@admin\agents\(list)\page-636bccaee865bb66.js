(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[30721],{64428:function(e,a,n){Promise.resolve().then(n.bind(n,5662))},5662:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return p}});var t=n(57437),s=n(85539),r=n(27186),l=n(85017),c=n(6512),i=n(75730),u=n(94508),d=n(99376),o=n(2265),h=n(43949),m=n(40127);function p(){var e;let a=(0,d.useSearchParams)(),[n,p]=o.useState(null!==(e=a.get("search"))&&void 0!==e?e:""),f=(0,d.useRouter)(),x=(0,d.usePathname)(),{t:g}=(0,h.$G)(),{data:v,meta:j,isLoading:N,refresh:k}=(0,i.Z)("/admin/agents?".concat(a.toString(),"&listType=pending"),{keepPreviousData:!0});return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(s.R,{value:n,onChange:e=>{e.preventDefault();let a=(0,u.w4)(e.target.value);p(e.target.value),f.replace("".concat(x,"?").concat(a.toString()))},iconPlacement:"end",placeholder:g("Search...")}),(0,t.jsx)(l.k,{canFilterUser:!0,canFilterByGender:!0,canFilterByCountryCode:!0}),(0,t.jsx)(r._,{url:"/admin/agents/export/all?listType=pending"})]}),(0,t.jsx)("div",{})]}),(0,t.jsx)(c.Z,{className:"my-4"}),(0,t.jsx)(m.Z,{data:v,meta:j,isLoading:N,refresh:k})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,31384,27443,83568,227,56993,85017,40127,92971,95030,1744],function(){return e(e.s=64428)}),_N_E=e.O()}]);