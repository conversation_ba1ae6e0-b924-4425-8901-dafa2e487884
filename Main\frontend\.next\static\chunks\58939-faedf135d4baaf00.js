"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[58939],{22291:function(e,t,r){r.d(t,{Z:function(){return h}});var n=r(74677),l=r(2265),o=r(40718),a=r.n(o),i=["variant","color","size"],u=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M15.2 10.492l-1.97-1.97-3.21-3.21c-.68-.67-1.84-.19-1.84.77v11.84c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.82.83-2.18 0-3.01z"}))},c=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M12.9 7.94l2.62 2.62c.77.77.77 2.03 0 2.8L9 19.87M9 4.04l1.04 1.04"}))},s=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M13.23 8.52l-5.05 3.79v5.61c0 .96 1.16 1.44 1.84.76l5.18-5.18c.83-.83.83-2.18 0-3.01l-1.97-1.97z",opacity:".4"}),l.createElement("path",{fill:t,d:"M8.18 6.08v6.23l5.05-3.79-3.21-3.21c-.68-.67-1.84-.19-1.84.77z"}))},d=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},p=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{fill:t,d:"M8.91 20.67c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06l6.52-6.52c.48-.48.48-1.26 0-1.74L8.38 4.61a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.51.51.8 1.2.8 1.93s-.28 1.42-.8 1.93l-6.52 6.52c-.15.14-.34.22-.53.22z"}))},f=function(e){var t=e.color;return l.createElement(l.Fragment,null,l.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M8.91 19.92l6.52-6.52c.77-.77.77-2.03 0-2.8L8.91 4.08"}))},v=function(e,t){switch(e){case"Bold":return l.createElement(u,{color:t});case"Broken":return l.createElement(c,{color:t});case"Bulk":return l.createElement(s,{color:t});case"Linear":default:return l.createElement(d,{color:t});case"Outline":return l.createElement(p,{color:t});case"TwoTone":return l.createElement(f,{color:t})}},h=(0,l.forwardRef)(function(e,t){var r=e.variant,o=e.color,a=e.size,u=(0,n._)(e,i);return l.createElement("svg",(0,n.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),v(r,o))});h.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},h.defaultProps={variant:"Linear",color:"currentColor",size:"24"},h.displayName="ArrowRight2"},40875:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},22135:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},62484:function(e,t,r){r.d(t,{u:function(){return n}});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},68856:function(e,t,r){r.d(t,{VY:function(){return eV},ZA:function(){return e_},JO:function(){return eD},ck:function(){return eH},wU:function(){return eF},eT:function(){return eA},__:function(){return eB},h_:function(){return eN},fC:function(){return eL},$G:function(){return ez},u_:function(){return eO},Z0:function(){return eK},xz:function(){return eI},B4:function(){return eP},l_:function(){return eW}});var n=r(2265),l=r(54887),o=r(62484),a=r(6741),i=r(58068),u=r(98575),c=r(73966),s=r(29114),d=r(15278),p=r(86097),f=r(99103),v=r(99255),h=r(26008),m=r(83832),w=r(66840),g=r(37053),x=r(26606),y=r(80886),b=r(61188),S=r(6718),C=r(57437),k=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.WV.span,{...e,ref:t,style:{...k,...e.style}})).displayName="VisuallyHidden";var M=r(5478),E=r(87922),j=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],R="Select",[L,I,P]=(0,i.B)(R),[D,N]=(0,c.b)(R,[P,h.D7]),V=(0,h.D7)(),[W,_]=D(R),[B,H]=D(R),A=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:u,onValueChange:c,dir:d,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=V(t),[b,S]=n.useState(null),[k,M]=n.useState(null),[E,j]=n.useState(!1),T=(0,s.gm)(d),[I,P]=(0,y.T)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:R}),[D,N]=(0,y.T)({prop:i,defaultProp:u,onChange:c,caller:R}),_=n.useRef(null),H=!b||g||!!b.closest("form"),[A,F]=n.useState(new Set),O=Array.from(A).map(e=>e.props.value).join(";");return(0,C.jsx)(h.fC,{...x,children:(0,C.jsxs)(W,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:k,onValueNodeChange:M,valueNodeHasChildren:E,onValueNodeHasChildrenChange:j,contentId:(0,v.M)(),value:D,onValueChange:N,open:I,onOpenChange:P,dir:T,triggerPointerDownPosRef:_,disabled:m,children:[(0,C.jsx)(L.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),H?(0,C.jsxs)(eE,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>N(e.target.value),disabled:m,form:g,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(A)]},O):null]})})};A.displayName=R;var F="SelectTrigger",O=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=V(r),c=_(F,r),s=c.disabled||l,d=(0,u.e)(t,c.onTriggerChange),p=I(r),f=n.useRef("touch"),[v,m,g]=eT(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===c.value),n=eR(t,e,r);void 0!==n&&c.onValueChange(n.value)}),x=e=>{s||(c.onOpenChange(!0),g()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(h.ee,{asChild:!0,...i,children:(0,C.jsx)(w.WV.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":ej(c.value)?"":void 0,...o,ref:d,onClick:(0,a.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.M)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.M)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&j.includes(e.key)&&(x(),e.preventDefault())})})})});O.displayName=F;var z="SelectValue",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,c=_(z,r),{onValueNodeHasChildrenChange:s}=c,d=void 0!==o,p=(0,u.e)(t,c.onValueNodeChange);return(0,b.b)(()=>{s(d)},[s,d]),(0,C.jsx)(w.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:ej(c.value)?(0,C.jsx)(C.Fragment,{children:a}):o})});K.displayName=z;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(w.WV.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var Z=e=>(0,C.jsx)(m.h,{asChild:!0,...e});Z.displayName="SelectPortal";var q="SelectContent",Y=n.forwardRef((e,t)=>{let r=_(q,e.__scopeSelect),[o,a]=n.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,C.jsx)($,{...e,ref:t}):o?l.createPortal((0,C.jsx)(X,{scope:e.__scopeSelect,children:(0,C.jsx)(L.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),o):null});Y.displayName=q;var[X,G]=D(q),J=(0,g.Z8)("SelectContent.RemoveScroll"),$=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:c,side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S,...k}=e,j=_(q,r),[T,R]=n.useState(null),[L,P]=n.useState(null),D=(0,u.e)(t,e=>R(e)),[N,V]=n.useState(null),[W,B]=n.useState(null),H=I(r),[A,F]=n.useState(!1),O=n.useRef(!1);n.useEffect(()=>{if(T)return(0,M.Ry)(T)},[T]),(0,p.EW)();let z=n.useCallback(e=>{let[t,...r]=H().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&L&&(L.scrollTop=0),r===n&&L&&(L.scrollTop=L.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[H,L]),K=n.useCallback(()=>z([N,T]),[z,N,T]);n.useEffect(()=>{A&&K()},[A,K]);let{onOpenChange:U,triggerPointerDownPosRef:Z}=j;n.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=Z.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=Z.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():T.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[T,U,Z]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Y,G]=eT(e=>{let t=H().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eR(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),$=n.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==j.value&&j.value===t||n)&&(V(e),n&&(O.current=!0))},[j.value]),et=n.useCallback(()=>null==T?void 0:T.focus(),[T]),er=n.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==j.value&&j.value===t||n)&&B(e)},[j.value]),en="popper"===l?ee:Q,el=en===ee?{side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(X,{scope:r,content:T,viewport:L,onViewportChange:P,itemRefCallback:$,selectedItem:N,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:K,selectedItemText:W,position:l,isPositioned:A,searchRef:Y,children:(0,C.jsx)(E.Z,{as:J,allowPinchZoom:!0,children:(0,C.jsx)(f.M,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(o,e=>{var t;null===(t=j.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...k,...el,onPlaced:()=>F(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,a.M)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(q,r),c=G(q,r),[s,d]=n.useState(null),[p,f]=n.useState(null),v=(0,u.e)(t,e=>f(e)),h=I(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:S,focusSelectedItem:k}=c,M=n.useCallback(()=>{if(i.trigger&&i.valueNode&&s&&p&&x&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,u=e.width+i,c=Math.max(u,t.width),d=window.innerWidth-10,p=(0,o.u)(a,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.left=p+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,u=e.width+i,c=Math.max(u,t.width),d=window.innerWidth-10,p=(0,o.u)(a,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.right=p+"px"}let a=h(),u=window.innerHeight-20,c=x.scrollHeight,d=window.getComputedStyle(p),f=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),w=parseInt(d.borderBottomWidth,10),g=f+v+c+parseInt(d.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(x),k=parseInt(C.paddingTop,10),M=parseInt(C.paddingBottom,10),E=e.top+e.height/2-10,j=y.offsetHeight/2,T=f+v+(y.offsetTop+j);if(T<=E){let e=a.length>0&&y===a[a.length-1].ref.current;s.style.bottom="0px";let t=p.clientHeight-x.offsetTop-x.offsetHeight;s.style.height=T+Math.max(u-E,j+(e?M:0)+t+w)+"px"}else{let e=a.length>0&&y===a[0].ref.current;s.style.top="0px";let t=Math.max(E,f+x.offsetTop+(e?k:0)+j);s.style.height=t+(g-T)+"px",x.scrollTop=T-E+x.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=b+"px",s.style.maxHeight=u+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,s,p,x,y,S,i.dir,l]);(0,b.b)(()=>M(),[M]);let[E,j]=n.useState();(0,b.b)(()=>{p&&j(window.getComputedStyle(p).zIndex)},[p]);let T=n.useCallback(e=>{e&&!0===g.current&&(M(),null==k||k(),g.current=!1)},[M,k]);return(0,C.jsx)(et,{scope:r,contentWrapper:s,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,C.jsx)(w.WV.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=V(r);return(0,C.jsx)(h.VY,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=G(en,r),c=er(en,r),s=(0,u.e)(t,i.onViewportChange),d=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(L.Slot,{scope:r,children:(0,C.jsx)(w.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=c;if((null==n?void 0:n.current)&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=D(eo),eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.M)();return(0,C.jsx)(ea,{scope:r,id:l,children:(0,C.jsx)(w.WV.div,{role:"group","aria-labelledby":l,...n,ref:t})})});eu.displayName=eo;var ec="SelectLabel",es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(ec,r);return(0,C.jsx)(w.WV.div,{id:l.id,...n,ref:t})});es.displayName=ec;var ed="SelectItem",[ep,ef]=D(ed),ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...c}=e,s=_(ed,r),d=G(ed,r),p=s.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),x=(0,u.e)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,l,o)}),y=(0,v.M)(),b=n.useRef("touch"),S=()=>{o||(s.onValueChange(l),s.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ep,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,C.jsx)(L.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,C.jsx)(w.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...c,ref:x,onFocus:(0,a.M)(c.onFocus,()=>g(!0)),onBlur:(0,a.M)(c.onBlur,()=>g(!1)),onClick:(0,a.M)(c.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.M)(c.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.M)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(c.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,a.M)(c.onKeyDown,e=>{var t;(null===(t=d.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(T.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ed;var eh="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,c=_(eh,r),s=G(eh,r),d=ef(eh,r),p=H(eh,r),[f,v]=n.useState(null),h=(0,u.e)(t,e=>v(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.b)(()=>(x(g),()=>y(g)),[x,y,g]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.WV.span,{id:d.textId,...i,ref:h}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?l.createPortal(i.children,c.valueNode):null]})});em.displayName=eh;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,C.jsx)(w.WV.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ex="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=G(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=G(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,u.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=G("SelectScrollButton",r),u=n.useRef(null),c=I(r),s=n.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return n.useEffect(()=>()=>s(),[s]),(0,b.b)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,C.jsx)(w.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.M)(o.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(l,50))}),onPointerMove:(0,a.M)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===u.current&&(u.current=window.setInterval(l,50))}),onPointerLeave:(0,a.M)(o.onPointerLeave,()=>{s()})})}),ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.WV.div,{"aria-hidden":!0,...n,ref:t})});ek.displayName="SelectSeparator";var eM="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=V(r),o=_(eM,r),a=G(eM,r);return o.open&&"popper"===a.position?(0,C.jsx)(h.Eh,{...l,...n,ref:t}):null}).displayName=eM;var eE=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,u.e)(t,a),c=(0,S.D)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[c,l]),(0,C.jsx)(w.WV.select,{...o,style:{...k,...o.style},ref:i,defaultValue:l})});function ej(e){return""===e||void 0===e}function eT(e){let t=(0,x.W)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eR(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==r?a:void 0}eE.displayName="SelectBubbleInput";var eL=A,eI=O,eP=K,eD=U,eN=Z,eV=Y,eW=el,e_=eu,eB=es,eH=ev,eA=em,eF=eg,eO=ey,ez=eS,eK=ek},6718:function(e,t,r){r.d(t,{D:function(){return l}});var n=r(2265);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}}]);