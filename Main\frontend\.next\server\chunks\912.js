exports.id=912,exports.ids=[912],exports.modules={70362:(e,s,t)=>{Promise.resolve().then(t.bind(t,54950))},54555:(e,s,t)=>{Promise.resolve().then(t.bind(t,54950))},48335:(e,s,t)=>{Promise.resolve().then(t.bind(t,85999))},54950:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>U});var r=t(10326),a=t(92392),n=t(5158),l=t(567),i=t(90772),d=t(30458),c=t(8281),o=t(49547),u=t(50833);async function x(e){try{let s=await o.Z.post("/wallets/create",e);return{statusCode:s.status,statusText:s.statusText,status:200===s.status,message:s.data?.message??""}}catch(r){let e=500,s="Internal Server Error",t="An unknown error occurred";return(0,u.IZ)(r)?(e=r.response?.status??500,s=r.response?.statusText??"Internal Server Error",t=r.response?.data?.message??r.message):r instanceof Error&&(t=r.message),{statusCode:e,statusText:s,status:!1,message:t}}}var m=t(90799),h=t(59598),f=t(29169),p=t(75817),j=t(52350),g=t(12051),y=t(17577),v=t.n(y),N=t(70012),w=t(85999),b=t(7291);function Z({userWallets:e}){let{t:s}=(0,N.$G)(),{data:t,isLoading:o}=(0,m.d)("/currencies"),{mutate:u}=(0,b.kY)(),y=e=>{w.toast.promise(x({currencyCode:e}),{loading:s("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return u("/currencies"),u("/wallets"),e.message},error:e=>e.message})};if(o)return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.Loader,{})});let Z=t?.data?.map(e=>new h.F(e)),A=Z?.filter(s=>e.findIndex(e=>e.currency.id===s.id)===-1);return(0,r.jsxs)(d.dy,{direction:"right",children:[r.jsx(d.Qz,{asChild:!0,children:(0,r.jsxs)(i.z,{variant:"outline",className:"gap-1 font-normal",children:[r.jsx(f.Z,{}),r.jsx("span",{children:s("Add Wallet")})]})}),(0,r.jsxs)(d.sc,{className:"inset-x-auto inset-y-0 bottom-auto left-auto right-0 top-0 m-0 flex h-full w-[90%] flex-col overflow-x-hidden overflow-y-scroll rounded-t-none bg-background px-0 py-8 sm:w-[400px]",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 px-6",children:[r.jsx(d.uh,{asChild:!0,children:r.jsx(i.z,{variant:"outline",size:"icon",children:r.jsx(p.Z,{})})}),(0,r.jsxs)(d.OX,{className:"flex-1 p-0",children:[r.jsx(d.iI,{className:"text-left",children:s("Create a New Wallet")}),r.jsx(d.u6,{className:"sr-only absolute text-xs text-secondary-text",children:s("Quickly and securely set up your new digital wallet by following the steps.")})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-1 px-3 py-6",children:[r.jsx("h6",{className:"mb-6 px-3 font-medium text-secondary-text",children:s("Currency Already in Use")}),e?.map(e=>r.jsxs(v().Fragment,{children:[r.jsxs("div",{className:"inline-flex items-center gap-2 rounded-lg px-3 py-2 hover:bg-accent",children:[r.jsx(j.Z,{size:32}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h6",{className:"text-foreground",children:e.currency.name}),r.jsxs("span",{className:"text-xs text-secondary-text",children:[s("Currency"),": ",e?.currency.code]})]}),r.jsx(l.C,{variant:"success",children:s("Added")})]}),r.jsx(c.Z,{className:"border-divider"})]},e.currency.id))]}),r.jsx(c.Z,{}),(0,r.jsxs)("div",{className:"flex flex-col gap-1 px-3 py-6",children:[r.jsx("h6",{className:"mb-6 px-3 font-medium text-secondary-text",children:s("Available Currency")}),r.jsx(n.J,{condition:A?.length===0,children:(0,r.jsxs)("div",{className:"ml-3 flex items-center gap-2 rounded-lg bg-accent py-4 pl-4 text-secondary-text",children:[r.jsx(g.Z,{}),s("No data...")]})}),r.jsx("div",{children:r.jsx(n.J,{condition:A?.length>0,children:A?.map(e=>r.jsxs(v().Fragment,{children:[r.jsxs("div",{className:"flex w-full items-center gap-2 rounded-lg px-3 py-2 hover:bg-accent",children:[r.jsx(j.Z,{size:32}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h6",{className:"text-foreground",children:e.name}),r.jsxs("span",{className:"text-xs text-secondary-text",children:[s("Currency"),": ",e.code]})]}),r.jsxs(i.z,{size:"sm",variant:"outline",className:"gap-1 hover:bg-background",onClick:()=>y(e.code),children:[r.jsx(f.Z,{}),r.jsx("span",{children:s("Add")})]})]}),r.jsx(c.Z,{className:"border-divider"})]},e.id))})})]})]})]})}var A=t(33071),C=t(2454),I=t(30811),S=t(54825),D=t(10734);async function z(e){try{let s=await o.Z.put(`wallets/pin-dashboard/${e.walletId}`,e);return(0,D.B)(s)}catch(e){return(0,D.D)(e)}}async function E(e){try{let s=await o.Z.put(`/wallets/make-default/${e.walletId}`,e);return{statusCode:s.status,statusText:s.statusText,status:200===s.status,message:s.data?.message??""}}catch(r){let e=500,s="Internal Server Error",t="An unknown error occurred";return(0,u.IZ)(r)?(e=r.response?.status??500,s=r.response?.statusText??"Internal Server Error",t=r.response?.data?.message??r.message):r instanceof Error&&(t=r.message),{statusCode:e,statusText:s,status:!1,message:t}}}var P=t(60102),k=t(31112),F=t(27922),R=t(23182),T=t(92487),L=t(52078),O=t(81466);function W({id:e,title:s,balance:t,currency:a,card:d,isDefaultWallet:c=!1,isPinnedDashboard:o=!1}){let{t:u}=(0,N.$G)(),[x,m]=(0,y.useState)(!1),{mutate:h}=(0,b.kY)(),{settings:f}=(0,P.h)(),p=()=>{w.toast.promise(E({walletId:e}),{loading:u("Processing..."),success:e=>{if(!e.status)throw Error(e.message);return h("/wallets"),e.message},error:e=>e.message})},j=s=>{w.toast.promise(z({pinDashboard:s,walletId:e?.toString()}),{loading:u("Processing..."),success:e=>{if(!e.status)throw Error(e.message);return h("/wallets"),e.message},error:e=>e.message})};return a&&(0,r.jsxs)(A.Zb,{className:"w-[350px] overflow-hidden rounded-2xl border-primary bg-gradient-to-b from-[#48E1D8] to-primary",children:[(0,r.jsxs)(A.aY,{className:"relative overflow-hidden px-6 py-4",children:[(0,r.jsxs)("div",{className:"absolute right-2.5 top-2.5 flex justify-end gap-1",children:[r.jsx(n.J,{condition:o,children:(0,r.jsxs)(l.C,{children:[r.jsx(O.Z,{size:14,className:"mr-1 rotate-45"}),u("Dashboard")]})}),r.jsx(n.J,{condition:c,children:r.jsx(l.C,{children:u("Default")})})]}),r.jsx("h2",{className:"text-shadow pointer-events-none absolute bottom-3 right-0 text-[104px] font-bold text-primary opacity-30",children:a}),r.jsx("div",{className:"relative z-20 mb-4 flex items-center gap-2.5",children:r.jsx("h6",{className:"font-bold text-primary-foreground",children:s})}),(0,r.jsxs)("div",{className:"relative z-20 text-primary-foreground",children:[r.jsx("span",{className:"text-xs font-normal",children:u("Balance")}),(0,r.jsxs)("h1",{className:"flex items-center gap-1 align-baseline text-[32px] font-semibold leading-10",children:[Number(t).toFixed(2),r.jsx("span",{className:"text-sm font-normal leading-5",children:a})]})]})]}),(0,r.jsxs)(A.eW,{className:"justify-end bg-primary px-6 py-2 text-primary-foreground",children:[d?.lastFour&&f?.virtual_card?.status==="on"?(0,r.jsxs)("div",{className:"flex-1 text-sm font-normal leading-5",children:["**** **** **** ",d?.lastFour]}):null,(0,r.jsxs)(I.J2,{open:x,onOpenChange:m,children:[(0,r.jsxs)(I.xo,{className:(0,i.d)({variant:"default",size:"sm",className:"text-sm font-semibold leading-5"}),children:[r.jsx(k.Z,{size:"20"}),r.jsx("span",{children:u("Menu")})]}),r.jsx(I.yk,{className:"w-64 p-0",align:"start",children:r.jsx(C.mY,{children:r.jsx(C.e8,{children:(0,r.jsxs)(C.fu,{children:[(0,r.jsxs)(C.di,{className:"mb-1",onSelect:()=>{j(!o),m(!1)},children:[o?r.jsx(F.Z,{size:"20"}):r.jsx(R.Z,{size:20}),(0,r.jsxs)("span",{className:"ml-1",children:[o?u("Unpin"):u("Pin")," ",u("from dashboard")]})]}),(0,r.jsxs)(C.di,{onSelect:()=>{p(),m(!1)},children:[r.jsx(T.Z,{size:"20"}),r.jsx("span",{className:"ml-1",children:u("Set as default")})]}),d?.id||f?.virtual_card?.status!=="on"?null:(0,r.jsxs)(C.di,{disabled:a?.toUpperCase()!=="USD"&&a?.toUpperCase()!=="NGN",onSelect:()=>{w.toast.promise((0,S.m)(e),{loading:u("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return h("/wallets"),e.message},error:e=>e.message})},children:[r.jsx(L.Z,{size:"20"}),r.jsx("span",{className:"ml-1",children:u("Issue card")})]})]})})})})]})]})]})}var Y=t(9652);function U(){let{data:e,isLoading:s}=(0,m.d)("/wallets");if(s)return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.Loader,{})});let t=e?.data?.map(e=>new Y.w(e));return(0,r.jsxs)("div",{className:"w-full bg-background p-4",children:[r.jsx("div",{className:"flex flex-wrap gap-4",children:t.map(e=>r.jsx(W,{id:e.id,title:e?.currency.code,balance:e.balance,currency:e?.currency.code,card:e.cards?.[0],isDefaultWallet:e.defaultStatus,isPinnedDashboard:e.pinDashboard},e.id))}),r.jsx("div",{className:"py-4",children:r.jsx(Z,{userWallets:t})})]})}},33071:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>i,SZ:()=>c,Zb:()=>l,aY:()=>o,eW:()=>u,ll:()=>d});var r=t(10326),a=t(17577),n=t(77863);let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));l.displayName="Card";let i=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.ZP)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,n.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,n.ZP)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.ZP)("p-6 pt-0",e),...s}));o.displayName="CardContent";let u=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.ZP)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},54825:(e,s,t)=>{"use strict";t.d(s,{m:()=>n});var r=t(49547),a=t(10734);async function n(e){try{let s=await r.Z.post("/cards/generate-virtual",{walletId:e});return(0,a.B)(s)}catch(e){return(0,a.D)(e)}}},48444:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});class r{constructor(e){this.id=e?.id,this.cardId=e?.cardId,this.userId=e?.userId,this.walletId=e?.walletId,this.number=e?.number,this.cvc=e?.cvc,this.lastFour=e?.lastFour,this.brand=e?.brand,this.expMonth=e?.expMonth,this.expYear=e?.expYear,this.status=e?.status,this.type=e?.type,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.wallet=e?.wallet,this.user=e?.user}}},59598:(e,s,t)=>{"use strict";t.d(s,{F:()=>r});class r{constructor(e){this.formatter=e=>{let s=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),t=s.formatToParts(e),r=t.find(e=>"currency"===e.type)?.value??this.code,a=s.format(e),n=a.substring(r.length).trim();return{currencyCode:this.code,currencySymbol:r,formattedAmount:a,amountText:n}},this.id=e?.id,this.name=e?.name,this.code=e?.code,this.logo=e?.logo??"",this.usdRate=e?.usdRate,this.acceptApiRate=!!e?.acceptApiRate,this.isCrypto=!!e?.isCrypto,this.active=!!e?.active,this.metaData=e?.metaData,this.minAmount=e?.minAmount,this.kycLimit=e?.kycLimit,this.maxAmount=e?.maxAmount,this.dailyTransferAmount=e?.dailyTransferAmount,this.dailyTransferLimit=e?.dailyTransferLimit,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}format(e){let{currencySymbol:s,amountText:t}=this.formatter(e);return`${t} ${s}`}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:s}=this.formatter(e);return s}}},9652:(e,s,t)=>{"use strict";t.d(s,{w:()=>n});var r=t(48444),a=t(59598);class n{constructor(e){this.id=e?.id,this.walletId=e?.walletId,this.logo=e?.logo,this.userId=e?.userId,this.balance=e?.balance,this.defaultStatus=!!e?.default,this.pinDashboard=!!e?.pinDashboard,this.currencyId=e?.currencyId,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.currency=new a.F(e?.currency),this.cards=e?.cards?.map(e=>new r.Z(e))}}},84514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(19510),a=t(40099),n=t(76609);function l({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(n.Z,{userRole:"agent"}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(a.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},18406:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(48413);function n(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.a,{})})}},4660:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(64644);function n(){return r.jsx(a.default,{})}},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(19510),a=t(40099),n=t(76609);function l({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(n.Z,{userRole:"customer"}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(a.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(48413);function n(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(a.a,{})})}},86606:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(19856);function n(){return r.jsx("div",{className:"h-full w-full bg-background p-4",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 p-4",children:[r.jsx(a.O,{className:"h-48 w-80 rounded-xl"}),r.jsx(a.O,{className:"h-48 w-80 rounded-xl"}),r.jsx(a.O,{className:"h-48 w-80 rounded-xl"}),r.jsx(a.O,{className:"h-48 w-80 rounded-xl"})]})})}},64644:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\wallets\page.tsx#default`)},43782:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(19510),a=t(64644);function n(){return r.jsx(a.default,{})}},19856:(e,s,t)=>{"use strict";t.d(s,{O:()=>l});var r=t(19510);process.env.SESSION_SECRET;var a=t(55761);t(51032);var n=t(62386);function l({className:e,...s}){return r.jsx("div",{className:function(...e){return(0,n.m6)((0,a.W)(e))}("animate-pulse rounded-md bg-muted",e),...s})}}};