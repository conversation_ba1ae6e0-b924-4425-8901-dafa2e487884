{"version": 3, "file": "edge-chunks/3390.js", "mappings": "4JAIAA,EAAA,2BAEAC,EAAA,SAAAC,CAAA,EACA,IAAAC,EAAAD,EAAAC,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,0dACAC,KAAAL,CACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAP,EAAAO,EAAAP,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,+QACAI,OAAAR,EACAS,YAAA,MACAC,iBAAA,KACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAC,EAAA,SAAAC,CAAA,EACA,IAAAd,EAAAc,EAAAd,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,kHACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCa,QAAA,KACAX,EAAA,kOACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,6IACAC,KAAAL,CACA,GACA,EAEAgB,EAAA,SAAAC,CAAA,EACA,IAAAjB,EAAAiB,EAAAjB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,oQACAI,OAAAR,EACAS,YAAA,MACAC,iBAAA,KACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAnB,EAAAmB,EAAAnB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,mqBACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,yFACAC,KAAAL,CACA,GACA,EAEAoB,EAAA,SAAAC,CAAA,EACA,IAAArB,EAAAqB,EAAArB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,kJACAI,OAAAR,EACAS,YAAA,MACAC,iBAAA,KACAC,cAAA,QACAC,eAAA,OACA,GAAmBX,EAAAC,aAAmB,SACtCa,QAAA,KACAX,EAAA,qHACAI,OAAAR,EACAS,YAAA,MACAC,iBAAA,KACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAU,EAAA,SAAAC,CAAA,CAAAvB,CAAA,EACA,OAAAuB,GACA,WACA,OAA0BtB,EAAAC,aAAmB,CAAAJ,EAAA,CAC7CE,MAAAA,CACA,EAEA,cACA,OAA0BC,EAAAC,aAAmB,CAAAI,EAAA,CAC7CN,MAAAA,CACA,EAEA,YACA,OAA0BC,EAAAC,aAAmB,CAAAW,EAAA,CAC7Cb,MAAAA,CACA,EAEA,cAeA,QAdA,OAA0BC,EAAAC,aAAmB,CAAAc,EAAA,CAC7ChB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAgB,EAAA,CAC7ClB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAkB,EAAA,CAC7CpB,MAAAA,CACA,EAMA,CACA,EAEAwB,EAAgC,GAAAvB,EAAAwB,UAAA,EAAU,SAAAC,CAAA,CAAAC,CAAA,EAC1C,IAAAJ,EAAAG,EAAAH,OAAA,CACAvB,EAAA0B,EAAA1B,KAAA,CACA4B,EAAAF,EAAAE,IAAA,CACAC,EAAa,GAAAC,EAAAC,CAAA,EAAwBL,EAAA7B,GAErC,OAAsBI,EAAAC,aAAmB,OAAQ,GAAA4B,EAAAE,CAAA,EAAQ,GAAGH,EAAA,CAC5DI,MAAA,6BACAN,IAAAA,EACAO,MAAAN,EACAO,OAAAP,EACAQ,QAAA,YACA/B,KAAA,MACA,GAAGiB,EAAAC,EAAAvB,GACH,EACAwB,CAAAA,EAAAa,SAAA,EACAd,QAAWe,IAAAC,KAAe,wDAC1BvC,MAASsC,IAAAE,MAAA,CACTZ,KAAQU,IAAAG,SAAmB,EAAEH,IAAAE,MAAA,CAAkBF,IAAAI,MAAA,CAAgB,CAC/D,EACAlB,EAAAmB,YAAA,EACApB,QAAA,SACAvB,MAAA,eACA4B,KAAA,IACA,EACAJ,EAAAoB,WAAA,gGChJA/C,EAAA,2BAEAC,EAAA,SAAAC,CAAA,EACA,IAAAC,EAAAD,EAAAC,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,sbACAC,KAAAL,CACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAP,EAAAO,EAAAP,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,kNACAI,OAAAR,EACAS,YAAA,MACAC,iBAAA,KACAC,cAAA,QACAC,eAAA,OACA,GAAmBX,EAAAC,aAAmB,SACtCE,EAAA,8MACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GACA,EAEAC,EAAA,SAAAC,CAAA,EACA,IAAAd,EAAAc,EAAAd,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,sOACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCa,QAAA,KACAX,EAAA,obACAC,KAAAL,CACA,GACA,EAEAgB,EAAA,SAAAC,CAAA,EACA,IAAAjB,EAAAiB,EAAAjB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,qNACAI,OAAAR,EACAS,YAAA,MACAC,iBAAA,KACAC,cAAA,QACAC,eAAA,OACA,GAAmBX,EAAAC,aAAmB,SACtCE,EAAA,mOACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAnB,EAAAmB,EAAAnB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,geACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,6hBACAC,KAAAL,CACA,GACA,EAEAoB,EAAA,SAAAC,CAAA,EACA,IAAArB,EAAAqB,EAAArB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,qNACAI,OAAAR,EACAS,YAAA,MACAC,iBAAA,KACAC,cAAA,QACAC,eAAA,OACA,GAAmBX,EAAAC,aAAmB,SACtCa,QAAA,KACAX,EAAA,oOACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GACA,EAEAU,EAAA,SAAAC,CAAA,CAAAvB,CAAA,EACA,OAAAuB,GACA,WACA,OAA0BtB,EAAAC,aAAmB,CAAAJ,EAAA,CAC7CE,MAAAA,CACA,EAEA,cACA,OAA0BC,EAAAC,aAAmB,CAAAI,EAAA,CAC7CN,MAAAA,CACA,EAEA,YACA,OAA0BC,EAAAC,aAAmB,CAAAW,EAAA,CAC7Cb,MAAAA,CACA,EAEA,cAeA,QAdA,OAA0BC,EAAAC,aAAmB,CAAAc,EAAA,CAC7ChB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAgB,EAAA,CAC7ClB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAkB,EAAA,CAC7CpB,MAAAA,CACA,EAMA,CACA,EAEA6C,EAA2B,GAAA5C,EAAAwB,UAAA,EAAU,SAAAC,CAAA,CAAAC,CAAA,EACrC,IAAAJ,EAAAG,EAAAH,OAAA,CACAvB,EAAA0B,EAAA1B,KAAA,CACA4B,EAAAF,EAAAE,IAAA,CACAC,EAAa,GAAAC,EAAAC,CAAA,EAAwBL,EAAA7B,GAErC,OAAsBI,EAAAC,aAAmB,OAAQ,GAAA4B,EAAAE,CAAA,EAAQ,GAAGH,EAAA,CAC5DI,MAAA,6BACAN,IAAAA,EACAO,MAAAN,EACAO,OAAAP,EACAQ,QAAA,YACA/B,KAAA,MACA,GAAGiB,EAAAC,EAAAvB,GACH,EACA6C,CAAAA,EAAAR,SAAA,EACAd,QAAWe,IAAAC,KAAe,wDAC1BvC,MAASsC,IAAAE,MAAA,CACTZ,KAAQU,IAAAG,SAAmB,EAAEH,IAAAE,MAAA,CAAkBF,IAAAI,MAAA,CAAgB,CAC/D,EACAG,EAAAF,YAAA,EACApB,QAAA,SACAvB,MAAA,eACA4B,KAAA,IACA,EACAiB,EAAAD,WAAA,2FCxJA/C,EAAA,2BAEAC,EAAA,SAAAC,CAAA,EACA,IAAAC,EAAAD,EAAAC,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,wbACAC,KAAAL,CACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAP,EAAAO,EAAAP,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,qNACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GAAmBX,EAAAC,aAAmB,SACtCE,EAAA,4GACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GACA,EAEAC,EAAA,SAAAC,CAAA,EACA,IAAAd,EAAAc,EAAAd,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGa,QAAA,KACAX,EAAA,uNACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,gLACAC,KAAAL,CACA,GACA,EAEAgB,EAAA,SAAAC,CAAA,EACA,IAAAjB,EAAAiB,EAAAjB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,uNACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GAAmBX,EAAAC,aAAmB,SACtCE,EAAA,sGACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAnB,EAAAmB,EAAAnB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,kdACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,6ZACAC,KAAAL,CACA,GACA,EAEAoB,EAAA,SAAAC,CAAA,EACA,IAAArB,EAAAqB,EAAArB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,uNACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GAAmBX,EAAAC,aAAmB,SACtCa,QAAA,KACAX,EAAA,4GACAI,OAAAR,EACAS,YAAA,MACAE,cAAA,QACAC,eAAA,OACA,GACA,EAEAU,EAAA,SAAAC,CAAA,CAAAvB,CAAA,EACA,OAAAuB,GACA,WACA,OAA0BtB,EAAAC,aAAmB,CAAAJ,EAAA,CAC7CE,MAAAA,CACA,EAEA,cACA,OAA0BC,EAAAC,aAAmB,CAAAI,EAAA,CAC7CN,MAAAA,CACA,EAEA,YACA,OAA0BC,EAAAC,aAAmB,CAAAW,EAAA,CAC7Cb,MAAAA,CACA,EAEA,cAeA,QAdA,OAA0BC,EAAAC,aAAmB,CAAAc,EAAA,CAC7ChB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAgB,EAAA,CAC7ClB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAkB,EAAA,CAC7CpB,MAAAA,CACA,EAMA,CACA,EAEA8C,EAA+B,GAAA7C,EAAAwB,UAAA,EAAU,SAAAC,CAAA,CAAAC,CAAA,EACzC,IAAAJ,EAAAG,EAAAH,OAAA,CACAvB,EAAA0B,EAAA1B,KAAA,CACA4B,EAAAF,EAAAE,IAAA,CACAC,EAAa,GAAAC,EAAAC,CAAA,EAAwBL,EAAA7B,GAErC,OAAsBI,EAAAC,aAAmB,OAAQ,GAAA4B,EAAAE,CAAA,EAAQ,GAAGH,EAAA,CAC5DI,MAAA,6BACAN,IAAAA,EACAO,MAAAN,EACAO,OAAAP,EACAQ,QAAA,YACA/B,KAAA,MACA,GAAGiB,EAAAC,EAAAvB,GACH,EACA8C,CAAAA,EAAAT,SAAA,EACAd,QAAWe,IAAAC,KAAe,wDAC1BvC,MAASsC,IAAAE,MAAA,CACTZ,KAAQU,IAAAG,SAAmB,EAAEH,IAAAE,MAAA,CAAkBF,IAAAI,MAAA,CAAgB,CAC/D,EACAI,EAAAH,YAAA,EACApB,QAAA,SACAvB,MAAA,eACA4B,KAAA,IACA,EACAkB,EAAAF,WAAA", "sources": ["webpack://_N_E/./node_modules/iconsax-react/dist/esm/ShoppingCart.js", "webpack://_N_E/./node_modules/iconsax-react/dist/esm/TagUser.js", "webpack://_N_E/./node_modules/iconsax-react/dist/esm/UserOctagon.js"], "sourcesContent": ["import { _ as _objectWithoutProperties, a as _extends } from './_rollupPluginBabelHelpers-3bc641ae.js';\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\n\nvar _excluded = [\"variant\", \"color\", \"size\"];\n\nvar Bold = function Bold(_ref) {\n  var color = _ref.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M16.25 22.5a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5ZM8.25 22.5a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5ZM4.84 3.94l-.2 2.45c-.04.47.33.86.8.86h15.31c.42 0 .77-.32.8-.74.13-1.77-1.22-3.21-2.99-3.21H6.27c-.1-.44-.3-.86-.61-1.21-.5-.53-1.2-.84-1.92-.84H2c-.41 0-.75.34-.75.75s.34.75.75.75h1.74c.31 0 .6.13.81.35.21.23.31.53.29.84ZM20.51 8.75H5.17c-.42 0-.76.32-.8.73l-.36 4.35A2.922 2.922 0 0 0 6.92 17h11.12c1.5 0 2.82-1.23 2.93-2.73l.33-4.67a.782.782 0 0 0-.79-.85Z\",\n    fill: color\n  }));\n};\n\nvar Broken = function Broken(_ref2) {\n  var color = _ref2.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M4.75 13.969a2.796 2.796 0 0 0 2.79 3.03h10.65c1.44 0 2.7-1.18 2.81-2.61l.54-7.5c.12-1.66-1.14-3.01-2.81-3.01H5.82M2 2h1.74c1.08 0 1.93.93 1.84 2l-.5 6.05M16.25 22a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5ZM8.25 22a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5ZM9 8h12\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeMiterlimit: \"10\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Bulk = function Bulk(_ref3) {\n  var color = _ref3.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M16.25 22.5a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5ZM8.25 22.5a1.75 1.75 0 1 0 0-3.5 1.75 1.75 0 0 0 0 3.5Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"m4.84 3.94-.2 2.45c-.04.47.33.86.8.86h15.31c.42 0 .77-.32.8-.74.13-1.77-1.22-3.21-2.99-3.21H6.29c-.1-.44-.3-.86-.61-1.21a2.62 2.62 0 0 0-1.91-.84H2c-.41 0-.75.34-.75.75s.34.75.75.75h1.74c.31 0 .6.13.81.35.21.23.31.53.29.84Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M20.51 8.75H5.17c-.42 0-.76.32-.8.73l-.36 4.35C3.87 15.53 5.21 17 6.92 17h11.12c1.5 0 2.82-1.23 2.93-2.73l.33-4.67a.782.782 0 0 0-.79-.85Z\",\n    fill: color\n  }));\n};\n\nvar Linear = function Linear(_ref4) {\n  var color = _ref4.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2 2h1.74c1.08 0 1.93.93 1.84 2l-.83 9.96a2.796 2.796 0 0 0 2.79 3.03h10.65c1.44 0 2.7-1.18 2.81-2.61l.54-7.5c.12-1.66-1.14-3.01-2.81-3.01H5.82M16.25 22a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5ZM8.25 22a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5ZM9 8h12\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeMiterlimit: \"10\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Outline = function Outline(_ref5) {\n  var color = _ref5.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18.19 17.75H7.54c-.99 0-1.94-.42-2.61-1.15A3.573 3.573 0 0 1 4 13.9l.83-9.96c.03-.31-.08-.61-.29-.84-.21-.23-.5-.35-.81-.35H2c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h1.74c.73 0 1.42.31 1.91.84.27.3.47.65.58 1.04h12.49c1.01 0 1.94.4 2.62 1.12.67.73 1.01 1.68.93 2.69l-.54 7.5c-.11 1.83-1.71 3.31-3.54 3.31ZM6.28 4.62l-.78 9.4c-.05.58.14 1.13.53 1.56.39.43.93.66 1.51.66h10.65c1.04 0 1.98-.88 2.06-1.92l.54-7.5a2.04 2.04 0 0 0-2.06-2.21H6.28v.01ZM16.25 22.75c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2Zm0-2.5c-.28 0-.5.22-.5.5s.22.5.5.5.5-.22.5-.5-.22-.5-.5-.5ZM8.25 22.75c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2Zm0-2.5c-.28 0-.5.22-.5.5s.22.5.5.5.5-.22.5-.5-.22-.5-.5-.5Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21 8.75H9c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h12c.41 0 .75.34.75.75s-.34.75-.75.75Z\",\n    fill: color\n  }));\n};\n\nvar TwoTone = function TwoTone(_ref6) {\n  var color = _ref6.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2 2h1.74c1.08 0 1.93.93 1.84 2l-.83 9.96a2.796 2.796 0 0 0 2.79 3.03h10.65c1.44 0 2.7-1.18 2.81-2.61l.54-7.5c.12-1.66-1.14-3.01-2.81-3.01H5.82\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeMiterlimit: \"10\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M16.25 22a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5ZM8.25 22a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5ZM9 8h12\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeMiterlimit: \"10\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar chooseVariant = function chooseVariant(variant, color) {\n  switch (variant) {\n    case 'Bold':\n      return /*#__PURE__*/React.createElement(Bold, {\n        color: color\n      });\n\n    case 'Broken':\n      return /*#__PURE__*/React.createElement(Broken, {\n        color: color\n      });\n\n    case 'Bulk':\n      return /*#__PURE__*/React.createElement(Bulk, {\n        color: color\n      });\n\n    case 'Linear':\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n\n    case 'Outline':\n      return /*#__PURE__*/React.createElement(Outline, {\n        color: color\n      });\n\n    case 'TwoTone':\n      return /*#__PURE__*/React.createElement(TwoTone, {\n        color: color\n      });\n\n    default:\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n  }\n};\n\nvar ShoppingCart = /*#__PURE__*/forwardRef(function (_ref7, ref) {\n  var variant = _ref7.variant,\n      color = _ref7.color,\n      size = _ref7.size,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: ref,\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\"\n  }), chooseVariant(variant, color));\n});\nShoppingCart.propTypes = {\n  variant: PropTypes.oneOf(['Linear', 'Bold', 'Broken', 'Bulk', 'Outline', 'TwoTone']),\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nShoppingCart.defaultProps = {\n  variant: 'Linear',\n  color: 'currentColor',\n  size: '24'\n};\nShoppingCart.displayName = 'ShoppingCart';\n\nexport { ShoppingCart as default };\n", "import { _ as _objectWithoutProperties, a as _extends } from './_rollupPluginBabelHelpers-3bc641ae.js';\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\n\nvar _excluded = [\"variant\", \"color\", \"size\"];\n\nvar Bold = function Bold(_ref) {\n  var color = _ref.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18 2H6C4.34 2 3 3.33 3 4.97v10.91c0 1.64 1.34 2.98 3 2.98h.76c.79 0 1.56.31 2.12.87l1.71 1.69c.78.77 2.04.77 2.82 0l1.71-1.69c.56-.56 1.33-.87 2.12-.87H18c1.66 0 3-1.34 3-2.98V4.97C21 3.33 19.66 2 18 2Zm-6 3.55c1.08 0 1.95.88 1.95 1.95 0 1.06-.84 1.91-1.88 1.95h-.15a1.945 1.945 0 0 1-1.88-1.95c.01-1.07.88-1.95 1.96-1.95Zm2.75 9.14c-1.51 1.01-3.99 1.01-5.5 0-1.33-.88-1.33-2.34 0-3.23 1.52-1.01 4-1.01 5.5 0 1.33.89 1.33 2.34 0 3.23Z\",\n    fill: color\n  }));\n};\n\nvar Broken = function Broken(_ref2) {\n  var color = _ref2.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21 8.95v6.93c0 1.64-1.34 2.97-3 2.97h-.76c-.8 0-1.56.31-2.12.87l-1.71 1.69c-.78.77-2.05.77-2.83 0l-1.71-1.69c-.56-.56-1.33-.87-2.12-.87H6c-1.66 0-3-1.33-3-2.97V4.97C3 3.33 4.34 2 6 2h12c1.66 0 3 1.33 3 2.97\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeMiterlimit: \"10\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.07 8.95h-.15A1.945 1.945 0 0 1 10.04 7c0-1.08.87-1.95 1.95-1.95s1.95.88 1.95 1.95c.01 1.06-.82 1.92-1.87 1.95ZM9.25 15.19c1.51 1.01 3.99 1.01 5.5 0 1.33-.89 1.33-2.34 0-3.23-1.51-1.01-3.99-1.01-5.5 0\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Bulk = function Bulk(_ref3) {\n  var color = _ref3.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.073 8.95h-.15A1.945 1.945 0 0 1 10.043 7c0-1.08.87-1.95 1.95-1.95s1.95.88 1.95 1.95c.01 1.06-.82 1.92-1.87 1.95ZM9.251 11.96c-1.33.89-1.33 2.34 0 3.23 1.51 1.01 3.99 1.01 5.5 0 1.33-.89 1.33-2.34 0-3.23-1.51-1-3.98-1-5.5 0Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M18 2H6C4.34 2 3 3.33 3 4.97v10.91c0 1.64 1.34 2.97 3 2.97h.76c.8 0 1.56.31 2.12.87l1.71 1.69c.78.77 2.05.77 2.83 0l1.71-1.69c.56-.56 1.33-.87 2.12-.87H18c1.66 0 3-1.33 3-2.97V4.97C21 3.33 19.66 2 18 2Zm-6 3.05c1.08 0 1.95.88 1.95 1.95 0 1.06-.84 1.91-1.88 1.95h-.15A1.945 1.945 0 0 1 10.04 7c.01-1.07.88-1.95 1.96-1.95Zm2.75 10.14c-1.51 1.01-3.99 1.01-5.5 0-1.33-.88-1.33-2.34 0-3.23 1.52-1.01 4-1.01 5.5 0 1.33.89 1.33 2.34 0 3.23Z\",\n    fill: color\n  }));\n};\n\nvar Linear = function Linear(_ref4) {\n  var color = _ref4.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18 18.86h-.76c-.8 0-1.56.31-2.12.87l-1.71 1.69c-.78.77-2.05.77-2.83 0l-1.71-1.69c-.56-.56-1.33-.87-2.12-.87H6c-1.66 0-3-1.33-3-2.97V4.97C3 3.33 4.34 2 6 2h12c1.66 0 3 1.33 3 2.97v10.91c0 1.64-1.34 2.98-3 2.98Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeMiterlimit: \"10\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.07 8.95h-.15A1.945 1.945 0 0 1 10.04 7c0-1.08.87-1.95 1.95-1.95s1.95.88 1.95 1.95c.01 1.06-.82 1.92-1.87 1.95ZM9.25 11.96c-1.33.89-1.33 2.34 0 3.23 1.51 1.01 3.99 1.01 5.5 0 1.33-.89 1.33-2.34 0-3.23-1.51-1-3.98-1-5.5 0Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Outline = function Outline(_ref5) {\n  var color = _ref5.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 22.75c-.7 0-1.41-.27-1.94-.8l-1.71-1.69c-.42-.42-1-.65-1.59-.65H6c-2.07 0-3.75-1.67-3.75-3.72V4.97c0-2.05 1.68-3.72 3.75-3.72h12c2.07 0 3.75 1.67 3.75 3.72v10.91c0 2.05-1.68 3.72-3.75 3.72h-.76a2.3 2.3 0 0 0-1.59.65l-1.71 1.69c-.53.54-1.24.81-1.94.81Zm-6-20c-1.24 0-2.25 1-2.25 2.22v10.91c0 1.23 1.01 2.22 2.25 2.22h.76c.99 0 1.95.4 2.65 1.09l1.71 1.69c.49.48 1.28.48 1.77 0l1.71-1.69c.7-.69 1.66-1.09 2.65-1.09H18c1.24 0 2.25-1 2.25-2.22V4.97c0-1.23-1.01-2.22-2.25-2.22H6Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.07 9.7h-.17A2.681 2.681 0 0 1 9.3 7c0-1.49 1.21-2.7 2.7-2.7a2.701 2.701 0 0 1 .09 5.4h-.02ZM12 5.8c-.66 0-1.2.54-1.2 1.2 0 .65.51 1.18 1.15 1.2 0-.01.06-.01.13 0 .63-.04 1.12-.56 1.12-1.2 0-.66-.54-1.2-1.2-1.2ZM12 16.698c-1.14 0-2.28-.3-3.17-.89-.84-.56-1.33-1.37-1.33-2.23 0-.86.48-1.68 1.33-2.24 1.78-1.18 4.56-1.18 6.33 0 .84.56 1.33 1.38 1.33 2.23 0 .86-.48 1.67-1.33 2.24-.88.6-2.02.89-3.16.89Zm-2.34-4.11c-.43.29-.66.64-.66.99s.24.7.66.99c1.27.85 3.4.85 4.67 0 .43-.29.67-.64.66-.99 0-.35-.24-.7-.66-.99-1.26-.85-3.4-.85-4.67 0Z\",\n    fill: color\n  }));\n};\n\nvar TwoTone = function TwoTone(_ref6) {\n  var color = _ref6.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M18 18.86h-.76c-.8 0-1.56.31-2.12.87l-1.71 1.69c-.78.77-2.05.77-2.83 0l-1.71-1.69c-.56-.56-1.33-.87-2.12-.87H6c-1.66 0-3-1.33-3-2.97V4.97C3 3.33 4.34 2 6 2h12c1.66 0 3 1.33 3 2.97v10.91c0 1.64-1.34 2.98-3 2.98Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeMiterlimit: \"10\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M12.07 8.95h-.15A1.945 1.945 0 0 1 10.04 7c0-1.08.87-1.95 1.95-1.95s1.95.88 1.95 1.95c.01 1.06-.82 1.92-1.87 1.95ZM9.251 11.96c-1.33.89-1.33 2.34 0 3.23 1.51 1.01 3.99 1.01 5.5 0 1.33-.89 1.33-2.34 0-3.23-1.51-1-3.98-1-5.5 0Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar chooseVariant = function chooseVariant(variant, color) {\n  switch (variant) {\n    case 'Bold':\n      return /*#__PURE__*/React.createElement(Bold, {\n        color: color\n      });\n\n    case 'Broken':\n      return /*#__PURE__*/React.createElement(Broken, {\n        color: color\n      });\n\n    case 'Bulk':\n      return /*#__PURE__*/React.createElement(Bulk, {\n        color: color\n      });\n\n    case 'Linear':\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n\n    case 'Outline':\n      return /*#__PURE__*/React.createElement(Outline, {\n        color: color\n      });\n\n    case 'TwoTone':\n      return /*#__PURE__*/React.createElement(TwoTone, {\n        color: color\n      });\n\n    default:\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n  }\n};\n\nvar TagUser = /*#__PURE__*/forwardRef(function (_ref7, ref) {\n  var variant = _ref7.variant,\n      color = _ref7.color,\n      size = _ref7.size,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: ref,\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\"\n  }), chooseVariant(variant, color));\n});\nTagUser.propTypes = {\n  variant: PropTypes.oneOf(['Linear', 'Bold', 'Broken', 'Bulk', 'Outline', 'TwoTone']),\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nTagUser.defaultProps = {\n  variant: 'Linear',\n  color: 'currentColor',\n  size: '24'\n};\nTagUser.displayName = 'TagUser';\n\nexport { TagUser as default };\n", "import { _ as _objectWithoutProperties, a as _extends } from './_rollupPluginBabelHelpers-3bc641ae.js';\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\n\nvar _excluded = [\"variant\", \"color\", \"size\"];\n\nvar Bold = function Bold(_ref) {\n  var color = _ref.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"m19.512 5.85-5.94-3.43c-.97-.56-2.17-.56-3.15 0l-5.93 3.43a3.15 3.15 0 0 0-1.57 2.73v6.84c0 1.12.6 2.16 1.57 2.73l5.94 3.43c.97.56 2.17.56 3.15 0l5.94-3.43a3.15 3.15 0 0 0 1.57-2.73V8.58a3.192 3.192 0 0 0-1.58-2.73Zm-7.51 1.49c1.29 0 2.33 1.04 2.33 2.33S13.292 12 12.002 12s-2.33-1.04-2.33-2.33c0-1.28 1.04-2.33 2.33-2.33Zm2.68 9.32h-5.36c-.81 0-1.28-.9-.83-1.57.68-1.01 2-1.69 3.51-1.69 1.51 0 2.83.68 3.51 1.69.45.66-.03 1.57-.83 1.57Z\",\n    fill: color\n  }));\n};\n\nvar Broken = function Broken(_ref2) {\n  var color = _ref2.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M10.432 2.42c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.56 1.57 1.6 1.57 2.73v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l1.9-1.1\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.002 11a2.33 2.33 0 1 0 0-4.66 2.33 2.33 0 0 0 0 4.66ZM16 16.658c0-1.8-1.79-3.26-4-3.26s-4 1.46-4 3.26\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Bulk = function Bulk(_ref3) {\n  var color = _ref3.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.002 12a2.33 2.33 0 1 0 0-4.66 2.33 2.33 0 0 0 0 4.66ZM14.681 16.658c.81 0 1.28-.9.83-1.57-.68-1.01-2-1.69-3.51-1.69-1.51 0-2.83.68-3.51 1.69-.45.67.02 1.57.83 1.57h5.36Z\",\n    fill: color\n  }));\n};\n\nvar Linear = function Linear(_ref4) {\n  var color = _ref4.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 11a2.33 2.33 0 1 0 0-4.66A2.33 2.33 0 0 0 12 11ZM16 16.66c0-1.8-1.79-3.26-4-3.26s-4 1.46-4 3.26\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Outline = function Outline(_ref5) {\n  var color = _ref5.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 22.751c-.67 0-1.35-.17-1.95-.52l-5.94-3.43c-1.2-.7-1.95-1.99-1.95-3.38v-6.84c0-1.39.75-2.68 1.95-3.38l5.94-3.43c1.2-.7 2.69-.7 3.9 0l5.94 3.43c1.2.7 1.95 1.99 1.95 3.38v6.84c0 1.39-.75 2.68-1.95 3.38l-5.94 3.43c-.6.35-1.28.52-1.95.52Zm0-20c-.41 0-.83.11-1.2.32l-5.94 3.43c-.74.43-1.2 1.22-1.2 2.08v6.84c0 .85.46 1.65 1.2 2.08l5.94 3.43c.74.43 1.66.43 2.4 0l5.94-3.43c.74-.43 1.2-1.22 1.2-2.08v-6.84c0-.85-.46-1.65-1.2-2.08l-5.94-3.43c-.37-.21-.79-.32-1.2-.32Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12.002 11.75c-1.7 0-3.08-1.38-3.08-3.08 0-1.7 1.38-3.08 3.08-3.08 1.7 0 3.08 1.38 3.08 3.08 0 1.7-1.38 3.08-3.08 3.08Zm0-4.66c-.87 0-1.58.71-1.58 1.58 0 .87.71 1.58 1.58 1.58.87 0 1.58-.71 1.58-1.58 0-.87-.71-1.58-1.58-1.58ZM16 17.408c-.41 0-.75-.34-.75-.75 0-1.38-1.46-2.51-3.25-2.51s-3.25 1.13-3.25 2.51c0 .41-.34.75-.75.75s-.75-.34-.75-.75c0-2.21 2.13-4.01 4.75-4.01s4.75 1.8 4.75 4.01c0 .41-.34.75-.75.75Z\",\n    fill: color\n  }));\n};\n\nvar TwoTone = function TwoTone(_ref6) {\n  var color = _ref6.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M12.002 11a2.33 2.33 0 1 0 0-4.66 2.33 2.33 0 0 0 0 4.66ZM16 16.658c0-1.8-1.79-3.26-4-3.26s-4 1.46-4 3.26\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar chooseVariant = function chooseVariant(variant, color) {\n  switch (variant) {\n    case 'Bold':\n      return /*#__PURE__*/React.createElement(Bold, {\n        color: color\n      });\n\n    case 'Broken':\n      return /*#__PURE__*/React.createElement(Broken, {\n        color: color\n      });\n\n    case 'Bulk':\n      return /*#__PURE__*/React.createElement(Bulk, {\n        color: color\n      });\n\n    case 'Linear':\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n\n    case 'Outline':\n      return /*#__PURE__*/React.createElement(Outline, {\n        color: color\n      });\n\n    case 'TwoTone':\n      return /*#__PURE__*/React.createElement(TwoTone, {\n        color: color\n      });\n\n    default:\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n  }\n};\n\nvar UserOctagon = /*#__PURE__*/forwardRef(function (_ref7, ref) {\n  var variant = _ref7.variant,\n      color = _ref7.color,\n      size = _ref7.size,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: ref,\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\"\n  }), chooseVariant(variant, color));\n});\nUserOctagon.propTypes = {\n  variant: PropTypes.oneOf(['Linear', 'Bold', 'Broken', 'Bulk', 'Outline', 'TwoTone']),\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nUserOctagon.defaultProps = {\n  variant: 'Linear',\n  color: 'currentColor',\n  size: '24'\n};\nUserOctagon.displayName = 'UserOctagon';\n\nexport { UserOctagon as default };\n"], "names": ["_excluded", "Bold", "_ref", "color", "react__WEBPACK_IMPORTED_MODULE_0__", "createElement", "Fragment", "d", "fill", "Broken", "_ref2", "stroke", "strokeWidth", "strokeMiterlimit", "strokeLinecap", "strokeLinejoin", "Bulk", "_ref3", "opacity", "Linear", "_ref4", "Outline", "_ref5", "TwoTone", "_ref6", "choose<PERSON><PERSON><PERSON>", "variant", "ShoppingCart", "forwardRef", "_ref7", "ref", "size", "rest", "_rollupPluginBabelHelpers_3bc641ae_js__WEBPACK_IMPORTED_MODULE_1__", "_", "a", "xmlns", "width", "height", "viewBox", "propTypes", "prop_types__WEBPACK_IMPORTED_MODULE_2___default", "oneOf", "string", "oneOfType", "number", "defaultProps", "displayName", "TagUser", "UserOctagon"], "sourceRoot": ""}