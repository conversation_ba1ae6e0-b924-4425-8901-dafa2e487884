"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[86648],{86648:function(e,t,n){n.r(t),n.d(t,{default:function(){return ea}});var a=n(57437),l=n(27300),s=n(70880),i=n(15681),r=n(3612),o=n(13590),d=n(2265),c=n(29501),u=n(43949),m=n(31229),h=n(16831),f=n(66070),v=n(53647),x=n(59532);function p(e){var t,n,l;let{form:s,name:i,avatar:r,agentId:o,commission:c,phoneNumber:m,processingTime:p,paymentMethods:g,onSelect:j}=e,{t:y}=(0,u.$G)(),w=null===(n=s.getFieldState("method"))||void 0===n?void 0:null===(t=n.error)||void 0===t?void 0:t.message;return d.useEffect(()=>{s.getValues("method")&&s.clearErrors("method")},[s.getValues("method")]),(0,a.jsx)(f.Zb,{children:(0,a.jsxs)(f.aY,{className:"px-4 py-4 text-foreground sm:px-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center gap-2",children:[(0,a.jsxs)(h.qE,{className:"size-10 border",children:[(0,a.jsx)(h.F$,{src:r,alt:i}),(0,a.jsx)(h.Q5,{children:(0,x.v)(i)})]}),(0,a.jsx)("span",{className:"font-bold",children:i})]}),(0,a.jsxs)("div",{className:"mb-8 grid grid-cols-12 gap-2 text-sm",children:[(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[(0,a.jsx)("p",{className:"opacity-70",children:y("Agent ID")}),(0,a.jsx)("p",{className:"font-medium",children:o})]}),(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[(0,a.jsx)("p",{className:"opacity-70",children:y("Phone")}),(0,a.jsx)("p",{className:"font-medium",children:m})]}),(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[(0,a.jsx)("p",{className:"opacity-70",children:y("Commission")}),(0,a.jsxs)("p",{className:"font-medium",children:[null!=c?c:0,"%"]})]}),p?(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[(0,a.jsx)("p",{className:"opacity-70",children:y("Processing Time")}),(0,a.jsx)("p",{className:"font-medium",children:p})]}):null]}),(0,a.jsxs)(v.Ph,{onValueChange:j,children:[(0,a.jsx)(v.i4,{className:"data-[placeholder]:text-placeholder capitalize",children:(0,a.jsx)(v.ki,{placeholder:y("Select payment gateway")})}),(0,a.jsx)(v.Bw,{children:null==g?void 0:null===(l=g.filter(e=>!!e.active&&!!e.allowWithdraw))||void 0===l?void 0:l.map(e=>(0,a.jsx)(v.Ql,{value:e.name,className:"capitalize",children:e.name},e.id))})]}),w?(0,a.jsx)("span",{className:"mt-2 block text-sm text-destructive",children:w}):null]})})}var g=n(41709),j=n(52323),y=n(85487),w=n(41062),N=n(62869),b=n(31117),C=n(19571),Z=n(58926),A=n(48674),I=n(73490),S=n(90433),k=n(22291);function z(e){var t,n,l,s,i;let{form:r,setAgent:o,onBack:c,onNext:m}=e,[h,f]=d.useState(),[v,x]=d.useState(!0),[z,P]=d.useState(""),{t:R}=(0,u.$G)(),V=r.getValues(),{data:E,isLoading:F}=(0,b.d)("/agents/deposit?countryCode=".concat(null==h?void 0:null===(t=h.code)||void 0===t?void 0:t.cca2,"&currencyCode=").concat(V.walletId,"&search=").concat(z));return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"mb-5",children:R("Select a country first")}),(0,a.jsx)(g.J,{condition:v,children:(0,a.jsx)(j.g,{defaultCountry:r.getValues("country"),onSelectChange:e=>{r.setValue("country",e.code.cca2),f(e)}})}),(0,a.jsx)(g.J,{condition:!v,children:(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"inline-flex gap-2",children:[(0,a.jsx)(w.W,{url:null==h?void 0:null===(n=h.flags)||void 0===n?void 0:n.png,className:"h-5 w-7"}),(0,a.jsx)("span",{children:null==h?void 0:h.name}),(0,a.jsxs)("div",{className:"flex items-center gap-2.5 pl-2.5",children:[(0,a.jsx)(C.Z,{size:"16",className:"text-primary",variant:"Bold"}),R("Selected")]})]}),(0,a.jsxs)(N.z,{variant:"ghost",size:"sm",className:"gap-2 font-medium",onClick:()=>x(!0),children:[R("Change country"),(0,a.jsx)(Z.Z,{size:16})]})]})})})]}),(0,a.jsx)(g.J,{condition:F,children:(0,a.jsx)(y.Loader,{className:"mt-8 flex justify-center py-10"})}),(0,a.jsxs)(g.J,{condition:!!h,children:[(0,a.jsxs)("div",{className:"mt-8 flex w-full flex-col justify-between gap-4 md:flex-row md:items-center",children:[(0,a.jsx)("h2",{children:R("Select agent")}),(0,a.jsxs)("div",{className:"flex w-full max-w-[200px] items-center rounded-[4px] border",children:[(0,a.jsx)("input",{type:"text",placeholder:R("Search agent..."),value:z,onChange:e=>P(e.target.value),className:"h-10 w-full flex-1 rounded-[4px] px-2.5 text-sm focus:outline-none"}),(0,a.jsx)("div",{className:"rounded-r-[4px] px-2.5",children:(0,a.jsx)(A.Z,{size:"20",variant:"Outline"})})]})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-12 gap-y-4 sm:gap-4",children:[(0,a.jsx)(g.J,{condition:(null==E?void 0:null===(l=E.data)||void 0===l?void 0:l.length)===0,children:(0,a.jsx)("div",{className:"col-span-12 h-32 text-sm font-semibold text-secondary-text",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,a.jsx)(I.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),(0,a.jsx)("p",{children:R("No agent available for this country.")})]})})}),(0,a.jsx)(g.J,{condition:!!(null==E?void 0:null===(s=E.data)||void 0===s?void 0:s.length),children:null==E?void 0:null===(i=E.data)||void 0===i?void 0:i.map(e=>{var t,n;return(0,a.jsx)("div",{className:"col-span-12 md:col-span-6",children:(0,a.jsx)(p,{form:r,name:e.name,avatar:"",agentId:e.agentId,commission:e.withdrawalCommission,processingTime:"",phoneNumber:null==e?void 0:null===(n=e.user)||void 0===n?void 0:null===(t=n.customer)||void 0===t?void 0:t.phone,paymentMethods:e.agentMethods,onSelect:t=>{var n;r.setValue("method",t),o({...e,method:null==e?void 0:null===(n=e.agentMethods)||void 0===n?void 0:n.find(e=>e.name===t)})}})},e.id)})})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex items-center justify-between gap-4",children:[(0,a.jsxs)(N.z,{variant:"outline",onClick:c,children:[(0,a.jsx)(S.Z,{}),R("Back")]}),(0,a.jsxs)(N.z,{type:"button",onClick:m,children:[R("Next"),(0,a.jsx)(k.Z,{})]})]})]})}var P=n(45932),R=n(95186);function V(e){let{form:t,toggleWithdrawType:n,toggleTab:l}=e,s=e=>{let a=0;t.watch("walletId")||(t.setError("walletId",{message:"Please select a wallet.",type:"custom"}),a+=1),t.watch("withdrawAmount")||(t.setError("withdrawAmount",{message:"Please enter withdraw amount.",type:"custom"},{shouldFocus:!0}),a+=1),0===a&&("regular"===e&&(n("regular"),l("review")),"agent"===e&&(n("agent"),l("agent_selection")))},{t:r}=(0,u.$G)();return(0,a.jsxs)("div",{className:"flex flex-col gap-4 md:gap-8",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,a.jsx)("h2",{children:r("Select wallet")}),(0,a.jsx)(i.Wi,{name:"walletId",control:t.control,render:e=>{let{field:t}=e;return(0,a.jsxs)(i.xJ,{children:[(0,a.jsx)(i.NI,{children:(0,a.jsx)(P.R,{value:t.value,onChange:t.onChange})}),(0,a.jsx)(i.zG,{})]})}})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,a.jsx)("h2",{children:r("How much?")}),(0,a.jsx)(i.Wi,{name:"withdrawAmount",control:t.control,render:e=>{let{field:t}=e;return(0,a.jsxs)(i.xJ,{children:[(0,a.jsx)(i.NI,{children:(0,a.jsx)(R.I,{type:"number",placeholder:r("Enter withdraw amount"),...t})}),(0,a.jsx)(i.zG,{})]})}})]}),(0,a.jsxs)("div",{className:"flex flex-col justify-end gap-2.5 md:flex-row",children:[(0,a.jsx)(N.z,{type:"button",variant:"outline",onClick:()=>s("agent"),children:r("Withdraw through an agent")}),(0,a.jsxs)(N.z,{type:"submit",onClick:()=>s("regular"),children:[r("Regular withdraw"),(0,a.jsx)(k.Z,{size:15})]})]})]})}var E=n(18629),F=n(6512),D=n(79981),T=n(97751);async function L(e){try{let t=await D.Z.post("/withdraw-requests/create",e);return(0,T.B)(t)}catch(e){return(0,T.D)(e)}}async function _(e){try{let t=await D.Z.post("/withdraws/create",e);return(0,T.B)(t)}catch(e){return(0,T.D)(e)}}var O=n(58414),G=n(99376),B=n(14438),W=n(55988),M=n(25318),J=n(94508),Y=n(43577);function $(e){var t,n,l;let{form:s,setActiveTab:i,method:r,type:o,currencyCode:c,agentId:m,amount:h}=e,{t:f}=(0,u.$G)(),v=new J.F(c),[x,p]=(0,d.useState)();return(0,d.useEffect)(()=>{(async()=>{"regular"===o?r&&await D.Z.get("/withdraws/preview/create?method=".concat(r,"&amount=").concat(h)).then(e=>{p(e.data)}).catch(e=>{if((0,Y.IZ)(e)){var t,n,a,l;i("withdraw_details"),s.setError("withdrawAmount",{message:null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(t=n.data)||void 0===t?void 0:t.message}),B.toast.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)}}):r&&await D.Z.get("/withdraw-requests/preview/create?agentId=".concat(m,"&amount=").concat(h)).then(e=>p(e.data)).catch(e=>{if((0,Y.IZ)(e)){var t,n,a,l;i("withdraw_details"),s.setError("withdrawAmount",{message:null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(t=n.data)||void 0===t?void 0:t.message}),B.toast.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)}})})()},[r]),(0,a.jsxs)(M.Y,{groupName:f("Withdraw details"),children:[(0,a.jsx)(M.r,{title:f("Amount"),value:v.formatVC(null!==(t=null==x?void 0:x.chargedAmount)&&void 0!==t?t:0)}),(0,a.jsx)(M.r,{title:f("Service charge"),value:v.formatVC(null!==(n=null==x?void 0:x.fee)&&void 0!==n?n:0)}),(0,a.jsx)(M.r,{title:f("You get"),value:v.formatVC(null!==(l=null==x?void 0:x.recievedAmount)&&void 0!==l?l:0),valueClassName:"text-xl sm:text-2xl font-semibold"})]})}var U=n(33145);function q(e){let{method:t,onEdit:n}=e,{t:l}=(0,u.$G)();return(0,a.jsx)("div",{className:"col-span-12",children:(0,a.jsxs)("div",{className:"flex flex-col items-start justify-between gap-4 md:flex-row md:items-center",children:[(0,a.jsxs)("div",{className:"inline-flex w-full items-center justify-between gap-2 md:justify-start",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 rounded-xl border px-3 py-2.5",children:[t.logo&&(0,a.jsx)(U.default,{src:t.logo,alt:t.name,width:100,height:100,className:"size-8 rounded-lg"}),(0,a.jsx)("span",{className:"font-semibold capitalize",children:null==t?void 0:t.name})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2.5 pl-2.5",children:[(0,a.jsx)(C.Z,{size:"16",className:"text-primary",variant:"Bold"}),l("Selected")]})]}),(0,a.jsxs)(N.z,{type:"button",variant:"ghost",size:"sm",className:"gap-2 font-medium",onClick:n,children:[l("Change"),(0,a.jsx)(Z.Z,{size:16})]})]})})}var Q=n(35974);function H(e){let{logo:t,type:n,name:l,fee:s,isRecommended:i=!1,defaultSelect:r="",onSelect:o}=e,{t:d}=(0,u.$G)();return(0,a.jsxs)("div",{"data-active":n===r,className:"relative flex h-full w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 p-5 transition-all duration-700 ease-in-out hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,a.jsx)("input",{type:"radio",checked:n===r,onChange:()=>o(n),className:"absolute inset-0 left-0 top-0 cursor-pointer opacity-0"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t?(0,a.jsx)(U.default,{src:t,alt:l,width:100,height:100,className:"size-8"}):null,(0,a.jsx)("h5",{className:"text",children:l})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2.5 py-2.5",children:[(0,a.jsxs)("p",{children:[d("Fee")," ",s]}),i?(0,a.jsx)(Q.C,{variant:"success",children:d("Recommended")}):null]})]})}function K(e){let{form:t,selectMethod:n,method:l,setMethod:s}=e,[i,r]=(0,d.useState)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.g,{defaultCountry:t.getValues("country"),onSelectChange:e=>{r(e),t.setValue("country",e.code.cca2)}}),(0,a.jsx)(X,{selectedMethod:l,country:i,form:t,setSelectedMethod:s,onSelect:n})]})}function X(e){var t,n;let{selectedMethod:l,country:s,setSelectedMethod:i,form:r,onSelect:o}=e,[c,m]=(0,d.useState)(!0),[h,f]=(0,d.useState)(),{t:v}=(0,u.$G)(),{data:x,isLoading:p}=(0,b.d)("/withdraws/methods/list?country=".concat(null==s?void 0:null===(t=s.code)||void 0===t?void 0:t.cca2,"&currency=").concat(r.getValues("walletId"))),g=new J.F;if(p)return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(y.Loader,{})});let j=null==x?void 0:null===(n=x.data)||void 0===n?void 0:n.filter(e=>!!e.active);return(null==j?void 0:j.length)?(0,a.jsx)("div",{className:"mt-8 grid grid-cols-12 gap-4",children:c?null==j?void 0:j.map(e=>(0,a.jsx)("div",{className:"col-span-6",children:(0,a.jsx)(H,{name:e.name,logo:(0,J.qR)(e.logoImage),type:e.value,isRecommended:!!e.recommended,fee:"".concat(e.percentageCharge,"% + ").concat(g.formatVC(e.fixedCharge,e.currencyCode)),defaultSelect:l,onSelect:t=>{f(e),i(t),m(!1),o(e)}})},e.id)):(0,a.jsx)(q,{method:{name:null==h?void 0:h.name,logo:(0,J.qR)(null==h?void 0:h.logoImage)},onEdit:()=>m(!0)})}):(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,a.jsx)(I.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),(0,a.jsx)("p",{children:v("No methods available for this country.")})]})})}function ee(e){var t,n;let{form:l,setActiveTab:s,agent:r,methodData:o,selectMethod:c,updateStateToComplete:m,withdrawType:h,onBack:f}=e,[v,x]=d.useTransition(),[p,j]=d.useState(l.getValues("method")),{t:w}=(0,u.$G)(),b=(0,G.useRouter)(),[C,Z]=d.useState(),A=()=>!!l.getValues("country")||(B.toast.error("Country is required"),!1),I=async e=>{x(async()=>{var t,n;let a={agentId:(null==r?void 0:r.agentId)?null==r?void 0:r.agentId:"",method:e.method,inputValue:null!==(t=e.inputValue)&&void 0!==t?t:"",amount:Number(e.withdrawAmount),currencyCode:e.walletId,countryCode:e.country},l=await L(a);l.status?(m("review"),b.push("/withdraw/transaction-status?trxId=".concat(null===(n=l.data)||void 0===n?void 0:n.trxId))):B.toast.error(l.message)})},z=async e=>{x(async()=>{var t,n;let a={method:p,amount:Number(null!==(t=e.withdrawAmount)&&void 0!==t?t:0),currencyCode:e.walletId,country:e.country,inputParams:C},l=await _(a);l.status?(m("review"),b.push("/withdraw/transaction-status?trxId=".concat(null===(n=l.data)||void 0===n?void 0:n.trxId))):B.toast.error(l.message)})},P=e=>{"agent"===h?I(e):z(e)};return(0,d.useEffect)(()=>{Z(void 0)},[p]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.J,{condition:"regular"===h,children:(0,a.jsx)(K,{form:l,selectMethod:c,method:p,setMethod:j})}),(0,a.jsx)(g.J,{condition:"agent"===h,children:(0,a.jsx)(q,{method:{name:null==r?void 0:null===(t=r.method)||void 0===t?void 0:t.name,logo:""},onEdit:()=>s("agent_selection")})}),(0,a.jsx)(F.Z,{className:"my-8"}),(0,a.jsx)($,{form:l,setActiveTab:s,method:p,type:h,agentId:(null==r?void 0:r.agentId)?r.agentId:"",currencyCode:l.getValues("walletId"),amount:Number(null!==(n=l.getValues("withdrawAmount"))&&void 0!==n?n:0)}),(0,a.jsxs)(g.J,{condition:"agent"===h,children:[(0,a.jsx)(F.Z,{className:"my-8"}),(0,W.EQ)(null==r?void 0:r.method).with({inputType:"phone"},()=>(0,a.jsxs)("div",{className:"mt-8 flex w-full flex-col",children:[(0,a.jsxs)("h2",{children:[w("Enter phone number"),"?"]}),(0,a.jsx)("p",{className:"mb-4",children:w("(Enter your number without country indicator)")}),(0,a.jsx)(i.Wi,{name:"inputValue",control:l.control,render:e=>{let{field:t}=e;return(0,a.jsx)(i.xJ,{children:(0,a.jsx)(i.NI,{children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(E.E,{onChange:e=>{if(!e){let n=(0,O.S)(e);t.onChange(null==n?void 0:n.number)}},options:{initialCountry:l.getValues("country")}})})})})}})]})).with({inputType:"email"},()=>(0,a.jsxs)("div",{className:"mt-8 flex w-full flex-col",children:[(0,a.jsxs)("h2",{children:[w("Enter email address"),"?"]}),(0,a.jsx)(R.I,{type:"email",name:"email",placeholder:w("Enter email address"),value:l.getValues("inputValue"),onChange:e=>l.setValue("inputValue",e.target.value)})]})).with({inputType:"other"},()=>{var e,t;return(0,a.jsxs)("div",{className:"mt-8 flex w-full flex-col",children:[(0,a.jsxs)("h2",{children:[w("Enter email ".concat(null==r?void 0:null===(e=r.method)||void 0===e?void 0:e.otherName)),"?"]}),(0,a.jsx)(R.I,{type:"text",name:null==r?void 0:null===(t=r.method)||void 0===t?void 0:t.otherName,value:l.getValues("inputValue"),onChange:e=>l.setValue("inputValue",e.target.value)})]})}).otherwise(()=>null)]}),(0,a.jsx)(g.J,{condition:"regular"===h,children:(0,a.jsx)(et,{params:(null==o?void 0:o.params)?JSON.parse(o.params):null,dynamicField:C,setDynamicField:Z})}),(0,a.jsx)(F.Z,{className:"my-8"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.z,{type:"button",variant:"outline",onClick:f,children:[(0,a.jsx)(S.Z,{}),w("Back")]}),(0,a.jsxs)(N.z,{type:"button",disabled:v,onClick:()=>{A()&&l.handleSubmit(P)()},children:[v?(0,a.jsx)(y.Loader,{title:w("Processing..."),className:"text-primary-foreground"}):w("Withdraw"),(0,a.jsx)(k.Z,{})]})]})]})}function et(e){let{params:t,dynamicField:n,setDynamicField:l}=e,{t:s}=(0,u.$G)();return t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(F.Z,{className:"mt-6"}),(0,a.jsx)("div",{className:"mt-8 flex flex-col gap-4",children:null==t?void 0:t.map((e,t)=>(0,a.jsxs)("div",{className:"flex w-full flex-col",children:[(0,a.jsx)("h3",{className:"mb-3",children:s(e.label)}),(0,a.jsx)(R.I,{type:e.type,name:e.name,value:null==n?void 0:n[e.name],placeholder:"Enter ".concat(e.label),onChange:e=>{l(t=>({...t,[e.target.name]:e.target.value}))}})]},t))})]}):null}let en=m.z.object({walletId:m.z.string().min(1,"Wallet ID is required."),withdrawAmount:m.z.string().min(1,"Withdraw amount is required."),method:m.z.string(),country:m.z.string(),phone:m.z.string().optional(),inputValue:m.z.string().optional()});function ea(){let{auth:e,deviceLocation:t}=(0,r.a)(),{t:n}=(0,u.$G)(),[m,h]=d.useState("withdraw_details"),[f,v]=d.useState("regular"),[x,p]=d.useState(null),[g,j]=d.useState(),[y,w]=d.useState([{value:"withdraw_details",title:n("Withdraw Details"),complete:!1},{value:"agent_selection",title:n("Agent Selection"),isVisible:!1,complete:!1},{value:"review",title:n("Payment & Review"),complete:!1}]),N=(0,c.cI)({resolver:(0,o.F)(en),mode:"all",defaultValues:{walletId:"",withdrawAmount:"",method:"",country:""}});d.useEffect(()=>{"withdraw_details"===m&&N.reset({walletId:N.getValues("walletId"),withdrawAmount:N.getValues("withdrawAmount"),method:"",country:null==t?void 0:t.countryCode,phone:""})},[m]),d.useEffect(()=>{w(e=>e.map(e=>"agent_selection"===e.value?{...e,isVisible:"agent"===f}:e))},[f]),d.useEffect(()=>{t&&"amount"===m&&N.reset({walletId:N.getValues("walletId"),withdrawAmount:N.getValues("withdrawAmount"),method:"",country:null==t?void 0:t.countryCode})},[t]),d.useEffect(()=>()=>{N.reset({walletId:"",withdrawAmount:"",method:"",country:null==t?void 0:t.countryCode,phone:""})},[]);let b=d.useCallback(e=>{w(y.map(t=>t.value===e?{...t,complete:!0}:t))},[y]);return(null==e?void 0:e.canMakeWithdraw())?(0,a.jsx)(i.l0,{...N,children:(0,a.jsx)("form",{className:"md:h-full",children:(0,a.jsx)("div",{className:"w-full p-4 pb-10 md:h-full md:p-12",children:(0,a.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,a.jsx)(s.R,{tabs:y,onTabChange:e=>h(e),value:m,children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)(s.Q,{value:"withdraw_details",children:(0,a.jsx)(V,{form:N,toggleTab:e=>{h(e),b("withdraw_details")},toggleWithdrawType:e=>v(e)})}),(0,a.jsx)(s.Q,{value:"agent_selection",children:(0,a.jsx)(z,{form:N,setAgent:p,onBack:()=>h("withdraw_details"),onNext:()=>{if("agent"===f&&!N.getValues("method")){N.setError("method",{message:n("Select a method to continue."),type:"required"});return}h("review"),b("agent_selection")}})}),(0,a.jsx)(s.Q,{value:"review",children:(0,a.jsx)(ee,{form:N,agent:x,methodData:g,selectMethod:j,setActiveTab:h,updateStateToComplete:b,withdrawType:f,onBack:()=>h("agent"===f?"agent_selection":"withdraw_details")})})]})})})})})}):(0,a.jsx)(l.Z,{className:"flex-1 p-10"})}},41709:function(e,t,n){function a(e){let{condition:t,children:n}=e;return t?n:null}n.d(t,{J:function(){return a}}),n(2265)},80114:function(e,t,n){n.d(t,{default:function(){return r}});var a=n(57437),l=n(85487),s=n(94508),i=n(43949);function r(e){let{className:t}=e,{t:n}=(0,i.$G)();return(0,a.jsx)("div",{className:(0,s.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,a.jsx)(l.Loader,{title:n("Loading..."),className:"text-foreground"})})}},27300:function(e,t,n){n.d(t,{Z:function(){return r}});var a=n(57437),l=n(94508),s=n(27648),i=n(43949);function r(e){let{className:t}=e,{t:n}=(0,i.$G)();return(0,a.jsx)("div",{className:(0,l.ZP)("flex items-center justify-center",t),children:(0,a.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[(0,a.jsx)("h3",{className:"mb-2.5",children:n("This feature is temporarily unavailable")}),(0,a.jsxs)("p",{className:"text-sm text-secondary-text",children:[n("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),(0,a.jsx)(s.default,{href:"/contact-supports",className:"text-primary hover:underline",children:n("support")}),"."]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-secondary-text",children:n("Thank you for your understanding.")})]})})}},25318:function(e,t,n){n.d(t,{Y:function(){return i},r:function(){return r}});var a=n(57437),l=n(93022),s=n(94508);function i(e){let{groupName:t,children:n,className:l}=e;return(0,a.jsxs)("ul",{className:(0,s.ZP)("flex flex-col gap-y-4 text-sm sm:text-base",l),children:[t?(0,a.jsx)("li",{className:"text-base font-medium leading-[22px]",children:t}):null,n]})}function r(e){let{title:t,value:n,className:i,titleClassName:r,valueClassName:o,isLoading:d=!1}=e;return d?(0,a.jsx)("li",{className:(0,s.ZP)("flex items-center gap-4",i),children:(0,a.jsx)(l.O,{className:"h-5 w-2/3"})}):(0,a.jsxs)("li",{className:(0,s.ZP)("flex items-center gap-4",i),children:[(0,a.jsx)("div",{className:(0,s.ZP)("flex-1",r),children:t}),(0,a.jsx)("div",{className:(0,s.ZP)("justify-self-end text-right font-medium leading-[22px]",o),children:n})]})}n(2265)},70880:function(e,t,n){n.d(t,{Q:function(){return c},R:function(){return d}});var a=n(57437),l=n(12339),s=n(94508),i=n(19571),r=n(2265),o=n(6512);function d(e){let{value:t="",tabs:n=[],children:d,onTabChange:c}=e,[u,m]=r.useState(0),h=n.filter(e=>void 0===e.isVisible||!0===e.isVisible),f=h.findIndex(e=>e.value===t),v=h.length;return r.useEffect(()=>{m((f+1)/v*100)},[f,v,t]),(0,a.jsxs)(l.mQ,{value:t,onValueChange:c,children:[(0,a.jsx)("div",{className:"hidden h-0.5 w-full bg-background-body md:flex",children:(0,a.jsx)(o.Z,{className:(0,s.ZP)("h-0.5 bg-primary transition-[width] duration-200"),style:{width:"".concat(u,"%")}})}),(0,a.jsx)(l.dr,{className:"hidden bg-transparent md:flex",children:h.map((e,t)=>(0,a.jsxs)(l.SP,{value:e.value,disabled:t>f,"data-complete":e.complete,className:"ring-none group h-8 justify-start rounded-lg border-none border-border px-3 text-sm font-normal leading-5 text-foreground shadow-none outline-none transition-all duration-200 hover:bg-accent hover:text-primary data-[state=active]:bg-transparent data-[complete=true]:text-primary data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:hover:bg-accent",children:[(0,a.jsx)(i.Z,{size:19,className:"mr-2 group-hover:text-primary",variant:e.complete?"Bold":"Linear"}),e.title]},e.value))}),d]})}function c(e){let{children:t,...n}=e;return(0,a.jsx)(l.nU,{...n,children:t})}},52323:function(e,t,n){n.d(t,{g:function(){return h}});var a=n(57437),l=n(2265),s=n(85487),i=n(41062),r=n(23518),o=n(57054),d=n(40593),c=n(94508),u=n(36887),m=n(43949);function h(e){var t,n;let{allCountry:h=!1,defaultValue:f,defaultCountry:v,onSelectChange:x,disabled:p=!1,triggerClassName:g,arrowClassName:j,flagClassName:y,display:w,placeholderClassName:N,align:b="start",side:C="bottom"}=e,{t:Z}=(0,m.$G)(),{countries:A,getCountryByCode:I,isLoading:S}=(0,d.F)(),[k,z]=l.useState(!1),[P,R]=l.useState(f);return l.useEffect(()=>{f&&R(f)},[f]),l.useEffect(()=>{(async()=>{v&&await I(v,e=>{e&&(R(e),x(e))})})()},[v]),(0,a.jsxs)(o.J2,{open:k,onOpenChange:z,children:[(0,a.jsxs)(o.xo,{disabled:p,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",g),children:[P?(0,a.jsx)("div",{className:"flex flex-1 items-center",children:(0,a.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,a.jsx)(i.W,{className:y,countryCode:(null===(t=P.code)||void 0===t?void 0:t.cca2)==="*"?"UN":null===(n=P.code)||void 0===n?void 0:n.cca2}),void 0!==w?w(P):(0,a.jsx)("span",{children:P.name})]})}):(0,a.jsx)("span",{className:(0,c.ZP)("text-placeholder",N),children:Z("Select country")}),(0,a.jsx)(u.Z,{className:(0,c.ZP)("size-6",j)})]}),(0,a.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:b,side:C,children:(0,a.jsxs)(r.mY,{children:[(0,a.jsx)(r.sZ,{placeholder:Z("Search...")}),(0,a.jsx)(r.e8,{children:(0,a.jsxs)(r.fu,{children:[S&&(0,a.jsx)(s.Loader,{}),h&&(0,a.jsxs)(r.di,{value:Z("All countries"),onSelect:()=>{R({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),x({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),z(!1)},children:[(0,a.jsx)(i.W,{countryCode:"UN"}),(0,a.jsx)("span",{className:"pl-1.5",children:Z("All countries")})]}),null==A?void 0:A.map(e=>"officially-assigned"===e.status?(0,a.jsxs)(r.di,{value:e.name,onSelect:()=>{R(e),x(e),z(!1)},children:[(0,a.jsx)(i.W,{countryCode:e.code.cca2}),(0,a.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},18629:function(e,t,n){n.d(t,{E:function(){return Z}});var a=n(57437),l=n(85487),s=n(41062),i=n(23518),r=n(95186),o=n(57054),d=n(40593),c=n(94508),u=n(95550),m=n(36887),h=n(58414),f=n(78286),v=n(19368),x=n(68953),p=n(56555),g=n(5874),j=n(19615),y=n(93781),w=n(83057),N=n(43949),b=n(2265);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function Z(e){let{value:t,defaultValue:n="",onChange:l,onBlur:s,disabled:i,inputClassName:o,options:d}=e,[u,m]=(0,b.useState)(null!=n?n:""),[y,N]=(0,b.useState)(""),[Z,I]=(0,b.useState)(null==d?void 0:d.initialCountry),S=e=>{if(e)try{let t=h.S(e,Z);t?(I(t.country),N("+".concat(t.countryCallingCode)),m(t.formatNational())):m(e)}catch(t){m(e)}else m(e)};(0,b.useEffect)(()=>{t&&S(t)},[t]);let k=f.L(Z||(null==d?void 0:d.initialCountry)||"US",w.Z);return(0,a.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(A,{country:Z,disabled:i,initialCountry:null==d?void 0:d.initialCountry,onSelect:e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase(),a=v.G(n);N("+".concat(a)),I(n)}}),(0,a.jsx)("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:y||"+".concat(null==k?void 0:k.countryCallingCode)})]}),(0,a.jsx)(r.I,{type:"tel",className:(0,c.ZP)("rounded-l-none pl-2",o),value:u,onChange:e=>{let{value:t}=e.target,n=h.S(t,Z);null==s||s(""),n&&x.t(t,Z)&&p.q(t,Z)?(I(n.country),N("+".concat(n.countryCallingCode)),null==l||l(n.number),m(t)):(n?m(n.nationalNumber):m(t),null==l||l(t))},onPaste:e=>{let t=e.clipboardData.getData("Text"),n=h.S(t);if(n&&x.t(t))S(n.formatNational()),I(n.country),N("+".concat(n.countryCallingCode)),null==l||l(n.number),null==s||s("");else{let e=h.S(t,Z);e&&x.t(t,Z)&&(S(e.formatNational()),null==l||l(e.number),null==s||s(""))}},onBlur:()=>{if(u&&!g.y(u,Z)){let e=j.d(u,Z);e&&(null==s||s(C[e]))}},placeholder:null==k?void 0:k.formatNational(),disabled:i})]})}function A(e){let{initialCountry:t,country:n,onSelect:l,disabled:i}=e,[r,d]=(0,b.useState)(!1);return(0,a.jsxs)(o.J2,{open:r,onOpenChange:d,children:[(0,a.jsxs)(o.xo,{disabled:i,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[t||n?(0,a.jsx)(s.W,{countryCode:n||t,className:"aspect-auto h-[18px] w-7 flex-1"}):(0,a.jsx)(u.Z,{}),(0,a.jsx)(m.Z,{variant:"Bold",size:16})]}),(0,a.jsx)(o.yk,{align:"start",className:"h-fit p-0",children:(0,a.jsx)(I,{defaultValue:n||t,onSelect:e=>{l(e),d(!1)}})})]})}function I(e){var t;let{defaultValue:n,onSelect:r}=e,{countries:o,isLoading:c}=(0,d.F)(),{t:u}=(0,N.$G)();return(0,a.jsxs)(i.mY,{children:[(0,a.jsx)(i.sZ,{placeholder:u("Search country by name"),className:"placeholder:text-input-placeholder"}),(0,a.jsx)(i.e8,{children:(0,a.jsx)(i.fu,{children:c?(0,a.jsx)(i.di,{children:(0,a.jsx)(l.Loader,{})}):null===(t=o.filter(e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase();return y.o().includes(n)}))||void 0===t?void 0:t.map(e=>(0,a.jsxs)(i.di,{value:e.name,"data-active":e.code.cca2===n,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>r(e),children:[(0,a.jsx)(s.W,{countryCode:e.code.cca2}),e.name]},e.code.ccn3))})})]})}},45932:function(e,t,n){n.d(t,{R:function(){return f}});var a=n(57437),l=n(41709),s=n(33145),i=n(43949);function r(e){let{walletId:t,logo:n,name:l,balance:r,selectedWallet:o,onSelect:d,id:c}=e,{t:u}=(0,i.$G)();return(0,a.jsxs)("label",{htmlFor:"wallet-".concat(t,"-").concat(c),"data-active":t===o,className:"relative flex w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 px-6 py-4 transition-all duration-300 ease-linear hover:border-transparent hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,a.jsx)("input",{type:"radio",id:"wallet-".concat(t,"-").concat(c),checked:t===o,onChange:()=>d(t),className:"absolute inset-0 left-0 top-0 z-10 cursor-pointer opacity-0"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,a.jsx)(s.default,{src:n,alt:l,width:100,height:100,className:"size-8"}),(0,a.jsx)("h6",{className:"text-sm font-bold leading-5",children:l})]}),(0,a.jsxs)("div",{className:"mt-2.5",children:[(0,a.jsx)("p",{className:"text-xs font-normal leading-4 text-foreground",children:u("Your Balance")}),(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:Number(r).toFixed(2)})]})]})}var o=n(62869),d=n(93022),c=n(48358),u=n(66605),m=n(36887),h=n(2265);let f=(0,h.forwardRef)(function(e,t){var n;let{value:s,onChange:f,id:v}=e,{t:x}=(0,i.$G)(),[p,g]=h.useState(!1),{wallets:j,isLoading:y}=(0,c.r)(),w=h.useMemo(()=>j,[j]);return(h.useEffect(()=>{let e=w.find(e=>e.defaultStatus);e&&!s&&f(null==e?void 0:e.currency.code)},[w]),y)?(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:[(0,a.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,a.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,a.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"})]}):(0,a.jsxs)("div",{ref:t,id:v,children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:null===(n=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;return t?e:e.slice(0,n)}(j,p))||void 0===n?void 0:n.map(e=>(null==e?void 0:e.currency.code)&&(0,a.jsx)(h.Fragment,{children:(0,a.jsx)(r,{walletId:null==e?void 0:e.currency.code,logo:e.logo,name:null==e?void 0:e.currency.code,balance:e.balance,selectedWallet:s,onSelect:f,id:v})},e.walletId))}),(0,a.jsx)(l.J,{condition:(null==j?void 0:j.length)>3,children:(0,a.jsx)("div",{className:"mt-2 flex justify-end",children:(0,a.jsxs)(o.z,{type:"button",variant:"link",onClick:()=>g(!p),className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[(0,a.jsx)("span",{className:"text-inherit",children:x(p?"Show less":"Show more")}),p?(0,a.jsx)(u.Z,{size:12}):(0,a.jsx)(m.Z,{size:12})]})})})]})})},16831:function(e,t,n){n.d(t,{F$:function(){return o},Q5:function(){return d},qE:function(){return r}});var a=n(57437),l=n(2265),s=n(61146),i=n(94508);let r=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)(s.fC,{ref:t,className:(0,i.ZP)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",n),...l})});r.displayName=s.fC.displayName;let o=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)(s.Ee,{ref:t,className:(0,i.ZP)("aspect-square h-full w-full",n),...l})});o.displayName=s.Ee.displayName;let d=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)(s.NY,{ref:t,className:(0,i.ZP)("flex h-full w-full items-center justify-center rounded-full bg-muted",n),...l})});d.displayName=s.NY.displayName},35974:function(e,t,n){n.d(t,{C:function(){return r}});var a=n(57437),l=n(90535);n(2265);var s=n(94508);let i=(0,l.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function r(e){let{className:t,variant:n,...l}=e;return(0,a.jsx)("div",{className:(0,s.ZP)(i({variant:n}),t),...l})}},66070:function(e,t,n){n.d(t,{Ol:function(){return r},SZ:function(){return d},Zb:function(){return i},aY:function(){return c},eW:function(){return u},ll:function(){return o}});var a=n(57437),l=n(2265),s=n(94508);let i=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...l})});i.displayName="Card";let r=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.ZP)("flex flex-col space-y-1.5 p-6",n),...l})});r.displayName="CardHeader";let o=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)("h3",{ref:t,className:(0,s.ZP)("text-2xl font-semibold leading-none tracking-tight",n),...l})});o.displayName="CardTitle";let d=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)("p",{ref:t,className:(0,s.ZP)("text-sm text-muted-foreground",n),...l})});d.displayName="CardDescription";let c=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.ZP)("p-6 pt-0",n),...l})});c.displayName="CardContent";let u=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.ZP)("flex items-center p-6 pt-0",n),...l})});u.displayName="CardFooter"},15681:function(e,t,n){n.d(t,{NI:function(){return x},Wi:function(){return u},l0:function(){return d},lX:function(){return v},xJ:function(){return f},zG:function(){return p}});var a=n(57437),l=n(37053),s=n(2265),i=n(29501),r=n(26815),o=n(94508);let d=i.RV,c=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(i.Qr,{...t})})},m=()=>{let e=s.useContext(c),t=s.useContext(h),{getFieldState:n,formState:a}=(0,i.Gc)(),l=n(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:r}=t;return{id:r,name:e.name,formItemId:"".concat(r,"-form-item"),formDescriptionId:"".concat(r,"-form-item-description"),formMessageId:"".concat(r,"-form-item-message"),...l}},h=s.createContext({}),f=s.forwardRef((e,t)=>{let{className:n,...l}=e,i=s.useId();return(0,a.jsx)(h.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",n),...l})})});f.displayName="FormItem";let v=s.forwardRef((e,t)=>{let{className:n,required:l,...s}=e,{error:i,formItemId:d}=m();return(0,a.jsx)("span",{children:(0,a.jsx)(r.Z,{ref:t,className:(0,o.ZP)(i&&"text-base font-medium text-destructive",n),htmlFor:d,...s})})});v.displayName="FormLabel";let x=s.forwardRef((e,t)=>{let{...n}=e,{error:s,formItemId:i,formDescriptionId:r,formMessageId:o}=m();return(0,a.jsx)(l.g7,{ref:t,id:i,"aria-describedby":s?"".concat(r," ").concat(o):"".concat(r),"aria-invalid":!!s,...n})});x.displayName="FormControl",s.forwardRef((e,t)=>{let{className:n,...l}=e,{formDescriptionId:s}=m();return(0,a.jsx)("p",{ref:t,id:s,className:(0,o.ZP)("text-sm text-muted-foreground",n),...l})}).displayName="FormDescription";let p=s.forwardRef((e,t)=>{let{className:n,children:l,...s}=e,{error:i,formMessageId:r}=m(),d=i?String(null==i?void 0:i.message):l;return d?(0,a.jsx)("p",{ref:t,id:r,className:(0,o.ZP)("text-sm font-medium text-destructive",n),...s,children:d}):null});p.displayName="FormMessage"},57054:function(e,t,n){n.d(t,{J2:function(){return r},xo:function(){return o},yk:function(){return d}});var a=n(57437),l=n(2265),s=n(27312),i=n(94508);let r=s.fC,o=s.xz,d=l.forwardRef((e,t)=>{let{className:n,align:l="center",sideOffset:r=4,...o}=e;return(0,a.jsx)(s.h_,{children:(0,a.jsx)(s.VY,{ref:t,align:l,sideOffset:r,className:(0,i.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...o})})});d.displayName=s.VY.displayName},53647:function(e,t,n){n.d(t,{Bw:function(){return x},Ph:function(){return u},Ql:function(){return p},i4:function(){return h},ki:function(){return m}});var a=n(57437),l=n(68856),s=n(22135),i=n(40875),r=n(2265),o=n(94508),d=n(36887),c=n(22291);let u=l.fC;l.ZA;let m=l.B4,h=r.forwardRef((e,t)=>{let{className:n,children:s,...i}=e;return(0,a.jsxs)(l.xz,{ref:t,className:(0,o.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),...i,children:[s,(0,a.jsx)(l.JO,{asChild:!0,children:(0,a.jsx)(d.Z,{size:"24",color:"#292D32"})})]})});h.displayName=l.xz.displayName;let f=r.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,a.jsx)(l.u_,{ref:t,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",n),...i,children:(0,a.jsx)(s.Z,{className:"h-4 w-4"})})});f.displayName=l.u_.displayName;let v=r.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(l.$G,{ref:t,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",n),...s,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});v.displayName=l.$G.displayName;let x=r.forwardRef((e,t)=>{let{className:n,children:s,position:i="popper",...r}=e;return(0,a.jsx)(l.h_,{children:(0,a.jsxs)(l.VY,{ref:t,className:(0,o.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:i,...r,children:[(0,a.jsx)(f,{}),(0,a.jsx)(l.l_,{className:(0,o.ZP)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(v,{})]})})});x.displayName=l.VY.displayName,r.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(l.__,{ref:t,className:(0,o.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",n),...s})}).displayName=l.__.displayName;let p=r.forwardRef((e,t)=>{let{className:n,children:s,...i}=e;return(0,a.jsxs)(l.ck,{ref:t,className:(0,o.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.wU,{children:(0,a.jsx)(c.Z,{variant:"Bold",className:"h-4 w-4"})})}),(0,a.jsx)(l.eT,{children:s})]})});p.displayName=l.ck.displayName,r.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(l.Z0,{ref:t,className:(0,o.ZP)("-mx-1 my-1 h-px bg-muted",n),...s})}).displayName=l.Z0.displayName},6512:function(e,t,n){var a=n(57437),l=n(55156),s=n(2265),i=n(94508);let r=s.forwardRef((e,t)=>{let{className:n,orientation:s="horizontal",decorative:r=!0,...o}=e;return(0,a.jsx)(l.f,{ref:t,decorative:r,orientation:s,className:(0,i.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",n),...o})});r.displayName=l.f.displayName,t.Z=r},93022:function(e,t,n){n.d(t,{O:function(){return s}});var a=n(57437),l=n(94508);function s(e){let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,l.ZP)("animate-pulse rounded-md bg-muted",t),...n})}},12339:function(e,t,n){n.d(t,{SP:function(){return d},dr:function(){return o},mQ:function(){return r},nU:function(){return c}});var a=n(57437),l=n(2265),s=n(20271),i=n(94508);let r=s.fC,o=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)(s.aV,{ref:t,className:(0,i.ZP)("inline-flex h-10 w-full items-center justify-center rounded-md bg-secondary p-1 text-muted-foreground",n),...l})});o.displayName=s.aV.displayName;let d=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)(s.xz,{ref:t,className:(0,i.ZP)("inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-semibold text-secondary-800 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite",n),...l})});d.displayName=s.xz.displayName;let c=l.forwardRef((e,t)=>{let{className:n,...l}=e;return(0,a.jsx)(s.VY,{ref:t,className:(0,i.ZP)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",n),...l})});c.displayName=s.VY.displayName},17062:function(e,t,n){n.d(t,{Z:function(){return v},O:function(){return f}});var a=n(57437),l=n(80114);n(83079);var s=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),i=n(31117),r=n(79981),o=n(78040),d=n(83130);class c{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var u=n(99376),m=n(2265);let h=m.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),f=()=>m.useContext(h);function v(e){let{children:t}=e,[n,f]=m.useState("Desktop"),[v,x]=m.useState(!1),[p,g]=m.useState(),{data:j,isLoading:y,error:w,mutate:N}=(0,i.d)("/auth/check",{revalidateOnFocus:!1}),{data:b,isLoading:C}=(0,i.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:Z,isLoading:A}=(0,i.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),I=(0,u.useRouter)(),S=(0,u.usePathname)();m.useEffect(()=>{(async()=>{f((await s()).deviceType)})()},[]),m.useEffect(()=>{let e=()=>{let e=window.innerWidth;f(e<768?"Mobile":e<1024?"Tablet":"Desktop"),x(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),m.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await r.Z.post("/auth/geo-location");g(new c(e))}catch(e){}})()},[]),m.useLayoutEffect(()=>{w&&!o.sp.includes(S)&&I.push("/signin")},[w]);let k=m.useMemo(()=>{var e,t,a;return{isAuthenticate:!!(null==j?void 0:null===(e=j.data)||void 0===e?void 0:e.login),auth:(null==j?void 0:null===(t=j.data)||void 0===t?void 0:t.user)?new d.n(null==j?void 0:null===(a=j.data)||void 0===a?void 0:a.user):null,isLoading:y,deviceLocation:p,refreshAuth:()=>N(j),isExpanded:v,device:n,setIsExpanded:x,branding:null==b?void 0:b.data,googleAnalytics:(null==Z?void 0:Z.data)?{active:null==Z?void 0:Z.data.active,apiKey:null==Z?void 0:Z.data.apiKey}:{active:!1,apiKey:""}}},[j,p,v,n]),z=!y&&!C&&!A;return(0,a.jsx)(h.Provider,{value:k,children:z?t:(0,a.jsx)(l.default,{})})}},97751:function(e,t,n){n.d(t,{B:function(){return l},D:function(){return s}});var a=n(43577);function l(e){var t,n,a;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(a=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==a?a:"",data:null===(n=e.data)||void 0===n?void 0:n.data}}function s(e){let t=500,n="Internal Server Error",l="An unknown error occurred";if((0,a.IZ)(e)){var s,i,r,o,d,c,u,m,h,f,v,x;t=null!==(h=null===(s=e.response)||void 0===s?void 0:s.status)&&void 0!==h?h:500,n=null!==(f=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==f?f:"Internal Server Error",l=null!==(x=null!==(v=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(r=o[0])||void 0===r?void 0:r.message)&&void 0!==v?v:null===(m=e.response)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:u.message)&&void 0!==x?x:e.message}else e instanceof Error&&(l=e.message);return{statusCode:t,statusText:n,status:!1,message:l,data:void 0,error:e}}},3612:function(e,t,n){n.d(t,{a:function(){return l}});var a=n(17062);let l=()=>{let e=(0,a.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},31117:function(e,t,n){n.d(t,{d:function(){return s}});var a=n(79981),l=n(85323);let s=(e,t)=>(0,l.ZP)(e||null,e=>a.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},48358:function(e,t,n){n.d(t,{r:function(){return i}});var a=n(79981),l=n(54763),s=n(85323);function i(){var e,t;let{data:n,isLoading:i,mutate:r}=(0,s.ZP)("/wallets",e=>a.Z.get(e));return{wallets:null!==(t=null==n?void 0:null===(e=n.data)||void 0===e?void 0:e.map(e=>new l.w(e)))&&void 0!==t?t:[],isLoading:i,getWalletByCurrencyCode:(e,t)=>null==e?void 0:e.find(e=>{var n;return(null==e?void 0:null===(n=e.currency)||void 0===n?void 0:n.code)===t}),mutate:r}}},74539:function(e,t,n){n.d(t,{k:function(){return a}});class a{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){n.d(t,{n:function(){return o}});class a{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var l=n(84937);class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var i=n(66419),r=n(78040);class o{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(r.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new i.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new l.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new s(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new a(e.agent):void 0}}},502:function(e,t,n){n.d(t,{Z:function(){return a}});class a{constructor(e){this.id=null==e?void 0:e.id,this.cardId=null==e?void 0:e.cardId,this.userId=null==e?void 0:e.userId,this.walletId=null==e?void 0:e.walletId,this.number=null==e?void 0:e.number,this.cvc=null==e?void 0:e.cvc,this.lastFour=null==e?void 0:e.lastFour,this.brand=null==e?void 0:e.brand,this.expMonth=null==e?void 0:e.expMonth,this.expYear=null==e?void 0:e.expYear,this.status=null==e?void 0:e.status,this.type=null==e?void 0:e.type,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.wallet=null==e?void 0:e.wallet,this.user=null==e?void 0:e.user}}},28315:function(e,t,n){n.d(t,{F:function(){return a}});class a{format(e){let{currencySymbol:t,amountText:n}=this.formatter(e);return"".concat(n," ").concat(t)}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}constructor(e){var t;this.formatter=e=>{var t,n;let a=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),l=null!==(n=null===(t=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===t?void 0:t.value)&&void 0!==n?n:this.code,s=a.format(e),i=s.substring(l.length).trim();return{currencyCode:this.code,currencySymbol:l,formattedAmount:s,amountText:i}},this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.code=null==e?void 0:e.code,this.logo=null!==(t=null==e?void 0:e.logo)&&void 0!==t?t:"",this.usdRate=null==e?void 0:e.usdRate,this.acceptApiRate=!!(null==e?void 0:e.acceptApiRate),this.isCrypto=!!(null==e?void 0:e.isCrypto),this.active=!!(null==e?void 0:e.active),this.metaData=null==e?void 0:e.metaData,this.minAmount=null==e?void 0:e.minAmount,this.kycLimit=null==e?void 0:e.kycLimit,this.maxAmount=null==e?void 0:e.maxAmount,this.dailyTransferAmount=null==e?void 0:e.dailyTransferAmount,this.dailyTransferLimit=null==e?void 0:e.dailyTransferLimit,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},84937:function(e,t,n){n.d(t,{O:function(){return l}});var a=n(74539);class l{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new a.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){n.d(t,{u:function(){return a}});class a{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},54763:function(e,t,n){n.d(t,{w:function(){return s}});var a=n(502),l=n(28315);class s{constructor(e){var t;this.id=null==e?void 0:e.id,this.walletId=null==e?void 0:e.walletId,this.logo=null==e?void 0:e.logo,this.userId=null==e?void 0:e.userId,this.balance=null==e?void 0:e.balance,this.defaultStatus=!!(null==e?void 0:e.default),this.pinDashboard=!!(null==e?void 0:e.pinDashboard),this.currencyId=null==e?void 0:e.currencyId,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.currency=new l.F(null==e?void 0:e.currency),this.cards=null==e?void 0:null===(t=e.cards)||void 0===t?void 0:t.map(e=>new a.Z(e))}}},59532:function(e,t,n){n.d(t,{v:function(){return a}});function a(e){if(!e)return"";let t=e.split(" ");return(t.length>2?t[0].length>3?t[0][0]+t[t.length-1][0]:t[1][0]+t[t.length-1][0]:2===t.length?t[0][0]+t[1][0]:t[0][0]).toUpperCase()}}}]);