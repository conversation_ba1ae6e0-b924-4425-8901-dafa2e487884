"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[12634],{12634:function(e,t,n){n.r(t),n.d(t,{default:function(){return C}});var r=n(57437),i=n(85539),s=n(62869),a=n(66070),o=n(95186),l=n(3612),d=n(21251),u=n(31117),c=n(94508),h=n(50813),f=n(43271),m=n(99376),v=n(2265),p=n(43949),g=n(25833),x=n(28456),b=n(66574),y=n(33145);let w=new c.F;function N(e){let{referralUsers:t,isLoading:n}=e,[i,s]=v.useState([]),{t:a}=(0,p.$G)();return(0,r.jsx)(g.Z,{data:t,sorting:i,setSorting:s,isLoading:n,structure:[{id:"name",header:a("Name"),cell:e=>{var t,n,i,s;let{row:a}=e,o=null==a?void 0:a.original;return(0,r.jsxs)("div",{className:"flex min-w-28 items-center gap-2.5 font-normal",children:[(null==o?void 0:null===(t=o.customer)||void 0===t?void 0:t.profileImage)?(0,r.jsx)(y.default,{src:(0,c.qR)(null==o?void 0:null===(n=o.customer)||void 0===n?void 0:n.profileImage),alt:null==o?void 0:null===(i=o.customer)||void 0===i?void 0:i.name,className:"size-8 rounded-full",width:32,height:32}):(0,r.jsx)("div",{className:"flex size-9 items-center justify-center rounded-full bg-muted",children:(0,r.jsx)(x.Z,{size:"16",variant:"Bold",className:"text-secondary-text"})}),(0,r.jsx)("span",{children:null==o?void 0:null===(s=o.customer)||void 0===s?void 0:s.name})]})}},{id:"contact_number",header:a("Contact Number"),cell:e=>{var t;let{row:n}=e,i=null==n?void 0:n.original,s=(0,b.h)((0,c.Fg)(null==i?void 0:null===(t=i.customer)||void 0===t?void 0:t.phone));return(0,r.jsx)("span",{className:"block min-w-32 font-normal text-secondary-text",children:s.formatInternational()})}},{id:"email",header:a("Email"),cell:e=>{let{row:t}=e,n=null==t?void 0:t.original;return(0,r.jsx)("span",{className:"font-normal text-secondary-text",children:null==n?void 0:n.email})}},{id:"totalBonus",header:a("Total bonus"),cell:e=>{let{row:t}=e,n=null==t?void 0:t.original;return(0,r.jsx)("span",{className:"font-normal text-secondary-text",children:w.format(null==n?void 0:n.totalBonus)})}}]})}let j=new c.F;function C(){var e;let{referral:t}=(0,d.T)(),n=(0,m.useSearchParams)(),{auth:g,isLoading:x}=(0,l.a)(),[b,y]=v.useState(""),w=(0,m.usePathname)(),C=(0,m.useRouter)(),{data:R,isLoading:S}=(0,u.d)("/customers/referred-users?".concat(n.toString())),{t:A}=(0,p.$G)();return(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 p-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row",children:[(0,r.jsxs)("div",{className:"inline-flex w-full items-center gap-4 rounded-xl border border-border bg-background p-6 md:w-1/3",children:[(0,r.jsx)("div",{className:"flex size-14 items-center justify-center rounded-full bg-muted",children:(0,r.jsx)(h.Z,{size:32})}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("h1",{className:"text-[32px] font-semibold leading-8",children:j.format(Number(null==t?void 0:t.bonusAmount))}),(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:A("Total Earnings")})]})]}),(0,r.jsxs)("div",{className:"w-full rounded-xl border border-border bg-background px-4 py-0",children:[(0,r.jsx)("div",{className:"py-6 hover:no-underline",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:A("Refer a friend")})}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 border-t border-divider px-1 py-4 sm:flex-row",children:[(0,r.jsx)(o.I,{value:x?A("Loading..."):null==g?void 0:g.getReferralLink(),className:"h-10",readOnly:!0,disabled:!0}),(0,r.jsxs)(s.z,{type:"button",onClick:()=>(0,c.Fp)(null==g?void 0:g.getReferralLink()),className:"w-full rounded-lg sm:max-w-[302px]",children:[(0,r.jsx)(f.Z,{}),(0,r.jsx)("span",{children:A("Copy link")})]})]})]})]}),(0,r.jsxs)(a.Zb,{className:"rounded-xl",children:[(0,r.jsxs)(a.Ol,{className:"flex items-start py-4 sm:flex-row sm:items-center",children:[(0,r.jsx)(a.ll,{className:"flex-1 text-base font-medium leading-[22px]",children:A("Referrals")}),(0,r.jsx)(i.R,{value:b,onChange:e=>{e.preventDefault();let t=(0,c.w4)(e.target.value);y(e.target.value),C.replace("".concat(w,"?").concat(t.toString()))},iconPlacement:"end",placeholder:A("Search..."),className:"h-10 rounded-lg",containerClass:"w-full sm:w-[280px]"})]}),(0,r.jsx)(a.aY,{className:"border-t border-divider py-4",children:(0,r.jsx)(N,{referralUsers:null==R?void 0:null===(e=R.data)||void 0===e?void 0:e.referralUsers,isLoading:S})})]})]})}},25833:function(e,t,n){n.d(t,{Z:function(){return g}});var r=n(57437),i=n(94508),s=n(71594),a=n(24525),o=n(73490),l=n(36887),d=n(64394),u=n(61756),c=n(99376),h=n(4751),f=n(2265),m=n(43949),v=n(62869),p=n(73578);function g(e){let{data:t,isLoading:n=!1,structure:g,sorting:x,setSorting:b,padding:y=!1,className:w,onRefresh:N,pagination:j}=e,C=(0,f.useMemo)(()=>g,[g]),R=(0,c.useRouter)(),S=(0,c.usePathname)(),A=(0,c.useSearchParams)(),{t:k}=(0,m.$G)(),P=(0,s.b7)({data:t||[],columns:C,state:{sorting:x,onRefresh:N},onSortingChange:b,getCoreRowModel:(0,a.sC)(),getSortedRowModel:(0,a.tj)(),debugTable:!1});return n?(0,r.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,r.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:k("Loading...")})}):(null==t?void 0:t.length)?(0,r.jsxs)("div",{className:(0,i.ZP)("".concat(y?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),w),children:[(0,r.jsxs)(p.iA,{children:[(0,r.jsx)(p.xD,{children:P.getHeaderGroups().map(e=>(0,r.jsx)(p.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,n,a,o;return(0,r.jsx)(p.ss,{className:(0,i.ZP)("",null==e?void 0:null===(a=e.column)||void 0===a?void 0:null===(n=a.columnDef)||void 0===n?void 0:null===(t=n.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,r.jsxs)(v.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[k((0,s.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(o=({asc:(0,r.jsx)(l.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,r.jsx)(l.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==o?o:(0,r.jsx)(l.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,r.jsx)(p.RM,{children:P.getRowModel().rows.map(e=>(0,r.jsx)(p.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,r.jsx)(p.pj,{className:"py-3 text-sm font-semibold",children:(0,s.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),j&&j.total>10&&(0,r.jsx)("div",{className:"pb-2 pt-6",children:(0,r.jsx)(h.Z,{showTotal:(e,t)=>k("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==j?void 0:j.page,total:null==j?void 0:j.total,pageSize:null==j?void 0:j.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(A);t.set("page",e.toString()),R.push("".concat(S,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,r.jsx)("a",{...e,children:(0,r.jsx)(d.Z,{size:"18"})}),nextIcon:e=>(0,r.jsx)("a",{...e,children:(0,r.jsx)(u.Z,{size:"18"})})})})]}):(0,r.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,r.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,r.jsx)(o.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),k("No data found!")]})})}},80114:function(e,t,n){n.d(t,{default:function(){return o}});var r=n(57437),i=n(85487),s=n(94508),a=n(43949);function o(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,r.jsx)("div",{className:(0,s.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,r.jsx)(i.Loader,{title:n("Loading..."),className:"text-foreground"})})}},85487:function(e,t,n){n.d(t,{Loader:function(){return a}});var r=n(57437),i=n(94508),s=n(43949);function a(e){let{title:t="Loading...",className:n}=e,{t:a}=(0,s.$G)();return(0,r.jsxs)("div",{className:(0,i.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-inherit",children:a(t)})]})}},85539:function(e,t,n){n.d(t,{R:function(){return o}});var r=n(57437);n(2265);var i=n(95186),s=n(94508),a=n(48674);function o(e){let{iconPlacement:t="start",className:n,containerClass:o,...l}=e;return(0,r.jsxs)("div",{className:(0,s.ZP)("relative flex items-center",o),children:[(0,r.jsx)(a.Z,{size:"20",className:(0,s.ZP)("absolute top-1/2 -translate-y-1/2","end"===t?"right-2.5":"left-2.5")}),(0,r.jsx)(i.I,{type:"text",className:(0,s.ZP)("h-10","end"===t?"pr-10":"pl-10",n),...l})]})}},62869:function(e,t,n){n.d(t,{d:function(){return l},z:function(){return d}});var r=n(57437),i=n(37053),s=n(90535),a=n(2265),o=n(94508);let l=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:n,variant:s,size:a,asChild:d=!1,...u}=e,c=d?i.g7:"button";return(0,r.jsx)(c,{className:(0,o.ZP)(l({variant:s,size:a,className:n})),ref:t,...u})});d.displayName="Button"},66070:function(e,t,n){n.d(t,{Ol:function(){return o},SZ:function(){return d},Zb:function(){return a},aY:function(){return u},eW:function(){return c},ll:function(){return l}});var r=n(57437),i=n(2265),s=n(94508);let a=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...i})});a.displayName="Card";let o=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.ZP)("flex flex-col space-y-1.5 p-6",n),...i})});o.displayName="CardHeader";let l=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("h3",{ref:t,className:(0,s.ZP)("text-2xl font-semibold leading-none tracking-tight",n),...i})});l.displayName="CardTitle";let d=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("p",{ref:t,className:(0,s.ZP)("text-sm text-muted-foreground",n),...i})});d.displayName="CardDescription";let u=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.ZP)("p-6 pt-0",n),...i})});u.displayName="CardContent";let c=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.ZP)("flex items-center p-6 pt-0",n),...i})});c.displayName="CardFooter"},95186:function(e,t,n){n.d(t,{I:function(){return a}});var r=n(57437),i=n(2265),s=n(94508);let a=i.forwardRef((e,t)=>{let{className:n,type:i,...a}=e;return(0,r.jsx)("input",{type:i,className:(0,s.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",n),ref:t,...a})});a.displayName="Input"},73578:function(e,t,n){n.d(t,{RM:function(){return l},SC:function(){return d},iA:function(){return a},pj:function(){return c},ss:function(){return u},xD:function(){return o}});var r=n(57437),i=n(2265),s=n(94508);let a=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,s.ZP)("w-full caption-bottom text-sm",n),...i})})});a.displayName="Table";let o=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("thead",{ref:t,className:(0,s.ZP)("",n),...i})});o.displayName="TableHeader";let l=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,s.ZP)("[&_tr:last-child]:border-0",n),...i})});l.displayName="TableBody",i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,s.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",n),...i})}).displayName="TableFooter";let d=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("tr",{ref:t,className:(0,s.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",n),...i})});d.displayName="TableRow";let u=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("th",{ref:t,className:(0,s.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",n),...i})});u.displayName="TableHead";let c=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("td",{ref:t,className:(0,s.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",n),...i})});c.displayName="TableCell",i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)("caption",{ref:t,className:(0,s.ZP)("mt-4 text-sm text-muted-foreground",n),...i})}).displayName="TableCaption"},17062:function(e,t,n){n.d(t,{Z:function(){return v},O:function(){return m}});var r=n(57437),i=n(80114);n(83079);var s=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=n(31117),o=n(79981),l=n(78040),d=n(83130);class u{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var c=n(99376),h=n(2265);let f=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),m=()=>h.useContext(f);function v(e){let{children:t}=e,[n,m]=h.useState("Desktop"),[v,p]=h.useState(!1),[g,x]=h.useState(),{data:b,isLoading:y,error:w,mutate:N}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:j,isLoading:C}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:R,isLoading:S}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),A=(0,c.useRouter)(),k=(0,c.usePathname)();h.useEffect(()=>{(async()=>{m((await s()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;m(e<768?"Mobile":e<1024?"Tablet":"Desktop"),p(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");x(new u(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{w&&!l.sp.includes(k)&&A.push("/signin")},[w]);let P=h.useMemo(()=>{var e,t,r;return{isAuthenticate:!!(null==b?void 0:null===(e=b.data)||void 0===e?void 0:e.login),auth:(null==b?void 0:null===(t=b.data)||void 0===t?void 0:t.user)?new d.n(null==b?void 0:null===(r=b.data)||void 0===r?void 0:r.user):null,isLoading:y,deviceLocation:g,refreshAuth:()=>N(b),isExpanded:v,device:n,setIsExpanded:p,branding:null==j?void 0:j.data,googleAnalytics:(null==R?void 0:R.data)?{active:null==R?void 0:R.data.active,apiKey:null==R?void 0:R.data.apiKey}:{active:!1,apiKey:""}}},[b,g,v,n]),Z=!y&&!C&&!S;return(0,r.jsx)(f.Provider,{value:P,children:Z?t:(0,r.jsx)(i.default,{})})}},3612:function(e,t,n){n.d(t,{a:function(){return i}});var r=n(17062);let i=()=>{let e=(0,r.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},21251:function(e,t,n){n.d(t,{T:function(){return i}});var r=n(17062);let i=()=>{let{branding:e}=(0,r.O)();return e}},31117:function(e,t,n){n.d(t,{d:function(){return s}});var r=n(79981),i=n(85323);let s=(e,t)=>(0,i.ZP)(e||null,e=>r.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,n){var r=n(78040),i=n(83464);t.Z=i.default.create({baseURL:r.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,n){n.d(t,{rH:function(){return r},sp:function(){return i}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},i=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){n.d(t,{F:function(){return u},Fg:function(){return f},Fp:function(){return d},Qp:function(){return h},ZP:function(){return o},fl:function(){return l},qR:function(){return c},w4:function(){return m}});var r=n(78040),i=n(61994),s=n(14438),a=n(53335);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,i.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>s.toast.success("Copied to clipboard!")).catch(()=>{s.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let i;let s=void 0===t?this.currencyCode:t;try{i=new Intl.NumberFormat("en-US",{style:"currency",currency:s,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){i=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let a=null!==(r=null===(n=i.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:s,o=i.format(e),l=o.substring(a.length).trim();return{currencyCode:s,currencySymbol:a,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let c=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",h=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",m=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",i=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?i.set(r,e):i.delete(r),i}},74539:function(e,t,n){n.d(t,{k:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){n.d(t,{n:function(){return l}});class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var i=n(84937);class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=n(66419),o=n(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new i.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new s(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new r(e.agent):void 0}}},84937:function(e,t,n){n.d(t,{O:function(){return i}});var r=n(74539);class i{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new r.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){n.d(t,{u:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}}]);