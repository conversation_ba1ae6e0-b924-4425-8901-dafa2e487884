"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[71489],{71489:function(e,t,r){r.r(t),r.d(t,{default:function(){return ea}});var n=r(57437),a=r(41709),s=r(85487),i=r(25318),l=r(3697),o=r(41062),d=r(16831),c=r(35974),u=r(62869),m=r(84190),f=r(6512),x=r(70569),p=r(31117),h=r(48358),v=r(94508),g=r(1735),j=r(19571),b=r(4677),y=r(80093),N=r(83504),w=r(43271),_=r(8400),z=r(61703),Z=r(66574),P=r(27648),C=r(99376),k=r(43949),S=r(14438);function R(e){var t,r,R,I;let{formData:F}=e,{t:V}=(0,k.$G)(),E=(0,C.useSearchParams)(),L=(0,C.useRouter)(),{wallets:Y,getWalletByCurrencyCode:D}=(0,h.r)(),{data:J,isLoading:G}=(0,p.d)("/transactions/trx/".concat(E.get("trxId")));if(G)return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.Loader,{})});let T=e=>{S.toast.promise((0,x.y)("".concat(e)),{loading:V("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return e.message},error:e=>e.message})},B=null==J?void 0:J.data,$=null==B?void 0:B.status,A=(null==B?void 0:B.from)?JSON.parse(B.from):null,M=(null==B?void 0:B.metaData)?JSON.parse(B.metaData):null,U=D(Y,A.currency),O=new v.F(null==A?void 0:A.currency),W=(0,Z.h)((0,v.Fg)(null!==(R=F.phone_number)&&void 0!==R?R:""));return(0,n.jsxs)("div",{className:"mx-auto max-w-3xl",children:[(0,n.jsxs)("h2",{className:"mb-1 flex items-center justify-center gap-2 text-2xl font-semibold text-foreground",children:[(0,n.jsxs)(a.J,{condition:"pending"===$,children:[(0,n.jsx)(g.Z,{variant:"Bold",className:"size-8 text-warning"}),(0,n.jsx)("span",{children:V("Payment pending")})]}),(0,n.jsxs)(a.J,{condition:"completed"===$,children:[(0,n.jsx)(j.Z,{size:"32",color:"#13A10E",variant:"Bold"}),(0,n.jsx)("span",{children:V("Payment successful")})]}),(0,n.jsxs)(a.J,{condition:"failed"===$,children:[(0,n.jsx)(b.Z,{variant:"Bold",className:"size-8 text-destructive"}),(0,n.jsx)("span",{children:V("Payment failed")})]})]}),(0,n.jsx)(f.Z,{orientation:"horizontal",className:"my-7"}),(0,n.jsxs)(i.Y,{groupName:V("Meter details"),children:[(0,n.jsx)(i.r,{title:V("Phone number"),value:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(o.W,{countryCode:W.country}),W.formatInternational()]})}),(0,n.jsx)(i.r,{title:V("Electricity name"),value:"Electricity 1"}),(0,n.jsx)(i.r,{title:V("Meter type"),value:(0,n.jsx)(c.C,{className:"bg-primary text-primary-foreground",children:F.meter_type})}),(0,n.jsx)(i.r,{title:V("Meter number"),value:F.meter_number})]}),(0,n.jsx)(f.Z,{className:"my-8"}),(0,n.jsxs)(i.Y,{groupName:V("Payment details"),children:[(0,n.jsx)(i.r,{title:"".concat(null==M?void 0:M.billerName," ").concat(V("will get")),value:O.formatVC(null==B?void 0:B.amount)}),(0,n.jsx)(i.r,{title:V("Service charge"),value:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.J,{condition:(null==B?void 0:B.fee)===0,children:(0,n.jsx)(c.C,{className:"bg-success text-success-foreground",children:V("Free")})}),(0,n.jsx)(a.J,{condition:(null==B?void 0:B.fee)!==0,children:O.formatVC(null==B?void 0:B.fee)})]})}),(0,n.jsx)(i.r,{title:V("Total"),value:O.formatVC(null==B?void 0:B.total)})]}),(0,n.jsx)(f.Z,{orientation:"horizontal",className:"my-7"}),(0,n.jsx)(l.T,{id:null==M?void 0:M.transactionId,className:"mb-4 text-sm sm:text-base"}),(0,n.jsxs)("div",{className:"mb-8 space-y-4",children:[(0,n.jsx)("h5",{className:"text-sm font-medium sm:text-base",children:V("New balance")}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(d.qE,{children:[(0,n.jsx)(d.F$,{src:null==U?void 0:U.logo}),(0,n.jsx)(d.Q5,{className:"bg-important font-bold text-important-foreground",children:null==U?void 0:null===(t=U.currency)||void 0===t?void 0:t.code})]}),(0,n.jsx)("span",{className:"text-sm font-bold",children:null==U?void 0:null===(r=U.currency)||void 0===r?void 0:r.code})]}),(0,n.jsx)("p",{className:"font-medium",children:O.formatVC(Number(null!==(I=null==U?void 0:U.balance)&&void 0!==I?I:0))})]})]}),(0,n.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,n.jsxs)(u.z,{variant:"outline",type:"button",className:"w-full md:w-auto",children:[(0,n.jsx)(y.Z,{size:16}),(0,n.jsx)("span",{children:V("Download Receipt")})]}),(0,n.jsxs)("div",{className:"flex w-full flex-wrap gap-4 sm:flex-nowrap md:w-auto md:justify-end",children:[(0,n.jsxs)(m.h_,{children:[(0,n.jsxs)(m.$F,{className:(0,v.ZP)("flex w-full items-center space-x-1.5 md:w-fit",(0,u.d)({variant:"outline"})),children:[(0,n.jsx)("span",{children:V("Menu")}),(0,n.jsx)(N.Z,{size:16})]}),(0,n.jsxs)(m.AW,{align:"start",className:"m-0",children:[(0,n.jsxs)(m.Xi,{onSelect:()=>(0,v.Fp)(null==M?void 0:M.transactionId),className:"flex items-center gap-2 text-sm font-medium",children:[(0,n.jsx)(w.Z,{size:"20",className:"text-primary",variant:"Outline"}),V("Copy transaction ID")]}),(0,n.jsxs)(m.Xi,{onSelect:()=>T(null==B?void 0:B.id),className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[(0,n.jsx)(_.Z,{size:"20",variant:"Outline"}),V("Bookmark receipt")]}),(0,n.jsx)(m.VD,{}),(0,n.jsxs)(m.Xi,{onSelect:()=>L.push("/services/electricity-bill"),className:"flex items-center gap-2 text-sm font-medium",children:[(0,n.jsx)(z.Z,{size:"20",variant:"Outline"}),V("Pay bill again")]})]})]}),(0,n.jsx)(u.z,{type:"button",className:"w-full md:max-w-48",asChild:!0,children:(0,n.jsx)(P.default,{href:"/services",children:V("Go to dashboard")})})]})]})]})}var I=r(23518),F=r(79981),V=r(85323),E=r(59532);function L(e){let{providerName:t,logo:r,meterNumber:s,meterType:i}=e;return(0,n.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,n.jsxs)(d.qE,{className:"border-2 border-border-primary",children:[(0,n.jsx)(d.F$,{src:r,alt:t}),(0,n.jsx)(d.Q5,{children:(0,E.v)(t)})]}),(0,n.jsxs)("div",{className:"flex-1 text-foreground",children:[(0,n.jsx)("h6",{className:"text-sm font-normal",children:t}),(0,n.jsx)(a.J,{condition:!!s,children:(0,n.jsx)("p",{className:"text-xs font-normal text-secondary-text",children:s})}),(0,n.jsx)(a.J,{condition:!!i,children:(0,n.jsx)("p",{className:"text-xs font-normal text-secondary-text",children:i})})]})]})}function Y(e){var t,r;let{onSelect:i}=e,{t:l}=(0,k.$G)(),{data:o,isLoading:d,error:c}=(0,V.ZP)("/services/utility/billers",e=>(0,F.Z)(e));return c&&S.toast.error(c.message),(0,n.jsx)(I.e8,{children:(0,n.jsxs)(I.fu,{children:[(0,n.jsx)(a.J,{condition:d,children:(0,n.jsx)("div",{className:"w-full px-4 py-2.5",children:(0,n.jsx)(s.Loader,{title:l("Loading...")})})}),(0,n.jsx)(a.J,{condition:!d&&(null==o?void 0:null===(t=o.data)||void 0===t?void 0:t.length)!==0,children:null==o?void 0:null===(r=o.data)||void 0===r?void 0:r.map(e=>(0,n.jsx)(I.di,{value:e.name,onSelect:()=>i(e),children:(0,n.jsx)(L,{providerName:e.name,logo:""})},e.id))})]})})}var D=r(15681),J=r(95186),G=r(57054),T=r(74991),B=r(90433),$=r(22291),A=r(2265);function M(e){let{form:t,onNext:r,meterProvider:a,setMeterProvider:s}=e,[i,l]=A.useState(!1),{t:o}=(0,k.$G)();return(0,n.jsxs)("div",{className:"flex w-full flex-col gap-4 md:gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"mb-4 font-semibold",children:o("Electricity name")}),(0,n.jsx)(D.Wi,{name:"meter_provider",control:t.control,render:e=>{let{field:t}=e;return(0,n.jsxs)(D.xJ,{children:[(0,n.jsx)(D.NI,{children:(0,n.jsxs)(G.J2,{open:i,onOpenChange:l,children:[(0,n.jsx)(G.xo,{className:"flex h-12 w-full items-center justify-between rounded-md border border-input bg-secondary px-3",children:t.value?(0,n.jsx)("span",{children:null==a?void 0:a.name}):(0,n.jsx)("span",{className:"font-normal text-secondary-text",children:o("Write Provider name")})}),(0,n.jsx)(G.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",children:(0,n.jsxs)(I.mY,{children:[(0,n.jsx)(I.sZ,{}),(0,n.jsx)(Y,{onSelect:e=>{t.onChange("".concat(null==e?void 0:e.id)),s(e),l(!1)}})]})})]})}),(0,n.jsx)(D.zG,{})]})}})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"mb-4 font-semibold",children:o("Meter Type")}),(0,n.jsx)(D.Wi,{name:"meter_type",control:t.control,render:e=>{let{field:t}=e;return(0,n.jsxs)(D.xJ,{children:[(0,n.jsx)(D.NI,{children:(0,n.jsxs)(T.E,{defaultValue:t.value,className:"flex w-full max-w-[500px] flex-col items-center gap-4 sm:flex-row",onValueChange:t.onChange,children:[(0,n.jsxs)(D.xJ,{"data-active":"prepaid"===t.value,className:"relative flex h-[52px] w-full items-center rounded-xl border-2 border-border bg-muted px-3 text-sm font-semibold leading-5 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,n.jsx)("span",{children:o("Prepaid")}),(0,n.jsx)(D.NI,{children:(0,n.jsx)(T.m,{value:"prepaid",className:"absolute inset-0 left-0 top-0 z-10 h-full w-full opacity-0"})})]}),(0,n.jsxs)(D.xJ,{"data-active":"postpaid"===t.value,className:"relative flex h-[52px] w-full items-center rounded-xl border-2 border-border bg-muted px-3 text-sm font-semibold leading-5 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,n.jsx)("span",{children:o("Postpaid")}),(0,n.jsx)(D.NI,{children:(0,n.jsx)(T.m,{value:"postpaid",className:"absolute inset-0 left-0 top-0 z-10 h-full w-full opacity-0"})})]})]})}),(0,n.jsx)(D.zG,{})]})}})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"mb-4 font-semibold",children:o("Meter Number")}),(0,n.jsx)(D.Wi,{name:"meter_number",control:t.control,render:e=>{let{field:t}=e;return(0,n.jsxs)(D.xJ,{children:[(0,n.jsx)(D.NI,{children:(0,n.jsx)(J.I,{placeholder:o("Enter meter number"),...t})}),(0,n.jsx)(D.zG,{})]})}})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,n.jsx)(u.z,{variant:"outline",type:"button",asChild:!0,className:"flex w-[102px] gap-0.5 rounded-lg px-4 py-2 text-base font-medium leading-[22x]",children:(0,n.jsxs)(P.default,{href:"/services",children:[(0,n.jsx)(B.Z,{size:"24"}),o("Back")]})}),(0,n.jsxs)(u.z,{type:"button",onClick:r,className:"flex w-[200px] gap-0.5 rounded-lg px-4 py-2 text-base font-medium leading-[22x]",children:[o("Next"),(0,n.jsx)($.Z,{size:"16"})]})]})]})}var U=r(45932);function O(e){let{form:t,onNext:r,onBack:a}=e,{t:s}=(0,k.$G)();return(0,n.jsxs)("div",{className:"flex w-full flex-col gap-4 md:gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"mb-4",children:s("Select wallet")}),(0,n.jsx)(D.Wi,{name:"sender_wallet_id",control:t.control,render:e=>{let{field:t}=e;return(0,n.jsxs)(D.xJ,{children:[(0,n.jsx)(D.NI,{children:(0,n.jsx)(U.R,{...t,value:t.value||""})}),(0,n.jsx)(D.zG,{})]})}})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"mb-4",children:s("How much is the bill?")}),(0,n.jsx)(D.Wi,{control:t.control,name:"bill_amount",render:e=>{let{field:t}=e;return(0,n.jsxs)(D.xJ,{children:[(0,n.jsx)(D.NI,{children:(0,n.jsx)(J.I,{placeholder:s("Enter payment amount"),...t,type:"number"})}),(0,n.jsx)(D.zG,{})]})}})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,n.jsxs)(u.z,{variant:"outline",type:"button",onClick:a,children:[(0,n.jsx)(B.Z,{size:15}),s("Back")]}),(0,n.jsxs)(u.z,{type:"button",onClick:()=>{let e=0;t.getValues("sender_wallet_id")||(t.setError("sender_wallet_id",{message:"Please select a wallet.",type:"custom"},{shouldFocus:!0}),e+=1),t.getValues("bill_amount")||(t.setError("bill_amount",{message:"Amount is required.",type:"custom"},{shouldFocus:!0}),e+=1),e||r()},children:[s("Next"),(0,n.jsx)($.Z,{size:15})]})]})]})}var W=r(97751);async function Q(e){try{let t=await F.Z.post("/services/utility/preview",e);return(0,W.B)(t)}catch(e){return(0,W.D)(e)}}async function q(e){try{let t=await F.Z.post("/services/utility/create",e);return(0,W.B)(t)}catch(e){return(0,W.D)(e)}}function H(e){var t,r,l,o,m,x,p,g;let{formData:j,isLoading:b,onBack:y,onNext:N,meterProvider:w}=e,[_,z]=A.useTransition(),[Z,P]=A.useState({}),{wallets:C,getWalletByCurrencyCode:R}=(0,h.r)(),{t:I}=(0,k.$G)(),F=R(C,j.sender_wallet_id);A.useEffect(()=>{V()},[]);let V=async()=>{z(async()=>{let e={meterNumber:j.meter_number,amount:Number(j.bill_amount),currencyCode:j.sender_wallet_id,billerId:Number(j.meter_provider)},t=await Q(e);t.status?P(null==t?void 0:t.data):S.toast.error(t.message)})};return(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("h2",{className:"mb-8",children:I("Confirm and proceed")}),(0,n.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,n.jsx)("h5",{className:"text-sm font-medium sm:text-base",children:I("Selected wallet")}),(0,n.jsxs)("div",{className:"flex flex-row items-center gap-2.5",children:[(0,n.jsxs)(d.qE,{className:"size-8",children:[(0,n.jsx)(d.F$,{src:null==F?void 0:F.logo}),(0,n.jsx)(d.Q5,{className:"bg-important text-xs font-bold text-important-foreground",children:null==F?void 0:null===(t=F.currency)||void 0===t?void 0:t.code})]}),(0,n.jsx)("h6",{className:"font-bold",children:null==F?void 0:null===(r=F.currency)||void 0===r?void 0:r.code})]})]}),(0,n.jsx)(f.Z,{className:"my-8"}),_?(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsx)(s.Loader,{})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(i.Y,{groupName:I("Meter details"),children:[(0,n.jsx)(i.r,{title:I("Electricity name"),value:null==Z?void 0:null===(l=Z.receiver)||void 0===l?void 0:l.name}),(0,n.jsx)(i.r,{title:I("Meter type"),value:(0,n.jsx)(c.C,{className:"bg-primary text-primary-foreground",children:(0,v.fl)(null==j?void 0:j.meter_type)})}),(0,n.jsx)(i.r,{title:I("Meter number"),value:null==j?void 0:j.meter_number})]}),(0,n.jsx)(f.Z,{className:"my-8"}),(0,n.jsxs)(i.Y,{groupName:I("Payment details"),children:[(0,n.jsx)(i.r,{title:I("Amount"),value:"".concat(null==Z?void 0:null===(o=Z.sender)||void 0===o?void 0:o.sendingAmount," ").concat(j.sender_wallet_id)}),(0,n.jsx)(i.r,{title:I("Service charge"),value:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.J,{condition:(null==w?void 0:w.localTransactionFee)===0,children:(0,n.jsx)(c.C,{className:"bg-success text-success-foreground",children:I("Free")})}),(0,n.jsxs)(a.J,{condition:(null==w?void 0:w.localTransactionFee)!==0,children:[null==Z?void 0:null===(m=Z.sender)||void 0===m?void 0:m.fee," ",j.sender_wallet_id]})]})}),(0,n.jsx)(i.r,{title:I("Total"),value:"".concat(Number(null==Z?void 0:null===(x=Z.sender)||void 0===x?void 0:x.receivingAmount)," ").concat(j.sender_wallet_id)}),(0,n.jsx)(i.r,{title:I("You will get"),value:"".concat(Number(null==Z?void 0:null===(p=Z.receiver)||void 0===p?void 0:p.fxRate)," ").concat(null==Z?void 0:null===(g=Z.receiver)||void 0===g?void 0:g.currencyCode)})]})]}),(0,n.jsx)(f.Z,{className:"my-8"}),(0,n.jsxs)("div",{className:"mt-8 flex flex-1 justify-between gap-4",children:[(0,n.jsxs)(u.z,{variant:"outline",onClick:y,children:[(0,n.jsx)(B.Z,{size:17}),I("Back")]}),(0,n.jsxs)(u.z,{onClick:N,disabled:b,children:[(0,n.jsxs)(a.J,{condition:!b,children:[I("Pay bill"),(0,n.jsx)($.Z,{size:17})]}),(0,n.jsx)(a.J,{condition:b,children:(0,n.jsx)(s.Loader,{title:I("Processing"),className:"text-primary-foreground"})})]})]})]})}var X=r(31026),K=r(70880),ee=r(13590),et=r(29501),er=r(31229);let en=er.z.object({meter_type:er.z.enum(["prepaid","postpaid"]).default("prepaid"),meter_provider:er.z.string().min(1,"Provider is required."),meter_number:er.z.string().min(1,"Meter number is required."),sender_wallet_id:er.z.string().optional(),bill_amount:er.z.string().optional(),phone_number:er.z.string().optional()});function ea(){let{t:e}=(0,k.$G)(),[t,r]=A.useState("meter_details"),[a,s]=A.useState(null),[i,l]=A.useTransition(),[o,d]=A.useState([{id:"meter_details",value:"meter_details",title:e("Meter Details"),complete:!1},{id:"payment_details",value:"payment_details",title:e("Payment Details"),complete:!1},{id:"review",value:"review",title:e("Review"),complete:!1},{id:"finish",value:"finish",title:e("Finish"),complete:!1}]),c=(0,et.cI)({resolver:(0,ee.F)(en),mode:"all",defaultValues:{meter_provider:"",meter_type:"prepaid",meter_number:"",sender_wallet_id:"",bill_amount:"",phone_number:""}});A.useEffect(()=>{"meter_details"===t&&c.reset({meter_provider:c.getValues("meter_provider"),meter_type:c.getValues("meter_type"),meter_number:c.getValues("meter_number"),sender_wallet_id:c.getValues("sender_wallet_id"),bill_amount:c.getValues("bill_amount"),phone_number:c.getValues("phone_number")})},[t,c]);let u=e=>{d(t=>t.map(t=>t.id===e?{...t,complete:!0}:t))},m=()=>{let t=c.getValues();l(async()=>{let r={meterNumber:t.meter_number,amount:Number(t.bill_amount),currencyCode:t.sender_wallet_id,billerId:Number(t.meter_provider)},n=await q(r);n.status?S.toast.success(e(n.message)):S.toast.error(e(n.message))})};return(0,n.jsx)(X.Xg,{children:(0,n.jsx)(D.l0,{...c,children:(0,n.jsx)("form",{onSubmit:c.handleSubmit(m),children:(0,n.jsx)("div",{className:"w-full p-4 pb-10 md:p-12",children:(0,n.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,n.jsx)(K.R,{tabs:o,value:t,onTabChange:e=>r(e),children:(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsx)(K.Q,{value:"meter_details",children:(0,n.jsx)(M,{form:c,meterProvider:a,setMeterProvider:e=>s(e),onNext:c.handleSubmit((e,t)=>{null==t||t.preventDefault(),r("payment_details"),u("meter_details")})})}),(0,n.jsx)(K.Q,{value:"payment_details",children:(0,n.jsx)(O,{form:c,onNext:c.handleSubmit((e,t)=>{null==t||t.preventDefault(),r("review"),u("payment_details")}),onBack:()=>r("meter_details")})}),(0,n.jsx)(K.Q,{value:"review",children:(0,n.jsx)(H,{formData:c.getValues(),isLoading:i,meterProvider:a,onBack:()=>r("payment_details"),onNext:c.handleSubmit(m,()=>{S.toast.error(e("An error occurred. Please try again."))})})}),(0,n.jsx)(K.Q,{value:"finish",children:(0,n.jsx)(R,{formData:c.getValues()})})]})})})})})})})}},85487:function(e,t,r){r.d(t,{Loader:function(){return i}});var n=r(57437),a=r(94508),s=r(43949);function i(e){let{title:t="Loading...",className:r}=e,{t:i}=(0,s.$G)();return(0,n.jsxs)("div",{className:(0,a.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:i(t)})]})}},31026:function(e,t,r){r.d(t,{Xg:function(){return x},cI:function(){return f},jT:function(){return p}});var n=r(57437),a=r(62869),s=r(28152),i=r(94508),l=r(8400),o=r(90433),d=r(2265),c=r(43949);let u=d.createContext(null),m=()=>{let e=d.useContext(u);if(!e)throw Error("usePageLayout must be used within an PageLayoutCtx. Please ensure that your component is wrapped with an PageLayoutCtx.");return e};function f(e){let{className:t}=e,{t:r}=(0,c.$G)(),{setRightSidebar:s}=m();return(0,n.jsx)("div",{className:(0,i.ZP)("flex items-center justify-end md:mb-4 xl:hidden",t),children:(0,n.jsxs)(a.z,{onClick:()=>s(e=>!e),variant:"outline",size:"sm",type:"button",className:"text-sm",children:[(0,n.jsx)(l.Z,{size:"20"}),r("Bookmarks")]})})}function x(e){let{children:t}=e,[r,a]=d.useState(!1),{width:i}=(0,s.B)();d.useEffect(()=>{i>=1280&&a(!0)},[i]);let l=d.useMemo(()=>({width:i,rightSidebar:r,setRightSidebar:a}),[i,r]);return(0,n.jsx)(u.Provider,{value:l,children:t})}function p(e){let{children:t}=e,{t:r}=(0,c.$G)(),{width:s,rightSidebar:i,setRightSidebar:l}=m();return(0,n.jsxs)("div",{"data-expanded":s>=1280||s<1280&&i,className:"absolute inset-y-0 right-0 top-0 w-full max-w-96 translate-x-full bg-background-body p-6 transition-all duration-300 ease-in-out data-[expanded=true]:translate-x-0 xl:relative",children:[(0,n.jsxs)(a.z,{variant:"outline",size:"sm",type:"button",onClick:()=>l(!1),className:"mb-4 gap-[2px] bg-background text-sm hover:bg-background xl:hidden",children:[(0,n.jsx)(o.Z,{size:14}),r("Hide bookmarks")]}),t]})}},70880:function(e,t,r){r.d(t,{Q:function(){return c},R:function(){return d}});var n=r(57437),a=r(12339),s=r(94508),i=r(19571),l=r(2265),o=r(6512);function d(e){let{value:t="",tabs:r=[],children:d,onTabChange:c}=e,[u,m]=l.useState(0),f=r.filter(e=>void 0===e.isVisible||!0===e.isVisible),x=f.findIndex(e=>e.value===t),p=f.length;return l.useEffect(()=>{m((x+1)/p*100)},[x,p,t]),(0,n.jsxs)(a.mQ,{value:t,onValueChange:c,children:[(0,n.jsx)("div",{className:"hidden h-0.5 w-full bg-background-body md:flex",children:(0,n.jsx)(o.Z,{className:(0,s.ZP)("h-0.5 bg-primary transition-[width] duration-200"),style:{width:"".concat(u,"%")}})}),(0,n.jsx)(a.dr,{className:"hidden bg-transparent md:flex",children:f.map((e,t)=>(0,n.jsxs)(a.SP,{value:e.value,disabled:t>x,"data-complete":e.complete,className:"ring-none group h-8 justify-start rounded-lg border-none border-border px-3 text-sm font-normal leading-5 text-foreground shadow-none outline-none transition-all duration-200 hover:bg-accent hover:text-primary data-[state=active]:bg-transparent data-[complete=true]:text-primary data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:hover:bg-accent",children:[(0,n.jsx)(i.Z,{size:19,className:"mr-2 group-hover:text-primary",variant:e.complete?"Bold":"Linear"}),e.title]},e.value))}),d]})}function c(e){let{children:t,...r}=e;return(0,n.jsx)(a.nU,{...r,children:t})}},3697:function(e,t,r){r.d(t,{T:function(){return d}});var n=r(57437),a=r(62869),s=r(94508),i=r(43271),l=r(43949),o=r(14438);function d(e){let{id:t,className:r}=e,{t:d}=(0,l.$G)();return(0,n.jsxs)("div",{className:(0,s.ZP)("inline-flex w-full items-center gap-4",r),children:[(0,n.jsx)("div",{className:"flex-1",children:d("Transaction ID")}),(0,n.jsxs)("div",{className:"inline-flex items-center gap-4",children:[(0,n.jsx)("span",{children:t}),(0,n.jsx)(a.z,{type:"button",onClick:()=>{navigator.clipboard.writeText(t).then(()=>o.toast.success("Copied to clipboard!")).catch(()=>{o.toast.error("Failed to copy!")})},variant:"outline",size:"sm",children:(0,n.jsx)(i.Z,{size:"20"})})]})]})}},45932:function(e,t,r){r.d(t,{R:function(){return x}});var n=r(57437),a=r(41709),s=r(33145),i=r(43949);function l(e){let{walletId:t,logo:r,name:a,balance:l,selectedWallet:o,onSelect:d,id:c}=e,{t:u}=(0,i.$G)();return(0,n.jsxs)("label",{htmlFor:"wallet-".concat(t,"-").concat(c),"data-active":t===o,className:"relative flex w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 px-6 py-4 transition-all duration-300 ease-linear hover:border-transparent hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,n.jsx)("input",{type:"radio",id:"wallet-".concat(t,"-").concat(c),checked:t===o,onChange:()=>d(t),className:"absolute inset-0 left-0 top-0 z-10 cursor-pointer opacity-0"}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[r&&(0,n.jsx)(s.default,{src:r,alt:a,width:100,height:100,className:"size-8"}),(0,n.jsx)("h6",{className:"text-sm font-bold leading-5",children:a})]}),(0,n.jsxs)("div",{className:"mt-2.5",children:[(0,n.jsx)("p",{className:"text-xs font-normal leading-4 text-foreground",children:u("Your Balance")}),(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:Number(l).toFixed(2)})]})]})}var o=r(62869),d=r(93022),c=r(48358),u=r(66605),m=r(36887),f=r(2265);let x=(0,f.forwardRef)(function(e,t){var r;let{value:s,onChange:x,id:p}=e,{t:h}=(0,i.$G)(),[v,g]=f.useState(!1),{wallets:j,isLoading:b}=(0,c.r)(),y=f.useMemo(()=>j,[j]);return(f.useEffect(()=>{let e=y.find(e=>e.defaultStatus);e&&!s&&x(null==e?void 0:e.currency.code)},[y]),b)?(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:[(0,n.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,n.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,n.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"})]}):(0,n.jsxs)("div",{ref:t,id:p,children:[(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:null===(r=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;return t?e:e.slice(0,r)}(j,v))||void 0===r?void 0:r.map(e=>(null==e?void 0:e.currency.code)&&(0,n.jsx)(f.Fragment,{children:(0,n.jsx)(l,{walletId:null==e?void 0:e.currency.code,logo:e.logo,name:null==e?void 0:e.currency.code,balance:e.balance,selectedWallet:s,onSelect:x,id:p})},e.walletId))}),(0,n.jsx)(a.J,{condition:(null==j?void 0:j.length)>3,children:(0,n.jsx)("div",{className:"mt-2 flex justify-end",children:(0,n.jsxs)(o.z,{type:"button",variant:"link",onClick:()=>g(!v),className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[(0,n.jsx)("span",{className:"text-inherit",children:h(v?"Show less":"Show more")}),v?(0,n.jsx)(u.Z,{size:12}):(0,n.jsx)(m.Z,{size:12})]})})})]})})},41062:function(e,t,r){r.d(t,{W:function(){return i}});var n=r(57437),a=r(94508),s=r(33145);function i(e){let{countryCode:t,className:r,url:i}=e;return t||i?(0,n.jsx)(s.default,{src:null!=i?i:"https://flagcdn.com/".concat(null==t?void 0:t.toLowerCase(),".svg"),alt:t,width:20,height:16,loading:"lazy",className:(0,a.ZP)("rounded-[2px]",r)}):null}},35974:function(e,t,r){r.d(t,{C:function(){return l}});var n=r(57437),a=r(90535);r(2265);var s=r(94508);let i=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{className:(0,s.ZP)(i({variant:r}),t),...a})}},62869:function(e,t,r){r.d(t,{d:function(){return o},z:function(){return d}});var n=r(57437),a=r(37053),s=r(90535),i=r(2265),l=r(94508);let o=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:d=!1,...c}=e,u=d?a.g7:"button";return(0,n.jsx)(u,{className:(0,l.ZP)(o({variant:s,size:i,className:r})),ref:t,...c})});d.displayName="Button"},23518:function(e,t,r){r.d(t,{di:function(){return x},e8:function(){return c},fu:function(){return m},mY:function(){return o},rb:function(){return u},sZ:function(){return d},zz:function(){return f}});var n=r(57437),a=r(46343),s=r(73247),i=r(2265);r(26110);var l=r(94508);let o=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.mY,{ref:t,className:(0,l.ZP)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",r),...s})});o.displayName=a.mY.displayName;let d=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[(0,n.jsx)(s.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),(0,n.jsx)(a.mY.Input,{ref:t,className:(0,l.ZP)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",r),...i})]})});d.displayName=a.mY.Input.displayName;let c=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.mY.List,{ref:t,className:(0,l.ZP)("max-h-[300px] overflow-y-auto overflow-x-hidden",r),...s})});c.displayName=a.mY.List.displayName;let u=i.forwardRef((e,t)=>(0,n.jsx)(a.mY.Empty,{ref:t,className:"py-6 text-center text-sm",...e}));u.displayName=a.mY.Empty.displayName;let m=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.mY.Group,{ref:t,className:(0,l.ZP)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",r),...s})});m.displayName=a.mY.Group.displayName;let f=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.mY.Separator,{ref:t,className:(0,l.ZP)("-mx-1 h-px bg-border",r),...s})});f.displayName=a.mY.Separator.displayName;let x=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.mY.Item,{ref:t,className:(0,l.ZP)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",r),...s})});x.displayName=a.mY.Item.displayName},26110:function(e,t,r){r.d(t,{$N:function(){return p},Be:function(){return h},GG:function(){return u},Vq:function(){return o},cZ:function(){return f},fK:function(){return x},hg:function(){return d}});var n=r(57437),a=r(2265),s=r(49027),i=r(32489),l=r(94508);let o=s.fC,d=s.xz,c=s.h_,u=s.x8,m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.aV,{ref:t,className:(0,l.ZP)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...a})});m.displayName=s.aV.displayName;let f=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,n.jsxs)(c,{children:[(0,n.jsx)(m,{}),(0,n.jsxs)(s.VY,{ref:t,className:(0,l.ZP)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...o,children:[a,(0,n.jsxs)(s.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(i.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=s.VY.displayName;let x=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,l.ZP)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};x.displayName="DialogHeader";let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.Dx,{ref:t,className:(0,l.ZP)("text-lg font-semibold leading-none tracking-tight",r),...a})});p.displayName=s.Dx.displayName;let h=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.dk,{ref:t,className:(0,l.ZP)("text-sm text-muted-foreground",r),...a})});h.displayName=s.dk.displayName},15681:function(e,t,r){r.d(t,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return p},xJ:function(){return x},zG:function(){return v}});var n=r(57437),a=r(37053),s=r(2265),i=r(29501),l=r(26815),o=r(94508);let d=i.RV,c=s.createContext({}),u=e=>{let{...t}=e;return(0,n.jsx)(c.Provider,{value:{name:t.name},children:(0,n.jsx)(i.Qr,{...t})})},m=()=>{let e=s.useContext(c),t=s.useContext(f),{getFieldState:r,formState:n}=(0,i.Gc)(),a=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...a}},f=s.createContext({}),x=s.forwardRef((e,t)=>{let{className:r,...a}=e,i=s.useId();return(0,n.jsx)(f.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",r),...a})})});x.displayName="FormItem";let p=s.forwardRef((e,t)=>{let{className:r,required:a,...s}=e,{error:i,formItemId:d}=m();return(0,n.jsx)("span",{children:(0,n.jsx)(l.Z,{ref:t,className:(0,o.ZP)(i&&"text-base font-medium text-destructive",r),htmlFor:d,...s})})});p.displayName="FormLabel";let h=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:i,formDescriptionId:l,formMessageId:o}=m();return(0,n.jsx)(a.g7,{ref:t,id:i,"aria-describedby":s?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!s,...r})});h.displayName="FormControl",s.forwardRef((e,t)=>{let{className:r,...a}=e,{formDescriptionId:s}=m();return(0,n.jsx)("p",{ref:t,id:s,className:(0,o.ZP)("text-sm text-muted-foreground",r),...a})}).displayName="FormDescription";let v=s.forwardRef((e,t)=>{let{className:r,children:a,...s}=e,{error:i,formMessageId:l}=m(),d=i?String(null==i?void 0:i.message):a;return d?(0,n.jsx)("p",{ref:t,id:l,className:(0,o.ZP)("text-sm font-medium text-destructive",r),...s,children:d}):null});v.displayName="FormMessage"},95186:function(e,t,r){r.d(t,{I:function(){return i}});var n=r(57437),a=r(2265),s=r(94508);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,n.jsx)("input",{type:a,className:(0,s.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...i})});i.displayName="Input"},26815:function(e,t,r){var n=r(57437),a=r(6394),s=r(90535),i=r(2265),l=r(94508);let o=(0,s.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.f,{ref:t,className:(0,l.ZP)(o(),r),...s})});d.displayName=a.f.displayName,t.Z=d},57054:function(e,t,r){r.d(t,{J2:function(){return l},xo:function(){return o},yk:function(){return d}});var n=r(57437),a=r(2265),s=r(27312),i=r(94508);let l=s.fC,o=s.xz,d=a.forwardRef((e,t)=>{let{className:r,align:a="center",sideOffset:l=4,...o}=e;return(0,n.jsx)(s.h_,{children:(0,n.jsx)(s.VY,{ref:t,align:a,sideOffset:l,className:(0,i.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})})});d.displayName=s.VY.displayName},74991:function(e,t,r){r.d(t,{E:function(){return o},m:function(){return d}});var n=r(57437),a=r(2265),s=r(42325),i=r(40519),l=r(94508);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.fC,{className:(0,l.ZP)("grid gap-2",r),...a,ref:t})});o.displayName=s.fC.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.ck,{ref:t,className:(0,l.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...a,children:(0,n.jsx)(s.z$,{className:"flex items-center justify-center",children:(0,n.jsx)(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=s.ck.displayName},12339:function(e,t,r){r.d(t,{SP:function(){return d},dr:function(){return o},mQ:function(){return l},nU:function(){return c}});var n=r(57437),a=r(2265),s=r(20271),i=r(94508);let l=s.fC,o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.aV,{ref:t,className:(0,i.ZP)("inline-flex h-10 w-full items-center justify-center rounded-md bg-secondary p-1 text-muted-foreground",r),...a})});o.displayName=s.aV.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.xz,{ref:t,className:(0,i.ZP)("inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-semibold text-secondary-800 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite",r),...a})});d.displayName=s.xz.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.VY,{ref:t,className:(0,i.ZP)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...a})});c.displayName=s.VY.displayName},70569:function(e,t,r){r.d(t,{y:function(){return s}});var n=r(79981),a=r(97751);async function s(e,t){try{let r=await n.Z.put("".concat(null!=t?t:"/transactions/toggle-bookmark","/").concat(e),{id:e});return(0,a.B)(r)}catch(e){return(0,a.D)(e)}}},28152:function(e,t,r){r.d(t,{B:function(){return a}});var n=r(2265);let a=()=>{let[e,t]=n.useState(0),[r,a]=n.useState(0);function s(){window&&(t(window.innerWidth),a(window.innerHeight))}return n.useEffect(()=>(s(),window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)),[]),{width:e,height:r}}},31117:function(e,t,r){r.d(t,{d:function(){return s}});var n=r(79981),a=r(85323);let s=(e,t)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,r){var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){r.d(t,{F:function(){return c},Fg:function(){return f},Fp:function(){return d},Qp:function(){return m},ZP:function(){return l},fl:function(){return o},qR:function(){return u},w4:function(){return x}});var n=r(78040),a=r(61994),s=r(14438),i=r(53335);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,a.W)(t))}function o(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>s.toast.success("Copied to clipboard!")).catch(()=>{s.toast.error("Failed to copy!")})};class c{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let s=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:s,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:s,l=a.format(e),o=l.substring(i.length).trim();return{currencyCode:s,currencySymbol:i,formattedAmount:l,amountText:o}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",x=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}},59532:function(e,t,r){r.d(t,{v:function(){return n}});function n(e){if(!e)return"";let t=e.split(" ");return(t.length>2?t[0].length>3?t[0][0]+t[t.length-1][0]:t[1][0]+t[t.length-1][0]:2===t.length?t[0][0]+t[1][0]:t[0][0]).toUpperCase()}}}]);