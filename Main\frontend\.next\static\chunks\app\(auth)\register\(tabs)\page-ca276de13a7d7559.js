(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[63467],{30308:function(e,t,i){Promise.resolve().then(i.bind(i,41476))},15054:function(e,t,i){"use strict";i.d(t,{h:function(){return r}});var n=i(57437);function r(e){let{title:t,subTitle:i}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{className:"mb-2.5 text-sm text-secondary-text",children:i}),(0,n.jsx)("h1",{className:"text-[28px] font-medium leading-10 md:text-[32px]",children:t})]})}},41476:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return m}});var n=i(57437),r=i(15054),s=i(66424),a=i(37128);function o(e){let{className:t}=e;return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",className:t,children:[(0,n.jsx)("path",{d:"M16.0001 14.3157C12.0469 14.3157 8.84221 11.1111 8.84221 7.15787C8.84221 3.20469 12.0469 0 16.0001 0C19.9533 0 23.158 3.20469 23.158 7.15787C23.158 11.1111 19.9533 14.3157 16.0001 14.3157Z"}),(0,n.jsx)("path",{d:"M16 16C22.9762 16 28.6316 21.6554 28.6316 28.6316C28.6316 30.4919 27.1235 32 25.2632 32H6.73684C4.87653 32 3.3684 30.4919 3.3684 28.6316C3.36847 21.6554 9.02384 16 16 16Z"})]})}var l=i(62869),d=i(6512),c=i(21251),u=i(61756),h=i(27648),C=i(99376);i(2265);var v=i(43949);function m(){let{t:e}=(0,v.$G)(),{siteName:t,customerRegistration:i,merchantRegistration:l,agentRegistration:u}=(0,c.T)(),h=[{type:"customer",enabled:i,title:e("Customer"),description:e("Deposit, transfer, withdraw, invest, pay online, and exchange money easily."),icon:(0,n.jsx)("div",{className:"grid size-16 place-items-center rounded-full bg-primary-selected",children:(0,n.jsx)(o,{className:"fill-primary"})})},{type:"merchant",enabled:l,title:e("Merchant"),description:e("Accept payments online, request payments, and manage finances seamlessly."),icon:(0,n.jsx)("div",{className:"grid size-16 place-items-center rounded-full bg-[#EAF6FF]",children:(0,n.jsx)(a.Z,{className:"fill-[#09A7FF]"})})},{type:"agent",enabled:u,title:e("Agent"),description:e("Help customers deposit and withdraw money while earning commissions."),icon:(0,n.jsx)("div",{className:"grid size-16 place-items-center rounded-full bg-[#FFE9E9]",children:(0,n.jsx)(s.S,{})})}],C=h.filter(e=>e.enabled).length;return(0,n.jsxs)("div",{className:"container w-full max-w-[716px]",children:[(0,n.jsx)(r.h,{title:e("Create an account"),subTitle:e("Welcome to {{siteName}}, let's get start",{siteName:t})}),(0,n.jsx)("div",{className:"my-6 flex h-[5px] items-center",children:(0,n.jsx)(d.Z,{className:"bg-divider"})}),(0,n.jsx)("p",{className:"mb-4 text-base font-medium leading-[22px]",children:e(C>0?"I am a...":"Registration is currently disabled")}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:h.filter(e=>e.enabled).map(e=>(0,n.jsx)(f,{type:e.type,icon:e.icon,title:e.title,description:e.description},e.type))})]})}function f(e){let{type:t,icon:i,title:r,description:s}=e,a=(0,C.useSearchParams)();return(0,n.jsx)(h.default,{href:"/register/".concat(t,"?").concat(a.toString()),prefetch:!1,children:(0,n.jsxs)("div",{className:"group relative h-full rounded-xl px-6 py-4 shadow-defaultLite transition-shadow duration-200 hover:shadow-light-8",children:[(0,n.jsx)(l.z,{variant:"ghost",size:"icon",className:"rounded-4 invisible absolute right-3 top-3 h-8 w-8 p-1.5 group-hover:visible",children:(0,n.jsx)(u.Z,{size:"20"})}),(0,n.jsxs)("div",{className:"flex w-full flex-col items-center gap-4",children:[i,(0,n.jsx)("h5",{className:"text-base font-medium leading-[22px]",children:r}),(0,n.jsx)("p",{className:"text-center text-sm text-secondary-text",children:s})]})]})})}},80114:function(e,t,i){"use strict";i.d(t,{default:function(){return o}});var n=i(57437),r=i(85487),s=i(94508),a=i(43949);function o(e){let{className:t}=e,{t:i}=(0,a.$G)();return(0,n.jsx)("div",{className:(0,s.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,n.jsx)(r.Loader,{title:i("Loading..."),className:"text-foreground"})})}},85487:function(e,t,i){"use strict";i.d(t,{Loader:function(){return a}});var n=i(57437),r=i(94508),s=i(43949);function a(e){let{title:t="Loading...",className:i}=e,{t:a}=(0,s.$G)();return(0,n.jsxs)("div",{className:(0,r.ZP)("flex items-center gap-1 text-sm text-foreground",i),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:a(t)})]})}},66424:function(e,t,i){"use strict";i.d(t,{S:function(){return s}});var n=i(57437),r=i(94508);function s(e){let{className:t}=e;return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"37",height:"37",viewBox:"0 0 37 37",fill:"none",className:(0,r.ZP)("fill-[#E04242]",t),children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M28.1625 18.712L20.0806 13.7707C19.5225 13.4222 18.8592 13.2824 18.208 13.376C17.5568 13.4696 16.9597 13.7907 16.5224 14.2823L14.976 15.9878C14.8404 16.1373 14.6701 16.2511 14.4802 16.3191C14.2902 16.3872 14.0864 16.4074 13.8868 16.378C13.6872 16.3486 13.4978 16.2704 13.3356 16.1505C13.1733 16.0306 13.0431 15.8725 12.9564 15.6903C12.9233 15.6204 12.8903 15.5462 12.8574 15.4677L15.9665 11.7763C16.1683 11.5269 16.425 11.3276 16.7165 11.1939C17.0081 11.0601 17.3266 10.9956 17.6473 11.0053L25.3485 11.087L29.3086 17.9455L28.1628 18.712H28.1625ZM28.5711 21.6074C28.7221 21.3579 28.7686 21.0589 28.7002 20.7754C28.6318 20.4919 28.4542 20.2469 28.2061 20.0937L19.4775 14.757C19.1484 14.5515 18.7572 14.469 18.3731 14.5242C17.9891 14.5794 17.6369 14.7687 17.3791 15.0587L15.8326 16.7647C15.5694 17.0549 15.2389 17.2758 14.8701 17.4079C14.5013 17.54 14.1056 17.5792 13.718 17.5221C13.3305 17.4649 12.963 17.3132 12.648 17.0802C12.333 16.8473 12.0803 16.5404 11.9121 16.1866C11.8513 16.0583 11.7911 15.9192 11.7333 15.7723C11.6698 15.615 11.6507 15.4432 11.6781 15.2758C11.7055 15.1084 11.7784 14.9517 11.8888 14.8229L13.4522 12.9666L11.2064 11.8475L7.39269 18.4526L8.33308 18.9592L8.53065 18.7614C8.81909 18.4726 9.18046 18.2675 9.57628 18.1679C9.97209 18.0683 10.3875 18.0779 10.7783 18.1958C11.169 18.3137 11.5205 18.5353 11.7952 18.8372C12.0699 19.1391 12.2576 19.5098 12.3383 19.9099C12.7562 19.7218 13.2214 19.6651 13.6723 19.7474C14.1232 19.8297 14.5384 20.0471 14.8629 20.3707C15.0729 20.58 15.2392 20.8289 15.3524 21.1029C15.4655 21.377 15.5232 21.6707 15.5221 21.9672C15.522 22.0931 15.5114 22.2188 15.4905 22.343C15.8096 22.3574 16.122 22.4395 16.407 22.5838C16.6919 22.7281 16.943 22.9313 17.1435 23.18C17.344 23.4287 17.4893 23.7171 17.5698 24.0262C17.6504 24.3353 17.6643 24.658 17.6107 24.9729C17.6652 24.9756 17.7197 24.9807 17.7741 24.9874C17.7852 24.8876 17.822 24.7925 17.881 24.7114C17.94 24.6302 18.0192 24.5658 18.1106 24.5245C18.2021 24.4831 18.3027 24.4663 18.4026 24.4757C18.5025 24.4851 18.5983 24.5203 18.6804 24.5779L20.7682 26.0403L21.3722 26.4074C21.5291 26.5059 21.7102 26.5592 21.8955 26.5613C22.0808 26.5635 22.2631 26.5144 22.4223 26.4196C22.822 26.2021 23.108 25.7736 23.1179 25.3775C23.1265 25.0366 22.9359 24.7441 22.5513 24.5093C22.5513 24.5093 22.5513 24.5093 22.5506 24.5083L19.4643 22.6214C19.3992 22.582 19.3425 22.5301 19.2974 22.4687C19.2524 22.4073 19.22 22.3376 19.202 22.2636C19.184 22.1896 19.1808 22.1128 19.1925 22.0376C19.2043 21.9624 19.2308 21.8902 19.2705 21.8252C19.3102 21.7603 19.3624 21.7038 19.424 21.6591C19.4856 21.6143 19.5554 21.5822 19.6295 21.5645C19.7035 21.5468 19.7803 21.544 19.8555 21.5561C19.9307 21.5681 20.0027 21.595 20.0675 21.635L24.2558 24.1961C24.505 24.3485 24.8044 24.3957 25.0884 24.3272C25.3723 24.2588 25.6174 24.0804 25.7698 23.8312C25.9221 23.582 25.9693 23.2826 25.9008 22.9986C25.8324 22.7147 25.654 22.4696 25.4048 22.3172L20.7769 19.4877C20.6476 19.4071 20.5555 19.2786 20.5204 19.1302C20.4854 18.9819 20.5103 18.8258 20.5898 18.6957C20.6693 18.5657 20.7969 18.4723 20.9449 18.4359C21.0929 18.3995 21.2493 18.423 21.3801 18.5013L26.0068 21.3298L26.0076 21.3305C26.0082 21.3305 26.0087 21.3315 26.009 21.3315L27.0573 21.9725C27.3068 22.1236 27.6058 22.17 27.8894 22.1016C28.1729 22.0332 28.4179 21.8556 28.5711 21.6074ZM20.2492 28.3281C20.1313 28.5205 19.9572 28.6721 19.7505 28.7623C19.5437 28.8526 19.3141 28.8771 19.0929 28.8326L19.1022 28.8231C19.3122 28.6138 19.4786 28.3649 19.5917 28.0909C19.7049 27.8169 19.7625 27.5231 19.7614 27.2267C19.7614 27.0507 19.741 26.8754 19.7008 26.7041L20.082 26.9709C20.2565 27.1464 20.3677 27.3751 20.398 27.6208C20.4283 27.8666 20.3759 28.1154 20.2492 28.3281ZM16.0749 29.7569C15.8571 29.7577 15.644 29.6936 15.4627 29.573C15.2814 29.4524 15.14 29.2806 15.0566 29.0794C14.9731 28.8783 14.9513 28.6569 14.994 28.4433C15.0366 28.2297 15.1418 28.0337 15.2962 27.88L15.2968 27.879L16.7271 26.4492C16.934 26.2449 17.2134 26.1308 17.5041 26.1317C17.7949 26.1327 18.0735 26.2486 18.2791 26.4542C18.4847 26.6598 18.6007 26.9384 18.6016 27.2291C18.6026 27.5199 18.4885 27.7993 18.2842 28.0062L16.8533 29.4374C16.7512 29.5396 16.6297 29.6205 16.4961 29.6754C16.3624 29.7303 16.2192 29.7582 16.0747 29.7574L16.0749 29.7569ZM12.6664 27.3166C12.4603 27.11 12.3444 26.8301 12.3444 26.5383C12.3443 26.2465 12.4599 25.9665 12.666 25.7598L14.607 23.819H14.6075C14.7097 23.7167 14.831 23.6356 14.9645 23.5803C15.0981 23.5249 15.2412 23.4964 15.3858 23.4964C15.5303 23.4964 15.6735 23.5248 15.807 23.5801C15.9406 23.6354 16.062 23.7164 16.1642 23.8186C16.2665 23.9208 16.3476 24.0422 16.4029 24.1757C16.4583 24.3092 16.4868 24.4524 16.4868 24.5969C16.4868 24.7415 16.4584 24.8846 16.4031 25.0182C16.3478 25.1518 16.2668 25.2731 16.1646 25.3754L15.9096 25.6305L14.2237 27.3166C14.0171 27.5229 13.737 27.6388 13.445 27.6388C13.1531 27.6388 12.873 27.5229 12.6664 27.3166ZM10.0365 25.1969C9.83033 24.9904 9.71452 24.7104 9.71452 24.4186C9.71452 24.1267 9.83033 23.8468 10.0365 23.6402L10.7939 22.8821C10.7966 22.8801 10.7994 22.8771 10.802 22.8749C10.8034 22.8735 10.8044 22.8718 10.8058 22.8705L11.7183 21.9577C11.7197 21.9563 11.7214 21.9557 11.7227 21.9543C11.7254 21.9513 11.728 21.9482 11.7308 21.9455L12.4878 21.1882C12.6947 20.9834 12.9743 20.8689 13.2654 20.8697C13.5564 20.8705 13.8353 20.9865 14.0411 21.1923C14.2469 21.3982 14.3629 21.6771 14.3636 21.9682C14.3643 22.2593 14.2497 22.5388 14.045 22.7456L11.5936 25.1968C11.387 25.4029 11.107 25.5187 10.8151 25.5187C10.5232 25.5187 10.2432 25.4029 10.0365 25.1968V25.1969ZM8.42724 20.4997L9.34791 19.5791C9.55381 19.3721 9.8335 19.2554 10.1255 19.2546C10.4174 19.2538 10.6977 19.3691 10.9047 19.575C11.1117 19.7809 11.2284 20.0606 11.2292 20.3525C11.23 20.6445 11.1147 20.9248 10.9088 21.1318L9.97987 22.0617C9.7728 22.2676 9.49243 22.3828 9.20043 22.3819C8.90844 22.3811 8.62874 22.2643 8.42287 22.0572C8.217 21.8501 8.10182 21.5697 8.10266 21.2778C8.10351 20.9858 8.22032 20.7061 8.42739 20.5002L8.42724 20.4997ZM31.5329 6.37782C31.4562 6.24504 31.33 6.14814 31.1819 6.10843C31.0338 6.06872 30.876 6.08946 30.7432 6.16609L25.067 9.4434C24.9818 9.49223 24.9105 9.56211 24.8601 9.64635C24.8096 9.7306 24.7816 9.82639 24.7788 9.92455L17.6595 9.84925C17.1682 9.83704 16.6806 9.93723 16.2338 10.1422C15.7871 10.3471 15.3931 10.6514 15.0818 11.0317L14.2175 12.0579C14.2103 12.0538 14.2027 12.0491 14.1948 12.0453L11.7854 10.8445L12.145 10.2216C12.2216 10.0888 12.2423 9.931 12.2026 9.78293C12.1629 9.63486 12.066 9.50863 11.9333 9.43199L6.25703 6.15452C6.19126 6.11657 6.11865 6.09195 6.04335 6.08207C5.96806 6.07218 5.89156 6.07723 5.81821 6.09692C5.74487 6.11661 5.67613 6.15056 5.6159 6.19682C5.55568 6.24308 5.50517 6.30076 5.46724 6.36655L0.0775274 15.7017C0.000998188 15.8344 -0.0197106 15.9921 0.0199506 16.1401C0.0596118 16.2881 0.156399 16.4143 0.289049 16.491L5.96522 19.7683C6.03098 19.8062 6.10357 19.8309 6.17885 19.8408C6.25413 19.8507 6.33062 19.8457 6.40396 19.826C6.4773 19.8064 6.54605 19.7725 6.60629 19.7262C6.66652 19.68 6.71706 19.6224 6.75502 19.5566L6.81384 19.4545L7.48634 19.8168C7.11961 20.2491 6.92869 20.8035 6.95145 21.3699C6.9742 21.9363 7.20897 22.4736 7.6092 22.8751C7.92237 23.1896 8.32218 23.4036 8.75757 23.4899C8.60201 23.8335 8.53516 24.2107 8.56315 24.5869C8.59113 24.963 8.71306 25.3261 8.91776 25.643C9.12246 25.9598 9.40339 26.2202 9.73481 26.4003C10.0662 26.5804 10.4376 26.6745 10.8148 26.6739C10.9407 26.6735 11.0663 26.663 11.1905 26.6424C11.2051 26.9615 11.2873 27.2738 11.4317 27.5587C11.5762 27.8436 11.7795 28.0946 12.0282 28.2949C12.2769 28.4953 12.5654 28.6406 12.8745 28.721C13.1836 28.8015 13.5063 28.8153 13.8211 28.7617C13.8418 29.1984 13.9889 29.6197 14.2444 29.9745C14.4999 30.3293 14.853 30.6023 15.2606 30.7603C15.6683 30.9183 16.1131 30.9546 16.541 30.8647C16.9689 30.7748 17.3615 30.5626 17.6711 30.254L18.2043 29.7206C18.5402 29.911 18.9198 30.0112 19.306 30.0114C19.6931 30.0118 20.0739 29.9127 20.4118 29.7236C20.7496 29.5346 21.0333 29.2619 21.2355 28.9317C21.4628 28.5588 21.5759 28.1274 21.5607 27.6908C22.0473 27.7667 22.5454 27.6769 22.9749 27.4357C23.7321 27.0231 24.2371 26.244 24.2714 25.4426C24.6074 25.5288 24.9587 25.5364 25.298 25.4647C25.6374 25.393 25.9556 25.244 26.228 25.0292C26.5003 24.8144 26.7194 24.5396 26.8682 24.2263C27.017 23.913 27.0915 23.5696 27.0859 23.2228C27.262 23.2667 27.4428 23.2893 27.6244 23.29C27.805 23.2897 27.985 23.2681 28.1605 23.2255C28.5531 23.1303 28.913 22.9315 29.2026 22.6498C29.4922 22.3681 29.701 22.0139 29.8071 21.6241C29.9132 21.2343 29.9128 20.8231 29.8059 20.4335C29.6991 20.0439 29.4896 19.6901 29.1995 19.409L29.8878 18.9489L30.2451 19.5678C30.3218 19.7006 30.4481 19.7974 30.5962 19.8371C30.7443 19.8768 30.9021 19.8561 31.035 19.7796L36.7109 16.5027C36.7767 16.4648 36.8343 16.4142 36.8805 16.354C36.9267 16.2937 36.9606 16.2249 36.9802 16.1516C36.9998 16.0782 37.0048 16.0018 36.9949 15.9265C36.985 15.8512 36.9603 15.7786 36.9223 15.7129L31.5329 6.37782Z"})})}},37128:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});var n=i(57437),r=i(94508);function s(e){let{className:t}=e;return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"34",height:"34",viewBox:"0 0 34 34",fill:"none",className:(0,r.ZP)("fill-[#09A7FF]",t),children:(0,n.jsx)("path",{d:"M26.8317 25.452H13.1851L12.3417 22.0782H30.1758L34 6.78136H8.51752L7.17339 1.40479H0V3.40753H5.60966L11.1408 25.5319C9.53061 25.8777 8.31969 27.3116 8.31969 29.0236C8.31969 30.9929 9.92187 32.5951 11.8913 32.5951C13.8607 32.5951 15.4629 30.9928 15.4629 29.0236C15.4629 28.461 15.3317 27.9286 15.099 27.4547H23.624C23.3912 27.9286 23.2601 28.461 23.2601 29.0236C23.2601 30.9929 24.8623 32.5951 26.8317 32.5951C28.801 32.5951 30.4032 30.9928 30.4032 29.0236C30.4032 27.0542 28.8009 25.452 26.8317 25.452Z"})})}},62869:function(e,t,i){"use strict";i.d(t,{d:function(){return l},z:function(){return d}});var n=i(57437),r=i(37053),s=i(90535),a=i(2265),o=i(94508);let l=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:i,variant:s,size:a,asChild:d=!1,...c}=e,u=d?r.g7:"button";return(0,n.jsx)(u,{className:(0,o.ZP)(l({variant:s,size:a,className:i})),ref:t,...c})});d.displayName="Button"},6512:function(e,t,i){"use strict";var n=i(57437),r=i(55156),s=i(2265),a=i(94508);let o=s.forwardRef((e,t)=>{let{className:i,orientation:s="horizontal",decorative:o=!0,...l}=e;return(0,n.jsx)(r.f,{ref:t,decorative:o,orientation:s,className:(0,a.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",i),...l})});o.displayName=r.f.displayName,t.Z=o},17062:function(e,t,i){"use strict";i.d(t,{Z:function(){return m},O:function(){return v}});var n=i(57437),r=i(80114);i(83079);var s=(0,i(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=i(31117),o=i(79981),l=i(78040),d=i(83130);class c{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var u=i(99376),h=i(2265);let C=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),v=()=>h.useContext(C);function m(e){let{children:t}=e,[i,v]=h.useState("Desktop"),[m,f]=h.useState(!1),[p,g]=h.useState(),{data:y,isLoading:w,error:x,mutate:L}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:b,isLoading:A}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:j,isLoading:N}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),S=(0,u.useRouter)(),k=(0,u.usePathname)();h.useEffect(()=>{(async()=>{v((await s()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;v(e<768?"Mobile":e<1024?"Tablet":"Desktop"),f(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");g(new c(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{x&&!l.sp.includes(k)&&S.push("/signin")},[x]);let F=h.useMemo(()=>{var e,t,n;return{isAuthenticate:!!(null==y?void 0:null===(e=y.data)||void 0===e?void 0:e.login),auth:(null==y?void 0:null===(t=y.data)||void 0===t?void 0:t.user)?new d.n(null==y?void 0:null===(n=y.data)||void 0===n?void 0:n.user):null,isLoading:w,deviceLocation:p,refreshAuth:()=>L(y),isExpanded:m,device:i,setIsExpanded:f,branding:null==b?void 0:b.data,googleAnalytics:(null==j?void 0:j.data)?{active:null==j?void 0:j.data.active,apiKey:null==j?void 0:j.data.apiKey}:{active:!1,apiKey:""}}},[y,p,m,i]),I=!w&&!A&&!N;return(0,n.jsx)(C.Provider,{value:F,children:I?t:(0,n.jsx)(r.default,{})})}},21251:function(e,t,i){"use strict";i.d(t,{T:function(){return r}});var n=i(17062);let r=()=>{let{branding:e}=(0,n.O)();return e}},31117:function(e,t,i){"use strict";i.d(t,{d:function(){return s}});var n=i(79981),r=i(85323);let s=(e,t)=>(0,r.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,i){"use strict";var n=i(78040),r=i(83464);t.Z=r.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,i){"use strict";i.d(t,{rH:function(){return n},sp:function(){return r}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:i(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},r=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,i){"use strict";i.d(t,{F:function(){return c},Fg:function(){return C},Fp:function(){return d},Qp:function(){return h},ZP:function(){return o},fl:function(){return l},qR:function(){return u},w4:function(){return v}});var n=i(78040),r=i(61994),s=i(14438),a=i(53335);function o(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return(0,a.m6)((0,r.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>s.toast.success("Copied to clipboard!")).catch(()=>{s.toast.error("Failed to copy!")})};class c{format(e,t){let{currencyCode:i,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(i)}formatVC(e,t){let{currencyCode:i,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(i," ")}constructor(e){this.formatter=(e,t)=>{var i,n;let r;let s=void 0===t?this.currencyCode:t;try{r=new Intl.NumberFormat("en-US",{style:"currency",currency:s,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){r=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let a=null!==(n=null===(i=r.formatToParts(e).find(e=>"currency"===e.type))||void 0===i?void 0:i.value)&&void 0!==n?n:s,o=r.format(e),l=o.substring(a.length).trim();return{currencyCode:s,currencySymbol:a,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",h=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",C=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",v=function(e){var t,i;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",r=new URLSearchParams(null===(i=window)||void 0===i?void 0:null===(t=i.location)||void 0===t?void 0:t.search);return e?r.set(n,e):r.delete(n),r}},74539:function(e,t,i){"use strict";i.d(t,{k:function(){return n}});class n{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,i){"use strict";i.d(t,{n:function(){return l}});class n{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var r=i(84937);class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=i(66419),o=i(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new r.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new s(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new n(e.agent):void 0}}},84937:function(e,t,i){"use strict";i.d(t,{O:function(){return r}});var n=i(74539);class r{constructor(e){var t,i;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(i=e.phone)||void 0===i?void 0:i.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new n.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,i){"use strict";i.d(t,{u:function(){return n}});class n{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,27648,35458,92971,95030,1744],function(){return e(e.s=30308)}),_N_E=e.O()}]);