"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[85017],{41709:function(e,l,a){function t(e){let{condition:l,children:a}=e;return l?a:null}a.d(l,{J:function(){return t}}),a(2265)},85017:function(e,l,a){a.d(l,{k:function(){return w}});var t=a(57437),s=a(2265),n=a(41709),i=a(52323),d=a(39785),c=a(85487),o=a(62869),r=a(26815),u=a(57054),h=a(53647),x=a(31117),m=a(52789),v=a(45348),j=a(61029),p=a(2901),f=a(96567),y=a(99376),g=a(43949);function w(e){var l,a,w,N,C,b,A;let{canFilterByStatus:S=!0,canFilterByDate:k=!0,canFilterByMethod:J=!1,canFilterByGateway:P=!1,canFilterByAgent:Q=!1,canFilterByAgentMethod:Z=!1,canFilterUser:D=!1,canFilterByGender:L=!1,canFilterByCountryCode:M=!1}=e,{t:K}=(0,g.$G)(),E=(0,y.useSearchParams)(),O=(0,y.usePathname)(),G=(0,y.useRouter)(),[W,z]=s.useState({}),[B,F]=s.useState(!1),{data:I,isLoading:U}=(0,x.d)("/methods"),{data:V,isLoading:_}=(0,x.d)("/gateways"),{data:R,isLoading:$}=(0,x.d)(Z?"/agent-methods?limit=100&page=1":""),T=(e,l)=>{let a=new URLSearchParams(E.toString());l?(a.set(e,l),z(a=>({...a,[e]:l}))):(a.delete(e),z(l=>({...l,[e]:""}))),G.replace("".concat(O,"?").concat(a.toString()))};return s.useEffect(()=>{let e=Object.fromEntries(E.entries());e&&z(e)},[]),(0,t.jsxs)(u.J2,{open:B,onOpenChange:F,children:[(0,t.jsx)(u.xo,{asChild:!0,children:(0,t.jsxs)(o.z,{variant:"outline",children:[(0,t.jsx)(f.Z,{size:20}),K("Filter")]})}),(0,t.jsx)(u.yk,{className:"w-full min-w-[300px] max-w-[400px]",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,t.jsx)(n.J,{condition:S,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,t.jsx)(r.Z,{className:"text-sm font-normal text-secondary-text",children:D?"Status":"Transaction status"}),(0,t.jsxs)(h.Ph,{value:null==W?void 0:W.status,onValueChange:e=>T("status",e),children:[(0,t.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,t.jsx)(h.ki,{placeholder:K("Status")})}),(0,t.jsxs)(h.Bw,{children:[(0,t.jsxs)(n.J,{condition:D,children:[(0,t.jsx)(h.Ql,{value:"true",children:K("Active")}),(0,t.jsx)(h.Ql,{value:"false",children:K("Inactive")})]}),(0,t.jsxs)(n.J,{condition:!D,children:[(0,t.jsxs)(h.Ql,{value:"pending",children:[" ",K("Pending")," "]}),(0,t.jsxs)(h.Ql,{value:"completed",children:[K("Completed")," "]}),(0,t.jsxs)(h.Ql,{value:"failed",children:[K("Failed")," "]})]})]})]})]})}),(0,t.jsx)(n.J,{condition:L,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,t.jsx)(r.Z,{className:"text-sm font-normal text-secondary-text",children:K("Gender")}),(0,t.jsxs)(h.Ph,{value:null==W?void 0:W.gender,onValueChange:e=>T("gender",e),children:[(0,t.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,t.jsx)(h.ki,{placeholder:K("Gender")})}),(0,t.jsxs)(h.Bw,{children:[(0,t.jsx)(h.Ql,{value:"male",children:K("Male")}),(0,t.jsx)(h.Ql,{value:"female",children:K("Female")})]})]})]})}),(0,t.jsx)(n.J,{condition:J,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,t.jsx)(r.Z,{className:"text-sm font-normal text-secondary-text",children:K("Withdraw method")}),(0,t.jsxs)(h.Ph,{value:null==W?void 0:W.method,onValueChange:e=>T("method",e),children:[(0,t.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,t.jsx)(h.ki,{placeholder:K("Withdraw method")})}),(0,t.jsxs)(h.Bw,{side:"right",align:"start",children:[(0,t.jsx)(n.J,{condition:Q,children:(0,t.jsx)(h.Ql,{value:"agent",children:K("Agent")})}),U?(0,t.jsx)(c.Loader,{}):null==I?void 0:null===(a=I.data)||void 0===a?void 0:null===(l=a.map(e=>new v.n(e)))||void 0===l?void 0:l.map(e=>(0,t.jsx)(h.Ql,{value:e.value,className:"border-b border-dashed",children:e.name},e.id))]})]})]})}),(0,t.jsx)(n.J,{condition:P,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,t.jsx)(r.Z,{className:"text-sm font-normal text-secondary-text",children:K("Deposit gateway")}),(0,t.jsxs)(h.Ph,{value:null==W?void 0:W.gateway,onValueChange:e=>T("method",e),children:[(0,t.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,t.jsx)(h.ki,{placeholder:K("Deposit gateway")})}),(0,t.jsxs)(h.Bw,{side:"right",align:"start",children:[(0,t.jsx)(n.J,{condition:Q,children:(0,t.jsx)(h.Ql,{value:"agent",children:K("Agent")})}),_?(0,t.jsx)(c.Loader,{}):null==V?void 0:null===(N=V.data)||void 0===N?void 0:null===(w=N.map(e=>new m.M(e)))||void 0===w?void 0:w.map(e=>(0,t.jsxs)(h.Ql,{value:e.value,className:"border-b border-dashed",children:[e.name," ",(0,t.jsx)("span",{className:"pl-1.5 text-secondary-text/80",children:e.value})]},e.id))]})]})]})}),(0,t.jsx)(n.J,{condition:Z,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,t.jsx)(r.Z,{className:"text-sm font-normal text-secondary-text",children:K("Agent method")}),(0,t.jsxs)(h.Ph,{value:null==W?void 0:W.method,onValueChange:e=>T("method",e),children:[(0,t.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,t.jsx)(h.ki,{placeholder:K("Method")})}),(0,t.jsx)(h.Bw,{side:"right",align:"start",children:$?(0,t.jsx)(c.Loader,{}):null==R?void 0:null===(A=R.data)||void 0===A?void 0:null===(b=A.data)||void 0===b?void 0:null===(C=b.map(e=>new v.n(e)))||void 0===C?void 0:C.map(e=>(0,t.jsx)(h.Ql,{value:e.name,className:"border-b border-dashed",children:e.name},e.id))})]})]})}),(0,t.jsx)(n.J,{condition:k,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,t.jsx)(r.Z,{className:"text-sm font-normal text-secondary-text",children:K("Date")}),(0,t.jsx)(d.M,{value:Object.prototype.hasOwnProperty.call(W,"date")&&W.date?new Date((0,j.Qc)(W.date,"yyyy-MM-dd",new Date)):void 0,onChange:e=>{T("date",e?(0,p.WU)(e,"yyyy-MM-dd"):"")},className:"h-10",placeholderClassName:"text-secondary-text"})]})}),(0,t.jsx)(n.J,{condition:M,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,t.jsx)(r.Z,{className:"text-sm font-normal text-secondary-text",children:K("Country")}),(0,t.jsx)(i.g,{defaultCountry:null==W?void 0:W.countryCode,onSelectChange:e=>{T("countryCode",e.code.cca2)},triggerClassName:"h-10",placeholderClassName:"text-secondary-text",side:"right",align:"start"})]})}),(0,t.jsxs)("div",{className:"flex flex-col items-stretch space-y-2",children:[(0,t.jsx)(o.z,{type:"button",onClick:()=>F(!1),className:"h-10",children:K("Done")}),(0,t.jsx)(o.z,{type:"button",variant:"outline",onClick:()=>{let e=new URLSearchParams;Object.keys(W).forEach(l=>e.delete(l)),z({}),G.replace("".concat(O,"?").concat(e.toString()))},className:"h-10",children:K("Clear Filter")})]})]})})]})}},52323:function(e,l,a){a.d(l,{g:function(){return x}});var t=a(57437),s=a(2265),n=a(85487),i=a(41062),d=a(23518),c=a(57054),o=a(40593),r=a(94508),u=a(36887),h=a(43949);function x(e){var l,a;let{allCountry:x=!1,defaultValue:m,defaultCountry:v,onSelectChange:j,disabled:p=!1,triggerClassName:f,arrowClassName:y,flagClassName:g,display:w,placeholderClassName:N,align:C="start",side:b="bottom"}=e,{t:A}=(0,h.$G)(),{countries:S,getCountryByCode:k,isLoading:J}=(0,o.F)(),[P,Q]=s.useState(!1),[Z,D]=s.useState(m);return s.useEffect(()=>{m&&D(m)},[m]),s.useEffect(()=>{(async()=>{v&&await k(v,e=>{e&&(D(e),j(e))})})()},[v]),(0,t.jsxs)(c.J2,{open:P,onOpenChange:Q,children:[(0,t.jsxs)(c.xo,{disabled:p,className:(0,r.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",f),children:[Z?(0,t.jsx)("div",{className:"flex flex-1 items-center",children:(0,t.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,t.jsx)(i.W,{className:g,countryCode:(null===(l=Z.code)||void 0===l?void 0:l.cca2)==="*"?"UN":null===(a=Z.code)||void 0===a?void 0:a.cca2}),void 0!==w?w(Z):(0,t.jsx)("span",{children:Z.name})]})}):(0,t.jsx)("span",{className:(0,r.ZP)("text-placeholder",N),children:A("Select country")}),(0,t.jsx)(u.Z,{className:(0,r.ZP)("size-6",y)})]}),(0,t.jsx)(c.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:C,side:b,children:(0,t.jsxs)(d.mY,{children:[(0,t.jsx)(d.sZ,{placeholder:A("Search...")}),(0,t.jsx)(d.e8,{children:(0,t.jsxs)(d.fu,{children:[J&&(0,t.jsx)(n.Loader,{}),x&&(0,t.jsxs)(d.di,{value:A("All countries"),onSelect:()=>{D({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),j({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),Q(!1)},children:[(0,t.jsx)(i.W,{countryCode:"UN"}),(0,t.jsx)("span",{className:"pl-1.5",children:A("All countries")})]}),null==S?void 0:S.map(e=>"officially-assigned"===e.status?(0,t.jsxs)(d.di,{value:e.name,onSelect:()=>{D(e),j(e),Q(!1)},children:[(0,t.jsx)(i.W,{countryCode:e.code.cca2}),(0,t.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},52789:function(e,l,a){a.d(l,{M:function(){return t}});class t{constructor(e){this.id=null==e?void 0:e.id,this.logoImage=null==e?void 0:e.logoImage,this.name=null==e?void 0:e.name,this.value=null==e?void 0:e.value,this.apiKey=null==e?void 0:e.apiKey,this.secretKey=null==e?void 0:e.secretKey,this.active=null==e?void 0:e.active,this.activeApi=null==e?void 0:e.activeApi,this.recommended=null==e?void 0:e.recommended,this.variables=null==e?void 0:e.variables,this.allowedCurrencies=null==e?void 0:e.allowedCurrencies,this.allowedCountries=null==e?void 0:e.allowedCountries,this.createdAt=(null==e?void 0:e.createdAt)?new Date(null==e?void 0:e.createdAt):null,this.updatedAt=(null==e?void 0:e.updatedAt)?new Date(null==e?void 0:e.updatedAt):null}}},45348:function(e,l,a){a.d(l,{n:function(){return t}});class t{constructor(e){var l,a,t;this.id=null==e?void 0:e.id,this.logoImage=null==e?void 0:e.logoImage,this.name=null==e?void 0:e.name,this.value=null==e?void 0:e.value,this.apiKey=null==e?void 0:e.apiKey,this.secretKey=null==e?void 0:e.secretKey,this.params=(null==e?void 0:e.params)?JSON.parse(null==e?void 0:e.params):null,this.currencyCode=null==e?void 0:e.currencyCode,this.countryCode=null==e?void 0:e.countryCode,this.active=!!(null==e?void 0:e.active),this.activeApi=!!(null==e?void 0:e.activeApi),this.recommended=!!(null==e?void 0:e.recommended),this.minAmount=null!==(l=null==e?void 0:e.minAmount)&&void 0!==l?l:0,this.maxAmount=null!==(a=null==e?void 0:e.maxAmount)&&void 0!==a?a:0,this.fixedCharge=null!==(t=null==e?void 0:e.fixedCharge)&&void 0!==t?t:0,this.percentageCharge=null==e?void 0:e.percentageCharge,this.createdAt=(null==e?void 0:e.createdAt)?new Date(e.createdAt):null,this.updatedAt=(null==e?void 0:e.updatedAt)?new Date(e.updatedAt):null}}}}]);