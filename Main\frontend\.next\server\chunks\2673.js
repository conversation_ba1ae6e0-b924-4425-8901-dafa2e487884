exports.id=2673,exports.ids=[2673],exports.modules={56450:(e,s,r)=>{Promise.resolve().then(r.bind(r,13048))},71642:(e,s,r)=>{Promise.resolve().then(r.bind(r,50276))},47802:(e,s,r)=>{Promise.resolve().then(r.bind(r,92392))},48335:(e,s,r)=>{Promise.resolve().then(r.bind(r,85999))},35303:()=>{},1194:(e,s,r)=>{"use strict";r.d(s,{h:()=>a});var t=r(10326);function a({title:e,subTitle:s}){return(0,t.jsxs)(t.Fragment,{children:[t.jsx("p",{className:"mb-2.5 text-sm text-secondary-text",children:s}),t.jsx("h1",{className:"text-[28px] font-medium leading-10 md:text-[32px]",children:e})]})}},13048:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(10326),a=r(72719),n=r(19395),i=r(35047),l=r(17577),o=r(36814),d=r(4066),c=r(77863);function u(){let{authBanner:e}=(0,d.T)();return t.jsx("div",{style:{backgroundImage:`url(${(0,c.qR)(e)})`},className:"hidden h-full w-full border-r bg-cover bg-no-repeat md:block md:max-w-[350px] lg:max-w-[510px]"})}function m({children:e}){let{isAuthenticate:s,isLoading:r}=(0,n.a)(),d=(0,i.useRouter)(),[c,m]=l.useState(!1);return(l.useLayoutEffect(()=>{!r&&s&&d.push("/")},[r]),l.useLayoutEffect(()=>{r||s||m(!0)},[r,s]),c)?(0,t.jsxs)("div",{className:"flex h-screen",children:[t.jsx(u,{}),(0,t.jsxs)("div",{className:"flex h-full w-full flex-col bg-background",children:[t.jsx(o.w,{path:"/signin"}),t.jsx("div",{className:"overflow-y-auto",children:e})]})]}):t.jsx(a.default,{})}},17077:(e,s,r)=>{"use strict";r.d(s,{Z:()=>v});var t=r(10326),a=r(17577),n=r(1194),i=r(5158),l=r(61718),o=r(720),d=r(92392),c=r(90772),u=r(55632),m=r(54432),x=r(31048),p=r(88846),f=r(8281),h=r(85319),g=r(74064),j=r(44221),y=r(44284),b=r(74723),N=r(70012);function v({onPrev:e,onSubmit:s,nextButtonLabel:r,title:v,subTitle:w,isLoading:C=!1,formData:z}){let{t:S}=(0,N.$G)(),Z=(0,b.cI)({resolver:(0,g.F)(h.Zg),defaultValues:{title:"",dateOfBirth:void 0,street:"",country:"",city:"",zipCode:""}});return a.useEffect(()=>{z&&Z.reset({...z})},[]),t.jsx(u.l0,{...Z,children:(0,t.jsxs)("form",{onSubmit:Z.handleSubmit(s),children:[t.jsx(n.h,{title:v,subTitle:w}),t.jsx("div",{className:"mt-6 flex h-[5px] items-center",children:t.jsx(f.Z,{className:"bg-divider"})}),(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[t.jsx(u.lX,{required:!0,children:S("Select your gender")}),t.jsx("div",{className:"grid grid-cols-12 gap-4",children:t.jsx(u.Wi,{control:Z.control,name:"title",render:({field:e})=>(0,t.jsxs)(u.xJ,{className:"col-span-12",children:[t.jsx(u.NI,{children:(0,t.jsxs)(p.E,{defaultValue:e.value,onValueChange:e.onChange,className:"grid-cols-12 gap-4",children:[(0,t.jsxs)(x.Z,{htmlFor:"male","data-active":"male"===e.value,className:"col-span-12 flex h-12 w-full cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[t.jsx(p.m,{id:"male",value:"male",className:"absolute left-0 top-0 opacity-0"}),t.jsx("span",{children:S("Male")})]}),(0,t.jsxs)(x.Z,{htmlFor:"female","data-active":"female"===e.value,className:"col-span-12 flex h-12 cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[t.jsx(p.m,{id:"female",value:"female",className:"absolute left-0 top-0 opacity-0"}),t.jsx("span",{children:S("Female")})]})]})}),t.jsx(u.zG,{})]})})})]}),t.jsx(u.Wi,{control:Z.control,name:"dateOfBirth",render:({field:e})=>(0,t.jsxs)(u.xJ,{children:[t.jsx(u.lX,{children:S("Select your birth date")}),t.jsx(u.NI,{children:t.jsx(o.M,{value:e.value,onChange:e.onChange})}),t.jsx(u.zG,{})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[t.jsx(x.Z,{className:"col-span-12",children:S("Your full mailing address")}),t.jsx(u.Wi,{control:Z.control,name:"street",render:({field:e})=>(0,t.jsxs)(u.xJ,{className:"col-span-12",children:[t.jsx(u.NI,{children:t.jsx(m.I,{type:"text",placeholder:S("Address Line"),...e})}),t.jsx(u.zG,{})]})}),t.jsx(u.Wi,{control:Z.control,name:"country",render:({field:e})=>(0,t.jsxs)(u.xJ,{className:"col-span-12",children:[t.jsx(u.NI,{children:t.jsx(l.g,{defaultCountry:e.value,onSelectChange:s=>e.onChange(s.code.cca2)})}),t.jsx(u.zG,{})]})}),t.jsx(u.Wi,{control:Z.control,name:"city",render:({field:e})=>(0,t.jsxs)(u.xJ,{className:"col-span-12 md:col-span-6",children:[t.jsx(u.NI,{children:t.jsx(m.I,{type:"text",placeholder:S("City"),...e})}),t.jsx(u.zG,{})]})}),t.jsx(u.Wi,{control:Z.control,name:"zipCode",render:({field:e})=>(0,t.jsxs)(u.xJ,{className:"col-span-12 md:col-span-6",children:[t.jsx(u.NI,{children:t.jsx(m.I,{type:"text",placeholder:S("Zip Code"),...e})}),t.jsx(u.zG,{})]})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,t.jsxs)(c.z,{className:"border-btn-outline-border p-4 text-base font-medium leading-[22px]",variant:"outline",type:"button",onClick:e,children:[t.jsx(j.Z,{size:24}),S("Back")]}),(0,t.jsxs)(c.z,{disabled:C,className:"w-[286px] p-4 text-base font-medium leading-[22px]",children:[(0,t.jsxs)(i.J,{condition:!C,children:[r,t.jsx(y.Z,{size:16})]}),t.jsx(i.J,{condition:C,children:t.jsx(d.Loader,{className:"text-background"})})]})]})]})]})})}},58116:(e,s,r)=>{"use strict";r.d(s,{Z:()=>N});var t=r(10326),a=r(1194),n=r(71227),i=r(29612),l=r(90772),o=r(53313),d=r(55632),c=r(54432),u=r(31048),m=r(8281),x=r(19395),p=r(85319),f=r(74064),h=r(44221),g=r(44284),j=r(35047);r(17577);var y=r(74723),b=r(70012);function N({onPrev:e,onSubmit:s,title:r,subTitle:N,formData:v}){let{t:w}=(0,b.$G)();(0,j.useSearchParams)();let{deviceLocation:C}=(0,x.a)(),z=(0,y.cI)({resolver:(0,f.F)(p.cN),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:"",phone:"",referralCode:"",termAndCondition:void 0}});return t.jsx(d.l0,{...z,children:(0,t.jsxs)("form",{onSubmit:z.handleSubmit(s),children:[t.jsx(a.h,{title:r,subTitle:N}),t.jsx("div",{className:"my-6 flex h-[5px] items-center",children:t.jsx(m.Z,{className:"bg-divider"})}),(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[t.jsx(d.Wi,{control:z.control,name:"firstName",render:({field:e})=>(0,t.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[t.jsx(d.lX,{children:w("First Name")}),t.jsx(d.NI,{children:t.jsx(c.I,{type:"text",placeholder:w("Enter first name"),...e})}),t.jsx(d.zG,{})]})}),t.jsx(d.Wi,{control:z.control,name:"lastName",render:({field:e})=>(0,t.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[t.jsx(d.lX,{children:w("Last Name")}),t.jsx(d.NI,{children:t.jsx(c.I,{type:"text",placeholder:w("Enter last name"),...e})}),t.jsx(d.zG,{})]})})]}),t.jsx(d.Wi,{control:z.control,name:"email",render:({field:e})=>(0,t.jsxs)(d.xJ,{children:[t.jsx(d.lX,{children:w("Email")}),t.jsx(d.NI,{children:t.jsx(c.I,{type:"email",placeholder:w("Enter your email address"),...e})}),t.jsx(d.zG,{})]})}),t.jsx(d.Wi,{control:z.control,name:"phone",render:({field:e})=>(0,t.jsxs)(d.xJ,{className:"w-full",children:[t.jsx(d.lX,{children:w("Phone")}),t.jsx(d.NI,{children:t.jsx(n.E,{onChange:e.onChange,onBlur:e=>{e?z.setError("phone",{type:"custom",message:e}):z.clearErrors("phone")},options:{initialCountry:C?.countryCode}})}),t.jsx(d.zG,{})]})}),t.jsx(d.Wi,{control:z.control,name:"password",render:({field:e})=>(0,t.jsxs)(d.xJ,{children:[t.jsx(d.lX,{children:w("Password")}),t.jsx(d.NI,{children:t.jsx(i.W,{placeholder:w("Create a strong password"),...e})}),t.jsx(d.zG,{})]})}),t.jsx(d.Wi,{control:z.control,name:"confirmPassword",render:({field:e})=>(0,t.jsxs)(d.xJ,{children:[t.jsx(d.lX,{children:w("Confirm Password")}),t.jsx(d.NI,{children:t.jsx(i.W,{placeholder:w("Enter the password again"),...e})}),t.jsx(d.zG,{})]})}),t.jsx(d.Wi,{control:z.control,name:"referralCode",render:({field:e})=>(0,t.jsxs)(d.xJ,{children:[t.jsx(d.lX,{children:w("Referral (optional)")}),t.jsx(d.NI,{children:t.jsx(c.I,{placeholder:w("Enter referral code (if applicable)"),...e})}),t.jsx(d.zG,{})]})}),t.jsx(d.Wi,{control:z.control,name:"termAndCondition",render:({field:e})=>(0,t.jsxs)(d.xJ,{children:[t.jsx(d.NI,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2.5",children:[t.jsx(o.X,{id:"termAndCondition",checked:e.value,onCheckedChange:e.onChange}),t.jsx(u.Z,{className:"text-sm font-normal leading-5 text-foreground",htmlFor:"termAndCondition",children:w("I read and accept the general terms & conditions of use")})]})}),t.jsx(d.zG,{})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,t.jsxs)(l.z,{variant:"outline",type:"button",onClick:e,className:"h-10 w-[102px] text-base font-medium leading-[22px] text-foreground",children:[t.jsx(h.Z,{size:"24"}),w("Back")]}),(0,t.jsxs)(l.z,{type:"submit",className:"w-[286px] rounded-[8px] px-4 py-2 text-base font-medium leading-[22px]",children:[w("Next"),t.jsx(g.Z,{size:"16"})]})]})]})]})})}},50276:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(10326),a=r(5158),n=r(35047),i=r(17577);function l({children:e,customer:s,merchant:r,agent:l}){let o=(0,n.usePathname)();return(0,t.jsxs)("div",{className:"my-10 flex-1 overflow-y-auto px-0 py-6 md:px-6",children:[t.jsx(a.J,{condition:"/register"===o,children:e}),t.jsx(a.J,{condition:o.startsWith("/register/customer"),children:t.jsx(i.Suspense,{children:s})}),t.jsx(a.J,{condition:o.startsWith("/register/merchant"),children:(0,t.jsxs)(i.Suspense,{children:[" ",r]})}),t.jsx(a.J,{condition:o.startsWith("/register/agent"),children:(0,t.jsxs)(i.Suspense,{children:[" ",l]})})]})}},61718:(e,s,r)=>{"use strict";r.d(s,{g:()=>x});var t=r(10326),a=r(17577),n=r(92392),i=r(80609),l=r(2454),o=r(30811),d=r(1868),c=r(77863),u=r(6216),m=r(70012);function x({allCountry:e=!1,defaultValue:s,defaultCountry:r,onSelectChange:x,disabled:p=!1,triggerClassName:f,arrowClassName:h,flagClassName:g,display:j,placeholderClassName:y,align:b="start",side:N="bottom"}){let{t:v}=(0,m.$G)(),{countries:w,getCountryByCode:C,isLoading:z}=(0,d.F)(),[S,Z]=a.useState(!1),[I,P]=a.useState(s);return a.useEffect(()=>{s&&P(s)},[s]),a.useEffect(()=>{(async()=>{r&&await C(r,e=>{e&&(P(e),x(e))})})()},[r]),(0,t.jsxs)(o.J2,{open:S,onOpenChange:Z,children:[(0,t.jsxs)(o.xo,{disabled:p,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",f),children:[I?t.jsx("div",{className:"flex flex-1 items-center",children:(0,t.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[t.jsx(i.W,{className:g,countryCode:I.code?.cca2==="*"?"UN":I.code?.cca2}),void 0!==j?j(I):t.jsx("span",{children:I.name})]})}):t.jsx("span",{className:(0,c.ZP)("text-placeholder",y),children:v("Select country")}),t.jsx(u.Z,{className:(0,c.ZP)("size-6",h)})]}),t.jsx(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:b,side:N,children:(0,t.jsxs)(l.mY,{children:[t.jsx(l.sZ,{placeholder:v("Search...")}),t.jsx(l.e8,{children:(0,t.jsxs)(l.fu,{children:[z&&t.jsx(n.Loader,{}),e&&(0,t.jsxs)(l.di,{value:v("All countries"),onSelect:()=>{P({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),x({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),Z(!1)},children:[t.jsx(i.W,{countryCode:"UN"}),t.jsx("span",{className:"pl-1.5",children:v("All countries")})]}),w?.map(e=>"officially-assigned"===e.status?t.jsxs(l.di,{value:e.name,onSelect:()=>{P(e),x(e),Z(!1)},children:[t.jsx(i.W,{countryCode:e.code.cca2}),t.jsxs("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},720:(e,s,r)=>{"use strict";r.d(s,{M:()=>m});var t=r(10326),a=r(9346),n=r(30811),i=r(77863),l=r(71305),o=r(76129),d=r(17577),c=r.n(d),u=r(70012);let m=c().forwardRef(({value:e,onChange:s,className:r,placeholderClassName:d,options:m},x)=>{let{t:p}=(0,u.$G)(),[f,h]=c().useState(!1);return(0,t.jsxs)(n.J2,{open:f,onOpenChange:h,children:[(0,t.jsxs)(n.xo,{disabled:!!m?.disabled,className:(0,i.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",r),children:[t.jsx("div",{ref:x,className:"flex flex-1 items-center",children:t.jsx("div",{className:"flex flex-1 items-center gap-2 text-left",children:e?(0,l.WU)(e,"dd/MM/yyyy"):t.jsx("span",{className:(0,i.ZP)("text-placeholder",d),children:p("Pick a Date")})})}),t.jsx(o.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),t.jsx(n.yk,{className:"w-auto p-0",align:"start",children:t.jsx(a.f,{...m,mode:"single",initialFocus:!0,selected:e??void 0,onSelect:e=>{s(e),h(!1)}})})]})})},71227:(e,s,r)=>{"use strict";r.d(s,{E:()=>z});var t=r(10326),a=r(92392),n=r(80609),i=r(2454),l=r(54432),o=r(30811),d=r(1868),c=r(77863),u=r(26138),m=r(6216),x=r(33436),p=r(34197),f=r(4017),h=r(70107),g=r(55991),j=r(4981),y=r(71132),b=r(60715),N=r(5389),v=r(70012),w=r(17577);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function z({value:e,defaultValue:s="",onChange:r,onBlur:a,disabled:n,inputClassName:i,options:o}){let[d,u]=(0,w.useState)(s??""),[m,b]=(0,w.useState)(""),[v,z]=(0,w.useState)(o?.initialCountry),Z=e=>{if(e)try{let s=x.S(e,v);s?(z(s.country),b(`+${s.countryCallingCode}`),u(s.formatNational())):u(e)}catch(s){u(e)}else u(e)},I=p.L(v||o?.initialCountry||"US",N.Z);return(0,t.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(S,{country:v,disabled:n,initialCountry:o?.initialCountry,onSelect:e=>{let s=e.code.cca2?.toUpperCase(),r=f.G(s);b(`+${r}`),z(s)}}),t.jsx("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:m||`+${I?.countryCallingCode}`})]}),t.jsx(l.I,{type:"tel",className:(0,c.ZP)("rounded-l-none pl-2",i),value:d,onChange:e=>{let{value:s}=e.target,t=x.S(s,v);a?.(""),t&&h.t(s,v)&&g.q(s,v)?(z(t.country),b(`+${t.countryCallingCode}`),r?.(t.number),u(s)):(t?u(t.nationalNumber):u(s),r?.(s))},onPaste:e=>{let s=e.clipboardData.getData("Text"),t=x.S(s);if(t&&h.t(s))Z(t.formatNational()),z(t.country),b(`+${t.countryCallingCode}`),r?.(t.number),a?.("");else{let e=x.S(s,v);e&&h.t(s,v)&&(Z(e.formatNational()),r?.(e.number),a?.(""))}},onBlur:()=>{if(d&&!j.y(d,v)){let e=y.d(d,v);e&&a?.(C[e])}},placeholder:I?.formatNational(),disabled:n})]})}function S({initialCountry:e,country:s,onSelect:r,disabled:a}){let[i,l]=(0,w.useState)(!1);return(0,t.jsxs)(o.J2,{open:i,onOpenChange:l,children:[(0,t.jsxs)(o.xo,{disabled:a,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[e||s?t.jsx(n.W,{countryCode:s||e,className:"aspect-auto h-[18px] w-7 flex-1"}):t.jsx(u.Z,{}),t.jsx(m.Z,{variant:"Bold",size:16})]}),t.jsx(o.yk,{align:"start",className:"h-fit p-0",children:t.jsx(Z,{defaultValue:s||e,onSelect:e=>{r(e),l(!1)}})})]})}function Z({defaultValue:e,onSelect:s}){let{countries:r,isLoading:l}=(0,d.F)(),{t:o}=(0,v.$G)();return(0,t.jsxs)(i.mY,{children:[t.jsx(i.sZ,{placeholder:o("Search country by name"),className:"placeholder:text-input-placeholder"}),t.jsx(i.e8,{children:t.jsx(i.fu,{children:l?t.jsx(i.di,{children:t.jsx(a.Loader,{})}):r.filter(e=>{let s=e.code.cca2?.toUpperCase();return b.o().includes(s)})?.map(r=>t.jsxs(i.di,{value:r.name,"data-active":r.code.cca2===e,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>s(r),children:[t.jsx(n.W,{countryCode:r.code.cca2}),r.name]},r.code.ccn3))})})]})}},29612:(e,s,r)=>{"use strict";r.d(s,{W:()=>c});var t=r(10326),a=r(17577),n=r(90772),i=r(54432),l=r(77863),o=r(9489),d=r(75138);let c=a.forwardRef(({className:e,type:s,...r},c)=>{let[u,m]=a.useState(!1);return(0,t.jsxs)("div",{className:"relative",children:[t.jsx(i.I,{type:u?"text":"password",className:(0,l.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:c,...r}),t.jsx(n.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),m(e=>!e)},children:u?t.jsx(o.Z,{}):t.jsx(d.Z,{})})]})});c.displayName="PasswordInput"},80609:(e,s,r)=>{"use strict";r.d(s,{W:()=>i});var t=r(10326),a=r(77863),n=r(46226);function i({countryCode:e,className:s,url:r}){return e||r?t.jsx(n.default,{src:r??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,a.ZP)("rounded-[2px]",s)}):null}},9346:(e,s,r)=>{"use strict";r.d(s,{f:()=>c});var t=r(10326),a=r(11890),n=r(39183),i=r(941);r(17577);var l=r(18493),o=r(90772),d=r(77863);function c({className:e,classNames:s,showOutsideDays:r=!0,...c}){return t.jsx(l._W,{showOutsideDays:r,className:(0,d.ZP)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,d.ZP)((0,o.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.ZP)((0,o.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:({...e})=>t.jsx(a.Z,{className:"h-4 w-4"}),IconRight:({...e})=>t.jsx(n.Z,{className:"h-4 w-4"}),Dropdown:({...e})=>(0,t.jsxs)("div",{className:"relative",children:[t.jsx("select",{...e,style:{opacity:0,position:"absolute"}}),(0,t.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[t.jsx("span",{className:"text-sm",children:e.caption}),t.jsx(i.Z,{className:"size-3"})]})]})},...c})}c.displayName="Calendar"},53313:(e,s,r)=>{"use strict";r.d(s,{X:()=>o});var t=r(10326),a=r(13635),n=r(32933),i=r(17577),l=r(77863);let o=i.forwardRef(({className:e,...s},r)=>t.jsx(a.fC,{ref:r,className:(0,l.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:t.jsx(a.z$,{className:(0,l.ZP)("flex items-center justify-center text-current"),children:t.jsx(n.Z,{className:"h-4 w-4"})})}));o.displayName=a.fC.displayName},55632:(e,s,r)=>{"use strict";r.d(s,{NI:()=>h,Wi:()=>u,l0:()=>d,lX:()=>f,xJ:()=>p,zG:()=>g});var t=r(10326),a=r(34214),n=r(17577),i=r(74723),l=r(31048),o=r(77863);let d=i.RV,c=n.createContext({}),u=({...e})=>t.jsx(c.Provider,{value:{name:e.name},children:t.jsx(i.Qr,{...e})}),m=()=>{let e=n.useContext(c),s=n.useContext(x),{getFieldState:r,formState:t}=(0,i.Gc)(),a=r(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...a}},x=n.createContext({}),p=n.forwardRef(({className:e,...s},r)=>{let a=n.useId();return t.jsx(x.Provider,{value:{id:a},children:t.jsx("div",{ref:r,className:(0,o.ZP)("space-y-2",e),...s})})});p.displayName="FormItem";let f=n.forwardRef(({className:e,required:s,...r},a)=>{let{error:n,formItemId:i}=m();return t.jsx("span",{children:t.jsx(l.Z,{ref:a,className:(0,o.ZP)(n&&"text-base font-medium text-destructive",e),htmlFor:i,...r})})});f.displayName="FormLabel";let h=n.forwardRef(({...e},s)=>{let{error:r,formItemId:n,formDescriptionId:i,formMessageId:l}=m();return t.jsx(a.g7,{ref:s,id:n,"aria-describedby":r?`${i} ${l}`:`${i}`,"aria-invalid":!!r,...e})});h.displayName="FormControl",n.forwardRef(({className:e,...s},r)=>{let{formDescriptionId:a}=m();return t.jsx("p",{ref:r,id:a,className:(0,o.ZP)("text-sm text-muted-foreground",e),...s})}).displayName="FormDescription";let g=n.forwardRef(({className:e,children:s,...r},a)=>{let{error:n,formMessageId:i}=m(),l=n?String(n?.message):s;return l?t.jsx("p",{ref:a,id:i,className:(0,o.ZP)("text-sm font-medium text-destructive",e),...r,children:l}):null});g.displayName="FormMessage"},54432:(e,s,r)=>{"use strict";r.d(s,{I:()=>i});var t=r(10326),a=r(17577),n=r(77863);let i=a.forwardRef(({className:e,type:s,...r},a)=>t.jsx("input",{type:s,className:(0,n.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:a,...r}));i.displayName="Input"},31048:(e,s,r)=>{"use strict";r.d(s,{Z:()=>c});var t=r(10326),a=r(34478),n=r(79360),i=r(17577),l=r(77863);let o=(0,n.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef(({className:e,...s},r)=>t.jsx(a.f,{ref:r,className:(0,l.ZP)(o(),e),...s}));d.displayName=a.f.displayName;let c=d},88846:(e,s,r)=>{"use strict";r.d(s,{E:()=>o,m:()=>d});var t=r(10326),a=r(17577),n=r(18623),i=r(53982),l=r(77863);let o=a.forwardRef(({className:e,...s},r)=>t.jsx(n.fC,{className:(0,l.ZP)("grid gap-2",e),...s,ref:r}));o.displayName=n.fC.displayName;let d=a.forwardRef(({className:e,...s},r)=>t.jsx(n.ck,{ref:r,className:(0,l.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:t.jsx(n.z$,{className:"flex items-center justify-center",children:t.jsx(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));d.displayName=n.ck.displayName},8281:(e,s,r)=>{"use strict";r.d(s,{Z:()=>o});var t=r(10326),a=r(90220),n=r(17577),i=r(77863);let l=n.forwardRef(({className:e,orientation:s="horizontal",decorative:r=!0,...n},l)=>t.jsx(a.f,{ref:l,decorative:r,orientation:s,className:(0,i.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...n}));l.displayName=a.f.displayName;let o=l},94989:(e,s,r)=>{"use strict";r.d(s,{Pq:()=>d,Pr:()=>u,jd:()=>o,o8:()=>c});var t=r(71305),a=r(49547),n=r(98196),i=r(50833);let l=e=>{let s={...e,email:e.email,password:e.password,passwordConfirmation:e.confirmPassword,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,firstName:e.firstName,lastName:e.lastName,phone:e.phone,gender:e.title.toLowerCase(),dob:(0,t.WU)(e.dateOfBirth,"yyyy-MM-dd"),roleId:e.accountType,acceptTermsCondition:e.termAndCondition};return void 0!==e.merchant?{...s,merchant:{...e.merchant,name:e.merchant.name,email:e.merchant.email,proof:e.merchant.license,addressLine:e.merchant.street,zipCode:e.merchant.zipCode,countryCode:e.merchant.country,city:e.merchant.city}}:void 0!==e.agent?{...s,agent:{...e.agent,proof:"agent",occupation:e.agent.occupation,email:e.email,name:e.agent.name,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,whatsapp:e.agent.whatsapp,agreeFundingCustomer:e.agent.fundingByAgentAccount?.toLowerCase()==="yes",agreeHonest:e.agent.honestyAgreement?.toLowerCase()==="yes",agreeRechargeCustomer:e.agent.rechargeAgreement?.toLowerCase()==="yes"}}:s};async function o(e){try{let s=await a.Z.post(`${n.rH.API_URL}/auth/register`,l(e));return{statusCode:s.status,statusText:s.statusText,status:201===s.status||200===s.status,message:s.data?.message??"",data:{email:e.email}}}catch(t){let e=500,s="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(t)&&(e=t.response?.status??500,s=t.response?.statusText??"Internal Server Error",r=t.response?.data?.message??t.response?.data?.messages?.[0]?.message??t.message),{statusCode:e,statusText:s,status:!1,message:r}}}async function d(e){try{let s=await a.Z.post(`${n.rH.API_URL}/auth/register`,l(e));return{statusCode:s.status,statusText:s.statusText,status:201===s.status||200===s.status,message:s.data?.message??"",data:{email:e.email}}}catch(t){let e=500,s="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(t)?(e=t.response?.status??500,s=t.response?.statusText??"Internal Server Error",r=t.response?.data?.message??t.message):t instanceof Error&&(r=t.message),{statusCode:e,statusText:s,status:!1,message:r}}}async function c(e){try{let s=await a.Z.post(`${n.rH.API_URL}/auth/register`,l(e));return{statusCode:s.status,statusText:s.statusText,status:201===s.status||200===s.status,message:s.data?.message??"",data:{email:e.email}}}catch(t){let e=500,s="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(t)?(e=t.response?.status??500,s=t.response?.statusText??"Internal Server Error",r=t.response?.data?.message??t.message):t instanceof Error&&(r=t.message),{statusCode:e,statusText:s,status:!1,message:r}}}async function u(e){try{let s=await a.Z.post(`${n.rH.API_URL}/auth/resend-verify-email`,{email:e});return{statusCode:s.status,statusText:s.statusText,status:201===s.status||200===s.status,message:s.data?.message??""}}catch(t){let e=500,s="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(t)?(e=t.response?.status??500,s=t.response?.statusText??"Internal Server Error",r=t.response?.data?.message??t.message):t instanceof Error&&(r=t.message),{statusCode:e,statusText:s,status:!1,message:r}}}},1868:(e,s,r)=>{"use strict";r.d(s,{F:()=>d});class t{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var a=r(44099),n=r(85999),i=r(84455);let l=a.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),o="name,cca2,ccn3,cca3,status,flag,flags";function d(){let{data:e,isLoading:s,...r}=(0,i.ZP)(`/all?fields=${o}`,e=>l.get(e)),d=e?.data,c=async(e,s)=>{try{let r=await l.get(`/alpha/${e.toLowerCase()}?fields=${o}`),a=r.data?new t(r.data):null;s(a)}catch(e){a.default.isAxiosError(e)&&n.toast.error("Failed to fetch country")}};return{countries:d?d.map(e=>new t(e)):[],isLoading:s,getCountryByCode:c,...r}}},85319:(e,s,r)=>{"use strict";r.d(s,{Zg:()=>n,cN:()=>a,yC:()=>i,z1:()=>l});var t=r(27256);let a=t.z.object({firstName:t.z.string().min(1,"First name is required."),lastName:t.z.string().min(1,"Last name is required."),email:t.z.string().email({message:"Invalid email address."}),phone:t.z.string().min(1,"Phone number is required."),password:t.z.string({required_error:"Password is required"}).min(8,"Your password must be at least 8 characters long"),confirmPassword:t.z.string({required_error:"Confirm password is required"}).min(8,"Password is required."),referralCode:t.z.string().optional(),termAndCondition:t.z.literal(!0,{errorMap:()=>({message:"You must accept our terms & conditions"})})}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),n=t.z.object({title:t.z.string().min(1,"Title is required."),dateOfBirth:t.z.date({required_error:"A date of birth is required."}),street:t.z.string().min(1,"Street is required."),country:t.z.string().min(1,"Country is required."),city:t.z.string().min(1,"City is required."),zipCode:t.z.string().min(1,"Zip code is required.")}),i=t.z.object({name:t.z.string({required_error:"Full name is required."}).min(1,"Full name is required."),email:t.z.string({required_error:"Email address is required."}).email({message:"Invalid email address."}),license:t.z.string().min(1,"Merchant license is required."),street:t.z.string({required_error:"Street is required"}).min(1,"Street is required."),country:t.z.string({required_error:"Country is required"}).min(1,"Country is required."),city:t.z.string({required_error:"City is required"}).min(1,"City is required."),zipCode:t.z.string({required_error:"Zip code is required"}).min(1,"Zip code is required.")}),l=t.z.object({name:t.z.string({required_error:"Full name is required."}).min(1,"Full name is required."),occupation:t.z.string({required_error:"Occupation is required."}).min(1,"Occupation is required."),whatsapp:t.z.string({required_error:"WhatsApp link is required."}).min(1,"WhatsApp link is required.")})},44713:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(auth)\layout.tsx#default`)},13958:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(19510),a=r(79365);function n(){return t.jsx(a.Z,{})}},18204:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(auth)\register\(tabs)\layout.tsx#default`)},18437:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(19510),a=r(48413);function n(){return t.jsx("div",{className:"flex items-center justify-center py-10",children:t.jsx(a.a,{})})}},4782:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(19510),a=r(19856);function n(){return(0,t.jsxs)("div",{className:"container max-w-2xl",children:[t.jsx(a.O,{className:"mb-2 h-3 w-full max-w-[500px]"}),t.jsx(a.O,{className:"mb-2 h-7 w-full max-w-[300px]"}),t.jsx(a.O,{className:"my-6 h-[1px] w-full"}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[t.jsx(a.O,{className:"mb-2 aspect-square w-full"}),t.jsx(a.O,{className:"mb-2 aspect-square w-full"}),t.jsx(a.O,{className:"mb-2 aspect-square w-full"})]})]})}},48413:(e,s,r)=>{"use strict";r.d(s,{a:()=>t});let t=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\Loader.tsx#Loader`)},19856:(e,s,r)=>{"use strict";r.d(s,{O:()=>i});var t=r(19510);process.env.SESSION_SECRET;var a=r(55761);r(51032);var n=r(62386);function i({className:e,...s}){return t.jsx("div",{className:function(...e){return(0,n.m6)((0,a.W)(e))}("animate-pulse rounded-md bg-muted",e),...s})}}};