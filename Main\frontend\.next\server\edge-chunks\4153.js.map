{"version": 3, "file": "edge-chunks/4153.js", "mappings": "8NAWA,IAAMA,EAAW,IAAIC,EAAAA,CAAQA,CAEd,SAASC,EAAkB,CACxCC,KAAAA,CAAI,CACJC,KAAAA,CAAI,CACJC,UAAAA,CAAS,CAKV,EACC,GAAM,CAACC,EAASC,EAAW,CAAGC,EAAAA,QAAc,CAAe,EAAE,EACvD,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAEd,MACE,GAAAC,EAAAC,GAAA,EAACC,EAAAA,CAASA,CAAAA,CACRV,KAAMA,EACNG,QAASA,EACTC,WAAYA,EACZF,UAAWA,EACXS,WAAY,CACVC,MAAOX,GAAMW,MACbC,KAAMZ,GAAMa,YACZC,MAAOd,GAAMe,OACf,EACAC,UAAW,CACT,CACEC,GAAI,QACJC,OAAQb,EAAE,UACVc,KAAM,GACJ,GAAAZ,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,mDACbC,EAAKC,GAAG,CAACC,QAAQ,EAAEC,OAG1B,EACA,CACER,GAAI,YACJC,OAAQb,EAAE,QACVc,KAAM,IACJ,IAAMO,EAAYJ,EAAKC,GAAG,CAACC,QAAQ,EAAEE,iBAErC,EAIE,GAAAnB,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,wEACbM,CAAAA,EAAAA,EAAAA,EAAAA,EAAOD,EAAW,yBAJd,GAAAnB,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,+BAAsB,SAOjD,CACF,EACA,CACEJ,GAAI,OACJC,OAAQb,EAAE,QACVc,KAAM,GACJ,GAAAZ,EAAAC,GAAA,EAACoB,MAAAA,UACC,GAAArB,EAAAC,GAAA,EAACqB,IAAAA,CAAER,UAAU,iDACVS,CAAAA,EAAAA,EAAAA,EAAAA,EAAUR,EAAKC,GAAG,CAACC,QAAQ,EAAEO,SAItC,EAEA,CACEd,GAAI,SACJC,OAAQb,EAAE,UACVc,KAAM,IACJ,IAAMa,EAASV,EAAKC,GAAG,CAACC,QAAQ,EAAEQ,aAElC,cAAIA,EACK,GAAAzB,EAAAC,GAAA,EAACyB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,mBAAW7B,EAAE,cAGjC2B,WAAAA,EACK,GAAAzB,EAAAC,GAAA,EAACyB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,uBAAe7B,EAAE,YAGlC,GAAAE,EAAAC,GAAA,EAACyB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,qBAAa7B,EAAE,YACvC,CACF,EAEA,CACEY,GAAI,SACJC,OAAQb,EAAE,UACVc,KAAM,CAAC,CAAEI,IAAAA,CAAG,CAAE,IACZ,IAAMxB,EAAOwB,GAAKC,SACZW,EAAOpC,GAAMoC,MAAQC,KAAKC,KAAK,CAACtC,EAAKoC,IAAI,EAEzCG,EAAWvC,GAAMwC,UAAYH,KAAKC,KAAK,CAACtC,GAAMwC,UAEpD,MACE,GAAAhC,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,8EACbmB,CAAAA,EAAAA,EAAAA,EAAAA,EAAMzC,GACJ0C,IAAI,CAAC,CAAEV,KAAM,UAAW,EAAG,IAC1BnC,EAAS+B,MAAM,CACbW,GAAUI,WACVJ,GAAUK,eAGbF,IAAI,CAAC,CAAEV,KAAM,SAAU,EAAG,IACzBnC,EAAS+B,MAAM,CAAC5B,EAAK6C,MAAM,CAAEN,GAAU1C,WAExCiD,SAAS,CAAC,IACTjD,EAAS+B,MAAM,CAAC5B,EAAK6C,MAAM,CAAET,GAAMvC,YAI7C,CACF,EAEA,CACEqB,GAAI,MACJC,OAAQb,EAAE,OACVc,KAAM,CAAC,CAAEI,IAAAA,CAAG,CAAE,IACZ,IAAMxB,EAAOwB,GAAKC,SACZW,EAAOpC,GAAMoC,MAAQC,KAAKC,KAAK,CAACtC,EAAKoC,IAAI,EACzCG,EAAWvC,GAAMwC,UAAYH,KAAKC,KAAK,CAACtC,GAAMwC,UAEpD,MACE,GAAAhC,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,8EACbmB,CAAAA,EAAAA,EAAAA,EAAAA,EAAMzC,GACJ0C,IAAI,CAAC,CAAEV,KAAM,UAAW,EAAG,IAC1BnC,EAAS+B,MAAM,CAAC5B,GAAM+C,IAAKR,GAAU1C,WAEtC6C,IAAI,CAAC,CAAEV,KAAM,SAAU,EAAG,IACzBnC,EAAS+B,MAAM,CAAC5B,EAAK+C,GAAG,CAAER,GAAU1C,WAErCiD,SAAS,CAAC,IACTjD,EAAS+B,MAAM,CAAC5B,EAAK+C,GAAG,CAAEX,GAAMvC,YAI1C,CACF,EAEA,CACEqB,GAAI,mBACJC,OAAQb,EAAE,oBACVc,KAAM,CAAC,CAAEI,IAAAA,CAAG,CAAE,IACZ,IAAMxB,EAAOwB,GAAKC,SACZuB,EAAKhD,GAAMgD,IAAMX,KAAKC,KAAK,CAACtC,EAAKgD,EAAE,EACnCT,EAAWvC,GAAMwC,UAAYH,KAAKC,KAAK,CAACtC,GAAMwC,UAEpD,MACE,GAAAhC,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,8EACbmB,CAAAA,EAAAA,EAAAA,EAAAA,EAAMzC,GACJ0C,IAAI,CAAC,CAAEV,KAAM,UAAW,EAAG,IAC1BnC,EAAS+B,MAAM,CAAC5B,EAAKY,KAAK,CAAE2B,GAAUU,aAEvCP,IAAI,CAAC,CAAEV,KAAM,SAAU,EAAG,IACzBnC,EAAS+B,MAAM,CAAC5B,EAAKY,KAAK,CAAE2B,GAAU1C,WAEvCiD,SAAS,CAAC,IACTjD,EAAS+B,MAAM,CAAC5B,EAAKY,KAAK,CAAEoC,GAAInD,YAI1C,CACF,EAEA,CACEqB,GAAI,SACJC,OAAQb,EAAE,UACVc,KAAM,CAAC,CAAEI,IAAAA,CAAG,CAAE,IACZ,IAAMxB,EAAOwB,EAAIC,QAAQ,QACzB,GAAWyB,OAGT,GAAA1C,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,sEACbtB,EAAKkD,MAAM,GAHP,GAAA1C,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,+BAAsB,OAMjD,CACF,EACA,CACEJ,GAAI,QACJC,OAAQb,EAAE,SACVc,KAAM,CAAC,CAAEI,IAAAA,CAAG,CAAE,IACZ,IAAMxB,EAAOwB,EAAIC,QAAQ,CAEnBuB,EAAKhD,GAAMgD,IAAMX,KAAKC,KAAK,CAACtC,EAAKgD,EAAE,SAEzC,GAAWhD,UAAAA,EAAKkD,MAAM,CAUpB,GAAA1C,EAAA2C,IAAA,EAACtB,MAAAA,CAAIP,UAAU,0DACb,GAAAd,EAAA2C,IAAA,EAACC,EAAAA,EAAMA,CAAAA,CAAC9B,UAAU,+CAChB,GAAAd,EAAAC,GAAA,EAAC4C,EAAAA,EAAWA,CAAAA,CAACC,IAAKN,EAAGO,KAAK,CAAEC,IAAKR,EAAGS,KAAK,GACzC,GAAAjD,EAAA2C,IAAA,EAACO,EAAAA,EAAcA,CAAAA,WACZ,IACAC,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBX,EAAGS,KAAK,EAAG,UAGlC,GAAAjD,EAAAC,GAAA,EAACY,OAAAA,UAAM2B,EAAGS,KAAK,MAhBf,GAAAjD,EAAA2C,IAAA,EAACtB,MAAAA,CAAIP,UAAU,0DACb,GAAAd,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,yCAChB,GAAAd,EAAAC,GAAA,EAACY,OAAAA,UAAK,UAiBd,CACF,EACA,CACEH,GAAI,WACJC,OAAQb,EAAE,YACVc,KAAM,CAAC,CAAEI,IAAAA,CAAG,CAAE,IACZ,IAAMxB,EAAOwB,EAAIC,QAAQ,CAEnBuB,EAAKhD,GAAMgD,IAAMX,KAAKC,KAAK,CAACtC,EAAKgD,EAAE,SAEzC,GAAWhD,UAAAA,EAAKkD,MAAM,CAUpB,GAAA1C,EAAA2C,IAAA,EAACtB,MAAAA,CAAIP,UAAU,0DACb,GAAAd,EAAA2C,IAAA,EAACC,EAAAA,EAAMA,CAAAA,CAAC9B,UAAU,+CAChB,GAAAd,EAAAC,GAAA,EAAC4C,EAAAA,EAAWA,CAAAA,CAACC,IAAKN,EAAGO,KAAK,CAAEC,IAAKR,EAAGS,KAAK,GACzC,GAAAjD,EAAAC,GAAA,EAACiD,EAAAA,EAAcA,CAAAA,UAAEC,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBX,EAAGS,KAAK,OAE7C,GAAAjD,EAAAC,GAAA,EAACY,OAAAA,UAAM2B,EAAGS,KAAK,MAbf,GAAAjD,EAAA2C,IAAA,EAACtB,MAAAA,CAAIP,UAAU,0DACb,GAAAd,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,yCAChB,GAAAd,EAAAC,GAAA,EAACY,OAAAA,UAAK,UAcd,CACF,EACD,EAGP,yEClPO,SAASuC,EAAW,CACzBC,MAAAA,CAAK,CACLC,MAAAA,CAAK,CACL7B,OAAAA,CAAM,CACN8B,KAAAA,CAAI,CACJC,UAAAA,CAAS,CACTC,YAAAA,CAAW,CACX3C,UAAAA,CAAS,CACTpB,UAAAA,CAAS,CAUV,SACC,EACS,GAAAM,EAAAC,GAAA,EAACyD,EAAAA,CAAQA,CAAAA,CAAC5C,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,GAAI7C,KAInC,GAAAd,EAAA2C,IAAA,EAACtB,MAAAA,CACCP,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,EACT,mFACA7C,aAGF,GAAAd,EAAAC,GAAA,EAACoB,MAAAA,CACCP,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,EACT,2EACAH,YAGDD,EAAK,CAAEK,KAAM,GAAIjC,QAAS,SAAU,KAEvC,GAAA3B,EAAA2C,IAAA,EAACtB,MAAAA,CAAIP,UAAU,kCACb,GAAAd,EAAAC,GAAA,EAAC4D,KAAAA,UAAIP,IACL,GAAAtD,EAAA2C,IAAA,EAAC9B,OAAAA,CAAKC,UAAU,gDAAuCuC,EAAM,OAC7D,GAAArD,EAAAC,GAAA,EAAC6D,KAAAA,CAAGhD,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,kCAAmCF,YAClDhC,SAKX,2OCpBe,SAASsC,EAA0B,CAChDC,OAAAA,CAAM,CAGP,EACC,IAAMC,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,IACf,CAAEpE,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACR,CAACoE,EAAMC,EAAQ,CAAGvE,EAAAA,QAAc,CAAC,IAEvC,MACE,GAAAG,EAAA2C,IAAA,EAAC0B,EAAAA,EAAOA,CAAAA,CAACF,KAAMA,EAAMG,aAAcF,YACjC,GAAApE,EAAA2C,IAAA,EAAC4B,EAAAA,EAAcA,CAAAA,CAACzD,UAAU,yHACxB,GAAAd,EAAAC,GAAA,EAACY,OAAAA,CAAKC,UAAU,oFACbmD,EAAaO,GAAG,CAAC,QACdjD,CAAAA,EAAAA,EAAAA,EAAAA,EAAU0C,EAAaO,GAAG,CAAC,SAC3B1E,EAAE,sBAER,GAAAE,EAAAC,GAAA,EAACwE,EAAAA,CAAUA,CAAAA,CACTb,KAAK,KACLc,YAAa,IACb5D,UAAU,2BAId,GAAAd,EAAAC,GAAA,EAAC0E,EAAAA,EAAcA,CAAAA,CACb7D,UAAU,6CACV8D,MAAM,iBAEN,GAAA5E,EAAAC,GAAA,EAAC4E,EAAAA,EAAOA,CAAAA,CAAC/D,UAAU,eACjB,GAAAd,EAAAC,GAAA,EAAC6E,EAAAA,EAAWA,CAAAA,CAAChE,UAAU,yBACrB,GAAAd,EAAA2C,IAAA,EAACoC,EAAAA,EAAYA,CAAAA,CAACjE,UAAU,gBACtB,GAAAd,EAAAC,GAAA,EAACoB,MAAAA,CAAIP,UAAU,uBACb,GAAAd,EAAAC,GAAA,EAACqB,IAAAA,CAAER,UAAU,iEACVhB,EAAE,mCAIP,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IAAMlB,EAAO,OAAQ,GAAI,IAAMI,EAAQ,KACjDtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACkF,EAAAA,CAAIA,CAAAA,CAACvB,KAAM,KACX9D,EAAE,uBAGL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IAAMlB,EAAO,OAAQ,UAAW,IAAMI,EAAQ,KACxDtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACmF,EAAAA,CAAGA,CAAAA,CAACxB,KAAM,KACV9D,EAAE,eAGL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IACRlB,EAAO,OAAQ,WAAY,IAAMI,EAAQ,KAE3CtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACoF,EAAAA,CAAUA,CAAAA,CAACzB,KAAM,KACjB9D,EAAE,eAGL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IACRlB,EAAO,OAAQ,WAAY,IAAMI,EAAQ,KAE3CtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACqF,EAAAA,CAAOA,CAAAA,CAAC1B,KAAK,OACb9D,EAAE,gBAGL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IACRlB,EAAO,OAAQ,WAAY,IAAMI,EAAQ,KAE3CtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACsF,EAAAA,CAAMA,CAAAA,CAAC3B,KAAK,OACZ9D,EAAE,eAGL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IAAMlB,EAAO,OAAQ,UAAW,IAAMI,EAAQ,KACxDtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACuF,EAAAA,CAAWA,CAAAA,CAAC5B,KAAK,OACjB9D,EAAE,cAGL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IAAMlB,EAAO,OAAQ,UAAW,IAAMI,EAAQ,KACxDtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACwF,EAAAA,CAAWA,CAAAA,CAAC7B,KAAK,OACjB9D,EAAE,eAEL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IACRlB,EAAO,OAAQ,aAAc,IAAMI,EAAQ,KAE7CtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACyF,EAAAA,CAAIA,CAAAA,CAAC9B,KAAK,OACV9D,EAAE,kBAEL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IACRlB,EAAO,OAAQ,oBAAqB,IAAMI,EAAQ,KAEpDtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAACyF,EAAAA,CAAIA,CAAAA,CAAC9B,KAAK,OACV9D,EAAE,wBAEL,GAAAE,EAAAC,GAAA,EAAC+E,EAAAA,EAAgBA,CAAAA,CAAClE,UAAU,kBAE5B,GAAAd,EAAA2C,IAAA,EAACsC,EAAAA,EAAWA,CAAAA,CACVC,SAAU,IACRlB,EAAO,OAAQ,iBAAkB,IAAMI,EAAQ,KAEjDtD,UAAU,qGAEV,GAAAd,EAAAC,GAAA,EAAC0F,EAAAA,CAAKA,CAAAA,CAAC/B,KAAK,OACX9D,EAAE,gCAQnB,8DCrLA,SAAS4D,EAAS,CAChB5C,UAAAA,CAAS,CACT,GAAG8E,EACkC,EACrC,MACE,GAAA5F,EAAAC,GAAA,EAACoB,MAAAA,CACCP,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,oCAAqC7C,GAClD,GAAG8E,CAAK,EAGf", "sources": ["webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/_components/transactions-table.tsx", "webpack://_N_E/./components/common/ReportCard.tsx", "webpack://_N_E/./components/common/TransactionCategoryFilter.tsx", "webpack://_N_E/./components/ui/skeleton.tsx"], "sourcesContent": ["import DataTable from \"@/components/common/DataTable\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Currency, startCase } from \"@/lib/utils\";\r\nimport { getAvatarFallback } from \"@/utils/getAvatarFallback\";\r\nimport { SortingState } from \"@tanstack/react-table\";\r\nimport { format } from \"date-fns\";\r\nimport React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { match } from \"ts-pattern\";\r\n\r\nconst currency = new Currency();\r\n\r\nexport default function TransactionsTable({\r\n  data,\r\n  meta,\r\n  isLoading,\r\n}: {\r\n  data: any;\r\n  meta: any;\r\n  isLoading: boolean;\r\n}) {\r\n  const [sorting, setSorting] = React.useState<SortingState>([]);\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <DataTable\r\n      data={data}\r\n      sorting={sorting}\r\n      setSorting={setSorting}\r\n      isLoading={isLoading}\r\n      pagination={{\r\n        total: meta?.total,\r\n        page: meta?.currentPage,\r\n        limit: meta?.perPage,\r\n      }}\r\n      structure={[\r\n        {\r\n          id: \"trxId\",\r\n          header: t(\"Trx ID\"),\r\n          cell: (args: any) => (\r\n            <span className=\"text-sm font-normal text-secondary-text\">\r\n              {args.row.original?.trxId}\r\n            </span>\r\n          ),\r\n        },\r\n        {\r\n          id: \"createdAt\",\r\n          header: t(\"Date\"),\r\n          cell: (args: any) => {\r\n            const createdAt = args.row.original?.createdAt;\r\n\r\n            if (!createdAt)\r\n              return <span className=\"text-secondary-text\"> N/A </span>;\r\n\r\n            return (\r\n              <span className=\"block w-24 text-sm font-normal leading-5 text-secondary-text\">\r\n                {format(createdAt, \"dd MMM yyyy hh:mm b\")}\r\n              </span>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          id: \"type\",\r\n          header: t(\"Type\"),\r\n          cell: (args: any) => (\r\n            <div>\r\n              <p className=\"text-xs font-bold text-secondary-text\">\r\n                {startCase(args.row.original?.type)}\r\n              </p>\r\n            </div>\r\n          ),\r\n        },\r\n\r\n        {\r\n          id: \"status\",\r\n          header: t(\"Status\"),\r\n          cell: (args: any) => {\r\n            const status = args.row.original?.status;\r\n\r\n            if (status === \"completed\") {\r\n              return <Badge variant=\"success\">{t(\"Complete\")}</Badge>;\r\n            }\r\n\r\n            if (status === \"failed\") {\r\n              return <Badge variant=\"destructive\">{t(\"Failed\")}</Badge>;\r\n            }\r\n\r\n            return <Badge variant=\"secondary\">{t(\"Pending\")}</Badge>;\r\n          },\r\n        },\r\n\r\n        {\r\n          id: \"amount\",\r\n          header: t(\"Amount\"),\r\n          cell: ({ row }) => {\r\n            const data = row?.original;\r\n            const from = data?.from && JSON.parse(data.from);\r\n\r\n            const metadata = data?.metaData && JSON.parse(data?.metaData);\r\n\r\n            return (\r\n              <span className=\"leading-20 whitespace-nowrap text-sm font-semibold text-foreground\">\r\n                {match(data)\r\n                  .with({ type: \"exchange\" }, () =>\r\n                    currency.format(\r\n                      metadata?.amountFrom,\r\n                      metadata?.currencyFrom,\r\n                    ),\r\n                  )\r\n                  .with({ type: \"deposit\" }, () =>\r\n                    currency.format(data.amount, metadata?.currency),\r\n                  )\r\n                  .otherwise(() =>\r\n                    currency.format(data.amount, from?.currency as string),\r\n                  )}\r\n              </span>\r\n            );\r\n          },\r\n        },\r\n\r\n        {\r\n          id: \"fee\",\r\n          header: t(\"Fee\"),\r\n          cell: ({ row }) => {\r\n            const data = row?.original;\r\n            const from = data?.from && JSON.parse(data.from);\r\n            const metadata = data?.metaData && JSON.parse(data?.metaData);\r\n\r\n            return (\r\n              <span className=\"leading-20 whitespace-nowrap text-sm font-semibold text-foreground\">\r\n                {match(data)\r\n                  .with({ type: \"exchange\" }, () =>\r\n                    currency.format(data?.fee, metadata?.currency),\r\n                  )\r\n                  .with({ type: \"deposit\" }, () =>\r\n                    currency.format(data.fee, metadata?.currency),\r\n                  )\r\n                  .otherwise(() =>\r\n                    currency.format(data.fee, from?.currency as string),\r\n                  )}\r\n              </span>\r\n            );\r\n          },\r\n        },\r\n\r\n        {\r\n          id: \"after_processing\",\r\n          header: t(\"After Processing\"),\r\n          cell: ({ row }) => {\r\n            const data = row?.original;\r\n            const to = data?.to && JSON.parse(data.to);\r\n            const metadata = data?.metaData && JSON.parse(data?.metaData);\r\n\r\n            return (\r\n              <span className=\"leading-20 whitespace-nowrap text-sm font-semibold text-foreground\">\r\n                {match(data)\r\n                  .with({ type: \"exchange\" }, () =>\r\n                    currency.format(data.total, metadata?.currencyTo),\r\n                  )\r\n                  .with({ type: \"deposit\" }, () =>\r\n                    currency.format(data.total, metadata?.currency),\r\n                  )\r\n                  .otherwise(() =>\r\n                    currency.format(data.total, to?.currency as string),\r\n                  )}\r\n              </span>\r\n            );\r\n          },\r\n        },\r\n\r\n        {\r\n          id: \"method\",\r\n          header: t(\"Method\"),\r\n          cell: ({ row }) => {\r\n            const data = row.original;\r\n            if (!data?.method)\r\n              return <span className=\"text-sm font-normal\">N/A</span>;\r\n            return (\r\n              <span className=\"line-clamp-2 w-[100px] text-sm font-normal text-foreground\">\r\n                {data.method}\r\n              </span>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          id: \"agent\",\r\n          header: t(\"Agent\"),\r\n          cell: ({ row }) => {\r\n            const data = row.original;\r\n\r\n            const to = data?.to && JSON.parse(data.to);\r\n\r\n            if (!to || data.method !== \"agent\") {\r\n              return (\r\n                <div className=\"text-sm font-normal leading-5 text-foreground\">\r\n                  <span className=\"block size-5 rounded-full bg-primary\" />\r\n                  <span>N/A</span>\r\n                </div>\r\n              );\r\n            }\r\n\r\n            return (\r\n              <div className=\"text-sm font-normal leading-5 text-foreground\">\r\n                <Avatar className=\"size-7 border-2 border-primary p-1\">\r\n                  <AvatarImage src={to.image} alt={to.label} />\r\n                  <AvatarFallback>\r\n                    {\" \"}\r\n                    {getAvatarFallback(to.label)}{\" \"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span>{to.label}</span>\r\n              </div>\r\n            );\r\n          },\r\n        },\r\n        {\r\n          id: \"merchant\",\r\n          header: t(\"Merchant\"),\r\n          cell: ({ row }) => {\r\n            const data = row.original;\r\n\r\n            const to = data?.to && JSON.parse(data.to);\r\n\r\n            if (!to || data.method === \"agent\") {\r\n              return (\r\n                <div className=\"text-sm font-normal leading-5 text-foreground\">\r\n                  <span className=\"block size-5 rounded-full bg-primary\" />\r\n                  <span>N/A</span>\r\n                </div>\r\n              );\r\n            }\r\n\r\n            return (\r\n              <div className=\"text-sm font-normal leading-5 text-foreground\">\r\n                <Avatar className=\"size-7 border-2 border-primary p-1\">\r\n                  <AvatarImage src={to.image} alt={to.label} />\r\n                  <AvatarFallback>{getAvatarFallback(to.label)}</AvatarFallback>\r\n                </Avatar>\r\n                <span>{to.label}</span>\r\n              </div>\r\n            );\r\n          },\r\n        },\r\n      ]}\r\n    />\r\n  );\r\n}\r\n", "import { Skeleton } from \"@/components/ui/skeleton\";\r\nimport cn from \"@/lib/utils\";\r\nimport { IconProps } from \"iconsax-react\";\r\nimport React from \"react\";\r\n\r\nexport function ReportCard({\r\n  title,\r\n  value,\r\n  status,\r\n  icon,\r\n  iconClass,\r\n  statusClass,\r\n  className,\r\n  isLoading,\r\n}: {\r\n  title: string;\r\n  value: string;\r\n  status: string;\r\n  iconClass?: string;\r\n  statusClass?: string;\r\n  className?: string;\r\n  isLoading?: boolean;\r\n  icon: (props: IconProps) => React.ReactElement;\r\n}) {\r\n  if (isLoading) {\r\n    return <Skeleton className={cn(\"\", className)} />;\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default\",\r\n        className,\r\n      )}\r\n    >\r\n      <div\r\n        className={cn(\r\n          \"flex h-[54px] w-[54px] items-center justify-center rounded-full bg-muted\",\r\n          iconClass,\r\n        )}\r\n      >\r\n        {icon({ size: 34, variant: \"Outline\" })}\r\n      </div>\r\n      <div className=\"flex flex-col gap-y-2\">\r\n        <h1>{value}</h1>\r\n        <span className=\"block text-xs font-normal leading-4\">{title} </span>\r\n        <h6 className={cn(\"text-sm font-semibold leading-5\", statusClass)}>\r\n          {status}\r\n        </h6>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport {\r\n  Command,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandList,\r\n  CommandSeparator,\r\n} from \"@/components/ui/command\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { startCase } from \"@/lib/utils\";\r\nimport {\r\n  Add,\r\n  ArrowDown2,\r\n  ArrowRight,\r\n  FlashCircle,\r\n  Receive,\r\n  Repeat,\r\n  Share,\r\n  ShoppingBag,\r\n  Tree,\r\n} from \"iconsax-react\";\r\nimport { Menu } from \"lucide-react\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nexport default function TransactionCategoryFilter({\r\n  filter,\r\n}: {\r\n  filter: (type: string, value: string, callback: () => void) => void;\r\n}) {\r\n  const searchParams = useSearchParams();\r\n  const { t } = useTranslation();\r\n  const [open, setOpen] = React.useState(false);\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger className=\"flex h-10 w-full items-center gap-2 rounded-sm bg-background px-3 text-foreground shadow-defaultLite sm:w-72\">\r\n        <span className=\"line-clamp-1 flex-1 text-left font-medium leading-[22px] text-foreground\">\r\n          {searchParams.get(\"type\")\r\n            ? startCase(searchParams.get(\"type\") as string)\r\n            : t(\"All Transactions\")}\r\n        </span>\r\n        <ArrowDown2\r\n          size=\"24\"\r\n          strokeWidth={1.5}\r\n          className=\"text-secondary-text\"\r\n        />\r\n      </PopoverTrigger>\r\n\r\n      <PopoverContent\r\n        className=\"w-[var(--radix-popover-trigger-width)] p-0\"\r\n        align=\"start\"\r\n      >\r\n        <Command className=\"p-1\">\r\n          <CommandList className=\"max-h-[450px]\">\r\n            <CommandGroup className=\"p-0\">\r\n              <div className=\"px-2 py-1.5\">\r\n                <p className=\"text-[10px] font-normal leading-4 text-secondary-text\">\r\n                  {t(\"Select what you want to see\")}\r\n                </p>\r\n              </div>\r\n\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() => filter(\"type\", \"\", () => setOpen(false))}\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <Menu size={24} />\r\n                {t(\"All Transactions\")}\r\n              </CommandItem>\r\n\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() => filter(\"type\", \"deposit\", () => setOpen(false))}\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <Add size={24} />\r\n                {t(\"Deposits\")}\r\n              </CommandItem>\r\n\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() =>\r\n                  filter(\"type\", \"transfer\", () => setOpen(false))\r\n                }\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <ArrowRight size={24} />\r\n                {t(\"Transfer\")}\r\n              </CommandItem>\r\n\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() =>\r\n                  filter(\"type\", \"withdraw\", () => setOpen(false))\r\n                }\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <Receive size=\"24\" />\r\n                {t(\"Withdraws\")}\r\n              </CommandItem>\r\n\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() =>\r\n                  filter(\"type\", \"exchange\", () => setOpen(false))\r\n                }\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <Repeat size=\"24\" />\r\n                {t(\"Exchange\")}\r\n              </CommandItem>\r\n\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() => filter(\"type\", \"payment\", () => setOpen(false))}\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <ShoppingBag size=\"24\" />\r\n                {t(\"Payment\")}\r\n              </CommandItem>\r\n\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() => filter(\"type\", \"service\", () => setOpen(false))}\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <FlashCircle size=\"24\" />\r\n                {t(\"Services\")}\r\n              </CommandItem>\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() =>\r\n                  filter(\"type\", \"investment\", () => setOpen(false))\r\n                }\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <Tree size=\"24\" />\r\n                {t(\"Investments\")}\r\n              </CommandItem>\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() =>\r\n                  filter(\"type\", \"investment_return\", () => setOpen(false))\r\n                }\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <Tree size=\"24\" />\r\n                {t(\"Investment return\")}\r\n              </CommandItem>\r\n              <CommandSeparator className=\"mb-1 mt-[5px]\" />\r\n\r\n              <CommandItem\r\n                onSelect={() =>\r\n                  filter(\"type\", \"referral_bonus\", () => setOpen(false))\r\n                }\r\n                className=\"mx-0 flex items-center gap-2 rounded-none px-4 py-2 text-base font-medium leading-[22px]\"\r\n              >\r\n                <Share size=\"24\" />\r\n                {t(\"Referral bonus\")}\r\n              </CommandItem>\r\n            </CommandGroup>\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n", "import cn from \"@/lib/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": ["currency", "<PERSON><PERSON><PERSON><PERSON>", "TransactionsTable", "data", "meta", "isLoading", "sorting", "setSorting", "React", "t", "useTranslation", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "DataTable", "pagination", "total", "page", "currentPage", "limit", "perPage", "structure", "id", "header", "cell", "span", "className", "args", "row", "original", "trxId", "createdAt", "format", "div", "p", "startCase", "type", "status", "Badge", "variant", "from", "JSON", "parse", "metadata", "metaData", "match", "with", "amountFrom", "currencyFrom", "amount", "otherwise", "fee", "to", "currencyTo", "method", "jsxs", "Avatar", "AvatarImage", "src", "image", "alt", "label", "AvatarFallback", "getAvatar<PERSON><PERSON><PERSON>", "ReportCard", "title", "value", "icon", "iconClass", "statusClass", "Skeleton", "cn", "size", "h1", "h6", "TransactionCategoryFilter", "filter", "searchParams", "useSearchParams", "open", "<PERSON><PERSON><PERSON>", "Popover", "onOpenChange", "PopoverTrigger", "get", "ArrowDown2", "strokeWidth", "PopoverC<PERSON>nt", "align", "Command", "CommandList", "CommandGroup", "CommandSeparator", "CommandItem", "onSelect", "<PERSON><PERSON>", "Add", "ArrowRight", "Receive", "Repeat", "ShoppingBag", "FlashCircle", "Tree", "Share", "props"], "sourceRoot": ""}