"use client";

import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Progress from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Zap,
  BarChart3,
  Activity
} from "lucide-react";

import { DashboardPerformanceMetrics } from "@/types/admin";

interface PerformanceMetricsCardProps {
  metrics: DashboardPerformanceMetrics;
}

export function PerformanceMetricsCard({ metrics }: PerformanceMetricsCardProps) {
  const { t } = useTranslation();

  const getPerformanceColor = (value: number, threshold: number) => {
    return value <= threshold ? 'text-green-600' : 'text-red-600';
  };

  const getPerformanceBadgeColor = (value: number, threshold: number) => {
    return value <= threshold 
      ? 'bg-green-100 text-green-800 border-green-200' 
      : 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>{t('Performance Metrics')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Response Time */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('Response Time')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-semibold ${getPerformanceColor(metrics.responseTime, 200)}`}>
                {metrics.responseTime}ms
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${getPerformanceBadgeColor(metrics.responseTime, 200)}`}
              >
                {metrics.responseTime <= 200 ? t('Good') : t('Slow')}
              </Badge>
            </div>
          </div>
          <Progress 
            value={Math.min((metrics.responseTime / 500) * 100, 100)} 
            className="h-2"
          />
        </div>

        {/* Uptime */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('Uptime')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-semibold ${getPerformanceColor(100 - metrics.uptime, 5)}`}>
                {metrics.uptime.toFixed(2)}%
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${getPerformanceBadgeColor(100 - metrics.uptime, 5)}`}
              >
                {metrics.uptime >= 99.5 ? t('Excellent') : 
                 metrics.uptime >= 99 ? t('Good') : t('Poor')}
              </Badge>
            </div>
          </div>
          <Progress 
            value={metrics.uptime} 
            className="h-2"
          />
        </div>

        {/* Error Rate */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('Error Rate')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-semibold ${getPerformanceColor(metrics.errorRate, 1)}`}>
                {metrics.errorRate.toFixed(2)}%
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${getPerformanceBadgeColor(metrics.errorRate, 1)}`}
              >
                {metrics.errorRate <= 0.5 ? t('Excellent') : 
                 metrics.errorRate <= 1 ? t('Good') : t('High')}
              </Badge>
            </div>
          </div>
          <Progress 
            value={Math.min((metrics.errorRate / 5) * 100, 100)} 
            className="h-2"
          />
        </div>

        {/* System Resources */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">{t('System Resources')}</h4>
          
          {/* CPU Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">{t('CPU Usage')}</span>
              <span className={`text-xs font-medium ${getPerformanceColor(metrics.cpuUsage, 80)}`}>
                {metrics.cpuUsage}%
              </span>
            </div>
            <Progress value={metrics.cpuUsage} className="h-1" />
          </div>

          {/* Memory Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">{t('Memory Usage')}</span>
              <span className={`text-xs font-medium ${getPerformanceColor(metrics.memoryUsage, 80)}`}>
                {metrics.memoryUsage}%
              </span>
            </div>
            <Progress value={metrics.memoryUsage} className="h-1" />
          </div>

          {/* Disk Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">{t('Disk Usage')}</span>
              <span className={`text-xs font-medium ${getPerformanceColor(metrics.diskUsage, 85)}`}>
                {metrics.diskUsage}%
              </span>
            </div>
            <Progress value={metrics.diskUsage} className="h-1" />
          </div>
        </div>

        {/* Network Performance */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('Network Latency')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-semibold ${getPerformanceColor(metrics.networkLatency, 50)}`}>
                {metrics.networkLatency}ms
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${getPerformanceBadgeColor(metrics.networkLatency, 50)}`}
              >
                {metrics.networkLatency <= 30 ? t('Excellent') : 
                 metrics.networkLatency <= 50 ? t('Good') : t('Slow')}
              </Badge>
            </div>
          </div>
          <Progress 
            value={Math.min((metrics.networkLatency / 100) * 100, 100)} 
            className="h-2"
          />
        </div>
      </CardContent>
    </Card>
  );
} 