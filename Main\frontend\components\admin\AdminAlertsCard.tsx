"use client";

import { useTranslation } from "react-i18next";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  AlertTriangle, 
  Shield, 
  DollarSign, 
  Users, 
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Bell
} from "lucide-react";

interface Alert {
  id: string;
  type: 'security' | 'financial' | 'system' | 'user' | 'performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  timestamp: string;
  isResolved: boolean;
  actionRequired: boolean;
  affectedUsers?: number;
  estimatedImpact?: string;
}

interface AdminAlertsCardProps {
  alerts: Alert[];
  onResolveAlert?: (alertId: string) => void;
  onDismissAlert?: (alertId: string) => void;
}

export function AdminAlertsCard({ 
  alerts, 
  onResolveAlert, 
  onDismissAlert 
}: AdminAlertsCardProps) {
  const { t } = useTranslation();

  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'security':
        return <Shield className="h-4 w-4" />;
      case 'financial':
        return <DollarSign className="h-4 w-4" />;
      case 'system':
        return <Activity className="h-4 w-4" />;
      case 'user':
        return <Users className="h-4 w-4" />;
      case 'performance':
        return <Activity className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: Alert['severity']) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSeverityBadgeColor = (severity: Alert['severity']) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return t('Just now');
    if (diffInMinutes < 60) return t('{{minutes}}m ago', { minutes: diffInMinutes });
    if (diffInMinutes < 1440) return t('{{hours}}h ago', { hours: Math.floor(diffInMinutes / 60) });
    return date.toLocaleDateString();
  };

  const activeAlerts = alerts.filter(alert => !alert.isResolved);
  const resolvedAlerts = alerts.filter(alert => alert.isResolved);

  const criticalAlerts = activeAlerts.filter(alert => alert.severity === 'critical');
  const highAlerts = activeAlerts.filter(alert => alert.severity === 'high');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Bell className="h-5 w-5" />
          <span>{t('System Alerts')}</span>
          {activeAlerts.length > 0 && (
            <Badge variant="destructive" className="ml-2">
              {activeAlerts.length}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Critical Alerts Summary */}
        {criticalAlerts.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t('{{count}} critical alerts require immediate attention', { 
                count: criticalAlerts.length 
              })}
            </AlertDescription>
          </Alert>
        )}

        {/* High Priority Alerts Summary */}
        {highAlerts.length > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t('{{count}} high priority alerts need attention', { 
                count: highAlerts.length 
              })}
            </AlertDescription>
          </Alert>
        )}

        {/* Active Alerts */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">{t('Active Alerts')}</h4>
          
          {activeAlerts.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <p>{t('No active alerts')}</p>
            </div>
          ) : (
            activeAlerts.map((alert) => (
              <div 
                key={alert.id} 
                className={`p-3 rounded-lg border ${getSeverityColor(alert.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="text-sm font-medium">{alert.title}</h5>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getSeverityBadgeColor(alert.severity)}`}
                        >
                          {t(alert.severity)}
                        </Badge>
                        {alert.actionRequired && (
                          <Badge variant="destructive" className="text-xs">
                            {t('Action Required')}
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-xs mb-2">{alert.description}</p>
                      
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatTimestamp(alert.timestamp)}</span>
                        </div>
                        
                        {alert.affectedUsers && (
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3" />
                            <span>{t('{{count}} users affected', { count: alert.affectedUsers })}</span>
                          </div>
                        )}
                        
                        {alert.estimatedImpact && (
                          <div className="flex items-center space-x-1">
                            <Activity className="h-3 w-3" />
                            <span>{alert.estimatedImpact}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {onResolveAlert && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onResolveAlert(alert.id)}
                      >
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {t('Resolve')}
                      </Button>
                    )}
                    {onDismissAlert && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onDismissAlert(alert.id)}
                      >
                        <XCircle className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Resolved Alerts */}
        {resolvedAlerts.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">{t('Recently Resolved')}</h4>
            <div className="space-y-2">
              {resolvedAlerts.slice(0, 3).map((alert) => (
                <div key={alert.id} className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{alert.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatTimestamp(alert.timestamp)}
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                    {t('Resolved')}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Alert Statistics */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div className="text-center">
              <p className="font-medium">{activeAlerts.length}</p>
              <p className="text-muted-foreground">{t('Active')}</p>
            </div>
            <div className="text-center">
              <p className="font-medium">{resolvedAlerts.length}</p>
              <p className="text-muted-foreground">{t('Resolved')}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 