exports.id=1770,exports.ids=[1770],exports.modules={35571:(e,s,t)=>{Promise.resolve().then(t.bind(t,8704))},94973:(e,s,t)=>{Promise.resolve().then(t.bind(t,48263))},96779:(e,s,t)=>{Promise.resolve().then(t.bind(t,93754))},15260:(e,s,t)=>{Promise.resolve().then(t.bind(t,27337))},72200:(e,s,t)=>{Promise.resolve().then(t.bind(t,10271))},20376:(e,s,t)=>{Promise.resolve().then(t.bind(t,66539))},64202:(e,s,t)=>{Promise.resolve().then(t.bind(t,10673))},8704:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(10326),r=t(56140),i=t(63761),n=t(28758),l=t(567),d=t(90772),o=t(33071),c=t(75584),u=t(77863),x=t(54033),m=t(3001),f=t(90434),g=t(35047),p=t(17577),h=t.n(p),j=t(70012);function b(){let{t:e}=(0,j.$G)(),s=(0,g.useSearchParams)(),[t,p]=h().useState([]),[b,v]=h().useState(s.get("search")??""),N=(0,g.useRouter)(),y=(0,g.usePathname)(),{data:S,meta:w,isLoading:P}=(0,c.Z)(`/admin/gateways?${s.toString()}`);return(0,a.jsxs)(o.Zb,{className:"rounded-xl",children:[(0,a.jsxs)(o.Ol,{className:"flex w-full flex-wrap items-start justify-between py-4 sm:flex-row sm:items-center",children:[a.jsx(o.ll,{className:"flex-1 text-base font-medium leading-[22px]",children:e("Deposit/Payment Gateways")}),a.jsx(i.R,{value:b,onChange:e=>{e.preventDefault();let s=(0,u.w4)(e.target.value);v(e.target.value),N.replace(`${y}?${s.toString()}`)},iconPlacement:"end",placeholder:e("Search...")})]}),a.jsx(o.aY,{className:"border-t border-divider py-4",children:a.jsx("div",{className:"flex flex-col gap-4",children:a.jsx(r.Z,{data:S,sorting:t,setSorting:p,isLoading:P,pagination:{total:w?.total,page:w?.currentPage,limit:w?.perPage},structure:[{id:"name",header:e("Name"),cell:({row:e})=>(0,a.jsxs)(f.default,{href:`/settings/gateways/${e.original?.id}?name=${e.original?.value}`,className:"flex items-center gap-2",children:[(0,a.jsxs)(n.qE,{children:[a.jsx(n.F$,{src:(0,u.qR)(e.original?.logoImage)}),a.jsx(n.Q5,{className:"font-semibold",children:(0,x.v)(e.original?.name)})]}),a.jsx("span",{className:"text-secondary-text hover:text-primary hover:underline",children:e.original?.name})]})},{id:"status",header:e("Status"),cell:({row:s})=>a.jsx(l.C,{variant:s.original?.active?"success":"secondary",children:s.original?.active?e("Active"):e("Inactive")})},{id:"recommended",header:e("Recommended"),cell:({row:s})=>a.jsx(l.C,{variant:s.original?.recommended?"important":"secondary",children:s.original?.recommended?e("Yes"):e("No")})},{id:"menu",header:e("Menu"),cell:({row:e})=>a.jsx(d.z,{size:"icon",variant:"outline",className:"size-8 bg-background p-1.5 text-primary",children:a.jsx(f.default,{href:`/settings/gateways/${e.original?.id}?name=${e.original?.value}`,children:a.jsx(m.Z,{size:"20",className:"text-primary"})})})}]})})})]})}},48263:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>u});var a=t(10326),r=t(33646),i=t(53844),n=t(88589),l=t(40508),d=t(20187),o=t(50493),c=t(70012);function u(){let{t:e}=(0,c.$G)(),s=[{title:e("Account Settings"),icon:a.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:e("Charges/Commissions"),icon:a.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/fees-commissions",id:"fees-commissions"},{title:e("KYC Verification"),icon:a.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Methods"),icon:a.jsx(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/methods",id:"methods"},{title:e("Login Sessions"),icon:a.jsx(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return a.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:a.jsx(r.a,{tabs:s})})}},93754:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(10326),r=t(92392),i=t(47752),n=t(5158),l=t(56672),d=t(90772),o=t(55632),c=t(54432),u=t(49547),x=t(10734);async function m(e){try{let s=await u.Z.put("/agents/update",{occupation:e.occupation,whatsapp:e.whatsapp});return(0,x.B)(s)}catch(e){return(0,x.D)(e)}}var f=t(74064),g=t(44284),p=t(17577),h=t(74723),j=t(70012),b=t(85999),v=t(84077),N=t(27256);let y=N.z.object({occupation:N.z.string({required_error:"Occupation is required."}),whatsapp:N.z.string({required_error:"Whatsapp is required."}),agentId:N.z.string({required_error:"Agent ID is required."})});function S({agent:e,isLoading:s}){let{t}=(0,j.$G)(),[i,u]=(0,p.useTransition)(),x=(0,h.cI)({resolver:(0,f.F)(y),defaultValues:{occupation:"",whatsapp:"",agentId:""}});return!e&&s?a.jsx("div",{className:"flex w-full items-center justify-center py-10",children:a.jsx(r.Loader,{})}):a.jsx(o.l0,{...x,children:a.jsx("form",{onSubmit:x.handleSubmit(e=>{u(async()=>{let s=await m(e);s&&s.status?((0,v.j)("/agents/detail"),b.toast.success(s.message)):b.toast.error(t(s.message))})}),className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)(l.Qd,{value:"AGENT_INFORMATION",className:"border-none px-4 py-0",children:[a.jsx(l.o4,{className:"py-6 hover:no-underline",children:a.jsx("p",{className:"text-base font-medium leading-[22px]",children:t("Agent profile")})}),(0,a.jsxs)(l.vF,{className:"flex flex-col gap-6 border-t px-1 py-4",children:[a.jsx(o.Wi,{control:x.control,name:"occupation",render:({field:e})=>(0,a.jsxs)(o.xJ,{children:[a.jsx(o.lX,{children:t("Job/Occupation")}),a.jsx(o.NI,{children:a.jsx(c.I,{type:"text",placeholder:t("Enter your job"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),a.jsx(o.zG,{})]})}),a.jsx(o.Wi,{control:x.control,name:"whatsapp",render:({field:e})=>(0,a.jsxs)(o.xJ,{children:[a.jsx(o.lX,{children:t("WhatsApp number/link")}),a.jsx(o.NI,{children:a.jsx(c.I,{type:"text",placeholder:t("Enter your WhatsApp account number or link"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),a.jsx(o.zG,{})]})}),a.jsx(o.Wi,{control:x.control,name:"agentId",render:({field:e})=>(0,a.jsxs)(o.xJ,{children:[a.jsx(o.lX,{children:t("Agent ID")}),a.jsx(o.NI,{children:a.jsx(c.I,{type:"text",disabled:!0,className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),a.jsx(o.zG,{})]})}),a.jsx("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,a.jsxs)(d.z,{disabled:i,children:[(0,a.jsxs)(n.J,{condition:!i,children:[t("Save"),a.jsx(g.Z,{size:20})]}),a.jsx(n.J,{condition:i,children:a.jsx(r.Loader,{title:t("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}var w=t(72847),P=t(71380),Z=t(38853),I=t(41122);function k(){let{t:e}=(0,j.$G)(),{data:s,user:t,address:n,isLoading:d,error:o}=(0,I.h)(),{agent:c,isLoading:u}=(0,Z.T)();return o?a.jsx("div",{className:"w-full bg-danger py-2.5 text-danger-foreground",children:a.jsx("p",{children:e("We encountered an issue while retrieving the requested data. Please try again later or contact support if the problem persists.")})}):d?a.jsx("div",{className:"flex w-full items-center justify-center py-10",children:a.jsx(r.Loader,{})}):a.jsx(l.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","AGENT_INFORMATION","PASSWORD_INFORMATION","ADDRESS_INFORMATION"],children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[a.jsx(P.O,{user:t(s),isLoading:d,error:o}),a.jsx(S,{agent:c,isLoading:u}),a.jsx(i.h,{address:n(s)}),a.jsx(w.G,{})]})})}},27337:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>o});var a=t(10326),r=t(33646),i=t(53844),n=t(40508),l=t(50493),d=t(70012);function o(){let{t:e}=(0,d.$G)(),s=[{title:e("Account Settings"),icon:a.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings/",id:"__DEFAULT__"},{title:e("KYC Verification"),icon:a.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Login Sessions"),icon:a.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return a.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-black/[8%] bg-white p-4",children:a.jsx(r.a,{tabs:s})})}},10271:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(10326),r=t(47752),i=t(72847),n=t(71380),l=t(56672),d=t(49547),o=t(13263),c=t(72450),u=t(84455);function x(){let{data:e,isLoading:s,error:t}=(0,u.ZP)("/customers/detail",e=>d.Z.get(e)),x=e?.data;return a.jsx(l.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","PASSWORD_INFORMATION","ADDRESS_INFORMATION"],children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[a.jsx(n.O,{user:x?new c.n({...x,...x?.user,address:x?.address,avatar:x?.profileImage}):null,isLoading:s,error:t}),a.jsx(r.h,{address:new o.k(x?.address)}),a.jsx(i.G,{})]})})}},66539:(e,s,t)=>{"use strict";t.d(s,{Tabbar:()=>x});var a=t(10326),r=t(33646),i=t(53844),n=t(40508),l=t(9155),d=t(418),o=t(26138),c=t(50493),u=t(70012);function x(){let{t:e}=(0,u.$G)(),s=[{title:e("Account Settings"),icon:a.jsx(i.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:e("KYC Verification"),icon:a.jsx(n.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Merchant Settings"),icon:a.jsx(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/merchant-settings",id:"merchant-settings"},{title:e("MPay API"),icon:a.jsx(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/mpay-api",id:"mpay-api"},{title:e("Webhook URL"),icon:a.jsx(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/webhook-url-settings",id:"webhook-url-settings"},{title:e("Login Sessions"),icon:a.jsx(c.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return a.jsx("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:a.jsx(r.a,{tabs:s})})}},10673:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(10326),r=t(92392),i=t(47752),n=t(72847),l=t(71380),d=t(56672),o=t(41122),c=t(70012);function u(){let{t:e}=(0,c.$G)(),{data:s,user:t,address:u,isLoading:x,error:m}=(0,o.h)();return!s&&m?a.jsx("div",{className:"w-full bg-danger py-2.5 text-danger-foreground",children:a.jsx("p",{children:e("We encountered an issue while retrieving the requested data. Please try again later or contact support if the problem persists.")})}):!s&&x?a.jsx("div",{className:"flex w-full items-center justify-center py-10",children:a.jsx(r.Loader,{})}):a.jsx(d.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","PASSWORD_INFORMATION","ADDRESS_INFORMATION"],children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[a.jsx(l.O,{user:t(s),isLoading:x,error:m}),a.jsx(i.h,{address:u(s)}),a.jsx(n.G,{})]})})}},56140:(e,s,t)=>{"use strict";t.d(s,{Z:()=>y});var a=t(10326),r=t(77863),i=t(86508),n=t(11798),l=t(77132),d=t(6216),o=t(75817),c=t(40420),u=t(35047),x=t(93327),m=t(17577),f=t(70012),g=t(90772);let p=m.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,r.ZP)("w-full caption-bottom text-sm",e),...s})}));p.displayName="Table";let h=m.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,r.ZP)("",e),...s}));h.displayName="TableHeader";let j=m.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,r.ZP)("[&_tr:last-child]:border-0",e),...s}));j.displayName="TableBody",m.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,r.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let b=m.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,r.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));b.displayName="TableRow";let v=m.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,r.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));v.displayName="TableHead";let N=m.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,r.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));function y({data:e,isLoading:s=!1,structure:t,sorting:y,setSorting:S,padding:w=!1,className:P,onRefresh:Z,pagination:I}){let k=(0,m.useMemo)(()=>t,[t]),R=(0,u.useRouter)(),A=(0,u.usePathname)(),D=(0,u.useSearchParams)(),{t:z}=(0,f.$G)(),T=(0,i.b7)({data:e||[],columns:k,state:{sorting:y,onRefresh:Z},onSortingChange:S,getCoreRowModel:(0,n.sC)(),getSortedRowModel:(0,n.tj)(),debugTable:!1});return s?a.jsx("div",{className:"rounded-md bg-background p-10",children:a.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:z("Loading...")})}):e?.length?(0,a.jsxs)("div",{className:(0,r.ZP)(`${w?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,P),children:[(0,a.jsxs)(p,{children:[a.jsx(h,{children:T.getHeaderGroups().map(e=>a.jsx(b,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>a.jsx(v,{className:(0,r.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,a.jsxs)(g.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[z((0,i.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:a.jsx(d.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:a.jsx(d.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??a.jsx(d.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),a.jsx(j,{children:T.getRowModel().rows.map(e=>a.jsx(b,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>a.jsx(N,{className:"py-3 text-sm font-semibold",children:(0,i.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),I&&I.total>10&&a.jsx("div",{className:"pb-2 pt-6",children:a.jsx(x.Z,{showTotal:(e,s)=>z("Showing {{start}}-{{end}} of {{total}}",{start:s[0],end:s[1],total:e}),align:"start",current:I?.page,total:I?.total,pageSize:I?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let s=new URLSearchParams(D);s.set("page",e.toString()),R.push(`${A}?${s.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>a.jsx("a",{...e,children:a.jsx(o.Z,{size:"18"})}),nextIcon:e=>a.jsx("a",{...e,children:a.jsx(c.Z,{size:"18"})})})})]}):a.jsx("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(l.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),z("No data found!")]})})}N.displayName="TableCell",m.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,r.ZP)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},63761:(e,s,t)=>{"use strict";t.d(s,{R:()=>l});var a=t(10326);t(17577);var r=t(54432),i=t(77863),n=t(32894);function l({iconPlacement:e="start",className:s,containerClass:t,...l}){return(0,a.jsxs)("div",{className:(0,i.ZP)("relative flex items-center",t),children:[a.jsx(n.Z,{size:"20",className:(0,i.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),a.jsx(r.I,{type:"text",className:(0,i.ZP)("h-10","end"===e?"pr-10":"pl-10",s),...l})]})}},33071:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>l,SZ:()=>o,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>d});var a=t(10326),r=t(17577),i=t(77863);let n=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let l=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,i.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,i.ZP)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.ZP)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},38853:(e,s,t)=>{"use strict";t.d(s,{T:()=>r});var a=t(90799);function r(){let{data:e,error:s,isLoading:t}=(0,a.d)("/agents/detail");return{agent:e?.data,isLoading:t,error:s}}},75584:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});var a=t(90799),r=t(35047);function i(e,s){let t=(0,r.usePathname)(),i=(0,r.useSearchParams)(),n=(0,r.useRouter)(),[l,d]=e.split("?"),o=new URLSearchParams(d);o.has("page")||o.set("page","1"),o.has("limit")||o.set("limit","10");let c=`${l}?${o.toString()}`,{data:u,error:x,isLoading:m,mutate:f,...g}=(0,a.d)(c,s);return{refresh:()=>f(u),data:u?.data?.data??[],meta:u?.data?.meta,filter:(e,s,a)=>{let r=new URLSearchParams(i.toString());s?r.set(e,s.toString()):r.delete(e),n.replace(`${t}?${r.toString()}`),a?.()},isLoading:m,error:x,...g}}},96640:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\page.tsx#default`)},84514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(40099),i=t(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(i.Z,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},18406:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},40903:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510);t(71159);let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,a.jsxs)("div",{className:"",children:[a.jsx(r,{}),a.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},285:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center",children:a.jsx(r.a,{})})}},41932:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\settings\page.tsx#default`)},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(40099),i=t(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(i.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},48218:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510);t(71159);let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,a.jsxs)("div",{children:[a.jsx(r,{}),a.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},80161:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center py-10",children:a.jsx(r.a,{})})}},20522:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\settings\page.tsx#default`)},80482:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510);t(71159);let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\settings\_components\Tabbar.tsx#Tabbar`);function i({children:e}){return(0,a.jsxs)("div",{children:[a.jsx(r,{}),a.jsx("div",{className:"p-4 md:pb-20",children:e})]})}},7016:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(48413);function i(){return a.jsx("div",{className:"flex justify-center",children:a.jsx(r.a,{})})}},29231:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\settings\page.tsx#default`)}};