exports.id=2007,exports.ids=[2007],exports.modules={26807:(e,t,n)=>{Promise.resolve().then(n.bind(n,73e3)),Promise.resolve().then(n.bind(n,75285))},95948:(e,t,n)=>{"use strict";n.d(t,{Z:()=>f});var r=n(10326),s=n(56038),a=n(56140),i=n(567),l=n(90772),o=n(77863),c=n(71305),d=n(9489),m=n(90434),u=n(17577),g=n.n(u),h=n(70012);let x=new o.F;function f({data:e,meta:t,isLoading:n,refresh:u}){let[f,p]=g().useState([]),{t:k}=(0,h.$G)();return r.jsx(a.Z,{data:e?e?.map(e=>e):[],isLoading:n,sorting:f,setSorting:p,onRefresh:u,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"trxId",header:k("Trx ID"),cell:({row:e})=>r.jsx(m.default,{href:`/deposits/${e.original?.id}`,className:"text-xs font-normal text-foreground hover:underline",children:e.original.trxId})},{id:"createdAt",header:k("Date"),cell:({row:e})=>(0,r.jsxs)("div",{children:[r.jsx("span",{className:"block min-w-24 text-sm font-normal leading-5 text-foreground",children:(0,c.WU)(e.original.createdAt,"dd MMM yyyy;")}),r.jsx("span",{className:"block min-w-24 text-sm font-normal leading-5 text-foreground",children:(0,c.WU)(e.original.createdAt,"hh:mm a")})]})},{id:"status",header:k("Status"),cell:({row:e})=>"completed"===e.original.status?r.jsx(i.C,{variant:"success",children:(0,o.fl)(e.original.status)}):"pending"===e.original.status?r.jsx(i.C,{variant:"secondary",className:"bg-muted",children:(0,o.fl)(e.original.status)}):"failed"===e.original.status?r.jsx(i.C,{variant:"destructive",children:(0,o.fl)(e.original.status)}):r.jsx(i.C,{variant:"secondary",className:"bg-muted",children:k("Pending")})},{id:"amount",header:k("Amount"),cell:({row:e})=>{let t;return t="exchange"===e.original.type?e.original?.metaData?.currencyFrom:"deposit"===e.original.type?e.original?.metaData?.currency:e.original?.from?.currency,r.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:x.format(e.original.amount,t)})}},{id:"fee",header:k("Fee"),cell:({row:e})=>{let t;return t="exchange"===e.original.type?e.original?.metaData?.currencyFrom:"deposit"===e.original.type?e.original?.metaData?.currency:e.original?.from?.currency,r.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:x.format(e.original.fee,t)})}},{id:"after_processing",header:k("After Processing"),cell:({row:e})=>{let t;return t="exchange"===e.original.type?e.original?.metaData?.currencyFrom:"deposit"===e.original.type?e.original?.metaData?.currency:e.original?.from?.currency,r.jsx("span",{className:"leading-20 text-sm font-semibold text-foreground",children:x.format(e.original.total,t)})}},{id:"method",header:k("Method"),cell:({row:e})=>r.jsx("span",{className:"line-clamp-2 w-[100px] text-sm font-normal capitalize text-foreground",children:e.original.method})},{id:"user",header:k("User"),cell:({row:e})=>r.jsx(s.Z,{row:e})},{id:"view",header:k("View"),cell:({row:e})=>r.jsx("div",{className:"flex items-center gap-1",children:r.jsx(l.z,{type:"button",variant:"ghost",size:"icon",className:"hover:bg-muted",asChild:!0,children:r.jsx(m.default,{href:`/deposits/${e.original?.id}`,prefetch:!1,children:r.jsx(d.Z,{})})})})}]})}},56038:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(10326),s=n(28758),a=n(54033),i=n(90434);let l=function({row:e}){return(0,r.jsxs)(i.default,{href:(()=>{switch(e.original?.user?.roleId){case 1:return`/staffs/edit/${e.original?.user?.id}`;case 2:default:return`/customers/${e.original?.user?.customer?.id}?name=${e.original?.user?.customer?.name}&active=${e?.original?.user?.status}`;case 3:return`/merchants/${e.original?.userId}/${e.original?.user?.merchant?.id}?name=${e.original.user?.customer?.name}&active=${e?.original?.user?.status}`;case 4:return`/agents/${e.original?.userId}/${e.original?.user?.agent?.id}?name=${e.original.user?.customer?.name}&active=${e?.original?.user?.status}`}})(),className:"flex min-w-[80px] items-center gap-2 font-normal text-secondary-text hover:text-foreground",children:[(0,r.jsxs)(s.qE,{children:[r.jsx(s.F$,{src:e.original.user.customer.profileImage}),r.jsx(s.Q5,{children:(0,a.v)(e.original.user.customer.name)})]}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"text-sm font-normal",children:e.original.user.customer.name}),e.original?.user.email?r.jsx("p",{className:"text-xs font-normal",children:e.original?.user.email}):null]})]})}},75285:(e,t,n)=>{"use strict";n.d(t,{default:()=>S});var r=n(10326),s=n(5158),a=n(90772),i=n(81638),l=n(6216),o=n(90434),c=n(35047),d=n(17577);function m({sidebarItem:e}){let[t,n]=d.useState("(dashboard)"),[m,u]=d.useState(!1),{setIsExpanded:g,device:h}=(0,i.q)(),x=(0,c.useSelectedLayoutSegment)();return d.useEffect(()=>{n(x)},[]),d.useEffect(()=>{u(e.segment===x)},[x,e.segment]),(0,r.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,r.jsxs)(o.default,{href:e.link,onClick:()=>{n(e.segment),e.children?.length||"Desktop"===h||g(!1)},"data-active":x===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[r.jsx(s.J,{condition:!!e.icon,children:r.jsx("div",{"data-active":x===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),r.jsx("span",{className:"flex-1",children:e.name}),r.jsx(s.J,{condition:!!e.children?.length,children:r.jsx(a.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),u(!m)},children:r.jsx(l.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),r.jsx(s.J,{condition:!!e.children?.length,children:r.jsx("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>r.jsx("li",{children:r.jsxs(o.default,{href:e.link,"data-active":t===e.segment,onClick:()=>{n(e.segment),"Desktop"!==h&&g(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[r.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var u=n(8281),g=n(4066),h=n(77863),x=n(1178),f=n(29169),p=n(40420),k=n(78564),y=n(53105),v=n(81770),j=n(45922),b=n(29764),w=n(26920),N=n(9155),Z=n(41334),E=n(73686),z=n(75073),L=n(44221),M=n(46226),P=n(70012);function S(){let{t:e}=(0,P.$G)(),{isExpanded:t,setIsExpanded:n}=(0,i.q)(),{logo:s,siteName:l}=(0,g.T)(),c=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:r.jsx(x.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:r.jsx(f.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:r.jsx(p.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:r.jsx(k.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:r.jsx(y.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:r.jsx(v.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:r.jsx(j.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:r.jsx(b.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:r.jsx(w.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:r.jsx(N.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:r.jsx(Z.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:r.jsx(E.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:r.jsx(z.Z,{size:"20"}),link:"/settings"}]}];return(0,r.jsxs)("div",{"data-expanded":t,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[r.jsx(a.z,{size:"icon",variant:"outline",onClick:()=>n(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${t?"":"hidden"} lg:hidden`,children:r.jsx(L.Z,{})}),r.jsx("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:r.jsx(o.default,{href:"/",className:"flex items-center justify-center",children:r.jsx(M.default,{src:(0,h.qR)(s),width:160,height:40,alt:l,className:"max-h-10 object-contain"})})}),r.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:c.map(e=>(0,r.jsxs)("div",{children:[""!==e.title?r.jsx("div",{children:r.jsx(u.Z,{className:"my-4"})}):null,r.jsx("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>r.jsx("li",{children:r.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},567:(e,t,n)=>{"use strict";n.d(t,{C:()=>l});var r=n(10326),s=n(79360);n(17577);var a=n(77863);let i=(0,s.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...n}){return r.jsx("div",{className:(0,a.ZP)(i({variant:t}),e),...n})}},9489:(e,t,n)=>{"use strict";n.d(t,{Z:()=>f});var r=n(52920),s=n(17577),a=n.n(s),i=n(78439),l=n.n(i),o=["variant","color","size"],c=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M21.25 9.15C18.94 5.52 15.56 3.43 12 3.43c-1.78 0-3.51.52-5.09 1.49-1.58.98-3 2.41-4.16 4.23-1 1.57-1 4.12 0 5.69 2.31 3.64 5.69 5.72 9.25 5.72 1.78 0 3.51-.52 5.09-1.49 1.58-.98 3-2.41 4.16-4.23 1-1.56 1-4.12 0-5.69ZM12 16.04c-2.24 0-4.04-1.81-4.04-4.04S9.76 7.96 12 7.96s4.04 1.81 4.04 4.04-1.8 4.04-4.04 4.04Z",fill:t}),a().createElement("path",{d:"M11.998 9.14a2.855 2.855 0 0 0 0 5.71c1.57 0 2.86-1.28 2.86-2.85s-1.29-2.86-2.86-2.86Z",fill:t}))},d=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M9.032 14.002c-.39-.57-.61-1.26-.61-2 0-1.98 1.6-3.58 3.58-3.58s3.58 1.6 3.58 3.58-1.6 3.58-3.58 3.58",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a().createElement("path",{d:"M17.56 5.58c-1.69-1.2-3.59-1.85-5.56-1.85-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68 3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{opacity:".4",d:"M21.25 9.15C18.94 5.52 15.56 3.43 12 3.43c-1.78 0-3.51.52-5.09 1.49-1.58.98-3 2.41-4.16 4.23-1 1.57-1 4.12 0 5.69 2.31 3.64 5.69 5.72 9.25 5.72 1.78 0 3.51-.52 5.09-1.49 1.58-.98 3-2.41 4.16-4.23 1-1.56 1-4.12 0-5.69ZM12 16.04c-2.24 0-4.04-1.81-4.04-4.04S9.76 7.96 12 7.96s4.04 1.81 4.04 4.04-1.8 4.04-4.04 4.04Z",fill:t}),a().createElement("path",{d:"M11.998 9.14a2.855 2.855 0 0 0 0 5.71c1.57 0 2.86-1.28 2.86-2.85s-1.29-2.86-2.86-2.86Z",fill:t}))},u=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M15.58 12c0 1.98-1.6 3.58-3.58 3.58S8.42 13.98 8.42 12s1.6-3.58 3.58-3.58 3.58 1.6 3.58 3.58Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a().createElement("path",{d:"M12 20.27c3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19-2.29-3.6-5.58-5.68-9.11-5.68-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},g=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{d:"M12.002 16.332c-2.39 0-4.33-1.94-4.33-4.33s1.94-4.33 4.33-4.33 4.33 1.94 4.33 4.33-1.94 4.33-4.33 4.33Zm0-7.16c-1.56 0-2.83 1.27-2.83 2.83s1.27 2.83 2.83 2.83 2.83-1.27 2.83-2.83-1.27-2.83-2.83-2.83Z",fill:t}),a().createElement("path",{d:"M11.998 21.02c-3.76 0-7.31-2.2-9.75-6.02-1.06-1.65-1.06-4.34 0-6 2.45-3.82 6-6.02 9.75-6.02s7.3 2.2 9.74 6.02c1.06 1.65 1.06 4.34 0 6-2.44 3.82-5.99 6.02-9.74 6.02Zm0-16.54c-3.23 0-6.32 1.94-8.48 5.33-.75 1.17-.75 3.21 0 4.38 2.16 3.39 5.25 5.33 8.48 5.33 3.23 0 6.32-1.94 8.48-5.33.75-1.17.75-3.21 0-4.38-2.16-3.39-5.25-5.33-8.48-5.33Z",fill:t}))},h=function(e){var t=e.color;return a().createElement(a().Fragment,null,a().createElement("path",{opacity:".4",d:"M15.582 12.002c0 1.98-1.6 3.58-3.58 3.58s-3.58-1.6-3.58-3.58 1.6-3.58 3.58-3.58 3.58 1.6 3.58 3.58Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a().createElement("path",{d:"M12 20.269c3.53 0 6.82-2.08 9.11-5.68.9-1.41.9-3.78 0-5.19-2.29-3.6-5.58-5.68-9.11-5.68-3.53 0-6.82 2.08-9.11 5.68-.9 1.41-.9 3.78 0 5.19 2.29 3.6 5.58 5.68 9.11 5.68Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},x=function(e,t){switch(e){case"Bold":return a().createElement(c,{color:t});case"Broken":return a().createElement(d,{color:t});case"Bulk":return a().createElement(m,{color:t});case"Linear":default:return a().createElement(u,{color:t});case"Outline":return a().createElement(g,{color:t});case"TwoTone":return a().createElement(h,{color:t})}},f=(0,s.forwardRef)(function(e,t){var n=e.variant,s=e.color,i=e.size,l=(0,r._)(e,o);return a().createElement("svg",(0,r.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),x(n,s))});f.propTypes={variant:l().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:l().string,size:l().oneOfType([l().string,l().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="Eye"},28437:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(19510),s=n(48413);function a(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(s.a,{})})}},11840:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var r=n(19510),s=n(40099);let a=(0,n(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function i({children:e}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[r.jsx(a,{}),(0,r.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[r.jsx(s.Z,{}),r.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}n(71159)},33661:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(19510),s=n(48413);function a(){return r.jsx("div",{className:"flex items-center justify-center py-10",children:r.jsx(s.a,{})})}}};