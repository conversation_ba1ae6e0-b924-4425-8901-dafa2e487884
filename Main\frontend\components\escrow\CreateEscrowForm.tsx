"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Separator from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CreateEscrowData, FeePayer } from "@/types/escrow";
import { CreateMilestoneData } from "@/types/escrow";
import SelectRecipientCombo from "@/components/common/form/SelectRecipientCombo";
import { SelectCurrency } from "@/components/common/form/SelectCurrency";
import { Plus, Trash2, AlertTriangle, DollarSign, Clock, Shield } from "lucide-react";
import { MilestoneForm } from "./MilestoneForm";

const createEscrowSchema = z.object({
  recipientId: z.number().min(1, "Please select a recipient"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  currencyCode: z.string().min(3, "Please select a currency"),
  description: z.string().optional(),
  termsConditions: z.string().optional(),
  deadlineHours: z.number().min(1, "Deadline must be at least 1 hour").max(8760, "Deadline cannot exceed 1 year"),
  feePayer: z.enum(['sender', 'recipient', 'split']),
  hasMilestones: z.boolean(),
  milestones: z.array(z.object({
    title: z.string().min(3, "Title must be at least 3 characters"),
    description: z.string().optional(),
    percentage: z.number().min(0.01, "Percentage must be greater than 0").max(100, "Percentage cannot exceed 100"),
    deliverables: z.array(z.string()).optional(),
    dueDate: z.string().optional(),
  })).optional(),
});

type CreateEscrowFormData = z.infer<typeof createEscrowSchema>;

interface CreateEscrowFormProps {
  onSubmit: (data: CreateEscrowData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

export function CreateEscrowForm({ onSubmit, isLoading = false, error }: CreateEscrowFormProps) {
  const { t } = useTranslation();
  const [selectedRecipient, setSelectedRecipient] = useState<any>(null);
  const [hasMilestones, setHasMilestones] = useState(false);
  const [milestones, setMilestones] = useState<CreateMilestoneData[]>([]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateEscrowFormData>({
    resolver: zodResolver(createEscrowSchema),
    defaultValues: {
      feePayer: 'sender',
      deadlineHours: 72,
      hasMilestones: false,
    },
  });

  const watchedAmount = watch('amount');
  const watchedCurrency = watch('currencyCode');

  const addMilestone = () => {
    const newMilestone: CreateMilestoneData = {
      title: '',
      description: '',
      percentage: 0,
      deliverables: [],
    };
    setMilestones([...milestones, newMilestone]);
  };

  const removeMilestone = (index: number) => {
    setMilestones(milestones.filter((_, i) => i !== index));
  };

  const updateMilestone = (index: number, milestone: CreateMilestoneData) => {
    const updatedMilestones = [...milestones];
    updatedMilestones[index] = milestone;
    setMilestones(updatedMilestones);
  };

  const getTotalPercentage = () => {
    return milestones.reduce((total, milestone) => total + (milestone.percentage || 0), 0);
  };

  const onFormSubmit = async (data: CreateEscrowFormData) => {
    const submitData: CreateEscrowData = {
      ...data,
      recipientId: selectedRecipient?.id || data.recipientId,
      milestones: hasMilestones ? milestones : undefined,
    };

    await onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>{t('Escrow Details')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Recipient Selection */}
          <div className="space-y-2">
            <Label htmlFor="recipient">{t('Recipient')} *</Label>
            <SelectRecipientCombo
              user={selectedRecipient}
              setUser={setSelectedRecipient}
              onChange={(value) => setValue('recipientId', parseInt(value))}
            />
            {errors.recipientId && (
              <p className="text-sm text-red-600">{errors.recipientId.message}</p>
            )}
          </div>

          {/* Amount and Currency */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">{t('Amount')} *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...register('amount', { valueAsNumber: true })}
              />
              {errors.amount && (
                <p className="text-sm text-red-600">{errors.amount.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">{t('Currency')} *</Label>
              <SelectCurrency
                value={watchedCurrency}
                onChange={(value) => setValue('currencyCode', value)}
              />
              {errors.currencyCode && (
                <p className="text-sm text-red-600">{errors.currencyCode.message}</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">{t('Description')}</Label>
            <Textarea
              id="description"
              placeholder={t('Describe what this escrow is for...')}
              {...register('description')}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          {/* Terms and Conditions */}
          <div className="space-y-2">
            <Label htmlFor="termsConditions">{t('Terms and Conditions')}</Label>
            <Textarea
              id="termsConditions"
              placeholder={t('Enter specific terms and conditions for this escrow...')}
              {...register('termsConditions')}
            />
            {errors.termsConditions && (
              <p className="text-sm text-red-600">{errors.termsConditions.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Escrow Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>{t('Escrow Settings')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Deadline */}
          <div className="space-y-2">
            <Label htmlFor="deadlineHours">{t('Deadline (Hours)')} *</Label>
            <Input
              id="deadlineHours"
              type="number"
              min="1"
              max="8760"
              {...register('deadlineHours', { valueAsNumber: true })}
            />
            {errors.deadlineHours && (
              <p className="text-sm text-red-600">{errors.deadlineHours.message}</p>
            )}
            <p className="text-sm text-muted-foreground">
              {t('Maximum 8760 hours (1 year)')}
            </p>
          </div>

          {/* Fee Payer */}
          <div className="space-y-2">
            <Label htmlFor="feePayer">{t('Who Pays the Fee?')} *</Label>
            <Select
              value={watch('feePayer')}
              onValueChange={(value) => setValue('feePayer', value as FeePayer)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sender">{t('Sender (You)')}</SelectItem>
                <SelectItem value="recipient">{t('Recipient')}</SelectItem>
                <SelectItem value="split">{t('Split Between Both')}</SelectItem>
              </SelectContent>
            </Select>
            {errors.feePayer && (
              <p className="text-sm text-red-600">{errors.feePayer.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Milestones */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>{t('Milestones')}</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Label htmlFor="hasMilestones">{t('Enable Milestones')}</Label>
              <Switch
                id="hasMilestones"
                checked={hasMilestones}
                onCheckedChange={(checked) => {
                  setHasMilestones(checked);
                  setValue('hasMilestones', checked);
                  if (!checked) {
                    setMilestones([]);
                  }
                }}
              />
            </div>
          </div>
        </CardHeader>
        
        {hasMilestones && (
          <CardContent className="space-y-4">
            {milestones.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  {t('No milestones added yet')}
                </p>
                <Button type="button" onClick={addMilestone} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('Add First Milestone')}
                </Button>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  {milestones.map((milestone, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium">
                          {t('Milestone {{number}}', { number: index + 1 })}
                        </h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeMilestone(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <MilestoneForm
                        milestone={milestone}
                        onChange={(updatedMilestone) => updateMilestone(index, updatedMilestone)}
                      />
                    </div>
                  ))}
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <Button type="button" onClick={addMilestone} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('Add Milestone')}
                  </Button>
                  
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">
                      {t('Total Percentage')}
                    </p>
                    <Badge 
                      variant={getTotalPercentage() === 100 ? "default" : "destructive"}
                      className="text-sm"
                    >
                      {getTotalPercentage()}%
                    </Badge>
                  </div>
                </div>

                {getTotalPercentage() !== 100 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {t('Milestone percentages must total 100%')}
                    </AlertDescription>
                  </Alert>
                )}
              </>
            )}
          </CardContent>
        )}
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline">
          {t('Cancel')}
        </Button>
        <Button 
          type="submit" 
          disabled={isLoading || (hasMilestones && getTotalPercentage() !== 100)}
        >
          {isLoading ? t('Creating...') : t('Create Escrow')}
        </Button>
      </div>
    </form>
  );
}
