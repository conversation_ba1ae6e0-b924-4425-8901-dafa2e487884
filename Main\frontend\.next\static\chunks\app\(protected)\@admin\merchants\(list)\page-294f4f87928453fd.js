(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[66042],{4371:function(e,a,n){Promise.resolve().then(n.bind(n,38067))},38067:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return f}});var t=n(57437),s=n(74667),r=n(85539),c=n(27186),l=n(85017),i=n(6512),u=n(75730),d=n(94508),o=n(99376),h=n(2265),m=n(43949);function f(){var e;let a=(0,o.useSearchParams)(),[n,f]=h.useState(null!==(e=a.get("search"))&&void 0!==e?e:""),p=(0,o.useRouter)(),x=(0,o.usePathname)(),{t:v}=(0,m.$G)(),{data:g,meta:j,isLoading:N,refresh:k}=(0,u.Z)("/admin/merchants?".concat(a.toString(),"&listType=pending"),{keepPreviousData:!0});return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(r.R,{value:n,onChange:e=>{e.preventDefault();let a=(0,d.w4)(e.target.value);f(e.target.value),p.replace("".concat(x,"?").concat(a.toString()))},iconPlacement:"end",placeholder:v("Search...")}),(0,t.jsx)(l.k,{canFilterUser:!0,canFilterByGender:!0,canFilterByCountryCode:!0}),(0,t.jsx)(c._,{url:"/admin/merchants/export/all"})]}),(0,t.jsx)("div",{})]}),(0,t.jsx)(i.Z,{className:"my-4"}),(0,t.jsx)(s.Z,{data:g,meta:j,isLoading:N,refresh:k})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,31384,27443,83568,227,56993,85017,74667,92971,95030,1744],function(){return e(e.s=4371)}),_N_E=e.O()}]);