(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[42319],{36941:function(e,t,r){Promise.resolve().then(r.bind(r,18823))},18823:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return T}});var n=r(57437),a=r(41709),i=r(25833),l=r(85487),s=r(62869),o=r(17814),c=r(6512),d=r(1828),u=r(79981),m=r(97751);async function f(e){try{let t=await u.Z.put("/admin/currencies/toggle-active/".concat(e),{});return(0,m.B)(t)}catch(e){return(0,m.D)(e)}}async function p(e){try{let t=await u.Z.put("/admin/currencies/toggle-crypto/".concat(e),{});return(0,m.B)(t)}catch(e){return(0,m.D)(e)}}var x=r(75730),h=r(8877),g=r(90433),y=r(58926),v=r(99376),b=r(2265),j=r(43949),N=r(14438),w=r(15681),C=r(95186);let R=async e=>{try{let t=await u.Z.post("/admin/currencies/create",e);return(0,m.B)(t)}catch(e){return(0,m.D)(e)}},S=async(e,t)=>{try{let r=await u.Z.put("/admin/currencies/update/".concat(t),e);return(0,m.B)(r)}catch(e){return(0,m.D)(e)}};var k=r(13590),Z=r(22291),P=r(29501),A=r(31229);let L=A.z.object({name:A.z.string().min(1,"Currency name is required."),code:A.z.string().min(1,"Currency code is required."),minAmount:A.z.string().min(1,"Minimum amount is required."),maxAmount:A.z.string().min(1,"Maximum amount is required."),dailyTransferAmount:A.z.string().min(1,"Daily transfer amount is required."),dailyTransferLimit:A.z.string().min(1,"Daily transfer limit is required."),kycLimit:A.z.string().optional(),notificationLimit:A.z.string().optional(),acceptApiRate:A.z.boolean().optional(),usdRate:A.z.string().optional(),isCrypto:A.z.boolean().optional()});function z(e){let{currency:t,onMutate:r}=e,{t:i}=(0,j.$G)(),[o,c]=(0,b.useTransition)(),u=(0,P.cI)({resolver:(0,k.F)(L),defaultValues:{name:"",code:"",minAmount:"",maxAmount:"",dailyTransferAmount:"",dailyTransferLimit:"",kycLimit:"",notificationLimit:"",acceptApiRate:!!(null==t?void 0:t.acceptApiRate),usdRate:"",isCrypto:!!(null==t?void 0:t.isCrypto)}});(0,b.useEffect)(()=>{t&&u.reset({name:null==t?void 0:t.name,code:null==t?void 0:t.code,minAmount:String(null==t?void 0:t.minAmount),maxAmount:String(null==t?void 0:t.maxAmount),dailyTransferAmount:String(null==t?void 0:t.dailyTransferAmount),dailyTransferLimit:String(null==t?void 0:t.dailyTransferLimit),kycLimit:(null==t?void 0:t.kycLimit)?String(null==t?void 0:t.kycLimit):"",notificationLimit:(null==t?void 0:t.notificationLimit)?String(null==t?void 0:t.notificationLimit):"",acceptApiRate:!!(null==t?void 0:t.acceptApiRate),usdRate:String(null==t?void 0:t.usdRate),isCrypto:!!(null==t?void 0:t.isCrypto)})},[t]);let m=u.watch("acceptApiRate"),f=[{name:"name",label:i("Currency Name"),placeholder:i("Enter currency name"),type:"text"},{name:"code",label:i("Code"),placeholder:i("Enter currency code"),type:"text"},{name:"minAmount",label:i("Minimum amount"),placeholder:i("Enter minimum transaction amount"),type:"text"},{name:"maxAmount",label:i("Maximum amount"),placeholder:i("Enter maximum transaction amount"),type:"text"},{name:"dailyTransferAmount",label:i("Daily transfer amount"),placeholder:i("Enter daily transfer amount"),type:"text"},{name:"dailyTransferLimit",label:i("Daily transfer limit"),placeholder:i("Enter daily transfer limit"),type:"text"},{name:"kycLimit",label:i("Kyc limit (Optional)"),placeholder:i("Enter kyc limit"),type:"text"},{name:"notificationLimit",label:i("Notification limit (Optional)"),placeholder:i("Enter notification limit"),type:"text"},{name:"acceptApiRate",label:i("Accept API rate"),type:"switch"},...m?[]:[{name:"usdRate",label:i("USD Rate"),placeholder:i("Enter USD rate"),type:"text"}],{name:"isCrypto",label:i("Is Crypto"),type:"switch"}];return(0,n.jsx)(w.l0,{...u,children:(0,n.jsxs)("form",{onSubmit:u.handleSubmit(e=>{let n={...e,kycLimit:e.kycLimit?e.kycLimit:null,notificationLimit:e.notificationLimit?e.notificationLimit:null,acceptApiRate:e.acceptApiRate?1:0,isCrypto:e.isCrypto?1:0};t?c(async()=>{let e=await S(n,null==t?void 0:t.id);e.status?(r(),N.toast.success(e.message),u.reset()):N.toast.error(e.message)}):c(async()=>{let e=await R(n);e.status?(r(),N.toast.success(e.message)):N.toast.error(e.message)})}),className:"flex flex-col gap-y-4",children:[f.map(e=>"text"===e.type?(0,n.jsx)(w.Wi,{control:u.control,name:e.name,render:t=>{let{field:r}=t;return(0,n.jsxs)(w.xJ,{className:"space-y-2.5",children:[(0,n.jsx)(w.lX,{children:e.label}),(0,n.jsx)(C.I,{placeholder:e.placeholder,className:"mx-0.5",...r})]})}},e.name):(0,n.jsx)(w.Wi,{control:u.control,name:e.name,render:()=>(0,n.jsxs)(w.xJ,{className:"flex items-center",children:[(0,n.jsx)(w.lX,{className:"mt-2 inline-block w-[150px]",children:e.label}),(0,n.jsx)(d.Z,{defaultChecked:u.getValues(e.name),onCheckedChange:t=>u.setValue(e.name,t)},e.name)]})},e.name)),(0,n.jsxs)(s.z,{disabled:o,children:[(0,n.jsx)(a.J,{condition:o,children:(0,n.jsx)(l.Loader,{title:i(t?"Updating...":"Processing..."),className:"text-primary-foreground"})}),(0,n.jsxs)(a.J,{condition:!o,children:[i(t?"Update Currency":"Create Currency"),(0,n.jsx)(Z.Z,{size:20})]})]})]})})}function T(){let{t:e}=(0,j.$G)(),t=(0,v.useSearchParams)(),[r,u]=(0,b.useState)([]),{data:m,meta:w,isLoading:C,error:R,refresh:S}=(0,x.Z)("/admin/currencies?".concat(t.toString())),k=t=>{N.toast.promise(f(t),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return S(),e.message},error:e=>e.message})},Z=t=>{N.toast.promise(p(t),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return S(),e.message},error:e=>e.message})};return R?(0,n.jsx)("div",{className:"w-full bg-danger py-2.5 text-danger-foreground",children:(0,n.jsx)("p",{children:e("We encountered an issue while retrieving the requested data. Please try again later or contact support if the problem persists.")})}):(0,n.jsxs)("div",{className:"rounded-xl bg-background p-4 shadow-default",children:[(0,n.jsxs)("div",{className:"mb-4 flex items-center justify-between gap-4",children:[(0,n.jsx)("h5",{className:"font-base font-medium leading-[22px]",children:e("Currencies")}),(0,n.jsxs)(o.dy,{direction:"right",children:[(0,n.jsx)(o.Qz,{asChild:!0,children:(0,n.jsxs)(s.z,{variant:"outline",className:"rounded-sm px-4 py-2 text-base font-medium leading-[22px]",children:[(0,n.jsx)(h.Z,{}),e("Add Currency")]})}),(0,n.jsxs)(o.sc,{className:"inset-x-auto inset-y-0 bottom-auto left-auto right-0 top-0 m-0 flex h-full w-[95%] flex-col overflow-x-hidden overflow-y-scroll rounded-t-none bg-white px-0 py-8 md:w-[400px]",children:[(0,n.jsxs)(o.iI,{className:"flex items-center gap-4 px-6 text-base font-semibold",children:[(0,n.jsx)(o.uh,{asChild:!0,children:(0,n.jsx)(s.z,{variant:"outline",size:"icon",children:(0,n.jsx)(g.Z,{})})}),e("Add Currency")]}),(0,n.jsx)(o.u6,{className:"hidden"}),(0,n.jsx)("div",{className:"px-6 py-4",children:(0,n.jsx)(z,{onMutate:S})})]})]})]}),(0,n.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-divider-secondary"}),(0,n.jsxs)("div",{className:"mt-4",children:[(0,n.jsx)(a.J,{condition:C,children:(0,n.jsx)("div",{className:"flex w-full items-center justify-center py-10",children:(0,n.jsx)(l.Loader,{})})}),(0,n.jsx)(a.J,{condition:!C&&!(null==w?void 0:w.total),children:(0,n.jsx)("div",{className:"flex w-full items-center py-10",children:e("No currency found")})}),(0,n.jsx)(a.J,{condition:!C&&m,children:(0,n.jsx)(i.Z,{data:m,isLoading:C,pagination:{total:null==w?void 0:w.total,page:null==w?void 0:w.currentPage,limit:null==w?void 0:w.perPage},structure:[{id:"name",header:e("Currency Name"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsx)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:r.name})}},{id:"code",header:e("Code"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsx)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:r.code})}},{id:"usdRate",header:e("USD Rate"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsx)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:(null==r?void 0:r.usdRate)?"".concat(parseFloat(r.usdRate).toFixed(2)," ").concat(null==r?void 0:r.code):"N/A"})}},{id:"minAmount",header:e("Min. Amount"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsxs)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:[parseFloat(r.minAmount).toFixed(2)," ",null==r?void 0:r.code]})}},{id:"maxAmount",header:e("Max. Amount"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsxs)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:[parseFloat(r.maxAmount).toFixed(2)," ",null==r?void 0:r.code]})}},{id:"dailyTransferAmount",header:e("Daily Transfer Amount"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsxs)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:[parseFloat(r.dailyTransferAmount).toFixed(2)," ",null==r?void 0:r.code]})}},{id:"dailyTransferLimit",header:e("Daily Transfer Limit"),cell:t=>{let{row:r}=t,a=null==r?void 0:r.original;return(0,n.jsxs)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:[a.dailyTransferLimit," ",e("times")]})}},{id:"kycLimit",header:e("Kyc Limit"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsx)("span",{className:"line-clamp-2 block min-w-28 font-normal text-secondary-text",children:r.kycLimit})}},{id:"active",header:e("Active"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsx)(d.Z,{defaultChecked:r.active,onCheckedChange:()=>k(r.id)})}},{id:"isCrypto",header:e("Crypto"),cell:e=>{let{row:t}=e,r=null==t?void 0:t.original;return(0,n.jsx)(d.Z,{defaultChecked:r.isCrypto,onCheckedChange:()=>Z(r.id)})}},{id:"edit",header:e("Edit"),cell:t=>{let{row:r}=t,a=null==r?void 0:r.original;return(0,n.jsxs)(o.dy,{direction:"right",children:[(0,n.jsx)(o.Qz,{asChild:!0,children:(0,n.jsx)(s.z,{variant:"outline",size:"icon",className:"h-8 w-8",children:(0,n.jsx)(y.Z,{size:"20",className:"text-primary"})})}),(0,n.jsxs)(o.sc,{className:"inset-x-auto inset-y-0 bottom-auto left-auto right-0 top-0 m-0 flex h-full w-[95%] flex-col overflow-x-hidden overflow-y-scroll rounded-t-none bg-white px-0 py-8 md:w-[400px]",children:[(0,n.jsxs)(o.iI,{className:"flex items-center gap-4 px-6 text-base font-semibold",children:[(0,n.jsx)(o.uh,{asChild:!0,children:(0,n.jsx)(s.z,{variant:"outline",size:"icon",children:(0,n.jsx)(g.Z,{})})}),e("Edit Currency")]}),(0,n.jsx)(o.u6,{className:"px-6 py-4",children:(0,n.jsx)(z,{currency:a,onMutate:S})})]})]})}}],sorting:r,setSorting:u,className:"border-none py-2.5"})})]})]})}},41709:function(e,t,r){"use strict";function n(e){let{condition:t,children:r}=e;return t?r:null}r.d(t,{J:function(){return n}}),r(2265)},25833:function(e,t,r){"use strict";r.d(t,{Z:function(){return g}});var n=r(57437),a=r(94508),i=r(71594),l=r(24525),s=r(73490),o=r(36887),c=r(64394),d=r(61756),u=r(99376),m=r(4751),f=r(2265),p=r(43949),x=r(62869),h=r(73578);function g(e){let{data:t,isLoading:r=!1,structure:g,sorting:y,setSorting:v,padding:b=!1,className:j,onRefresh:N,pagination:w}=e,C=(0,f.useMemo)(()=>g,[g]),R=(0,u.useRouter)(),S=(0,u.usePathname)(),k=(0,u.useSearchParams)(),{t:Z}=(0,p.$G)(),P=(0,i.b7)({data:t||[],columns:C,state:{sorting:y,onRefresh:N},onSortingChange:v,getCoreRowModel:(0,l.sC)(),getSortedRowModel:(0,l.tj)(),debugTable:!1});return r?(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:Z("Loading...")})}):(null==t?void 0:t.length)?(0,n.jsxs)("div",{className:(0,a.ZP)("".concat(b?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),j),children:[(0,n.jsxs)(h.iA,{children:[(0,n.jsx)(h.xD,{children:P.getHeaderGroups().map(e=>(0,n.jsx)(h.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,r,l,s;return(0,n.jsx)(h.ss,{className:(0,a.ZP)("",null==e?void 0:null===(l=e.column)||void 0===l?void 0:null===(r=l.columnDef)||void 0===r?void 0:null===(t=r.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,n.jsxs)(x.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[Z((0,i.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(s=({asc:(0,n.jsx)(o.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,n.jsx)(o.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==s?s:(0,n.jsx)(o.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,n.jsx)(h.RM,{children:P.getRowModel().rows.map(e=>(0,n.jsx)(h.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,n.jsx)(h.pj,{className:"py-3 text-sm font-semibold",children:(0,i.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),w&&w.total>10&&(0,n.jsx)("div",{className:"pb-2 pt-6",children:(0,n.jsx)(m.Z,{showTotal:(e,t)=>Z("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==w?void 0:w.page,total:null==w?void 0:w.total,pageSize:null==w?void 0:w.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(k);t.set("page",e.toString()),R.push("".concat(S,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(c.Z,{size:"18"})}),nextIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(d.Z,{size:"18"})})})})]}):(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,n.jsx)(s.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),Z("No data found!")]})})}},85487:function(e,t,r){"use strict";r.d(t,{Loader:function(){return l}});var n=r(57437),a=r(94508),i=r(43949);function l(e){let{title:t="Loading...",className:r}=e,{t:l}=(0,i.$G)();return(0,n.jsxs)("div",{className:(0,a.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:l(t)})]})}},62869:function(e,t,r){"use strict";r.d(t,{d:function(){return o},z:function(){return c}});var n=r(57437),a=r(37053),i=r(90535),l=r(2265),s=r(94508);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,t)=>{let{className:r,variant:i,size:l,asChild:c=!1,...d}=e,u=c?a.g7:"button";return(0,n.jsx)(u,{className:(0,s.ZP)(o({variant:i,size:l,className:r})),ref:t,...d})});c.displayName="Button"},17814:function(e,t,r){"use strict";r.d(t,{OX:function(){return f},Qz:function(){return o},dy:function(){return s},iI:function(){return p},sc:function(){return m},u6:function(){return x},uh:function(){return d}});var n=r(57437),a=r(2265),i=r(4216),l=r(94508);let s=e=>{let{shouldScaleBackground:t=!1,...r}=e;return(0,n.jsx)(i.dy.Root,{shouldScaleBackground:t,...r})};s.displayName="Drawer";let o=i.dy.Trigger,c=i.dy.Portal,d=i.dy.Close,u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.dy.Overlay,{ref:t,className:(0,l.ZP)("fixed inset-0 z-[999] bg-black/80",r),...a})});u.displayName=i.dy.Overlay.displayName;let m=a.forwardRef((e,t)=>{let{className:r,children:a,...s}=e;return(0,n.jsxs)(c,{children:[(0,n.jsx)(u,{}),(0,n.jsx)(i.dy.Content,{ref:t,className:(0,l.ZP)("fixed inset-x-0 bottom-0 z-[9999] mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background",r),...s,children:a})]})});m.displayName="DrawerContent";let f=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,l.ZP)("grid gap-1.5 p-4 text-center sm:text-left",t),...r})};f.displayName="DrawerHeader";let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.dy.Title,{ref:t,className:(0,l.ZP)("text-lg font-semibold leading-none tracking-tight",r),...a})});p.displayName=i.dy.Title.displayName;let x=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.dy.Description,{ref:t,className:(0,l.ZP)("text-sm text-muted-foreground",r),...a})});x.displayName=i.dy.Description.displayName},15681:function(e,t,r){"use strict";r.d(t,{NI:function(){return h},Wi:function(){return u},l0:function(){return c},lX:function(){return x},xJ:function(){return p},zG:function(){return g}});var n=r(57437),a=r(37053),i=r(2265),l=r(29501),s=r(26815),o=r(94508);let c=l.RV,d=i.createContext({}),u=e=>{let{...t}=e;return(0,n.jsx)(d.Provider,{value:{name:t.name},children:(0,n.jsx)(l.Qr,{...t})})},m=()=>{let e=i.useContext(d),t=i.useContext(f),{getFieldState:r,formState:n}=(0,l.Gc)(),a=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:"".concat(s,"-form-item"),formDescriptionId:"".concat(s,"-form-item-description"),formMessageId:"".concat(s,"-form-item-message"),...a}},f=i.createContext({}),p=i.forwardRef((e,t)=>{let{className:r,...a}=e,l=i.useId();return(0,n.jsx)(f.Provider,{value:{id:l},children:(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",r),...a})})});p.displayName="FormItem";let x=i.forwardRef((e,t)=>{let{className:r,required:a,...i}=e,{error:l,formItemId:c}=m();return(0,n.jsx)("span",{children:(0,n.jsx)(s.Z,{ref:t,className:(0,o.ZP)(l&&"text-base font-medium text-destructive",r),htmlFor:c,...i})})});x.displayName="FormLabel";let h=i.forwardRef((e,t)=>{let{...r}=e,{error:i,formItemId:l,formDescriptionId:s,formMessageId:o}=m();return(0,n.jsx)(a.g7,{ref:t,id:l,"aria-describedby":i?"".concat(s," ").concat(o):"".concat(s),"aria-invalid":!!i,...r})});h.displayName="FormControl",i.forwardRef((e,t)=>{let{className:r,...a}=e,{formDescriptionId:i}=m();return(0,n.jsx)("p",{ref:t,id:i,className:(0,o.ZP)("text-sm text-muted-foreground",r),...a})}).displayName="FormDescription";let g=i.forwardRef((e,t)=>{let{className:r,children:a,...i}=e,{error:l,formMessageId:s}=m(),c=l?String(null==l?void 0:l.message):a;return c?(0,n.jsx)("p",{ref:t,id:s,className:(0,o.ZP)("text-sm font-medium text-destructive",r),...i,children:c}):null});g.displayName="FormMessage"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return l}});var n=r(57437),a=r(2265),i=r(94508);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,n.jsx)("input",{type:a,className:(0,i.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...l})});l.displayName="Input"},26815:function(e,t,r){"use strict";var n=r(57437),a=r(6394),i=r(90535),l=r(2265),s=r(94508);let o=(0,i.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(a.f,{ref:t,className:(0,s.ZP)(o(),r),...i})});c.displayName=a.f.displayName,t.Z=c},6512:function(e,t,r){"use strict";var n=r(57437),a=r(55156),i=r(2265),l=r(94508);let s=i.forwardRef((e,t)=>{let{className:r,orientation:i="horizontal",decorative:s=!0,...o}=e;return(0,n.jsx)(a.f,{ref:t,decorative:s,orientation:i,className:(0,l.ZP)("shrink-0 bg-divider","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",r),...o})});s.displayName=a.f.displayName,t.Z=s},1828:function(e,t,r){"use strict";var n=r(57437),a=r(50721),i=r(2265),l=r(94508);let s=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(a.fC,{className:(0,l.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",r),...i,ref:t,children:(0,n.jsx)(a.bU,{className:(0,l.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});s.displayName=a.fC.displayName,t.Z=s},73578:function(e,t,r){"use strict";r.d(t,{RM:function(){return o},SC:function(){return c},iA:function(){return l},pj:function(){return u},ss:function(){return d},xD:function(){return s}});var n=r(57437),a=r(2265),i=r(94508);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,i.ZP)("w-full caption-bottom text-sm",r),...a})})});l.displayName="Table";let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("thead",{ref:t,className:(0,i.ZP)("",r),...a})});s.displayName="TableHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,i.ZP)("[&_tr:last-child]:border-0",r),...a})});o.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,i.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("tr",{ref:t,className:(0,i.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});c.displayName="TableRow";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("th",{ref:t,className:(0,i.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});d.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("td",{ref:t,className:(0,i.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("caption",{ref:t,className:(0,i.ZP)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},97751:function(e,t,r){"use strict";r.d(t,{B:function(){return a},D:function(){return i}});var n=r(43577);function a(e){var t,r,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function i(e){let t=500,r="Internal Server Error",a="An unknown error occurred";if((0,n.IZ)(e)){var i,l,s,o,c,d,u,m,f,p,x,h;t=null!==(f=null===(i=e.response)||void 0===i?void 0:i.status)&&void 0!==f?f:500,r=null!==(p=null===(l=e.response)||void 0===l?void 0:l.statusText)&&void 0!==p?p:"Internal Server Error",a=null!==(h=null!==(x=null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:null===(o=c.messages)||void 0===o?void 0:null===(s=o[0])||void 0===s?void 0:s.message)&&void 0!==x?x:null===(m=e.response)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:u.message)&&void 0!==h?h:e.message}else e instanceof Error&&(a=e.message);return{statusCode:t,statusText:r,status:!1,message:a,data:void 0,error:e}}},31117:function(e,t,r){"use strict";r.d(t,{d:function(){return i}});var n=r(79981),a=r(85323);let i=(e,t)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},75730:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(31117),a=r(99376);function i(e,t){var r,i,l;let s=(0,a.usePathname)(),o=(0,a.useSearchParams)(),c=(0,a.useRouter)(),[d,u]=e.split("?"),m=new URLSearchParams(u);m.has("page")||m.set("page","1"),m.has("limit")||m.set("limit","10");let f="".concat(d,"?").concat(m.toString()),{data:p,error:x,isLoading:h,mutate:g,...y}=(0,n.d)(f,t);return{refresh:()=>g(p),data:null!==(l=null==p?void 0:null===(r=p.data)||void 0===r?void 0:r.data)&&void 0!==l?l:[],meta:null==p?void 0:null===(i=p.data)||void 0===i?void 0:i.meta,filter:(e,t,r)=>{let n=new URLSearchParams(o.toString());t?n.set(e,t.toString()):n.delete(e),c.replace("".concat(s,"?").concat(n.toString())),null==r||r()},isLoading:h,error:x,...y}}},79981:function(e,t,r){"use strict";var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return d},Fg:function(){return f},Fp:function(){return c},Qp:function(){return m},ZP:function(){return s},fl:function(){return o},qR:function(){return u},w4:function(){return p}});var n=r(78040),a=r(61994),i=r(14438),l=r(53335);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.m6)((0,a.W)(t))}function o(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>i.toast.success("Copied to clipboard!")).catch(()=>{i.toast.error("Failed to copy!")})};class d{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let i=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:i,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let l=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:i,s=a.format(e),o=s.substring(l.length).trim();return{currencyCode:i,currencySymbol:l,formattedAmount:s,amountText:o}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,49027,38658,85210,19935,1105,92971,95030,1744],function(){return e(e.s=36941)}),_N_E=e.O()}]);