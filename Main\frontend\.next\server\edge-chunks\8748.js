(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8748],{35303:()=>{},44335:(e,t,a)=>{"use strict";a.d(t,{Z:()=>g});var s=a(60926),r=a(65091),n=a(28764),l=a(61922),d=a(66697),i=a(86059),c=a(36086),o=a(65694),x=a(64947),h=a(72784),m=a(29220),u=a(39228),p=a(36162),f=a(92207);function g({data:e,isLoading:t=!1,structure:a,sorting:g,setSorting:j,padding:y=!1,className:v,onRefresh:w,pagination:b}){let N=(0,m.useMemo)(()=>a,[a]),C=(0,x.tv)(),Z=(0,x.jD)(),S=(0,x.lr)(),{t:D}=(0,u.$G)(),P=(0,n.b7)({data:e||[],columns:N,state:{sorting:g,onRefresh:w},onSortingChange:j,getCoreRowModel:(0,l.sC)(),getSortedRowModel:(0,l.tj)(),debugTable:!1});return t?(0,s.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,s.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:D("Loading...")})}):e?.length?(0,s.jsxs)("div",{className:(0,r.ZP)(`${y?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,v),children:[(0,s.jsxs)(f.iA,{children:[(0,s.jsx)(f.xD,{children:P.getHeaderGroups().map(e=>(0,s.jsx)(f.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>(0,s.jsx)(f.ss,{className:(0,r.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,s.jsxs)(p.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[D((0,n.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:(0,s.jsx)(i.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,s.jsx)(i.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??(0,s.jsx)(i.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),(0,s.jsx)(f.RM,{children:P.getRowModel().rows.map(e=>(0,s.jsx)(f.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,s.jsx)(f.pj,{className:"py-3 text-sm font-semibold",children:(0,n.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),b&&b.total>10&&(0,s.jsx)("div",{className:"pb-2 pt-6",children:(0,s.jsx)(h.Z,{showTotal:(e,t)=>D("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:b?.page,total:b?.total,pageSize:b?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(S);t.set("page",e.toString()),C.push(`${Z}?${t.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,s.jsx)("a",{...e,children:(0,s.jsx)(c.Z,{size:"18"})}),nextIcon:e=>(0,s.jsx)("a",{...e,children:(0,s.jsx)(o.Z,{size:"18"})})})})]}):(0,s.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,s.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,s.jsx)(d.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),D("No data found!")]})})}},69226:(e,t,a)=>{"use strict";a.d(t,{_:()=>f});var s=a(60926),r=a(36162),n=a(64930),l=a(23183),d=a(74988),i=a(24013),c=a(65091),o=a(14455),x=a(96264),h=a(24184),m=a(737),u=a(29220),p=a(39228);function f({url:e,className:t,align:a="center"}){let{t:f}=(0,p.$G)(),[g,j]=(0,u.useState)({from:new Date,to:new Date});return(0,s.jsx)("div",{className:(0,c.ZP)("grid gap-2",t),children:(0,s.jsxs)(l.J2,{children:[(0,s.jsx)(l.xo,{asChild:!0,children:(0,s.jsxs)(r.z,{variant:"outline",className:"flex-1 sm:flex-initial",children:[(0,s.jsx)(x.Z,{size:20}),f("Export")]})}),(0,s.jsxs)(l.yk,{className:"w-auto p-0",align:a,children:[(0,s.jsx)(n.f,{initialFocus:!0,mode:"range",defaultMonth:g?.from,selected:g,onSelect:j,numberOfMonths:2}),(0,s.jsx)(d.Z,{}),(0,s.jsxs)("div",{className:(0,c.ZP)("flex items-center justify-between px-4 py-2 text-left text-sm font-normal text-secondary-text",!g&&"text-muted-foreground"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(h.Z,{className:"mr-2 h-5 w-5"}),g?.from?g.to?(0,s.jsxs)(s.Fragment,{children:[(0,o.WU)(g.from,"LLL dd, y")," -"," ",(0,o.WU)(g.to,"LLL dd, y")]}):(0,o.WU)(g.from,"LLL dd, y"):(0,s.jsx)("span",{children:f("Pick a date")})]}),(0,s.jsx)(r.z,{size:"sm",className:"flex-1 text-sm sm:flex-initial",asChild:!0,children:(0,s.jsxs)(m.Z,{href:`${i.rH.API_URL}${(()=>{if(!e)return"";let t=new Date,a=new Date;g?.from&&(t=new Date(g.from),a=g.to?new Date(g.to):t);let s=e.split("?"),r=new URLSearchParams(s[1]||"");return r.set("fromDate",(0,o.WU)(t,"yyyy-MM-dd")),r.set("toDate",(0,o.WU)(a,"yyyy-MM-dd")),`${s[0]}?${r.toString()}`})()}`,children:[(0,s.jsx)(x.Z,{size:17}),f("Export")]})})]})]})]})})}},20293:(e,t,a)=>{"use strict";a.d(t,{k:()=>w});var s=a(60926),r=a(29220),n=a(58387),l=a(73806),d=a(93739),i=a(29411),c=a(36162),o=a(66817),x=a(23183),h=a(83968),m=a(43291),u=a(75785),p=a(81379),f=a(30893),g=a(14455),j=a(56550),y=a(64947),v=a(39228);function w({canFilterByStatus:e=!0,canFilterByDate:t=!0,canFilterByMethod:a=!1,canFilterByGateway:w=!1,canFilterByAgent:b=!1,canFilterByAgentMethod:N=!1,canFilterUser:C=!1,canFilterByGender:Z=!1,canFilterByCountryCode:S=!1}){let{t:D}=(0,v.$G)(),P=(0,y.lr)(),_=(0,y.jD)(),A=(0,y.tv)(),[L,k]=r.useState({}),[M,z]=r.useState(!1),{data:$,isLoading:J}=(0,m.d)("/methods"),{data:U,isLoading:Q}=(0,m.d)("/gateways"),{data:R,isLoading:I}=(0,m.d)(N?"/agent-methods?limit=100&page=1":""),O=(e,t)=>{let a=new URLSearchParams(P.toString());t?(a.set(e,t),k(a=>({...a,[e]:t}))):(a.delete(e),k(t=>({...t,[e]:""}))),A.replace(`${_}?${a.toString()}`)};return r.useEffect(()=>{let e=Object.fromEntries(P.entries());e&&k(e)},[]),(0,s.jsxs)(x.J2,{open:M,onOpenChange:z,children:[(0,s.jsx)(x.xo,{asChild:!0,children:(0,s.jsxs)(c.z,{variant:"outline",children:[(0,s.jsx)(j.Z,{size:20}),D("Filter")]})}),(0,s.jsx)(x.yk,{className:"w-full min-w-[300px] max-w-[400px]",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsx)(n.J,{condition:e,children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,s.jsx)(o.Z,{className:"text-sm font-normal text-secondary-text",children:C?"Status":"Transaction status"}),(0,s.jsxs)(h.Ph,{value:L?.status,onValueChange:e=>O("status",e),children:[(0,s.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,s.jsx)(h.ki,{placeholder:D("Status")})}),(0,s.jsxs)(h.Bw,{children:[(0,s.jsxs)(n.J,{condition:C,children:[(0,s.jsx)(h.Ql,{value:"true",children:D("Active")}),(0,s.jsx)(h.Ql,{value:"false",children:D("Inactive")})]}),(0,s.jsxs)(n.J,{condition:!C,children:[(0,s.jsxs)(h.Ql,{value:"pending",children:[" ",D("Pending")," "]}),(0,s.jsxs)(h.Ql,{value:"completed",children:[D("Completed")," "]}),(0,s.jsxs)(h.Ql,{value:"failed",children:[D("Failed")," "]})]})]})]})]})}),(0,s.jsx)(n.J,{condition:Z,children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,s.jsx)(o.Z,{className:"text-sm font-normal text-secondary-text",children:D("Gender")}),(0,s.jsxs)(h.Ph,{value:L?.gender,onValueChange:e=>O("gender",e),children:[(0,s.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,s.jsx)(h.ki,{placeholder:D("Gender")})}),(0,s.jsxs)(h.Bw,{children:[(0,s.jsx)(h.Ql,{value:"male",children:D("Male")}),(0,s.jsx)(h.Ql,{value:"female",children:D("Female")})]})]})]})}),(0,s.jsx)(n.J,{condition:a,children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,s.jsx)(o.Z,{className:"text-sm font-normal text-secondary-text",children:D("Withdraw method")}),(0,s.jsxs)(h.Ph,{value:L?.method,onValueChange:e=>O("method",e),children:[(0,s.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,s.jsx)(h.ki,{placeholder:D("Withdraw method")})}),(0,s.jsxs)(h.Bw,{side:"right",align:"start",children:[(0,s.jsx)(n.J,{condition:b,children:(0,s.jsx)(h.Ql,{value:"agent",children:D("Agent")})}),J?(0,s.jsx)(i.Loader,{}):$?.data?.map(e=>new p.n(e))?.map(e=>s.jsx(h.Ql,{value:e.value,className:"border-b border-dashed",children:e.name},e.id))]})]})]})}),(0,s.jsx)(n.J,{condition:w,children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,s.jsx)(o.Z,{className:"text-sm font-normal text-secondary-text",children:D("Deposit gateway")}),(0,s.jsxs)(h.Ph,{value:L?.gateway,onValueChange:e=>O("method",e),children:[(0,s.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,s.jsx)(h.ki,{placeholder:D("Deposit gateway")})}),(0,s.jsxs)(h.Bw,{side:"right",align:"start",children:[(0,s.jsx)(n.J,{condition:b,children:(0,s.jsx)(h.Ql,{value:"agent",children:D("Agent")})}),Q?(0,s.jsx)(i.Loader,{}):U?.data?.map(e=>new u.M(e))?.map(e=>s.jsxs(h.Ql,{value:e.value,className:"border-b border-dashed",children:[e.name," ",s.jsx("span",{className:"pl-1.5 text-secondary-text/80",children:e.value})]},e.id))]})]})]})}),(0,s.jsx)(n.J,{condition:N,children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,s.jsx)(o.Z,{className:"text-sm font-normal text-secondary-text",children:D("Agent method")}),(0,s.jsxs)(h.Ph,{value:L?.method,onValueChange:e=>O("method",e),children:[(0,s.jsx)(h.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,s.jsx)(h.ki,{placeholder:D("Method")})}),(0,s.jsx)(h.Bw,{side:"right",align:"start",children:I?(0,s.jsx)(i.Loader,{}):R?.data?.data?.map(e=>new p.n(e))?.map(e=>s.jsx(h.Ql,{value:e.name,className:"border-b border-dashed",children:e.name},e.id))})]})]})}),(0,s.jsx)(n.J,{condition:t,children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,s.jsx)(o.Z,{className:"text-sm font-normal text-secondary-text",children:D("Date")}),(0,s.jsx)(d.M,{value:Object.prototype.hasOwnProperty.call(L,"date")&&L.date?new Date((0,f.Qc)(L.date,"yyyy-MM-dd",new Date)):void 0,onChange:e=>{O("date",e?(0,g.WU)(e,"yyyy-MM-dd"):"")},className:"h-10",placeholderClassName:"text-secondary-text"})]})}),(0,s.jsx)(n.J,{condition:S,children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,s.jsx)(o.Z,{className:"text-sm font-normal text-secondary-text",children:D("Country")}),(0,s.jsx)(l.g,{defaultCountry:L?.countryCode,onSelectChange:e=>{O("countryCode",e.code.cca2)},triggerClassName:"h-10",placeholderClassName:"text-secondary-text",side:"right",align:"start"})]})}),(0,s.jsxs)("div",{className:"flex flex-col items-stretch space-y-2",children:[(0,s.jsx)(c.z,{type:"button",onClick:()=>z(!1),className:"h-10",children:D("Done")}),(0,s.jsx)(c.z,{type:"button",variant:"outline",onClick:()=>{let e=new URLSearchParams;Object.keys(L).forEach(t=>e.delete(t)),k({}),A.replace(`${_}?${e.toString()}`)},className:"h-10",children:D("Clear Filter")})]})]})})]})}},93739:(e,t,a)=>{"use strict";a.d(t,{M:()=>x});var s=a(60926),r=a(64930),n=a(23183),l=a(65091),d=a(14455),i=a(36442),c=a(29220),o=a(39228);let x=c.forwardRef(({value:e,onChange:t,className:a,placeholderClassName:x,options:h},m)=>{let{t:u}=(0,o.$G)(),[p,f]=c.useState(!1);return(0,s.jsxs)(n.J2,{open:p,onOpenChange:f,children:[(0,s.jsxs)(n.xo,{disabled:!!h?.disabled,className:(0,l.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",a),children:[(0,s.jsx)("div",{ref:m,className:"flex flex-1 items-center",children:(0,s.jsx)("div",{className:"flex flex-1 items-center gap-2 text-left",children:e?(0,d.WU)(e,"dd/MM/yyyy"):(0,s.jsx)("span",{className:(0,l.ZP)("text-placeholder",x),children:u("Pick a Date")})})}),(0,s.jsx)(i.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),(0,s.jsx)(n.yk,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(r.f,{...h,mode:"single",initialFocus:!0,selected:e??void 0,onSelect:e=>{t(e),f(!1)}})})]})})},28871:(e,t,a)=>{"use strict";a.d(t,{C:()=>d});var s=a(60926),r=a(8206);a(29220);var n=a(65091);let l=(0,r.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...a}){return(0,s.jsx)("div",{className:(0,n.ZP)(l({variant:t}),e),...a})}},64930:(e,t,a)=>{"use strict";a.d(t,{f:()=>o});var s=a(60926),r=a(97896),n=a(8440),l=a(10471);a(29220);var d=a(68809),i=a(36162),c=a(65091);function o({className:e,classNames:t,showOutsideDays:a=!0,...o}){return(0,s.jsx)(d._W,{showOutsideDays:a,className:(0,c.ZP)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,c.ZP)((0,i.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.ZP)((0,i.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:({...e})=>(0,s.jsx)(r.Z,{className:"h-4 w-4"}),IconRight:({...e})=>(0,s.jsx)(n.Z,{className:"h-4 w-4"}),Dropdown:({...e})=>(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("select",{...e,style:{opacity:0,position:"absolute"}}),(0,s.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm",children:e.caption}),(0,s.jsx)(l.Z,{className:"size-3"})]})]})},...o})}o.displayName="Calendar"},89551:(e,t,a)=>{"use strict";a.d(t,{z:()=>n});var s=a(1181),r=a(25694);async function n(e){try{let t=await s.Z.put(`/admin/users/toggle-active/${e}`,{});return(0,r.B)(t)}catch(e){return(0,r.D)(e)}}},98903:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var s=a(43291),r=a(64947);function n(e,t){let a=(0,r.jD)(),n=(0,r.lr)(),l=(0,r.tv)(),[d,i]=e.split("?"),c=new URLSearchParams(i);c.has("page")||c.set("page","1"),c.has("limit")||c.set("limit","10");let o=`${d}?${c.toString()}`,{data:x,error:h,isLoading:m,mutate:u,...p}=(0,s.d)(o,t);return{refresh:()=>u(x),data:x?.data?.data??[],meta:x?.data?.meta,filter:(e,t,s)=>{let r=new URLSearchParams(n.toString());t?r.set(e,t.toString()):r.delete(e),l.replace(`${a}?${r.toString()}`),s?.()},isLoading:m,error:h,...p}}},75785:(e,t,a)=>{"use strict";a.d(t,{M:()=>s});class s{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.active=e?.active,this.activeApi=e?.activeApi,this.recommended=e?.recommended,this.variables=e?.variables,this.allowedCurrencies=e?.allowedCurrencies,this.allowedCountries=e?.allowedCountries,this.createdAt=e?.createdAt?new Date(e?.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):null}}},81379:(e,t,a)=>{"use strict";a.d(t,{n:()=>s});class s{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.params=e?.params?JSON.parse(e?.params):null,this.currencyCode=e?.currencyCode,this.countryCode=e?.countryCode,this.active=!!e?.active,this.activeApi=!!e?.activeApi,this.recommended=!!e?.recommended,this.minAmount=e?.minAmount??0,this.maxAmount=e?.maxAmount??0,this.fixedCharge=e?.fixedCharge??0,this.percentageCharge=e?.percentageCharge,this.createdAt=e?.createdAt?new Date(e.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e.updatedAt):null}}}}]);
//# sourceMappingURL=8748.js.map