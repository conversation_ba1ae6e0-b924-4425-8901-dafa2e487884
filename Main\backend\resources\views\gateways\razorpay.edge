<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>
            Razorpay Gateway
        </title>
        <style>
            form {
                display: none;
            }
            
            body {
                display: flex;
                height: 100vh;
                align-items: center;
                justify-content: center;
            }
            
            .loader {
                border: 16px solid #f3f3f3;
                /* Light grey */
                border-top: 16px solid #3498db;
                /* Blue */
                border-radius: 50%;
                width: 80px;
                height: 80px;
                animation: spin 2s linear infinite;
            }
            
            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }
            
                100% {
                    transform: rotate(360deg);
                }
            }
        </style>
    </head>
    <body>
        <div class="loader">
        </div>
        <form method="POST" action="https://api.razorpay.com/v1/checkout/embedded">
            <input type="hidden" name="key_id" value="{{ keyId }}" />
            <input type="hidden" name="amount" value=amount />
            <input type="hidden" name="currency" value="{{ currency }}" />
            <input type="hidden" name="order_id" value="{{ orderId }}" />
            <input type="hidden" name="name" value="{{ name }}" />
            <input type="hidden" name="prefill[contact]" value="{{ prefillContact }}" />
            <input type="hidden" name="prefill[email]" value="{{ prefillEmail }}" />
            <input type="hidden" name="callback_url" value="{{ callbackUrl }}" />
            <input type="hidden" name="cancel_url" value="{{ cancelUrl }}" />
            <button id="submitButton" style="display:none;">Submit</button>
        </form>
        <script>
            function autoClickButton() {
                const button = document.getElementById("submitButton");
                button.click();
            }
            
            autoClickButton();
        </script>
    </body>
</html>
