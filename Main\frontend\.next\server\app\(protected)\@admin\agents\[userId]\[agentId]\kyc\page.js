(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7458],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},41249:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>I,default:()=>D});var r,a={};s.r(a),s.d(a,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>x,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>g,pages:()=>f,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>h,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),s(67206);var n=s(79319),i=s(20518),o=s(61902),l=s(62042),c=s(44630),d=s(44828),m=s(65505),u=s(13839);let p=["",{children:["(protected)",{admin:["children",{children:["agents",{children:["[userId]",{children:["[agentId]",{children:["kyc",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,37006)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\kyc\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,15725)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\kyc\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,12771)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,12885)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,29670)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,74030)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],f=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\kyc\\page.tsx"],g="/(protected)/@admin/agents/[userId]/[agentId]/kyc/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page",pathname:"/agents/[userId]/[agentId]/kyc",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=s(69094),b=s(5787),j=s(90527);let y=e=>e?JSON.parse(e):void 0,N=self.__BUILD_MANIFEST,S=y(self.__REACT_LOADABLE_MANIFEST),E=null==(r=self.__RSC_MANIFEST)?void 0:r["/(protected)/@admin/agents/[userId]/[agentId]/kyc/page"],k=y(self.__RSC_SERVER_MANIFEST),A=y(self.__NEXT_FONT_MANIFEST),P=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];E&&k&&(0,b.Mo)({clientReferenceManifest:E,serverActionsManifest:k,serverModuleMap:(0,j.w)({serverActionsManifest:k,pageName:"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page"})});let w=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page",appMod:null,pageMod:a,errorMod:null,error500Mod:null,Document:null,buildManifest:N,renderToHTML:l.f,reactLoadableManifest:S,clientReferenceManifest:E,serverActionsManifest:k,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:A,incrementalCacheHandler:null,interceptionRouteRewrites:P}),I=a;function D(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:w})}},98254:(e,t,s)=>{Promise.resolve().then(s.bind(s,29431))},80541:(e,t,s)=>{Promise.resolve().then(s.bind(s,87339))},35303:()=>{},29431:(e,t,s)=>{"use strict";s.d(t,{Tabbar:()=>b});var r=s(60926),a=s(14579),n=s(30417),i=s(89551),o=s(53042),l=s(23181),c=s(44788),d=s(38071),m=s(28531),u=s(5764),p=s(47020),f=s(737),g=s(64947),x=s(39228),h=s(32167),v=s(91500);function b(){let e=(0,g.UO)(),t=(0,g.jD)(),s=(0,g.tv)(),b=(0,g.lr)(),{t:j}=(0,x.$G)(),y=[{title:j("Account Details"),icon:(0,r.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}?${b.toString()}`,id:"__DEFAULT__"},{title:j("Charges/Commissions"),icon:(0,r.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/commissions?${b.toString()}`,id:"commissions"},{title:j("Fees"),icon:(0,r.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/fees?${b.toString()}`,id:"fees"},{title:j("Transactions"),icon:(0,r.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/transactions?${b.toString()}`,id:"transactions"},{title:j("KYC"),icon:(0,r.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/kyc?${b.toString()}`,id:"kyc"},{title:j("Permissions"),icon:(0,r.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/permissions?${b.toString()}`,id:"permissions"},{title:j("Send Email"),icon:(0,r.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/send-email?${b.toString()}`,id:"send-email"}];return(0,r.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,r.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,r.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,r.jsx)("li",{children:(0,r.jsxs)(f.Z,{href:"/agents/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,r.jsx)(p.Z,{className:"size-4 sm:size-6"}),j("Back")]})}),(0,r.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",b.get("name")," "]}),(0,r.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",j("Agents")," #",e.agentId]})]}),(0,r.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,r.jsx)("span",{children:j("Active")}),(0,r.jsx)(n.Z,{defaultChecked:"1"===b.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:r=>{h.toast.promise((0,i.z)(e.userId),{loading:j("Loading..."),success:a=>{if(!a.status)throw Error(a.message);let n=new URLSearchParams(b);return(0,v.j)(`/admin/agents/${e.agentId}`),n.set("active",r?"1":"0"),s.push(`${t}?${n.toString()}`),a.message},error:e=>e.message})}})]})]}),(0,r.jsx)(a.a,{tabs:y})]})}},87339:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var r=s(60926),a=s(58387),n=s(29411),i=s(59571),o=s(80317),l=s(28871),c=s(36162),d=s(68870),m=s(92207),u=s(40847),p=s(43291),f=s(43553),g=s(31809),x=s(66277),h=s(18825),v=s(32797),b=s(90543),j=s(51018),y=s(81279),N=s(66757),S=s(28277),E=s(64947),k=s(39228),A=s(32167);function P(){let{t:e}=(0,k.$G)(),t=(0,E.UO)(),{data:s,isLoading:d,mutate:y}=(0,p.d)(`/admin/users/${t.userId}`),N=(t,r)=>{A.toast.promise((0,u.$)(t,r),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return y(s),e.message},error:e=>e.message})},S=s?.data?.kyc?new f.t(s.data.kyc):null;return(0,r.jsx)(i.UQ,{type:"multiple",defaultValue:["KYC_STATUS","DOCUMENT_INFORMATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,r.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(i.Qd,{value:"KYC_STATUS",className:"border-none px-4 py-0",children:[(0,r.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:e("KYC Status")}),(0,r.jsx)(a.J,{condition:!S,children:(0,r.jsx)(l.C,{className:"h-5 bg-foreground text-[10px] text-background",children:e("Awaiting submission")})}),(0,r.jsx)(a.J,{condition:S?.status==="pending",children:(0,r.jsx)(l.C,{className:"h-5 bg-primary text-[10px] text-primary-foreground",children:e("Pending")})}),(0,r.jsx)(a.J,{condition:S?.status==="verified",children:(0,r.jsx)(l.C,{className:"h-5 bg-spacial-green text-[10px] text-spacial-green-foreground",children:e("Verified")})}),(0,r.jsx)(a.J,{condition:S?.status==="failed",children:(0,r.jsx)(l.C,{className:"h-5 bg-danger text-[10px] text-destructive-foreground",children:e("Rejected")})})]})}),(0,r.jsxs)(i.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[(0,r.jsx)(a.J,{condition:!S,children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-foreground",children:[(0,r.jsx)(g.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:e("User have not submitted documents yet")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:e("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]})}),(0,r.jsx)(a.J,{condition:S?.status==="pending",children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-primary",children:[(0,r.jsx)(x.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:e("Pending verification")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:e("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]})}),(0,r.jsx)(a.J,{condition:S?.status==="verified",children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-spacial-green",children:[(0,r.jsx)(h.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:e("Your account is verified")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:e("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]})}),(0,r.jsx)(a.J,{condition:S?.status==="failed",children:(0,r.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-danger",children:[(0,r.jsx)(v.Z,{size:"32",variant:"Bulk"}),(0,r.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:e("KYC Document Rejected")}),(0,r.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:e("The submitted KYC document has been rejected. Please review the document for discrepancies or invalid details and request the user to submit accurate information for verification.")})]})})]})]})}),(0,r.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(i.Qd,{value:"DOCUMENT_INFORMATION",className:"border-none px-4 py-0",children:[(0,r.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("div",{className:"flex items-center gap-1",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:e("Documents")})})}),(0,r.jsx)(i.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:S?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"max-w-[900px]",children:(0,r.jsxs)(m.iA,{className:"table-fixed",children:[(0,r.jsx)(m.xD,{className:"[&_tr]:border-b-0",children:(0,r.jsxs)(m.SC,{children:[(0,r.jsx)(m.ss,{children:e("KYC")}),(0,r.jsx)(m.ss,{children:e("Menu")})]})}),(0,r.jsx)(m.RM,{children:d?(0,r.jsx)(m.SC,{children:(0,r.jsx)(m.pj,{colSpan:2,children:(0,r.jsx)(n.Loader,{})})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(m.SC,{className:"odd:bg-accent",children:[(0,r.jsx)(m.pj,{children:e("Document type")}),(0,r.jsx)(m.pj,{children:(0,r.jsx)("div",{className:"flex items-center gap-1 sm:gap-10",children:(0,r.jsx)("span",{className:"hidden font-semibold sm:block",children:S?.documentType})})})]}),(0,r.jsx)(w,{title:e("Front image"),preview:S?.front}),(0,r.jsx)(w,{title:e("Back image"),preview:S?.back}),(0,r.jsx)(w,{title:e("Selfie"),preview:S?.selfie})]})})]})}),(0,r.jsx)(a.J,{condition:S.status?.toLowerCase()==="pending",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2.5 sm:gap-4",children:[(0,r.jsxs)(c.z,{type:"button",onClick:()=>N(S.id,"accept"),className:"bg-[#0B6A0B] text-white hover:bg-[#208c20]",children:[(0,r.jsx)(b.Z,{}),e("Approve")]}),(0,r.jsxs)(c.z,{type:"button",onClick:()=>N(S.id,"decline"),className:"bg-[#D13438] text-white hover:bg-[#c32d32]",children:[(0,r.jsx)(j.Z,{}),e("Reject")]})]})})]}):(0,r.jsx)("div",{className:"py-4",children:(0,r.jsx)("p",{className:"text-secondary-text",children:e("KYC Documents not submitted yet")})})})]})})]})})}function w({title:e,preview:t}){let{t:s}=(0,k.$G)();return(0,r.jsxs)(m.SC,{className:"odd:bg-accent",children:[(0,r.jsx)(m.pj,{children:e}),(0,r.jsx)(m.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-1 sm:gap-10",children:[(0,r.jsxs)(d.Vq,{children:[(0,r.jsx)(d.hg,{asChild:!0,children:(0,r.jsxs)(c.z,{type:"button",variant:"outline",className:"bg-background hover:bg-muted",children:[(0,r.jsx)(y.Z,{}),(0,r.jsx)("span",{className:"hidden sm:block",children:s("View")})]})}),(0,r.jsxs)(d.cZ,{className:"max-w-7xl",children:[(0,r.jsxs)(d.fK,{children:[(0,r.jsxs)(d.$N,{children:[" ",e," "]}),(0,r.jsx)(d.Be,{className:"hidden","aria-hidden":!0})]}),(0,r.jsx)("div",{className:"relative mx-auto aspect-square w-full max-w-xl",children:t?(0,r.jsx)(S.Z,{src:t,alt:e,fill:!0,sizes:"500px",style:{width:"100%",height:"100%"},quality:"90",loading:"lazy",placeholder:"blur",className:"object-contain",blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mN8Vw8AAmEBb87E6jIAAAAASUVORK5CYII="}):(0,r.jsx)("div",{className:"flex h-full w-full items-center justify-center rounded-md bg-accent",children:(0,r.jsx)("span",{className:"font-semibold opacity-70",children:s("No preview")})})})]})]}),(0,r.jsx)(c.z,{type:"button",variant:"outline",className:"bg-background hover:bg-muted",asChild:!0,children:(0,r.jsxs)("a",{href:t,download:!0,children:[(0,r.jsx)(N.Z,{}),(0,r.jsx)("span",{className:"hidden sm:inline-block",children:s("Download")})]})})]})})]})}},59571:(e,t,s)=>{"use strict";s.d(t,{Qd:()=>c,UQ:()=>l,o4:()=>d,vF:()=>m});var r=s(60926),a=s(73837),n=s(29220),i=s(65091),o=s(86059);let l=a.fC,c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.ck,{ref:s,className:(0,i.ZP)("border-b",e),...t}));c.displayName="AccordionItem";let d=n.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsx)(a.h4,{className:"flex",children:(0,r.jsxs)(a.xz,{ref:n,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...s,children:[t,(0,r.jsx)(o.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));d.displayName=a.xz.displayName;let m=n.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsx)(a.VY,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...s,children:(0,r.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",e),children:t})}));m.displayName=a.VY.displayName},80317:(e,t,s)=>{"use strict";s.d(t,{Cd:()=>c,X:()=>d,bZ:()=>l});var r=s(60926),a=s(8206),n=s(29220),i=s(65091);let o=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=n.forwardRef(({className:e,variant:t,...s},a)=>(0,r.jsx)("div",{ref:a,role:"alert",className:(0,i.ZP)(o({variant:t}),e),...s}));l.displayName="Alert";let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h5",{ref:s,className:(0,i.ZP)("mb-1 font-medium leading-none tracking-tight",e),...t}));c.displayName="AlertTitle";let d=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.ZP)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},28871:(e,t,s)=>{"use strict";s.d(t,{C:()=>o});var r=s(60926),a=s(8206);s(29220);var n=s(65091);let i=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,n.ZP)(i({variant:t}),e),...s})}},92207:(e,t,s)=>{"use strict";s.d(t,{RM:()=>l,SC:()=>c,iA:()=>i,pj:()=>m,ss:()=>d,xD:()=>o});var r=s(60926),a=s(29220),n=s(65091);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:s,className:(0,n.ZP)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("thead",{ref:s,className:(0,n.ZP)("",e),...t}));o.displayName="TableHeader";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tbody",{ref:s,className:(0,n.ZP)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tfoot",{ref:s,className:(0,n.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tr",{ref:s,className:(0,n.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));c.displayName="TableRow";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("th",{ref:s,className:(0,n.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));d.displayName="TableHead";let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("td",{ref:s,className:(0,n.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));m.displayName="TableCell",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("caption",{ref:s,className:(0,n.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},89551:(e,t,s)=>{"use strict";s.d(t,{z:()=>n});var r=s(1181),a=s(25694);async function n(e){try{let t=await r.Z.put(`/admin/users/toggle-active/${e}`,{});return(0,a.B)(t)}catch(e){return(0,a.D)(e)}}},40847:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(1181),a=s(25694);let n=async(e,t)=>{try{let s=await r.Z.put(`/admin/kycs/${t}/${e}`,{});return(0,a.B)(s)}catch(e){return(0,a.D)(e)}}},23181:(e,t,s)=>{"use strict";s.d(t,{Z:()=>g});var r=s(61394),a=s(29220),n=s(31036),i=s.n(n),o=["variant","color","size"],l=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7.75 9.14c0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48 0 .81-.66 1.48-1.48 1.48a1.49 1.49 0 0 1-1.48-1.48Zm1.07 6.88c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.35 15.8c-.15.15-.34.22-.53.22Zm6.95.32c-.81 0-1.48-.66-1.48-1.48 0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48s-.66 1.48-1.48 1.48Z",fill:t}))},c=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M2 13.05V15c0 5 2 7 7 7h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9M8.32 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{opacity:".4",d:"M16.192 2h-8.37c-3.64 0-5.81 2.17-5.81 5.81v8.37c0 3.64 2.17 5.81 5.81 5.81h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81c0-3.64-2.17-5.81-5.81-5.81Z",fill:t}),a.createElement("path",{d:"M8.822 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM9.23 7.66c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48a1.49 1.49 0 0 0-1.48-1.48ZM15.769 13.39c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48s-.66-1.48-1.48-1.48Z",fill:t}))},m=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7ZM8.57 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"M8.98 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.52 16.09a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h6c5.43 0 7.75 2.32 7.75 7.75v6c0 5.43-2.32 7.75-7.75 7.75Zm-6-20C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25V9c0-4.61-1.64-6.25-6.25-6.25H9Z",fill:t}),a.createElement("path",{d:"M8.568 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM8.98 11.108c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48a.48.48 0 0 0-.48-.48ZM15.519 16.839c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48-.21-.48-.48-.48Z",fill:t}))},p=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{opacity:".4",d:"m8.32 15.27 6.54-6.54M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e,t){switch(e){case"Bold":return a.createElement(l,{color:t});case"Broken":return a.createElement(c,{color:t});case"Bulk":return a.createElement(d,{color:t});case"Linear":default:return a.createElement(m,{color:t});case"Outline":return a.createElement(u,{color:t});case"TwoTone":return a.createElement(p,{color:t})}},g=(0,a.forwardRef)(function(e,t){var s=e.variant,n=e.color,i=e.size,l=(0,r._)(e,o);return a.createElement("svg",(0,r.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),f(s,n))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="PercentageSquare"},43553:(e,t,s)=>{"use strict";s.d(t,{t:()=>a});var r=s(65091);class a{constructor(e){this.id=e?.id,this.userId=e?.userId,this.documentType=e?.documentType?.toUpperCase(),this.selfie=(0,r.qR)(e?.selfie),this.front=(0,r.qR)(e?.front),this.back=(0,r.qR)(e?.back),this.status=e?.status,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt)}}},15725:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(42416),a=s(21237);function n(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(a.a,{})})}},37006:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\kyc\page.tsx#default`)},12771:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,runtime:()=>n});var r=s(42416);s(87908);let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\_components\Tabbar.tsx#Tabbar`),n="edge";function i({children:e}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a,{}),e]})}},12885:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(42416),a=s(21237);function n(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(a.a,{})})}},29670:(e,t,s)=>{"use strict";function r({children:e}){return e}s.r(t),s.d(t,{default:()=>r}),s(87908)},74030:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(42416),a=s(21237);function n(){return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(a.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,870,2607,7283,5089,3711],()=>t(41249));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page"]=s}]);
//# sourceMappingURL=page.js.map