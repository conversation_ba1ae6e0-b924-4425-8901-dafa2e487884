(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1933],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},97292:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>C,default:()=>M});var a,s={};r.r(s),r.d(s,{AppRouter:()=>m.WY,ClientPageRoot:()=>m.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>m.yO,NotFoundBoundary:()=>m.O4,Postpone:()=>m.hQ,RenderFromTemplateContext:()=>m.b5,__next_app__:()=>f,actionAsyncStorage:()=>m.Wz,createDynamicallyTrackedSearchParams:()=>m.rL,createUntrackedSearchParams:()=>m.S5,decodeAction:()=>m.Hs,decodeFormState:()=>m.dH,decodeReply:()=>m.kf,originalPathname:()=>g,pages:()=>h,patchFetch:()=>m.XH,preconnect:()=>m.$P,preloadFont:()=>m.C5,preloadStyle:()=>m.oH,renderToReadableStream:()=>m.aW,requestAsyncStorage:()=>m.Fg,routeModule:()=>x,serverHooks:()=>m.GP,staticGenerationAsyncStorage:()=>m.AT,taintObjectReference:()=>m.nr,tree:()=>p}),r(67206);var n=r(79319),i=r(20518),o=r(61902),c=r(62042),l=r(44630),d=r(44828),u=r(65505),m=r(13839);let p=["",{children:["(protected)",{admin:["children",{children:["agents",{children:["[userId]",{children:["[agentId]",{children:["permissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96718)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\permissions\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,94695)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\permissions\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,12771)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,12885)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,29670)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,74030)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\permissions\\page.tsx"],g="/(protected)/@admin/agents/[userId]/[agentId]/permissions/page",f={require:r,loadChunk:()=>Promise.resolve()},x=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/agents/[userId]/[agentId]/permissions/page",pathname:"/agents/[userId]/[agentId]/permissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=r(69094),j=r(5787),b=r(90527);let E=e=>e?JSON.parse(e):void 0,S=self.__BUILD_MANIFEST,k=E(self.__REACT_LOADABLE_MANIFEST),y=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/agents/[userId]/[agentId]/permissions/page"],N=E(self.__RSC_SERVER_MANIFEST),P=E(self.__NEXT_FONT_MANIFEST),A=E(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];y&&N&&(0,j.Mo)({clientReferenceManifest:y,serverActionsManifest:N,serverModuleMap:(0,b.w)({serverActionsManifest:N,pageName:"/(protected)/@admin/agents/[userId]/[agentId]/permissions/page"})});let w=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@admin/agents/[userId]/[agentId]/permissions/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:S,renderToHTML:c.f,reactLoadableManifest:k,clientReferenceManifest:y,serverActionsManifest:N,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:P,incrementalCacheHandler:null,interceptionRouteRewrites:A}),C=s;function M(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:w})}},98254:(e,t,r)=>{Promise.resolve().then(r.bind(r,29431))},63784:(e,t,r)=>{Promise.resolve().then(r.bind(r,75650))},35303:()=>{},29431:(e,t,r)=>{"use strict";r.d(t,{Tabbar:()=>j});var a=r(60926),s=r(14579),n=r(30417),i=r(89551),o=r(53042),c=r(23181),l=r(44788),d=r(38071),u=r(28531),m=r(5764),p=r(47020),h=r(737),g=r(64947),f=r(39228),x=r(32167),v=r(91500);function j(){let e=(0,g.UO)(),t=(0,g.jD)(),r=(0,g.tv)(),j=(0,g.lr)(),{t:b}=(0,f.$G)(),E=[{title:b("Account Details"),icon:(0,a.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}?${j.toString()}`,id:"__DEFAULT__"},{title:b("Charges/Commissions"),icon:(0,a.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/commissions?${j.toString()}`,id:"commissions"},{title:b("Fees"),icon:(0,a.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/fees?${j.toString()}`,id:"fees"},{title:b("Transactions"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/transactions?${j.toString()}`,id:"transactions"},{title:b("KYC"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/kyc?${j.toString()}`,id:"kyc"},{title:b("Permissions"),icon:(0,a.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/permissions?${j.toString()}`,id:"permissions"},{title:b("Send Email"),icon:(0,a.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/send-email?${j.toString()}`,id:"send-email"}];return(0,a.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,a.jsx)("li",{children:(0,a.jsxs)(h.Z,{href:"/agents/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,a.jsx)(p.Z,{className:"size-4 sm:size-6"}),b("Back")]})}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",j.get("name")," "]}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",b("Agents")," #",e.agentId]})]}),(0,a.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,a.jsx)("span",{children:b("Active")}),(0,a.jsx)(n.Z,{defaultChecked:"1"===j.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:a=>{x.toast.promise((0,i.z)(e.userId),{loading:b("Loading..."),success:s=>{if(!s.status)throw Error(s.message);let n=new URLSearchParams(j);return(0,v.j)(`/admin/agents/${e.agentId}`),n.set("active",a?"1":"0"),r.push(`${t}?${n.toString()}`),s.message},error:e=>e.message})}})]})]}),(0,a.jsx)(s.a,{tabs:E})]})}},75650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(60926),s=r(78133),n=r(29411),i=r(28871),o=r(87198),c=r(30417),l=r(92207),d=r(41911),u=r(43291),m=r(75785),p=r(81379),h=r(18001),g=r(64947),f=r(29220),x=r(39228),v=r(32167);function j(){let{t:e}=(0,x.$G)(),t=(0,g.UO)(),[r,j]=f.useState(""),[E,S]=f.useState(""),{data:k,isLoading:y}=(0,u.d)(`/admin/users/permission/${t.userId}`),{data:N,isLoading:P}=(0,u.d)(`/admin/users/blacklisted-methods/${t.userId}&search=${r}`),{data:A,isLoading:w}=(0,u.d)(`/admin/users/blacklisted-gateways/${t.userId}&search=${E}`),C=(t,r)=>{v.toast.promise((0,d.Y)(t,r),{loading:e("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return e.message},error:e=>e.message})};return(0,a.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsx)("div",{className:"flex items-center gap-1",children:(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:e("Permitted Actions")})})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsx)("div",{className:"max-w-[900px]",children:(0,a.jsxs)(l.iA,{children:[(0,a.jsx)(l.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(l.SC,{children:[(0,a.jsx)(l.ss,{children:e("Actions")}),(0,a.jsx)(l.ss,{children:e("Permission")})]})}),(0,a.jsx)(l.RM,{children:y?Array.from({length:8}).map((e,t)=>(0,a.jsxs)(l.SC,{children:[(0,a.jsx)(l.pj,{className:"w-full",children:(0,a.jsx)(o.O,{className:"h-4 w-2/3"})}),(0,a.jsx)(l.pj,{children:(0,a.jsx)(o.O,{className:"h-5 w-16"})})]},t)):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b,{title:e("Deposit money"),type:"deposit",defaultStatus:!!k?.data?.permission?.deposit,onChange:e=>C(e,k?.data?.permission?.id)}),(0,a.jsx)(b,{title:e("Withdraw money"),type:"withdraw",defaultStatus:!!k?.data?.permission?.withdraw,onChange:e=>C(e,k?.data?.permission?.id)}),(0,a.jsx)(b,{title:e("Payment"),type:"payment",defaultStatus:!!k?.data?.permission?.payment,onChange:e=>C(e,k?.data?.permission?.id)}),(0,a.jsx)(b,{title:e("Exchange"),type:"exchange",defaultStatus:!!k?.data?.permission?.exchange,onChange:e=>C(e,k?.data?.permission?.id)}),(0,a.jsx)(b,{title:e("Transfer"),type:"transfer",defaultStatus:!!k?.data?.permission?.transfer,onChange:e=>C(e,k?.data?.permission?.id)}),(0,a.jsx)(b,{title:e("Add account"),type:"addAccount",defaultStatus:!!k?.data?.permission?.addAccount,onChange:e=>C(e,k?.data?.permission?.id)}),(0,a.jsx)(b,{title:e("Add/Remove balance"),type:"addRemoveBalance",defaultStatus:!!k?.data?.permission?.addRemoveBalance,onChange:e=>C(e,k?.data?.permission?.id)}),(0,a.jsx)(b,{title:e("User services"),type:"services",defaultStatus:!!k?.data?.permission?.services,onChange:e=>C(e,k?.data?.permission?.id)})]})})]})})})]})}),(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center justify-between gap-1 gap-y-2 sm:gap-y-0",children:[(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:e("Blacklisted Gateways")}),(0,a.jsx)(s.R,{value:r,onChange:e=>j(e.target.value),placeholder:e("Search"),iconPlacement:"end"})]})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsxs)(l.iA,{children:[(0,a.jsx)(l.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(l.SC,{children:[(0,a.jsx)(l.ss,{children:e("Logo")}),(0,a.jsx)(l.ss,{children:e("Name")}),(0,a.jsx)(l.ss,{children:e("Status")}),(0,a.jsx)(l.ss,{children:e("Recommended")}),(0,a.jsx)(l.ss,{children:e("Permission")})]})}),(0,a.jsx)(l.RM,{children:P?(0,a.jsx)(l.SC,{children:(0,a.jsx)(l.pj,{colSpan:5,children:(0,a.jsx)(n.Loader,{})})}):N?.data?.blackListedMethods?.length===0?(0,a.jsx)(l.SC,{children:(0,a.jsx)(l.pj,{colSpan:5,className:"bg-accent/50",children:e("No Data")})}):N?.data?.blackListedMethods.map(e=>new p.n(e))?.map(t=>a.jsxs(l.SC,{className:"odd:bg-accent",children:[a.jsx(l.pj,{children:a.jsx("div",{className:"flex size-10 items-center justify-center rounded-full bg-muted",children:a.jsx(h.Z,{size:20})})}),a.jsx(l.pj,{className:"w-[420px]",children:t.name}),a.jsx(l.pj,{children:t.active?a.jsx(i.C,{variant:"success",children:e("Active")}):a.jsx(i.C,{variant:"secondary",children:e("Inactive")})}),a.jsx(l.pj,{children:t.recommended?a.jsx(i.C,{variant:"important",children:e("Yes")}):a.jsx(i.C,{variant:"secondary",children:e("No")})}),a.jsx(l.pj,{children:a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx("span",{children:e("No")}),a.jsx(c.Z,{defaultChecked:!1,disabled:!0,className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]},t.id))})]})})]})}),(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center justify-between gap-1 gap-y-2 sm:gap-y-0",children:[(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:e("Blacklisted Methods")}),(0,a.jsx)(s.R,{value:E,onChange:e=>S(e.target.value),placeholder:e("Search"),iconPlacement:"end"})]})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsxs)(l.iA,{children:[(0,a.jsx)(l.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(l.SC,{children:[(0,a.jsx)(l.ss,{className:"w-2/5",children:e("Name")}),(0,a.jsx)(l.ss,{children:e("Status")}),(0,a.jsx)(l.ss,{children:e("Recommended")}),(0,a.jsx)(l.ss,{children:e("Permission")})]})}),(0,a.jsx)(l.RM,{children:w?(0,a.jsx)(l.SC,{children:(0,a.jsx)(l.pj,{colSpan:5,children:(0,a.jsx)(n.Loader,{})})}):A?.data?.blackListedGateways?.length===0?(0,a.jsx)(l.SC,{children:(0,a.jsx)(l.pj,{colSpan:5,className:"bg-accent/50",children:e("No Data")})}):A?.data?.blackListedGateways?.map(e=>new m.M(e))?.map(t=>a.jsxs(l.SC,{className:"odd:bg-accent",children:[a.jsx(l.pj,{className:"w-2/5",children:t?.name}),a.jsx(l.pj,{children:t.active?a.jsx(i.C,{variant:"success",children:e("Active")}):a.jsxs(i.C,{variant:"secondary",children:[" ",e("Inactive")," "]})}),a.jsx(l.pj,{children:t.recommended?a.jsx(i.C,{variant:"important",children:e("Yes")}):a.jsxs(i.C,{variant:"secondary",children:[" ",e("No")," "]})}),a.jsx(l.pj,{children:a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx("span",{children:e("No")}),a.jsx(c.Z,{defaultChecked:!1,disabled:!0,className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]},t?.id))})]})})]})})]})}function b({title:e,type:t,defaultStatus:r,onChange:s}){let{t:n}=(0,x.$G)();return(0,a.jsxs)(l.SC,{className:"odd:bg-accent",children:[(0,a.jsx)(l.pj,{children:e}),(0,a.jsx)(l.pj,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("span",{children:n(r?"Yes":"No")}),(0,a.jsx)(c.Z,{defaultChecked:r,onCheckedChange:e=>s({permission:t,status:e}),className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]})}},78133:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var a=r(60926);r(29220);var s=r(18662),n=r(65091),i=r(51670);function o({iconPlacement:e="start",className:t,containerClass:r,...o}){return(0,a.jsxs)("div",{className:(0,n.ZP)("relative flex items-center",r),children:[(0,a.jsx)(i.Z,{size:"20",className:(0,n.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),(0,a.jsx)(s.I,{type:"text",className:(0,n.ZP)("h-10","end"===e?"pr-10":"pl-10",t),...o})]})}},28871:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var a=r(60926),s=r(8206);r(29220);var n=r(65091);let i=(0,s.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,n.ZP)(i({variant:t}),e),...r})}},18662:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var a=r(60926),s=r(29220),n=r(65091);let i=s.forwardRef(({className:e,type:t,...r},s)=>(0,a.jsx)("input",{type:t,className:(0,n.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...r}));i.displayName="Input"},87198:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});var a=r(60926),s=r(65091);function n({className:e,...t}){return(0,a.jsx)("div",{className:(0,s.ZP)("animate-pulse rounded-md bg-muted",e),...t})}},92207:(e,t,r)=>{"use strict";r.d(t,{RM:()=>c,SC:()=>l,iA:()=>i,pj:()=>u,ss:()=>d,xD:()=>o});var a=r(60926),s=r(29220),n=r(65091);let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,n.ZP)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("thead",{ref:r,className:(0,n.ZP)("",e),...t}));o.displayName="TableHeader";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tbody",{ref:r,className:(0,n.ZP)("[&_tr:last-child]:border-0",e),...t}));c.displayName="TableBody",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tfoot",{ref:r,className:(0,n.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tr",{ref:r,className:(0,n.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));l.displayName="TableRow";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("th",{ref:r,className:(0,n.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));d.displayName="TableHead";let u=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("td",{ref:r,className:(0,n.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("caption",{ref:r,className:(0,n.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},89551:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var a=r(1181),s=r(25694);async function n(e){try{let t=await a.Z.put(`/admin/users/toggle-active/${e}`,{});return(0,s.B)(t)}catch(e){return(0,s.D)(e)}}},41911:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});var a=r(1181),s=r(25694);async function n(e,t){try{let r=await a.Z.put(`/admin/users/permission/${t}`,e);return(0,s.B)(r)}catch(e){return(0,s.D)(e)}}},23181:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var a=r(61394),s=r(29220),n=r(31036),i=r.n(n),o=["variant","color","size"],c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7.75 9.14c0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48 0 .81-.66 1.48-1.48 1.48a1.49 1.49 0 0 1-1.48-1.48Zm1.07 6.88c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.35 15.8c-.15.15-.34.22-.53.22Zm6.95.32c-.81 0-1.48-.66-1.48-1.48 0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48s-.66 1.48-1.48 1.48Z",fill:t}))},l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M2 13.05V15c0 5 2 7 7 7h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9M8.32 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M16.192 2h-8.37c-3.64 0-5.81 2.17-5.81 5.81v8.37c0 3.64 2.17 5.81 5.81 5.81h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81c0-3.64-2.17-5.81-5.81-5.81Z",fill:t}),s.createElement("path",{d:"M8.822 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM9.23 7.66c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48a1.49 1.49 0 0 0-1.48-1.48ZM15.769 13.39c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48s-.66-1.48-1.48-1.48Z",fill:t}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7ZM8.57 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M8.98 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.52 16.09a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h6c5.43 0 7.75 2.32 7.75 7.75v6c0 5.43-2.32 7.75-7.75 7.75Zm-6-20C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25V9c0-4.61-1.64-6.25-6.25-6.25H9Z",fill:t}),s.createElement("path",{d:"M8.568 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM8.98 11.108c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48a.48.48 0 0 0-.48-.48ZM15.519 16.839c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48-.21-.48-.48-.48Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"m8.32 15.27 6.54-6.54M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(c,{color:t});case"Broken":return s.createElement(l,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(u,{color:t});case"Outline":return s.createElement(m,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,n=e.color,i=e.size,c=(0,a._)(e,o);return s.createElement("svg",(0,a.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,n))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="PercentageSquare"},51670:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var a=r(61394),s=r(29220),n=r(31036),i=r.n(n),o=["variant","color","size"],c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",fill:t}),s.createElement("path",{d:"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"m22 22-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(c,{color:t});case"Broken":return s.createElement(l,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(u,{color:t});case"Outline":return s.createElement(m,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,n=e.color,i=e.size,c=(0,a._)(e,o);return s.createElement("svg",(0,a.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,n))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="SearchNormal1"},18001:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var a=r(61394),s=r(29220),n=r(31036),i=r.n(n),o=["variant","color","size"],c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM12 14.5c-5.01 0-9.09 3.36-9.09 7.5 0 .28.22.5.5.5h17.18c.28 0 .5-.22.5-.5 0-4.14-4.08-7.5-9.09-7.5Z",fill:t}))},l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M15.02 3.01A4.944 4.944 0 0 0 12 2C9.24 2 7 4.24 7 7s2.24 5 5 5 5-2.24 5-5M20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z",fill:t}),s.createElement("path",{d:"M12 14.5c-5.01 0-9.09 3.36-9.09 7.5 0 .28.22.5.5.5h17.18c.28 0 .5-.22.5-.5 0-4.14-4.08-7.5-9.09-7.5Z",fill:t}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12.75c-3.17 0-5.75-2.58-5.75-5.75S8.83 1.25 12 1.25 17.75 3.83 17.75 7s-2.58 5.75-5.75 5.75Zm0-10A4.26 4.26 0 0 0 7.75 7 4.26 4.26 0 0 0 12 11.25 4.26 4.26 0 0 0 16.25 7 4.26 4.26 0 0 0 12 2.75ZM20.59 22.75c-.41 0-.75-.34-.75-.75 0-3.45-3.52-6.25-7.84-6.25S4.16 18.55 4.16 22c0 .41-.34.75-.75.75s-.75-.34-.75-.75c0-4.27 4.19-7.75 9.34-7.75 5.15 0 9.34 3.48 9.34 7.75 0 .41-.34.75-.75.75Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"M20.59 22c0-3.87-3.85-7-8.59-7s-8.59 3.13-8.59 7",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return s.createElement(c,{color:t});case"Broken":return s.createElement(l,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(u,{color:t});case"Outline":return s.createElement(m,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},g=(0,s.forwardRef)(function(e,t){var r=e.variant,n=e.color,i=e.size,c=(0,a._)(e,o);return s.createElement("svg",(0,a.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),h(r,n))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="User"},75785:(e,t,r)=>{"use strict";r.d(t,{M:()=>a});class a{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.active=e?.active,this.activeApi=e?.activeApi,this.recommended=e?.recommended,this.variables=e?.variables,this.allowedCurrencies=e?.allowedCurrencies,this.allowedCountries=e?.allowedCountries,this.createdAt=e?.createdAt?new Date(e?.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):null}}},81379:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});class a{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.params=e?.params?JSON.parse(e?.params):null,this.currencyCode=e?.currencyCode,this.countryCode=e?.countryCode,this.active=!!e?.active,this.activeApi=!!e?.activeApi,this.recommended=!!e?.recommended,this.minAmount=e?.minAmount??0,this.maxAmount=e?.maxAmount??0,this.fixedCharge=e?.fixedCharge??0,this.percentageCharge=e?.percentageCharge,this.createdAt=e?.createdAt?new Date(e.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e.updatedAt):null}}},12771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,runtime:()=>n});var a=r(42416);r(87908);let s=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\_components\Tabbar.tsx#Tabbar`),n="edge";function i({children:e}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s,{}),e]})}},12885:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(s.a,{})})}},94695:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(s.a,{})})}},96718:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\permissions\page.tsx#default`)},29670:(e,t,r)=>{"use strict";function a({children:e}){return e}r.r(t),r.d(t,{default:()=>a}),r(87908)},74030:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(42416),s=r(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(s.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,7283,5089,3711],()=>t(97292));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/agents/[userId]/[agentId]/permissions/page"]=r}]);
//# sourceMappingURL=page.js.map