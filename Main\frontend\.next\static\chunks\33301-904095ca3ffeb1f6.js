(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[33301],{43876:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return m}});var r=n(57437),i=n(27300);function o(e){let{className:t}=e;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"62",height:"62",viewBox:"0 0 62 62",fill:"none",className:t,children:[(0,r.jsxs)("g",{clipPath:"url(#clip0_9133_12963)",children:[(0,r.jsx)("path",{d:"M31 62C48.1208 62 62 48.1208 62 31C62 13.8792 48.1208 0 31 0C13.8792 0 0 13.8792 0 31C0 48.1208 13.8792 62 31 62Z",fill:"#FFF8E0"}),(0,r.jsx)("path",{d:"M44.5125 32.1343C45.3493 32.9711 45.3493 34.328 44.5125 35.1647C43.6756 36.0015 42.3189 36.0015 41.4821 35.1647L26.8354 20.5179C25.9986 19.6811 25.9986 18.3243 26.8354 17.4876C27.6721 16.6508 29.029 16.6508 29.8657 17.4876L44.5125 32.1343ZM48.5529 21.5281C47.5767 20.5518 45.9937 20.5518 45.0175 21.5281L40.9769 25.5686L44.5124 29.1041L48.5529 25.0636C49.5293 24.0872 49.5293 22.5044 48.5529 21.5281ZM40.472 16.9825C41.4482 16.0062 41.4482 14.4233 40.472 13.447C39.4957 12.4708 37.9128 12.4708 36.9365 13.447L32.896 17.4876L36.4314 21.023L40.472 16.9825ZM25.5727 22.2857L39.7144 36.4274L34.9163 41.2255C31.0112 45.1306 24.6798 45.1306 20.7746 41.2255C16.8695 37.3203 16.8695 30.9889 20.7746 27.0838L25.5727 22.2857ZM25.739 31.8756C24.9834 32.1274 24.757 33.0864 25.3202 33.6496L27.1248 35.4541L24.2239 36.4211C23.6626 36.6082 23.3591 37.215 23.5462 37.7762C23.7333 38.3376 24.3401 38.641 24.9015 38.4539L29.447 36.9387C30.2026 36.6869 30.429 35.7279 29.8657 35.1647L28.0612 33.3602L30.9621 32.3932C31.5235 32.2062 31.8269 31.5994 31.6398 31.0381C31.4527 30.4767 30.8459 30.1734 30.2846 30.3604L25.739 31.8756ZM17.2926 40.162L16.7341 40.7205C15.7446 41.71 15.5358 43.1839 16.1063 44.3786L13.0286 47.4563C12.6102 47.8747 12.6102 48.5531 13.0286 48.9715C13.447 49.3898 14.1254 49.3898 14.5438 48.9715L17.6215 45.8937C18.8162 46.4643 20.2901 46.2554 21.2796 45.266L21.8381 44.7075C20.9149 44.1823 20.046 43.5272 19.2594 42.7407C18.4729 41.9541 17.8178 41.0852 17.2926 40.162Z",fill:"#FF7A00"})]}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_9133_12963",children:(0,r.jsx)("rect",{width:"62",height:"62",fill:"white"})})})]})}function s(e){let{className:t}=e;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"62",height:"62",viewBox:"0 0 62 62",fill:"none",className:t,children:[(0,r.jsxs)("g",{clipPath:"url(#clip0_9133_9778)",children:[(0,r.jsx)("path",{d:"M61.9014 31C61.9014 48.1208 48.0223 62 30.9014 62C-10.1753 60.4803 -10.1641 1.51355 30.9018 0C48.0223 0 61.9014 13.8792 61.9014 31Z",fill:"#EAF6FF"}),(0,r.jsx)("path",{d:"M18.7588 44.2853H43.0438V46.071C43.0438 47.8433 41.6019 49.2852 39.8296 49.2852H21.973C20.2007 49.2852 18.7588 47.8433 18.7588 46.071V44.2853ZM33.7004 15.929L34.7717 12.7148H27.0308L28.1023 15.929H33.7004ZM33.845 32.7112C33.4266 33.1296 32.7483 33.1296 32.3299 32.7112L28.2894 28.6706C27.871 28.2523 27.871 27.5739 28.2894 27.1555L29.0469 26.398L26.5216 23.8727L24.7539 25.6404C24.0576 26.3367 24.0576 27.4695 24.7539 28.1657L32.8348 36.2466C33.5311 36.9429 34.664 36.9429 35.3601 36.2466L37.1279 34.4789L34.6026 31.9536L33.845 32.7112ZM43.0438 42.1426H18.7588V15.929C18.7588 14.1567 20.2007 12.7148 21.973 12.7148H24.7721L26.3135 17.3392C26.4593 17.7767 26.8687 18.0718 27.3299 18.0718H34.4726C34.9338 18.0718 35.3432 17.7767 35.4891 17.3392L37.0305 12.7148H39.8296C41.6019 12.7148 43.0438 14.1567 43.0438 15.929V42.1426ZM39.4007 33.7213L35.3601 29.6808C34.9418 29.2624 34.2634 29.2624 33.845 29.6808L33.0874 30.4384L30.5622 27.9131L31.3197 27.1555C31.7381 26.7371 31.7381 26.0588 31.3197 25.6403L27.2792 21.5997C26.8608 21.1814 26.1824 21.1814 25.7641 21.5997L23.2388 24.125C21.7071 25.6567 21.7071 28.149 23.2388 29.6807L31.3197 37.7616C32.8514 39.2933 35.3437 39.2933 36.8754 37.7616L39.4007 35.2363C39.819 34.8181 39.819 34.1397 39.4007 33.7213Z",fill:"#09A7FF"})]}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_9133_9778",children:(0,r.jsx)("rect",{width:"62",height:"62",fill:"white"})})})]})}var a=n(3612),l=n(43949),c=n(62869),u=n(94508),d=n(61756),h=n(27648);function f(e){let{serviceName:t,description:n,href:i="/",icon:o,disabled:s=!1}=e;return(0,r.jsx)(h.default,{href:i,prefetch:!1,className:"w-full md:w-auto ".concat(s?"pointer-events-none":"group"),children:(0,r.jsx)("div",{className:(0,u.ZP)("group relative flex h-full w-full flex-col items-center justify-between rounded-lg bg-background px-6 py-4 opacity-100 shadow-defaultLite md:w-[300px]",s?"cursor-not-allowed":"hover:shadow-light-8"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(c.z,{variant:"ghost",className:"invisible absolute right-2.5 top-2.5 group-hover:visible",children:(0,r.jsx)(d.Z,{size:20})}),o,(0,r.jsx)("h5",{className:(0,u.ZP)("mb-2 mt-4 text-center font-normal",s&&"text-secondary-text"),children:t}),(0,r.jsx)("p",{className:"mb-4 text-center text-xs text-secondary-text",children:n})]})})})}function p(e){let{children:t}=e;return(0,r.jsx)("div",{className:"flex flex-col gap-4",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-4",children:t})})}function m(){let{auth:e}=(0,a.a)(),{t}=(0,l.$G)();return(null==e?void 0:e.canMakePayment())?(0,r.jsx)("div",{className:"h-full w-full overflow-y-auto bg-background p-4",children:(0,r.jsx)("div",{className:"flex flex-col gap-14",children:(0,r.jsxs)(p,{children:[(0,r.jsx)(f,{href:"/services/top-up",icon:(0,r.jsx)(s,{}),serviceName:t("Top-up"),description:"Recharge your mobile in seconds!"}),(0,r.jsx)(f,{href:"/services/electricity-bill",icon:(0,r.jsx)(o,{}),serviceName:t("Electricity bill"),description:"Skip the queues and pay your electricity bills from the comfort of your home. "})]})})}):(0,r.jsx)(i.Z,{className:"flex-1 p-10"})}n(2265)},80114:function(e,t,n){"use strict";n.d(t,{default:function(){return a}});var r=n(57437),i=n(85487),o=n(94508),s=n(43949);function a(e){let{className:t}=e,{t:n}=(0,s.$G)();return(0,r.jsx)("div",{className:(0,o.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,r.jsx)(i.Loader,{title:n("Loading..."),className:"text-foreground"})})}},85487:function(e,t,n){"use strict";n.d(t,{Loader:function(){return s}});var r=n(57437),i=n(94508),o=n(43949);function s(e){let{title:t="Loading...",className:n}=e,{t:s}=(0,o.$G)();return(0,r.jsxs)("div",{className:(0,i.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-inherit",children:s(t)})]})}},27300:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(57437),i=n(94508),o=n(27648),s=n(43949);function a(e){let{className:t}=e,{t:n}=(0,s.$G)();return(0,r.jsx)("div",{className:(0,i.ZP)("flex items-center justify-center",t),children:(0,r.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[(0,r.jsx)("h3",{className:"mb-2.5",children:n("This feature is temporarily unavailable")}),(0,r.jsxs)("p",{className:"text-sm text-secondary-text",children:[n("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),(0,r.jsx)(o.default,{href:"/contact-supports",className:"text-primary hover:underline",children:n("support")}),"."]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-secondary-text",children:n("Thank you for your understanding.")})]})})}},62869:function(e,t,n){"use strict";n.d(t,{d:function(){return l},z:function(){return c}});var r=n(57437),i=n(37053),o=n(90535),s=n(2265),a=n(94508);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:n,variant:o,size:s,asChild:c=!1,...u}=e,d=c?i.g7:"button";return(0,r.jsx)(d,{className:(0,a.ZP)(l({variant:o,size:s,className:n})),ref:t,...u})});c.displayName="Button"},17062:function(e,t,n){"use strict";n.d(t,{Z:function(){return m},O:function(){return p}});var r=n(57437),i=n(80114);n(83079);var o=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),s=n(31117),a=n(79981),l=n(78040),c=n(83130);class u{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var d=n(99376),h=n(2265);let f=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),p=()=>h.useContext(f);function m(e){let{children:t}=e,[n,p]=h.useState("Desktop"),[m,v]=h.useState(!1),[g,y]=h.useState(),{data:x,isLoading:w,error:C,mutate:b}=(0,s.d)("/auth/check",{revalidateOnFocus:!1}),{data:L,isLoading:j}=(0,s.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:k,isLoading:S}=(0,s.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),A=(0,d.useRouter)(),E=(0,d.usePathname)();h.useEffect(()=>{(async()=>{p((await o()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;p(e<768?"Mobile":e<1024?"Tablet":"Desktop"),v(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await a.Z.post("/auth/geo-location");y(new u(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{C&&!l.sp.includes(E)&&A.push("/signin")},[C]);let P=h.useMemo(()=>{var e,t,r;return{isAuthenticate:!!(null==x?void 0:null===(e=x.data)||void 0===e?void 0:e.login),auth:(null==x?void 0:null===(t=x.data)||void 0===t?void 0:t.user)?new c.n(null==x?void 0:null===(r=x.data)||void 0===r?void 0:r.user):null,isLoading:w,deviceLocation:g,refreshAuth:()=>b(x),isExpanded:m,device:n,setIsExpanded:v,branding:null==L?void 0:L.data,googleAnalytics:(null==k?void 0:k.data)?{active:null==k?void 0:k.data.active,apiKey:null==k?void 0:k.data.apiKey}:{active:!1,apiKey:""}}},[x,g,m,n]),N=!w&&!j&&!S;return(0,r.jsx)(f.Provider,{value:P,children:N?t:(0,r.jsx)(i.default,{})})}},3612:function(e,t,n){"use strict";n.d(t,{a:function(){return i}});var r=n(17062);let i=()=>{let e=(0,r.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},31117:function(e,t,n){"use strict";n.d(t,{d:function(){return o}});var r=n(79981),i=n(85323);let o=(e,t)=>(0,i.ZP)(e||null,e=>r.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,n){"use strict";var r=n(78040),i=n(83464);t.Z=i.default.create({baseURL:r.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,n){"use strict";n.d(t,{rH:function(){return r},sp:function(){return i}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},i=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){"use strict";n.d(t,{F:function(){return u},Fg:function(){return f},Fp:function(){return c},Qp:function(){return h},ZP:function(){return a},fl:function(){return l},qR:function(){return d},w4:function(){return p}});var r=n(78040),i=n(61994),o=n(14438),s=n(53335);function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.m6)((0,i.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>o.toast.success("Copied to clipboard!")).catch(()=>{o.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let i;let o=void 0===t?this.currencyCode:t;try{i=new Intl.NumberFormat("en-US",{style:"currency",currency:o,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){i=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let s=null!==(r=null===(n=i.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:o,a=i.format(e),l=a.substring(s.length).trim();return{currencyCode:o,currencySymbol:s,formattedAmount:a,amountText:l}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",h=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",i=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?i.set(r,e):i.delete(r),i}},61756:function(e,t,n){"use strict";n.d(t,{Z:function(){return m}});var r=n(74677),i=n(2265),o=n(40718),s=n.n(o),a=["variant","color","size"],l=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2zm2.34 10.53l-4.29 4.29c-.15.15-.34.22-.53.22s-.38-.07-.53-.22a.754.754 0 010-1.06l3.01-3.01H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h10.19l-3.01-3.01a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l4.29 4.29a.75.75 0 010 1.06z"}))},c=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M14.43 5.93L20.5 12l-6.07 6.07M11.01 12h9.32M3.5 12h3.47"}))},u=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2z",opacity:".4"}),i.createElement("path",{fill:t,d:"M18.53 11.47l-4.29-4.29a.754.754 0 00-1.06 0c-.29.29-.29.77 0 1.06l3.01 3.01H6c-.41 0-.75.34-.75.75s.34.75.75.75h10.19l-3.01 3.01c-.29.29-.29.77 0 1.06.15.15.34.22.53.22s.38-.07.53-.22l4.29-4.29a.75.75 0 000-1.06z"}))},d=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M14.43 5.93L20.5 12l-6.07 6.07M3.5 12h16.83"}))},h=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M14.43 18.82c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06L19.44 12 13.9 6.46a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.07 6.07c.29.29.29.77 0 1.06l-6.07 6.07c-.15.15-.34.22-.53.22z"}),i.createElement("path",{fill:t,d:"M20.33 12.75H3.5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h16.83c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},f=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M14.43 5.93L20.5 12l-6.07 6.07"}),i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M3.5 12h16.83",opacity:".4"}))},p=function(e,t){switch(e){case"Bold":return i.createElement(l,{color:t});case"Broken":return i.createElement(c,{color:t});case"Bulk":return i.createElement(u,{color:t});case"Linear":default:return i.createElement(d,{color:t});case"Outline":return i.createElement(h,{color:t});case"TwoTone":return i.createElement(f,{color:t})}},m=(0,i.forwardRef)(function(e,t){var n=e.variant,o=e.color,s=e.size,l=(0,r._)(e,a);return i.createElement("svg",(0,r.a)({},l,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(n,o))});m.propTypes={variant:s().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:s().string,size:s().oneOfType([s().string,s().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="ArrowRight"},74677:function(e,t,n){"use strict";function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function i(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}n.d(t,{_:function(){return i},a:function(){return r}})},99376:function(e,t,n){"use strict";var r=n(35475);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useSelectedLayoutSegment")&&n.d(t,{useSelectedLayoutSegment:function(){return r.useSelectedLayoutSegment}}),n.o(r,"useSelectedLayoutSegments")&&n.d(t,{useSelectedLayoutSegments:function(){return r.useSelectedLayoutSegments}})},12119:function(e,t,n){"use strict";Object.defineProperty(t,"$",{enumerable:!0,get:function(){return i}});let r=n(83079);function i(e){let{createServerReference:t}=n(6671);return t(e,r.callServer)}},25523:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(47043)._(n(2265)).default.createContext(null)},48049:function(e,t,n){"use strict";var r=n(14397);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,o,s){if(s!==r){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return n.PropTypes=n,n}},40718:function(e,t,n){e.exports=n(48049)()},14397:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},74539:function(e,t,n){"use strict";n.d(t,{k:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){"use strict";n.d(t,{n:function(){return l}});class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var i=n(84937);class o{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var s=n(66419),a=n(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(a.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new s.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new i.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new o(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new r(e.agent):void 0}}},84937:function(e,t,n){"use strict";n.d(t,{O:function(){return i}});var r=n(74539);class i{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new r.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){"use strict";n.d(t,{u:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},98575:function(e,t,n){"use strict";n.d(t,{F:function(){return o},e:function(){return s}});var r=n(2265);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function s(...e){return r.useCallback(o(...e),e)}},37053:function(e,t,n){"use strict";n.d(t,{Z8:function(){return s},g7:function(){return a},sA:function(){return c}});var r=n(2265),i=n(98575),o=n(57437);function s(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,s;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,l=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{o(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(l.ref=t?(0,i.F)(t,a):a),r.cloneElement(n,l)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...s}=e,a=r.Children.toArray(i),l=a.find(u);if(l){let e=l.props.children,i=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...s,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...s,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var a=s("Slot"),l=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},90535:function(e,t,n){"use strict";n.d(t,{j:function(){return s}});var r=n(61994);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.W,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:a}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...c}[t]):({...a,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}}]);