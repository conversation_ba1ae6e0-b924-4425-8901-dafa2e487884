(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[54141],{37012:function(e,n,u){Promise.resolve().then(u.bind(u,75171))},75171:function(e,n,u){"use strict";u.r(n),u.d(n,{default:function(){return d}});var r=u(57437),t=u(45702),l=u(68332),s=u(61149),i=u(6596),a=u(68338);function d(){let{data:e,user:n,address:u,error:d,isLoading:o}=(0,a.h)();return(0,r.jsx)(i.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","ADDRESS_INFORMATION","PASSWORD_INFORMATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,r.jsx)(s.O,{user:n(e),isLoading:o,error:d}),(0,r.jsx)(t.h,{address:u(e)}),(0,r.jsx)(l.G,{})]})})}},68338:function(e,n,u){"use strict";u.d(n,{h:function(){return i}});var r=u(79981),t=u(74539),l=u(70517),s=u(85323);function i(){let{data:e,error:n,isLoading:u}=(0,s.ZP)("/customers/detail",e=>r.Z.get(e));return{data:null==e?void 0:e.data,user:e=>e?new l.n({...e,...null==e?void 0:e.user,avatar:null==e?void 0:e.profileImage}):null,address:e=>e?new t.k(null==e?void 0:e.address):null,isLoading:u,error:n}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,31384,60627,85598,77317,227,45967,92971,95030,1744],function(){return e(e.s=37012)}),_N_E=e.O()}]);