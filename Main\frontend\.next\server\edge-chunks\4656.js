"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4656],{73806:(e,a,s)=>{s.d(a,{g:()=>p});var t=s(60926),l=s(29220),r=s(29411),d=s(7643),i=s(98019),o=s(23183),n=s(92773),c=s(65091),f=s(86059),m=s(39228);function p({allCountry:e=!1,defaultValue:a,defaultCountry:s,onSelectChange:p,disabled:u=!1,triggerClassName:x,arrowClassName:h,flagClassName:b,display:N,placeholderClassName:j,align:y="start",side:g="bottom"}){let{t:w}=(0,m.$G)(),{countries:Z,getCountryByCode:v,isLoading:P}=(0,n.F)(),[R,C]=l.useState(!1),[_,k]=l.useState(a);return l.useEffect(()=>{a&&k(a)},[a]),l.useEffect(()=>{(async()=>{s&&await v(s,e=>{e&&(k(e),p(e))})})()},[s]),(0,t.jsxs)(o.J2,{open:R,onOpenChange:C,children:[(0,t.jsxs)(o.xo,{disabled:u,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",x),children:[_?(0,t.jsx)("div",{className:"flex flex-1 items-center",children:(0,t.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,t.jsx)(d.W,{className:b,countryCode:_.code?.cca2==="*"?"UN":_.code?.cca2}),void 0!==N?N(_):(0,t.jsx)("span",{children:_.name})]})}):(0,t.jsx)("span",{className:(0,c.ZP)("text-placeholder",j),children:w("Select country")}),(0,t.jsx)(f.Z,{className:(0,c.ZP)("size-6",h)})]}),(0,t.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:y,side:g,children:(0,t.jsxs)(i.mY,{children:[(0,t.jsx)(i.sZ,{placeholder:w("Search...")}),(0,t.jsx)(i.e8,{children:(0,t.jsxs)(i.fu,{children:[P&&(0,t.jsx)(r.Loader,{}),e&&(0,t.jsxs)(i.di,{value:w("All countries"),onSelect:()=>{k({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),p({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),C(!1)},children:[(0,t.jsx)(d.W,{countryCode:"UN"}),(0,t.jsx)("span",{className:"pl-1.5",children:w("All countries")})]}),Z?.map(e=>"officially-assigned"===e.status?t.jsxs(i.di,{value:e.name,onSelect:()=>{k(e),p(e),C(!1)},children:[t.jsx(d.W,{countryCode:e.code.cca2}),t.jsxs("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},78133:(e,a,s)=>{s.d(a,{R:()=>i});var t=s(60926);s(29220);var l=s(18662),r=s(65091),d=s(51670);function i({iconPlacement:e="start",className:a,containerClass:s,...i}){return(0,t.jsxs)("div",{className:(0,r.ZP)("relative flex items-center",s),children:[(0,t.jsx)(d.Z,{size:"20",className:(0,r.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),(0,t.jsx)(l.I,{type:"text",className:(0,r.ZP)("h-10","end"===e?"pr-10":"pl-10",a),...i})]})}},7643:(e,a,s)=>{s.d(a,{W:()=>d});var t=s(60926),l=s(65091),r=s(28277);function d({countryCode:e,className:a,url:s}){return e||s?(0,t.jsx)(r.Z,{src:s??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,l.ZP)("rounded-[2px]",a)}):null}},18662:(e,a,s)=>{s.d(a,{I:()=>d});var t=s(60926),l=s(29220),r=s(65091);let d=l.forwardRef(({className:e,type:a,...s},l)=>(0,t.jsx)("input",{type:a,className:(0,r.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:l,...s}));d.displayName="Input"},66817:(e,a,s)=>{s.d(a,{Z:()=>c});var t=s(60926),l=s(11537),r=s(8206),d=s(29220),i=s(65091);let o=(0,r.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=d.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.f,{ref:s,className:(0,i.ZP)(o(),e),...a}));n.displayName=l.f.displayName;let c=n},83968:(e,a,s)=>{s.d(a,{Bw:()=>h,Ph:()=>f,Ql:()=>b,i4:()=>p,ki:()=>m});var t=s(60926),l=s(52959),r=s(589),d=s(10471),i=s(29220),o=s(65091),n=s(86059),c=s(14761);let f=l.fC;l.ZA;let m=l.B4,p=i.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(l.xz,{ref:r,className:(0,o.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,(0,t.jsx)(l.JO,{asChild:!0,children:(0,t.jsx)(n.Z,{size:"24",color:"#292D32"})})]}));p.displayName=l.xz.displayName;let u=i.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.u_,{ref:s,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(r.Z,{className:"h-4 w-4"})}));u.displayName=l.u_.displayName;let x=i.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.$G,{ref:s,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(d.Z,{className:"h-4 w-4"})}));x.displayName=l.$G.displayName;let h=i.forwardRef(({className:e,children:a,position:s="popper",...r},d)=>(0,t.jsx)(l.h_,{children:(0,t.jsxs)(l.VY,{ref:d,className:(0,o.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,t.jsx)(u,{}),(0,t.jsx)(l.l_,{className:(0,o.ZP)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(x,{})]})}));h.displayName=l.VY.displayName,i.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.__,{ref:s,className:(0,o.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=l.__.displayName;let b=i.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(l.ck,{ref:r,className:(0,o.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.wU,{children:(0,t.jsx)(c.Z,{variant:"Bold",className:"h-4 w-4"})})}),(0,t.jsx)(l.eT,{children:a})]}));b.displayName=l.ck.displayName,i.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.Z0,{ref:s,className:(0,o.ZP)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=l.Z0.displayName},92207:(e,a,s)=>{s.d(a,{RM:()=>o,SC:()=>n,iA:()=>d,pj:()=>f,ss:()=>c,xD:()=>i});var t=s(60926),l=s(29220),r=s(65091);let d=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,r.ZP)("w-full caption-bottom text-sm",e),...a})}));d.displayName="Table";let i=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("thead",{ref:s,className:(0,r.ZP)("",e),...a}));i.displayName="TableHeader";let o=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tbody",{ref:s,className:(0,r.ZP)("[&_tr:last-child]:border-0",e),...a}));o.displayName="TableBody",l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tfoot",{ref:s,className:(0,r.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let n=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tr",{ref:s,className:(0,r.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));n.displayName="TableRow";let c=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("th",{ref:s,className:(0,r.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));c.displayName="TableHead";let f=l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("td",{ref:s,className:(0,r.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));f.displayName="TableCell",l.forwardRef(({className:e,...a},s)=>(0,t.jsx)("caption",{ref:s,className:(0,r.ZP)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},92773:(e,a,s)=>{s.d(a,{F:()=>n});class t{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var l=s(82844),r=s(32167),d=s(32898);let i=l.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),o="name,cca2,ccn3,cca3,status,flag,flags";function n(){let{data:e,isLoading:a,...s}=(0,d.ZP)(`/all?fields=${o}`,e=>i.get(e)),n=e?.data,c=async(e,a)=>{try{let s=await i.get(`/alpha/${e.toLowerCase()}?fields=${o}`),l=s.data?new t(s.data):null;a(l)}catch(e){l.default.isAxiosError(e)&&r.toast.error("Failed to fetch country")}};return{countries:n?n.map(e=>new t(e)):[],isLoading:a,getCountryByCode:c,...s}}}}]);
//# sourceMappingURL=4656.js.map