(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[50693],{61537:function(e,a,s){Promise.resolve().then(s.bind(s,87846))},87846:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return f}});var t=s(57437),n=s(45504),r=s(85539),l=s(27186),c=s(85017),i=s(6512),u=s(75730),d=s(94508),o=s(99376),h=s(2265),m=s(43949);function f(){var e;let{t:a}=(0,m.$G)(),s=(0,o.useSearchParams)(),[f,x]=h.useState(null!==(e=s.get("search"))&&void 0!==e?e:""),v=(0,o.useRouter)(),w=(0,o.usePathname)(),{data:p,isLoading:g,meta:j,refresh:N}=(0,u.Z)("/admin/withdraws?".concat(s.toString()));return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(r.R,{value:f,onChange:e=>{e.preventDefault();let a=(0,d.w4)(e.target.value);x(e.target.value),v.replace("".concat(w,"?").concat(a.toString()))},iconPlacement:"end",placeholder:a("Search..."),containerClass:"w-full sm:w-auto min-w-64"}),(0,t.jsx)(c.k,{canFilterByMethod:!0}),(0,t.jsx)(l._,{url:"/admin/withdraws/export/all"})]}),(0,t.jsx)("div",{})]}),(0,t.jsx)(i.Z,{className:"my-4"}),(0,t.jsx)(n.Z,{data:p,meta:j,isLoading:g,refresh:N})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,227,56993,85017,1804,92971,95030,1744],function(){return e(e.s=61537)}),_N_E=e.O()}]);