(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[45966],{47239:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{html:t,height:n=null,width:s=null,children:o,dataNtpc:a=""}=e;return(0,i.useEffect)(()=>{a&&performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-".concat(a)}})},[a]),(0,r.jsxs)(r.Fragment,{children:[o,t?(0,r.jsx)("div",{style:{height:null!=n?"".concat(n,"px"):"auto",width:null!=s?"".concat(s,"px"):"auto"},"data-ntpc":a,dangerouslySetInnerHTML:{__html:t}}):null]})};let r=n(57437),i=n(2265)},14888:function(e,t,n){"use strict";var r;let i;Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleAnalytics=function(e){let{gaId:t,debugMode:n,dataLayerName:r="dataLayer",nonce:l}=e;return void 0===i&&(i=r),(0,o.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-ga"}})},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{id:"_next-ga-init",dangerouslySetInnerHTML:{__html:"\n          window['".concat(r,"'] = window['").concat(r,"'] || [];\n          function gtag(){window['").concat(r,"'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '").concat(t,"' ").concat(n?",{ 'debug_mode': true }":"",");")},nonce:l}),(0,s.jsx)(a.default,{id:"_next-ga",src:"https://www.googletagmanager.com/gtag/js?id=".concat(t),nonce:l})]})},t.sendGAEvent=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(void 0===i){console.warn("@next/third-parties: GA has not been initialized");return}window[i]?window[i].push(arguments):console.warn("@next/third-parties: GA dataLayer ".concat(i," does not exist"))};let s=n(57437),o=n(2265),a=(r=n(48667))&&r.__esModule?r:{default:r}},81992:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{apiKey:t,...n}=e,r={...n,key:t},{html:a}=(0,s.GoogleMapsEmbed)(r);return(0,i.jsx)(o.default,{height:r.height||null,width:r.width||null,html:a,dataNtpc:"GoogleMapsEmbed"})};let i=n(57437),s=n(4855),o=r(n(47239))},98087:function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.sendGTMEvent=void 0,t.GoogleTagManager=function(e){let{gtmId:t,gtmScriptUrl:n="https://www.googletagmanager.com/gtm.js",dataLayerName:r="dataLayer",auth:l,preview:u,dataLayer:c,nonce:d}=e;a=r;let h="dataLayer"!==r?"&l=".concat(r):"";return(0,s.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-gtm"}})},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o.default,{id:"_next-gtm-init",dangerouslySetInnerHTML:{__html:"\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ".concat(c?"w[l].push(".concat(JSON.stringify(c),")"):"","\n      })(window,'").concat(r,"');")},nonce:d}),(0,i.jsx)(o.default,{id:"_next-gtm","data-ntpc":"GTM",src:"".concat(n,"?id=").concat(t).concat(h).concat(l?"&gtm_auth=".concat(l):"").concat(u?"&gtm_preview=".concat(u,"&gtm_cookies_win=x"):""),nonce:d})]})};let i=n(57437),s=n(2265),o=(r=n(48667))&&r.__esModule?r:{default:r},a="dataLayer";t.sendGTMEvent=(e,t)=>{let n=t||a;window[n]=window[n]||[],window[n].push(e)}},13786:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.sendGAEvent=t.GoogleAnalytics=t.sendGTMEvent=t.GoogleTagManager=t.YouTubeEmbed=t.GoogleMapsEmbed=void 0;var i=n(81992);Object.defineProperty(t,"GoogleMapsEmbed",{enumerable:!0,get:function(){return r(i).default}});var s=n(60538);Object.defineProperty(t,"YouTubeEmbed",{enumerable:!0,get:function(){return r(s).default}});var o=n(98087);Object.defineProperty(t,"GoogleTagManager",{enumerable:!0,get:function(){return o.GoogleTagManager}}),Object.defineProperty(t,"sendGTMEvent",{enumerable:!0,get:function(){return o.sendGTMEvent}});var a=n(14888);Object.defineProperty(t,"GoogleAnalytics",{enumerable:!0,get:function(){return a.GoogleAnalytics}}),Object.defineProperty(t,"sendGAEvent",{enumerable:!0,get:function(){return a.sendGAEvent}})},60538:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{html:t,scripts:n,stylesheets:r}=(0,o.YouTubeEmbed)(e);return(0,i.jsx)(a.default,{height:e.height||null,width:e.width||null,html:t,dataNtpc:"YouTubeEmbed",children:null==n?void 0:n.map(e=>(0,i.jsx)(s.default,{src:e.url,strategy:l[e.strategy],stylesheets:r},e.url))})};let i=n(57437),s=r(n(48667)),o=n(4855),a=r(n(47239)),l={server:"beforeInteractive",client:"afterInteractive",idle:"lazyOnload",worker:"worker"}},59408:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(2265),i={exports:{}};function s(){}function o(){}o.resetWarningCache=s,i.exports=function(){function e(e,t,n,r,i,s){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==s){var o=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:s};return n.PropTypes=n,n}();var a=i.exports;let l=e=>!!e&&0!==e.length&&null!=e&&"string"==typeof e,u=({propertyId:e="",widgetId:t="",embedId:n="",basePath:r="tawk.to"})=>{if(n.length){if(!document.getElementById(n)){let e=document.createElement("div");e.id=n,document.body.appendChild(e)}window.Tawk_API.embedded=n}let i=document.createElement("script");i.async=!0,i.src=`https://embed.${r}/${e}/${t}`,i.charset="UTF-8",i.setAttribute("crossorigin","*");let s=document.getElementsByTagName("script")[0];s.parentNode.insertBefore(i,s)},c=(0,r.forwardRef)((e,t)=>{(0,r.useEffect)(()=>{n()},[]);let n=()=>{if(!l(e.propertyId)){console.error("[Tawk-messenger-react warn]: You didn't specified 'propertyId' property in the plugin.");return}if(!l(e.widgetId)){console.error("[Tawk-messenger-react warn]: You didn't specified 'widgetId' property in the plugin.");return}window&&document&&i()},i=()=>{window.Tawk_API=window.Tawk_API||{},window.Tawk_LoadStart=new Date,u({propertyId:e.propertyId,widgetId:e.widgetId,embedId:e.embedId,basePath:e.basePath}),e.customStyle&&"object"==typeof e.customStyle&&(window.Tawk_API.customStyle=e.customStyle),s()};(0,r.useImperativeHandle)(t,()=>({maximize:()=>window.Tawk_API.maximize(),minimize:()=>window.Tawk_API.minimize(),toggle:()=>window.Tawk_API.toggle(),popup:()=>window.Tawk_API.popup(),showWidget:()=>window.Tawk_API.showWidget(),hideWidget:()=>window.Tawk_API.hideWidget(),toggleVisibility:()=>window.Tawk_API.toggleVisibility(),endChat:()=>window.Tawk_API.endChat(),getWindowType:()=>window.Tawk_API.getWindowType(),getStatus:()=>window.Tawk_API.getStatus(),isChatMaximized:()=>window.Tawk_API.isChatMaximized(),isChatMinimized:()=>window.Tawk_API.isChatMinimized(),isChatHidden:()=>window.Tawk_API.isChatHidden(),isChatOngoing:()=>window.Tawk_API.isChatOngoing(),isVisitorEngaged:()=>window.Tawk_API.isVisitorEngaged(),onLoaded:()=>window.Tawk_API.onLoaded,onBeforeLoaded:()=>window.Tawk_API.onBeforeLoaded,widgetPosition:()=>window.Tawk_API.widgetPosition(),visitor:e=>{window.Tawk_API.visitor=e},setAttributes:(e,t)=>{window.Tawk_API.setAttributes(e,t)},addEvent:(e,t,n)=>{window.Tawk_API.addEvent(e,t,n)},addTags:(e,t)=>{window.Tawk_API.addTags(e,t)},removeTags:(e,t)=>{window.Tawk_API.removeTags(e,t)}}));let s=()=>{window.addEventListener("tawkLoad",()=>{e.onLoad()}),window.addEventListener("tawkStatusChange",t=>{e.onStatusChange(t.detail)}),window.addEventListener("tawkBeforeLoad",()=>{e.onBeforeLoad()}),window.addEventListener("tawkChatMaximized",()=>{e.onChatMaximized()}),window.addEventListener("tawkChatMinimized",()=>{e.onChatMinimized()}),window.addEventListener("tawkChatHidden",()=>{e.onChatHidden()}),window.addEventListener("tawkChatStarted",()=>{e.onChatStarted()}),window.addEventListener("tawkChatEnded",()=>{e.onChatEnded()}),window.addEventListener("tawkPrechatSubmit",t=>{e.onPrechatSubmit(t.detail)}),window.addEventListener("tawkOfflineSubmit",t=>{e.onOfflineSubmit(t.detail)}),window.addEventListener("tawkChatMessageVisitor",t=>{e.onChatMessageVisitor(t.detail)}),window.addEventListener("tawkChatMessageAgent",t=>{e.onChatMessageAgent(t.detail)}),window.addEventListener("tawkChatMessageSystem",t=>{e.onChatMessageSystem(t.detail)}),window.addEventListener("tawkAgentJoinChat",t=>{e.onAgentJoinChat(t.detail)}),window.addEventListener("tawkAgentLeaveChat",t=>{e.onAgentLeaveChat(t.detail)}),window.addEventListener("tawkChatSatisfaction",t=>{e.onChatSatisfaction(t.detail)}),window.addEventListener("tawkVisitorNameChanged",t=>{e.onVisitorNameChanged(t.detail)}),window.addEventListener("tawkFileUpload",t=>{e.onFileUpload(t.detail)}),window.addEventListener("tawkTagsUpdated",t=>{e.onTagsUpdated(t.detail)}),window.addEventListener("tawkUnreadCountChanged",t=>{e.onUnreadCountChanged(t.detail)})};return null});c.displayName="TawkMessenger",c.defaultProps={customStyle:null,embedId:"",basePath:"tawk.to",onLoad:()=>{},onStatusChange:()=>{},onBeforeLoad:()=>{},onChatMaximized:()=>{},onChatMinimized:()=>{},onChatHidden:()=>{},onChatStarted:()=>{},onChatEnded:()=>{},onPrechatSubmit:()=>{},onOfflineSubmit:()=>{},onChatMessageVisitor:()=>{},onChatMessageAgent:()=>{},onChatMessageSystem:()=>{},onAgentJoinChat:()=>{},onAgentLeaveChat:()=>{},onChatSatisfaction:()=>{},onVisitorNameChanged:()=>{},onFileUpload:()=>{},onTagsUpdated:()=>{},onUnreadCountChanged:()=>{}},c.propTypes={propertyId:a.string.isRequired,widgetId:a.string.isRequired,customStyle:a.object,embedId:a.string,basePath:a.string,onLoad:a.func,onStatusChange:a.func,onBeforeLoad:a.func,onChatMaximized:a.func,onChatMinimized:a.func,onChatHidden:a.func,onChatStarted:a.func,onChatEnded:a.func,onPrechatSubmit:a.func,onOfflineSubmit:a.func,onChatMessageVisitor:a.func,onChatMessageAgent:a.func,onChatMessageSystem:a.func,onAgentJoinChat:a.func,onAgentLeaveChat:a.func,onChatSatisfaction:a.func,onVisitorNameChanged:a.func,onFileUpload:a.func,onTagsUpdated:a.func,onUnreadCountChanged:a.func}},80751:function(e,t,n){var r="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==n.g&&n.g,i=function(){function e(){this.fetch=!1,this.DOMException=r.DOMException}return e.prototype=r,new e}();(function(e){var t=void 0!==i&&i||"undefined"!=typeof self&&self||void 0!==t&&t,n={searchParams:"URLSearchParams"in t,iterable:"Symbol"in t&&"iterator"in Symbol,blob:"FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(e){return!1}}(),formData:"FormData"in t,arrayBuffer:"ArrayBuffer"in t};if(n.arrayBuffer)var r=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],s=ArrayBuffer.isView||function(e){return e&&r.indexOf(Object.prototype.toString.call(e))>-1};function o(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e)||""===e)throw TypeError('Invalid character in header field name: "'+e+'"');return e.toLowerCase()}function a(e){return"string"!=typeof e&&(e=String(e)),e}function l(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return n.iterable&&(t[Symbol.iterator]=function(){return t}),t}function u(e){this.map={},e instanceof u?e.forEach(function(e,t){this.append(t,e)},this):Array.isArray(e)?e.forEach(function(e){this.append(e[0],e[1])},this):e&&Object.getOwnPropertyNames(e).forEach(function(t){this.append(t,e[t])},this)}function c(e){if(e.bodyUsed)return Promise.reject(TypeError("Already read"));e.bodyUsed=!0}function d(e){return new Promise(function(t,n){e.onload=function(){t(e.result)},e.onerror=function(){n(e.error)}})}function h(e){var t=new FileReader,n=d(t);return t.readAsArrayBuffer(e),n}function p(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function f(){return this.bodyUsed=!1,this._initBody=function(e){if(this.bodyUsed=this.bodyUsed,this._bodyInit=e,e){if("string"==typeof e)this._bodyText=e;else if(n.blob&&Blob.prototype.isPrototypeOf(e))this._bodyBlob=e;else if(n.formData&&FormData.prototype.isPrototypeOf(e))this._bodyFormData=e;else if(n.searchParams&&URLSearchParams.prototype.isPrototypeOf(e))this._bodyText=e.toString();else{var t;n.arrayBuffer&&n.blob&&(t=e)&&DataView.prototype.isPrototypeOf(t)?(this._bodyArrayBuffer=p(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):n.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(e)||s(e))?this._bodyArrayBuffer=p(e):this._bodyText=e=Object.prototype.toString.call(e)}}else this._bodyText="";!this.headers.get("content-type")&&("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):n.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},n.blob&&(this.blob=function(){var e=c(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(!this._bodyFormData)return Promise.resolve(new Blob([this._bodyText]));throw Error("could not read FormData body as blob")},this.arrayBuffer=function(){return this._bodyArrayBuffer?c(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer)):this.blob().then(h)}),this.text=function(){var e,t,n,r=c(this);if(r)return r;if(this._bodyBlob)return e=this._bodyBlob,n=d(t=new FileReader),t.readAsText(e),n;if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),n=Array(t.length),r=0;r<t.length;r++)n[r]=String.fromCharCode(t[r]);return n.join("")}(this._bodyArrayBuffer));if(!this._bodyFormData)return Promise.resolve(this._bodyText);throw Error("could not read FormData body as text")},n.formData&&(this.formData=function(){return this.text().then(y)}),this.json=function(){return this.text().then(JSON.parse)},this}u.prototype.append=function(e,t){e=o(e),t=a(t);var n=this.map[e];this.map[e]=n?n+", "+t:t},u.prototype.delete=function(e){delete this.map[o(e)]},u.prototype.get=function(e){return e=o(e),this.has(e)?this.map[e]:null},u.prototype.has=function(e){return this.map.hasOwnProperty(o(e))},u.prototype.set=function(e,t){this.map[o(e)]=a(t)},u.prototype.forEach=function(e,t){for(var n in this.map)this.map.hasOwnProperty(n)&&e.call(t,this.map[n],n,this)},u.prototype.keys=function(){var e=[];return this.forEach(function(t,n){e.push(n)}),l(e)},u.prototype.values=function(){var e=[];return this.forEach(function(t){e.push(t)}),l(e)},u.prototype.entries=function(){var e=[];return this.forEach(function(t,n){e.push([n,t])}),l(e)},n.iterable&&(u.prototype[Symbol.iterator]=u.prototype.entries);var g=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function m(e,t){if(!(this instanceof m))throw TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var n,r,i=(t=t||{}).body;if(e instanceof m){if(e.bodyUsed)throw TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new u(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,i||null==e._bodyInit||(i=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",(t.headers||!this.headers)&&(this.headers=new u(t.headers)),this.method=(r=(n=t.method||this.method||"GET").toUpperCase(),g.indexOf(r)>-1?r:n),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(i),("GET"===this.method||"HEAD"===this.method)&&("no-store"===t.cache||"no-cache"===t.cache)){var s=/([?&])_=[^&]*/;s.test(this.url)?this.url=this.url.replace(s,"$1_="+new Date().getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+new Date().getTime()}}function y(e){var t=new FormData;return e.trim().split("&").forEach(function(e){if(e){var n=e.split("="),r=n.shift().replace(/\+/g," "),i=n.join("=").replace(/\+/g," ");t.append(decodeURIComponent(r),decodeURIComponent(i))}}),t}function b(e,t){if(!(this instanceof b))throw TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');t||(t={}),this.type="default",this.status=void 0===t.status?200:t.status,this.ok=this.status>=200&&this.status<300,this.statusText=void 0===t.statusText?"":""+t.statusText,this.headers=new u(t.headers),this.url=t.url||"",this._initBody(e)}m.prototype.clone=function(){return new m(this,{body:this._bodyInit})},f.call(m.prototype),f.call(b.prototype),b.prototype.clone=function(){return new b(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new u(this.headers),url:this.url})},b.error=function(){var e=new b(null,{status:0,statusText:""});return e.type="error",e};var v=[301,302,303,307,308];b.redirect=function(e,t){if(-1===v.indexOf(t))throw RangeError("Invalid status code");return new b(null,{status:t,headers:{location:e}})},e.DOMException=t.DOMException;try{new e.DOMException}catch(t){e.DOMException=function(e,t){this.message=e,this.name=t;var n=Error(e);this.stack=n.stack},e.DOMException.prototype=Object.create(Error.prototype),e.DOMException.prototype.constructor=e.DOMException}function w(r,i){return new Promise(function(s,o){var l=new m(r,i);if(l.signal&&l.signal.aborted)return o(new e.DOMException("Aborted","AbortError"));var c=new XMLHttpRequest;function d(){c.abort()}c.onload=function(){var e,t,n={status:c.status,statusText:c.statusText,headers:(e=c.getAllResponseHeaders()||"",t=new u,e.replace(/\r?\n[\t ]+/g," ").split("\r").map(function(e){return 0===e.indexOf("\n")?e.substr(1,e.length):e}).forEach(function(e){var n=e.split(":"),r=n.shift().trim();if(r){var i=n.join(":").trim();t.append(r,i)}}),t)};n.url="responseURL"in c?c.responseURL:n.headers.get("X-Request-URL");var r="response"in c?c.response:c.responseText;setTimeout(function(){s(new b(r,n))},0)},c.onerror=function(){setTimeout(function(){o(TypeError("Network request failed"))},0)},c.ontimeout=function(){setTimeout(function(){o(TypeError("Network request failed"))},0)},c.onabort=function(){setTimeout(function(){o(new e.DOMException("Aborted","AbortError"))},0)},c.open(l.method,function(e){try{return""===e&&t.location.href?t.location.href:e}catch(t){return e}}(l.url),!0),"include"===l.credentials?c.withCredentials=!0:"omit"===l.credentials&&(c.withCredentials=!1),"responseType"in c&&(n.blob?c.responseType="blob":n.arrayBuffer&&l.headers.get("Content-Type")&&-1!==l.headers.get("Content-Type").indexOf("application/octet-stream")&&(c.responseType="arraybuffer")),!i||"object"!=typeof i.headers||i.headers instanceof u?l.headers.forEach(function(e,t){c.setRequestHeader(t,e)}):Object.getOwnPropertyNames(i.headers).forEach(function(e){c.setRequestHeader(e,a(i.headers[e]))}),l.signal&&(l.signal.addEventListener("abort",d),c.onreadystatechange=function(){4===c.readyState&&l.signal.removeEventListener("abort",d)}),c.send(void 0===l._bodyInit?null:l._bodyInit)})}w.polyfill=!0,t.fetch||(t.fetch=w,t.Headers=u,t.Request=m,t.Response=b),e.Headers=u,e.Request=m,e.Response=b,e.fetch=w})({}),i.fetch.ponyfill=!0,delete i.fetch.polyfill;var s=r.fetch?r:i;(t=s.fetch).default=s.fetch,t.fetch=s.fetch,t.Headers=s.Headers,t.Request=s.Request,t.Response=s.Response,e.exports=t},99376:function(e,t,n){"use strict";var r=n(35475);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useSelectedLayoutSegment")&&n.d(t,{useSelectedLayoutSegment:function(){return r.useSelectedLayoutSegment}}),n.o(r,"useSelectedLayoutSegments")&&n.d(t,{useSelectedLayoutSegments:function(){return r.useSelectedLayoutSegments}})},48667:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return i.a}});var r=n(88003),i=n.n(r),s={};for(var o in r)"default"!==o&&(s[o]=(function(e){return r[e]}).bind(0,o));n.d(t,s)},12119:function(e,t,n){"use strict";Object.defineProperty(t,"$",{enumerable:!0,get:function(){return i}});let r=n(83079);function i(e){let{createServerReference:t}=n(6671);return t(e,r.callServer)}},8221:function(e,t){"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DOMAttributeNames:function(){return r},default:function(){return o},isEqualNode:function(){return s}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function i(e){let{type:t,props:n}=e,i=document.createElement(t);for(let e in n){if(!n.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===n[e])continue;let s=r[e]||e.toLowerCase();"script"===t&&("async"===s||"defer"===s||"noModule"===s)?i[s]=!!n[e]:i.setAttribute(s,n[e])}let{children:s,dangerouslySetInnerHTML:o}=n;return o?i.innerHTML=o.__html||"":s&&(i.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):""),i}function s(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let n=t.getAttribute("nonce");if(n&&!e.getAttribute("nonce")){let r=t.cloneNode(!0);return r.setAttribute("nonce",""),r.nonce=n,n===e.nonce&&e.isEqualNode(r)}}return e.isEqualNode(t)}function o(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let n=t[e.type]||[];n.push(e),t[e.type]=n});let r=t.title?t.title[0]:null,i="";if(r){let{children:e}=r.props;i="string"==typeof e?e:Array.isArray(e)?e.join(""):""}i!==document.title&&(document.title=i),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let n=document.getElementsByTagName("head")[0],r=n.querySelector("meta[name=next-head-count]"),o=Number(r.content),a=[];for(let t=0,n=r.previousElementSibling;t<o;t++,n=(null==n?void 0:n.previousElementSibling)||null){var l;(null==n?void 0:null==(l=n.tagName)?void 0:l.toLowerCase())===e&&a.push(n)}let u=t.map(i).filter(e=>{for(let t=0,n=a.length;t<n;t++)if(s(a[t],e))return a.splice(t,1),!1;return!0});a.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),u.forEach(e=>n.insertBefore(e,r)),r.content=(o-a.length+u.length).toString()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63515:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88003:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return y}});let r=n(47043),i=n(53099),s=n(57437),o=r._(n(54887)),a=i._(n(2265)),l=n(48701),u=n(8221),c=n(63515),d=new Map,h=new Set,p=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],f=e=>{if(o.default.preinit){e.forEach(e=>{o.default.preinit(e,{as:"style"})});return}if("undefined"!=typeof window){let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},g=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:i=null,dangerouslySetInnerHTML:s,children:o="",strategy:a="afterInteractive",onError:l,stylesheets:c}=e,g=n||t;if(g&&h.has(g))return;if(d.has(t)){h.add(g),d.get(t).then(r,l);return}let m=()=>{i&&i(),h.add(g)},y=document.createElement("script"),b=new Promise((e,t)=>{y.addEventListener("load",function(t){e(),r&&r.call(this,t),m()}),y.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});for(let[n,r]of(s?(y.innerHTML=s.__html||"",m()):o?(y.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):"",m()):t&&(y.src=t,d.set(t,b)),Object.entries(e))){if(void 0===r||p.includes(n))continue;let e=u.DOMAttributeNames[n]||n.toLowerCase();y.setAttribute(e,r)}"worker"===a&&y.setAttribute("type","text/partytown"),y.setAttribute("data-nscript",a),c&&f(c),document.body.appendChild(y)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>g(e))}):g(e)}function y(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");h.add(t)})}function b(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:i=null,strategy:u="afterInteractive",onError:d,stylesheets:p,...f}=e,{updateScripts:m,scripts:y,getIsSsr:b,appDir:v,nonce:w}=(0,a.useContext)(l.HeadManagerContext),O=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=t||n;O.current||(i&&e&&h.has(e)&&i(),O.current=!0)},[i,t,n]);let S=(0,a.useRef)(!1);if((0,a.useEffect)(()=>{!S.current&&("afterInteractive"===u?g(e):"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>g(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>g(e))})),S.current=!0)},[e,u]),("beforeInteractive"===u||"worker"===u)&&(m?(y[u]=(y[u]||[]).concat([{id:t,src:n,onLoad:r,onReady:i,onError:d,...f}]),m(y)):b&&b()?h.add(t||n):b&&!b()&&g(e)),v){if(p&&p.forEach(e=>{o.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)return n?(o.default.preload(n,f.integrity?{as:"script",integrity:f.integrity,nonce:w,crossOrigin:f.crossOrigin}:{as:"script",nonce:w,crossOrigin:f.crossOrigin}),(0,s.jsx)("script",{nonce:w,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...f,id:t}])+")"}})):(f.dangerouslySetInnerHTML&&(f.children=f.dangerouslySetInnerHTML.__html,delete f.dangerouslySetInnerHTML),(0,s.jsx)("script",{nonce:w,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...f,id:t}])+")"}}));"afterInteractive"===u&&n&&o.default.preload(n,f.integrity?{as:"script",integrity:f.integrity,nonce:w,crossOrigin:f.crossOrigin}:{as:"script",nonce:w,crossOrigin:f.crossOrigin})}return null}Object.defineProperty(b,"__nextScript",{value:!0});let v=b;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4855:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.YouTubeEmbed=t.GoogleMapsEmbed=t.GoogleAnalytics=void 0;var r=n(36014);Object.defineProperty(t,"GoogleAnalytics",{enumerable:!0,get:function(){return r.GoogleAnalytics}});var i=n(16630);Object.defineProperty(t,"GoogleMapsEmbed",{enumerable:!0,get:function(){return i.GoogleMapsEmbed}});var s=n(98712);Object.defineProperty(t,"YouTubeEmbed",{enumerable:!0,get:function(){return s.YouTubeEmbed}})},36014:function(e,t,n){"use strict";var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleAnalytics=void 0;let s=i(n(46893)),o=n(11076);t.GoogleAnalytics=e=>{var t=r(e,[]);return(0,o.formatData)(s.default,t)}},16630:function(e,t,n){"use strict";var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleMapsEmbed=void 0;let s=i(n(46362)),o=n(11076);t.GoogleMapsEmbed=e=>{var t=r(e,[]);return(0,o.formatData)(s.default,t)}},98712:function(e,t,n){"use strict";var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.YouTubeEmbed=void 0;let s=i(n(95825)),o=n(11076);t.YouTubeEmbed=e=>{var t=r(e,[]);return(0,o.formatData)(s.default,t)}},11076:function(e,t){"use strict";function n(e,t,n=!1){return t?Object.keys(e).filter(e=>n?!t.includes(e):t.includes(e)).reduce((t,n)=>(t[n]=e[n],t),{}):{}}function r(e,t,n,r){let i=r&&Object.keys(r).length>0?new URL(Object.values(r)[0],e):new URL(e);return t&&n&&t.forEach(e=>{n[e]&&i.searchParams.set(e,n[e])}),i.toString()}function i(e,t,n,i,s){var o;if(!t)return`<${e}></${e}>`;let a=(null===(o=t.src)||void 0===o?void 0:o.url)?Object.assign(Object.assign({},t),{src:r(t.src.url,t.src.params,i,s)}):t,l=Object.keys(Object.assign(Object.assign({},a),n)).reduce((e,t)=>{let r=null==n?void 0:n[t],i=a[t],s=null!=r?r:i,o=!0===s?t:`${t}="${s}"`;return s?e+` ${o}`:e},"");return`<${e}${l}></${e}>`}Object.defineProperty(t,"__esModule",{value:!0}),t.formatData=t.createHtml=t.formatUrl=void 0,t.formatUrl=r,t.createHtml=i,t.formatData=function(e,t){var s,o,a,l,u;let c=n(t,null===(s=e.scripts)||void 0===s?void 0:s.reduce((e,t)=>[...e,...Array.isArray(t.params)?t.params:[]],[])),d=n(t,null===(a=null===(o=e.html)||void 0===o?void 0:o.attributes.src)||void 0===a?void 0:a.params),h=n(t,[null===(u=null===(l=e.html)||void 0===l?void 0:l.attributes.src)||void 0===u?void 0:u.slugParam]),p=n(t,[...Object.keys(c),...Object.keys(d),...Object.keys(h)],!0);return Object.assign(Object.assign({},e),{html:e.html?i(e.html.element,e.html.attributes,p,d,h):null,scripts:e.scripts?e.scripts.map(e=>Object.assign(Object.assign({},e),{url:r(e.url,e.params,c)})):null})}},22715:function(e){e.exports={style:{fontFamily:"'__Poppins_4afbf9', '__Poppins_Fallback_4afbf9'"},className:"__className_4afbf9",variable:"__variable_4afbf9"}},69196:function(e,t,n){var r="function"==typeof fetch?fetch:void 0;if(void 0!==n.g&&n.g.fetch?r=n.g.fetch:"undefined"!=typeof window&&window.fetch&&(r=window.fetch),"undefined"==typeof window){var i=r||n(80751);i.default&&(i=i.default),t.default=i,e.exports=t.default}},60022:function(e,t,n){"use strict";n.d(t,{w:function(){return _}});var r=n(99376),i=n(2265);function s(e,t,n){return Math.max(t,Math.min(e,n))}function o(e,t){return"rtl"===t?(1-e)*100:(-1+e)*100}function a(e,t,n){if("string"==typeof t)void 0!==n&&(e.style[t]=n);else for(let n in t)if(t.hasOwnProperty(n)){let r=t[n];void 0!==r&&(e.style[n]=r)}}function l(e,t){e.classList.add(t)}function u(e,t){e.classList.remove(t)}function c(e){e&&e.parentNode&&e.parentNode.removeChild(e)}var d={minimum:.08,maximum:1,template:`<div class="bar"><div class="peg"></div></div>
             <div class="spinner"><div class="spinner-icon"></div></div>
             <div class="indeterminate"><div class="inc"></div><div class="dec"></div></div>`,easing:"linear",positionUsing:"",speed:200,trickle:!0,trickleSpeed:200,showSpinner:!0,indeterminate:!1,indeterminateSelector:".indeterminate",barSelector:".bar",spinnerSelector:".spinner",parent:"body",direction:"ltr"},h=class{static settings=d;static status=null;static pending=[];static isPaused=!1;static reset(){return this.status=null,this.isPaused=!1,this.pending=[],this.settings=d,this}static configure(e){return Object.assign(this.settings,e),this}static isStarted(){return"number"==typeof this.status}static set(e){if(this.isPaused)return this;let t=this.isStarted();e=s(e,this.settings.minimum,this.settings.maximum),this.status=e===this.settings.maximum?null:e;let n=this.render(!t),r=this.settings.speed,i=this.settings.easing;return n.forEach(e=>e.offsetWidth),this.queue(t=>{n.forEach(t=>{this.settings.indeterminate||a(t.querySelector(this.settings.barSelector),this.barPositionCSS({n:e,speed:r,ease:i}))}),e===this.settings.maximum?(n.forEach(e=>{a(e,{transition:"none",opacity:"1"}),e.offsetWidth}),setTimeout(()=>{n.forEach(e=>{a(e,{transition:`all ${r}ms ${i}`,opacity:"0"})}),setTimeout(()=>{n.forEach(e=>{this.remove(e),null===this.settings.template&&a(e,{transition:"none",opacity:"1"})}),t()},r)},r)):setTimeout(t,r)}),this}static start(){this.status||this.set(0);let e=()=>{this.isPaused||setTimeout(()=>{this.status&&(this.trickle(),e())},this.settings.trickleSpeed)};return this.settings.trickle&&e(),this}static done(e){return e||this.status?this.inc(.3+.5*Math.random()).set(1):this}static inc(e){if(this.isPaused||this.settings.indeterminate)return this;let t=this.status;return t?t>1?this:("number"!=typeof e&&(e=t>=0&&t<.2?.1:t>=.2&&t<.5?.04:t>=.5&&t<.8?.02:t>=.8&&t<.99?.005:0),t=s(t+e,0,.994),this.set(t)):this.start()}static dec(e){if(this.isPaused||this.settings.indeterminate)return this;let t=this.status;return"number"!=typeof t?this:("number"!=typeof e&&(e=t>.8?.1:t>.5?.05:t>.2?.02:.01),t=s(t-e,0,.994),this.set(t))}static trickle(){return this.isPaused||this.settings.indeterminate?this:this.inc()}static promise(e){if(!e||"resolved"===e.state())return this;let t=0,n=0;return this.start(),t++,n++,e.always(()=>{0==--n?(t=0,this.done()):this.set((t-n)/t)}),this}static render(e=!1){let t="string"==typeof this.settings.parent?document.querySelector(this.settings.parent):this.settings.parent,n=t?Array.from(t.querySelectorAll(".bprogress")):[];if(null!==this.settings.template&&0===n.length){l(document.documentElement,"bprogress-busy");let e=document.createElement("div");l(e,"bprogress"),e.innerHTML=this.settings.template,t!==document.body&&l(t,"bprogress-custom-parent"),t.appendChild(e),n.push(e)}return n.forEach(n=>{if(null===this.settings.template&&(n.style.display=""),l(document.documentElement,"bprogress-busy"),t!==document.body&&l(t,"bprogress-custom-parent"),this.settings.indeterminate){let e=n.querySelector(this.settings.barSelector);e&&(e.style.display="none");let t=n.querySelector(this.settings.indeterminateSelector);t&&(t.style.display="")}else{let t=n.querySelector(this.settings.barSelector),r=e?o(0,this.settings.direction):o(this.status||0,this.settings.direction);a(t,this.barPositionCSS({n:this.status||0,speed:this.settings.speed,ease:this.settings.easing,perc:r}));let i=n.querySelector(this.settings.indeterminateSelector);i&&(i.style.display="none")}if(null===this.settings.template){let e=n.querySelector(this.settings.spinnerSelector);e&&(e.style.display=this.settings.showSpinner?"block":"none")}else if(!this.settings.showSpinner){let e=n.querySelector(this.settings.spinnerSelector);e&&c(e)}}),n}static remove(e){e?null===this.settings.template?e.style.display="none":c(e):(u(document.documentElement,"bprogress-busy"),("string"==typeof this.settings.parent?document.querySelectorAll(this.settings.parent):[this.settings.parent]).forEach(e=>{u(e,"bprogress-custom-parent")}),document.querySelectorAll(".bprogress").forEach(e=>{null===this.settings.template?e.style.display="none":c(e)}))}static pause(){return!this.isStarted()||this.settings.indeterminate||(this.isPaused=!0),this}static resume(){if(!this.isStarted()||this.settings.indeterminate)return this;if(this.isPaused=!1,this.settings.trickle){let e=()=>{this.isPaused||setTimeout(()=>{this.status&&(this.trickle(),e())},this.settings.trickleSpeed)};e()}return this}static isRendered(){return document.querySelectorAll(".bprogress").length>0}static getPositioningCSS(){let e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return`${t}Perspective` in e?"translate3d":`${t}Transform` in e?"translate":"margin"}static queue(e){this.pending.push(e),1===this.pending.length&&this.next()}static next(){let e=this.pending.shift();e&&e(this.next.bind(this))}static initPositionUsing(){""===this.settings.positionUsing&&(this.settings.positionUsing=this.getPositioningCSS())}static barPositionCSS({n:e,speed:t,ease:n,perc:r}){this.initPositionUsing();let i={},s=r??o(e,this.settings.direction);return"translate3d"===this.settings.positionUsing?i={transform:`translate3d(${s}%,0,0)`}:"translate"===this.settings.positionUsing?i={transform:`translate(${s}%,0)`}:"width"===this.settings.positionUsing?i={width:`${"rtl"===this.settings.direction?100-s:s+100}%`,..."rtl"===this.settings.direction?{right:"0",left:"auto"}:{}}:"margin"===this.settings.positionUsing&&(i="rtl"===this.settings.direction?{"margin-left":`${-s}%`}:{"margin-right":`${-s}%`}),i.transition=`all ${t}ms ${n}`,i}},p=({color:e="#29d",height:t="2px",spinnerPosition:n="top-right"})=>`
:root {
  --bprogress-color: ${e};
  --bprogress-height: ${t};
  --bprogress-spinner-size: 18px;
  --bprogress-spinner-animation-duration: 400ms;
  --bprogress-spinner-border-size: 2px;
  --bprogress-box-shadow: 0 0 10px ${e}, 0 0 5px ${e};
  --bprogress-z-index: 99999;
  --bprogress-spinner-top: ${"top-right"===n||"top-left"===n?"15px":"auto"};
  --bprogress-spinner-bottom: ${"bottom-right"===n||"bottom-left"===n?"15px":"auto"};
  --bprogress-spinner-right: ${"top-right"===n||"bottom-right"===n?"15px":"auto"};
  --bprogress-spinner-left: ${"top-left"===n||"bottom-left"===n?"15px":"auto"};
}

.bprogress {
  width: 0;
  height: 0;
  pointer-events: none;
  z-index: var(--bprogress-z-index);
}

.bprogress .bar {
  background: var(--bprogress-color);
  position: fixed;
  z-index: var(--bprogress-z-index);
  top: 0;
  left: 0;
  width: 100%;
  height: var(--bprogress-height);
}

/* Fancy blur effect */
.bprogress .peg {
  display: block;
  position: absolute;
  right: 0;
  width: 100px;
  height: 100%;
  box-shadow: var(--bprogress-box-shadow);
  opacity: 1.0;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
.bprogress .spinner {
  display: block;
  position: fixed;
  z-index: var(--bprogress-z-index);
  top: var(--bprogress-spinner-top);
  bottom: var(--bprogress-spinner-bottom);
  right: var(--bprogress-spinner-right);
  left: var(--bprogress-spinner-left);
}

.bprogress .spinner-icon {
  width: var(--bprogress-spinner-size);
  height: var(--bprogress-spinner-size);
  box-sizing: border-box;
  border: solid var(--bprogress-spinner-border-size) transparent;
  border-top-color: var(--bprogress-color);
  border-left-color: var(--bprogress-color);
  border-radius: 50%;
  -webkit-animation: bprogress-spinner var(--bprogress-spinner-animation-duration) linear infinite;
  animation: bprogress-spinner var(--bprogress-spinner-animation-duration) linear infinite;
}

.bprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.bprogress-custom-parent .bprogress .spinner,
.bprogress-custom-parent .bprogress .bar {
  position: absolute;
}

.bprogress .indeterminate {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--bprogress-height);
  overflow: hidden;
}

.bprogress .indeterminate .inc,
.bprogress .indeterminate .dec {
  position: absolute;
  top: 0;
  height: 100%;
  background-color: var(--bprogress-color);
}

.bprogress .indeterminate .inc {
  animation: bprogress-indeterminate-increase 2s infinite;
}

.bprogress .indeterminate .dec {
  animation: bprogress-indeterminate-decrease 2s 0.5s infinite;
}

@-webkit-keyframes bprogress-spinner {
  0%   { -webkit-transform: rotate(0deg); transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }
}

@keyframes bprogress-spinner {
  0%   { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bprogress-indeterminate-increase {
  from { left: -5%; width: 5%; }
  to { left: 130%; width: 100%; }
}

@keyframes bprogress-indeterminate-decrease {
  from { left: -80%; width: 80%; }
  to { left: 110%; width: 10%; }
}
`;function f(e,t){if("string"==typeof t&&"data-disable-progress"===t){let n=t.substring(5).replace(/-([a-z])/g,(e,t)=>t.toUpperCase());return e.dataset[n]}let n=e[t];if(n instanceof SVGAnimatedString){let e=n.baseVal;return"href"===t?function(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:r,hash:i}=function(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}(e);return`${t}${n}${r}${i}`}(e,location.origin):e}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){var r;r=n[t],t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r})}return e}function m(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var y=(0,i.createContext)(void 0),b=function(){var e=(0,i.useContext)(y);if(!e)throw Error("useProgress must be used within a ProgressProvider");return e},v=function(e){var t=e.children,n=e.color,r=void 0===n?"#0A2FFF":n,s=e.height,o=void 0===s?"2px":s,a=e.options,l=e.spinnerPosition,u=void 0===l?"top-right":l,c=e.style,d=e.disableStyle,f=e.nonce,m=e.shallowRouting,b=e.disableSameURL,v=e.startPosition,w=e.delay,O=e.stopDelay,S=(0,i.useRef)(null),x=(0,i.useRef)(!1),k=(0,i.useCallback)(function(){return x.current=!0},[]),P=(0,i.useCallback)(function(){return x.current=!1},[]),E=(0,i.useCallback)(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n&&k(),S.current=setTimeout(function(){e>0&&h.set(e),h.start()},t)},[k]),j=(0,i.useCallback)(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;setTimeout(function(){S.current&&clearTimeout(S.current),S.current=setTimeout(function(){h.isStarted()&&(h.done(),x.current&&P())},e)},t)},[P]),L=(0,i.useCallback)(function(e){return h.inc(e)},[]),C=(0,i.useCallback)(function(e){return h.dec(e)},[]),_=(0,i.useCallback)(function(e){return h.set(e)},[]),T=(0,i.useCallback)(function(){return h.pause()},[]),A=(0,i.useCallback)(function(){return h.resume()},[]),R=(0,i.useCallback)(function(){return h.settings},[]),N=(0,i.useCallback)(function(e){var t=R(),n="function"==typeof e?e(t):e,r=g({},t,n);h.configure(r)},[R]),I=(0,i.useMemo)(function(){return i.createElement("style",{nonce:f},c||p({color:r,height:o,spinnerPosition:u}))},[r,o,f,u,c]);return h.configure(a||{}),i.createElement(y.Provider,{value:{start:E,stop:j,inc:L,dec:C,set:_,pause:T,resume:A,setOptions:N,getOptions:R,isAutoStopDisabled:x,disableAutoStop:k,enableAutoStop:P,shallowRouting:void 0!==m&&m,disableSameURL:void 0===b||b,startPosition:void 0===v?0:v,delay:void 0===w?0:w,stopDelay:void 0===O?0:O}},void 0!==d&&d?null:I,t)};function w(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")}var O=i.forwardRef(function(e,t){var n=e.as,r=e.children,s=e.className,o=e.classSelector,a=m(e,["as","children","className","classSelector"]);return i.createElement(null!=n?n:"div",g({ref:t,className:w(void 0===o?"bar":o,s)},a),r)}),S=i.forwardRef(function(e,t){var n=e.as,r=e.children,s=e.className,o=e.classSelector,a=m(e,["as","children","className","classSelector"]);return i.createElement(null!=n?n:"div",g({ref:t,className:w(void 0===o?"peg":o,s)},a),r)}),x=i.forwardRef(function(e,t){var n=e.as,r=e.children,s=e.className,o=e.classSelector,a=m(e,["as","children","className","classSelector"]);return i.createElement(null!=n?n:"div",g({ref:t,className:w(void 0===o?"spinner":o,s)},a),r)}),k=i.forwardRef(function(e,t){var n=e.as,r=e.children,s=e.className,o=e.classSelector,a=m(e,["as","children","className","classSelector"]);return i.createElement(null!=n?n:"div",g({ref:t,className:w(void 0===o?"spinner-icon":o,s)},a),r)});function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){var r;r=n[t],t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r})}return e}function E(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}i.forwardRef(function(e,t){var n,r,s=e.as,o=e.children,a=e.className,l=e.style,u=m(e,["as","children","className","style"]);return i.createElement(null!=s?s:"div",g({ref:t,className:w("bprogress",a),style:(n=g({},l),r=r={display:"none"},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):(function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n})(Object(r)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(r,e))}),n)},u),o||i.createElement(i.Fragment,null,i.createElement(O,null,i.createElement(S,null)),i.createElement(x,null,i.createElement(k,null))))}),i.forwardRef(function(e,t){var n=e.as,r=e.className,s=e.classSelector,o=m(e,["as","className","classSelector"]);return i.createElement(null!=n?n:"div",g({ref:t,className:w(void 0===s?"indeterminate":s,r)},o),i.createElement("div",{className:"inc"}),i.createElement("div",{className:"dec"}))});var j=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["memo","shouldCompareComplexProps"];return(0,i.memo)(e,function(e,n){return!1!==n.memo&&(!n.shouldCompareComplexProps||function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=Object.keys(e).filter(function(e){return!n.includes(e)}),i=Object.keys(t).filter(function(e){return!n.includes(e)});if(r.length!==i.length)return!1;var s=!0,o=!1,a=void 0;try{for(var l,u=r[Symbol.iterator]();!(s=(l=u.next()).done);s=!0){var c=l.value;if(e[c]!==t[c])return!1}}catch(e){o=!0,a=e}finally{try{s||null==u.return||u.return()}finally{if(o)throw a}}return!0}(e,n,t))})}(function(e){return!function(e){var t=e.shallowRouting,n=void 0!==t&&t,r=e.disableSameURL,s=void 0===r||r,o=e.startPosition,a=void 0===o?0:o,l=e.delay,u=void 0===l?0:l,c=e.stopDelay,d=void 0===c?0:c,h=e.targetPreprocessor,p=e.disableAnchorClick,g=void 0!==p&&p,m=e.startOnLoad,y=void 0!==m&&m,v=e.forcedStopDelay,w=void 0===v?0:v,O=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],S=(0,i.useRef)([]),x=(0,i.useRef)(null),k=b(),P=k.start,E=k.stop,j=k.isAutoStopDisabled;(0,i.useEffect)(function(){y&&P(a,u)},[]),(0,i.useEffect)(function(){return x.current&&clearTimeout(x.current),x.current=setTimeout(function(){j.current||E()},d),function(){x.current&&clearTimeout(x.current)}},O),(0,i.useEffect)(function(){if(!g){var e=function(e){if(!e.defaultPrevented){var t=e.currentTarget;if(!t.hasAttribute("download")){var r=e.target,i=(null==r?void 0:r.getAttribute("data-prevent-progress"))==="true"||(null==t?void 0:t.getAttribute("data-prevent-progress"))==="true";if(!i)for(var o,l=r;l&&"a"!==l.tagName.toLowerCase();){if((null===(o=l.parentElement)||void 0===o?void 0:o.getAttribute("data-prevent-progress"))==="true"){i=!0;break}l=l.parentElement}if(!i&&"_blank"!==f(t,"target")&&!e.metaKey&&!e.ctrlKey&&!e.shiftKey&&!e.altKey){var c=f(t,"href"),d=h?h(new URL(c)):new URL(c),p=new URL(location.href);(!(n&&d.protocol+"//"+d.host+d.pathname==p.protocol+"//"+p.host+p.pathname)||!s)&&(d.protocol+"//"+d.host+d.pathname+d.search==p.protocol+"//"+p.host+p.pathname+p.search&&s||P(a,u))}}}},t=new MutationObserver(function(){var t=Array.from(document.querySelectorAll("a")).filter(function(e){var t=f(e,"href"),n="true"===e.getAttribute("data-disable-progress"),r=t&&!t.startsWith("tel:")&&!t.startsWith("mailto:")&&!t.startsWith("blob:")&&!t.startsWith("javascript:");return!n&&r&&"_blank"!==f(e,"target")});t.forEach(function(t){t.addEventListener("click",e,!0)}),S.current=t});t.observe(document,{childList:!0,subtree:!0});var r=window.history.pushState;return window.history.pushState=new Proxy(window.history.pushState,{apply:function(e,t,n){return j.current||E(d,w),e.apply(t,n)}}),function(){t.disconnect(),S.current.forEach(function(t){t.removeEventListener("click",e,!0)}),S.current=[],window.history.pushState=r}}},[g,h,n,s,u,d,a,P,E,w,j])}(e,[(0,r.usePathname)(),(0,r.useSearchParams)()]),null});j.displayName="AppProgress";var L=function(e){var t=e.children,n=e.ProgressComponent,r=e.color,s=e.height,o=e.options,a=e.spinnerPosition,l=e.style,u=e.disableStyle,c=e.nonce,d=e.stopDelay,h=e.delay,p=e.startPosition,f=e.disableSameURL,g=e.shallowRouting,m=E(e,["children","ProgressComponent","color","height","options","spinnerPosition","style","disableStyle","nonce","stopDelay","delay","startPosition","disableSameURL","shallowRouting"]);return i.createElement(v,{color:r,height:s,options:o,spinnerPosition:a,style:l,disableStyle:u,nonce:c,stopDelay:d,delay:h,startPosition:p,disableSameURL:f,shallowRouting:g},i.createElement(n,P({stopDelay:d,delay:h,startPosition:p,disableSameURL:f,shallowRouting:g},m)),t)},C=function(e){return i.createElement(i.Suspense,null,i.createElement(j,g({},e)))},_=function(e){var t=e.children,n=E(e,["children"]);return i.createElement(L,P({ProgressComponent:C},n),t)}},53690:function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(){return"function"==typeof XMLHttpRequest||("undefined"==typeof XMLHttpRequest?"undefined":r(XMLHttpRequest))==="object"}n.d(t,{Z:function(){return j}});var s,o,a,l,u=n(69196),c=n.t(u,2);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach(function(t){var r,i;r=t,i=n[t],(r=function(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=p(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:t+""}(r))in e?Object.defineProperty(e,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[r]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var f="function"==typeof fetch?fetch:void 0;"undefined"!=typeof global&&global.fetch?f=global.fetch:"undefined"!=typeof window&&window.fetch&&(f=window.fetch),i()&&("undefined"!=typeof global&&global.XMLHttpRequest?a=global.XMLHttpRequest:"undefined"!=typeof window&&window.XMLHttpRequest&&(a=window.XMLHttpRequest)),"function"==typeof ActiveXObject&&("undefined"!=typeof global&&global.ActiveXObject?l=global.ActiveXObject:"undefined"!=typeof window&&window.ActiveXObject&&(l=window.ActiveXObject)),f||!c||a||l||(f=u||c),"function"!=typeof f&&(f=void 0);var g=function(e,t){if(t&&"object"===p(t)){var n="";for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r]);if(!n)return e;e=e+(-1!==e.indexOf("?")?"&":"?")+n.slice(1)}return e},m=function(e,t,n,r){var i=function(e){if(!e.ok)return n(e.statusText||"Error",{status:e.status});e.text().then(function(t){n(null,{status:e.status,data:t})}).catch(n)};if(r){var s=r(e,t);if(s instanceof Promise){s.then(i).catch(n);return}}"function"==typeof fetch?fetch(e,t).then(i).catch(n):f(e,t).then(i).catch(n)},y=!1,b=function(e,t,n,r){e.queryStringParams&&(t=g(t,e.queryStringParams));var i=h({},"function"==typeof e.customHeaders?e.customHeaders():e.customHeaders);"undefined"==typeof window&&"undefined"!=typeof global&&void 0!==global.process&&global.process.versions&&global.process.versions.node&&(i["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),n&&(i["Content-Type"]="application/json");var s="function"==typeof e.requestOptions?e.requestOptions(n):e.requestOptions,o=h({method:n?"POST":"GET",body:n?e.stringify(n):void 0,headers:i},y?{}:s),a="function"==typeof e.alternateFetch&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{m(t,o,r,a)}catch(e){if(!s||0===Object.keys(s).length||!e.message||0>e.message.indexOf("not implemented"))return r(e);try{Object.keys(s).forEach(function(e){delete o[e]}),m(t,o,r,a),y=!0}catch(e){r(e)}}},v=function(e,t,n,r){n&&"object"===p(n)&&(n=g("",n).slice(1)),e.queryStringParams&&(t=g(t,e.queryStringParams));try{(i=a?new a:new l("MSXML2.XMLHTTP.3.0")).open(n?"POST":"GET",t,1),e.crossDomain||i.setRequestHeader("X-Requested-With","XMLHttpRequest"),i.withCredentials=!!e.withCredentials,n&&i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.overrideMimeType&&i.overrideMimeType("application/json");var i,s=e.customHeaders;if(s="function"==typeof s?s():s)for(var o in s)i.setRequestHeader(o,s[o]);i.onreadystatechange=function(){i.readyState>3&&r(i.status>=400?i.statusText:null,{status:i.status,data:i.responseText})},i.send(n)}catch(e){console&&console.log(e)}},w=function(e,t,n,r){return("function"==typeof n&&(r=n,n=void 0),r=r||function(){},f&&0!==t.indexOf("file:"))?b(e,t,n,r):i()||"function"==typeof ActiveXObject?v(e,t,n,r):void r(Error("No fetch and no xhr implementation found!"))};function O(e){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach(function(t){k(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function k(e,t,n){return(t=P(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e){var t=function(e,t){if("object"!=O(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=O(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==O(t)?t:t+""}var E=(s=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,e),this.services=t,this.options=n,this.allOptions=r,this.type="backend",this.init(t,n,r)},o=[{key:"init",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.services=e,this.options=x(x(x({},{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(e){return JSON.parse(e)},stringify:JSON.stringify,parsePayload:function(e,t,n){return k({},t,n||"")},parseLoadPayload:function(e,t){},request:w,reloadInterval:"undefined"==typeof window&&36e5,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}),this.options||{}),n),this.allOptions=r,this.services&&this.options.reloadInterval){var i=setInterval(function(){return t.reload()},this.options.reloadInterval);"object"===O(i)&&"function"==typeof i.unref&&i.unref()}}},{key:"readMulti",value:function(e,t,n){this._readAny(e,e,t,t,n)}},{key:"read",value:function(e,t,n){this._readAny([e],e,[t],t,n)}},{key:"_readAny",value:function(e,t,n,r,i){var s,o,a=this,l=this.options.loadPath;"function"==typeof this.options.loadPath&&(l=this.options.loadPath(e,n)),(l=(o=s=l)&&"function"==typeof o.then?s:Promise.resolve(s)).then(function(s){if(!s)return i(null,{});var o=a.services.interpolator.interpolate(s,{lng:e.join("+"),ns:n.join("+")});a.loadUrl(o,i,t,r)})}},{key:"loadUrl",value:function(e,t,n,r){var i=this,s=this.options.parseLoadPayload("string"==typeof n?[n]:n,"string"==typeof r?[r]:r);this.options.request(this.options,e,s,function(s,o){if(o&&(o.status>=500&&o.status<600||!o.status))return t("failed loading "+e+"; status code: "+o.status,!0);if(o&&o.status>=400&&o.status<500)return t("failed loading "+e+"; status code: "+o.status,!1);if(!o&&s&&s.message){var a,l,u=s.message.toLowerCase();if(["failed","fetch","network","load"].find(function(e){return u.indexOf(e)>-1}))return t("failed loading "+e+": "+s.message,!0)}if(s)return t(s,!1);try{a="string"==typeof o.data?i.options.parse(o.data,n,r):o.data}catch(t){l="failed parsing "+e+" to json"}if(l)return t(l,!1);t(null,a)})}},{key:"create",value:function(e,t,n,r,i){var s=this;if(this.options.addPath){"string"==typeof e&&(e=[e]);var o=this.options.parsePayload(t,n,r),a=0,l=[],u=[];e.forEach(function(n){var r=s.options.addPath;"function"==typeof s.options.addPath&&(r=s.options.addPath(n,t));var c=s.services.interpolator.interpolate(r,{lng:n,ns:t});s.options.request(s.options,c,o,function(t,n){a+=1,l.push(t),u.push(n),a===e.length&&"function"==typeof i&&i(l,u)})})}}},{key:"reload",value:function(){var e=this,t=this.services,n=t.backendConnector,r=t.languageUtils,i=t.logger,s=n.language;if(!s||"cimode"!==s.toLowerCase()){var o=[],a=function(e){r.toResolveHierarchy(e).forEach(function(e){0>o.indexOf(e)&&o.push(e)})};a(s),this.allOptions.preload&&this.allOptions.preload.forEach(function(e){return a(e)}),o.forEach(function(t){e.allOptions.ns.forEach(function(e){n.read(t,e,"read",null,null,function(r,s){r&&i.warn("loading namespace ".concat(e," for language ").concat(t," failed"),r),!r&&s&&i.log("loaded namespace ".concat(e," for language ").concat(t),s),n.loaded("".concat(t,"|").concat(e),r,s)})})})}}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,P(r.key),r)}}(s.prototype,o),Object.defineProperty(s,"prototype",{writable:!1}),s);E.type="backend";var j=E},46550:function(e,t,n){"use strict";n.d(t,{ZP:function(){return et}});let r=e=>"string"==typeof e,i=()=>{let e,t;let n=new Promise((n,r)=>{e=n,t=r});return n.resolve=e,n.reject=t,n},s=e=>null==e?"":""+e,o=(e,t,n)=>{e.forEach(e=>{t[e]&&(n[e]=t[e])})},a=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(a,"."):e,u=e=>!e||r(e),c=(e,t,n)=>{let i=r(t)?t.split("."):t,s=0;for(;s<i.length-1;){if(u(e))return{};let t=l(i[s]);!e[t]&&n&&(e[t]=new n),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++s}return u(e)?{}:{obj:e,k:l(i[s])}},d=(e,t,n)=>{let{obj:r,k:i}=c(e,t,Object);if(void 0!==r||1===t.length){r[i]=n;return}let s=t[t.length-1],o=t.slice(0,t.length-1),a=c(e,o,Object);for(;void 0===a.obj&&o.length;)s=`${o[o.length-1]}.${s}`,(a=c(e,o=o.slice(0,o.length-1),Object))&&a.obj&&void 0!==a.obj[`${a.k}.${s}`]&&(a.obj=void 0);a.obj[`${a.k}.${s}`]=n},h=(e,t,n,r)=>{let{obj:i,k:s}=c(e,t,Object);i[s]=i[s]||[],i[s].push(n)},p=(e,t)=>{let{obj:n,k:r}=c(e,t);if(n)return n[r]},f=(e,t,n)=>{let r=p(e,n);return void 0!==r?r:p(t,n)},g=(e,t,n)=>{for(let i in t)"__proto__"!==i&&"constructor"!==i&&(i in e?r(e[i])||e[i]instanceof String||r(t[i])||t[i]instanceof String?n&&(e[i]=t[i]):g(e[i],t[i],n):e[i]=t[i]);return e},m=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var y={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let b=e=>r(e)?e.replace(/[&<>"'\/]/g,e=>y[e]):e;class v{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}let w=[" ",",","?","!",";"],O=new v(20),S=(e,t,n)=>{t=t||"",n=n||"";let r=w.filter(e=>0>t.indexOf(e)&&0>n.indexOf(e));if(0===r.length)return!0;let i=O.getRegExp(`(${r.map(e=>"?"===e?"\\?":e).join("|")})`),s=!i.test(e);if(!s){let t=e.indexOf(n);t>0&&!i.test(e.substring(0,t))&&(s=!0)}return s},x=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];let r=t.split(n),i=e;for(let e=0;e<r.length;){let t;if(!i||"object"!=typeof i)return;let s="";for(let o=e;o<r.length;++o)if(o!==e&&(s+=n),s+=r[o],void 0!==(t=i[s])){if(["string","number","boolean"].indexOf(typeof t)>-1&&o<r.length-1)continue;e+=o-e+1;break}i=t}return i},k=e=>e&&e.replace("_","-"),P={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class E{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||P,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,i){return i&&!this.debug?null:(r(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new E(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new E(this.logger,e)}}var j=new E;class L{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let n=this.observers[e].get(t)||0;this.observers[e].set(t,n+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(e=>{let[t,r]=e;for(let e=0;e<r;e++)t(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(t=>{let[r,i]=t;for(let t=0;t<i;t++)r.apply(r,[e,...n])})}}class C extends L{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let i,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=void 0!==s.keySeparator?s.keySeparator:this.options.keySeparator,a=void 0!==s.ignoreJSONStructure?s.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?i=e.split("."):(i=[e,t],n&&(Array.isArray(n)?i.push(...n):r(n)&&o?i.push(...n.split(o)):i.push(n)));let l=p(this.data,i);return(!l&&!t&&!n&&e.indexOf(".")>-1&&(e=i[0],t=i[1],n=i.slice(2).join(".")),!l&&a&&r(n))?x(this.data&&this.data[e]&&this.data[e][t],n,o):l}addResource(e,t,n,r){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},s=void 0!==i.keySeparator?i.keySeparator:this.options.keySeparator,o=[e,t];n&&(o=o.concat(s?n.split(s):n)),e.indexOf(".")>-1&&(o=e.split("."),r=t,t=o[1]),this.addNamespaces(t),d(this.data,o,r),i.silent||this.emit("added",e,t,n,r)}addResources(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let i in n)(r(n[i])||Array.isArray(n[i]))&&this.addResource(e,t,i,n[i],{silent:!0});i.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,r,i){let s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},o=[e,t];e.indexOf(".")>-1&&(o=e.split("."),r=n,n=t,t=o[1]),this.addNamespaces(t);let a=p(this.data,o)||{};s.skipCopy||(n=JSON.parse(JSON.stringify(n))),r?g(a,n,i):a={...a,...n},d(this.data,o,a),s.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return(t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI)?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var _={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,i){return e.forEach(e=>{this.processors[e]&&(t=this.processors[e].process(t,n,r,i))}),t}};let T={};class A extends L{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),o(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=j.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let n=this.resolve(e,t);return n&&void 0!==n.res}extractFromKey(e,t){let n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");let i=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,s=t.ns||this.options.defaultNS||[],o=n&&e.indexOf(n)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!S(e,n,i);if(o&&!a){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:r(s)?[s]:s};let o=e.split(n);(n!==i||n===i&&this.options.ns.indexOf(o[0])>-1)&&(s=o.shift()),e=o.join(i)}return{key:e,namespaces:r(s)?[s]:s}}translate(e,t,n){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let i=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,s=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:o,namespaces:a}=this.extractFromKey(e[e.length-1],t),l=a[a.length-1],u=t.lng||this.language,c=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&"cimode"===u.toLowerCase()){if(c){let e=t.nsSeparator||this.options.nsSeparator;return i?{res:`${l}${e}${o}`,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:`${l}${e}${o}`}return i?{res:o,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:o}let d=this.resolve(e,t),h=d&&d.res,p=d&&d.usedKey||o,f=d&&d.exactUsedKey||o,g=Object.prototype.toString.apply(h),m=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,b=!r(h)&&"boolean"!=typeof h&&"number"!=typeof h;if(y&&h&&b&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(g)&&!(r(m)&&Array.isArray(h))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(p,h,{...t,ns:a}):`key '${o} (${this.language})' returned an object instead of string.`;return i?(d.res=e,d.usedParams=this.getUsedParamsDetails(t),d):e}if(s){let e=Array.isArray(h),n=e?[]:{},r=e?f:p;for(let e in h)if(Object.prototype.hasOwnProperty.call(h,e)){let i=`${r}${s}${e}`;n[e]=this.translate(i,{...t,joinArrays:!1,ns:a}),n[e]===i&&(n[e]=h[e])}h=n}}else if(y&&r(m)&&Array.isArray(h))(h=h.join(m))&&(h=this.extendTranslation(h,e,t,n));else{let i=!1,a=!1,c=void 0!==t.count&&!r(t.count),p=A.hasDefaultValue(t),f=c?this.pluralResolver.getSuffix(u,t.count,t):"",g=t.ordinal&&c?this.pluralResolver.getSuffix(u,t.count,{ordinal:!1}):"",m=c&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),y=m&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${f}`]||t[`defaultValue${g}`]||t.defaultValue;!this.isValidLookup(h)&&p&&(i=!0,h=y),this.isValidLookup(h)||(a=!0,h=o);let b=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&a?void 0:h,v=p&&y!==h&&this.options.updateMissing;if(a||i||v){if(this.logger.log(v?"updateKey":"missingKey",u,l,o,v?y:h),s){let e=this.resolve(o,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],n=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&n&&n[0])for(let t=0;t<n.length;t++)e.push(n[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let r=(e,n,r)=>{let i=p&&r!==h?r:b;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,n,i,v,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,l,n,i,v,t),this.emit("missingKey",e,l,n,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&c?e.forEach(e=>{let n=this.pluralResolver.getSuffixes(e,t);m&&t[`defaultValue${this.options.pluralSeparator}zero`]&&0>n.indexOf(`${this.options.pluralSeparator}zero`)&&n.push(`${this.options.pluralSeparator}zero`),n.forEach(n=>{r([e],o+n,t[`defaultValue${n}`]||y)})}):r(e,o,y))}h=this.extendTranslation(h,e,t,d,n),a&&h===o&&this.options.appendNamespaceToMissingKey&&(h=`${l}:${o}`),(a||i)&&this.options.parseMissingKeyHandler&&(h="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${o}`:o,i?h:void 0):this.options.parseMissingKeyHandler(h))}return i?(d.res=h,d.usedParams=this.getUsedParamsDetails(t),d):h}extendTranslation(e,t,n,i,s){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!n.skipInterpolation){let a;n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});let l=r(e)&&(n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(l){let t=e.match(this.interpolator.nestingRegexp);a=t&&t.length}let u=n.replace&&!r(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(u={...this.options.interpolation.defaultVariables,...u}),e=this.interpolator.interpolate(e,u,n.lng||this.language||i.usedLng,n),l){let t=e.match(this.interpolator.nestingRegexp);a<(t&&t.length)&&(n.nest=!1)}!n.lng&&"v1"!==this.options.compatibilityAPI&&i&&i.res&&(n.lng=this.language||i.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return s&&s[0]===r[0]&&!n.context?(o.logger.warn(`It seems you are nesting recursively key: ${r[0]} in key: ${t[0]}`),null):o.translate(...r,t)},n)),n.interpolation&&this.interpolator.reset()}let a=n.postProcess||this.options.postProcess,l=r(a)?[a]:a;return null!=e&&l&&l.length&&!1!==n.applyPostProcessor&&(e=_.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e){let t,n,i,s,o,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return r(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let l=this.extractFromKey(e,a),u=l.key;n=u;let c=l.namespaces;this.options.fallbackNS&&(c=c.concat(this.options.fallbackNS));let d=void 0!==a.count&&!r(a.count),h=d&&!a.ordinal&&0===a.count&&this.pluralResolver.shouldUseIntlApi(),p=void 0!==a.context&&(r(a.context)||"number"==typeof a.context)&&""!==a.context,f=a.lngs?a.lngs:this.languageUtils.toResolveHierarchy(a.lng||this.language,a.fallbackLng);c.forEach(e=>{this.isValidLookup(t)||(o=e,!T[`${f[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(T[`${f[0]}-${e}`]=!0,this.logger.warn(`key "${n}" for languages "${f.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),f.forEach(n=>{let r;if(this.isValidLookup(t))return;s=n;let o=[u];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(o,u,n,e,a);else{let e;d&&(e=this.pluralResolver.getSuffix(n,a.count,a));let t=`${this.options.pluralSeparator}zero`,r=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(d&&(o.push(u+e),a.ordinal&&0===e.indexOf(r)&&o.push(u+e.replace(r,this.options.pluralSeparator)),h&&o.push(u+t)),p){let n=`${u}${this.options.contextSeparator}${a.context}`;o.push(n),d&&(o.push(n+e),a.ordinal&&0===e.indexOf(r)&&o.push(n+e.replace(r,this.options.pluralSeparator)),h&&o.push(n+t))}}for(;r=o.pop();)this.isValidLookup(t)||(i=r,t=this.getResource(n,e,r,a))}))})}),{res:t,usedKey:n,exactUsedKey:i,usedLng:s,usedNS:o}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!r(e.replace),n=t?e.replace:e;if(t&&void 0!==e.count&&(n.count=e.count),this.options.interpolation.defaultVariables&&(n={...this.options.interpolation.defaultVariables,...n}),!t)for(let e of(n={...n},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete n[e];return n}static hasDefaultValue(e){let t="defaultValue";for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,t.length)&&void 0!==e[n])return!0;return!1}}let R=e=>e.charAt(0).toUpperCase()+e.slice(1);class N{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=j.create("languageUtils")}getScriptPartFromCode(e){if(!(e=k(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=k(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(r(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(e){}let t=["hans","hant","latn","cyrl","cans","mong","arab"],n=e.split("-");return this.options.lowerCaseLng?n=n.map(e=>e.toLowerCase()):2===n.length?(n[0]=n[0].toLowerCase(),n[1]=n[1].toUpperCase(),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=R(n[1].toLowerCase()))):3===n.length&&(n[0]=n[0].toLowerCase(),2===n[1].length&&(n[1]=n[1].toUpperCase()),"sgn"!==n[0]&&2===n[2].length&&(n[2]=n[2].toUpperCase()),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=R(n[1].toLowerCase())),t.indexOf(n[2].toLowerCase())>-1&&(n[2]=R(n[2].toLowerCase()))),n.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let n=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(n))&&(t=n)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let n=this.getLanguagePartFromCode(e);if(this.isSupportedCode(n))return t=n;t=this.options.supportedLngs.find(e=>{if(e===n||!(0>e.indexOf("-")&&0>n.indexOf("-"))&&(e.indexOf("-")>0&&0>n.indexOf("-")&&e.substring(0,e.indexOf("-"))===n||0===e.indexOf(n)&&n.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),r(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){let n=this.getFallbackCodes(t||this.options.fallbackLng||[],e),i=[],s=e=>{e&&(this.isSupportedCode(e)?i.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return r(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&s(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&s(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&s(this.getLanguagePartFromCode(e))):r(e)&&s(this.formatLanguageCode(e)),n.forEach(e=>{0>i.indexOf(e)&&s(this.formatLanguageCode(e))}),i}}let I=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],M={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)},$=["v1","v2","v3"],D=["v4"],U={zero:0,one:1,two:2,few:3,many:4,other:5},F=()=>{let e={};return I.forEach(t=>{t.lngs.forEach(n=>{e[n]={numbers:t.nr,plurals:M[t.fc]}})}),e};class q{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=j.create("pluralResolver"),(!this.options.compatibilityJSON||D.includes(this.options.compatibilityJSON))&&("undefined"==typeof Intl||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=F(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){let n;let r=k("dev"===e?"en":e),i=t.ordinal?"ordinal":"cardinal",s=JSON.stringify({cleanedCode:r,type:i});if(s in this.pluralRulesCache)return this.pluralRulesCache[s];try{n=new Intl.PluralRules(r,{type:i})}catch(i){if(!e.match(/-|_/))return;let r=this.languageUtils.getLanguagePartFromCode(e);n=this.getRule(r,t)}return this.pluralRulesCache[s]=n,n}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getRule(e,t);return this.shouldUseIntlApi()?n&&n.resolvedOptions().pluralCategories.length>1:n&&n.numbers.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,n).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getRule(e,t);return n?this.shouldUseIntlApi()?n.resolvedOptions().pluralCategories.sort((e,t)=>U[e]-U[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):n.numbers.map(n=>this.getSuffix(e,n,t)):[]}getSuffix(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=this.getRule(e,n);return r?this.shouldUseIntlApi()?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${r.select(t)}`:this.getSuffixRetroCompatible(r,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){let n=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),r=e.numbers[n];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===r?r="plural":1===r&&(r=""));let i=()=>this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString();return"v1"===this.options.compatibilityJSON?1===r?"":"number"==typeof r?`_plural_${r.toString()}`:i():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?i():this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString()}shouldUseIntlApi(){return!$.includes(this.options.compatibilityJSON)}}let H=function(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",s=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=f(e,t,n);return!o&&s&&r(n)&&void 0===(o=x(e,n,i))&&(o=x(t,n,i)),o},B=e=>e.replace(/\$/g,"$$$$");class z{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=j.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:n,useRawValueToEscape:r,prefix:i,prefixEscaped:s,suffix:o,suffixEscaped:a,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:h,nestingSuffix:p,nestingSuffixEscaped:f,nestingOptionsSeparator:g,maxReplaces:y,alwaysFormat:v}=e.interpolation;this.escape=void 0!==t?t:b,this.escapeValue=void 0===n||n,this.useRawValueToEscape=void 0!==r&&r,this.prefix=i?m(i):s||"{{",this.suffix=o?m(o):a||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?m(d):h||m("$t("),this.nestingSuffix=p?m(p):f||m(")"),this.nestingOptionsSeparator=g||",",this.maxReplaces=y||1e3,this.alwaysFormat=void 0!==v&&v,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,i){let o,a,l;let u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=e=>{if(0>e.indexOf(this.formatSeparator)){let r=H(t,u,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(r,void 0,n,{...i,...t,interpolationkey:e}):r}let r=e.split(this.formatSeparator),s=r.shift().trim(),o=r.join(this.formatSeparator).trim();return this.format(H(t,u,s,this.options.keySeparator,this.options.ignoreJSONStructure),o,n,{...i,...t,interpolationkey:s})};this.resetRegExp();let d=i&&i.missingInterpolationHandler||this.options.missingInterpolationHandler,h=i&&i.interpolation&&void 0!==i.interpolation.skipOnVariables?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>B(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?B(this.escape(e)):B(e)}].forEach(t=>{for(l=0;o=t.regex.exec(e);){let n=o[1].trim();if(void 0===(a=c(n))){if("function"==typeof d){let t=d(e,o,i);a=r(t)?t:""}else if(i&&Object.prototype.hasOwnProperty.call(i,n))a="";else if(h){a=o[0];continue}else this.logger.warn(`missed to pass in variable ${n} for interpolating ${e}`),a=""}else r(a)||this.useRawValueToEscape||(a=s(a));let u=t.safeValue(a);if(e=e.replace(o[0],u),h?(t.regex.lastIndex+=a.length,t.regex.lastIndex-=o[0].length):t.regex.lastIndex=0,++l>=this.maxReplaces)break}}),e}nest(e,t){let n,i,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=(e,t)=>{let n=this.nestingOptionsSeparator;if(0>e.indexOf(n))return e;let r=e.split(RegExp(`${n}[ ]*{`)),i=`{${r[1]}`;e=r[0];let s=(i=this.interpolate(i,o)).match(/'/g),a=i.match(/"/g);(s&&s.length%2==0&&!a||a.length%2!=0)&&(i=i.replace(/'/g,'"'));try{o=JSON.parse(i),t&&(o={...t,...o})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${n}${i}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,e};for(;n=this.nestingRegexp.exec(e);){let u=[];(o=(o={...a}).replace&&!r(o.replace)?o.replace:o).applyPostProcessor=!1,delete o.defaultValue;let c=!1;if(-1!==n[0].indexOf(this.formatSeparator)&&!/{.*}/.test(n[1])){let e=n[1].split(this.formatSeparator).map(e=>e.trim());n[1]=e.shift(),u=e,c=!0}if((i=t(l.call(this,n[1].trim(),o),o))&&n[0]===e&&!r(i))return i;r(i)||(i=s(i)),i||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),i=""),c&&(i=u.reduce((e,t)=>this.format(e,t,a.lng,{...a,interpolationkey:n[1].trim()}),i.trim())),e=e.replace(n[0],i),this.regexp.lastIndex=0}return e}}let V=e=>{let t=e.toLowerCase().trim(),n={};if(e.indexOf("(")>-1){let r=e.split("(");t=r[0].toLowerCase().trim();let i=r[1].substring(0,r[1].length-1);"currency"===t&&0>i.indexOf(":")?n.currency||(n.currency=i.trim()):"relativetime"===t&&0>i.indexOf(":")?n.range||(n.range=i.trim()):i.split(";").forEach(e=>{if(e){let[t,...r]=e.split(":"),i=r.join(":").trim().replace(/^'+|'+$/g,""),s=t.trim();n[s]||(n[s]=i),"false"===i&&(n[s]=!1),"true"===i&&(n[s]=!0),isNaN(i)||(n[s]=parseInt(i,10))}})}return{formatName:t,formatOptions:n}},G=e=>{let t={};return(n,r,i)=>{let s=i;i&&i.interpolationkey&&i.formatParams&&i.formatParams[i.interpolationkey]&&i[i.interpolationkey]&&(s={...s,[i.interpolationkey]:void 0});let o=r+JSON.stringify(s),a=t[o];return a||(a=e(k(r),i),t[o]=a),a(n)}};class J{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=j.create("formatter"),this.options=e,this.formats={number:G((e,t)=>{let n=new Intl.NumberFormat(e,{...t});return e=>n.format(e)}),currency:G((e,t)=>{let n=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>n.format(e)}),datetime:G((e,t)=>{let n=new Intl.DateTimeFormat(e,{...t});return e=>n.format(e)}),relativetime:G((e,t)=>{let n=new Intl.RelativeTimeFormat(e,{...t});return e=>n.format(e,t.range||"day")}),list:G((e,t)=>{let n=new Intl.ListFormat(e,{...t});return e=>n.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=G(t)}format(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&0>i[0].indexOf(")")&&i.find(e=>e.indexOf(")")>-1)){let e=i.findIndex(e=>e.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,e)].join(this.formatSeparator)}return i.reduce((e,t)=>{let{formatName:i,formatOptions:s}=V(t);if(this.formats[i]){let t=e;try{let o=r&&r.formatParams&&r.formatParams[r.interpolationkey]||{},a=o.locale||o.lng||r.locale||r.lng||n;t=this.formats[i](e,a,{...s,...r,...o})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${i}`),e},e)}}let K=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class W extends L{constructor(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=r,this.logger=j.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(n,r.backend,r)}queueLoad(e,t,n,r){let i={},s={},o={},a={};return e.forEach(e=>{let r=!0;t.forEach(t=>{let o=`${e}|${t}`;!n.reload&&this.store.hasResourceBundle(e,t)?this.state[o]=2:this.state[o]<0||(1===this.state[o]?void 0===s[o]&&(s[o]=!0):(this.state[o]=1,r=!1,void 0===s[o]&&(s[o]=!0),void 0===i[o]&&(i[o]=!0),void 0===a[t]&&(a[t]=!0)))}),r||(o[e]=!0)}),(Object.keys(i).length||Object.keys(s).length)&&this.queue.push({pending:s,pendingCount:Object.keys(s).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(i),pending:Object.keys(s),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(a)}}loaded(e,t,n){let r=e.split("|"),i=r[0],s=r[1];t&&this.emit("failedLoading",i,s,t),!t&&n&&this.store.addResourceBundle(i,s,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);let o={};this.queue.forEach(n=>{h(n.loaded,[i],s),K(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach(e=>{o[e]||(o[e]={});let t=n.loaded[e];t.length&&t.forEach(t=>{void 0===o[e][t]&&(o[e][t]=!0)})}),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(e=>!e.done)}read(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,s=arguments.length>5?arguments[5]:void 0;if(!e.length)return s(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:n,tried:r,wait:i,callback:s});return}this.readingCalls++;let o=(o,a)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(o&&a&&r<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,n,r+1,2*i,s)},i);return}s(o,a)},a=this.backend[n].bind(this.backend);if(2===a.length){try{let n=a(e,t);n&&"function"==typeof n.then?n.then(e=>o(null,e)).catch(o):o(null,n)}catch(e){o(e)}return}return a(e,t,o)}prepareLoading(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();r(e)&&(e=this.languageUtils.toResolveHierarchy(e)),r(t)&&(t=[t]);let s=this.queueLoad(e,t,n,i);if(!s.toLoad.length)return s.pending.length||i(),null;s.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=e.split("|"),r=n[0],i=n[1];this.read(r,i,"read",void 0,void 0,(n,s)=>{n&&this.logger.warn(`${t}loading namespace ${i} for language ${r} failed`,n),!n&&s&&this.logger.log(`${t}loaded namespace ${i} for language ${r}`,s),this.loaded(e,n,s)})}saveMissing(e,t,n,r,i){let s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=n&&""!==n){if(this.backend&&this.backend.create){let a={...s,isUpdate:i},l=this.backend.create.bind(this.backend);if(l.length<6)try{let i;(i=5===l.length?l(e,t,n,r,a):l(e,t,n,r))&&"function"==typeof i.then?i.then(e=>o(null,e)).catch(o):o(null,i)}catch(e){o(e)}else l(e,t,n,r,o,a)}e&&e[0]&&this.store.addResource(e[0],t,n,r)}}}let X=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),r(e[1])&&(t.defaultValue=e[1]),r(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let n=e[3]||e[2];Object.keys(n).forEach(e=>{t[e]=n[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Y=e=>(r(e.ns)&&(e.ns=[e.ns]),r(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),r(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&0>e.supportedLngs.indexOf("cimode")&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),Q=()=>{},Z=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class ee extends L{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Y(e),this.services={},this.logger=j,this.modules={external:[]},Z(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(n=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(r(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let s=X();this.options={...s,...this.options,...Y(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...s.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let o=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?j.init(o(this.modules.logger),this.options):j.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=J);let n=new N(this.options);this.store=new C(this.options.resources,this.options);let r=this.services;r.logger=j,r.resourceStore=this.store,r.languageUtils=n,r.pluralResolver=new q(n,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(r.formatter=o(t),r.formatter.init(r,this.options),this.options.interpolation.format=r.formatter.format.bind(r.formatter)),r.interpolator=new z(this.options),r.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},r.backendConnector=new W(o(this.modules.backend),r.resourceStore,r,this.options),r.backendConnector.on("*",function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];e.emit(t,...r)}),this.modules.languageDetector&&(r.languageDetector=o(this.modules.languageDetector),r.languageDetector.init&&r.languageDetector.init(r,this.options.detection,this.options)),this.modules.i18nFormat&&(r.i18nFormat=o(this.modules.i18nFormat),r.i18nFormat.init&&r.i18nFormat.init(this)),this.translator=new A(this.services,this.options),this.translator.on("*",function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];e.emit(t,...r)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,n||(n=Q),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let a=i(),l=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),n(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?l():setTimeout(l,0),a}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Q,n=t,i=r(e)?e:this.language;if("function"==typeof e&&(n=e),!this.options.resources||this.options.partialBundledLanguages){if(i&&"cimode"===i.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return n();let e=[],t=t=>{t&&"cimode"!==t&&this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};i?t(i):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>t(e)),this.options.preload&&this.options.preload.forEach(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),n(e)})}else n(null)}reloadResources(e,t,n){let r=i();return"function"==typeof e&&(n=e,e=void 0),"function"==typeof t&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=Q),this.services.backendConnector.reload(e,t,e=>{r.resolve(),n(e)}),r}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&_.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;let s=i();this.emit("languageChanging",e);let o=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(e,r)=>{r?(o(r),this.translator.changeLanguage(r),this.isLanguageChangingTo=void 0,this.emit("languageChanged",r),this.logger.log("languageChanged",r)):this.isLanguageChangingTo=void 0,s.resolve(function(){return n.t(...arguments)}),t&&t(e,function(){return n.t(...arguments)})},l=t=>{e||t||!this.services.languageDetector||(t=[]);let n=r(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);n&&(this.language||o(n),this.translator.language||this.translator.changeLanguage(n),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(n)),this.loadResources(n,e=>{a(e,n)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),s}getFixedT(e,t,n){var i=this;let s=function(e,t){let r,o;if("object"!=typeof t){for(var a=arguments.length,l=Array(a>2?a-2:0),u=2;u<a;u++)l[u-2]=arguments[u];r=i.options.overloadTranslationOptionHandler([e,t].concat(l))}else r={...t};r.lng=r.lng||s.lng,r.lngs=r.lngs||s.lngs,r.ns=r.ns||s.ns,""!==r.keyPrefix&&(r.keyPrefix=r.keyPrefix||n||s.keyPrefix);let c=i.options.keySeparator||".";return o=r.keyPrefix&&Array.isArray(e)?e.map(e=>`${r.keyPrefix}${c}${e}`):r.keyPrefix?`${r.keyPrefix}${c}${e}`:e,i.t(o,r)};return r(e)?s.lng=e:s.lngs=e,s.ns=t,s.keyPrefix=n,s}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let n=t.lng||this.resolvedLanguage||this.languages[0],r=!!this.options&&this.options.fallbackLng,i=this.languages[this.languages.length-1];if("cimode"===n.toLowerCase())return!0;let s=(e,t)=>{let n=this.services.backendConnector.state[`${e}|${t}`];return -1===n||0===n||2===n};if(t.precheck){let e=t.precheck(this,s);if(void 0!==e)return e}return!!(this.hasResourceBundle(n,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||s(n,e)&&(!r||s(i,e)))}loadNamespaces(e,t){let n=i();return this.options.ns?(r(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){let n=i();r(e)&&(e=[e]);let s=this.options.preload||[],o=e.filter(e=>0>s.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return o.length?(this.options.preload=s.concat(o),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}dir(e){return(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),e)?["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf((this.services&&this.services.languageUtils||new N(X())).getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr":"rtl"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new ee(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Q,n=e.forkResourceStore;n&&delete e.forkResourceStore;let r={...this.options,...e,isClone:!0},i=new ee(r);return(void 0!==e.debug||void 0!==e.prefix)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(e=>{i[e]=this[e]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},n&&(i.store=new C(this.store.data,r),i.services.resourceStore=i.store),i.translator=new A(i.services,r),i.translator.on("*",function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];i.emit(e,...n)}),i.init(r,t),i.translator.options=r,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let et=ee.createInstance();et.createInstance=ee.createInstance,et.createInstance,et.dir,et.init,et.loadResources,et.reloadResources,et.use,et.changeLanguage,et.getFixedT,et.t,et.exists,et.setDefaultNamespace,et.hasLoadedNamespace,et.loadNamespaces,et.loadLanguages},46893:function(e){"use strict";e.exports=JSON.parse('{"id":"google-analytics","description":"Install a Google Analytics tag on your website","website":"https://analytics.google.com/analytics/web/","scripts":[{"url":"https://www.googletagmanager.com/gtag/js","params":["id"],"strategy":"worker","location":"head","action":"append"},{"code":"window.dataLayer=window.dataLayer||[];window.gtag=function gtag(){window.dataLayer.push(arguments);};gtag(\'js\',new Date());gtag(\'config\',\'${args.id}\')","strategy":"worker","location":"head","action":"append"}]}')},46362:function(e){"use strict";e.exports=JSON.parse('{"id":"google-maps-embed","description":"Embed a Google Maps embed on your webpage","website":"https://developers.google.com/maps/documentation/embed/get-started","html":{"element":"iframe","attributes":{"loading":"lazy","src":{"url":"https://www.google.com/maps/embed/v1/place","slugParam":"mode","params":["key","q","center","zoom","maptype","language","region"]},"referrerpolicy":"no-referrer-when-downgrade","frameborder":"0","style":"border:0","allowfullscreen":true,"width":null,"height":null}}}')},95825:function(e){"use strict";e.exports=JSON.parse('{"id":"youtube-embed","description":"Embed a YouTube embed on your webpage.","website":"https://github.com/paulirish/lite-youtube-embed","html":{"element":"lite-youtube","attributes":{"videoid":null,"playlabel":null}},"stylesheets":["https://cdn.jsdelivr.net/gh/paulirish/lite-youtube-embed@master/src/lite-yt-embed.css"],"scripts":[{"url":"https://cdn.jsdelivr.net/gh/paulirish/lite-youtube-embed@master/src/lite-yt-embed.js","strategy":"idle","location":"head","action":"append"}]}')}}]);