"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[54712],{80114:function(t,e,i){i.d(e,{default:function(){return o}});var s=i(57437),n=i(85487),r=i(94508),a=i(43949);function o(t){let{className:e}=t,{t:i}=(0,a.$G)();return(0,s.jsx)("div",{className:(0,r.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",e),children:(0,s.jsx)(n.<PERSON><PERSON>,{title:i("Loading..."),className:"text-foreground"})})}},35974:function(t,e,i){i.d(e,{C:function(){return o}});var s=i(57437),n=i(90535);i(2265);var r=i(94508);let a=(0,n.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(t){let{className:e,variant:i,...n}=t;return(0,s.jsx)("div",{className:(0,r.ZP)(a({variant:i}),e),...n})}},17062:function(t,e,i){i.d(e,{Z:function(){return v},O:function(){return m}});var s=i(57437),n=i(80114);i(83079);var r=(0,i(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=i(31117),o=i(79981),d=i(78040),u=i(83130);class l{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(t){this.as=t.as,this.asname=t.asname,this.city=t.city,this.continent=t.continent,this.continentCode=t.continentCode,this.country=t.country,this.countryCode=t.countryCode,this.currency=t.currency,this.district=t.district,this.hosting=t.hosting,this.isp=t.isp,this.lat=t.lat,this.lon=t.lon,this.mobile=t.mobile,this.offset=t.offset,this.org=t.org,this.proxy=t.proxy,this.query=t.query,this.region=t.region,this.regionName=t.regionName,this.reverse=t.reverse,this.status=t.status,this.timezone=t.timezone,this.zip=t.zip}}var h=i(99376),c=i(2265);let p=c.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),m=()=>c.useContext(p);function v(t){let{children:e}=t,[i,m]=c.useState("Desktop"),[v,f]=c.useState(!1),[g,w]=c.useState(),{data:y,isLoading:A,error:C,mutate:I}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:b,isLoading:k}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:x,isLoading:D}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),L=(0,h.useRouter)(),F=(0,h.usePathname)();c.useEffect(()=>{(async()=>{m((await r()).deviceType)})()},[]),c.useEffect(()=>{let t=()=>{let t=window.innerWidth;m(t<768?"Mobile":t<1024?"Tablet":"Desktop"),f(t>1024)};return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]),c.useLayoutEffect(()=>{(async()=>{try{let{data:t}=await o.Z.post("/auth/geo-location");w(new l(t))}catch(t){}})()},[]),c.useLayoutEffect(()=>{C&&!d.sp.includes(F)&&L.push("/signin")},[C]);let E=c.useMemo(()=>{var t,e,s;return{isAuthenticate:!!(null==y?void 0:null===(t=y.data)||void 0===t?void 0:t.login),auth:(null==y?void 0:null===(e=y.data)||void 0===e?void 0:e.user)?new u.n(null==y?void 0:null===(s=y.data)||void 0===s?void 0:s.user):null,isLoading:A,deviceLocation:g,refreshAuth:()=>I(y),isExpanded:v,device:i,setIsExpanded:f,branding:null==b?void 0:b.data,googleAnalytics:(null==x?void 0:x.data)?{active:null==x?void 0:x.data.active,apiKey:null==x?void 0:x.data.apiKey}:{active:!1,apiKey:""}}},[y,g,v,i]),N=!A&&!k&&!D;return(0,s.jsx)(p.Provider,{value:E,children:N?e:(0,s.jsx)(n.default,{})})}},21251:function(t,e,i){i.d(e,{T:function(){return n}});var s=i(17062);let n=()=>{let{branding:t}=(0,s.O)();return t}},74539:function(t,e,i){i.d(e,{k:function(){return s}});class s{constructor(t){this.id=null==t?void 0:t.id,this.city=null==t?void 0:t.city,this.countryCode=null==t?void 0:t.countryCode,this.addressLine=null==t?void 0:t.addressLine,this.street=null==t?void 0:t.street,this.type=null==t?void 0:t.type,this.zipCode=null==t?void 0:t.zipCode,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}},83130:function(t,e,i){i.d(e,{n:function(){return d}});class s{constructor(t){this.id=null==t?void 0:t.id,this.userId=null==t?void 0:t.userId,this.agentId=null==t?void 0:t.agentId,this.name=null==t?void 0:t.name,this.email=null==t?void 0:t.email,this.occupation=null==t?void 0:t.occupation,this.status=null==t?void 0:t.status,this.isRecommended=!!(null==t?void 0:t.isRecommended),this.isSuspend=!!(null==t?void 0:t.isSuspend),this.proof=null==t?void 0:t.proof,this.depositFee=null==t?void 0:t.depositFee,this.withdrawFee=null==t?void 0:t.withdrawFee,this.withdrawCommission=null==t?void 0:t.withdrawCommission,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}var n=i(84937);class r{constructor(t){this.id=null==t?void 0:t.id,this.userId=null==t?void 0:t.userId,this.merchantId=null==t?void 0:t.merchantId,this.name=null==t?void 0:t.name,this.email=null==t?void 0:t.email,this.status=null==t?void 0:t.status,this.isSuspend=null==t?void 0:t.isSuspend,this.proof=null==t?void 0:t.proof,this.depositFee=null==t?void 0:t.depositFee,this.withdrawFee=null==t?void 0:t.withdrawFee,this.webhookUrl=null==t?void 0:t.webhookUrl,this.allowedIp=null==t?void 0:t.allowedIp,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}var a=i(66419),o=i(78040);class d{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(t){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=t.id,this.roleId=t.roleId,this.email=t.email,this.isEmailVerified=t.isEmailVerified,this.status=t.status,this.kycStatus=t.kycStatus,this.kyc=t.kyc||null,this.lastIpAddress=t.lastIpAddress,this.lastCountryName=t.lastCountryName,this.passwordUpdated=t.passwordUpdated,this.referredBy=t.referredBy,this.referralCode=t.referralCode,this.otpCode=t.otpCode,this.createdAt=new Date(t.createdAt),this.updatedAt=new Date(t.updatedAt),this.role=new a.u(t.role),this.permission=t.permission,this.customer=(null==t?void 0:t.customer)?new n.O(t.customer):void 0,this.merchant=(null==t?void 0:t.merchant)?new r(t.merchant):void 0,this.agent=(null==t?void 0:t.agent)?new s(t.agent):void 0}}},84937:function(t,e,i){i.d(e,{O:function(){return n}});var s=i(74539);class n{constructor(t){var e,i;this.id=null==t?void 0:t.id,this.userId=null==t?void 0:t.userId,this.name=null==t?void 0:t.name,this.email=null==t?void 0:null===(e=t.user)||void 0===e?void 0:e.email,this.phone=(null==t?void 0:null===(i=t.phone)||void 0===i?void 0:i.match(/^\+/))?t.phone:"+".concat(null==t?void 0:t.phone),this.gender=null==t?void 0:t.gender,this.dob=new Date(null==t?void 0:t.dob),this.avatar=null==t?void 0:t.profileImage,this.address=new s.k(null==t?void 0:t.address),this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}},66419:function(t,e,i){i.d(e,{u:function(){return s}});class s{constructor(t){this.id=null==t?void 0:t.id,this.name=null==t?void 0:t.name,this.createdAt=new Date(null==t?void 0:t.createdAt),this.updatedAt=new Date(null==t?void 0:t.updatedAt)}}},63261:function(t,e,i){i.d(e,{E:function(){return o}});class s{constructor(t={}){this.id=t.id||0,this.trxId=t.trxId||"",this.type=t.type||"",this.from=t.from||"",this.to=t.to||"",this.amount=t.amount||0,this.fee=t.fee||0,this.total=t.total||0,this.status=t.status||"",this.method=t.method||"",this.isBookmarked=t.isBookmarked||0,this.metaData=t.metaData||"",this.userId=t.userId||0,this.createdAt=t.createdAt||"",this.updatedAt=t.updatedAt||""}}class n{constructor(t={}){this.id=t.id||0,this.userId=t.userId||0,this.addressId=t.addressId||0,this.agentId=t.agentId||"",this.name=t.name||"",this.email=t.email||"",this.occupation=t.occupation||"",this.whatsapp=t.whatsapp||null,this.status=t.status||"",this.isRecommended=t.isRecommended||0,this.isSuspend=t.isSuspend||0,this.proof=t.proof||"",this.depositFee=t.depositFee||0,this.withdrawalFee=t.withdrawalFee||0,this.depositCommission=t.depositCommission||0,this.withdrawalCommission=t.withdrawalCommission||0,this.agreeFundingCustomer=t.agreeFundingCustomer||0,this.agreeHonest=t.agreeHonest||0,this.agreeRechargeCustomer=t.agreeRechargeCustomer||0,this.createdAt=t.createdAt||"",this.updatedAt=t.updatedAt||"",this.user=t.user?new r(t.user):new r}}class r{constructor(t={}){this.id=t.id||0,this.roleId=t.roleId||0,this.email=t.email||"",this.isEmailVerified=t.isEmailVerified||0,this.status=t.status||0,this.kycStatus=t.kycStatus||0,this.lastIpAddress=t.lastIpAddress||"",this.lastCountryName=t.lastCountryName||"",this.passwordUpdated=t.passwordUpdated||0,this.referralCode=t.referralCode||"",this.referredBy=t.referredBy||null,this.otpCode=t.otpCode||null,this.acceptTermsCondition=t.acceptTermsCondition||0,this.limitTransfer=t.limitTransfer||0,this.dailyTransferLimit=t.dailyTransferLimit||null,this.createdAt=t.createdAt||"",this.updatedAt=t.updatedAt||"",this.customer=(null==t?void 0:t.customer)?new a(t.customer):new a}}class a{constructor(t={}){this.id=t.id||0,this.userId=t.userId||0,this.addressId=t.addressId||0,this.firstName=t.firstName||"",this.lastName=t.lastName||"",this.profileImage=t.profileImage||null,this.phone=t.phone||"",this.gender=t.gender||"",this.dob=t.dob||"",this.createdAt=t.createdAt||"",this.updatedAt=t.updatedAt||""}}class o{constructor(t={}){this.id=t.id||0,this.agentId=t.agentId||0,this.transactionId=t.transactionId||0,this.amount=t.amount||0,this.currency=t.currency||"",this.status=t.status||"",this.createdAt=t.createdAt||"",this.updatedAt=t.updatedAt||"",this.transaction=t.transaction?new s(t.transaction):new s,this.agent=t.agent?new n(t.agent):new n}}}}]);