(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2880],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},90145:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>L,default:()=>N});var a,r={};s.r(r),s.d(r,{AppRouter:()=>u.WY,ClientPageRoot:()=>u.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>u.yO,NotFoundBoundary:()=>u.O4,Postpone:()=>u.hQ,RenderFromTemplateContext:()=>u.b5,__next_app__:()=>g,actionAsyncStorage:()=>u.Wz,createDynamicallyTrackedSearchParams:()=>u.rL,createUntrackedSearchParams:()=>u.S5,decodeAction:()=>u.Hs,decodeFormState:()=>u.dH,decodeReply:()=>u.kf,originalPathname:()=>f,pages:()=>h,patchFetch:()=>u.XH,preconnect:()=>u.$P,preloadFont:()=>u.C5,preloadStyle:()=>u.oH,renderToReadableStream:()=>u.aW,requestAsyncStorage:()=>u.Fg,routeModule:()=>x,serverHooks:()=>u.GP,staticGenerationAsyncStorage:()=>u.AT,taintObjectReference:()=>u.nr,tree:()=>p}),s(67206);var n=s(79319),i=s(20518),o=s(61902),c=s(62042),l=s(44630),d=s(44828),m=s(65505),u=s(13839);let p=["",{children:["(protected)",{admin:["children",{children:["merchants",{children:["[userId]",{children:["[merchantId]",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56700)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\transactions\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,7207)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\transactions\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,26105)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,73722)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,76667)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,94626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\merchants\\[userId]\\[merchantId]\\transactions\\page.tsx"],f="/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page",g={require:s,loadChunk:()=>Promise.resolve()},x=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page",pathname:"/merchants/[userId]/[merchantId]/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var S=s(69094),E=s(5787),P=s(90527);let v=e=>e?JSON.parse(e):void 0,I=self.__BUILD_MANIFEST,D=v(self.__REACT_LOADABLE_MANIFEST),A=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page"],b=v(self.__RSC_SERVER_MANIFEST),j=v(self.__NEXT_FONT_MANIFEST),_=v(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];A&&b&&(0,E.Mo)({clientReferenceManifest:A,serverActionsManifest:b,serverModuleMap:(0,P.w)({serverActionsManifest:b,pageName:"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page"})});let y=(0,i.d)({pagesType:S.s.APP,dev:!1,page:"/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page",appMod:null,pageMod:r,errorMod:null,error500Mod:null,Document:null,buildManifest:I,renderToHTML:c.f,reactLoadableManifest:D,clientReferenceManifest:A,serverActionsManifest:b,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:j,incrementalCacheHandler:null,interceptionRouteRewrites:_}),L=r;function N(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:y})}},31591:(e,t,s)=>{Promise.resolve().then(s.bind(s,13600))},47193:(e,t,s)=>{Promise.resolve().then(s.bind(s,84548))},13600:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E,runtime:()=>S});var a=s(60926),r=s(14579),n=s(30417),i=s(89551),o=s(53042),c=s(44788),l=s(38071),d=s(28531),m=s(5764),u=s(47020),p=s(737),h=s(64947);s(29220);var f=s(39228),g=s(32167),x=s(91500);let S="edge";function E({children:e}){let t=(0,h.UO)(),s=(0,h.lr)(),S=(0,h.tv)(),E=(0,h.jD)(),{t:P}=(0,f.$G)(),v=[{title:P("Account Details"),icon:(0,a.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}?${s.toString()}`,id:"__DEFAULT__"},{title:P("Transactions"),icon:(0,a.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/transactions?${s.toString()}`,id:"transactions"},{title:P("KYC"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/kyc?${s.toString()}`,id:"kyc"},{title:P("Fees"),icon:(0,a.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/fees?${s.toString()}`,id:"fees"},{title:P("Permissions"),icon:(0,a.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/permissions?${s.toString()}`,id:"permissions"},{title:P("Send Email"),icon:(0,a.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/merchants/${t?.userId}/${t?.merchantId}/send-email?${s.toString()}`,id:"send-email"}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,a.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,a.jsx)("li",{children:(0,a.jsxs)(p.Z,{href:"/merchants/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,a.jsx)(u.Z,{}),P("Back")]})}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",s.get("name")]}),(0,a.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",P("Merchant")," #",t.merchantId]})]}),(0,a.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,a.jsx)("span",{children:P("Active")}),(0,a.jsx)(n.Z,{defaultChecked:"1"===s.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:e=>{g.toast.promise((0,i.z)(t.userId),{loading:P("Loading..."),success:a=>{if(!a.status)throw Error(a.message);let r=new URLSearchParams(s);return(0,x.j)(`/admin/merchants/${t.merchantId}`),r.set("active",e?"1":"0"),S.push(`${E}?${r.toString()}`),a.message},error:e=>e.message})}})]})]}),(0,a.jsx)(r.a,{tabs:v})]}),e]})}},84548:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(60926),r=s(3770),n=s(78133),i=s(69226),o=s(20293),c=s(58904),l=s(74988),d=s(43291),m=s(98903),u=s(65091),p=s(32917),h=s(34870),f=s(65694),g=s(48132),x=s(64947),S=s(29220),E=s(39228),P=s(44942);function v(){let e=(0,x.lr)(),t=(0,x.tv)(),s=(0,x.UO)(),v=(0,x.jD)(),{t:I}=(0,E.$G)(),[D,A]=S.useState(e.get("search")??""),{data:b,meta:j,isLoading:_,filter:y}=(0,m.Z)(`/admin/transactions/${s.userId}?${e.toString()}`),{data:L,isLoading:N}=(0,d.d)(`/admin/transactions/counts/${s.userId}`);return(0,a.jsxs)("div",{className:"h-full p-4",children:[(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-12 gap-4",children:[(0,a.jsx)(P.x,{value:L?.data?.deposit,title:I("Total Deposit"),icon:e=>(0,a.jsx)(p.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",statusClass:"text-spacial-green",iconClass:"bg-spacial-green-foreground",isLoading:N}),(0,a.jsx)(P.x,{value:L?.data?.withdraw,title:I("Total Withdraw"),icon:e=>(0,a.jsx)(h.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-red-foreground text-spacial-red",statusClass:"text-spacial-red",isLoading:N}),(0,a.jsx)(P.x,{value:L?.data?.transfer,title:I("Total Transfers"),icon:e=>(0,a.jsx)(f.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-blue-foreground text-spacial-blue",statusClass:"text-spacial-blue",isLoading:N}),(0,a.jsx)(P.x,{value:L?.data?.exchange,title:I("Total Exchange"),icon:e=>(0,a.jsx)(g.Z,{...e}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",isLoading:N})]}),(0,a.jsxs)("div",{className:"h-fit w-full overflow-auto rounded-xl bg-background p-6 shadow-default",children:[(0,a.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,a.jsx)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:(0,a.jsx)(c.Z,{filter:y})}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,a.jsx)(n.R,{value:D,onChange:e=>{e.preventDefault();let s=(0,u.w4)(e.target.value);A(e.target.value),t.replace(`${v}?${s.toString()}`)},iconPlacement:"end",placeholder:I("Search...")}),(0,a.jsx)(o.k,{canFilterByAgent:!0,canFilterByMethod:!0,canFilterByGateway:!0}),(0,a.jsx)(i._,{url:`/admin/transactions/export/${s.userId}`})]})]}),(0,a.jsx)(l.Z,{className:"my-4"}),(0,a.jsx)(r.Z,{data:b,meta:j,isLoading:_})]})]})}},26105:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,runtime:()=>r});var a=s(18264);let r=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#runtime`),n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\layout.tsx#default`)},73722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}},7207:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(r.a,{})})}},56700:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\merchants\[userId]\[merchantId]\transactions\page.tsx#default`)},76667:(e,t,s)=>{"use strict";function a({children:e}){return e}s.r(t),s.d(t,{default:()=>a}),s(87908)},94626:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4774,7848,6147,1991,7283,5089,3711,4656,8748,4153],()=>t(90145));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page"]=s}]);
//# sourceMappingURL=page.js.map