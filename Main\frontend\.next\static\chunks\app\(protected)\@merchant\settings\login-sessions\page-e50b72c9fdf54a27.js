(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[49960],{72899:function(e,s,l){Promise.resolve().then(l.bind(l,79758))},79758:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return f}});var a=l(57437),n=l(99376);l(2265);var t=l(41169),i=l(62869),r=l(66070),c=l(6512),o=l(97054),u=l(75730),m=l(43949),d=l(14438);function f(){var e,s;let{t:l}=(0,m.$G)(),f=(0,n.useSearchParams)(),x=(0,n.useRouter)(),{data:h,meta:g,isLoading:p}=(0,u.Z)("/login-sessions?page=".concat(null!==(e=f.get("page"))&&void 0!==e?e:1,"&limit=").concat(null!==(s=f.get("limit"))&&void 0!==s?s:10));return(0,a.jsx)("div",{className:"flex flex-col gap-4",children:(0,a.jsxs)(r.Zb,{className:"flex flex-col gap-4 rounded-xl p-4 shadow-default",children:[(0,a.jsx)(r.Ol,{className:"justify-center p-0 sm:h-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 sm:flex-row sm:items-center",children:[(0,a.jsx)(r.ll,{className:"text-base font-medium leading-[22px]",children:l("Login Sessions")}),(0,a.jsx)(i.z,{onClick:e=>{e.preventDefault(),d.toast.promise(o.x,{loading:l("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return x.refresh(),e.message},error:e=>e.message})},variant:"outline",size:"sm",type:"button",className:"ml-2.5 cursor-pointer text-sm",asChild:!0,children:(0,a.jsx)("div",{children:l("Logout from all device")})})]})}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px]"}),(0,a.jsx)(r.aY,{className:"p-0",children:(0,a.jsx)(t.Z,{data:h,isLoading:p,meta:g})})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,2901,85210,68211,81527,92971,95030,1744],function(){return e(e.s=72899)}),_N_E=e.O()}]);