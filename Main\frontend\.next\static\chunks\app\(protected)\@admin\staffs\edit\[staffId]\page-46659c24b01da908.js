(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[54311],{6673:function(e,d,i){Promise.resolve().then(i.bind(i,2855))},2855:function(e,d,i){"use strict";i.r(d),i.d(d,{default:function(){return j}});var r=i(57437),s=i(8142),n=i(85487),o=i(62869),a=i(15681),t=i(79981),l=i(97751);async function u(e,d){try{let i=await t.Z.put("/admin/users/edit-admin/".concat(d),e);return(0,l.B)(i)}catch(e){return(0,l.D)(e)}}var c=i(40593),v=i(31117),m=i(31229);let f=m.z.object({firstName:m.z.string().optional(),lastName:m.z.string({required_error:"Last name is required"}),email:m.z.string().email("Invalid email address"),newPassword:m.z.string().optional(),addressLine:m.z.string({required_error:"Address line is required"}),zipCode:m.z.string({required_error:"Zip code is required"}),city:m.z.string({required_error:"City is required"}),countryCode:m.z.string({required_error:"Country is required"}),phone:m.z.string({required_error:"Phone is required"}),gender:m.z.enum(["male","female"]).default("male"),dob:m.z.date()});var y=i(13590),h=i(2901),p=i(64394),x=i(99376),g=i(2265),b=i(29501),z=i(43949),N=i(14438);function j(){var e,d;let{t:i}=(0,z.$G)(),t=(0,x.useParams)(),[l,m]=(0,g.useTransition)(),j=(0,x.useRouter)(),[C,w]=g.useState(),{getCountryByCode:q}=(0,c.F)(),{data:_,isLoading:k}=(0,v.d)("/admin/users/".concat(t.staffId)),L=null==_?void 0:null===(e=_.data)||void 0===e?void 0:e.customer,P=(0,b.cI)({resolver:(0,y.F)(f),defaultValues:{firstName:"",lastName:"",email:"",newPassword:"",addressLine:"",countryCode:null==L?void 0:null===(d=L.address)||void 0===d?void 0:d.countryCode,zipCode:"",city:"",phone:"",gender:"male",dob:void 0}}),E=g.useCallback(()=>{var e,d,i,r,s,n,o,a;(null==_?void 0:null===(e=_.data)||void 0===e?void 0:e.id)&&(q(null==L?void 0:null===(d=L.address)||void 0===d?void 0:d.countryCode,w),P.reset({firstName:null==L?void 0:L.firstName,lastName:null==L?void 0:L.lastName,email:null==_?void 0:null===(i=_.data)||void 0===i?void 0:i.email,addressLine:null==L?void 0:null===(r=L.address)||void 0===r?void 0:r.addressLine,countryCode:null==L?void 0:null===(s=L.address)||void 0===s?void 0:s.countryCode,zipCode:null==L?void 0:null===(n=L.address)||void 0===n?void 0:n.zipCode,city:null==L?void 0:null===(o=L.address)||void 0===o?void 0:o.city,phone:null==L?void 0:L.phone,gender:null==L?void 0:null===(a=L.customer)||void 0===a?void 0:a.gender,dob:new Date(null==L?void 0:L.dob)}))},[k]);return(g.useEffect(()=>E(),[E]),k)?(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(n.Loader,{})}):(0,r.jsx)("div",{className:"min-h-[calc(100vh-78px)] bg-background",children:(0,r.jsxs)("div",{className:"container max-w-[716px] py-16",children:[(0,r.jsxs)("div",{className:"mb-8 flex items-center gap-4",children:[(0,r.jsx)(o.z,{type:"button",variant:"outline",size:"icon",onClick:()=>j.back(),children:(0,r.jsx)(p.Z,{})}),(0,r.jsx)("h3",{children:i("Edit Staff")})]}),(0,r.jsx)(a.l0,{...P,children:(0,r.jsxs)("form",{onSubmit:P.handleSubmit(e=>{m(async()=>{let{newPassword:d,...r}=e,s=d?{...r,password:d,dob:(0,h.WU)(e.dob,"yyyy-MM-dd")}:{...r,dob:(0,h.WU)(e.dob,"yyyy-MM-dd")},n=await u(s,t.staffId);(null==n?void 0:n.status)?(N.toast.success(n.message),j.push("/staffs"),P.reset()):N.toast.error(i(n.message))})},()=>{N.toast.error(i("Something went wrong."))}),className:"flex flex-col space-y-4",children:[(0,r.jsx)(s.Z,{form:P,defaultCountry:C,defaultPhone:null==L?void 0:L.phone}),(0,r.jsx)("div",{className:"flex items-center justify-end py-4",children:(0,r.jsx)(o.z,{disabled:l,children:l?(0,r.jsx)(n.Loader,{title:i("Processing..."),className:"text-primary-foreground"}):i("Update")})})]})})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,31384,60627,50666,227,71400,92971,95030,1744],function(){return e(e.s=6673)}),_N_E=e.O()}]);