"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[93300],{93300:function(e,t,r){r.r(t),r.d(t,{default:function(){return w}});var n=r(57437),s=r(41709),a=r(85487),i=r(62869),o=r(95186),l=r(26815),c=r(76818),u=r(79981),d=r(97751);async function f(e){try{let t=await u.Z.post("/users/send-support-email",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,d.B)(t)}catch(e){return(0,d.D)(e)}}var m=r(15641),p=r(38503),v=r(48391),h=r(33145),g=r(2265),x=r(43949),b=r(14438);function y(){var e;let{t}=(0,x.$G)(),[r,u]=(0,g.useTransition)(),[d,y]=(0,g.useState)({subject:"",message:"",attachments:[]}),j=e=>{if(e.preventDefault(),"file"===e.target.type){let t=e.target;t.files&&t.files.length>0&&y(e=>({...e,attachments:[...e.attachments||[],...Array.from(t.files)]}))}else y(t=>({...t,[e.target.name]:e.target.value}))},w=e=>{y(t=>{var r;let n=null===(r=t.attachments)||void 0===r?void 0:r.filter(t=>t.name!==e.name||t.size!==e.size);return{...t,attachments:n}})};return(0,n.jsxs)("form",{onSubmit:e=>{var t;e.preventDefault();let r=new FormData;r.append("subject",d.subject),r.append("message",d.message),null===(t=d.attachments)||void 0===t||t.forEach(e=>{r.append("attachments[]",e)}),u(async()=>{let e=await f(r);null==e||e.status,b.toast.success(e.message)})},className:"flex flex-col gap-4",children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)(l.Z,{children:t("Subject")}),(0,n.jsx)(o.I,{name:"subject",type:"text",value:d.subject,onChange:j})]}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)(l.Z,{children:t("Message")}),(0,n.jsx)(c.g,{name:"message",rows:20,value:d.message,onChange:j})]}),(0,n.jsxs)("div",{className:"relative w-full",children:[(0,n.jsx)(s.J,{condition:(null==d?void 0:null===(e=d.attachments)||void 0===e?void 0:e.length)!==0,children:(0,n.jsx)("div",{className:"mb-4 flex flex-col gap-3 rounded-md border bg-input p-4",children:(e=>{var t,r;if(!e||(null==e?void 0:e.length)===0)return null;let s=Object.groupBy(e,e=>e.type.startsWith("image/")?"images":"others");return(0,n.jsxs)(n.Fragment,{children:[Object.prototype.hasOwnProperty.call(s,"images")&&(0,n.jsx)("div",{className:"flex flex-wrap items-center gap-2 [&img]:aspect-square [&img]:max-w-28",children:null==s?void 0:null===(t=s.images)||void 0===t?void 0:t.map((e,t)=>(0,n.jsxs)("div",{className:"group relative flex",children:[(0,n.jsx)(h.default,{src:URL.createObjectURL(e),alt:e.name,width:128,height:128,loading:"lazy",className:"t-fill h-28 w-28 rounded-xl border"}),(0,n.jsx)(i.z,{type:"button",variant:"destructive",size:"icon",onClick:()=>w(e),className:"pointer-events-none invisible absolute right-1 top-1 size-7 group-hover:pointer-events-auto group-hover:visible",children:(0,n.jsx)(m.Z,{size:15})})]},t))}),Object.prototype.hasOwnProperty.call(s,"others")&&(0,n.jsx)("ul",{className:"list-inside list-disc",children:null==s?void 0:null===(r=s.others)||void 0===r?void 0:r.map(e=>(0,n.jsxs)("li",{className:"group text-sm",children:[e.name,(0,n.jsx)(i.z,{type:"button",variant:"link",size:"icon",onClick:()=>w(e),className:"pointer-events-none invisible size-7 group-hover:pointer-events-auto group-hover:visible",children:(0,n.jsx)(m.Z,{size:15})})]},e.name+e.lastModified))})]})})(d.attachments)})}),(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsxs)(i.z,{disabled:r,type:"submit",className:"w-fit min-w-32",children:[(0,n.jsxs)(s.J,{condition:!r,children:[(0,n.jsx)(p.Z,{}),t("Send")]}),(0,n.jsx)(s.J,{condition:r,children:(0,n.jsx)(a.Loader,{title:t("Sending..."),className:"text-primary-foreground"})})]}),(0,n.jsx)(i.z,{type:"button",variant:"link",className:"cursor-pointer gap-1 text-secondary-text hover:text-foreground",asChild:!0,children:(0,n.jsxs)(l.Z,{htmlFor:"attachments",children:[(0,n.jsx)(v.Z,{}),t("Attachment"),(0,n.jsx)(o.I,{id:"attachments",type:"file",multiple:!0,name:"attachments",onChange:j,className:"hidden"})]})})]})]})]})}var j=r(6512);function w(){let{t:e}=(0,x.$G)();return(0,n.jsxs)("div",{className:"m-4 rounded-xl bg-background p-4 md:p-10",children:[(0,n.jsx)("h2",{className:"mb-1",children:e("Need Help?")}),(0,n.jsx)("p",{className:"text-sm text-secondary-text",children:e("We're here to assist you with any questions or issues you may encounter. Please feel free to contact our support team by emailing:")}),(0,n.jsx)(j.Z,{className:"my-6"}),(0,n.jsx)("div",{className:"mb-6 rounded-md bg-info/20 p-4 text-sm",children:e("For faster assistance, please provide as much detail as possible about your issue, including screenshots or error messages if applicable. Our support hours are 24/7.")}),(0,n.jsx)(y,{})]})}},41709:function(e,t,r){function n(e){let{condition:t,children:r}=e;return t?r:null}r.d(t,{J:function(){return n}}),r(2265)},85487:function(e,t,r){r.d(t,{Loader:function(){return i}});var n=r(57437),s=r(94508),a=r(43949);function i(e){let{title:t="Loading...",className:r}=e,{t:i}=(0,a.$G)();return(0,n.jsxs)("div",{className:(0,s.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:i(t)})]})}},62869:function(e,t,r){r.d(t,{d:function(){return l},z:function(){return c}});var n=r(57437),s=r(37053),a=r(90535),i=r(2265),o=r(94508);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...u}=e,d=c?s.g7:"button";return(0,n.jsx)(d,{className:(0,o.ZP)(l({variant:a,size:i,className:r})),ref:t,...u})});c.displayName="Button"},95186:function(e,t,r){r.d(t,{I:function(){return i}});var n=r(57437),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,type:s,...i}=e;return(0,n.jsx)("input",{type:s,className:(0,a.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...i})});i.displayName="Input"},26815:function(e,t,r){var n=r(57437),s=r(6394),a=r(90535),i=r(2265),o=r(94508);let l=(0,a.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.f,{ref:t,className:(0,o.ZP)(l(),r),...a})});c.displayName=s.f.displayName,t.Z=c},6512:function(e,t,r){var n=r(57437),s=r(55156),a=r(2265),i=r(94508);let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:o=!0,...l}=e;return(0,n.jsx)(s.f,{ref:t,decorative:o,orientation:a,className:(0,i.ZP)("shrink-0 bg-divider","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...l})});o.displayName=s.f.displayName,t.Z=o},76818:function(e,t,r){r.d(t,{g:function(){return i}});var n=r(57437),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("textarea",{className:(0,a.ZP)("placeholder:text-placeholder flex min-h-[80px] w-full rounded-md border border-input bg-input px-3 py-2 text-base ring-offset-background placeholder:font-normal focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});i.displayName="Textarea"},97751:function(e,t,r){r.d(t,{B:function(){return s},D:function(){return a}});var n=r(43577);function s(e){var t,r,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function a(e){let t=500,r="Internal Server Error",s="An unknown error occurred";if((0,n.IZ)(e)){var a,i,o,l,c,u,d,f,m,p,v,h;t=null!==(m=null===(a=e.response)||void 0===a?void 0:a.status)&&void 0!==m?m:500,r=null!==(p=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==p?p:"Internal Server Error",s=null!==(h=null!==(v=null===(u=e.response)||void 0===u?void 0:null===(c=u.data)||void 0===c?void 0:null===(l=c.messages)||void 0===l?void 0:null===(o=l[0])||void 0===o?void 0:o.message)&&void 0!==v?v:null===(f=e.response)||void 0===f?void 0:null===(d=f.data)||void 0===d?void 0:d.message)&&void 0!==h?h:e.message}else e instanceof Error&&(s=e.message);return{statusCode:t,statusText:r,status:!1,message:s,data:void 0,error:e}}},79981:function(e,t,r){var n=r(78040),s=r(83464);t.Z=s.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){r.d(t,{rH:function(){return n},sp:function(){return s}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},s=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){r.d(t,{F:function(){return u},Fg:function(){return m},Fp:function(){return c},Qp:function(){return f},ZP:function(){return o},fl:function(){return l},qR:function(){return d},w4:function(){return p}});var n=r(78040),s=r(61994),a=r(14438),i=r(53335);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,s.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let s;let a=void 0===t?this.currencyCode:t;try{s=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=s.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:a,o=s.format(e),l=o.substring(i.length).trim();return{currencyCode:a,currencySymbol:i,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",s=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?s.set(n,e):s.delete(n),s}}}]);