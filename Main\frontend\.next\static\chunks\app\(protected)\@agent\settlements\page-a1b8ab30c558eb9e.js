(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[99431],{95578:function(e,t,n){Promise.resolve().then(n.bind(n,11883))},11883:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return R}});var r=n(57437),a=n(25833),i=n(85539),o=n(27186),s=n(35974),l=n(6512),c=n(3612),u=n(21251),d=n(75730),f=n(94508),m=n(63261),p=n(2901),v=n(99376),h=n(2265),x=n(43949),g=n(39785),y=n(62869),b=n(26815),w=n(57054),N=n(53647),j=n(61029),S=n(96567);function C(){let{t:e}=(0,x.$G)(),t=(0,v.useSearchParams)(),n=(0,v.usePathname)(),a=(0,v.useRouter)(),[i,o]=h.useState({}),[s,l]=h.useState(!1),c=(e,r)=>{let i=new URLSearchParams(t.toString());r?(i.set(e,r),o(t=>({...t,[e]:r}))):(i.delete(e),o(t=>({...t,[e]:""}))),a.replace("".concat(n,"?").concat(i.toString()))};return h.useEffect(()=>{let e=Object.fromEntries(t.entries());e&&o(e)},[]),(0,r.jsxs)(w.J2,{open:s,onOpenChange:l,children:[(0,r.jsx)(w.xo,{asChild:!0,children:(0,r.jsxs)(y.z,{variant:"outline",children:[(0,r.jsx)(S.Z,{size:20}),e("Filter")]})}),(0,r.jsx)(w.yk,{className:"w-full min-w-[300px] max-w-[400px]",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,r.jsx)(b.Z,{className:"text-sm font-normal text-secondary-text",children:e("Status")}),(0,r.jsxs)(N.Ph,{value:null==i?void 0:i.status,onValueChange:e=>c("status",e),children:[(0,r.jsx)(N.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:(0,r.jsx)(N.ki,{placeholder:e("Status")})}),(0,r.jsxs)(N.Bw,{children:[(0,r.jsx)(N.Ql,{value:"pending",children:e("Pending")}),(0,r.jsx)(N.Ql,{value:"completed",children:e("Complete")}),(0,r.jsx)(N.Ql,{value:"failed",children:e("Failed")})]})]})]}),(0,r.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[(0,r.jsx)(b.Z,{className:"text-sm font-normal text-secondary-text",children:e("Date")}),(0,r.jsx)(g.M,{value:Object.prototype.hasOwnProperty.call(i,"date")?(0,j.Qc)(i.date,"yyyy-MM-dd",new Date):void 0,onChange:e=>{c("date",(0,p.WU)(e,"yyyy-MM-dd"))},className:"h-10",placeholderClassName:"text-secondary-text"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-stretch space-y-2",children:[(0,r.jsx)(y.z,{type:"button",onClick:()=>l(!1),className:"h-10",children:e("Done")}),(0,r.jsx)(y.z,{type:"button",variant:"outline",onClick:()=>{let e=new URLSearchParams;Object.keys(i).forEach(t=>e.delete(t)),o({}),a.replace("".concat(n,"?").concat(e.toString()))},className:"h-10",children:e("Clear Filter")})]})]})})]})}let P=new f.F;function R(){var e;let{t}=(0,x.$G)(),{defaultCurrency:n}=(0,u.T)(),g=(0,v.useSearchParams)(),[y,b]=h.useState(null!==(e=g.get("search"))&&void 0!==e?e:""),[w,N]=h.useState([]),j=(0,v.useRouter)(),S=(0,v.usePathname)(),{auth:R}=(0,c.a)(),{data:O,isLoading:A}=(0,d.Z)("/commissions/".concat(null==R?void 0:R.id,"?").concat(g.toString()));return(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-end sm:h-12",children:[(0,r.jsxs)("div",{className:"flex flex-1 flex-wrap items-center gap-4 sm:flex-initial sm:flex-row sm:flex-nowrap",children:[(0,r.jsx)(i.R,{value:y,onChange:e=>{e.preventDefault();let t=(0,f.w4)(e.target.value);b(e.target.value),j.replace("".concat(S,"?").concat(t.toString()))},iconPlacement:"end",placeholder:t("Search..."),containerClass:"w-full sm:max-w-56"}),(0,r.jsx)(C,{}),(0,r.jsx)(o._,{url:"/commissions/export/".concat(null==R?void 0:R.id,"?").concat(g.toString())})]}),(0,r.jsx)("div",{})]}),(0,r.jsx)(l.Z,{className:"my-4"}),(0,r.jsx)(a.Z,{data:O?null==O?void 0:O.map(e=>new m.E(e)):[],isLoading:A,sorting:w,setSorting:N,structure:[{id:"createdAt",header:t("Date"),cell:e=>{var t;let{row:n}=e;return(0,r.jsx)("span",{className:"whitespace-nowrap text-xs font-normal leading-5 text-foreground sm:text-sm",children:(null===(t=n.original)||void 0===t?void 0:t.createdAt)?(0,p.WU)(n.original.createdAt,"dd MMM yyyy"):"N/A"})}},{id:"status",header:t("Status"),cell:e=>{var n,a;let{row:i}=e;return(null===(n=i.original)||void 0===n?void 0:n.status)==="completed"?(0,r.jsx)(s.C,{variant:"success",children:t("Completed")}):(null===(a=i.original)||void 0===a?void 0:a.status)==="failed"?(0,r.jsx)(s.C,{variant:"destructive",children:t("Failed")}):(0,r.jsx)(s.C,{variant:"secondary",children:t("Pending")})}},{id:"amount",header:t("Amount sent"),cell:e=>{var t;let{row:a}=e;return(0,r.jsx)("span",{className:"text-xs font-semibold leading-4 text-foreground sm:text-sm",children:P.format(null===(t=a.original)||void 0===t?void 0:t.amount,n)})}},{id:"transaction.trxId",header:t("Trx ID"),cell:e=>{var t;let{row:n}=e;return(0,r.jsx)("span",{className:"text-xs font-normal leading-4 text-foreground sm:text-sm",children:null===(t=n.original)||void 0===t?void 0:t.transaction.trxId})}}]})]})})}},85487:function(e,t,n){"use strict";n.d(t,{Loader:function(){return o}});var r=n(57437),a=n(94508),i=n(43949);function o(e){let{title:t="Loading...",className:n}=e,{t:o}=(0,i.$G)();return(0,r.jsxs)("div",{className:(0,a.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-inherit",children:o(t)})]})}},62869:function(e,t,n){"use strict";n.d(t,{d:function(){return l},z:function(){return c}});var r=n(57437),a=n(37053),i=n(90535),o=n(2265),s=n(94508);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef((e,t)=>{let{className:n,variant:i,size:o,asChild:c=!1,...u}=e,d=c?a.g7:"button";return(0,r.jsx)(d,{className:(0,s.ZP)(l({variant:i,size:o,className:n})),ref:t,...u})});c.displayName="Button"},95186:function(e,t,n){"use strict";n.d(t,{I:function(){return o}});var r=n(57437),a=n(2265),i=n(94508);let o=a.forwardRef((e,t)=>{let{className:n,type:a,...o}=e;return(0,r.jsx)("input",{type:a,className:(0,i.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",n),ref:t,...o})});o.displayName="Input"},26815:function(e,t,n){"use strict";var r=n(57437),a=n(6394),i=n(90535),o=n(2265),s=n(94508);let l=(0,i.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)(a.f,{ref:t,className:(0,s.ZP)(l(),n),...i})});c.displayName=a.f.displayName,t.Z=c},3612:function(e,t,n){"use strict";n.d(t,{a:function(){return a}});var r=n(17062);let a=()=>{let e=(0,r.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},79981:function(e,t,n){"use strict";var r=n(78040),a=n(83464);t.Z=a.default.create({baseURL:r.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,n){"use strict";n.d(t,{rH:function(){return r},sp:function(){return a}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){"use strict";n.d(t,{F:function(){return u},Fg:function(){return m},Fp:function(){return c},Qp:function(){return f},ZP:function(){return s},fl:function(){return l},qR:function(){return d},w4:function(){return p}});var r=n(78040),a=n(61994),i=n(14438),o=n(53335);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,o.m6)((0,a.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>i.toast.success("Copied to clipboard!")).catch(()=>{i.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let a;let i=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:i,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let o=null!==(r=null===(n=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:i,s=a.format(e),l=s.substring(o.length).trim();return{currencyCode:i,currencySymbol:o,formattedAmount:s,amountText:l}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?a.set(r,e):a.delete(r),a}},12119:function(e,t,n){"use strict";Object.defineProperty(t,"$",{enumerable:!0,get:function(){return a}});let r=n(83079);function a(e){let{createServerReference:t}=n(6671);return t(e,r.callServer)}},25523:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(47043)._(n(2265)).default.createContext(null)},6394:function(e,t,n){"use strict";n.d(t,{f:function(){return s}});var r=n(2265),a=n(66840),i=n(57437),o=r.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var s=o},71599:function(e,t,n){"use strict";n.d(t,{z:function(){return o}});var r=n(2265),a=n(98575),i=n(61188),o=e=>{var t,n;let o,l;let{present:c,children:u}=e,d=function(e){var t,n;let[a,o]=r.useState(),l=r.useRef(null),c=r.useRef(e),u=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=s(l.current);u.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=l.current,n=c.current;if(n!==e){let r=u.current,a=s(t);e?f("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==a?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(a){var e;let t;let n=null!==(e=a.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=s(l.current).includes(e.animationName);if(e.target===a&&r&&(f("ANIMATION_END"),!c.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},i=e=>{e.target===a&&(u.current=s(l.current))};return a.addEventListener("animationstart",i),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{n.clearTimeout(t),a.removeEventListener("animationstart",i),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}f("ANIMATION_END")},[a,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(c),f="function"==typeof u?u({present:d.isPresent}):r.Children.only(u),m=(0,a.e)(d.ref,(o=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?f.ref:(o=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in o&&o.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof u||d.isPresent?r.cloneElement(f,{ref:m}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,27648,2901,85210,58939,98604,27443,56993,54712,92971,95030,1744],function(){return e(e.s=95578)}),_N_E=e.O()}]);