"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[227],{85487:function(e,t,r){r.d(t,{Loader:function(){return s}});var n=r(57437),a=r(94508),o=r(43949);function s(e){let{title:t="Loading...",className:r}=e,{t:s}=(0,o.$G)();return(0,n.jsxs)("div",{className:(0,a.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:s(t)})]})}},41062:function(e,t,r){r.d(t,{W:function(){return s}});var n=r(57437),a=r(94508),o=r(33145);function s(e){let{countryCode:t,className:r,url:s}=e;return t||s?(0,n.jsx)(o.default,{src:null!=s?s:"https://flagcdn.com/".concat(null==t?void 0:t.toLowerCase(),".svg"),alt:t,width:20,height:16,loading:"lazy",className:(0,a.ZP)("rounded-[2px]",r)}):null}},62869:function(e,t,r){r.d(t,{d:function(){return l},z:function(){return c}});var n=r(57437),a=r(37053),o=r(90535),s=r(2265),i=r(94508);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:o,size:s,asChild:c=!1,...d}=e,u=c?a.g7:"button";return(0,n.jsx)(u,{className:(0,i.ZP)(l({variant:o,size:s,className:r})),ref:t,...d})});c.displayName="Button"},23518:function(e,t,r){r.d(t,{di:function(){return p},e8:function(){return d},fu:function(){return f},mY:function(){return l},rb:function(){return u},sZ:function(){return c},zz:function(){return m}});var n=r(57437),a=r(46343),o=r(73247),s=r(2265);r(26110);var i=r(94508);let l=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(a.mY,{ref:t,className:(0,i.ZP)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",r),...o})});l.displayName=a.mY.displayName;let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[(0,n.jsx)(o.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),(0,n.jsx)(a.mY.Input,{ref:t,className:(0,i.ZP)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",r),...s})]})});c.displayName=a.mY.Input.displayName;let d=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(a.mY.List,{ref:t,className:(0,i.ZP)("max-h-[300px] overflow-y-auto overflow-x-hidden",r),...o})});d.displayName=a.mY.List.displayName;let u=s.forwardRef((e,t)=>(0,n.jsx)(a.mY.Empty,{ref:t,className:"py-6 text-center text-sm",...e}));u.displayName=a.mY.Empty.displayName;let f=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(a.mY.Group,{ref:t,className:(0,i.ZP)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",r),...o})});f.displayName=a.mY.Group.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(a.mY.Separator,{ref:t,className:(0,i.ZP)("-mx-1 h-px bg-border",r),...o})});m.displayName=a.mY.Separator.displayName;let p=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(a.mY.Item,{ref:t,className:(0,i.ZP)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",r),...o})});p.displayName=a.mY.Item.displayName},26110:function(e,t,r){r.d(t,{$N:function(){return g},Be:function(){return x},GG:function(){return u},Vq:function(){return l},cZ:function(){return m},fK:function(){return p},hg:function(){return c}});var n=r(57437),a=r(2265),o=r(49027),s=r(32489),i=r(94508);let l=o.fC,c=o.xz,d=o.h_,u=o.x8,f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.aV,{ref:t,className:(0,i.ZP)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...a})});f.displayName=o.aV.displayName;let m=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,n.jsxs)(d,{children:[(0,n.jsx)(f,{}),(0,n.jsxs)(o.VY,{ref:t,className:(0,i.ZP)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...l,children:[a,(0,n.jsxs)(o.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(s.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=o.VY.displayName;let p=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,i.ZP)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};p.displayName="DialogHeader";let g=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.Dx,{ref:t,className:(0,i.ZP)("text-lg font-semibold leading-none tracking-tight",r),...a})});g.displayName=o.Dx.displayName;let x=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.dk,{ref:t,className:(0,i.ZP)("text-sm text-muted-foreground",r),...a})});x.displayName=o.dk.displayName},95186:function(e,t,r){r.d(t,{I:function(){return s}});var n=r(57437),a=r(2265),o=r(94508);let s=a.forwardRef((e,t)=>{let{className:r,type:a,...s}=e;return(0,n.jsx)("input",{type:a,className:(0,o.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...s})});s.displayName="Input"},26815:function(e,t,r){var n=r(57437),a=r(6394),o=r(90535),s=r(2265),i=r(94508);let l=(0,o.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(a.f,{ref:t,className:(0,i.ZP)(l(),r),...o})});c.displayName=a.f.displayName,t.Z=c},40593:function(e,t,r){r.d(t,{F:function(){return c}});class n{constructor(e){var t;this.name=null==e?void 0:null===(t=e.name)||void 0===t?void 0:t.common,this.flags=null==e?void 0:e.flags,this.flag=null==e?void 0:e.flag,this.code={cca2:null==e?void 0:e.cca2,cca3:null==e?void 0:e.cca3,ccn3:null==e?void 0:e.ccn3},this.status=null==e?void 0:e.status}}var a=r(83464),o=r(14438),s=r(85323);let i=a.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),l="name,cca2,ccn3,cca3,status,flag,flags";function c(){let{data:e,isLoading:t,...r}=(0,s.ZP)("/all?fields=".concat(l),e=>i.get(e)),c=null==e?void 0:e.data,d=async(e,t)=>{try{let r=await i.get("/alpha/".concat(e.toLowerCase(),"?fields=").concat(l)),a=r.data?new n(r.data):null;t(a)}catch(e){a.default.isAxiosError(e)&&o.toast.error("Failed to fetch country")}};return{countries:c?c.map(e=>new n(e)):[],isLoading:t,getCountryByCode:d,...r}}},79981:function(e,t,r){var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){r.d(t,{F:function(){return d},Fg:function(){return m},Fp:function(){return c},Qp:function(){return f},ZP:function(){return i},fl:function(){return l},qR:function(){return u},w4:function(){return p}});var n=r(78040),a=r(61994),o=r(14438),s=r(53335);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,a.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>o.toast.success("Copied to clipboard!")).catch(()=>{o.toast.error("Failed to copy!")})};class d{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let o=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:o,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let s=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:o,i=a.format(e),l=i.substring(s.length).trim();return{currencyCode:o,currencySymbol:s,formattedAmount:i,amountText:l}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}}}]);