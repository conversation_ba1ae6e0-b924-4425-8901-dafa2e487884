(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[36984],{87485:function(e,t,r){Promise.resolve().then(r.bind(r,50452))},50452:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return F}});var n=r(57437),s=r(85487),a=r(41709),i=r(52323),l=r(78939),o=r(25429),d=r(6596),c=r(62869),u=r(15681),m=r(95186),f=r(26815),x=r(79981),p=r(97751);async function h(e){try{let t=new FormData;t.append("name",e.merchant_name),t.append("addressLine",e.street),t.append("countryCode",e.country),t.append("city",e.city),t.append("zipCode",e.zipCode),t.append("storeProfileImage",e.profile),t.append("email",e.merchant_email);let r=await x.Z.put("/merchants/update",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,p.B)(r)}catch(e){return(0,p.D)(e)}}var j=r(94508),v=r(13590),g=r(22291),b=r(2265),y=r(29501),N=r(43949),C=r(14438),w=r(2602),z=r(31229);let I=["image/png","image/jpg","image/jpeg","image/webp"],_=z.z.any().optional().refine(e=>!e||e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||I.includes(e.type),"File must be a png, jpg, jpeg, webp"),Z=z.z.object({profile:_,merchant_name:z.z.string({required_error:"Merchant name is required."}),merchant_email:z.z.string({required_error:"Merchant email is required."}),merchant_id:z.z.string({required_error:"Merchant ID is required."}),street:z.z.string({required_error:"Street is required"}),country:z.z.string({required_error:"Country is required"}),city:z.z.string({required_error:"City is required"}),zipCode:z.z.string({required_error:"Zip code is required"})});function S(e){let{data:t,isLoading:r}=e,[x,p]=(0,b.useTransition)(),{t:z}=(0,N.$G)(),I=(0,y.cI)({resolver:(0,v.F)(Z),defaultValues:{profile:"",merchant_name:"John doe",merchant_email:"<EMAIL>",merchant_id:"24223423422",street:"",country:"",city:"",zipCode:""}});return b.useEffect(()=>{if(t){var e,r,n,s;I.reset({merchant_name:t.name,merchant_email:t.email,merchant_id:t.merchantId,street:null===(e=t.address)||void 0===e?void 0:e.addressLine,country:null===(r=t.address)||void 0===r?void 0:r.countryCode,city:null===(n=t.address)||void 0===n?void 0:n.city,zipCode:null===(s=t.address)||void 0===s?void 0:s.zipCode})}},[r]),(0,n.jsx)(u.l0,{...I,children:(0,n.jsx)("form",{onSubmit:I.handleSubmit(e=>{p(async()=>{let t=await h(e);t&&t.status?((0,w.j)("/merchants/detail"),C.toast.success(t.message)):C.toast.error(z(t.message))})}),className:"rounded-xl border border-border bg-background",children:(0,n.jsxs)(d.Qd,{value:"STORE_PROFILE",className:"border-none px-4 py-0",children:[(0,n.jsx)(d.o4,{className:"py-6 hover:no-underline",children:(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:z("Store Profile")})}),(0,n.jsxs)(d.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:[(0,n.jsx)(u.Wi,{control:I.control,name:"profile",render:e=>{let{field:r}=e;return(0,n.jsxs)(u.xJ,{children:[(0,n.jsx)(u.lX,{children:z("Store profile picture")}),(0,n.jsx)(u.NI,{children:(0,n.jsx)(l.S,{defaultValue:(0,j.qR)(null==t?void 0:t.storeProfileImage),id:"documentFrontSideFile",onChange:e=>r.onChange(e),className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,n.jsx)(o.X,{}),(0,n.jsx)("p",{className:"text-sm font-normal text-primary",children:z("Upload photo")})]})})}),(0,n.jsx)(u.zG,{})]})}}),(0,n.jsx)(u.Wi,{control:I.control,name:"merchant_name",render:e=>{let{field:t}=e;return(0,n.jsxs)(u.xJ,{children:[(0,n.jsx)(u.lX,{children:z("Merchant name")}),(0,n.jsx)(u.NI,{children:(0,n.jsx)(m.I,{type:"text",placeholder:z("Merchant name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,n.jsx)(u.zG,{})]})}}),(0,n.jsx)(u.Wi,{control:I.control,name:"merchant_email",render:e=>{let{field:t}=e;return(0,n.jsxs)(u.xJ,{children:[(0,n.jsx)(u.lX,{children:z("Merchant email")}),(0,n.jsx)(u.NI,{children:(0,n.jsx)(m.I,{type:"email",placeholder:z("Enter your email"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,n.jsx)(u.zG,{})]})}}),(0,n.jsx)(u.Wi,{control:I.control,name:"merchant_id",render:e=>{let{field:t}=e;return(0,n.jsxs)(u.xJ,{children:[(0,n.jsx)(u.lX,{children:z("Merchant ID")}),(0,n.jsx)(u.NI,{children:(0,n.jsx)(m.I,{type:"text",disabled:!0,placeholder:z("Enter Merchant ID"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,n.jsx)(u.zG,{})]})}}),(0,n.jsx)(f.Z,{children:z("Merchant address")}),(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-2.5",children:[(0,n.jsx)(u.Wi,{control:I.control,name:"street",render:e=>{let{field:t}=e;return(0,n.jsxs)(u.xJ,{className:"col-span-12",children:[(0,n.jsx)(u.NI,{children:(0,n.jsx)(m.I,{type:"text",placeholder:z("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,n.jsx)(u.zG,{})]})}}),(0,n.jsx)(u.Wi,{control:I.control,name:"country",render:e=>{let{field:t}=e;return(0,n.jsxs)(u.xJ,{className:"col-span-12",children:[(0,n.jsx)(u.NI,{children:(0,n.jsx)(i.g,{defaultCountry:t.value,onSelectChange:e=>t.onChange(e.code.cca2)})}),(0,n.jsx)(u.zG,{})]})}}),(0,n.jsx)(u.Wi,{control:I.control,name:"city",render:e=>{let{field:t}=e;return(0,n.jsxs)(u.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(u.NI,{children:(0,n.jsx)("div",{className:"relative flex items-center gap-2.5",children:(0,n.jsx)(m.I,{type:"text",placeholder:z("City"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})})}),(0,n.jsx)(u.zG,{})]})}}),(0,n.jsx)(u.Wi,{control:I.control,name:"zipCode",render:e=>{let{field:t}=e;return(0,n.jsxs)(u.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(u.NI,{children:(0,n.jsx)(m.I,{type:"text",placeholder:z("Zip code"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,n.jsx)(u.zG,{})]})}})]}),(0,n.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,n.jsxs)(c.z,{disabled:x,children:[(0,n.jsxs)(a.J,{condition:!x,children:[z("Save"),(0,n.jsx)(g.Z,{size:20})]}),(0,n.jsx)(a.J,{condition:x,children:(0,n.jsx)(s.Loader,{title:z("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}var P=r(1409);function F(){let{t:e}=(0,N.$G)(),{data:t,isLoading:r,error:a}=(0,P.f)();return!t&&a?(0,n.jsx)("div",{className:"w-full bg-danger py-2.5 text-danger-foreground",children:(0,n.jsx)("p",{children:e("We encountered an issue while retrieving the requested data. Please try again later or contact support if the problem persists.")})}):!t&&r?(0,n.jsx)("div",{className:"flex w-full items-center justify-center py-10",children:(0,n.jsx)(s.Loader,{})}):(0,n.jsx)(d.UQ,{type:"multiple",defaultValue:["STORE_PROFILE"],children:(0,n.jsx)("div",{className:"flex flex-col gap-4",children:(0,n.jsx)(S,{data:t,isLoading:r})})})}},41709:function(e,t,r){"use strict";function n(e){let{condition:t,children:r}=e;return t?r:null}r.d(t,{J:function(){return n}}),r(2265)},52323:function(e,t,r){"use strict";r.d(t,{g:function(){return f}});var n=r(57437),s=r(2265),a=r(85487),i=r(41062),l=r(23518),o=r(57054),d=r(40593),c=r(94508),u=r(36887),m=r(43949);function f(e){var t,r;let{allCountry:f=!1,defaultValue:x,defaultCountry:p,onSelectChange:h,disabled:j=!1,triggerClassName:v,arrowClassName:g,flagClassName:b,display:y,placeholderClassName:N,align:C="start",side:w="bottom"}=e,{t:z}=(0,m.$G)(),{countries:I,getCountryByCode:_,isLoading:Z}=(0,d.F)(),[S,P]=s.useState(!1),[F,R]=s.useState(x);return s.useEffect(()=>{x&&R(x)},[x]),s.useEffect(()=>{(async()=>{p&&await _(p,e=>{e&&(R(e),h(e))})})()},[p]),(0,n.jsxs)(o.J2,{open:S,onOpenChange:P,children:[(0,n.jsxs)(o.xo,{disabled:j,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",v),children:[F?(0,n.jsx)("div",{className:"flex flex-1 items-center",children:(0,n.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,n.jsx)(i.W,{className:b,countryCode:(null===(t=F.code)||void 0===t?void 0:t.cca2)==="*"?"UN":null===(r=F.code)||void 0===r?void 0:r.cca2}),void 0!==y?y(F):(0,n.jsx)("span",{children:F.name})]})}):(0,n.jsx)("span",{className:(0,c.ZP)("text-placeholder",N),children:z("Select country")}),(0,n.jsx)(u.Z,{className:(0,c.ZP)("size-6",g)})]}),(0,n.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:C,side:w,children:(0,n.jsxs)(l.mY,{children:[(0,n.jsx)(l.sZ,{placeholder:z("Search...")}),(0,n.jsx)(l.e8,{children:(0,n.jsxs)(l.fu,{children:[Z&&(0,n.jsx)(a.Loader,{}),f&&(0,n.jsxs)(l.di,{value:z("All countries"),onSelect:()=>{R({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),h({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),P(!1)},children:[(0,n.jsx)(i.W,{countryCode:"UN"}),(0,n.jsx)("span",{className:"pl-1.5",children:z("All countries")})]}),null==I?void 0:I.map(e=>"officially-assigned"===e.status?(0,n.jsxs)(l.di,{value:e.name,onSelect:()=>{R(e),h(e),P(!1)},children:[(0,n.jsx)(i.W,{countryCode:e.code.cca2}),(0,n.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},78939:function(e,t,r){"use strict";r.d(t,{S:function(){return o}});var n=r(57437),s=r(94508),a=r(33145),i=r(2265),l=r(85598);function o(e){let{defaultValue:t,onChange:r,className:o,children:d,disabled:c=!1,id:u}=e,[m,f]=i.useState(t);i.useEffect(()=>{f(t)},[t]);let{getRootProps:x,getInputProps:p}=(0,l.uI)({onDrop:e=>{let t=null==e?void 0:e[0];t&&(r(t),f(URL.createObjectURL(t)))},disabled:c});return(0,n.jsxs)("div",{...x({className:(0,s.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o)}),children:[!!m&&(0,n.jsx)(a.default,{src:m,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,n.jsx)("input",{id:u,...p()}),!m&&(0,n.jsx)("div",{children:d})]})}},25429:function(e,t,r){"use strict";r.d(t,{X:function(){return a}});var n=r(57437),s=r(94508);function a(e){let{className:t}=e;return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,s.ZP)("fill-primary",t),children:[(0,n.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},6596:function(e,t,r){"use strict";r.d(t,{Qd:function(){return d},UQ:function(){return o},o4:function(){return c},vF:function(){return u}});var n=r(57437),s=r(13134),a=r(2265),i=r(94508),l=r(36887);let o=s.fC,d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.ck,{ref:t,className:(0,i.ZP)("border-b",r),...a})});d.displayName="AccordionItem";let c=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,n.jsx)(s.h4,{className:"flex",children:(0,n.jsxs)(s.xz,{ref:t,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",r),...o,children:[a,(0,n.jsx)(l.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=s.xz.displayName;let u=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,n.jsx)(s.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...l,children:(0,n.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",r),children:a})})});u.displayName=s.VY.displayName},15681:function(e,t,r){"use strict";r.d(t,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return p},xJ:function(){return x},zG:function(){return j}});var n=r(57437),s=r(37053),a=r(2265),i=r(29501),l=r(26815),o=r(94508);let d=i.RV,c=a.createContext({}),u=e=>{let{...t}=e;return(0,n.jsx)(c.Provider,{value:{name:t.name},children:(0,n.jsx)(i.Qr,{...t})})},m=()=>{let e=a.useContext(c),t=a.useContext(f),{getFieldState:r,formState:n}=(0,i.Gc)(),s=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...s}},f=a.createContext({}),x=a.forwardRef((e,t)=>{let{className:r,...s}=e,i=a.useId();return(0,n.jsx)(f.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",r),...s})})});x.displayName="FormItem";let p=a.forwardRef((e,t)=>{let{className:r,required:s,...a}=e,{error:i,formItemId:d}=m();return(0,n.jsx)("span",{children:(0,n.jsx)(l.Z,{ref:t,className:(0,o.ZP)(i&&"text-base font-medium text-destructive",r),htmlFor:d,...a})})});p.displayName="FormLabel";let h=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:i,formDescriptionId:l,formMessageId:o}=m();return(0,n.jsx)(s.g7,{ref:t,id:i,"aria-describedby":a?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!a,...r})});h.displayName="FormControl",a.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:a}=m();return(0,n.jsx)("p",{ref:t,id:a,className:(0,o.ZP)("text-sm text-muted-foreground",r),...s})}).displayName="FormDescription";let j=a.forwardRef((e,t)=>{let{className:r,children:s,...a}=e,{error:i,formMessageId:l}=m(),d=i?String(null==i?void 0:i.message):s;return d?(0,n.jsx)("p",{ref:t,id:l,className:(0,o.ZP)("text-sm font-medium text-destructive",r),...a,children:d}):null});j.displayName="FormMessage"},57054:function(e,t,r){"use strict";r.d(t,{J2:function(){return l},xo:function(){return o},yk:function(){return d}});var n=r(57437),s=r(2265),a=r(27312),i=r(94508);let l=a.fC,o=a.xz,d=s.forwardRef((e,t)=>{let{className:r,align:s="center",sideOffset:l=4,...o}=e;return(0,n.jsx)(a.h_,{children:(0,n.jsx)(a.VY,{ref:t,align:s,sideOffset:l,className:(0,i.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})})});d.displayName=a.VY.displayName},97751:function(e,t,r){"use strict";r.d(t,{B:function(){return s},D:function(){return a}});var n=r(43577);function s(e){var t,r,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function a(e){let t=500,r="Internal Server Error",s="An unknown error occurred";if((0,n.IZ)(e)){var a,i,l,o,d,c,u,m,f,x,p,h;t=null!==(f=null===(a=e.response)||void 0===a?void 0:a.status)&&void 0!==f?f:500,r=null!==(x=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==x?x:"Internal Server Error",s=null!==(h=null!==(p=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(l=o[0])||void 0===l?void 0:l.message)&&void 0!==p?p:null===(m=e.response)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:u.message)&&void 0!==h?h:e.message}else e instanceof Error&&(s=e.message);return{statusCode:t,statusText:r,status:!1,message:s,data:void 0,error:e}}},1409:function(e,t,r){"use strict";r.d(t,{f:function(){return a}});var n=r(79981),s=r(85323);function a(){let{data:e,error:t,isLoading:r}=(0,s.ZP)("/merchants/detail",e=>n.Z.get(e));return{error:t,data:null==e?void 0:e.data,isLoading:r}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,38658,42592,85598,98484,227,92971,95030,1744],function(){return e(e.s=87485)}),_N_E=e.O()}]);