(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5323],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},6062:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>w,default:()=>C});var n,a={};r.r(a),r.d(a,{AppRouter:()=>m.WY,ClientPageRoot:()=>m.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>m.yO,NotFoundBoundary:()=>m.O4,Postpone:()=>m.hQ,RenderFromTemplateContext:()=>m.b5,__next_app__:()=>g,actionAsyncStorage:()=>m.Wz,createDynamicallyTrackedSearchParams:()=>m.rL,createUntrackedSearchParams:()=>m.S5,decodeAction:()=>m.Hs,decodeFormState:()=>m.dH,decodeReply:()=>m.kf,originalPathname:()=>x,pages:()=>h,patchFetch:()=>m.XH,preconnect:()=>m.$P,preloadFont:()=>m.C5,preloadStyle:()=>m.oH,renderToReadableStream:()=>m.aW,requestAsyncStorage:()=>m.Fg,routeModule:()=>f,serverHooks:()=>m.GP,staticGenerationAsyncStorage:()=>m.AT,taintObjectReference:()=>m.nr,tree:()=>p}),r(67206);var s=r(79319),c=r(20518),l=r(61902),o=r(62042),i=r(44630),d=r(44828),u=r(65505),m=r(13839);let p=["",{children:["(protected)",{admin:["children",{children:["agents",{children:["[userId]",{children:["[agentId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,17907)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,12771)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,12885)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,29670)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,74030)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],h=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\page.tsx"],x="/(protected)/@admin/agents/[userId]/[agentId]/page",g={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/agents/[userId]/[agentId]/page",pathname:"/agents/[userId]/[agentId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var j=r(69094),v=r(5787),k=r(90527);let E=e=>e?JSON.parse(e):void 0,b=self.__BUILD_MANIFEST,y=E(self.__REACT_LOADABLE_MANIFEST),N=null==(n=self.__RSC_MANIFEST)?void 0:n["/(protected)/@admin/agents/[userId]/[agentId]/page"],S=E(self.__RSC_SERVER_MANIFEST),L=E(self.__NEXT_FONT_MANIFEST),I=E(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];N&&S&&(0,v.Mo)({clientReferenceManifest:N,serverActionsManifest:S,serverModuleMap:(0,k.w)({serverActionsManifest:S,pageName:"/(protected)/@admin/agents/[userId]/[agentId]/page"})});let M=(0,c.d)({pagesType:j.s.APP,dev:!1,page:"/(protected)/@admin/agents/[userId]/[agentId]/page",appMod:null,pageMod:a,errorMod:null,error500Mod:null,Document:null,buildManifest:b,renderToHTML:o.f,reactLoadableManifest:y,clientReferenceManifest:N,serverActionsManifest:S,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:L,incrementalCacheHandler:null,interceptionRouteRewrites:I}),w=a;function C(e){return(0,s.C)({...e,IncrementalCache:l.k,handler:M})}},98254:(e,t,r)=>{Promise.resolve().then(r.bind(r,29431))},59047:(e,t,r)=>{Promise.resolve().then(r.bind(r,29572))},29431:(e,t,r)=>{"use strict";r.d(t,{Tabbar:()=>v});var n=r(60926),a=r(14579),s=r(30417),c=r(89551),l=r(53042),o=r(23181),i=r(44788),d=r(38071),u=r(28531),m=r(5764),p=r(47020),h=r(737),x=r(64947),g=r(39228),f=r(32167),j=r(91500);function v(){let e=(0,x.UO)(),t=(0,x.jD)(),r=(0,x.tv)(),v=(0,x.lr)(),{t:k}=(0,g.$G)(),E=[{title:k("Account Details"),icon:(0,n.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}?${v.toString()}`,id:"__DEFAULT__"},{title:k("Charges/Commissions"),icon:(0,n.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/commissions?${v.toString()}`,id:"commissions"},{title:k("Fees"),icon:(0,n.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/fees?${v.toString()}`,id:"fees"},{title:k("Transactions"),icon:(0,n.jsx)(i.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/transactions?${v.toString()}`,id:"transactions"},{title:k("KYC"),icon:(0,n.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/kyc?${v.toString()}`,id:"kyc"},{title:k("Permissions"),icon:(0,n.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/permissions?${v.toString()}`,id:"permissions"},{title:k("Send Email"),icon:(0,n.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/send-email?${v.toString()}`,id:"send-email"}];return(0,n.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,n.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,n.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,n.jsx)("li",{children:(0,n.jsxs)(h.Z,{href:"/agents/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,n.jsx)(p.Z,{className:"size-4 sm:size-6"}),k("Back")]})}),(0,n.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",v.get("name")," "]}),(0,n.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",k("Agents")," #",e.agentId]})]}),(0,n.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,n.jsx)("span",{children:k("Active")}),(0,n.jsx)(s.Z,{defaultChecked:"1"===v.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:n=>{f.toast.promise((0,c.z)(e.userId),{loading:k("Loading..."),success:a=>{if(!a.status)throw Error(a.message);let s=new URLSearchParams(v);return(0,j.j)(`/admin/agents/${e.agentId}`),s.set("active",n?"1":"0"),r.push(`${t}?${s.toString()}`),a.message},error:e=>e.message})}})]})]}),(0,n.jsx)(a.a,{tabs:E})]})}},83941:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var n=r(60926),a=r(58387),s=r(43291),c=r(23181),l=r(64947),o=r(39228);function i(){let e=(0,l.UO)(),{t}=(0,o.$G)(),{data:r,isLoading:i}=(0,s.d)(`/commissions/total-pending/${e.userId}`);return(0,n.jsxs)("div",{className:"col-span-12 inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default md:col-span-6",children:[(0,n.jsx)("div",{className:"flex h-[54px] w-[54px] items-center justify-center rounded-full bg-muted",children:(0,n.jsx)(c.Z,{variant:"Bulk",size:34})}),(0,n.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,n.jsx)(a.J,{condition:i,children:(0,n.jsx)("h1",{children:"0.00"})}),(0,n.jsx)(a.J,{condition:!i,children:(0,n.jsx)("h1",{children:`${r?.data?.total??"0.00"} ${r?.data?.currency}`})}),(0,n.jsx)("span",{className:"block text-xs font-normal leading-4",children:t("Total Commission")})]})]})}},29572:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eB});var n=r(60926),a=r(29411),s=r(59571),c=r(43291),l=r(65091),o=r(75638),i=r(90543),d=r(18825),u=r(66277),m=r(61394),p=r(29220),h=r(31036),x=r.n(h),g=["variant","color","size"],f=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"m18.02 12.33-1.22-1.22a1.39 1.39 0 0 1-.47-1.03c-.02-.45.16-.9.49-1.23l1.2-1.2c1.04-1.04 1.43-2.04 1.1-2.83-.32-.78-1.31-1.21-2.77-1.21H5.9v-.86c0-.41-.34-.75-.75-.75s-.75.34-.75.75v18.5c0 .41.34.75.75.75s.75-.34.75-.75v-4.88h10.45c1.44 0 2.41-.44 2.74-1.23.33-.79-.05-1.78-1.07-2.81Z",fill:t}))},j=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M5.15 2v20M16.35 4c2.7 0 3.3 1.5 1.4 3.4l-1.2 1.2c-.8.8-.8 2.1 0 2.8l1.2 1.2c1.9 1.9 1.2 3.4-1.4 3.4H5.15M5.15 4H12",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},v=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M5.15 22c-.41 0-.75-.34-.75-.75V2.75c0-.41.34-.75.75-.75s.75.34.75.75v18.5c0 .41-.34.75-.75.75Z",fill:t}),p.createElement("path",{opacity:".4",d:"m18.02 12.33-1.22-1.22a1.39 1.39 0 0 1-.47-1.03c-.02-.45.16-.9.49-1.23l1.2-1.2c1.04-1.04 1.43-2.04 1.1-2.83-.32-.78-1.31-1.21-2.77-1.21H5.15c-.21.01-.38.18-.38.39v12c0 .21.17.38.38.38h11.2c1.44 0 2.41-.44 2.74-1.23.33-.8-.05-1.79-1.07-2.82Z",fill:t}))},k=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M5.15 2v20M5.15 4h11.2c2.7 0 3.3 1.5 1.4 3.4l-1.2 1.2c-.8.8-.8 2.1 0 2.8l1.2 1.2c1.9 1.9 1.2 3.4-1.4 3.4H5.15",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},E=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M5.15 22.75c-.41 0-.75-.34-.75-.75V2c0-.41.34-.75.75-.75s.75.34.75.75v20c0 .41-.34.75-.75.75Z",fill:t}),p.createElement("path",{d:"M16.35 16.75H5.15c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h11.2c1.09 0 1.6-.29 1.7-.54.1-.25-.05-.81-.83-1.58l-1.2-1.2c-.49-.43-.79-1.08-.82-1.8-.03-.76.27-1.51.82-2.06l1.2-1.2c.74-.74.97-1.34.86-1.6-.11-.26-.68-.52-1.73-.52H5.15a.749.749 0 1 1 0-1.5h11.2c2.19 0 2.89.91 3.12 1.45.22.54.37 1.68-1.19 3.24l-1.2 1.2c-.25.25-.39.6-.38.95.01.3.13.57.34.76l1.24 1.23c1.53 1.53 1.38 2.67 1.16 3.22-.23.53-.94 1.45-3.09 1.45Z",fill:t}))},b=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M5.15 2v20",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),p.createElement("path",{opacity:".34",d:"M5.15 4h11.2c2.7 0 3.3 1.5 1.4 3.4l-1.2 1.2c-.8.8-.8 2.1 0 2.8l1.2 1.2c1.9 1.9 1.2 3.4-1.4 3.4H5.15",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}))},y=function(e,t){switch(e){case"Bold":return p.createElement(f,{color:t});case"Broken":return p.createElement(j,{color:t});case"Bulk":return p.createElement(v,{color:t});case"Linear":default:return p.createElement(k,{color:t});case"Outline":return p.createElement(E,{color:t});case"TwoTone":return p.createElement(b,{color:t})}},N=(0,p.forwardRef)(function(e,t){var r=e.variant,n=e.color,a=e.size,s=(0,m._)(e,g);return p.createElement("svg",(0,m.a)({},s,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),y(r,n))});N.propTypes={variant:x().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:x().string,size:x().oneOfType([x().string,x().number])},N.defaultProps={variant:"Linear",color:"currentColor",size:"24"},N.displayName="Flag";var S=["variant","color","size"],L=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"m21.25 18.47-1.65.39c-.37.09-.66.37-.74.74l-.35 1.47c-.19.8-1.21 1.05-1.74.42l-2.99-3.44a.499.499 0 0 1 .25-.81 8.492 8.492 0 0 0 4.53-2.83c.19-.23.53-.26.74-.05l2.22 2.22c.76.76.49 1.71-.27 1.89ZM2.7 18.47l1.65.39c.37.09.66.37.74.74l.35 1.47c.19.8 1.21 1.05 1.74.42l2.99-3.44c.24-.28.11-.72-.25-.81a8.492 8.492 0 0 1-4.53-2.83.499.499 0 0 0-.74-.05l-2.22 2.22c-.76.76-.49 1.71.27 1.89ZM12 2C8.13 2 5 5.13 5 9c0 1.45.43 2.78 1.17 3.89a6.985 6.985 0 0 0 4.78 3.02c.34.06.69.09 1.05.09.36 0 .71-.03 1.05-.09 1.99-.29 3.7-1.42 4.78-3.02A6.968 6.968 0 0 0 19 9c0-3.87-3.13-7-7-7Zm3.06 6.78-.83.83c-.14.14-.22.41-.17.61l.24 1.03c.19.81-.24 1.13-.96.7l-1-.59a.701.701 0 0 0-.66 0l-1 .59c-.72.42-1.15.11-.96-.7l.24-1.03c.04-.19-.03-.47-.17-.61l-.85-.83c-.49-.49-.33-.98.35-1.09l1.07-.18c.18-.03.39-.19.47-.35l.59-1.18c.32-.64.84-.64 1.16 0l.59 1.18c.08.16.29.32.48.35l1.07.18c.67.11.83.6.34 1.09Z",fill:t}))},I=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M16.25 3.44A6.986 6.986 0 0 1 19 9c0 1.45-.43 2.78-1.17 3.89a6.985 6.985 0 0 1-4.78 3.02c-.34.06-.69.09-1.05.09-.36 0-.71-.03-1.05-.09a6.985 6.985 0 0 1-4.78-3.02A6.968 6.968 0 0 1 5 9c0-3.87 3.13-7 7-7",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),p.createElement("path",{d:"m21.25 18.468-1.65.39c-.37.09-.66.37-.74.74l-.35 1.47a1 1 0 0 1-1.74.41L12 15.998l-4.77 5.49a1 1 0 0 1-1.74-.41l-.35-1.47a.996.996 0 0 0-.74-.74l-1.65-.39a1.003 1.003 0 0 1-.48-1.68l3.9-3.9a6.986 6.986 0 0 0 4.78 3.02c.34.06.69.09 1.05.09.36 0 .71-.03 1.05-.09 1.99-.29 3.7-1.42 4.78-3.02l3.9 3.9c.55.54.28 1.49-.48 1.67ZM12.58 5.98l.59 1.18c.08.16.29.32.48.35l1.07.18c.68.11.84.61.35 1.1l-.83.83c-.14.14-.22.41-.17.61l.24 1.03c.19.81-.24 1.13-.96.7l-1-.59a.701.701 0 0 0-.66 0l-1 .59c-.72.42-1.15.11-.96-.7l.24-1.03c.04-.19-.03-.47-.17-.61l-.83-.83c-.49-.49-.33-.98.35-1.1l1.07-.18c.18-.03.39-.19.47-.35l.59-1.18c.29-.64.81-.64 1.13 0Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},M=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{opacity:".4",d:"M19 9c0 1.45-.43 2.78-1.17 3.89a6.985 6.985 0 0 1-4.78 3.02c-.34.06-.69.09-1.05.09-.36 0-.71-.03-1.05-.09a6.985 6.985 0 0 1-4.78-3.02A6.968 6.968 0 0 1 5 9c0-3.87 3.13-7 7-7s7 3.13 7 7Z",fill:t}),p.createElement("path",{d:"m21.25 18.468-1.65.39c-.37.09-.66.37-.74.74l-.35 1.47a1 1 0 0 1-1.74.41L12 15.998l-4.77 5.49a1 1 0 0 1-1.74-.41l-.35-1.47a.996.996 0 0 0-.74-.74l-1.65-.39a1.003 1.003 0 0 1-.48-1.68l3.9-3.9a6.986 6.986 0 0 0 4.78 3.02c.34.06.69.09 1.05.09.36 0 .71-.03 1.05-.09 1.99-.29 3.7-1.42 4.78-3.02l3.9 3.9c.55.54.28 1.49-.48 1.67ZM12.58 5.98l.59 1.18c.08.16.29.32.48.35l1.07.18c.68.11.84.61.35 1.1l-.83.83c-.14.14-.22.41-.17.61l.24 1.03c.19.81-.24 1.13-.96.7l-1-.59a.701.701 0 0 0-.66 0l-1 .59c-.72.42-1.15.11-.96-.7l.24-1.03c.04-.19-.03-.47-.17-.61l-.83-.83c-.49-.49-.33-.98.35-1.1l1.07-.18c.18-.03.39-.19.47-.35l.59-1.18c.29-.64.81-.64 1.13 0Z",fill:t}))},w=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M19 9c0 1.45-.43 2.78-1.17 3.89a6.985 6.985 0 0 1-4.78 3.02c-.34.06-.69.09-1.05.09-.36 0-.71-.03-1.05-.09a6.985 6.985 0 0 1-4.78-3.02A6.968 6.968 0 0 1 5 9c0-3.87 3.13-7 7-7s7 3.13 7 7Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),p.createElement("path",{d:"m21.25 18.47-1.65.39c-.37.09-.66.37-.74.74l-.35 1.47a1 1 0 0 1-1.74.41L12 16l-4.77 5.49a1 1 0 0 1-1.74-.41l-.35-1.47a.996.996 0 0 0-.74-.74l-1.65-.39a1.003 1.003 0 0 1-.48-1.68l3.9-3.9a6.985 6.985 0 0 0 4.78 3.02c.34.06.69.09 1.05.09.36 0 .71-.03 1.05-.09 1.99-.29 3.7-1.42 4.78-3.02l3.9 3.9c.55.54.28 1.49-.48 1.67ZM12.58 5.98l.59 1.18c.08.16.29.32.48.35l1.07.18c.68.11.84.61.35 1.1l-.83.83c-.14.14-.22.41-.17.61l.24 1.03c.19.81-.24 1.13-.96.7l-1-.59a.701.701 0 0 0-.66 0l-1 .59c-.72.42-1.15.11-.96-.7l.24-1.03c.04-.19-.03-.47-.17-.61l-.83-.83c-.49-.49-.33-.98.35-1.1l1.07-.18c.18-.03.39-.19.47-.35l.59-1.18c.29-.64.81-.64 1.13 0Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},C=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M12 16.75c-.4 0-.8-.03-1.18-.1-2.12-.31-4.05-1.53-5.27-3.34A7.767 7.767 0 0 1 4.25 9c0-4.27 3.48-7.75 7.75-7.75S19.75 4.73 19.75 9c0 1.54-.45 3.03-1.3 4.31a7.8 7.8 0 0 1-5.3 3.35c-.35.06-.75.09-1.15.09Zm0-14c-3.45 0-6.25 2.8-6.25 6.25 0 1.25.36 2.45 1.04 3.47a6.254 6.254 0 0 0 4.26 2.69c.64.11 1.27.11 1.86 0 1.75-.25 3.3-1.24 4.29-2.7a6.232 6.232 0 0 0 1.04-3.47c.01-3.44-2.79-6.24-6.24-6.24Z",fill:t}),p.createElement("path",{d:"M6.47 22.588c-.14 0-.27-.02-.41-.05-.65-.15-1.15-.65-1.3-1.3l-.35-1.47a.261.261 0 0 0-.19-.19l-1.65-.39a1.74 1.74 0 0 1-1.28-1.22c-.17-.61 0-1.27.45-1.72l3.9-3.9c.16-.16.38-.24.6-.22.22.02.42.14.55.33.99 1.46 2.54 2.45 4.27 2.7.64.11 1.27.11 1.86 0 1.75-.25 3.3-1.24 4.29-2.7.12-.19.33-.31.55-.33.22-.02.44.06.6.22l3.9 3.9c.45.45.62 1.11.45 1.72a1.74 1.74 0 0 1-1.28 1.22l-1.65.39c-.09.02-.16.09-.19.19l-.35 1.47c-.15.65-.65 1.15-1.3 1.3-.65.16-1.32-.07-1.74-.58l-4.2-4.83-4.2 4.84c-.34.4-.82.62-1.33.62Zm-.38-8.56-3.29 3.29c-.09.09-.08.19-.06.25.01.05.06.15.18.17l1.65.39c.65.15 1.15.65 1.3 1.3l.35 1.47c.03.13.13.17.19.19.06.01.16.02.25-.08l3.83-4.41a7.768 7.768 0 0 1-4.4-2.57Zm7.42 2.56 3.83 4.4c.09.11.2.11.26.09.06-.01.15-.06.19-.19l.35-1.47c.15-.65.65-1.15 1.3-1.3l1.65-.39c.12-.03.17-.12.18-.17.02-.05.03-.16-.06-.25l-3.29-3.29a7.793 7.793 0 0 1-4.41 2.57Z",fill:t}),p.createElement("path",{d:"M13.891 12.888c-.26 0-.57-.07-.94-.29l-.95-.57-.95.56c-.87.52-1.44.22-1.65.07-.21-.15-.66-.6-.43-1.59l.24-1.03-.8-.74c-.44-.44-.6-.97-.45-1.45.15-.48.59-.82 1.21-.92l1.07-.18.51-1.12c.29-.57.74-.89 1.25-.89s.97.33 1.25.9l.59 1.18.99.12c.61.1 1.05.44 1.21.92.15.48-.01 1.01-.45 1.45l-.83.83.26.93c.23.99-.22 1.44-.43 1.59-.11.09-.35.23-.7.23Zm-4.28-4.5.69.69c.32.32.48.86.38 1.3l-.19.8.8-.47c.43-.25 1.01-.25 1.43 0l.8.47-.18-.8c-.1-.45.05-.98.37-1.3l.69-.69-.87-.15c-.42-.07-.84-.38-1.03-.76l-.5-.98-.5 1c-.18.37-.6.69-1.02.76l-.87.13Z",fill:t}))},Z=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M19 9c0 1.45-.43 2.78-1.17 3.89a6.985 6.985 0 0 1-4.78 3.02c-.34.06-.69.09-1.05.09-.36 0-.71-.03-1.05-.09a6.985 6.985 0 0 1-4.78-3.02A6.968 6.968 0 0 1 5 9c0-3.87 3.13-7 7-7s7 3.13 7 7Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),p.createElement("path",{opacity:".4",d:"m21.25 18.468-1.65.39c-.37.09-.66.37-.74.74l-.35 1.47a1 1 0 0 1-1.74.41L12 15.998l-4.77 5.49a1 1 0 0 1-1.74-.41l-.35-1.47a.996.996 0 0 0-.74-.74l-1.65-.39a1.003 1.003 0 0 1-.48-1.68l3.9-3.9a6.986 6.986 0 0 0 4.78 3.02c.34.06.69.09 1.05.09.36 0 .71-.03 1.05-.09 1.99-.29 3.7-1.42 4.78-3.02l3.9 3.9c.55.54.28 1.49-.48 1.67ZM12.58 5.98l.59 1.18c.08.16.29.32.48.35l1.07.18c.68.11.84.61.35 1.1l-.83.83c-.14.14-.22.41-.17.61l.24 1.03c.19.81-.24 1.13-.96.7l-1-.59a.701.701 0 0 0-.66 0l-1 .59c-.72.42-1.15.11-.96-.7l.24-1.03c.04-.19-.03-.47-.17-.61l-.83-.83c-.49-.49-.33-.98.35-1.1l1.07-.18c.18-.03.39-.19.47-.35l.59-1.18c.29-.64.81-.64 1.13 0Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},A=function(e,t){switch(e){case"Bold":return p.createElement(L,{color:t});case"Broken":return p.createElement(I,{color:t});case"Bulk":return p.createElement(M,{color:t});case"Linear":default:return p.createElement(w,{color:t});case"Outline":return p.createElement(C,{color:t});case"TwoTone":return p.createElement(Z,{color:t})}},P=(0,p.forwardRef)(function(e,t){var r=e.variant,n=e.color,a=e.size,s=(0,m._)(e,S);return p.createElement("svg",(0,m.a)({},s,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),A(r,n))});P.propTypes={variant:x().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:x().string,size:x().oneOfType([x().string,x().number])},P.defaultProps={variant:"Linear",color:"currentColor",size:"24"},P.displayName="MedalStar";var F=r(64947),z=r(39228),T=r(58387),D=r(73806),_=r(36162),B=r(34451),R=r(18662),O=r(66817),W=r(85430),G=r(92773),$=r(15487),Q=r(14761),q=r(45475),U=r(32167),H=r(93633);let V=H.z.object({street:H.z.string({required_error:"Street is required."}),country:H.z.string({required_error:"Country is required."}),city:H.z.string({required_error:"city is required."}),zipCode:H.z.string({required_error:"Zip code is required."})});function Y({customer:e,onMutate:t}){let[r,c]=p.useTransition(),[l,o]=p.useState(),{getCountryByCode:i}=(0,G.F)(),{t:d}=(0,z.$G)(),u=(0,q.cI)({resolver:(0,$.F)(V),defaultValues:{street:"",city:"",country:"",zipCode:""}});return(0,n.jsx)(B.l0,{...u,children:(0,n.jsx)("form",{onSubmit:u.handleSubmit(r=>{c(async()=>{let n=await (0,W.H)(r,e.id);n?.status?(t(),U.toast.success(n.message)):U.toast.error(d(n.message))})}),className:"rounded-xl border border-border bg-background",children:(0,n.jsxs)(s.Qd,{value:"ADDRESS_INFORMATION",className:"border-none px-4 py-0",children:[(0,n.jsx)(s.o4,{className:"py-6 hover:no-underline",children:(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:d("Address")})}),(0,n.jsxs)(s.vF,{className:"flex flex-col gap-2 border-t px-1 pt-4",children:[(0,n.jsx)(O.Z,{children:d("Full mailing address")}),(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-2.5",children:[(0,n.jsx)(B.Wi,{control:u.control,name:"street",render:({field:e})=>(0,n.jsxs)(B.xJ,{className:"col-span-12",children:[(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:d("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:u.control,name:"country",render:({field:e})=>(0,n.jsxs)(B.xJ,{className:"col-span-12",children:[(0,n.jsx)(B.NI,{children:(0,n.jsx)(D.g,{defaultValue:l,onSelectChange:t=>e.onChange(t.code.cca2)})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:u.control,name:"city",render:({field:e})=>(0,n.jsxs)(B.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:d("City"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:u.control,name:"zipCode",render:({field:e})=>(0,n.jsxs)(B.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:d("Zip code"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})})]}),(0,n.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,n.jsxs)(_.z,{disabled:r,children:[(0,n.jsxs)(T.J,{condition:!r,children:[d("Save"),(0,n.jsx)(Q.Z,{size:20})]}),(0,n.jsx)(T.J,{condition:r,children:(0,n.jsx)(a.Loader,{title:d("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}var J=r(1181),X=r(25694);async function K(e,t){try{let r=await J.Z.put(`/admin/agents/update/${t}`,e);return(0,X.B)(r)}catch(e){return(0,X.D)(e)}}let ee=H.z.object({name:H.z.string({required_error:"Agent name is required."}),occupation:H.z.string({required_error:"Occupation is required."}),whatsapp:H.z.string({required_error:"Whatsapp is required."}),agentId:H.z.string({required_error:"Agent ID is required."}),processingTime:H.z.string({required_error:"Processing time is required."})});function et({agentInfo:e,onMutate:t}){let[r,c]=(0,p.useTransition)(),l=(0,F.UO)(),{t:o}=(0,z.$G)(),i=(0,q.cI)({resolver:(0,$.F)(ee),defaultValues:{name:"",occupation:"",whatsapp:"",processingTime:"",agentId:""}});return(0,n.jsx)(B.l0,{...i,children:(0,n.jsx)("form",{onSubmit:i.handleSubmit(r=>{let n={...r,email:e?.email,addressLine:e?.address?.addressLine,zipCode:e?.address?.zipCode,countryCode:e?.address?.countryCode,city:e?.address?.city};c(async()=>{let e=await K(n,l?.userId);e.status?(t(),U.toast.success(e.message)):U.toast.error(o(e.message))})}),className:"rounded-xl border border-border bg-background",children:(0,n.jsxs)(s.Qd,{value:"AGENT_INFORMATION",className:"border-none px-4 py-0",children:[(0,n.jsx)(s.o4,{className:"py-6 hover:no-underline",children:(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:o("Agent profile")})}),(0,n.jsxs)(s.vF,{className:"flex flex-col gap-6 border-t px-1 py-4",children:[(0,n.jsx)(B.Wi,{control:i.control,name:"name",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:o("Agent name")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:o("Enter your name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:i.control,name:"occupation",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:o("Job/Occupation")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:o("Enter your job"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:i.control,name:"whatsapp",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:o("WhatsApp number/link")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:o("Enter your WhatsApp account number or link"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:i.control,name:"agentId",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:o("Agent ID")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",disabled:!0,placeholder:"1241SDFE3",className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:i.control,name:"processingTime",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:o("Processing Time (Hours)")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:o("Enter processing time"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,n.jsx)(_.z,{children:r?(0,n.jsx)(a.Loader,{title:o("Updating..."),className:"text-primary-foreground"}):(0,n.jsxs)(n.Fragment,{children:[o("Save"),(0,n.jsx)(Q.Z,{size:20})]})})})]})]})})})}var er=r(30417);async function en(e,t){try{let r=await J.Z.put(`/admin/agents/${t}/${e}`,{});return(0,X.B)(r)}catch(e){return(0,X.D)(e)}}async function ea(e,t){try{let r=await J.Z.put(`/admin/agents/update-status/${e}`,t);return(0,X.B)(r)}catch(e){return(0,X.D)(e)}}var es=r(51018);function ec({id:e,agentId:t,status:r,suspended:a,recommended:c,onMutate:l}){let{t:o}=(0,z.$G)(),d=({isSuspend:t=a,isRecommended:r=c})=>{U.toast.promise(ea(e,{isSuspend:t,isRecommended:r}),{loading:o("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return l(),e.message},error:e=>e.message})},u=e=>{U.toast.promise(en(t,e),{loading:o("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return l(),e.message},error:e=>e.message})};return(0,n.jsxs)(s.Qd,{value:"AgentStatus",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,n.jsx)(s.o4,{className:"py-6 hover:no-underline",children:(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:o("Agent status")})}),(0,n.jsxs)(s.vF,{className:"flex flex-col gap-6 border-t pt-4",children:[(0,n.jsxs)("div",{className:"inline-flex items-center gap-2",children:[(0,n.jsx)("h6",{className:"w-[150px]",children:o("Suspended")}),(0,n.jsx)(er.Z,{defaultChecked:a,onCheckedChange:()=>d({isSuspend:!a})})]}),(0,n.jsxs)("div",{className:"inline-flex items-center gap-2",children:[(0,n.jsx)("h6",{className:"w-[150px]",children:o("Recommended")}),(0,n.jsx)(er.Z,{defaultChecked:c,onCheckedChange:()=>d({isRecommended:!c})})]}),"pending"===r?(0,n.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,n.jsx)("h5",{className:"text-base font-medium",children:o("Suspended")}),(0,n.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,n.jsxs)(_.z,{type:"button",onClick:()=>u("accept"),className:"bg-[#0B6A0B] text-white hover:bg-[#149014]",children:[(0,n.jsx)(i.Z,{}),o("Grant Access")]}),(0,n.jsxs)(_.z,{type:"button",onClick:()=>u("decline"),className:"bg-[#D13438] text-white hover:bg-[#b42328]",children:[(0,n.jsx)(es.Z,{}),o("Reject")]})]})]}):null]})]})}var el=r(48673),eo=r(68870),ei=r(74988),ed=r(86079);class eu{constructor(e){this.id=e?.id,this.cardId=e?.cardId,this.userId=e?.userId,this.walletId=e?.walletId,this.number=e?.number,this.cvc=e?.cvc,this.lastFour=e?.lastFour,this.brand=e?.brand,this.expMonth=e?.expMonth,this.expYear=e?.expYear,this.status=e?.status,this.type=e?.type,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.wallet=e?.wallet,this.user=e?.user}}var em=r(35114);class ep{constructor(e){this.id=e?.id,this.walletId=e?.walletId,this.logo=e?.logo,this.userId=e?.userId,this.balance=e?.balance,this.defaultStatus=!!e?.default,this.pinDashboard=!!e?.pinDashboard,this.currencyId=e?.currencyId,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.currency=new em.F(e?.currency),this.cards=e?.cards?.map(e=>new eu(e))}}var eh=r(32917),ex=["variant","color","size"],eg=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM18 12.75H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h12c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},ef=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M16 12h2M6 12h5.66",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},ej=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{opacity:".4",d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2Z",fill:t}),p.createElement("path",{d:"M18 12.75H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h12c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},ev=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M6 12h12",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},ek=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M18 12.75H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h12c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},eE=function(e){var t=e.color;return p.createElement(p.Fragment,null,p.createElement("path",{d:"M6 12h12",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},eb=function(e,t){switch(e){case"Bold":return p.createElement(eg,{color:t});case"Broken":return p.createElement(ef,{color:t});case"Bulk":return p.createElement(ej,{color:t});case"Linear":default:return p.createElement(ev,{color:t});case"Outline":return p.createElement(ek,{color:t});case"TwoTone":return p.createElement(eE,{color:t})}},ey=(0,p.forwardRef)(function(e,t){var r=e.variant,n=e.color,a=e.size,s=(0,m._)(e,ex);return p.createElement("svg",(0,m.a)({},s,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),eb(r,n))});function eN({wallets:e}){let{t}=(0,z.$G)();return(0,n.jsxs)(s.Qd,{value:"BALANCE",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,n.jsx)(s.o4,{className:"py-6 hover:no-underline",children:(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:t("Balance")})}),(0,n.jsx)(s.vF,{className:"grid grid-cols-12 gap-4 border-t pt-4",children:e?.map(e=>n.jsx(eS,{item:e},e.id))})]})}function eS({item:e}){let t=new ep(e);return(0,n.jsxs)("div",{className:"relative col-span-12 flex flex-col gap-2 rounded-xl border border-border bg-accent p-6 text-accent-foreground sm:col-span-6 md:col-span-4 lg:col-span-3",children:[(0,n.jsxs)("div",{className:"absolute right-1 top-1 flex items-center gap-1",children:[(0,n.jsx)(eI,{wallet:t,userId:t.userId}),(0,n.jsx)(eL,{wallet:t,userId:t.userId})]}),(0,n.jsx)("span",{className:"text-xs font-normal leading-4",children:t?.currency.code}),(0,n.jsxs)("h6",{className:"text-sm font-semibold leading-5",children:[t.balance," ",t?.currency.code]})]})}function eL({userId:e,wallet:t}){let[r,s]=p.useState(!1),[c,l]=p.useState(!1),{t:o}=(0,z.$G)(),[i,d]=p.useState({amount:"0",currencyCode:t?.currency.code,userId:e,keepRecords:!0}),u=()=>{d({amount:"0",currencyCode:t?.currency.code,userId:e,keepRecords:!0})},m=async e=>{e.preventDefault(),l(!0);let t=await (0,ed.y)({amount:Number(i.amount),currencyCode:i.currencyCode,userId:i.userId,keepRecords:i.keepRecords},"add");t.status?(U.toast.success(t.message),l(!1),s(!1)):(U.toast.error(t.message),l(!1))};return(0,n.jsxs)(eo.Vq,{open:r,onOpenChange:e=>{s(e),u()},children:[(0,n.jsx)(eo.hg,{asChild:!0,children:(0,n.jsx)(_.z,{variant:"ghost",size:"icon",className:"size-8 hover:bg-secondary-text/30 active:bg-secondary-text/50",children:(0,n.jsx)(eh.Z,{strokeWidth:3,size:17})})}),(0,n.jsxs)(eo.cZ,{children:[(0,n.jsxs)(eo.fK,{children:[(0,n.jsx)(eo.$N,{className:"text-semibold",children:o("Add Balance")}),(0,n.jsx)(eo.Be,{className:"hidden"})]}),(0,n.jsx)(ei.Z,{}),(0,n.jsx)("div",{children:(0,n.jsxs)("form",{onSubmit:m,className:"flex flex-col space-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsxs)(O.Z,{className:"text-sm",children:[" ",o("Balance")," "]}),(0,n.jsx)(R.I,{type:"number",value:i.amount,min:0,onChange:e=>d(t=>({...t,amount:e.target.value}))})]}),(0,n.jsxs)(O.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,n.jsx)(el.X,{checked:i.keepRecords,onCheckedChange:e=>d(t=>({...t,keepRecords:e}))}),(0,n.jsx)("span",{children:o("Keep in record")})]}),(0,n.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,n.jsx)(eo.GG,{asChild:!0,children:(0,n.jsx)(_.z,{type:"button",variant:"ghost",children:o("Cancel")})}),(0,n.jsx)(_.z,{disabled:c,children:c?(0,n.jsx)(a.Loader,{title:o("Uploading..."),className:"text-primary-foreground"}):o("Update")})]})]})})]})]})}function eI({userId:e,wallet:t}){let[r,s]=p.useState(!1),[c,l]=p.useState(!1),{t:o}=(0,z.$G)(),[i,d]=p.useState({amount:"0",currencyCode:t?.currency.code,userId:e,keepRecords:!0}),u=()=>{d({amount:"0",currencyCode:t?.currency.code,userId:e,keepRecords:!0})},m=async e=>{e.preventDefault(),s(!0);let t=await (0,ed.y)({amount:Number(i.amount),currencyCode:i.currencyCode,userId:i.userId,keepRecords:i.keepRecords},"remove");t.status?(u(),l(!1),s(!1),U.toast.success(t.status)):(s(!1),U.toast.error(t.status))};return(0,n.jsxs)(eo.Vq,{open:c,onOpenChange:e=>{l(e),u()},children:[(0,n.jsx)(eo.hg,{asChild:!0,children:(0,n.jsx)(_.z,{variant:"ghost",size:"icon",className:"size-8 hover:bg-secondary-text/30 active:bg-secondary-text/50",children:(0,n.jsx)(ey,{strokeWidth:3,size:17})})}),(0,n.jsxs)(eo.cZ,{children:[(0,n.jsxs)(eo.fK,{children:[(0,n.jsx)(eo.$N,{className:"text-semibold",children:o("Remove Balance")}),(0,n.jsx)(eo.Be,{className:"hidden"})]}),(0,n.jsx)(ei.Z,{}),(0,n.jsx)("div",{children:(0,n.jsxs)("form",{onSubmit:m,className:"flex flex-col space-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsxs)(O.Z,{className:"text-sm",children:[" ",o("Balance")," "]}),(0,n.jsx)(R.I,{type:"number",value:i.amount,min:0,onChange:e=>d(t=>({...t,amount:e.target.value}))})]}),(0,n.jsxs)(O.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,n.jsx)(el.X,{checked:i.keepRecords,onCheckedChange:e=>d(t=>({...t,keepRecords:e}))}),(0,n.jsx)("span",{children:o("Keep in record")})]}),(0,n.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,n.jsx)(eo.GG,{asChild:!0,children:(0,n.jsx)(_.z,{type:"button",variant:"ghost",children:o("Cancel")})}),(0,n.jsx)(_.z,{disabled:r,children:r?(0,n.jsx)(a.Loader,{title:o("Uploading..."),className:"text-primary-foreground"}):o("Update")})]})]})})]})]})}ey.propTypes={variant:x().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:x().string,size:x().oneOfType([x().string,x().number])},ey.defaultProps={variant:"Linear",color:"currentColor",size:"24"},ey.displayName="Minus";var eM=r(83941),ew=r(93739),eC=r(7602),eZ=r(82944),eA=r(5670),eP=r(26734),eF=r(12403),ez=r(72382);let eT=H.z.object({profile:ez.K,firstName:H.z.string({required_error:"First name is required."}),lastName:H.z.string({required_error:"Last name is required."}),email:H.z.string({required_error:"Email is required."}),phone:H.z.string({required_error:"Phone is required."}),dateOfBirth:H.z.date({required_error:"Date of Birth is required."}),gender:H.z.string({required_error:"Gender is required"})});function eD({customer:e,onMutate:t,isLoading:r=!1}){let[c,o]=(0,p.useTransition)(),{t:i}=(0,z.$G)(),d=(0,q.cI)({resolver:(0,$.F)(eT),defaultValues:{profile:void 0,firstName:"",lastName:"",email:"",phone:"",dateOfBirth:void 0,gender:""}});return(0,p.useCallback)(()=>{e&&d.reset({firstName:e?.customer?.firstName,lastName:e?.customer?.lastName,email:e?.email,phone:e?.customer?.phone,dateOfBirth:new Date(e?.customer?.dob),gender:e?.customer?.gender})},[r]),(0,n.jsx)(B.l0,{...d,children:(0,n.jsx)("form",{onSubmit:d.handleSubmit(r=>{o(async()=>{let n=await (0,eF.n)(r,e.id);n?.status?(t(),U.toast.success(n.message)):U.toast.error(i(n.message))})}),className:"rounded-xl border border-border bg-background",children:(0,n.jsxs)(s.Qd,{value:"PROFILE_INFORMATION",className:"border-none px-4 py-0",children:[(0,n.jsx)(s.o4,{className:"py-6 hover:no-underline",children:(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:i("Profile")})}),(0,n.jsxs)(s.vF,{className:"flex flex-col gap-6 border-t px-1 py-4",children:[(0,n.jsx)(B.Wi,{control:d.control,name:"profile",render:({field:t})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:i("Profile picture")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(eC.S,{defaultValue:(0,l.qR)(e?.customer?.profileImage),id:"documentFrontSideFile",onChange:e=>t.onChange(e),className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,n.jsx)(eA.X,{}),(0,n.jsx)("p",{className:"text-sm font-normal text-primary",children:i("Upload photo")})]})})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,n.jsx)(B.Wi,{control:d.control,name:"firstName",render:({field:e})=>(0,n.jsxs)(B.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(B.lX,{children:"First name"}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:i("First name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:d.control,name:"lastName",render:({field:e})=>(0,n.jsxs)(B.xJ,{className:"col-span-12 md:col-span-6",children:[(0,n.jsx)(B.lX,{children:"Last name"}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"text",placeholder:i("Last name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})})]}),(0,n.jsx)(B.Wi,{control:d.control,name:"email",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:i("Email")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(R.I,{type:"email",placeholder:i("Enter your email"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:d.control,name:"phone",render:({field:t})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:i("Phone")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(eZ.E,{value:e?.customer?.phone,onChange:t.onChange,inputClassName:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",onBlur:e=>{e?d.setError("phone",{type:"custom",message:i(e)}):d.clearErrors("phone")}})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:d.control,name:"dateOfBirth",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:i("Date of birth")}),(0,n.jsx)(B.NI,{children:(0,n.jsx)(ew.M,{...e})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)(B.Wi,{control:d.control,name:"gender",render:({field:e})=>(0,n.jsxs)(B.xJ,{children:[(0,n.jsx)(B.lX,{children:i("Gender")}),(0,n.jsx)(B.NI,{children:(0,n.jsxs)(eP.E,{defaultValue:e.value,onValueChange:e.onChange,className:"flex",children:[(0,n.jsxs)(O.Z,{htmlFor:"GenderMale","data-selected":"male"===e.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,n.jsx)(eP.m,{id:"GenderMale",value:"male",className:"absolute opacity-0"}),(0,n.jsx)("span",{children:i("Male")})]}),(0,n.jsxs)(O.Z,{htmlFor:"GenderFemale","data-selected":"female"===e.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,n.jsx)(eP.m,{id:"GenderFemale",value:"female",className:"absolute opacity-0"}),(0,n.jsx)("span",{children:i("Female")})]})]})}),(0,n.jsx)(B.zG,{})]})}),(0,n.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,n.jsxs)(_.z,{disabled:c,children:[(0,n.jsx)(T.J,{condition:c,children:(0,n.jsx)(a.Loader,{className:"text-primary-foreground"})}),(0,n.jsxs)(T.J,{condition:!c,children:[i("Save"),(0,n.jsx)(Q.Z,{size:20})]})]})})]})]})})})}function e_({title:e,status:t,icon:r,iconClass:a,statusClass:s,className:c}){return(0,n.jsxs)("div",{className:(0,l.ZP)("inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default",c),children:[(0,n.jsx)("div",{className:(0,l.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full bg-important/20",a),children:r({size:34,variant:"Bulk"})}),(0,n.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,n.jsxs)("span",{className:"block text-xs font-normal leading-4",children:[e," "]}),(0,n.jsx)("h6",{className:(0,l.ZP)("text-sm font-semibold leading-5",s),children:t})]})]})}function eB(){let{t:e}=(0,z.$G)(),t=(0,F.UO)(),{data:r,isLoading:m,mutate:p}=(0,c.d)(`/admin/agents/${t.agentId}`);if(m)return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(a.Loader,{})});let h=r?.data;return(0,n.jsx)(s.UQ,{type:"multiple",defaultValue:["ADDRESS_INFORMATION","BALANCE","PROFILE_INFORMATION","ConvertAccountType","AGENT_INFORMATION","AgentStatus"],children:(0,n.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,n.jsxs)("div",{className:"grid w-full grid-cols-12 gap-4",children:[(0,n.jsx)(eM.n,{}),(0,n.jsx)(e_,{title:e("Agent access"),icon:e=>(0,n.jsx)(o.Z,{...e}),status:e(h?.status==="verified"?"Granted":h?.status==="failed"?"Not Granted":"Pending"),className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,n.jsx)(e_,{title:e("Account Status"),icon:e=>(0,n.jsx)(i.Z,{...e,variant:"Outline"}),statusClass:h?.user?.status?"text-success":"",status:e(h?.user?.status?"Active":"Inactive"),iconClass:"bg-success/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,n.jsx)(e_,{title:e("KYC Status"),icon:e=>h?.user?.kycStatus?(0,n.jsx)(d.Z,{className:(0,l.ZP)(e.className,"text-success"),...e}):(0,n.jsx)(u.Z,{className:(0,l.ZP)(e.className,"text-primary"),...e}),statusClass:h?.user?.kycStatus?"text-success":"text-primary",status:e(h?.user?.kycStatus?"Verified":h?.user?.kyc?"Pending Verification":"Not Submitted Yet"),iconClass:h?.user?.kycStatus?"bg-success/20":"bg-primary/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,n.jsx)(e_,{title:e("Suspended"),icon:e=>(0,n.jsx)(N,{...e}),statusClass:h?.isSuspend?"text-danger":"",status:e(h?.isSuspend?"Yes":"No"),iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,n.jsx)(e_,{title:e("Recommended"),icon:e=>(0,n.jsx)(P,{...e}),statusClass:"text-spacial-blue",status:e(h?.isRecommended?"Yes":"No"),iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"})]}),(0,n.jsx)(eN,{wallets:h?.user?.wallets,onMutate:()=>p(r)}),(0,n.jsx)(eD,{isLoading:m,customer:h?.user,onMutate:()=>p(r)}),(0,n.jsx)(et,{agentInfo:h,onMutate:()=>p(r)}),(0,n.jsx)(Y,{customer:h?.user,onMutate:()=>p(r)}),(0,n.jsx)(ec,{id:h?.userId,agentId:h?.id,status:h?.status,recommended:!!h?.isRecommended,suspended:!!h?.isSuspend,onMutate:()=>p()})]})})}},51018:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var n=r(61394),a=r(29220),s=r(31036),c=r.n(s),l=["variant","color","size"],o=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm3.36 12.3c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-2.3-2.3-2.3 2.3c-.15.15-.34.22-.53.22s-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l2.3-2.3-2.3-2.3a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3 2.3-2.3c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-2.3 2.3 2.3 2.3Z",fill:t}))},i=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"m13.99 10.012.84-.84M9.17 14.828l2.75-2.75M14.83 14.832l-5.66-5.66M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),a.createElement("path",{d:"m13.06 12 2.3-2.3c.29-.29.29-.77 0-1.06a.754.754 0 0 0-1.06 0l-2.3 2.3-2.3-2.3a.754.754 0 0 0-1.06 0c-.29.29-.29.77 0 1.06l2.3 2.3-2.3 2.3c-.29.29-.29.77 0 1.06.15.15.34.22.53.22s.38-.07.53-.22l2.3-2.3 2.3 2.3c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77 0-1.06l-2.3-2.3Z",fill:t}))},u=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10ZM9.17 14.83l5.66-5.66M14.83 14.83 9.17 9.17",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z",fill:t}),a.createElement("path",{d:"M9.17 15.58c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l5.66-5.66c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.7 15.36c-.14.15-.34.22-.53.22Z",fill:t}),a.createElement("path",{d:"M14.83 15.58c-.19 0-.38-.07-.53-.22L8.64 9.7a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l5.66 5.66c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},p=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M12 22c5.5 0 10-4.5 10-10S17.5 2 12 2 2 6.5 2 12s4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("g",{opacity:".4",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},a.createElement("path",{d:"m9.17 14.832 5.66-5.66M14.83 14.832l-5.66-5.66"})))},h=function(e,t){switch(e){case"Bold":return a.createElement(o,{color:t});case"Broken":return a.createElement(i,{color:t});case"Bulk":return a.createElement(d,{color:t});case"Linear":default:return a.createElement(u,{color:t});case"Outline":return a.createElement(m,{color:t});case"TwoTone":return a.createElement(p,{color:t})}},x=(0,a.forwardRef)(function(e,t){var r=e.variant,s=e.color,c=e.size,o=(0,n._)(e,l);return a.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:c,height:c,viewBox:"0 0 24 24",fill:"none"}),h(r,s))});x.propTypes={variant:c().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:c().string,size:c().oneOfType([c().string,c().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="CloseCircle"},75638:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var n=r(61394),a=r(29220),s=r(31036),c=r.n(s),l=["variant","color","size"],o=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M19.789 4.22c-2.96-2.95-7.76-2.95-10.7 0-2.07 2.05-2.69 5-1.89 7.6l-4.7 4.7c-.33.34-.56 1.01-.49 1.49l.3 2.18c.11.72.78 1.4 1.5 1.5l2.18.3c.48.07 1.15-.15 1.49-.5l.82-.82c.2-.19.2-.51 0-.71l-1.94-1.94a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l1.95 1.95c.19.19.51.19.7 0l2.12-2.11c2.59.81 5.54.18 7.6-1.87 2.95-2.95 2.95-7.76 0-10.71ZM14.499 12a2.5 2.5 0 0 1 0-5 2.5 2.5 0 0 1 0 5Z",fill:t}))},i=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M12.989 2.15c2.38-.46 4.95.23 6.8 2.07 2.95 2.95 2.95 7.76 0 10.7a7.575 7.575 0 0 1-7.6 1.87l-4.71 4.7c-.34.35-1.01.56-1.49.49l-2.18-.3c-.72-.1-1.39-.78-1.5-1.5l-.3-2.18c-.07-.48.16-1.15.49-1.49l1.12-1.12 3.57-3.57c-.8-2.6-.18-5.55 1.88-7.6M6.89 17.488l2.3 2.3",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"M14.5 11a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{opacity:".4",d:"M19.789 4.22c-2.96-2.95-7.76-2.95-10.7 0-2.07 2.05-2.69 5-1.89 7.6l-4.7 4.7c-.33.34-.56 1.01-.49 1.49l.3 2.18c.11.72.78 1.4 1.5 1.5l2.18.3c.48.07 1.15-.15 1.49-.5l.82-.82c.2-.19.2-.51 0-.71l-1.94-1.94a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l1.95 1.95c.19.19.51.19.7 0l2.12-2.11c2.59.81 5.54.18 7.6-1.87 2.95-2.95 2.95-7.76 0-10.71ZM14.499 12a2.5 2.5 0 0 1 0-5 2.5 2.5 0 0 1 0 5Z",fill:t}),a.createElement("path",{d:"M14.5 12a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z",fill:t}))},u=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M19.79 14.93a7.575 7.575 0 0 1-7.6 1.87l-4.71 4.7c-.34.35-1.01.56-1.49.49l-2.18-.3c-.72-.1-1.39-.78-1.5-1.5l-.3-2.18c-.07-.48.16-1.15.49-1.49l4.7-4.7c-.8-2.6-.18-5.55 1.88-7.6 2.95-2.95 7.74-2.95 10.7 0 2.96 2.95 2.96 7.76.01 10.71ZM6.89 17.49l2.3 2.3",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"M14.5 11a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M6.18 22.752c-.1 0-.21-.01-.3-.02l-2.17-.3c-1.04-.14-1.98-1.07-2.14-2.13l-.3-2.19c-.1-.7.2-1.61.7-2.12l4.39-4.39c-.71-2.84.11-5.84 2.2-7.91 3.24-3.23 8.51-3.24 11.76 0a8.26 8.26 0 0 1 2.43 5.88c0 2.22-.86 4.31-2.43 5.88-2.1 2.08-5.09 2.9-7.91 2.18l-4.4 4.39c-.42.44-1.17.73-1.83.73Zm8.25-19.99c-1.75 0-3.49.66-4.82 1.99a6.803 6.803 0 0 0-1.7 6.85c.08.27.01.55-.19.75l-4.7 4.7c-.17.17-.31.61-.28.84l.3 2.19c.06.38.47.81.85.86l2.18.3c.24.04.68-.1.85-.27l4.72-4.71c.2-.2.49-.26.75-.18 2.41.76 5.04.11 6.84-1.69 1.28-1.28 1.99-3 1.99-4.82 0-1.83-.71-3.54-1.99-4.82a6.727 6.727 0 0 0-4.8-1.99Z",fill:t}),a.createElement("path",{d:"M9.188 20.54c-.19 0-.38-.07-.53-.22l-2.3-2.3a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.3 2.3c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22ZM14.5 11.75c-1.24 0-2.25-1.01-2.25-2.25s1.01-2.25 2.25-2.25 2.25 1.01 2.25 2.25-1.01 2.25-2.25 2.25Zm0-3c-.41 0-.75.34-.75.75s.34.75.75.75.75-.34.75-.75-.34-.75-.75-.75Z",fill:t}))},p=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M19.789 14.93a7.575 7.575 0 0 1-7.6 1.87l-4.71 4.7c-.34.35-1.01.56-1.49.49l-2.18-.3c-.72-.1-1.39-.78-1.5-1.5l-.3-2.18c-.07-.48.16-1.15.49-1.49l4.7-4.7c-.8-2.6-.18-5.55 1.88-7.6 2.95-2.95 7.74-2.95 10.7 0 2.96 2.95 2.96 7.76.01 10.71Z",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{opacity:".4",d:"m6.89 17.488 2.3 2.3",stroke:t,strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{opacity:".4",d:"M14.5 11a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return a.createElement(o,{color:t});case"Broken":return a.createElement(i,{color:t});case"Bulk":return a.createElement(d,{color:t});case"Linear":default:return a.createElement(u,{color:t});case"Outline":return a.createElement(m,{color:t});case"TwoTone":return a.createElement(p,{color:t})}},x=(0,a.forwardRef)(function(e,t){var r=e.variant,s=e.color,c=e.size,o=(0,n._)(e,l);return a.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:c,height:c,viewBox:"0 0 24 24",fill:"none"}),h(r,s))});x.propTypes={variant:c().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:c().string,size:c().oneOfType([c().string,c().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="Key"},23181:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var n=r(61394),a=r(29220),s=r(31036),c=r.n(s),l=["variant","color","size"],o=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7.75 9.14c0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48 0 .81-.66 1.48-1.48 1.48a1.49 1.49 0 0 1-1.48-1.48Zm1.07 6.88c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.35 15.8c-.15.15-.34.22-.53.22Zm6.95.32c-.81 0-1.48-.66-1.48-1.48 0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48s-.66 1.48-1.48 1.48Z",fill:t}))},i=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M2 13.05V15c0 5 2 7 7 7h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9M8.32 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{opacity:".4",d:"M16.192 2h-8.37c-3.64 0-5.81 2.17-5.81 5.81v8.37c0 3.64 2.17 5.81 5.81 5.81h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81c0-3.64-2.17-5.81-5.81-5.81Z",fill:t}),a.createElement("path",{d:"M8.822 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM9.23 7.66c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48a1.49 1.49 0 0 0-1.48-1.48ZM15.769 13.39c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48s-.66-1.48-1.48-1.48Z",fill:t}))},u=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7ZM8.57 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"M8.98 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.52 16.09a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h6c5.43 0 7.75 2.32 7.75 7.75v6c0 5.43-2.32 7.75-7.75 7.75Zm-6-20C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25V9c0-4.61-1.64-6.25-6.25-6.25H9Z",fill:t}),a.createElement("path",{d:"M8.568 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM8.98 11.108c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48a.48.48 0 0 0-.48-.48ZM15.519 16.839c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48-.21-.48-.48-.48Z",fill:t}))},p=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{opacity:".4",d:"m8.32 15.27 6.54-6.54M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return a.createElement(o,{color:t});case"Broken":return a.createElement(i,{color:t});case"Bulk":return a.createElement(d,{color:t});case"Linear":default:return a.createElement(u,{color:t});case"Outline":return a.createElement(m,{color:t});case"TwoTone":return a.createElement(p,{color:t})}},x=(0,a.forwardRef)(function(e,t){var r=e.variant,s=e.color,c=e.size,o=(0,n._)(e,l);return a.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:c,height:c,viewBox:"0 0 24 24",fill:"none"}),h(r,s))});x.propTypes={variant:c().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:c().string,size:c().oneOfType([c().string,c().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="PercentageSquare"},18825:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var n=r(61394),a=r(29220),s=r(31036),c=r.n(s),l=["variant","color","size"],o=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"m18.54 4.12-5.5-2.06c-.57-.21-1.5-.21-2.07 0l-5.5 2.06c-1.06.4-1.92 1.64-1.92 2.77v8.1c0 .81.53 1.88 1.18 2.36l5.5 4.11c.97.73 2.56.73 3.53 0l5.5-4.11c.65-.49 1.18-1.55 1.18-2.36v-8.1c.01-1.13-.85-2.37-1.9-2.77Zm-3.06 5.6-4.3 4.3c-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-1.6-1.62a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l1.08 1.08 3.77-3.77c.29-.29.77-.29 1.06 0 .29.29.29.78-.01 1.07Z",fill:t}))},i=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"m9.05 11.87 1.61 1.61 4.3-4.3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"M20.59 7.119c0-1.23-.94-2.59-2.09-3.02l-4.99-1.87c-.83-.31-2.19-.31-3.02 0L5.5 4.109c-1.15.43-2.09 1.79-2.09 3.01v7.43c0 1.18.78 2.73 1.73 3.44l4.3 3.21c1.41 1.06 3.73 1.06 5.14 0l4.3-3.21c.95-.71 1.73-2.26 1.73-3.44v-3.52",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{opacity:".4",d:"m10.96 2.059-5.5 2.06c-1.05.4-1.91 1.64-1.91 2.77v8.1c0 .81.53 1.88 1.18 2.36l5.5 4.11c.97.73 2.56.73 3.53 0l5.5-4.11c.65-.49 1.18-1.55 1.18-2.36v-8.1c0-1.12-.86-2.37-1.91-2.76l-5.5-2.06c-.56-.22-1.5-.22-2.07-.01Z",fill:t}),a.createElement("path",{d:"M10.658 14.231c-.19 0-.38-.07-.53-.22l-1.61-1.61a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l1.08 1.08 3.77-3.77c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-4.3 4.3c-.15.15-.34.22-.53.22Z",fill:t}))},u=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M10.49 2.23 5.5 4.11c-1.15.43-2.09 1.79-2.09 3.01v7.43c0 1.18.78 2.73 1.73 3.44l4.3 3.21c1.41 1.06 3.73 1.06 5.14 0l4.3-3.21c.95-.71 1.73-2.26 1.73-3.44V7.12c0-1.23-.94-2.59-2.09-3.02l-4.99-1.87c-.85-.31-2.21-.31-3.04 0Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{d:"m9.05 11.87 1.61 1.61 4.3-4.3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.998 22.761c-1.09 0-2.17-.32-3.02-.95l-4.3-3.21c-1.14-.85-2.03-2.63-2.03-4.04v-7.44c0-1.54 1.13-3.18 2.58-3.72l4.99-1.87c.99-.37 2.55-.37 3.54 0l4.99 1.87c1.45.54 2.58 2.18 2.58 3.72v7.43c0 1.42-.89 3.19-2.03 4.04l-4.3 3.21c-.83.64-1.91.96-3 .96Zm-1.25-19.82-4.99 1.87c-.85.32-1.6 1.4-1.6 2.32v7.43c0 .95.67 2.28 1.42 2.84l4.3 3.21c1.15.86 3.09.86 4.25 0l4.3-3.21c.76-.57 1.42-1.89 1.42-2.84v-7.44c0-.91-.75-1.99-1.6-2.32l-4.99-1.87c-.68-.24-1.84-.24-2.51.01Z",fill:t}),a.createElement("path",{d:"M10.658 14.231c-.19 0-.38-.07-.53-.22l-1.61-1.61a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l1.08 1.08 3.77-3.77c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-4.3 4.3c-.15.15-.34.22-.53.22Z",fill:t}))},p=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M10.49 2.229 5.5 4.109c-1.15.43-2.09 1.79-2.09 3.01v7.43c0 1.18.78 2.73 1.73 3.44l4.3 3.21c1.41 1.06 3.73 1.06 5.14 0l4.3-3.21c.95-.71 1.73-2.26 1.73-3.44v-7.43c0-1.23-.94-2.59-2.09-3.02l-4.99-1.87c-.85-.31-2.21-.31-3.04 0Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{opacity:".4",d:"m9.05 11.87 1.61 1.61 4.3-4.3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},h=function(e,t){switch(e){case"Bold":return a.createElement(o,{color:t});case"Broken":return a.createElement(i,{color:t});case"Bulk":return a.createElement(d,{color:t});case"Linear":default:return a.createElement(u,{color:t});case"Outline":return a.createElement(m,{color:t});case"TwoTone":return a.createElement(p,{color:t})}},x=(0,a.forwardRef)(function(e,t){var r=e.variant,s=e.color,c=e.size,o=(0,n._)(e,l);return a.createElement("svg",(0,n.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:c,height:c,viewBox:"0 0 24 24",fill:"none"}),h(r,s))});x.propTypes={variant:c().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:c().string,size:c().oneOfType([c().string,c().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="ShieldTick"},35114:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});class n{constructor(e){this.formatter=e=>{let t=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),r=t.formatToParts(e),n=r.find(e=>"currency"===e.type)?.value??this.code,a=t.format(e),s=a.substring(n.length).trim();return{currencyCode:this.code,currencySymbol:n,formattedAmount:a,amountText:s}},this.id=e?.id,this.name=e?.name,this.code=e?.code,this.logo=e?.logo??"",this.usdRate=e?.usdRate,this.acceptApiRate=!!e?.acceptApiRate,this.isCrypto=!!e?.isCrypto,this.active=!!e?.active,this.metaData=e?.metaData,this.minAmount=e?.minAmount,this.kycLimit=e?.kycLimit,this.maxAmount=e?.maxAmount,this.dailyTransferAmount=e?.dailyTransferAmount,this.dailyTransferLimit=e?.dailyTransferLimit,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}format(e){let{currencySymbol:t,amountText:r}=this.formatter(e);return`${r} ${t}`}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}}},12771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,runtime:()=>s});var n=r(42416);r(87908);let a=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\_components\Tabbar.tsx#Tabbar`),s="edge";function c({children:e}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a,{}),e]})}},12885:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(42416),a=r(21237);function s(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(a.a,{})})}},17907:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\page.tsx#default`)},29670:(e,t,r)=>{"use strict";function n({children:e}){return e}r.r(t),r.d(t,{default:()=>n}),r(87908)},74030:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(42416),a=r(21237);function s(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(a.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4969,4774,870,1474,3099,7283,5089,3711,3214],()=>t(6062));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/agents/[userId]/[agentId]/page"]=r}]);
//# sourceMappingURL=page.js.map