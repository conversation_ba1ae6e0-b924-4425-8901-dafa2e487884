"use strict";exports.id=6262,exports.ids=[6262],exports.modules={12649:(e,t,a)=>{a.d(t,{Y:()=>n,r:()=>l});var r=a(10326),s=a(65304),i=a(77863);function n({groupName:e,children:t,className:a}){return(0,r.jsxs)("ul",{className:(0,i.ZP)("flex flex-col gap-y-4 text-sm sm:text-base",a),children:[e?r.jsx("li",{className:"text-base font-medium leading-[22px]",children:e}):null,t]})}function l({title:e,value:t,className:a,titleClassName:n,valueClassName:l,isLoading:d=!1}){return d?r.jsx("li",{className:(0,i.ZP)("flex items-center gap-4",a),children:r.jsx(s.O,{className:"h-5 w-2/3"})}):(0,r.jsxs)("li",{className:(0,i.ZP)("flex items-center gap-4",a),children:[r.jsx("div",{className:(0,i.ZP)("flex-1",n),children:e}),r.jsx("div",{className:(0,i.ZP)("justify-self-end text-right font-medium leading-[22px]",l),children:t})]})}a(17577)},48054:(e,t,a)=>{a.d(t,{Q:()=>u,R:()=>c});var r=a(10326),s=a(79210),i=a(77863),n=a(47237),l=a(17577),d=a.n(l),o=a(8281);function c({value:e="",tabs:t=[],children:a,onTabChange:l}){let[c,u]=d().useState(0),m=t.filter(e=>void 0===e.isVisible||!0===e.isVisible),f=m.findIndex(t=>t.value===e);return m.length,(0,r.jsxs)(s.mQ,{value:e,onValueChange:l,children:[r.jsx("div",{className:"hidden h-0.5 w-full bg-background-body md:flex",children:r.jsx(o.Z,{className:(0,i.ZP)("h-0.5 bg-primary transition-[width] duration-200"),style:{width:`${c}%`}})}),r.jsx(s.dr,{className:"hidden bg-transparent md:flex",children:m.map((e,t)=>(0,r.jsxs)(s.SP,{value:e.value,disabled:t>f,"data-complete":e.complete,className:"ring-none group h-8 justify-start rounded-lg border-none border-border px-3 text-sm font-normal leading-5 text-foreground shadow-none outline-none transition-all duration-200 hover:bg-accent hover:text-primary data-[state=active]:bg-transparent data-[complete=true]:text-primary data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:hover:bg-accent",children:[r.jsx(n.Z,{size:19,className:"mr-2 group-hover:text-primary",variant:e.complete?"Bold":"Linear"}),e.title]},e.value))}),a]})}function u({children:e,...t}){return r.jsx(s.nU,{...t,children:e})}},74743:(e,t,a)=>{a.d(t,{R:()=>x});var r=a(10326),s=a(5158),i=a(46226),n=a(70012);function l({walletId:e,logo:t,name:a,balance:s,selectedWallet:l,onSelect:d,id:o}){let{t:c}=(0,n.$G)();return(0,r.jsxs)("label",{htmlFor:`wallet-${e}-${o}`,"data-active":e===l,className:"relative flex w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 px-6 py-4 transition-all duration-300 ease-linear hover:border-transparent hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[r.jsx("input",{type:"radio",id:`wallet-${e}-${o}`,checked:e===l,onChange:()=>d(e),className:"absolute inset-0 left-0 top-0 z-10 cursor-pointer opacity-0"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[t&&r.jsx(i.default,{src:t,alt:a,width:100,height:100,className:"size-8"}),r.jsx("h6",{className:"text-sm font-bold leading-5",children:a})]}),(0,r.jsxs)("div",{className:"mt-2.5",children:[r.jsx("p",{className:"text-xs font-normal leading-4 text-foreground",children:c("Your Balance")}),r.jsx("p",{className:"text-base font-medium leading-[22px]",children:Number(s).toFixed(2)})]})]})}var d=a(90772),o=a(65304),c=a(60814),u=a(40716),m=a(6216),f=a(17577),h=a.n(f);let x=(0,f.forwardRef)(function({value:e,onChange:t,id:a},i){let{t:f}=(0,n.$G)(),[x,p]=h().useState(!1),{wallets:g,isLoading:b}=(0,c.r)(),y=h().useMemo(()=>g,[g]);return(h().useEffect(()=>{let a=y.find(e=>e.defaultStatus);a&&!e&&t(a?.currency.code)},[y]),b)?(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:[r.jsx(o.O,{className:"h-[128px] w-full rounded-xl"}),r.jsx(o.O,{className:"h-[128px] w-full rounded-xl"}),r.jsx(o.O,{className:"h-[128px] w-full rounded-xl"})]}):(0,r.jsxs)("div",{ref:i,id:a,children:[r.jsx("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:(function(e,t=!1,a=3){return t?e:e.slice(0,a)})(g,x)?.map(s=>s?.currency.code&&r.jsx(h().Fragment,{children:r.jsx(l,{walletId:s?.currency.code,logo:s.logo,name:s?.currency.code,balance:s.balance,selectedWallet:e,onSelect:t,id:a})},s.walletId))}),r.jsx(s.J,{condition:g?.length>3,children:r.jsx("div",{className:"mt-2 flex justify-end",children:(0,r.jsxs)(d.z,{type:"button",variant:"link",onClick:()=>p(!x),className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[r.jsx("span",{className:"text-inherit",children:f(x?"Show less":"Show more")}),x?r.jsx(u.Z,{size:12}):r.jsx(m.Z,{size:12})]})})})]})})},55632:(e,t,a)=>{a.d(t,{NI:()=>p,Wi:()=>u,l0:()=>o,lX:()=>x,xJ:()=>h,zG:()=>g});var r=a(10326),s=a(34214),i=a(17577),n=a(74723),l=a(31048),d=a(77863);let o=n.RV,c=i.createContext({}),u=({...e})=>r.jsx(c.Provider,{value:{name:e.name},children:r.jsx(n.Qr,{...e})}),m=()=>{let e=i.useContext(c),t=i.useContext(f),{getFieldState:a,formState:r}=(0,n.Gc)(),s=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...s}},f=i.createContext({}),h=i.forwardRef(({className:e,...t},a)=>{let s=i.useId();return r.jsx(f.Provider,{value:{id:s},children:r.jsx("div",{ref:a,className:(0,d.ZP)("space-y-2",e),...t})})});h.displayName="FormItem";let x=i.forwardRef(({className:e,required:t,...a},s)=>{let{error:i,formItemId:n}=m();return r.jsx("span",{children:r.jsx(l.Z,{ref:s,className:(0,d.ZP)(i&&"text-base font-medium text-destructive",e),htmlFor:n,...a})})});x.displayName="FormLabel";let p=i.forwardRef(({...e},t)=>{let{error:a,formItemId:i,formDescriptionId:n,formMessageId:l}=m();return r.jsx(s.g7,{ref:t,id:i,"aria-describedby":a?`${n} ${l}`:`${n}`,"aria-invalid":!!a,...e})});p.displayName="FormControl",i.forwardRef(({className:e,...t},a)=>{let{formDescriptionId:s}=m();return r.jsx("p",{ref:a,id:s,className:(0,d.ZP)("text-sm text-muted-foreground",e),...t})}).displayName="FormDescription";let g=i.forwardRef(({className:e,children:t,...a},s)=>{let{error:i,formMessageId:n}=m(),l=i?String(i?.message):t;return l?r.jsx("p",{ref:s,id:n,className:(0,d.ZP)("text-sm font-medium text-destructive",e),...a,children:l}):null});g.displayName="FormMessage"},54432:(e,t,a)=>{a.d(t,{I:()=>n});var r=a(10326),s=a(17577),i=a(77863);let n=s.forwardRef(({className:e,type:t,...a},s)=>r.jsx("input",{type:t,className:(0,i.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...a}));n.displayName="Input"},31048:(e,t,a)=>{a.d(t,{Z:()=>c});var r=a(10326),s=a(34478),i=a(79360),n=a(17577),l=a(77863);let d=(0,i.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=n.forwardRef(({className:e,...t},a)=>r.jsx(s.f,{ref:a,className:(0,l.ZP)(d(),e),...t}));o.displayName=s.f.displayName;let c=o},65304:(e,t,a)=>{a.d(t,{O:()=>i});var r=a(10326),s=a(77863);function i({className:e,...t}){return r.jsx("div",{className:(0,s.ZP)("animate-pulse rounded-md bg-muted",e),...t})}},79210:(e,t,a)=>{a.d(t,{SP:()=>o,dr:()=>d,mQ:()=>l,nU:()=>c});var r=a(10326),s=a(17577),i=a(28407),n=a(77863);let l=i.fC,d=s.forwardRef(({className:e,...t},a)=>r.jsx(i.aV,{ref:a,className:(0,n.ZP)("inline-flex h-10 w-full items-center justify-center rounded-md bg-secondary p-1 text-muted-foreground",e),...t}));d.displayName=i.aV.displayName;let o=s.forwardRef(({className:e,...t},a)=>r.jsx(i.xz,{ref:a,className:(0,n.ZP)("inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-semibold text-secondary-800 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite",e),...t}));o.displayName=i.xz.displayName;let c=s.forwardRef(({className:e,...t},a)=>r.jsx(i.VY,{ref:a,className:(0,n.ZP)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=i.VY.displayName},60814:(e,t,a)=>{a.d(t,{r:()=>n});var r=a(49547),s=a(9652),i=a(84455);function n(){let{data:e,isLoading:t,mutate:a}=(0,i.ZP)("/wallets",e=>r.Z.get(e));return{wallets:e?.data?.map(e=>new s.w(e))??[],isLoading:t,getWalletByCurrencyCode:(e,t)=>e?.find(e=>e?.currency?.code===t),mutate:a}}},48444:(e,t,a)=>{a.d(t,{Z:()=>r});class r{constructor(e){this.id=e?.id,this.cardId=e?.cardId,this.userId=e?.userId,this.walletId=e?.walletId,this.number=e?.number,this.cvc=e?.cvc,this.lastFour=e?.lastFour,this.brand=e?.brand,this.expMonth=e?.expMonth,this.expYear=e?.expYear,this.status=e?.status,this.type=e?.type,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.wallet=e?.wallet,this.user=e?.user}}},59598:(e,t,a)=>{a.d(t,{F:()=>r});class r{constructor(e){this.formatter=e=>{let t=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),a=t.formatToParts(e),r=a.find(e=>"currency"===e.type)?.value??this.code,s=t.format(e),i=s.substring(r.length).trim();return{currencyCode:this.code,currencySymbol:r,formattedAmount:s,amountText:i}},this.id=e?.id,this.name=e?.name,this.code=e?.code,this.logo=e?.logo??"",this.usdRate=e?.usdRate,this.acceptApiRate=!!e?.acceptApiRate,this.isCrypto=!!e?.isCrypto,this.active=!!e?.active,this.metaData=e?.metaData,this.minAmount=e?.minAmount,this.kycLimit=e?.kycLimit,this.maxAmount=e?.maxAmount,this.dailyTransferAmount=e?.dailyTransferAmount,this.dailyTransferLimit=e?.dailyTransferLimit,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}format(e){let{currencySymbol:t,amountText:a}=this.formatter(e);return`${a} ${t}`}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}}},9652:(e,t,a)=>{a.d(t,{w:()=>i});var r=a(48444),s=a(59598);class i{constructor(e){this.id=e?.id,this.walletId=e?.walletId,this.logo=e?.logo,this.userId=e?.userId,this.balance=e?.balance,this.defaultStatus=!!e?.default,this.pinDashboard=!!e?.pinDashboard,this.currencyId=e?.currencyId,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.currency=new s.F(e?.currency),this.cards=e?.cards?.map(e=>new r.Z(e))}}}};