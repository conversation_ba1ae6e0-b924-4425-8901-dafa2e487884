"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[17492],{17492:function(e,t,n){n.r(t),n.d(t,{default:function(){return B}});var r=n(57437),i=n(2265),s=n(27300),a=n(70880),o=n(15681),l=n(79981),c=n(97751);async function d(e){try{let t=await l.Z.post("/exchanges/create-request",{currencyFrom:e.currencyFrom,currencyTo:e.currencyTo,amountFrom:Number(e.amount)});return(0,c.B)(t)}catch(e){return(0,c.D)(e)}}var u=n(3612),m=n(13590),h=n(29501),f=n(43949),v=n(14438),x=n(31229),p=n(45932),g=n(62869),y=n(95186),j=n(10407);function b(e){let{form:t,onNext:n}=e,{t:i}=(0,f.$G)();return(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h2",{children:i("From")}),(0,r.jsx)(o.Wi,{name:"currencyFrom",control:t.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.NI,{children:(0,r.jsx)(p.R,{...t,id:"currency-from"})}),(0,r.jsx)(o.zG,{})]})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h2",{children:i("To")}),(0,r.jsx)(o.Wi,{name:"currencyTo",control:t.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.NI,{children:(0,r.jsx)(p.R,{...t,id:"currency-to"})}),(0,r.jsx)(o.zG,{})]})}})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)("h2",{children:i("How much?")}),(0,r.jsx)(o.Wi,{name:"amount",control:t.control,render:e=>{let{field:t}=e;return(0,r.jsxs)(o.xJ,{children:[(0,r.jsx)(o.NI,{children:(0,r.jsx)(y.I,{type:"number",min:0,placeholder:i("Enter exchange amount"),...t})}),(0,r.jsx)(o.zG,{})]})}})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(g.z,{type:"submit",onClick:n,children:[i("Next"),(0,r.jsx)(j.Z,{size:15})]})})]})}var w=n(37781),N=n(25318),C=n(16831),F=n(84190),A=n(6512),z=n(70569),I=n(85323);function S(e){let{from:t,to:n,amount:r}=e,{data:i,isLoading:s}=(0,I.ZP)("/exchanges/calculations?currencyFrom=".concat(t,"&currencyTo=").concat(n,"&amountFrom=").concat(r),e=>l.Z.get(e));return{data:null==i?void 0:i.data,isLoading:s}}var k=n(48358),R=n(94508),Z=n(19571),L=n(83504),E=n(43271),P=n(8400),T=n(61703),D=n(22291),U=n(99376);function O(e){var t,n,i,s;let{res:a,values:o,onAgainExchange:l}=e,{t:c}=(0,f.$G)(),d=(0,U.useRouter)(),{wallets:u}=(0,k.r)(),{data:m}=S({from:o.currencyFrom,to:o.currencyTo,amount:o.amount}),h=null==u?void 0:u.find(e=>{var t;return(null==e?void 0:null===(t=e.currency)||void 0===t?void 0:t.code)===(null==o?void 0:o.currencyFrom)}),x=e=>{v.toast.promise((0,z.y)("".concat(e)),{loading:c("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return e.message},error:e=>e.message})};return(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"mb-1 flex items-center justify-center gap-2 text-xl font-semibold text-foreground sm:text-2xl",children:[(0,r.jsx)(Z.Z,{size:"32",color:"#13A10E",variant:"Bold"}),(0,r.jsx)("span",{children:c("Exchange successful")})]}),(0,r.jsx)(A.Z,{orientation:"horizontal",className:"my-7"}),(0,r.jsxs)(N.Y,{groupName:c("Exchange details"),children:[(0,r.jsx)(N.r,{title:c("Exchange rate"),value:null==m?void 0:m.exchangeRate}),(0,r.jsx)(N.r,{title:c("Exchange"),value:"".concat(null==m?void 0:m.amountFrom," ").concat(null==m?void 0:m.currencyFrom)}),(0,r.jsx)(N.r,{title:c("Exchange amount"),value:"".concat(null==m?void 0:m.amountTo," ").concat(null==m?void 0:m.currencyTo)}),(0,r.jsx)(N.r,{title:c("Service charge"),value:"".concat(null==m?void 0:m.fee," ").concat(null==m?void 0:m.currencyTo)}),(0,r.jsx)(N.r,{title:c("You get"),value:"".concat(null==m?void 0:m.total," ").concat(null==m?void 0:m.currencyTo),valueClassName:"text-xl sm:text-2xl font-semibold leading-8"})]}),(0,r.jsx)(A.Z,{orientation:"horizontal",className:"my-7"}),(0,r.jsxs)("div",{className:"mb-8 space-y-4",children:[(0,r.jsx)("h4",{className:"text-base font-medium sm:text-lg",children:c("New balance")}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(C.qE,{className:"size-10",children:[(0,r.jsx)(C.F$,{src:null==h?void 0:h.logo}),(0,r.jsx)(C.Q5,{className:"bg-important font-bold text-important-foreground",children:null==h?void 0:null===(t=h.currency)||void 0===t?void 0:t.code})]}),(0,r.jsx)("span",{className:"text-sm font-bold",children:null==h?void 0:null===(n=h.currency)||void 0===n?void 0:n.code})]}),(0,r.jsx)("p",{className:"font-medium",children:"".concat(null==h?void 0:h.balance," ").concat(null==h?void 0:null===(i=h.currency)||void 0===i?void 0:i.code)})]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center justify-between gap-4 md:flex-row",children:[(0,r.jsx)(w.T,{trxId:null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.trxId}),(0,r.jsxs)("div",{className:"flex w-full flex-col justify-end gap-4 sm:flex-row",children:[(0,r.jsxs)(F.h_,{children:[(0,r.jsxs)(F.$F,{className:(0,R.ZP)("flex w-full items-center space-x-1.5 md:w-fit",(0,g.d)({variant:"outline"})),children:[(0,r.jsx)("span",{children:c("Menu")}),(0,r.jsx)(L.Z,{size:16})]}),(0,r.jsxs)(F.AW,{align:"start",className:"m-0",children:[(0,r.jsxs)(F.Xi,{onSelect:()=>{var e;return(0,R.Fp)(null==a?void 0:null===(e=a.data)||void 0===e?void 0:e.trxId)},className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[(0,r.jsx)(E.Z,{size:"20",variant:"Outline"}),c("Copy transaction ID")]}),(0,r.jsxs)(F.Xi,{onSelect:()=>{var e;return x(null==a?void 0:null===(e=a.data)||void 0===e?void 0:e.id)},className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[(0,r.jsx)(P.Z,{size:"20",variant:"Outline"}),c("Bookmark receipt")]}),(0,r.jsx)(F.VD,{}),(0,r.jsxs)(F.Xi,{onSelect:l,className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[(0,r.jsx)(T.Z,{size:"20",variant:"Outline"}),c("Exchange again")]})]})]}),(0,r.jsxs)(g.z,{type:"button",onClick:()=>d.push("/"),className:"w-full md:max-w-48",children:[(0,r.jsx)("span",{children:c("Go to dashboard")}),(0,r.jsx)(D.Z,{size:16})]})]})]})]})}var V=n(41709),_=n(85487),G=n(92451);function M(e){var t,n;let{values:i,onNext:s,onPrev:a,isLoading:o=!1,nextLabel:l="Exchange"}=e,{t:c}=(0,f.$G)(),{wallets:d}=(0,k.r)(),{data:u,isLoading:m}=S({from:i.currencyFrom,to:i.currencyTo,amount:i.amount}),h=null==d?void 0:d.find(e=>{var t;return(null==e?void 0:null===(t=e.currency)||void 0===t?void 0:t.code)===(null==i?void 0:i.currencyFrom)}),v=null==d?void 0:d.find(e=>{var t;return(null==e?void 0:null===(t=e.currency)||void 0===t?void 0:t.code)===(null==i?void 0:i.currencyTo)});return(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"mb-8",children:c("Confirm and proceed")}),(0,r.jsxs)("div",{className:"mb-8 flex items-end justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-bold",children:c("From")}),(0,r.jsxs)("div",{className:"relative flex h-14 min-w-14 items-center justify-center gap-2",children:[(0,r.jsxs)(C.qE,{className:"size-10",children:[(0,r.jsx)(C.F$,{src:h.logo}),(0,r.jsx)(C.Q5,{className:"bg-important font-bold text-important-foreground",children:h.currency.code})]}),(0,r.jsx)("span",{className:"font-bold",children:null==h?void 0:null===(t=h.currency)||void 0===t?void 0:t.code})]})]}),(0,r.jsx)("div",{className:"mb-7 h-1 flex-1 border-b border-dotted border-green-700"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-bold",children:c("To")}),(0,r.jsxs)("div",{className:"relative flex h-14 min-w-14 items-center justify-center gap-2",children:[(0,r.jsxs)(C.qE,{className:"size-10",children:[(0,r.jsx)(C.F$,{src:v.logo}),(0,r.jsx)(C.Q5,{className:"bg-important font-bold text-important-foreground",children:v.currency.code})]}),(0,r.jsx)("span",{className:"font-bold",children:null==v?void 0:null===(n=v.currency)||void 0===n?void 0:n.code})]})]})]}),(0,r.jsxs)(N.Y,{groupName:c("Exchange details"),children:[(0,r.jsx)(N.r,{title:c("Exchange rate"),value:null==u?void 0:u.exchangeRate,isLoading:m}),(0,r.jsx)(N.r,{title:c("Exchange"),value:"".concat(null==u?void 0:u.amountFrom," ").concat(null==u?void 0:u.currencyFrom),isLoading:m}),(0,r.jsx)(N.r,{title:c("Exchange amount"),value:"".concat(null==u?void 0:u.amountTo," ").concat(null==u?void 0:u.currencyTo),isLoading:m}),(0,r.jsx)(N.r,{title:c("Service charge"),value:"".concat(null==u?void 0:u.fee," ").concat(null==u?void 0:u.currencyTo),isLoading:m}),(0,r.jsx)(N.r,{title:c("You get"),value:"".concat(null==u?void 0:u.total," ").concat(null==u?void 0:u.currencyTo),valueClassName:"text-xl sm:text-2xl font-semibold leading-8",isLoading:m})]}),(0,r.jsx)(A.Z,{orientation:"horizontal",className:"my-7"}),(0,r.jsxs)("div",{className:"flex flex-col items-center justify-between gap-2 sm:flex-row",children:[(0,r.jsxs)(g.z,{variant:"outline",onClick:a,type:"button",className:"order-2 w-full sm:order-1 sm:w-fit",children:[(0,r.jsx)(G.Z,{size:16}),(0,r.jsx)("span",{children:c("Back")})]}),(0,r.jsxs)(g.z,{type:"submit",onClick:s,disabled:o,className:"order-1 w-full sm:order-2 sm:max-w-48",children:[(0,r.jsxs)(V.J,{condition:!o,children:[(0,r.jsx)("span",{children:c(l)}),(0,r.jsx)(j.Z,{size:16})]}),(0,r.jsx)(V.J,{condition:o,children:(0,r.jsx)(_.Loader,{title:c("Processing..."),className:"text-primary-foreground"})})]})]})]})}let $=x.z.object({amount:x.z.string().min(1,"Amount is required."),currencyFrom:x.z.string().min(1,"Currency is required."),currencyTo:x.z.string().min(1,"Currency is required.")});function B(){let{auth:e}=(0,u.a)(),{t}=(0,f.$G)(),[n,l]=i.useState("amount"),[c,x]=i.useState(null),[p,g]=i.useTransition(),[y,j]=i.useState([{value:"amount",title:t("Amount"),complete:!1},{value:"review",title:t("Payment & Review"),complete:!1},{value:"finish",title:t("Finish"),complete:!1}]),w=(0,h.cI)({resolver:(0,m.F)($),mode:"all",defaultValues:{amount:"",currencyFrom:"",currencyTo:""}}),N=()=>{l("amount"),j([{value:"amount",title:t("Amount"),complete:!1},{value:"review",title:t("Payment & Review"),complete:!1},{value:"finish",title:t("Finish"),complete:!1}]),x(null),w.reset()},C=e=>{j(t=>t.map(t=>t.value===e?{...t,complete:!0}:t))};return(i.useEffect(()=>()=>{N()},[]),null==e?void 0:e.canMakeExchange())?(0,r.jsx)(o.l0,{...w,children:(0,r.jsx)("form",{className:"md:h-full",children:(0,r.jsx)("div",{className:"w-full p-4 pb-10 md:h-full md:p-12",children:(0,r.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,r.jsx)(a.R,{tabs:y,value:n,onTabChange:e=>l(e),children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)(a.Q,{value:"amount",children:(0,r.jsx)(b,{form:w,onNext:w.handleSubmit(()=>{let e=w.getValues();if((null==e?void 0:e.currencyFrom)===(null==e?void 0:e.currencyTo)){w.setError("currencyTo",{type:"manual",message:t("Currency must be different.")});return}l("review"),C("amount")})})}),(0,r.jsx)(a.Q,{value:"review",children:(0,r.jsx)(M,{values:w.getValues(),onNext:w.handleSubmit(e=>{c?(l("finish"),C("review"),C("finish")):g(async()=>{let n=await d(e);n&&n.status?(v.toast.success(n.message),x(n),l("finish"),C("review"),C("finish")):v.toast.error(t(n.message))})}),onPrev:()=>l("amount"),isLoading:p})}),(0,r.jsx)(a.Q,{value:"finish",children:(0,r.jsx)(O,{res:c,values:w.getValues(),onAgainExchange:N})})]})})})})})}):(0,r.jsx)(s.Z,{className:"flex-1 p-10"})}},37781:function(e,t,n){n.d(t,{T:function(){return c}});var r=n(57437),i=n(62869),s=n(78040),a=n(94508),o=n(80093),l=n(43949);function c(e){let{trxId:t,className:n}=e,{t:c}=(0,l.$G)();return(0,r.jsx)(i.z,{variant:"outline",type:"button",className:(0,a.ZP)("w-full md:w-auto",n),asChild:!0,children:(0,r.jsxs)("a",{href:"".concat(s.rH.API_URL,"/transactions/download-receipt/").concat(t),children:[(0,r.jsx)(o.Z,{size:16}),(0,r.jsx)("span",{children:c("Download Receipt")})]})})}},80114:function(e,t,n){n.d(t,{default:function(){return o}});var r=n(57437),i=n(85487),s=n(94508),a=n(43949);function o(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,r.jsx)("div",{className:(0,s.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,r.jsx)(i.Loader,{title:n("Loading..."),className:"text-foreground"})})}},85487:function(e,t,n){n.d(t,{Loader:function(){return a}});var r=n(57437),i=n(94508),s=n(43949);function a(e){let{title:t="Loading...",className:n}=e,{t:a}=(0,s.$G)();return(0,r.jsxs)("div",{className:(0,i.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-inherit",children:a(t)})]})}},27300:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(57437),i=n(94508),s=n(27648),a=n(43949);function o(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,r.jsx)("div",{className:(0,i.ZP)("flex items-center justify-center",t),children:(0,r.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[(0,r.jsx)("h3",{className:"mb-2.5",children:n("This feature is temporarily unavailable")}),(0,r.jsxs)("p",{className:"text-sm text-secondary-text",children:[n("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),(0,r.jsx)(s.default,{href:"/contact-supports",className:"text-primary hover:underline",children:n("support")}),"."]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-secondary-text",children:n("Thank you for your understanding.")})]})})}},70880:function(e,t,n){n.d(t,{Q:function(){return d},R:function(){return c}});var r=n(57437),i=n(12339),s=n(94508),a=n(19571),o=n(2265),l=n(6512);function c(e){let{value:t="",tabs:n=[],children:c,onTabChange:d}=e,[u,m]=o.useState(0),h=n.filter(e=>void 0===e.isVisible||!0===e.isVisible),f=h.findIndex(e=>e.value===t),v=h.length;return o.useEffect(()=>{m((f+1)/v*100)},[f,v,t]),(0,r.jsxs)(i.mQ,{value:t,onValueChange:d,children:[(0,r.jsx)("div",{className:"hidden h-0.5 w-full bg-background-body md:flex",children:(0,r.jsx)(l.Z,{className:(0,s.ZP)("h-0.5 bg-primary transition-[width] duration-200"),style:{width:"".concat(u,"%")}})}),(0,r.jsx)(i.dr,{className:"hidden bg-transparent md:flex",children:h.map((e,t)=>(0,r.jsxs)(i.SP,{value:e.value,disabled:t>f,"data-complete":e.complete,className:"ring-none group h-8 justify-start rounded-lg border-none border-border px-3 text-sm font-normal leading-5 text-foreground shadow-none outline-none transition-all duration-200 hover:bg-accent hover:text-primary data-[state=active]:bg-transparent data-[complete=true]:text-primary data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:hover:bg-accent",children:[(0,r.jsx)(a.Z,{size:19,className:"mr-2 group-hover:text-primary",variant:e.complete?"Bold":"Linear"}),e.title]},e.value))}),c]})}function d(e){let{children:t,...n}=e;return(0,r.jsx)(i.nU,{...n,children:t})}},45932:function(e,t,n){n.d(t,{R:function(){return f}});var r=n(57437),i=n(41709),s=n(33145),a=n(43949);function o(e){let{walletId:t,logo:n,name:i,balance:o,selectedWallet:l,onSelect:c,id:d}=e,{t:u}=(0,a.$G)();return(0,r.jsxs)("label",{htmlFor:"wallet-".concat(t,"-").concat(d),"data-active":t===l,className:"relative flex w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 px-6 py-4 transition-all duration-300 ease-linear hover:border-transparent hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,r.jsx)("input",{type:"radio",id:"wallet-".concat(t,"-").concat(d),checked:t===l,onChange:()=>c(t),className:"absolute inset-0 left-0 top-0 z-10 cursor-pointer opacity-0"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,r.jsx)(s.default,{src:n,alt:i,width:100,height:100,className:"size-8"}),(0,r.jsx)("h6",{className:"text-sm font-bold leading-5",children:i})]}),(0,r.jsxs)("div",{className:"mt-2.5",children:[(0,r.jsx)("p",{className:"text-xs font-normal leading-4 text-foreground",children:u("Your Balance")}),(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:Number(o).toFixed(2)})]})]})}var l=n(62869),c=n(93022),d=n(48358),u=n(66605),m=n(36887),h=n(2265);let f=(0,h.forwardRef)(function(e,t){var n;let{value:s,onChange:f,id:v}=e,{t:x}=(0,a.$G)(),[p,g]=h.useState(!1),{wallets:y,isLoading:j}=(0,d.r)(),b=h.useMemo(()=>y,[y]);return(h.useEffect(()=>{let e=b.find(e=>e.defaultStatus);e&&!s&&f(null==e?void 0:e.currency.code)},[b]),j)?(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:[(0,r.jsx)(c.O,{className:"h-[128px] w-full rounded-xl"}),(0,r.jsx)(c.O,{className:"h-[128px] w-full rounded-xl"}),(0,r.jsx)(c.O,{className:"h-[128px] w-full rounded-xl"})]}):(0,r.jsxs)("div",{ref:t,id:v,children:[(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:null===(n=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;return t?e:e.slice(0,n)}(y,p))||void 0===n?void 0:n.map(e=>(null==e?void 0:e.currency.code)&&(0,r.jsx)(h.Fragment,{children:(0,r.jsx)(o,{walletId:null==e?void 0:e.currency.code,logo:e.logo,name:null==e?void 0:e.currency.code,balance:e.balance,selectedWallet:s,onSelect:f,id:v})},e.walletId))}),(0,r.jsx)(i.J,{condition:(null==y?void 0:y.length)>3,children:(0,r.jsx)("div",{className:"mt-2 flex justify-end",children:(0,r.jsxs)(l.z,{type:"button",variant:"link",onClick:()=>g(!p),className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[(0,r.jsx)("span",{className:"text-inherit",children:x(p?"Show less":"Show more")}),p?(0,r.jsx)(u.Z,{size:12}):(0,r.jsx)(m.Z,{size:12})]})})})]})})},62869:function(e,t,n){n.d(t,{d:function(){return l},z:function(){return c}});var r=n(57437),i=n(37053),s=n(90535),a=n(2265),o=n(94508);let l=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:n,variant:s,size:a,asChild:c=!1,...d}=e,u=c?i.g7:"button";return(0,r.jsx)(u,{className:(0,o.ZP)(l({variant:s,size:a,className:n})),ref:t,...d})});c.displayName="Button"},15681:function(e,t,n){n.d(t,{NI:function(){return x},Wi:function(){return u},l0:function(){return c},lX:function(){return v},xJ:function(){return f},zG:function(){return p}});var r=n(57437),i=n(37053),s=n(2265),a=n(29501),o=n(26815),l=n(94508);let c=a.RV,d=s.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(a.Qr,{...t})})},m=()=>{let e=s.useContext(d),t=s.useContext(h),{getFieldState:n,formState:r}=(0,a.Gc)(),i=n(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...i}},h=s.createContext({}),f=s.forwardRef((e,t)=>{let{className:n,...i}=e,a=s.useId();return(0,r.jsx)(h.Provider,{value:{id:a},children:(0,r.jsx)("div",{ref:t,className:(0,l.ZP)("space-y-2",n),...i})})});f.displayName="FormItem";let v=s.forwardRef((e,t)=>{let{className:n,required:i,...s}=e,{error:a,formItemId:c}=m();return(0,r.jsx)("span",{children:(0,r.jsx)(o.Z,{ref:t,className:(0,l.ZP)(a&&"text-base font-medium text-destructive",n),htmlFor:c,...s})})});v.displayName="FormLabel";let x=s.forwardRef((e,t)=>{let{...n}=e,{error:s,formItemId:a,formDescriptionId:o,formMessageId:l}=m();return(0,r.jsx)(i.g7,{ref:t,id:a,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...n})});x.displayName="FormControl",s.forwardRef((e,t)=>{let{className:n,...i}=e,{formDescriptionId:s}=m();return(0,r.jsx)("p",{ref:t,id:s,className:(0,l.ZP)("text-sm text-muted-foreground",n),...i})}).displayName="FormDescription";let p=s.forwardRef((e,t)=>{let{className:n,children:i,...s}=e,{error:a,formMessageId:o}=m(),c=a?String(null==a?void 0:a.message):i;return c?(0,r.jsx)("p",{ref:t,id:o,className:(0,l.ZP)("text-sm font-medium text-destructive",n),...s,children:c}):null});p.displayName="FormMessage"},95186:function(e,t,n){n.d(t,{I:function(){return a}});var r=n(57437),i=n(2265),s=n(94508);let a=i.forwardRef((e,t)=>{let{className:n,type:i,...a}=e;return(0,r.jsx)("input",{type:i,className:(0,s.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",n),ref:t,...a})});a.displayName="Input"},26815:function(e,t,n){var r=n(57437),i=n(6394),s=n(90535),a=n(2265),o=n(94508);let l=(0,s.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(i.f,{ref:t,className:(0,o.ZP)(l(),n),...s})});c.displayName=i.f.displayName,t.Z=c},12339:function(e,t,n){n.d(t,{SP:function(){return c},dr:function(){return l},mQ:function(){return o},nU:function(){return d}});var r=n(57437),i=n(2265),s=n(20271),a=n(94508);let o=s.fC,l=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)(s.aV,{ref:t,className:(0,a.ZP)("inline-flex h-10 w-full items-center justify-center rounded-md bg-secondary p-1 text-muted-foreground",n),...i})});l.displayName=s.aV.displayName;let c=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)(s.xz,{ref:t,className:(0,a.ZP)("inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-semibold text-secondary-800 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite",n),...i})});c.displayName=s.xz.displayName;let d=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,r.jsx)(s.VY,{ref:t,className:(0,a.ZP)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",n),...i})});d.displayName=s.VY.displayName},17062:function(e,t,n){n.d(t,{Z:function(){return v},O:function(){return f}});var r=n(57437),i=n(80114);n(83079);var s=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=n(31117),o=n(79981),l=n(78040),c=n(83130);class d{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var u=n(99376),m=n(2265);let h=m.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),f=()=>m.useContext(h);function v(e){let{children:t}=e,[n,f]=m.useState("Desktop"),[v,x]=m.useState(!1),[p,g]=m.useState(),{data:y,isLoading:j,error:b,mutate:w}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:N,isLoading:C}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:F,isLoading:A}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),z=(0,u.useRouter)(),I=(0,u.usePathname)();m.useEffect(()=>{(async()=>{f((await s()).deviceType)})()},[]),m.useEffect(()=>{let e=()=>{let e=window.innerWidth;f(e<768?"Mobile":e<1024?"Tablet":"Desktop"),x(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),m.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");g(new d(e))}catch(e){}})()},[]),m.useLayoutEffect(()=>{b&&!l.sp.includes(I)&&z.push("/signin")},[b]);let S=m.useMemo(()=>{var e,t,r;return{isAuthenticate:!!(null==y?void 0:null===(e=y.data)||void 0===e?void 0:e.login),auth:(null==y?void 0:null===(t=y.data)||void 0===t?void 0:t.user)?new c.n(null==y?void 0:null===(r=y.data)||void 0===r?void 0:r.user):null,isLoading:j,deviceLocation:p,refreshAuth:()=>w(y),isExpanded:v,device:n,setIsExpanded:x,branding:null==N?void 0:N.data,googleAnalytics:(null==F?void 0:F.data)?{active:null==F?void 0:F.data.active,apiKey:null==F?void 0:F.data.apiKey}:{active:!1,apiKey:""}}},[y,p,v,n]),k=!j&&!C&&!A;return(0,r.jsx)(h.Provider,{value:S,children:k?t:(0,r.jsx)(i.default,{})})}},70569:function(e,t,n){n.d(t,{y:function(){return s}});var r=n(79981),i=n(97751);async function s(e,t){try{let n=await r.Z.put("".concat(null!=t?t:"/transactions/toggle-bookmark","/").concat(e),{id:e});return(0,i.B)(n)}catch(e){return(0,i.D)(e)}}},3612:function(e,t,n){n.d(t,{a:function(){return i}});var r=n(17062);let i=()=>{let e=(0,r.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},31117:function(e,t,n){n.d(t,{d:function(){return s}});var r=n(79981),i=n(85323);let s=(e,t)=>(0,i.ZP)(e||null,e=>r.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,n){var r=n(78040),i=n(83464);t.Z=i.default.create({baseURL:r.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,n){n.d(t,{rH:function(){return r},sp:function(){return i}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},i=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){n.d(t,{F:function(){return d},Fg:function(){return h},Fp:function(){return c},Qp:function(){return m},ZP:function(){return o},fl:function(){return l},qR:function(){return u},w4:function(){return f}});var r=n(78040),i=n(61994),s=n(14438),a=n(53335);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,i.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>s.toast.success("Copied to clipboard!")).catch(()=>{s.toast.error("Failed to copy!")})};class d{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let i;let s=void 0===t?this.currencyCode:t;try{i=new Intl.NumberFormat("en-US",{style:"currency",currency:s,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){i=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let a=null!==(r=null===(n=i.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:s,o=i.format(e),l=o.substring(a.length).trim();return{currencyCode:s,currencySymbol:a,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",h=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",f=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",i=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?i.set(r,e):i.delete(r),i}},74539:function(e,t,n){n.d(t,{k:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){n.d(t,{n:function(){return l}});class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var i=n(84937);class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=n(66419),o=n(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new i.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new s(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new r(e.agent):void 0}}},84937:function(e,t,n){n.d(t,{O:function(){return i}});var r=n(74539);class i{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new r.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){n.d(t,{u:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}}]);