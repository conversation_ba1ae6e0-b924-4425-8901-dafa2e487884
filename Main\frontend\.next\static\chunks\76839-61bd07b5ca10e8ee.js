"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[76839],{76839:function(e,t,n){n.r(t),n.d(t,{default:function(){return S}});var i=n(57437),s=n(41709),r=n(18629),l=n(45932),a=n(85487),o=n(62869),d=n(15681),u=n(95186),c=n(3612),h=n(79981),v=n(97751);async function m(e){try{let t=await h.Z.post("/services/topup/create",e);return(0,v.B)(t)}catch(e){return(0,v.D)(e)}}var p=n(94508),f=n(13590),x=n(90433),g=n(22291),y=n(27648),w=n(99376),b=n(2265),j=n(29501),N=n(43949),C=n(14438),A=n(31229);let I=A.z.object({walletId:A.z.string().min(1,"Please select a wallet"),topUpAmount:A.z.string().min(1,"Amount is required."),topUpNumber:A.z.string().min(1,"Phone number is required.")});var S=function(){var e,t;let{t:n}=(0,N.$G)(),[h,v]=(0,b.useTransition)(),{auth:A}=(0,c.a)(),{createTopUpRequest:S}={createTopUpRequest:async function(e){let{data:t}=e;return m(t)}},D=(0,w.useRouter)(),k=(0,w.useSearchParams)(),F=null==A?void 0:null===(t=A.customer)||void 0===t?void 0:null===(e=t.address)||void 0===e?void 0:e.countryCode,L=(0,j.cI)({resolver:(0,f.F)(I),mode:"all",defaultValues:{walletId:"",topUpAmount:"",topUpNumber:""}});return(0,b.useEffect)(()=>{k.has("phone")&&L.setValue("topUpNumber",(0,p.Fg)(k.get("phone")))},[k]),(0,i.jsx)(d.l0,{...L,children:(0,i.jsx)("form",{onSubmit:L.handleSubmit(e=>{v(async()=>{let t=Number(null==e?void 0:e.topUpAmount),n=await S({data:{number:null==e?void 0:e.topUpNumber,countryCode:null!=F?F:"",amount:Number.isNaN(t)?0:t,currencyCode:null==e?void 0:e.walletId}});(null==n?void 0:n.status)?(C.toast.success(n.message),D.push("/services/top-up/success")):C.toast.error(n.message)})}),children:(0,i.jsx)("div",{className:"w-full p-4 pb-10 md:p-12",children:(0,i.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,i.jsxs)("div",{className:"flex flex-col gap-4 md:gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"mb-4",children:n("Select wallet")}),(0,i.jsx)(d.Wi,{name:"walletId",control:L.control,render:e=>{let{field:t}=e;return(0,i.jsxs)(d.xJ,{children:[(0,i.jsx)(d.NI,{children:(0,i.jsx)(l.R,{value:t.value,onChange:t.onChange})}),(0,i.jsx)(d.zG,{})]})}})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"mb-4",children:n("How much?")}),(0,i.jsx)(d.Wi,{control:L.control,name:"topUpAmount",render:e=>{let{field:t}=e;return(0,i.jsxs)(d.xJ,{children:[(0,i.jsx)(d.NI,{children:(0,i.jsx)(u.I,{type:"number",placeholder:n("Enter recharge amount"),...t})}),(0,i.jsx)(d.zG,{})]})}})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"mb-4",children:n("Enter phone number")}),(0,i.jsx)(d.Wi,{control:L.control,name:"topUpNumber",render:e=>{let{field:t}=e;return(0,i.jsxs)(d.xJ,{children:[(0,i.jsx)(d.NI,{children:(0,i.jsx)(r.E,{value:t.value,onChange:t.onChange,onBlur:e=>{L.setError("topUpNumber",{type:"custom",message:e})},options:{initialCountry:F}})}),(0,i.jsx)(d.zG,{})]})}})]}),(0,i.jsxs)("div",{className:"flex flex-col items-center justify-between gap-4 sm:flex-row",children:[(0,i.jsx)(o.z,{variant:"outline",type:"button",className:"order-2 flex w-full gap-0 px-4 py-2 text-base font-medium text-foreground sm:order-1 sm:w-fit",asChild:!0,children:(0,i.jsxs)(y.default,{href:"/services",children:[(0,i.jsx)(x.Z,{size:24})," ",n("Back")]})}),(0,i.jsxs)(o.z,{type:"submit",disabled:h,className:"order-1 flex w-full gap-0 rounded-lg px-4 py-2 text-base font-medium leading-[22px] sm:order-2 sm:w-[286px]",children:[(0,i.jsxs)(s.J,{condition:!h,children:[n("Confirm and Recharge"),(0,i.jsx)(g.Z,{size:"16"})]}),(0,i.jsx)(s.J,{condition:h,children:(0,i.jsx)(a.Loader,{title:n("Processing..."),className:"text-primary-foreground"})})]})]})]})})})})})}},41709:function(e,t,n){function i(e){let{condition:t,children:n}=e;return t?n:null}n.d(t,{J:function(){return i}}),n(2265)},80114:function(e,t,n){n.d(t,{default:function(){return a}});var i=n(57437),s=n(85487),r=n(94508),l=n(43949);function a(e){let{className:t}=e,{t:n}=(0,l.$G)();return(0,i.jsx)("div",{className:(0,r.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,i.jsx)(s.Loader,{title:n("Loading..."),className:"text-foreground"})})}},18629:function(e,t,n){n.d(t,{E:function(){return A}});var i=n(57437),s=n(85487),r=n(41062),l=n(23518),a=n(95186),o=n(57054),d=n(40593),u=n(94508),c=n(95550),h=n(36887),v=n(58414),m=n(78286),p=n(19368),f=n(68953),x=n(56555),g=n(5874),y=n(19615),w=n(93781),b=n(83057),j=n(43949),N=n(2265);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function A(e){let{value:t,defaultValue:n="",onChange:s,onBlur:r,disabled:l,inputClassName:o,options:d}=e,[c,h]=(0,N.useState)(null!=n?n:""),[w,j]=(0,N.useState)(""),[A,S]=(0,N.useState)(null==d?void 0:d.initialCountry),D=e=>{if(e)try{let t=v.S(e,A);t?(S(t.country),j("+".concat(t.countryCallingCode)),h(t.formatNational())):h(e)}catch(t){h(e)}else h(e)};(0,N.useEffect)(()=>{t&&D(t)},[t]);let k=m.L(A||(null==d?void 0:d.initialCountry)||"US",b.Z);return(0,i.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(I,{country:A,disabled:l,initialCountry:null==d?void 0:d.initialCountry,onSelect:e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase(),i=p.G(n);j("+".concat(i)),S(n)}}),(0,i.jsx)("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:w||"+".concat(null==k?void 0:k.countryCallingCode)})]}),(0,i.jsx)(a.I,{type:"tel",className:(0,u.ZP)("rounded-l-none pl-2",o),value:c,onChange:e=>{let{value:t}=e.target,n=v.S(t,A);null==r||r(""),n&&f.t(t,A)&&x.q(t,A)?(S(n.country),j("+".concat(n.countryCallingCode)),null==s||s(n.number),h(t)):(n?h(n.nationalNumber):h(t),null==s||s(t))},onPaste:e=>{let t=e.clipboardData.getData("Text"),n=v.S(t);if(n&&f.t(t))D(n.formatNational()),S(n.country),j("+".concat(n.countryCallingCode)),null==s||s(n.number),null==r||r("");else{let e=v.S(t,A);e&&f.t(t,A)&&(D(e.formatNational()),null==s||s(e.number),null==r||r(""))}},onBlur:()=>{if(c&&!g.y(c,A)){let e=y.d(c,A);e&&(null==r||r(C[e]))}},placeholder:null==k?void 0:k.formatNational(),disabled:l})]})}function I(e){let{initialCountry:t,country:n,onSelect:s,disabled:l}=e,[a,d]=(0,N.useState)(!1);return(0,i.jsxs)(o.J2,{open:a,onOpenChange:d,children:[(0,i.jsxs)(o.xo,{disabled:l,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[t||n?(0,i.jsx)(r.W,{countryCode:n||t,className:"aspect-auto h-[18px] w-7 flex-1"}):(0,i.jsx)(c.Z,{}),(0,i.jsx)(h.Z,{variant:"Bold",size:16})]}),(0,i.jsx)(o.yk,{align:"start",className:"h-fit p-0",children:(0,i.jsx)(S,{defaultValue:n||t,onSelect:e=>{s(e),d(!1)}})})]})}function S(e){var t;let{defaultValue:n,onSelect:a}=e,{countries:o,isLoading:u}=(0,d.F)(),{t:c}=(0,j.$G)();return(0,i.jsxs)(l.mY,{children:[(0,i.jsx)(l.sZ,{placeholder:c("Search country by name"),className:"placeholder:text-input-placeholder"}),(0,i.jsx)(l.e8,{children:(0,i.jsx)(l.fu,{children:u?(0,i.jsx)(l.di,{children:(0,i.jsx)(s.Loader,{})}):null===(t=o.filter(e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase();return w.o().includes(n)}))||void 0===t?void 0:t.map(e=>(0,i.jsxs)(l.di,{value:e.name,"data-active":e.code.cca2===n,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>a(e),children:[(0,i.jsx)(r.W,{countryCode:e.code.cca2}),e.name]},e.code.ccn3))})})]})}},45932:function(e,t,n){n.d(t,{R:function(){return m}});var i=n(57437),s=n(41709),r=n(33145),l=n(43949);function a(e){let{walletId:t,logo:n,name:s,balance:a,selectedWallet:o,onSelect:d,id:u}=e,{t:c}=(0,l.$G)();return(0,i.jsxs)("label",{htmlFor:"wallet-".concat(t,"-").concat(u),"data-active":t===o,className:"relative flex w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 px-6 py-4 transition-all duration-300 ease-linear hover:border-transparent hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,i.jsx)("input",{type:"radio",id:"wallet-".concat(t,"-").concat(u),checked:t===o,onChange:()=>d(t),className:"absolute inset-0 left-0 top-0 z-10 cursor-pointer opacity-0"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,i.jsx)(r.default,{src:n,alt:s,width:100,height:100,className:"size-8"}),(0,i.jsx)("h6",{className:"text-sm font-bold leading-5",children:s})]}),(0,i.jsxs)("div",{className:"mt-2.5",children:[(0,i.jsx)("p",{className:"text-xs font-normal leading-4 text-foreground",children:c("Your Balance")}),(0,i.jsx)("p",{className:"text-base font-medium leading-[22px]",children:Number(a).toFixed(2)})]})]})}var o=n(62869),d=n(93022),u=n(48358),c=n(66605),h=n(36887),v=n(2265);let m=(0,v.forwardRef)(function(e,t){var n;let{value:r,onChange:m,id:p}=e,{t:f}=(0,l.$G)(),[x,g]=v.useState(!1),{wallets:y,isLoading:w}=(0,u.r)(),b=v.useMemo(()=>y,[y]);return(v.useEffect(()=>{let e=b.find(e=>e.defaultStatus);e&&!r&&m(null==e?void 0:e.currency.code)},[b]),w)?(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:[(0,i.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,i.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,i.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"})]}):(0,i.jsxs)("div",{ref:t,id:p,children:[(0,i.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:null===(n=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;return t?e:e.slice(0,n)}(y,x))||void 0===n?void 0:n.map(e=>(null==e?void 0:e.currency.code)&&(0,i.jsx)(v.Fragment,{children:(0,i.jsx)(a,{walletId:null==e?void 0:e.currency.code,logo:e.logo,name:null==e?void 0:e.currency.code,balance:e.balance,selectedWallet:r,onSelect:m,id:p})},e.walletId))}),(0,i.jsx)(s.J,{condition:(null==y?void 0:y.length)>3,children:(0,i.jsx)("div",{className:"mt-2 flex justify-end",children:(0,i.jsxs)(o.z,{type:"button",variant:"link",onClick:()=>g(!x),className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[(0,i.jsx)("span",{className:"text-inherit",children:f(x?"Show less":"Show more")}),x?(0,i.jsx)(c.Z,{size:12}):(0,i.jsx)(h.Z,{size:12})]})})})]})})},15681:function(e,t,n){n.d(t,{NI:function(){return f},Wi:function(){return c},l0:function(){return d},lX:function(){return p},xJ:function(){return m},zG:function(){return x}});var i=n(57437),s=n(37053),r=n(2265),l=n(29501),a=n(26815),o=n(94508);let d=l.RV,u=r.createContext({}),c=e=>{let{...t}=e;return(0,i.jsx)(u.Provider,{value:{name:t.name},children:(0,i.jsx)(l.Qr,{...t})})},h=()=>{let e=r.useContext(u),t=r.useContext(v),{getFieldState:n,formState:i}=(0,l.Gc)(),s=n(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:a}=t;return{id:a,name:e.name,formItemId:"".concat(a,"-form-item"),formDescriptionId:"".concat(a,"-form-item-description"),formMessageId:"".concat(a,"-form-item-message"),...s}},v=r.createContext({}),m=r.forwardRef((e,t)=>{let{className:n,...s}=e,l=r.useId();return(0,i.jsx)(v.Provider,{value:{id:l},children:(0,i.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",n),...s})})});m.displayName="FormItem";let p=r.forwardRef((e,t)=>{let{className:n,required:s,...r}=e,{error:l,formItemId:d}=h();return(0,i.jsx)("span",{children:(0,i.jsx)(a.Z,{ref:t,className:(0,o.ZP)(l&&"text-base font-medium text-destructive",n),htmlFor:d,...r})})});p.displayName="FormLabel";let f=r.forwardRef((e,t)=>{let{...n}=e,{error:r,formItemId:l,formDescriptionId:a,formMessageId:o}=h();return(0,i.jsx)(s.g7,{ref:t,id:l,"aria-describedby":r?"".concat(a," ").concat(o):"".concat(a),"aria-invalid":!!r,...n})});f.displayName="FormControl",r.forwardRef((e,t)=>{let{className:n,...s}=e,{formDescriptionId:r}=h();return(0,i.jsx)("p",{ref:t,id:r,className:(0,o.ZP)("text-sm text-muted-foreground",n),...s})}).displayName="FormDescription";let x=r.forwardRef((e,t)=>{let{className:n,children:s,...r}=e,{error:l,formMessageId:a}=h(),d=l?String(null==l?void 0:l.message):s;return d?(0,i.jsx)("p",{ref:t,id:a,className:(0,o.ZP)("text-sm font-medium text-destructive",n),...r,children:d}):null});x.displayName="FormMessage"},57054:function(e,t,n){n.d(t,{J2:function(){return a},xo:function(){return o},yk:function(){return d}});var i=n(57437),s=n(2265),r=n(27312),l=n(94508);let a=r.fC,o=r.xz,d=s.forwardRef((e,t)=>{let{className:n,align:s="center",sideOffset:a=4,...o}=e;return(0,i.jsx)(r.h_,{children:(0,i.jsx)(r.VY,{ref:t,align:s,sideOffset:a,className:(0,l.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...o})})});d.displayName=r.VY.displayName},93022:function(e,t,n){n.d(t,{O:function(){return r}});var i=n(57437),s=n(94508);function r(e){let{className:t,...n}=e;return(0,i.jsx)("div",{className:(0,s.ZP)("animate-pulse rounded-md bg-muted",t),...n})}},17062:function(e,t,n){n.d(t,{Z:function(){return p},O:function(){return m}});var i=n(57437),s=n(80114);n(83079);var r=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),l=n(31117),a=n(79981),o=n(78040),d=n(83130);class u{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var c=n(99376),h=n(2265);let v=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),m=()=>h.useContext(v);function p(e){let{children:t}=e,[n,m]=h.useState("Desktop"),[p,f]=h.useState(!1),[x,g]=h.useState(),{data:y,isLoading:w,error:b,mutate:j}=(0,l.d)("/auth/check",{revalidateOnFocus:!1}),{data:N,isLoading:C}=(0,l.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:A,isLoading:I}=(0,l.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),S=(0,c.useRouter)(),D=(0,c.usePathname)();h.useEffect(()=>{(async()=>{m((await r()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;m(e<768?"Mobile":e<1024?"Tablet":"Desktop"),f(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await a.Z.post("/auth/geo-location");g(new u(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{b&&!o.sp.includes(D)&&S.push("/signin")},[b]);let k=h.useMemo(()=>{var e,t,i;return{isAuthenticate:!!(null==y?void 0:null===(e=y.data)||void 0===e?void 0:e.login),auth:(null==y?void 0:null===(t=y.data)||void 0===t?void 0:t.user)?new d.n(null==y?void 0:null===(i=y.data)||void 0===i?void 0:i.user):null,isLoading:w,deviceLocation:x,refreshAuth:()=>j(y),isExpanded:p,device:n,setIsExpanded:f,branding:null==N?void 0:N.data,googleAnalytics:(null==A?void 0:A.data)?{active:null==A?void 0:A.data.active,apiKey:null==A?void 0:A.data.apiKey}:{active:!1,apiKey:""}}},[y,x,p,n]),F=!w&&!C&&!I;return(0,i.jsx)(v.Provider,{value:k,children:F?t:(0,i.jsx)(s.default,{})})}},97751:function(e,t,n){n.d(t,{B:function(){return s},D:function(){return r}});var i=n(43577);function s(e){var t,n,i;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(i=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==i?i:"",data:null===(n=e.data)||void 0===n?void 0:n.data}}function r(e){let t=500,n="Internal Server Error",s="An unknown error occurred";if((0,i.IZ)(e)){var r,l,a,o,d,u,c,h,v,m,p,f;t=null!==(v=null===(r=e.response)||void 0===r?void 0:r.status)&&void 0!==v?v:500,n=null!==(m=null===(l=e.response)||void 0===l?void 0:l.statusText)&&void 0!==m?m:"Internal Server Error",s=null!==(f=null!==(p=null===(u=e.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(a=o[0])||void 0===a?void 0:a.message)&&void 0!==p?p:null===(h=e.response)||void 0===h?void 0:null===(c=h.data)||void 0===c?void 0:c.message)&&void 0!==f?f:e.message}else e instanceof Error&&(s=e.message);return{statusCode:t,statusText:n,status:!1,message:s,data:void 0,error:e}}},3612:function(e,t,n){n.d(t,{a:function(){return s}});var i=n(17062);let s=()=>{let e=(0,i.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},31117:function(e,t,n){n.d(t,{d:function(){return r}});var i=n(79981),s=n(85323);let r=(e,t)=>(0,s.ZP)(e||null,e=>i.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},48358:function(e,t,n){n.d(t,{r:function(){return l}});var i=n(79981),s=n(54763),r=n(85323);function l(){var e,t;let{data:n,isLoading:l,mutate:a}=(0,r.ZP)("/wallets",e=>i.Z.get(e));return{wallets:null!==(t=null==n?void 0:null===(e=n.data)||void 0===e?void 0:e.map(e=>new s.w(e)))&&void 0!==t?t:[],isLoading:l,getWalletByCurrencyCode:(e,t)=>null==e?void 0:e.find(e=>{var n;return(null==e?void 0:null===(n=e.currency)||void 0===n?void 0:n.code)===t}),mutate:a}}},74539:function(e,t,n){n.d(t,{k:function(){return i}});class i{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){n.d(t,{n:function(){return o}});class i{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var s=n(84937);class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var l=n(66419),a=n(78040);class o{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(a.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new l.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new s.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new r(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new i(e.agent):void 0}}},502:function(e,t,n){n.d(t,{Z:function(){return i}});class i{constructor(e){this.id=null==e?void 0:e.id,this.cardId=null==e?void 0:e.cardId,this.userId=null==e?void 0:e.userId,this.walletId=null==e?void 0:e.walletId,this.number=null==e?void 0:e.number,this.cvc=null==e?void 0:e.cvc,this.lastFour=null==e?void 0:e.lastFour,this.brand=null==e?void 0:e.brand,this.expMonth=null==e?void 0:e.expMonth,this.expYear=null==e?void 0:e.expYear,this.status=null==e?void 0:e.status,this.type=null==e?void 0:e.type,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.wallet=null==e?void 0:e.wallet,this.user=null==e?void 0:e.user}}},28315:function(e,t,n){n.d(t,{F:function(){return i}});class i{format(e){let{currencySymbol:t,amountText:n}=this.formatter(e);return"".concat(n," ").concat(t)}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}constructor(e){var t;this.formatter=e=>{var t,n;let i=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),s=null!==(n=null===(t=i.formatToParts(e).find(e=>"currency"===e.type))||void 0===t?void 0:t.value)&&void 0!==n?n:this.code,r=i.format(e),l=r.substring(s.length).trim();return{currencyCode:this.code,currencySymbol:s,formattedAmount:r,amountText:l}},this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.code=null==e?void 0:e.code,this.logo=null!==(t=null==e?void 0:e.logo)&&void 0!==t?t:"",this.usdRate=null==e?void 0:e.usdRate,this.acceptApiRate=!!(null==e?void 0:e.acceptApiRate),this.isCrypto=!!(null==e?void 0:e.isCrypto),this.active=!!(null==e?void 0:e.active),this.metaData=null==e?void 0:e.metaData,this.minAmount=null==e?void 0:e.minAmount,this.kycLimit=null==e?void 0:e.kycLimit,this.maxAmount=null==e?void 0:e.maxAmount,this.dailyTransferAmount=null==e?void 0:e.dailyTransferAmount,this.dailyTransferLimit=null==e?void 0:e.dailyTransferLimit,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},84937:function(e,t,n){n.d(t,{O:function(){return s}});var i=n(74539);class s{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new i.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){n.d(t,{u:function(){return i}});class i{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},54763:function(e,t,n){n.d(t,{w:function(){return r}});var i=n(502),s=n(28315);class r{constructor(e){var t;this.id=null==e?void 0:e.id,this.walletId=null==e?void 0:e.walletId,this.logo=null==e?void 0:e.logo,this.userId=null==e?void 0:e.userId,this.balance=null==e?void 0:e.balance,this.defaultStatus=!!(null==e?void 0:e.default),this.pinDashboard=!!(null==e?void 0:e.pinDashboard),this.currencyId=null==e?void 0:e.currencyId,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.currency=new s.F(null==e?void 0:e.currency),this.cards=null==e?void 0:null===(t=e.cards)||void 0===t?void 0:t.map(e=>new i.Z(e))}}}}]);