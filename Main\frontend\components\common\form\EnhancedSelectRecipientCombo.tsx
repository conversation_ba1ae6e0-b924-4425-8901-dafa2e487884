"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { 
  IContact, 
  ContactContext, 
  ContactFilters,
  ContactSearchResponse,
  ContactStatusUpdate,
  ContactVerificationUpdate 
} from "@/types/contact";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { 
  Check, 
  ChevronsUpDown, 
  Shield, 
  AlertTriangle, 
  Star,
  Clock,
  Plus,
  Search
} from "lucide-react";
import cn from "@/lib/utils";
import { useWebSocket } from "@/hooks/useWebSocket";
import { contactApi } from "@/lib/contact-api";

interface EnhancedSelectRecipientComboProps {
  contact: IContact | null;
  setContact: (contact: IContact | null) => void;
  onChange?: (contactId: string) => void;
  
  // Context-aware filtering
  context?: ContactContext;
  filterBy?: {
    canReceiveEscrow?: boolean;
    canReceiveRecurringTransfer?: boolean;
    canReceiveFundraisingContribution?: boolean;
    verificationLevel?: 'basic' | 'enhanced' | 'premium';
    maxRiskScore?: number;
  };
  
  // UI customization
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  
  // Advanced features
  enableRealTimeUpdates?: boolean;
  showContactStatus?: boolean;
  showVerificationBadge?: boolean;
  showRiskIndicator?: boolean;
  showRecentContacts?: boolean;
  showFrequentContacts?: boolean;
  allowNewContact?: boolean;
  
  // Callbacks
  onCreateNewContact?: (contactData: { name: string; email: string }) => void;
  onContactSelect?: (contact: IContact) => void;
}

export function EnhancedSelectRecipientCombo({
  contact,
  setContact,
  onChange,
  context = 'general',
  filterBy,
  placeholder = "Select recipient...",
  disabled = false,
  className,
  enableRealTimeUpdates = true,
  showContactStatus = true,
  showVerificationBadge = true,
  showRiskIndicator = false,
  showRecentContacts = true,
  showFrequentContacts = true,
  allowNewContact = false,
  onCreateNewContact,
  onContactSelect,
}: EnhancedSelectRecipientComboProps) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [contacts, setContacts] = useState<IContact[]>([]);
  const [recentContacts, setRecentContacts] = useState<IContact[]>([]);
  const [frequentContacts, setFrequentContacts] = useState<IContact[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<ContactSearchResponse | null>(null);

  // WebSocket for real-time updates
  const { subscribe, unsubscribe } = useWebSocket({
    enabled: enableRealTimeUpdates,
  });

  // Build filters based on context and props
  const buildFilters = useCallback((): ContactFilters => {
    const filters: ContactFilters = {
      search: searchValue,
      limit: 50,
      ...filterBy,
    };

    // Context-specific filtering
    switch (context) {
      case 'escrow':
        filters.canReceiveEscrow = true;
        break;
      case 'recurring-transfer':
        filters.canReceiveRecurringTransfer = true;
        break;
      case 'fundraising-pool':
        filters.canReceiveFundraisingContribution = true;
        break;
    }

    return filters;
  }, [searchValue, filterBy, context]);

  // Fetch contacts
  const fetchContacts = useCallback(async () => {
    setIsLoading(true);
    try {
      const filters = buildFilters();
      const response = await contactApi.searchContacts(filters);
      
      if (response.success) {
        setContacts(response.data.contacts);
        setRecentContacts(response.data.recentContacts || []);
        setFrequentContacts(response.data.frequentContacts || []);
        setSearchResults(response);
      }
    } catch (error) {
      console.error('Failed to fetch contacts:', error);
    } finally {
      setIsLoading(false);
    }
  }, [buildFilters]);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue || open) {
        fetchContacts();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue, open, fetchContacts]);

  // Real-time updates
  useEffect(() => {
    if (enableRealTimeUpdates) {
      const handleContactStatusUpdate = (update: ContactStatusUpdate) => {
        setContacts(prev => prev.map(contact => 
          contact.id === update.contactId 
            ? { ...contact, status: update.status }
            : contact
        ));
      };

      const handleContactVerificationUpdate = (update: ContactVerificationUpdate) => {
        setContacts(prev => prev.map(contact => 
          contact.id === update.contactId 
            ? { 
                ...contact, 
                verificationLevel: update.verificationLevel,
                isVerified: update.isVerified 
              }
            : contact
        ));
      };

      subscribe('contact:status_changed', handleContactStatusUpdate);
      subscribe('contact:verification_updated', handleContactVerificationUpdate);

      return () => {
        unsubscribe('contact:status_changed', handleContactStatusUpdate);
        unsubscribe('contact:verification_updated', handleContactVerificationUpdate);
      };
    }
  }, [enableRealTimeUpdates, subscribe, unsubscribe]);

  const handleContactSelect = (selectedContact: IContact) => {
    setContact(selectedContact);
    onChange?.(selectedContact.id.toString());
    onContactSelect?.(selectedContact);
    setOpen(false);
  };

  const getContactStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'blocked':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getVerificationIcon = (level: string) => {
    switch (level) {
      case 'premium':
        return <Shield className="h-3 w-3 text-blue-600" />;
      case 'enhanced':
        return <Shield className="h-3 w-3 text-green-600" />;
      case 'basic':
        return <Shield className="h-3 w-3 text-gray-600" />;
      default:
        return null;
    }
  };

  const getRiskIndicator = (riskScore: number) => {
    if (riskScore >= 70) {
      return <AlertTriangle className="h-3 w-3 text-red-600" />;
    } else if (riskScore >= 40) {
      return <AlertTriangle className="h-3 w-3 text-yellow-600" />;
    }
    return null;
  };

  const ContactItem = ({ contact: itemContact, showLabel = false }: { contact: IContact; showLabel?: boolean }) => (
    <CommandItem
      key={itemContact.id}
      value={itemContact.id.toString()}
      onSelect={() => handleContactSelect(itemContact)}
      className="flex items-center space-x-3 p-3"
    >
      <Avatar className="h-8 w-8">
        <AvatarImage src={itemContact.avatar} alt={itemContact.name} />
        <AvatarFallback>
          {itemContact.name.split(' ').map(n => n[0]).join('').toUpperCase()}
        </AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium truncate">{itemContact.name}</p>
          {showVerificationBadge && getVerificationIcon(itemContact.verificationLevel)}
          {showRiskIndicator && getRiskIndicator(itemContact.riskScore)}
          {itemContact.relationshipType === 'frequent' && (
            <Star className="h-3 w-3 text-yellow-500" />
          )}
        </div>
        <p className="text-xs text-muted-foreground truncate">{itemContact.email}</p>
        {showLabel && (
          <p className="text-xs text-blue-600">{showLabel}</p>
        )}
      </div>
      
      <div className="flex flex-col items-end space-y-1">
        {showContactStatus && (
          <Badge variant="outline" className={`text-xs ${getContactStatusColor(itemContact.status)}`}>
            {t(itemContact.status)}
          </Badge>
        )}
        <Check
          className={cn(
            "h-4 w-4",
            contact?.id === itemContact.id ? "opacity-100" : "opacity-0"
          )}
        />
      </div>
    </CommandItem>
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={disabled}
        >
          {contact ? (
            <div className="flex items-center space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={contact.avatar} alt={contact.name} />
                <AvatarFallback className="text-xs">
                  {contact.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">{contact.name}</span>
              {showVerificationBadge && getVerificationIcon(contact.verificationLevel)}
            </div>
          ) : (
            <span className="text-muted-foreground">{t(placeholder)}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput
            placeholder={t("Search contacts...")}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          
          <CommandList>
            {isLoading ? (
              <CommandEmpty>{t("Loading contacts...")}</CommandEmpty>
            ) : (
              <>
                {contacts.length === 0 && searchValue && (
                  <CommandEmpty>
                    <div className="text-center py-4">
                      <p className="text-sm text-muted-foreground mb-2">
                        {t("No contacts found")}
                      </p>
                      {allowNewContact && onCreateNewContact && (
                        <Button
                          size="sm"
                          onClick={() => {
                            onCreateNewContact({ name: searchValue, email: '' });
                            setOpen(false);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          {t("Add new contact")}
                        </Button>
                      )}
                    </div>
                  </CommandEmpty>
                )}

                {/* Recent Contacts */}
                {showRecentContacts && recentContacts.length > 0 && (
                  <CommandGroup heading={t("Recent")}>
                    {recentContacts.slice(0, 3).map((recentContact) => (
                      <ContactItem 
                        key={`recent-${recentContact.id}`} 
                        contact={recentContact}
                        showLabel={t("Recent")}
                      />
                    ))}
                  </CommandGroup>
                )}

                {/* Frequent Contacts */}
                {showFrequentContacts && frequentContacts.length > 0 && (
                  <CommandGroup heading={t("Frequent")}>
                    {frequentContacts.slice(0, 3).map((frequentContact) => (
                      <ContactItem 
                        key={`frequent-${frequentContact.id}`} 
                        contact={frequentContact}
                        showLabel={t("Frequent")}
                      />
                    ))}
                  </CommandGroup>
                )}

                {/* Separator if we have recent/frequent contacts */}
                {((showRecentContacts && recentContacts.length > 0) || 
                  (showFrequentContacts && frequentContacts.length > 0)) && 
                  contacts.length > 0 && (
                  <CommandSeparator />
                )}

                {/* All Contacts */}
                {contacts.length > 0 && (
                  <CommandGroup heading={searchValue ? t("Search Results") : t("All Contacts")}>
                    {contacts.map((searchContact) => (
                      <ContactItem 
                        key={`all-${searchContact.id}`} 
                        contact={searchContact}
                      />
                    ))}
                  </CommandGroup>
                )}
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
