(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[97877],{54700:function(e,r,t){Promise.resolve().then(t.bind(t,42823))},42823:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return E}});var n=t(57437),a=t(62869),s=t(79981),l=t(97751);async function i(e){try{let r=await s.Z.post("/agent-methods/create",e);return(0,l.B)(r)}catch(e){return(0,l.D)(e)}}async function o(e,r){try{let t=await s.Z.put("/agent-methods/".concat(r),e);return(0,l.B)(t)}catch(e){return(0,l.D)(e)}}async function d(e){try{let r=await s.<PERSON>.delete("/agent-methods/".concat(e));return(0,l.B)(r)}catch(e){return(0,l.D)(e)}}var c=t(15641),u=t(43949),x=t(14438),m=t(2602);function h(e){let{row:r}=e,{t}=(0,u.$G)();return(0,n.jsx)(a.z,{variant:"destructive",size:"icon",type:"button",onClick:()=>{x.toast.promise(d(r.id),{loading:t("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return(0,m.j)("/agent-methods"),e.message},error:e=>e.message})},className:"size-8",children:(0,n.jsx)(c.Z,{size:17})})}var p=t(41709),f=t(52323),j=t(85487),v=t(19060),g=t(26110),N=t(15681),y=t(95186),w=t(26815),b=t(53647),C=t(6512),Z=t(1828),z=t(31117),I=t(94508),P=t(13590),k=t(13987),S=t(2265),R=t(29501),W=t(31229);let T=W.z.object({name:W.z.string().min(1,"Method name is required."),currencyCode:W.z.string().min(1,"Currency code is required."),countryCode:W.z.string().min(1,"Country is required."),inputType:W.z.enum(["phone","email","other"]).optional(),requiredInput:W.z.boolean().optional().default(!1),allowDeposit:W.z.boolean().optional().default(!0),allowWithdraw:W.z.boolean().optional().default(!0),value:W.z.string().optional(),otherName:W.z.string().optional()});function q(e){let{row:r}=e,{t}=(0,u.$G)(),[s,l]=(0,S.useState)(!1),[i,d]=(0,S.useTransition)(),{data:c,isLoading:h}=(0,z.d)("/currencies"),W=(0,R.cI)({resolver:(0,P.F)(T),defaultValues:{name:"",currencyCode:"",countryCode:"",inputType:void 0,value:"",requiredInput:!1,allowDeposit:!0,allowWithdraw:!0,otherName:""}});return(0,S.useEffect)(()=>{if(r){var e,t,n;W.reset({name:null==r?void 0:r.name,currencyCode:null==r?void 0:r.currencyCode,countryCode:null==r?void 0:r.countryCode,inputType:null!==(e=null==r?void 0:r.inputType)&&void 0!==e?e:void 0,value:null!==(t=null==r?void 0:r.value)&&void 0!==t?t:"",requiredInput:!!(null==r?void 0:r.requiredInput),allowDeposit:!!(null==r?void 0:r.allowDeposit),allowWithdraw:!!(null==r?void 0:r.allowWithdraw),otherName:null!==(n=null==r?void 0:r.otherName)&&void 0!==n?n:""})}},[r]),(0,n.jsxs)(g.Vq,{open:s,onOpenChange:l,children:[(0,n.jsx)(g.hg,{asChild:!0,children:(0,n.jsx)(a.z,{size:"icon",variant:"outline",type:"button",className:"mr-2.5 size-8 text-sm hover:bg-white",children:(0,n.jsx)(k.Z,{size:17})})}),(0,n.jsxs)(g.cZ,{className:"p-0",children:[(0,n.jsxs)(g.fK,{className:"p-6",children:[(0,n.jsx)(g.$N,{children:t("Update method")}),(0,n.jsx)(g.Be,{className:"hidden"})]}),(0,n.jsx)(N.l0,{...W,children:(0,n.jsxs)("form",{onSubmit:W.handleSubmit(e=>{d(async()=>{var n,a,s;let i=await o({...e,inputType:null!==(n=e.inputType)&&void 0!==n?n:"",value:null!==(a=e.value)&&void 0!==a?a:"",requiredInput:e.requiredInput,allowDeposit:e.allowDeposit,allowWithdraw:e.allowWithdraw,otherName:null!==(s=e.otherName)&&void 0!==s?s:""},null==r?void 0:r.id);i.status?((0,m.j)("/agent-methods?page=1&limit=10"),l(!1),x.toast.success(i.message)):x.toast.error(t(i.message))})},()=>{x.toast.error(t("Something went wrong."))}),className:"max-h-[80vh] overflow-y-auto overflow-x-hidden px-6 pb-6",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsx)(N.Wi,{control:W.control,name:"name",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:t("Name")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(y.I,{type:"text",placeholder:t("Enter method name"),...r})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:W.control,name:"value",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:t("Value")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(y.I,{type:"text",placeholder:t("Enter method value"),...r})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:W.control,name:"currencyCode",render:e=>{var r;let{field:a}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:t("Currency")}),(0,n.jsx)(N.NI,{children:(0,n.jsxs)(b.Ph,{value:a.value,onValueChange:a.onChange,children:[(0,n.jsx)(b.i4,{children:(0,n.jsx)(b.ki,{placeholder:t("Enter currency")})}),(0,n.jsxs)(b.Bw,{children:[h?(0,n.jsx)("div",{className:"px-2.5 py-2",children:(0,n.jsx)(j.Loader,{})}):null,!h&&(null==c?void 0:null===(r=c.data)||void 0===r?void 0:r.map(e=>(0,n.jsxs)(b.Ql,{value:null==e?void 0:e.code,children:[e.name," (",null==e?void 0:e.code,")"]},null==e?void 0:e.id)))]})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:W.control,name:"countryCode",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:t("Country")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(f.g,{onSelectChange:e=>r.onChange(e.code.cca2)})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:W.control,name:"requiredInput",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2 py-5",children:[(0,n.jsx)(N.NI,{children:(0,n.jsxs)(w.Z,{className:"flex items-center gap-2.5",children:[(0,n.jsx)(v.X,{id:"requiredAdditionalField",defaultChecked:r.value,onCheckedChange:e=>{r.onChange(e),W.setValue("inputType",void 0),W.setValue("otherName","")}}),(0,n.jsxs)("span",{children:[" ",t("Required additional field")," "]})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(p.J,{condition:W.watch("requiredInput"),children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.Wi,{control:W.control,name:"inputType",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:t("Input type")}),(0,n.jsx)(N.NI,{children:(0,n.jsxs)(b.Ph,{value:r.value,onValueChange:r.onChange,children:[(0,n.jsx)(b.i4,{children:(0,n.jsx)(b.ki,{placeholder:t("Select input type")})}),(0,n.jsx)(b.Bw,{children:["phone","email","other"].map(e=>(0,n.jsx)(b.Ql,{value:e,children:(0,I.fl)(e)},e))})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(p.J,{condition:"other"===W.watch("inputType"),children:(0,n.jsx)(N.Wi,{control:W.control,name:"otherName",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:t("Input name")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(y.I,{type:"text",placeholder:t("Write required field name"),...r})}),(0,n.jsx)(N.zG,{})]})}})})]})}),(0,n.jsx)(C.Z,{className:"my-4"}),(0,n.jsx)(N.Wi,{control:W.control,name:"allowDeposit",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.NI,{children:(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(w.Z,{className:"w-36",children:t("Allow Deposit")}),(0,n.jsx)(Z.Z,{defaultChecked:r.value,onCheckedChange:r.onChange})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:W.control,name:"allowWithdraw",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.NI,{children:(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(w.Z,{className:"w-36",children:t("Allow Withdraw")}),(0,n.jsx)(Z.Z,{defaultChecked:r.value,onCheckedChange:r.onChange})]})}),(0,n.jsx)(N.zG,{})]})}})]}),(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)(a.z,{disabled:i,type:"submit",className:"ml-auto mt-5",children:i?(0,n.jsx)(j.Loader,{title:t("Processing..."),className:"text-primary-foreground"}):t("Update")})})]})})]})]})}var G=t(25833),D=t(35974),J=t(75730),V=t(8877);let F=W.z.object({name:W.z.string().min(1,"Method name is required."),currencyCode:W.z.string().min(1,"Currency code is required."),countryCode:W.z.string().min(1,"Country is required."),inputType:W.z.enum(["phone","email","other"]).optional(),requiredInput:W.z.boolean().optional().default(!1),allowDeposit:W.z.boolean().optional().default(!0),allowWithdraw:W.z.boolean().optional().default(!0),value:W.z.string().optional(),otherName:W.z.string().optional()});function A(e){let{onMutate:r,close:t}=e,{t:s}=(0,u.$G)(),[l,o]=(0,S.useTransition)(),{data:d,isLoading:c}=(0,z.d)("/currencies"),m=(0,R.cI)({resolver:(0,P.F)(F),defaultValues:{name:"",currencyCode:"",countryCode:"",inputType:void 0,value:"",requiredInput:!1,allowDeposit:!0,allowWithdraw:!0,otherName:""}});return(0,n.jsx)(N.l0,{...m,children:(0,n.jsxs)("form",{onSubmit:m.handleSubmit(e=>{o(async()=>{var n,a,l;let o=await i({...e,inputType:null!==(n=e.inputType)&&void 0!==n?n:"",value:null!==(a=e.value)&&void 0!==a?a:"",requiredInput:e.requiredInput,allowDeposit:e.allowDeposit,allowWithdraw:e.allowWithdraw,otherName:null!==(l=e.otherName)&&void 0!==l?l:""});o.status?(r(),x.toast.success(o.message),t()):x.toast.error(s(o.message))})},()=>{x.toast.error(s("Something went wrong."))}),className:"max-h-[80vh] overflow-y-auto overflow-x-hidden px-3 pb-6 sm:px-6",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsx)(N.Wi,{control:m.control,name:"name",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:s("Name")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(y.I,{type:"text",placeholder:s("Enter method name"),...r})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:m.control,name:"value",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:s("Value")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(y.I,{type:"text",placeholder:s("Enter method value"),...r})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:m.control,name:"currencyCode",render:e=>{var r;let{field:t}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:s("Currency")}),(0,n.jsx)(N.NI,{children:(0,n.jsxs)(b.Ph,{value:t.value,onValueChange:t.onChange,children:[(0,n.jsx)(b.i4,{children:(0,n.jsx)(b.ki,{placeholder:s("Enter currency")})}),(0,n.jsxs)(b.Bw,{children:[c?(0,n.jsx)("div",{className:"px-2.5 py-2",children:(0,n.jsx)(j.Loader,{})}):null,!c&&(null==d?void 0:null===(r=d.data)||void 0===r?void 0:r.map(e=>(0,n.jsxs)(b.Ql,{value:null==e?void 0:e.code,children:[e.name," (",null==e?void 0:e.code,")"]},null==e?void 0:e.id)))]})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:m.control,name:"countryCode",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:s("Country")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(f.g,{onSelectChange:e=>r.onChange(e.code.cca2)})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:m.control,name:"requiredInput",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2 py-5",children:[(0,n.jsx)(N.NI,{children:(0,n.jsxs)(w.Z,{className:"flex items-center gap-2.5",children:[(0,n.jsx)(v.X,{id:"requiredAdditionalField",defaultChecked:r.value,onCheckedChange:e=>{r.onChange(e),m.setValue("inputType",void 0),m.setValue("otherName","")}}),(0,n.jsxs)("span",{children:[" ",s("Required additional field")," "]})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(p.J,{condition:m.watch("requiredInput"),children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.Wi,{control:m.control,name:"inputType",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:s("Input type")}),(0,n.jsx)(N.NI,{children:(0,n.jsxs)(b.Ph,{value:r.value,onValueChange:r.onChange,children:[(0,n.jsx)(b.i4,{children:(0,n.jsx)(b.ki,{placeholder:s("Select input type")})}),(0,n.jsx)(b.Bw,{children:["phone","email","other"].map(e=>(0,n.jsx)(b.Ql,{value:e,children:(0,I.fl)(e)},e))})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(p.J,{condition:"other"===m.watch("inputType"),children:(0,n.jsx)(N.Wi,{control:m.control,name:"otherName",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.lX,{children:s("Input name")}),(0,n.jsx)(N.NI,{children:(0,n.jsx)(y.I,{type:"text",placeholder:s("Write required field name"),...r})}),(0,n.jsx)(N.zG,{})]})}})})]})}),(0,n.jsx)(C.Z,{className:"my-4"}),(0,n.jsx)(N.Wi,{control:m.control,name:"allowDeposit",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.NI,{children:(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(w.Z,{className:"w-36",children:s("Allow Deposit")}),(0,n.jsx)(Z.Z,{defaultChecked:r.value,onCheckedChange:r.onChange})]})}),(0,n.jsx)(N.zG,{})]})}}),(0,n.jsx)(N.Wi,{control:m.control,name:"allowWithdraw",render:e=>{let{field:r}=e;return(0,n.jsxs)(N.xJ,{className:"px-2",children:[(0,n.jsx)(N.NI,{children:(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(w.Z,{className:"w-36",children:s("Allow Withdraw")}),(0,n.jsx)(Z.Z,{defaultChecked:r.value,onCheckedChange:r.onChange})]})}),(0,n.jsx)(N.zG,{})]})}})]}),(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)(a.z,{disabled:l,type:"submit",className:"ml-auto mt-5",children:l?(0,n.jsx)(j.Loader,{title:s("Processing..."),className:"text-primary-foreground"}):s("Create")})})]})})}function E(){let{t:e}=(0,u.$G)(),[r,t]=(0,S.useState)(!1),[s,l]=S.useState([]),{data:i,isLoading:o,meta:d,refresh:c}=(0,J.Z)("/agent-methods",{keepPreviousData:!0});return(0,n.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,n.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,n.jsx)("div",{className:"py-6 hover:no-underline",children:(0,n.jsxs)("div",{className:"flex w-full flex-wrap items-center gap-1",children:[(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:e("Available methods")}),(0,n.jsx)("div",{className:"flex-1"}),(0,n.jsx)("div",{onClick:e=>e.stopPropagation(),role:"presentation",children:(0,n.jsxs)(g.Vq,{open:r,onOpenChange:t,children:[(0,n.jsx)(g.hg,{asChild:!0,children:(0,n.jsx)(a.z,{size:"sm",variant:"outline",type:"button",className:"mr-2.5 cursor-pointer text-sm",asChild:!0,children:(0,n.jsxs)("div",{children:[(0,n.jsx)(V.Z,{size:17}),e("Add New Method")]})})}),(0,n.jsxs)(g.cZ,{className:"p-0",children:[(0,n.jsxs)(g.fK,{className:"p-6",children:[(0,n.jsx)(g.$N,{children:e("Create method")}),(0,n.jsx)(g.Be,{className:"hidden"})]}),(0,n.jsx)(A,{onMutate:()=>c(),close:()=>t(!1)})]})]})})]})}),(0,n.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,n.jsx)(G.Z,{data:i,sorting:s,isLoading:o,setSorting:l,pagination:{total:null==d?void 0:d.total,page:null==d?void 0:d.currentPage,limit:null==d?void 0:d.perPage},structure:[{id:"name",header:e("Name"),cell:e=>{var r;let{row:t}=e;return(0,n.jsx)("span",{className:"block min-w-32",children:null===(r=t.original)||void 0===r?void 0:r.name})}},{id:"value",header:e("Value"),cell:e=>{var r;let{row:t}=e;return(null===(r=t.original)||void 0===r?void 0:r.value)||(0,n.jsx)("span",{className:"text-sm font-normal text-secondary-text",children:"N/A"})}},{id:"inputType",header:e("Input type"),cell:e=>{var r;let{row:t}=e;return(0,n.jsx)(D.C,{variant:"secondary",children:(0,I.fl)(null===(r=t.original)||void 0===r?void 0:r.inputType)})}},{id:"otherName",header:e("Input name"),cell:e=>{var r;let{row:t}=e;return(null===(r=t.original)||void 0===r?void 0:r.otherName)||(0,n.jsx)("span",{className:"text-sm font-normal text-secondary-text",children:"N/A"})}},{id:"required",header:e("Required"),cell:e=>{var r;let{row:t}=e;return(0,n.jsx)(D.C,{variant:"secondary",children:(null===(r=t.original)||void 0===r?void 0:r.requiredInput)?"Yes":"No"})}},{id:"countryCode",header:e("Country code"),cell:e=>{var r;let{row:t}=e;return(0,n.jsx)(D.C,{variant:"secondary",children:null===(r=t.original)||void 0===r?void 0:r.countryCode})}},{id:"currencyCode",header:e("Currency code"),cell:e=>{var r;let{row:t}=e;return(0,n.jsx)(D.C,{variant:"secondary",children:null===(r=t.original)||void 0===r?void 0:r.currencyCode})}},{id:"status",header:e("Status"),cell:e=>{var r;let{row:t}=e,a=(null===(r=t.original)||void 0===r?void 0:r.active)?"active":"inactive";return(0,n.jsx)(D.C,{variant:"active"===a?"success":"secondary",children:(0,I.fl)(a)})}},{id:"menu",header:e("Menu"),cell:e=>{let{row:r}=e;return(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)(q,{row:r.original}),(0,n.jsx)(h,{row:r.original})]})}}]})})]})})}},41709:function(e,r,t){"use strict";function n(e){let{condition:r,children:t}=e;return r?t:null}t.d(r,{J:function(){return n}}),t(2265)},25833:function(e,r,t){"use strict";t.d(r,{Z:function(){return j}});var n=t(57437),a=t(94508),s=t(71594),l=t(24525),i=t(73490),o=t(36887),d=t(64394),c=t(61756),u=t(99376),x=t(4751),m=t(2265),h=t(43949),p=t(62869),f=t(73578);function j(e){let{data:r,isLoading:t=!1,structure:j,sorting:v,setSorting:g,padding:N=!1,className:y,onRefresh:w,pagination:b}=e,C=(0,m.useMemo)(()=>j,[j]),Z=(0,u.useRouter)(),z=(0,u.usePathname)(),I=(0,u.useSearchParams)(),{t:P}=(0,h.$G)(),k=(0,s.b7)({data:r||[],columns:C,state:{sorting:v,onRefresh:w},onSortingChange:g,getCoreRowModel:(0,l.sC)(),getSortedRowModel:(0,l.tj)(),debugTable:!1});return t?(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:P("Loading...")})}):(null==r?void 0:r.length)?(0,n.jsxs)("div",{className:(0,a.ZP)("".concat(N?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),y),children:[(0,n.jsxs)(f.iA,{children:[(0,n.jsx)(f.xD,{children:k.getHeaderGroups().map(e=>(0,n.jsx)(f.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var r,t,l,i;return(0,n.jsx)(f.ss,{className:(0,a.ZP)("",null==e?void 0:null===(l=e.column)||void 0===l?void 0:null===(t=l.columnDef)||void 0===t?void 0:null===(r=t.meta)||void 0===r?void 0:r.className),children:e.isPlaceholder?null:(0,n.jsxs)(p.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[P((0,s.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(i=({asc:(0,n.jsx)(o.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,n.jsx)(o.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==i?i:(0,n.jsx)(o.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,n.jsx)(f.RM,{children:k.getRowModel().rows.map(e=>(0,n.jsx)(f.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,n.jsx)(f.pj,{className:"py-3 text-sm font-semibold",children:(0,s.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),b&&b.total>10&&(0,n.jsx)("div",{className:"pb-2 pt-6",children:(0,n.jsx)(x.Z,{showTotal:(e,r)=>P("Showing {{start}}-{{end}} of {{total}}",{start:r[0],end:r[1],total:e}),align:"start",current:null==b?void 0:b.page,total:null==b?void 0:b.total,pageSize:null==b?void 0:b.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let r=new URLSearchParams(I);r.set("page",e.toString()),Z.push("".concat(z,"?").concat(r.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(d.Z,{size:"18"})}),nextIcon:e=>(0,n.jsx)("a",{...e,children:(0,n.jsx)(c.Z,{size:"18"})})})})]}):(0,n.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,n.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,n.jsx)(i.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),P("No data found!")]})})}},52323:function(e,r,t){"use strict";t.d(r,{g:function(){return m}});var n=t(57437),a=t(2265),s=t(85487),l=t(41062),i=t(23518),o=t(57054),d=t(40593),c=t(94508),u=t(36887),x=t(43949);function m(e){var r,t;let{allCountry:m=!1,defaultValue:h,defaultCountry:p,onSelectChange:f,disabled:j=!1,triggerClassName:v,arrowClassName:g,flagClassName:N,display:y,placeholderClassName:w,align:b="start",side:C="bottom"}=e,{t:Z}=(0,x.$G)(),{countries:z,getCountryByCode:I,isLoading:P}=(0,d.F)(),[k,S]=a.useState(!1),[R,W]=a.useState(h);return a.useEffect(()=>{h&&W(h)},[h]),a.useEffect(()=>{(async()=>{p&&await I(p,e=>{e&&(W(e),f(e))})})()},[p]),(0,n.jsxs)(o.J2,{open:k,onOpenChange:S,children:[(0,n.jsxs)(o.xo,{disabled:j,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",v),children:[R?(0,n.jsx)("div",{className:"flex flex-1 items-center",children:(0,n.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,n.jsx)(l.W,{className:N,countryCode:(null===(r=R.code)||void 0===r?void 0:r.cca2)==="*"?"UN":null===(t=R.code)||void 0===t?void 0:t.cca2}),void 0!==y?y(R):(0,n.jsx)("span",{children:R.name})]})}):(0,n.jsx)("span",{className:(0,c.ZP)("text-placeholder",w),children:Z("Select country")}),(0,n.jsx)(u.Z,{className:(0,c.ZP)("size-6",g)})]}),(0,n.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:b,side:C,children:(0,n.jsxs)(i.mY,{children:[(0,n.jsx)(i.sZ,{placeholder:Z("Search...")}),(0,n.jsx)(i.e8,{children:(0,n.jsxs)(i.fu,{children:[P&&(0,n.jsx)(s.Loader,{}),m&&(0,n.jsxs)(i.di,{value:Z("All countries"),onSelect:()=>{W({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),f({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),S(!1)},children:[(0,n.jsx)(l.W,{countryCode:"UN"}),(0,n.jsx)("span",{className:"pl-1.5",children:Z("All countries")})]}),null==z?void 0:z.map(e=>"officially-assigned"===e.status?(0,n.jsxs)(i.di,{value:e.name,onSelect:()=>{W(e),f(e),S(!1)},children:[(0,n.jsx)(l.W,{countryCode:e.code.cca2}),(0,n.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},35974:function(e,r,t){"use strict";t.d(r,{C:function(){return i}});var n=t(57437),a=t(90535);t(2265);var s=t(94508);let l=(0,a.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:t,...a}=e;return(0,n.jsx)("div",{className:(0,s.ZP)(l({variant:t}),r),...a})}},19060:function(e,r,t){"use strict";t.d(r,{X:function(){return o}});var n=t(57437),a=t(9270),s=t(30401),l=t(2265),i=t(94508);let o=l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)(a.fC,{ref:r,className:(0,i.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...l,children:(0,n.jsx)(a.z$,{className:(0,i.ZP)("flex items-center justify-center text-current"),children:(0,n.jsx)(s.Z,{className:"h-4 w-4"})})})});o.displayName=a.fC.displayName},15681:function(e,r,t){"use strict";t.d(r,{NI:function(){return f},Wi:function(){return u},l0:function(){return d},lX:function(){return p},xJ:function(){return h},zG:function(){return j}});var n=t(57437),a=t(37053),s=t(2265),l=t(29501),i=t(26815),o=t(94508);let d=l.RV,c=s.createContext({}),u=e=>{let{...r}=e;return(0,n.jsx)(c.Provider,{value:{name:r.name},children:(0,n.jsx)(l.Qr,{...r})})},x=()=>{let e=s.useContext(c),r=s.useContext(m),{getFieldState:t,formState:n}=(0,l.Gc)(),a=t(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...a}},m=s.createContext({}),h=s.forwardRef((e,r)=>{let{className:t,...a}=e,l=s.useId();return(0,n.jsx)(m.Provider,{value:{id:l},children:(0,n.jsx)("div",{ref:r,className:(0,o.ZP)("space-y-2",t),...a})})});h.displayName="FormItem";let p=s.forwardRef((e,r)=>{let{className:t,required:a,...s}=e,{error:l,formItemId:d}=x();return(0,n.jsx)("span",{children:(0,n.jsx)(i.Z,{ref:r,className:(0,o.ZP)(l&&"text-base font-medium text-destructive",t),htmlFor:d,...s})})});p.displayName="FormLabel";let f=s.forwardRef((e,r)=>{let{...t}=e,{error:s,formItemId:l,formDescriptionId:i,formMessageId:o}=x();return(0,n.jsx)(a.g7,{ref:r,id:l,"aria-describedby":s?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!s,...t})});f.displayName="FormControl",s.forwardRef((e,r)=>{let{className:t,...a}=e,{formDescriptionId:s}=x();return(0,n.jsx)("p",{ref:r,id:s,className:(0,o.ZP)("text-sm text-muted-foreground",t),...a})}).displayName="FormDescription";let j=s.forwardRef((e,r)=>{let{className:t,children:a,...s}=e,{error:l,formMessageId:i}=x(),d=l?String(null==l?void 0:l.message):a;return d?(0,n.jsx)("p",{ref:r,id:i,className:(0,o.ZP)("text-sm font-medium text-destructive",t),...s,children:d}):null});j.displayName="FormMessage"},57054:function(e,r,t){"use strict";t.d(r,{J2:function(){return i},xo:function(){return o},yk:function(){return d}});var n=t(57437),a=t(2265),s=t(27312),l=t(94508);let i=s.fC,o=s.xz,d=a.forwardRef((e,r)=>{let{className:t,align:a="center",sideOffset:i=4,...o}=e;return(0,n.jsx)(s.h_,{children:(0,n.jsx)(s.VY,{ref:r,align:a,sideOffset:i,className:(0,l.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})})});d.displayName=s.VY.displayName},53647:function(e,r,t){"use strict";t.d(r,{Bw:function(){return f},Ph:function(){return u},Ql:function(){return j},i4:function(){return m},ki:function(){return x}});var n=t(57437),a=t(68856),s=t(22135),l=t(40875),i=t(2265),o=t(94508),d=t(36887),c=t(22291);let u=a.fC;a.ZA;let x=a.B4,m=i.forwardRef((e,r)=>{let{className:t,children:s,...l}=e;return(0,n.jsxs)(a.xz,{ref:r,className:(0,o.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[s,(0,n.jsx)(a.JO,{asChild:!0,children:(0,n.jsx)(d.Z,{size:"24",color:"#292D32"})})]})});m.displayName=a.xz.displayName;let h=i.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,n.jsx)(a.u_,{ref:r,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,n.jsx)(s.Z,{className:"h-4 w-4"})})});h.displayName=a.u_.displayName;let p=i.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.$G,{ref:r,className:(0,o.ZP)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,n.jsx)(l.Z,{className:"h-4 w-4"})})});p.displayName=a.$G.displayName;let f=i.forwardRef((e,r)=>{let{className:t,children:s,position:l="popper",...i}=e;return(0,n.jsx)(a.h_,{children:(0,n.jsxs)(a.VY,{ref:r,className:(0,o.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...i,children:[(0,n.jsx)(h,{}),(0,n.jsx)(a.l_,{className:(0,o.ZP)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,n.jsx)(p,{})]})})});f.displayName=a.VY.displayName,i.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.__,{ref:r,className:(0,o.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...s})}).displayName=a.__.displayName;let j=i.forwardRef((e,r)=>{let{className:t,children:s,...l}=e;return(0,n.jsxs)(a.ck,{ref:r,className:(0,o.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(a.wU,{children:(0,n.jsx)(c.Z,{variant:"Bold",className:"h-4 w-4"})})}),(0,n.jsx)(a.eT,{children:s})]})});j.displayName=a.ck.displayName,i.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.Z0,{ref:r,className:(0,o.ZP)("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=a.Z0.displayName},6512:function(e,r,t){"use strict";var n=t(57437),a=t(55156),s=t(2265),l=t(94508);let i=s.forwardRef((e,r)=>{let{className:t,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,n.jsx)(a.f,{ref:r,decorative:i,orientation:s,className:(0,l.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...o})});i.displayName=a.f.displayName,r.Z=i},1828:function(e,r,t){"use strict";var n=t(57437),a=t(50721),s=t(2265),l=t(94508);let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.fC,{className:(0,l.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",t),...s,ref:r,children:(0,n.jsx)(a.bU,{className:(0,l.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});i.displayName=a.fC.displayName,r.Z=i},73578:function(e,r,t){"use strict";t.d(r,{RM:function(){return o},SC:function(){return d},iA:function(){return l},pj:function(){return u},ss:function(){return c},xD:function(){return i}});var n=t(57437),a=t(2265),s=t(94508);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:r,className:(0,s.ZP)("w-full caption-bottom text-sm",t),...a})})});l.displayName="Table";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("thead",{ref:r,className:(0,s.ZP)("",t),...a})});i.displayName="TableHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("tbody",{ref:r,className:(0,s.ZP)("[&_tr:last-child]:border-0",t),...a})});o.displayName="TableBody",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("tfoot",{ref:r,className:(0,s.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...a})}).displayName="TableFooter";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("tr",{ref:r,className:(0,s.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...a})});d.displayName="TableRow";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("th",{ref:r,className:(0,s.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...a})});c.displayName="TableHead";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("td",{ref:r,className:(0,s.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...a})});u.displayName="TableCell",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("caption",{ref:r,className:(0,s.ZP)("mt-4 text-sm text-muted-foreground",t),...a})}).displayName="TableCaption"},97751:function(e,r,t){"use strict";t.d(r,{B:function(){return a},D:function(){return s}});var n=t(43577);function a(e){var r,t,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(r=e.data)||void 0===r?void 0:r.message)&&void 0!==n?n:"",data:null===(t=e.data)||void 0===t?void 0:t.data}}function s(e){let r=500,t="Internal Server Error",a="An unknown error occurred";if((0,n.IZ)(e)){var s,l,i,o,d,c,u,x,m,h,p,f;r=null!==(m=null===(s=e.response)||void 0===s?void 0:s.status)&&void 0!==m?m:500,t=null!==(h=null===(l=e.response)||void 0===l?void 0:l.statusText)&&void 0!==h?h:"Internal Server Error",a=null!==(f=null!==(p=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(i=o[0])||void 0===i?void 0:i.message)&&void 0!==p?p:null===(x=e.response)||void 0===x?void 0:null===(u=x.data)||void 0===u?void 0:u.message)&&void 0!==f?f:e.message}else e instanceof Error&&(a=e.message);return{statusCode:r,statusText:t,status:!1,message:a,data:void 0,error:e}}},31117:function(e,r,t){"use strict";t.d(r,{d:function(){return s}});var n=t(79981),a=t(85323);let s=(e,r)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...r})},75730:function(e,r,t){"use strict";t.d(r,{Z:function(){return s}});var n=t(31117),a=t(99376);function s(e,r){var t,s,l;let i=(0,a.usePathname)(),o=(0,a.useSearchParams)(),d=(0,a.useRouter)(),[c,u]=e.split("?"),x=new URLSearchParams(u);x.has("page")||x.set("page","1"),x.has("limit")||x.set("limit","10");let m="".concat(c,"?").concat(x.toString()),{data:h,error:p,isLoading:f,mutate:j,...v}=(0,n.d)(m,r);return{refresh:()=>j(h),data:null!==(l=null==h?void 0:null===(t=h.data)||void 0===t?void 0:t.data)&&void 0!==l?l:[],meta:null==h?void 0:null===(s=h.data)||void 0===s?void 0:s.meta,filter:(e,r,t)=>{let n=new URLSearchParams(o.toString());r?n.set(e,r.toString()):n.delete(e),d.replace("".concat(i,"?").concat(n.toString())),null==t||t()},isLoading:f,error:p,...v}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,38658,42592,85210,58939,50438,227,92971,95030,1744],function(){return e(e.s=54700)}),_N_E=e.O()}]);