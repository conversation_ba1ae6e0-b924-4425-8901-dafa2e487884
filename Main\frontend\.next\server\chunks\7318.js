exports.id=7318,exports.ids=[7318],exports.modules={49119:(e,t,a)=>{Promise.resolve().then(a.bind(a,36141))},55746:(e,t,a)=>{Promise.resolve().then(a.bind(a,36141))},35303:()=>{},36141:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>es});var s=a(10326),l=a(14926),r=a(48054),n=a(55632),i=a(19395),c=a(74064),d=a(17577),o=a.n(d),u=a(74723),m=a(70012),x=a(27256),h=a(28758),p=a(33071),f=a(34474),j=a(54033);function g({form:e,name:t,avatar:a,agentId:l,commission:r,phoneNumber:n,processingTime:i,paymentMethods:c,onSelect:d}){let{t:o}=(0,m.$G)(),u=e.getFieldState("method")?.error?.message;return s.jsx(p.Zb,{children:(0,s.jsxs)(p.aY,{className:"px-4 py-4 text-foreground sm:px-6",children:[(0,s.jsxs)("div",{className:"mb-6 flex items-center gap-2",children:[(0,s.jsxs)(h.qE,{className:"size-10 border",children:[s.jsx(h.F$,{src:a,alt:t}),s.jsx(h.Q5,{children:(0,j.v)(t)})]}),s.jsx("span",{className:"font-bold",children:t})]}),(0,s.jsxs)("div",{className:"mb-8 grid grid-cols-12 gap-2 text-sm",children:[(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[s.jsx("p",{className:"opacity-70",children:o("Agent ID")}),s.jsx("p",{className:"font-medium",children:l})]}),(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[s.jsx("p",{className:"opacity-70",children:o("Phone")}),s.jsx("p",{className:"font-medium",children:n})]}),(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[s.jsx("p",{className:"opacity-70",children:o("Commission")}),(0,s.jsxs)("p",{className:"font-medium",children:[r??0,"%"]})]}),i?(0,s.jsxs)("div",{className:"col-span-6 sm:col-span-6",children:[s.jsx("p",{className:"opacity-70",children:o("Processing Time")}),s.jsx("p",{className:"font-medium",children:i})]}):null]}),(0,s.jsxs)(f.Ph,{onValueChange:d,children:[s.jsx(f.i4,{className:"data-[placeholder]:text-placeholder capitalize",children:s.jsx(f.ki,{placeholder:o("Select payment gateway")})}),s.jsx(f.Bw,{children:c?.filter(e=>!!e.active&&!!e.allowWithdraw)?.map(e=>s.jsx(f.Ql,{value:e.name,className:"capitalize",children:e.name},e.id))})]}),u?s.jsx("span",{className:"mt-2 block text-sm text-destructive",children:u}):null]})})}var y=a(5158),v=a(61718),N=a(92392),w=a(80609),b=a(90772),C=a(90799),S=a(47237),Z=a(3001),z=a(32894),I=a(77132),V=a(44221),P=a(44284);function $({form:e,setAgent:t,onBack:a,onNext:l}){let[r,n]=o().useState(),[i,c]=o().useState(!0),[d,u]=o().useState(""),{t:x}=(0,m.$G)(),h=e.getValues(),{data:p,isLoading:f}=(0,C.d)(`/agents/deposit?countryCode=${r?.code?.cca2}&currencyCode=${h.walletId}&search=${d}`);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"mb-5",children:x("Select a country first")}),s.jsx(y.J,{condition:i,children:s.jsx(v.g,{defaultCountry:e.getValues("country"),onSelectChange:t=>{e.setValue("country",t.code.cca2),n(t)}})}),s.jsx(y.J,{condition:!i,children:s.jsx("div",{children:(0,s.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"inline-flex gap-2",children:[s.jsx(w.W,{url:r?.flags?.png,className:"h-5 w-7"}),s.jsx("span",{children:r?.name}),(0,s.jsxs)("div",{className:"flex items-center gap-2.5 pl-2.5",children:[s.jsx(S.Z,{size:"16",className:"text-primary",variant:"Bold"}),x("Selected")]})]}),(0,s.jsxs)(b.z,{variant:"ghost",size:"sm",className:"gap-2 font-medium",onClick:()=>c(!0),children:[x("Change country"),s.jsx(Z.Z,{size:16})]})]})})})]}),s.jsx(y.J,{condition:f,children:s.jsx(N.Loader,{className:"mt-8 flex justify-center py-10"})}),(0,s.jsxs)(y.J,{condition:!!r,children:[(0,s.jsxs)("div",{className:"mt-8 flex w-full flex-col justify-between gap-4 md:flex-row md:items-center",children:[s.jsx("h2",{children:x("Select agent")}),(0,s.jsxs)("div",{className:"flex w-full max-w-[200px] items-center rounded-[4px] border",children:[s.jsx("input",{type:"text",placeholder:x("Search agent..."),value:d,onChange:e=>u(e.target.value),className:"h-10 w-full flex-1 rounded-[4px] px-2.5 text-sm focus:outline-none"}),s.jsx("div",{className:"rounded-r-[4px] px-2.5",children:s.jsx(z.Z,{size:"20",variant:"Outline"})})]})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-12 gap-y-4 sm:gap-4",children:[s.jsx(y.J,{condition:p?.data?.length===0,children:s.jsx("div",{className:"col-span-12 h-32 text-sm font-semibold text-secondary-text",children:(0,s.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[s.jsx(I.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),s.jsx("p",{children:x("No agent available for this country.")})]})})}),s.jsx(y.J,{condition:!!p?.data?.length,children:p?.data?.map(a=>s.jsx("div",{className:"col-span-12 md:col-span-6",children:s.jsx(g,{form:e,name:a.name,avatar:"",agentId:a.agentId,commission:a.withdrawalCommission,processingTime:"",phoneNumber:a?.user?.customer?.phone,paymentMethods:a.agentMethods,onSelect:s=>{e.setValue("method",s),t({...a,method:a?.agentMethods?.find(e=>e.name===s)})}})},a.id))})]})]}),(0,s.jsxs)("div",{className:"mt-8 flex items-center justify-between gap-4",children:[(0,s.jsxs)(b.z,{variant:"outline",onClick:a,children:[s.jsx(V.Z,{}),x("Back")]}),(0,s.jsxs)(b.z,{type:"button",onClick:l,children:[x("Next"),s.jsx(P.Z,{})]})]})]})}var k=a(74743),T=a(54432);function R({form:e,toggleWithdrawType:t,toggleTab:a}){let l=s=>{let l=0;e.watch("walletId")||(e.setError("walletId",{message:"Please select a wallet.",type:"custom"}),l+=1),e.watch("withdrawAmount")||(e.setError("withdrawAmount",{message:"Please enter withdraw amount.",type:"custom"},{shouldFocus:!0}),l+=1),0===l&&("regular"===s&&(t("regular"),a("review")),"agent"===s&&(t("agent"),a("agent_selection")))},{t:r}=(0,m.$G)();return(0,s.jsxs)("div",{className:"flex flex-col gap-4 md:gap-8",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[s.jsx("h2",{children:r("Select wallet")}),s.jsx(n.Wi,{name:"walletId",control:e.control,render:({field:e})=>(0,s.jsxs)(n.xJ,{children:[s.jsx(n.NI,{children:s.jsx(k.R,{value:e.value,onChange:e.onChange})}),s.jsx(n.zG,{})]})})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[s.jsx("h2",{children:r("How much?")}),s.jsx(n.Wi,{name:"withdrawAmount",control:e.control,render:({field:e})=>(0,s.jsxs)(n.xJ,{children:[s.jsx(n.NI,{children:s.jsx(T.I,{type:"number",placeholder:r("Enter withdraw amount"),...e})}),s.jsx(n.zG,{})]})})]}),(0,s.jsxs)("div",{className:"flex flex-col justify-end gap-2.5 md:flex-row",children:[s.jsx(b.z,{type:"button",variant:"outline",onClick:()=>l("agent"),children:r("Withdraw through an agent")}),(0,s.jsxs)(b.z,{type:"submit",onClick:()=>l("regular"),children:[r("Regular withdraw"),s.jsx(P.Z,{size:15})]})]})]})}var A=a(71227),_=a(8281),E=a(49547),G=a(10734);async function W(e){try{let t=await E.Z.post("/withdraw-requests/create",e);return(0,G.B)(t)}catch(e){return(0,G.D)(e)}}async function F(e){try{let t=await E.Z.post("/withdraws/create",e);return(0,G.B)(t)}catch(e){return(0,G.D)(e)}}var B=a(33436),J=a(35047),D=a(85999),L=a(43173),O=a(12649),M=a(77863);function Y({form:e,setActiveTab:t,method:a,type:l,currencyCode:r,agentId:n,amount:i}){let{t:c}=(0,m.$G)(),o=new M.F(r),[u,x]=(0,d.useState)();return(0,s.jsxs)(O.Y,{groupName:c("Withdraw details"),children:[s.jsx(O.r,{title:c("Amount"),value:o.formatVC(u?.chargedAmount??0)}),s.jsx(O.r,{title:c("Service charge"),value:o.formatVC(u?.fee??0)}),s.jsx(O.r,{title:c("You get"),value:o.formatVC(u?.recievedAmount??0),valueClassName:"text-xl sm:text-2xl font-semibold"})]})}var q=a(46226);function U({method:e,onEdit:t}){let{t:a}=(0,m.$G)();return s.jsx("div",{className:"col-span-12",children:(0,s.jsxs)("div",{className:"flex flex-col items-start justify-between gap-4 md:flex-row md:items-center",children:[(0,s.jsxs)("div",{className:"inline-flex w-full items-center justify-between gap-2 md:justify-start",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 rounded-xl border px-3 py-2.5",children:[e.logo&&s.jsx(q.default,{src:e.logo,alt:e.name,width:100,height:100,className:"size-8 rounded-lg"}),s.jsx("span",{className:"font-semibold capitalize",children:e?.name})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2.5 pl-2.5",children:[s.jsx(S.Z,{size:"16",className:"text-primary",variant:"Bold"}),a("Selected")]})]}),(0,s.jsxs)(b.z,{type:"button",variant:"ghost",size:"sm",className:"gap-2 font-medium",onClick:t,children:[a("Change"),s.jsx(Z.Z,{size:16})]})]})})}var Q=a(567);function H({logo:e,type:t,name:a,fee:l,isRecommended:r=!1,defaultSelect:n="",onSelect:i}){let{t:c}=(0,m.$G)();return(0,s.jsxs)("div",{"data-active":t===n,className:"relative flex h-full w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 p-5 transition-all duration-700 ease-in-out hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[s.jsx("input",{type:"radio",checked:t===n,onChange:()=>i(t),className:"absolute inset-0 left-0 top-0 cursor-pointer opacity-0"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e?s.jsx(q.default,{src:e,alt:a,width:100,height:100,className:"size-8"}):null,s.jsx("h5",{className:"text",children:a})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2.5 py-2.5",children:[(0,s.jsxs)("p",{children:[c("Fee")," ",l]}),r?s.jsx(Q.C,{variant:"success",children:c("Recommended")}):null]})]})}function K({form:e,selectMethod:t,method:a,setMethod:l}){let[r,n]=(0,d.useState)();return(0,s.jsxs)(s.Fragment,{children:[s.jsx(v.g,{defaultCountry:e.getValues("country"),onSelectChange:t=>{n(t),e.setValue("country",t.code.cca2)}}),s.jsx(X,{selectedMethod:a,country:r,form:e,setSelectedMethod:l,onSelect:t})]})}function X({selectedMethod:e,country:t,setSelectedMethod:a,form:l,onSelect:r}){let[n,i]=(0,d.useState)(!0),[c,o]=(0,d.useState)(),{t:u}=(0,m.$G)(),{data:x,isLoading:h}=(0,C.d)(`/withdraws/methods/list?country=${t?.code?.cca2}&currency=${l.getValues("walletId")}`),p=new M.F;if(h)return s.jsx("div",{className:"flex items-center justify-center py-10",children:s.jsx(N.Loader,{})});let f=x?.data?.filter(e=>!!e.active);return f?.length?s.jsx("div",{className:"mt-8 grid grid-cols-12 gap-4",children:n?f?.map(t=>s.jsx("div",{className:"col-span-6",children:s.jsx(H,{name:t.name,logo:M.qR(t.logoImage),type:t.value,isRecommended:!!t.recommended,fee:`${t.percentageCharge}% + ${p.formatVC(t.fixedCharge,t.currencyCode)}`,defaultSelect:e,onSelect:e=>{o(t),a(e),i(!1),r(t)}})},t.id)):s.jsx(U,{method:{name:c?.name,logo:(0,M.qR)(c?.logoImage)},onEdit:()=>i(!0)})}):s.jsx("div",{className:"flex items-center justify-center py-10",children:(0,s.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[s.jsx(I.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),s.jsx("p",{children:u("No methods available for this country.")})]})})}function ee({form:e,setActiveTab:t,agent:a,methodData:l,selectMethod:r,updateStateToComplete:i,withdrawType:c,onBack:d}){let[u,x]=o().useTransition(),[h,p]=o().useState(e.getValues("method")),{t:f}=(0,m.$G)(),j=(0,J.useRouter)(),[g,v]=o().useState(),w=()=>!!e.getValues("country")||(D.toast.error("Country is required"),!1),C=async e=>{x(async()=>{let t={agentId:a?.agentId?a?.agentId:"",method:e.method,inputValue:e.inputValue??"",amount:Number(e.withdrawAmount),currencyCode:e.walletId,countryCode:e.country},s=await W(t);s.status?(i("review"),j.push(`/withdraw/transaction-status?trxId=${s.data?.trxId}`)):D.toast.error(s.message)})},S=async e=>{x(async()=>{let t={method:h,amount:Number(e.withdrawAmount??0),currencyCode:e.walletId,country:e.country,inputParams:g},a=await F(t);a.status?(i("review"),j.push(`/withdraw/transaction-status?trxId=${a.data?.trxId}`)):D.toast.error(a.message)})},Z=e=>{"agent"===c?C(e):S(e)};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(y.J,{condition:"regular"===c,children:s.jsx(K,{form:e,selectMethod:r,method:h,setMethod:p})}),s.jsx(y.J,{condition:"agent"===c,children:s.jsx(U,{method:{name:a?.method?.name,logo:""},onEdit:()=>t("agent_selection")})}),s.jsx(_.Z,{className:"my-8"}),s.jsx(Y,{form:e,setActiveTab:t,method:h,type:c,agentId:a?.agentId?a.agentId:"",currencyCode:e.getValues("walletId"),amount:Number(e.getValues("withdrawAmount")??0)}),(0,s.jsxs)(y.J,{condition:"agent"===c,children:[s.jsx(_.Z,{className:"my-8"}),(0,L.EQ)(a?.method).with({inputType:"phone"},()=>(0,s.jsxs)("div",{className:"mt-8 flex w-full flex-col",children:[(0,s.jsxs)("h2",{children:[f("Enter phone number"),"?"]}),s.jsx("p",{className:"mb-4",children:f("(Enter your number without country indicator)")}),s.jsx(n.Wi,{name:"inputValue",control:e.control,render:({field:t})=>s.jsx(n.xJ,{children:s.jsx(n.NI,{children:s.jsx("div",{className:"relative",children:s.jsx(A.E,{onChange:e=>{if(!e){let a=(0,B.S)(e);t.onChange(a?.number)}},options:{initialCountry:e.getValues("country")}})})})})})]})).with({inputType:"email"},()=>(0,s.jsxs)("div",{className:"mt-8 flex w-full flex-col",children:[(0,s.jsxs)("h2",{children:[f("Enter email address"),"?"]}),s.jsx(T.I,{type:"email",name:"email",placeholder:f("Enter email address"),value:e.getValues("inputValue"),onChange:t=>e.setValue("inputValue",t.target.value)})]})).with({inputType:"other"},()=>(0,s.jsxs)("div",{className:"mt-8 flex w-full flex-col",children:[(0,s.jsxs)("h2",{children:[f(`Enter email ${a?.method?.otherName}`),"?"]}),s.jsx(T.I,{type:"text",name:a?.method?.otherName,value:e.getValues("inputValue"),onChange:t=>e.setValue("inputValue",t.target.value)})]})).otherwise(()=>null)]}),s.jsx(y.J,{condition:"regular"===c,children:s.jsx(et,{params:l?.params?JSON.parse(l.params):null,dynamicField:g,setDynamicField:v})}),s.jsx(_.Z,{className:"my-8"}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(b.z,{type:"button",variant:"outline",onClick:d,children:[s.jsx(V.Z,{}),f("Back")]}),(0,s.jsxs)(b.z,{type:"button",disabled:u,onClick:()=>{w()&&e.handleSubmit(Z)()},children:[u?s.jsx(N.Loader,{title:f("Processing..."),className:"text-primary-foreground"}):f("Withdraw"),s.jsx(P.Z,{})]})]})]})}function et({params:e,dynamicField:t,setDynamicField:a}){let{t:l}=(0,m.$G)();return e?(0,s.jsxs)(s.Fragment,{children:[s.jsx(_.Z,{className:"mt-6"}),s.jsx("div",{className:"mt-8 flex flex-col gap-4",children:e?.map((e,r)=>s.jsxs("div",{className:"flex w-full flex-col",children:[s.jsx("h3",{className:"mb-3",children:l(e.label)}),s.jsx(T.I,{type:e.type,name:e.name,value:t?.[e.name],placeholder:`Enter ${e.label}`,onChange:e=>{a(t=>({...t,[e.target.name]:e.target.value}))}})]},r))})]}):null}let ea=x.z.object({walletId:x.z.string().min(1,"Wallet ID is required."),withdrawAmount:x.z.string().min(1,"Withdraw amount is required."),method:x.z.string(),country:x.z.string(),phone:x.z.string().optional(),inputValue:x.z.string().optional()});function es(){let{auth:e,deviceLocation:t}=(0,i.a)(),{t:a}=(0,m.$G)(),[d,x]=o().useState("withdraw_details"),[h,p]=o().useState("regular"),[f,j]=o().useState(null),[g,y]=o().useState(),[v,N]=o().useState([{value:"withdraw_details",title:a("Withdraw Details"),complete:!1},{value:"agent_selection",title:a("Agent Selection"),isVisible:!1,complete:!1},{value:"review",title:a("Payment & Review"),complete:!1}]),w=(0,u.cI)({resolver:(0,c.F)(ea),mode:"all",defaultValues:{walletId:"",withdrawAmount:"",method:"",country:""}}),b=o().useCallback(e=>{N(v.map(t=>t.value===e?{...t,complete:!0}:t))},[v]);return e?.canMakeWithdraw()?s.jsx(n.l0,{...w,children:s.jsx("form",{className:"md:h-full",children:s.jsx("div",{className:"w-full p-4 pb-10 md:h-full md:p-12",children:s.jsx("div",{className:"mx-auto max-w-3xl",children:s.jsx(r.R,{tabs:v,onTabChange:e=>x(e),value:d,children:(0,s.jsxs)("div",{className:"p-4",children:[s.jsx(r.Q,{value:"withdraw_details",children:s.jsx(R,{form:w,toggleTab:e=>{x(e),b("withdraw_details")},toggleWithdrawType:e=>p(e)})}),s.jsx(r.Q,{value:"agent_selection",children:s.jsx($,{form:w,setAgent:j,onBack:()=>x("withdraw_details"),onNext:()=>{if("agent"===h&&!w.getValues("method")){w.setError("method",{message:a("Select a method to continue."),type:"required"});return}x("review"),b("agent_selection")}})}),s.jsx(r.Q,{value:"review",children:s.jsx(ee,{form:w,agent:f,methodData:g,selectMethod:y,setActiveTab:x,updateStateToComplete:b,withdrawType:h,onBack:()=>x("agent"===h?"agent_selection":"withdraw_details")})})]})})})})})}):s.jsx(l.Z,{className:"flex-1 p-10"})}},14926:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var s=a(10326),l=a(77863),r=a(90434),n=a(70012);function i({className:e}){let{t}=(0,n.$G)();return s.jsx("div",{className:(0,l.ZP)("flex items-center justify-center",e),children:(0,s.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[s.jsx("h3",{className:"mb-2.5",children:t("This feature is temporarily unavailable")}),(0,s.jsxs)("p",{className:"text-sm text-secondary-text",children:[t("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),s.jsx(r.default,{href:"/contact-supports",className:"text-primary hover:underline",children:t("support")}),"."]}),s.jsx("p",{className:"mt-2 text-sm text-secondary-text",children:t("Thank you for your understanding.")})]})})}},61718:(e,t,a)=>{"use strict";a.d(t,{g:()=>x});var s=a(10326),l=a(17577),r=a(92392),n=a(80609),i=a(2454),c=a(30811),d=a(1868),o=a(77863),u=a(6216),m=a(70012);function x({allCountry:e=!1,defaultValue:t,defaultCountry:a,onSelectChange:x,disabled:h=!1,triggerClassName:p,arrowClassName:f,flagClassName:j,display:g,placeholderClassName:y,align:v="start",side:N="bottom"}){let{t:w}=(0,m.$G)(),{countries:b,getCountryByCode:C,isLoading:S}=(0,d.F)(),[Z,z]=l.useState(!1),[I,V]=l.useState(t);return l.useEffect(()=>{t&&V(t)},[t]),l.useEffect(()=>{(async()=>{a&&await C(a,e=>{e&&(V(e),x(e))})})()},[a]),(0,s.jsxs)(c.J2,{open:Z,onOpenChange:z,children:[(0,s.jsxs)(c.xo,{disabled:h,className:(0,o.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",p),children:[I?s.jsx("div",{className:"flex flex-1 items-center",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[s.jsx(n.W,{className:j,countryCode:I.code?.cca2==="*"?"UN":I.code?.cca2}),void 0!==g?g(I):s.jsx("span",{children:I.name})]})}):s.jsx("span",{className:(0,o.ZP)("text-placeholder",y),children:w("Select country")}),s.jsx(u.Z,{className:(0,o.ZP)("size-6",f)})]}),s.jsx(c.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:v,side:N,children:(0,s.jsxs)(i.mY,{children:[s.jsx(i.sZ,{placeholder:w("Search...")}),s.jsx(i.e8,{children:(0,s.jsxs)(i.fu,{children:[S&&s.jsx(r.Loader,{}),e&&(0,s.jsxs)(i.di,{value:w("All countries"),onSelect:()=>{V({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),x({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),z(!1)},children:[s.jsx(n.W,{countryCode:"UN"}),s.jsx("span",{className:"pl-1.5",children:w("All countries")})]}),b?.map(e=>"officially-assigned"===e.status?s.jsxs(i.di,{value:e.name,onSelect:()=>{V(e),x(e),z(!1)},children:[s.jsx(n.W,{countryCode:e.code.cca2}),s.jsxs("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},71227:(e,t,a)=>{"use strict";a.d(t,{E:()=>S});var s=a(10326),l=a(92392),r=a(80609),n=a(2454),i=a(54432),c=a(30811),d=a(1868),o=a(77863),u=a(26138),m=a(6216),x=a(33436),h=a(34197),p=a(4017),f=a(70107),j=a(55991),g=a(4981),y=a(71132),v=a(60715),N=a(5389),w=a(70012),b=a(17577);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function S({value:e,defaultValue:t="",onChange:a,onBlur:l,disabled:r,inputClassName:n,options:c}){let[d,u]=(0,b.useState)(t??""),[m,v]=(0,b.useState)(""),[w,S]=(0,b.useState)(c?.initialCountry),z=e=>{if(e)try{let t=x.S(e,w);t?(S(t.country),v(`+${t.countryCallingCode}`),u(t.formatNational())):u(e)}catch(t){u(e)}else u(e)},I=h.L(w||c?.initialCountry||"US",N.Z);return(0,s.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(Z,{country:w,disabled:r,initialCountry:c?.initialCountry,onSelect:e=>{let t=e.code.cca2?.toUpperCase(),a=p.G(t);v(`+${a}`),S(t)}}),s.jsx("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:m||`+${I?.countryCallingCode}`})]}),s.jsx(i.I,{type:"tel",className:(0,o.ZP)("rounded-l-none pl-2",n),value:d,onChange:e=>{let{value:t}=e.target,s=x.S(t,w);l?.(""),s&&f.t(t,w)&&j.q(t,w)?(S(s.country),v(`+${s.countryCallingCode}`),a?.(s.number),u(t)):(s?u(s.nationalNumber):u(t),a?.(t))},onPaste:e=>{let t=e.clipboardData.getData("Text"),s=x.S(t);if(s&&f.t(t))z(s.formatNational()),S(s.country),v(`+${s.countryCallingCode}`),a?.(s.number),l?.("");else{let e=x.S(t,w);e&&f.t(t,w)&&(z(e.formatNational()),a?.(e.number),l?.(""))}},onBlur:()=>{if(d&&!g.y(d,w)){let e=y.d(d,w);e&&l?.(C[e])}},placeholder:I?.formatNational(),disabled:r})]})}function Z({initialCountry:e,country:t,onSelect:a,disabled:l}){let[n,i]=(0,b.useState)(!1);return(0,s.jsxs)(c.J2,{open:n,onOpenChange:i,children:[(0,s.jsxs)(c.xo,{disabled:l,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[e||t?s.jsx(r.W,{countryCode:t||e,className:"aspect-auto h-[18px] w-7 flex-1"}):s.jsx(u.Z,{}),s.jsx(m.Z,{variant:"Bold",size:16})]}),s.jsx(c.yk,{align:"start",className:"h-fit p-0",children:s.jsx(z,{defaultValue:t||e,onSelect:e=>{a(e),i(!1)}})})]})}function z({defaultValue:e,onSelect:t}){let{countries:a,isLoading:i}=(0,d.F)(),{t:c}=(0,w.$G)();return(0,s.jsxs)(n.mY,{children:[s.jsx(n.sZ,{placeholder:c("Search country by name"),className:"placeholder:text-input-placeholder"}),s.jsx(n.e8,{children:s.jsx(n.fu,{children:i?s.jsx(n.di,{children:s.jsx(l.Loader,{})}):a.filter(e=>{let t=e.code.cca2?.toUpperCase();return v.o().includes(t)})?.map(a=>s.jsxs(n.di,{value:a.name,"data-active":a.code.cca2===e,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>t(a),children:[s.jsx(r.W,{countryCode:a.code.cca2}),a.name]},a.code.ccn3))})})]})}},80609:(e,t,a)=>{"use strict";a.d(t,{W:()=>n});var s=a(10326),l=a(77863),r=a(46226);function n({countryCode:e,className:t,url:a}){return e||a?s.jsx(r.default,{src:a??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,l.ZP)("rounded-[2px]",t)}):null}},33071:(e,t,a)=>{"use strict";a.d(t,{Ol:()=>i,SZ:()=>d,Zb:()=>n,aY:()=>o,eW:()=>u,ll:()=>c});var s=a(10326),l=a(17577),r=a(77863);let n=l.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,r.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let i=l.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,r.ZP)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let c=l.forwardRef(({className:e,...t},a)=>s.jsx("h3",{ref:a,className:(0,r.ZP)("text-2xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let d=l.forwardRef(({className:e,...t},a)=>s.jsx("p",{ref:a,className:(0,r.ZP)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let o=l.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,r.ZP)("p-6 pt-0",e),...t}));o.displayName="CardContent";let u=l.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,r.ZP)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},34474:(e,t,a)=>{"use strict";a.d(t,{Bw:()=>f,Ph:()=>u,Ql:()=>j,i4:()=>x,ki:()=>m});var s=a(10326),l=a(13869),r=a(96633),n=a(941),i=a(17577),c=a(77863),d=a(6216),o=a(44284);let u=l.fC;l.ZA;let m=l.B4,x=i.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(l.xz,{ref:r,className:(0,c.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,s.jsx(l.JO,{asChild:!0,children:s.jsx(d.Z,{size:"24",color:"#292D32"})})]}));x.displayName=l.xz.displayName;let h=i.forwardRef(({className:e,...t},a)=>s.jsx(l.u_,{ref:a,className:(0,c.ZP)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(r.Z,{className:"h-4 w-4"})}));h.displayName=l.u_.displayName;let p=i.forwardRef(({className:e,...t},a)=>s.jsx(l.$G,{ref:a,className:(0,c.ZP)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(n.Z,{className:"h-4 w-4"})}));p.displayName=l.$G.displayName;let f=i.forwardRef(({className:e,children:t,position:a="popper",...r},n)=>s.jsx(l.h_,{children:(0,s.jsxs)(l.VY,{ref:n,className:(0,c.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[s.jsx(h,{}),s.jsx(l.l_,{className:(0,c.ZP)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),s.jsx(p,{})]})}));f.displayName=l.VY.displayName,i.forwardRef(({className:e,...t},a)=>s.jsx(l.__,{ref:a,className:(0,c.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=l.__.displayName;let j=i.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(l.ck,{ref:r,className:(0,c.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(l.wU,{children:s.jsx(o.Z,{variant:"Bold",className:"h-4 w-4"})})}),s.jsx(l.eT,{children:t})]}));j.displayName=l.ck.displayName,i.forwardRef(({className:e,...t},a)=>s.jsx(l.Z0,{ref:a,className:(0,c.ZP)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=l.Z0.displayName},1868:(e,t,a)=>{"use strict";a.d(t,{F:()=>d});class s{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var l=a(44099),r=a(85999),n=a(84455);let i=l.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),c="name,cca2,ccn3,cca3,status,flag,flags";function d(){let{data:e,isLoading:t,...a}=(0,n.ZP)(`/all?fields=${c}`,e=>i.get(e)),d=e?.data,o=async(e,t)=>{try{let a=await i.get(`/alpha/${e.toLowerCase()}?fields=${c}`),l=a.data?new s(a.data):null;t(l)}catch(e){l.default.isAxiosError(e)&&r.toast.error("Failed to fetch country")}};return{countries:d?d.map(e=>new s(e)):[],isLoading:t,getCountryByCode:o,...a}}},84514:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var s=a(19510),l=a(40099),r=a(76609);function n({children:e}){return(0,s.jsxs)("div",{className:"flex h-screen",children:[s.jsx(r.Z,{userRole:"agent"}),(0,s.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[s.jsx(l.Z,{}),s.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}a(71159)},18406:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(19510),l=a(48413);function r(){return s.jsx("div",{className:"flex items-center justify-center py-10",children:s.jsx(l.a,{})})}},75095:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(19510),l=a(18559);function r(){return s.jsx(l.default,{})}},88728:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var s=a(19510),l=a(40099),r=a(76609);function n({children:e}){return(0,s.jsxs)("div",{className:"flex h-screen",children:[s.jsx(r.Z,{userRole:"customer"}),(0,s.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[s.jsx(l.Z,{}),s.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}a(71159)},80549:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(19510),l=a(48413);function r(){return s.jsx("div",{className:"flex items-center justify-center py-10",children:s.jsx(l.a,{})})}},19411:(e,t,a)=>{"use strict";function s({children:e}){return e}a.r(t),a.d(t,{default:()=>s})},13149:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(19510),l=a(48413);function r(){return s.jsx("div",{className:"flex items-center justify-center py-10",children:s.jsx(l.a,{})})}},18559:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\withdraw\page.tsx#default`)},86893:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(19510),l=a(18559);function r(){return s.jsx(l.default,{})}}};