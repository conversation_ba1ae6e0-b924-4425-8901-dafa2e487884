{"Welcome to {{siteName}}": "Welcome to {{siteName}}", "Welcome to {{siteName}}, let's get start": "Welcome to {{siteName}}, let's get started", "Cards": "Cards", "No more": "No more", "Completed": "Completed", "Deposit gateway": "Deposit gateway", "Withdraw method": "Withdraw method", "Agent method": "Agent method", "Done": "Done", "Plugins": "Plugins", "Site Settings": "Site Settings", "Disbursements": "Disbursements", "Investments History": "Investments History", "Favorites": "Favorites", "Deposit to Customer": "Deposit to Customer", "Share this referral link to your friends and earn money.": "Share this referral link to your friends and earn money.", "No favorite item": "No favorite item", "Are you sure you want to issue a card for this wallet?": "Are you sure you want to issue a card for this wallet?", "Card Not Available": "Card Not Available", "Issue Card": "Issue Card", "Confirm Your Card": "Confirm Your Card", "By Agent": "By Agent", "Hours": "Hours", "Charges": "Charges", "Pick a date": "Pick a date", "Account Settings": "Account <PERSON><PERSON>", "Charges/Commissions": "Charges/Commissions", "Sex": "Sex", "Charges and commissions": "Charges and commissions", "Agent withdrawal charge (Long distance)": "Agent withdrawal charge (Long distance)", "Agent deposit charge (Long distance)": "Agent deposit charge (Long distance)", "Withdraw details": "Withdraw details", "Withdraw again": "Withdraw again", "Your QR Code": "Your QR Code", "Total merchant payments received": "Total merchant payments received", "Received": "Received", "Total payments today": "Total payments today", "Customers can scan this QR code to make payments.": "Customers can scan this QR code to make payments.", "To make payment, scan the QR Code.": "To make payment, scan the QR Code.", "Total Earnings": "Total Earnings", "Total bonus": "Total bonus", "Bulk withdraw": "Bulk withdraw", "Disburse Now": "Disburse Now", "MPay API": "MPay API", "Other name": "Other name", "Write required field name": "Write required field name", "Enter method value": "Enter method value", "Enter method name": "Enter method name", "Enter currency": "Enter currency", "Your account is not yet verified. Please complete the KYC process by submitting the required documents.": "Your account is not yet verified. Please complete the KYC process by submitting the required documents.", "Thank you for submitting your documents! Your KYC verification is currently under review. Our team is working to process your submission as quickly as possible.": "Thank you for submitting your documents! Your KYC verification is currently under review. Our team is working to process your submission as quickly as possible.", "Your account has been successfully verified. ": "Your account has been successfully verified. ", "Your account has been successfully verified. If you have any questions feel free to reach out to our support team.": "Your account has been successfully verified. If you have any questions feel free to reach out to our support team.", "Card ID": "Card ID", "Issue date": "Issue date", "Exp. Date": "Exp. Date", "Payment Cards": "Payment Cards", "CVV": "CVV", "Interest Rate": "Interest Rate", "Amount Invested": "Amount Invested", "Duration Type": "Duration Type", "Duration": "Duration", "Profit": "Profit", "Matured Withdraw": "Matured Withdraw", "Merchant Transactions": "Merchant Transactions", "Electricity bill": "Electricity bill", "No cards available": "No cards available", "My Investments": "My Investments", "Available Plans": "Available Plans", "No investments available!": "No investments available!", "Range": "Range", "Profit Adjust": "Profit <PERSON>", "Withdraw After Matured": "Withdraw After Matured", "Featured": "Featured", "Days": "Days", "Max Amount": "<PERSON>", "Invest Now": "Invest Now", "No description": "No description", "Required Amount": "Required Amount", "View details": "View details", "Invest": "Invest", "Fixed": "Fixed", "Min Amount": "<PERSON>", "No cards found!": "No cards found!", "Favorite phone numbers": "Favorite phone numbers", "Enter recharge amount": "Enter recharge amount", "Meter Details": "Meter Details", "Click to autofill recipient": "Click to autofill recipient", "from dashboard": "from dashboard", "Document type": "Document type", "Card Management": "Card Management", "Profit rate": "Profit rate", "Opening date": "Opening date", "Edit investment plan": "Edit investment plan", "Term": "Term", "Plan": "Plan", "Manage Plans": "Manage Plans", "Are you sure you want to delete this customer? This action cannot be undone, and all associated data will be permanently removed.": "Are you sure you want to delete this customer? This action cannot be undone, and all associated data will be permanently removed.", "Delete Customer Confirmation": "Delete Customer Confirmation", "Continue": "Continue", "Pending Verification": "Pending Verification", "Site Info": "Site Info", "Site Logo": "Site Logo", "Upload logo": "Upload logo", "Favicon": "Favicon", "Card Background": "Card Background", "Upload favicon": "Upload favicon", "Sign In Page Banner": "Sign In Page Banner", "Upload background": "Upload background", "Upload banner": "Upload banner", "Site Name": "Site Name", "Enter Site Name": "Enter Site Name", "Site Url": "Site Url", "Enter Site Url": "Enter Site Url", "Enter API Url": "Enter API Url", "API Url": "API Url", "Referral Bonus Amount": "Referral Bonus Amount", "Apply Referral": "Apply Referral", "First deposit": "First deposit", "Referrer": "<PERSON><PERSON><PERSON>", "Verify email": "Verify email", "Default Currency": "<PERSON><PERSON><PERSON>", "Select referral": "Select referral", "Both": "Both", "Referral Bonus Receiver": "Referral Bonus Receiver", "Customer Registration": "Customer Registration", "Default Language": "Default Language", "Select language": "Select language", "Agent Registration": "Agent Registration", "Enter referral bonus amount": "Enter referral bonus amount", "Merchant Registration": "Merchant Registration", "Deposit Commission": "Deposit Commission", "Default fee": "Default fee", "Withdraw Commission": "Withdraw Commission", "Virtual Card": "Virtual Card", "Select card provider": "Select card provider", "Card Provider": "Card Provider", "Min. Amount": "<PERSON><PERSON>", "Edit": "Edit", "Kyc Limit": "Kyc Limit", "Crypto": "Crypto", "Max. Amount": "<PERSON><PERSON>", "USD Rate": "USD Rate", "times": "times", "Edit Currency": "<PERSON>", "Checking...": "Checking...", "Enter recipient’s email": "Enter recipient’s email", "Favorite merchants": "Favorite merchants", "Click to autofill merchant": "Click to autofill merchant", "Enter merchant account": "Enter merchant account", "Contact number": "Contact number", "Last Name": "Last Name", "First Name": "First Name", "Zip Code": "Zip Code", "Deactivated": "Deactivated", "Daily transfer limit": "Daily transfer limit", "Verification Successful": "Verification Successful", "Select your gender": "Select your gender", "Select your birth date": "Select your birth date", "Your full mailing address": "Your full mailing address", "URL is not defined": "URL is not defined", "Add to contact": "Add to contact", "You can add customers to the block list to prevent them from using the platform.": "You can add customers to the block list to prevent them from using the platform.", "Gateway logo": "Gateway logo", "Enter {{label}}": "Enter {{label}}", "Public Key": "Public Key", "Master Key": "Master Key", "Private Key": "Private Key", "Token": "Token", "Webhook Id": "Webhook Id", "Secret Key": "Secret Key", "Live": "Live", "Mode": "Mode", "Sandbox": "Sandbox", "Add to blacklist": "Add to blacklist", "Id": "Id", "Deposit Request": "Deposit Request", "Withdraw Request": "Withdraw Request", "Amount Sent": "Amount <PERSON>", "Processing Time (Hours)": "Processing Time (Hours)", "Agent name": "Agent name", "Enter processing time": "Enter processing time", "Amount received": "Amount received", "The inputType field must be defined": "The inputType field must be defined", "Close Card": "Close Card", "Expiry Date": "Expiry Date", "Card Details": "Card Details", "Expiry date": "Expiry date", "Card holder name": "Card holder name", "View Card": "View Card", "Deposit Money": "Deposit Money", "Copy Number": "Copy Number", "Card Type": "Card Type", "Contact Supports": "Contact Supports", "Account Number": "Account Number", "Account Name": "Account Name", "Bank Name": "Bank Name", "IBAN": "IBAN", "Enter exchange amount": "Enter exchange amount", "Investment return": "Investment return", "Referral bonus": "Referral bonus", "No methods available for this country.": "No methods available for this country.", "Fixed charge": "Fixed charge", "Name (Unique)": "Name (Unique)", "Method logo": "Method logo", "Method Fields": "Method Fields", "Method name": "Method name", "Field type": "Field type", "Method details": "Method details", "Blacklist": "Blacklist", "payload.uploadLogo.move is not a function": "payload.uploadLogo.move is not a function", "Creating...": "Creating...", "Select a payment gateway": "Select a payment gateway", "Select a method to continue.": "Select a method to continue.", "The params field must be a string": "The params field must be a string", "Receiver will get": "Receiver will get", "You are not allowed to deposit more than 250 USD": "You are not allowed to deposit more than 250 USD", "Balance insufficiant": "Balance insufficiant", "The countryCode field must be defined": "The countryCode field must be defined", "No agent available for this country.": "No agent available for this country.", "Withdraw approved": "Withdraw approved", "Accept withdraw": "Accept withdraw", "Additional info": "Additional info", "Reject withdraw": "Reject withdraw", "The params field must be an array": "The params field must be an array", "Invalid file extension svg. Only jpg, png, jpeg, webp are allowed": "Invalid file extension svg. Only jpg, png, jpeg, webp are allowed", "I read and accept the general terms & conditions of use": "I read and accept the general terms & conditions of use", "We provide funding for your AGENT account, you must be able to pay 50% of that amount to fund the customers, do you agree?": "We provide funding for your AGENT account, you must be able to pay 50% of that amount to fund the customers, do you agree?", "You must be always available to recharge account for our customers, do you agree?": "You must be always available to recharge account for our customers, do you agree?", "Other": "Other", "How much money do you want to start with? (USD)": "How much money do you want to start with? (USD)", "You must be honest, do you agree?": "You must be honest, do you agree?", "Transfer limit": "Transfer limit", "Add balance": "Add balance", "Daily transfer amount": "Daily transfer amount", "Remove balance": "Remove balance", "Transfer amount limit": "Transfer amount limit", "Enter merchant license or register number": "Enter merchant license or register number", "Merchant proof": "Merchant proof", "Enter merchant name": "Enter merchant name", "Address line": "Address line", "Enter merchant email": "Enter merchant email", "Invalid file extension JPEG. Only jpg, png, jpeg, webp are allowed": "Invalid file extension JPEG. Only jpg, png, jpeg, webp are allowed", "Please provide all required field.": "Please provide all required field.", "Please wait": "Please wait", "Request is processing...": "Request is processing...", "Currency must be different.": "Currency must be different.", "Notification limit (Optional)": "Notification limit (Optional)", "Enter kyc limit": "Enter kyc limit", "Enter daily transfer limit": "Enter daily transfer limit", "Enter daily transfer amount": "Enter daily transfer amount", "Enter USD rate": "Enter USD rate", "Create Currency": "Create C<PERSON><PERSON>cy", "Enter notification limit": "Enter notification limit", "Is Crypto": "Is Crypto", "Kyc limit (Optional)": "Kyc limit (Optional)", "Accept API rate": "Accept API rate", "Show less": "Show less", "User not found.": "User not found.", "Pay Now": "Pay Now", "Add to favorites": "Add to favorites", "Payment details": "Payment details", "This merchant is not at service": "This merchant is not at service", "Loading...": "Loading...", "Withdraw Requests": "Withdraw Requests", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Transfer": "Transfer", "Exchange": "Exchange", "Withdraw": "Withdraw", "Contacts": "Contacts", "Referral": "Referral", "Services": "Services", "Deposit Requests": "Deposit Requests", "Settlements": "Settlements", "Transaction History": "Transaction History", "Merchant Transaction": "Merchant Transaction", "Log out": "Log out", "Investments": "Investments", "Settings": "Settings", "Support": "Support", "Wallets": "Wallets", "Stay updated with alerts and offers. Customize your notifications.": "Stay updated with alerts and offers. Customize your notifications.", "Payment Requests": "Payment Requests", "Mark all as read": "Mark all as read", "Notifications": "Notifications", "Select up to 5 contacts to add them in the Quick Send list.": "Select up to 5 contacts to add them in the Quick Send list.", "Copy link": "Copy link", "Refer a friend": "Refer a friend", "Search...": "Search...", "Quick Send": "Quick Send", "See all": "See all", "After Processing": "After Processing", "Amount": "Amount", "Fee": "Fee", "Status": "Status", "Payment": "Payment", "Dashboard": "Dashboard", "Type": "Type", "Show all wallets": "Show all wallets", "Cancel": "Cancel", "Balance": "Balance", "Deposits": "Deposits", "Withdraws": "Withdraws", "Pending": "Pending", "Exchanges": "Exchanges", "Payments": "Payments", "Customers": "Customers", "Customer List": "Customer List", "Bulk Email": "Bulk Email", "Agents": "Agents", "Merchant List": "Merchant List", "Merchants": "Merchants", "Transfers": "Transfers", "Pending Kyc": "Pending Kyc", "Staffs": "Staffs", "Payment Request": "Payment Request", "Total Withdraws": "Total Withdraws", "Total Deposits": "Total Deposits", "Agent List": "Agent List", "Recently registered": "Recently registered", "Total Transfers": "Total Transfers", "Merchant": "Merchant", "Customer": "Customer", "History": "History", "Total Exchanges": "Total Exchanges", "Recent transactions": "Recent transactions", "Active": "Active", "Failed": "Failed", "Gender": "Gender", "Clear Filter": "Clear Filter", "Export": "Export", "Agent": "Agent", "Filter": "Filter", "Country": "Country", "Male": "Male", "Inactive": "Inactive", "Method": "Method", "Date": "Date", "User": "User", "View": "View", "Showing {{start}}-{{end}} of {{total}}": "Showing {{start}}-{{end}} of {{total}}", "Trx ID": "Trx ID", "Female": "Female", "Transaction ID": "Transaction ID", "User gets": "User gets", "Reject deposit": "Reject deposit", "Service charge": "Service charge", "Deposit failed": "Depo<PERSON><PERSON> failed", "Accept deposit": "Accept deposit", "Deposit approved": "Deposit approved", "Method used": "Method used", "Method info": "Method info", "Wallet": "Wallet", "Received by": "Received by", "Send by": "Send by", "No data found!": "No data found!", "Send": "Send", "Withdraw failed": "Withdraw failed", "Number": "Number", "Orange Money CIV fee": "Orange Money CIV fee", "Payment method": "Payment method", "Review": "Review", "Enter deposit amount": "Enter deposit amount", "Next": "Next", "Show more": "Show more", "Your Balance": "Your Balance", "Select wallet": "Select wallet", "How much?": "How much?", "Enter transfer amount": "Enter transfer amount", "Add recipient": "Add recipient", "Finish": "Finish", "Transfer details": "Transfer details", "Bookmarks": "Bookmarks", "Add amount": "Add amount", "Hide bookmarks": "Hide bookmarks", "Regular withdraw": "Regular withdraw", "Enter withdraw amount": "Enter withdraw amount", "Agent Selection": "Agent Selection", "Withdraw through an agent": "Withdraw through an agent", "Payment & Review": "Payment & Review", "From": "From", "To": "To", "No results found.": "No results found.", "Add Merchant": "Add Merchant", "Selected": "Selected", "Change": "Change", "Enter payment amount": "Enter payment amount", "MerchantID": "MerchantID", "Top-up": "Top-up", "Number/ID": "Number/ID", "Name": "Name", "Website": "Website", "Menu": "<PERSON><PERSON>", "Email": "Email", "Recipient": "Recipient", "Processing...": "Processing...", "will get": "will get", "Free": "Free", "Total": "Total", "Confirm and proceed": "Confirm and proceed", "Back": "Back", "Selected wallet": "Selected wallet", "Bookmark receipt": "Bookmark receipt", "New balance": "New balance", "Go to dashboard": "Go to dashboard", "Transfer successful": "Transfer successful", "Copy transaction ID": "Copy transaction ID", "Transfer again": "Transfer again", "Download Receipt": "Download Receipt", "Search agent...": "Search agent...", "Select country": "Select country", "Select agent": "Select agent", "Select a country first": "Select a country first", "Change country": "Change country", "Investment Amount": "Investment Amount", "Exchange amount": "Exchange amount", "Exchange rate": "Exchange rate", "You get": "You get", "Exchange details": "Exchange details", "Exchange again": "Exchange again", "Exchange successful": "Exchange successful", "Payment again": "Payment again", "Enter phone number": "Enter phone number", "Confirm and Recharge": "Confirm and Recharge", "Search country by name": "Search country by name", "Deposit Gateways": "Deposit Gateways", "Login Sessions": "<PERSON><PERSON>", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Withdraw Methods": "Withdraw Methods", "Currencies": "Currencies", "Currency Name": "Currency Name", "Code": "Code", "Add Currency": "Add <PERSON>cy", "More": "More", "No currency found": "No currency found", "Save": "Save", "Services and Status": "Services and Status", "KYC": "KYC", "Select currency": "Select currency", "Full name": "Full name", "Profile picture": "Profile picture", "Upload photo": "Upload photo", "Profile": "Profile", "Phone": "Phone", "Address": "Address", "Date of birth": "Date of birth", "Privacy & Security": "Privacy & Security", "City name": "City name", "Full mailing address": "Full mailing address", "Enter your email": "Enter your email", "Two-factor authentication is on.": "Two-factor authentication is on.", "Edit Password": "Edit Password", "To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.": "To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.", "Password": "Password", "KYC Verification": "KYC Verification", "Actions": "Actions", "End date": "End date", "Don’t have an account?": "Don’t have an account?", "Sign up": "Sign up", "Have an account?": "Have an account?", "Enter your password": "Enter your password", "Forgot password?": "Forgot password?", "Sign in": "Sign in", "Enter your email address": "Enter your email address", "Investment ID": "Investment ID", "Create new plan": "Create new plan", "Description": "Description", "Yes": "Yes", "Edit plan": "Edit plan", "Invalid user credentials": "Invalid user credentials", "Remember this device": "Remember this device", "Two-factor authentication": "Two-factor authentication", "Resend code": "Resend code", "Verify": "Verify", "Please verify it's you": "Please verify it's you", "We’ve sent you a 4 digit code to the email address you provided, please enter the code to verify it’s you.": "We’ve sent you a 4 digit code to the email address you provided, please enter the code to verify it’s you.", "Download": "Download", "Awaiting KYC verification": "Awaiting KYC verification", "Total Payments": "Total Payments", "Submit Documents": "Submit Documents", "Personal": "Personal", "Investment type": "Investment type", "Enter name for the investment plan": "Enter name for the investment plan", "Enter investment amount": "Enter investment amount", "Plan name": "Plan name", "Duration (Days)": "Duration (Days)", "Investment amount (Fixed)": "Investment amount (Fixed)", "Enter investment duration": "Enter investment duration", "Enter a profit rate": "Enter a profit rate", "Profit rate (Percentage)": "Profit rate (Percentage)", "daily": "daily", "Select portfolio adjust": "Select portfolio adjust", "weekly": "weekly", "yearly": "yearly", "monthly": "monthly", "Min": "Min", "Investment amount (min-max)": "Investment amount (min-max)", "No": "No", "Max": "Max", "No data found": "No data found", "Last logged in": "Last logged in", "Add Staff": "Add Staff", "Agent is not permitted for deposit": "Agent is not permitted for deposit", "Deposit/Payment Gateways": "Deposit/Payment Gateways", "Recommended": "Recommended", "Meter Type": "Meter Type", "Electricity name": "Electricity name", "Write Provider name": "Write Provider name", "Meter Number": "Meter Number", "Prepaid": "Prepaid", "Postpaid": "Postpaid", "Enter meter number": "Enter meter number", "Agent ID": "Agent ID", "Send an email to": "Send an email to", "Write a message here...": "Write a message here...", "Select users to send mail": "Select users to send mail", "CC": "CC", "Message": "Message", "Subject of your mail...": "Subject of your mail...", "Subject": "Subject", "How much is the bill?": "How much is the bill?", "Confirm password": "Confirm password", "Create New Staff": "Create New Staff", "Create": "Create", "Processing": "Processing", "Email address": "Email address", "Pay bill": "Pay bill", "You will get": "You will get", "City": "City", "Cannot read properties of undefined (reading 'transaction')": "Cannot read properties of undefined (reading 'transaction')", "Investment successful!": "Investment successful!", "Insufficient balance": "Insufficient balance", "withdrawn": "withdrawn", "Delete Investment": "Delete Investment", "Are you sure you want to delete this investment? This action cannot be undone, and all associated data will be permanently removed.": "Are you sure you want to delete this investment? This action cannot be undone, and all associated data will be permanently removed.", "on_hold": "on_hold", "Sending...": "Sending...", "Update": "Update", "Total Profit": "Total Profit", "Are you sure you want to proceed? This action is irreversible and will permanently withdraw your investment.": "Are you sure you want to proceed? This action is irreversible and will permanently withdraw your investment.", "Confirm Withdrawal": "Confirm <PERSON>", "Start Date": "Start Date", "Invested": "Invested", "All Transactions": "All Transactions", "Show bookmarks": "Show bookmarks", "Select what you want to see": "Select what you want to see", "Action": "Action", "Delete Staff": "Delete Staff", "Are you sure you want to delete this staff? This action cannot be undone, and all associated data will be permanently removed.": "Are you sure you want to delete this staff? This action cannot be undone, and all associated data will be permanently removed.", "Set as default": "Set as default", "Unpin": "Unpin", "Default": "<PERSON><PERSON><PERSON>", "Create a New Wallet": "Create a New Wallet", "No data...": "No data...", "Add Wallet": "Add Wallet", "Currency Already in Use": "Currency Already in Use", "Available Currency": "Available Currency", "Added": "Added", "Add": "Add", "Quickly and securely set up your new digital wallet by following the steps.": "Quickly and securely set up your new digital wallet by following the steps.", "Referrals": "Referrals", "Verified": "Verified", "KYC Status": "KYC Status", "Documents": "Documents", "Your account is verified": "Your account is verified", "Attach selfie": "Attach selfie", "You have not submitted documents yet.": "You have not submitted documents yet.", "Attach pictures": "Attach pictures", "Select document type": "Select document type", "Front Side": "Front Side", "Drag and drop file here or upload": "Drag and drop file here or upload", "Selfie": "<PERSON><PERSON>", "Upload": "Upload", "Awaiting submission": "Awaiting submission", "Back Side": "Back Side", "IP Address": "IP Address", "Device": "<PERSON><PERSON>", "Logged in": "Logged in", "Logout from all device": "Logout from all device", "We're here to assist you with any questions or issues you may encounter. Please feel free to contact our support team by emailing:": "We're here to assist you with any questions or issues you may encounter. Please feel free to contact our support team by emailing:", "Need Help?": "Need Help?", "For faster assistance, please provide as much detail as possible about your issue, including screenshots or error messages if applicable. Our support hours are 24/7.": "For faster assistance, please provide as much detail as possible about your issue, including screenshots or error messages if applicable. Our support hours are 24/7.", "Attachment": "Attachment", "Successfully logout.": "Successfully logout.", "Total Commission": "Total Commission", "Commission": "Commission", "Tip": "Tip", "Fee by customer": "Fee by customer", "Request payment": "Request payment", "Enter recipient's address": "Enter recipient's address", "Enter recipient's email address": "Enter recipient's email address", "When you request payment, the recipient will receive an automated email to pay you.": "When you request payment, the recipient will receive an automated email to pay you.", "Enter request amount": "Enter request amount", "Enter recipient's name": "Enter recipient's name", "Close": "Close", "The recipient will get an email with a payment link. You can also download and share the QR code. This request is valid for 15 minutes.": "The recipient will get an email with a payment link. You can also download and share the QR code. This request is valid for 15 minutes.", "Payment Request Sent": "Payment Request Sent", "Merchant Settings": "Merchant Settings", "Webhook URL": "Webhook URL", "Store profile picture": "Store profile picture", "Enter Merchant ID": "Enter Merchant ID", "Store Profile": "Store Profile", "Merchant address": "Merchant address", "Merchant email": "Merchant email", "Merchant ID": "Merchant ID", "Merchant name": "Merchant name", "Generating...": "Generating...", "API Key": "API Key", "Introducing MPay API": "Introducing MPay API", "Create Payment": "Create Payment", "Re-generate key": "Re-generate key", "Copy": "Copy", "API Documentation": "API Documentation", "Introduction": "Introduction", "Check Payment Status": "Check Payment Status", "Delete Key": "Delete Key", "Generate an API Key to start implementing our gateway. See documentation below for more.": "Generate an API Key to start implementing our gateway. See documentation below for more.", "Show Key": "Show Key", "Learn how to integrate the Merchant Payment API to receive payments from your customer in your own platform/system.": "Learn how to integrate the Merchant Payment API to receive payments from your customer in your own platform/system.", "Hide menu": "Hide menu", "amount to be received": "amount to be received", "a value compatible with {{name}} supported currencies": "a value compatible with {{name}} supported currencies", "An URL to redirect the customer after payment is cancelled/failed.": "An URL to redirect the customer after payment is cancelled/failed.", "An URL to receive webhook callback about payment status.": "An URL to receive webhook callback about payment status.", "An URL to redirect the customer after payment is completed.": "An URL to redirect the customer after payment is completed.", "An URL to your logo. It'll be shown on the payment page.": "An URL to your logo. It'll be shown on the payment page.", "Customer email for identification purpose.": "Customer email for identification purpose.", "A custom object where you can add your custom data like customer email or transaction ID to verify the payment from your side. This field will be sent during webhook callbacks.": "A custom object where you can add your custom data like customer email or transaction ID to verify the payment from your side. This field will be sent during webhook callbacks.", "If set to \"true\" the payment will take the fee from the customer directly.": "If set to \"true\" the payment will take the fee from the customer directly.", "400 Bad Request": "400 Bad Request", "500 Internal Server Error": "500 Internal Server Error", "This operation is used to take payments directly from your customers in your account.": "This operation is used to take payments directly from your customers in your account.", "Request URL": "Request URL", "Set it to \"true\" to test payment integration during development phase. Otherwise keep it \"false\" to take real payments.": "Set it to \"true\" to test payment integration during development phase. Otherwise keep it \"false\" to take real payments.", "Value": "Value", "Bearer API_KEY (replace API_KEY with your actual API key).": "Bearer API_KEY (replace API_KEY with your actual API key).", "Mandatory": "Mandatory", "Headers": "Headers", "Redirect your customer to": "Redirect your customer to", "Responses": "Responses", "Authorization": "Authorization", "Webhook (POST)": "Webhook (POST)", "Body Parameters": "Body Parameters", "Example": "Example", "401 Unauthorized": "401 Unauthorized", "This operation is used to get the status of a payment request. TrxId is the id received after request to payment has been successful.": "This operation is used to get the status of a payment request. TrxId is the id received after request to payment has been successful.", "Request Body": "Request Body", "Response Body": "Response Body", "Webhooks": "Webhooks", "Total transaction /": "Total transaction /", "Deposit details": "Deposit details", "Saved phone numbers": "Saved phone numbers", "Add recipient email": "Add recipient email", "Click to autofill phone number": "Click to autofill phone number", "Direct Deposit": "Direct Deposit", "Reject request": "Reject request", "Payment Status": "Payment Status", "Approve request": "Approve request", "Preview": "Preview", "Complete": "Complete", "Methods": "Methods", "Job/Occupation": "Job/Occupation", "Enter your WhatsApp account number or link": "Enter your WhatsApp account number or link", "Enter your job": "Enter your job", "WhatsApp number/link": "WhatsApp number/link", "Agent profile": "Agent profile", "Available methods": "Available methods", "Required": "Required", "Input name": "Input name", "Create method": "Create method", "Country code": "Country code", "Add New Method": "Add New Method", "Currency code": "Currency code", "Input type": "Input type", "Update method": "Update method", "Agent withdrawal commission": "Agent withdrawal commission", "Agent deposit commission": "Agent deposit commission", "Required additional field": "Required additional field", "Allow Deposit": "Allow Deposit", "Allow Withdraw": "Allow Withdraw", "Select input type": "Select input type", "Exchange from": "Exchange from", "Exchanged to": "Exchanged to", "Approve": "Approve", "Reject": "Reject", "Transactions": "Transactions", "Transaction": "Transaction", "Role": "Role", "Account Details": "Account Details", "Send Email": "Send Email", "Permissions": "Permissions", "Account Status": "Account Status", "Default Wallet": "<PERSON><PERSON><PERSON>", "Account type": "Account type", "Convert account type": "Convert account type", "This is a Customer Account": "This is a Customer Account", "Convert to Agent": "Convert to Agent", "You will need to add additional information to convert this account into a Merchant of Agent.": "You will need to add additional information to convert this account into a Merchant of Agent.", "Converting...": "Converting...", "Convert": "Convert", "Convert to Customer": "Convert to Customer", "Add merchant information": "Add merchant information", "Add agent information": "Add agent information", "Convert to Merchant": "Convert to Merchant", "dialog description": "dialog description", "Total Deposit": "Total Deposit", "Total Withdraw": "Total Withdraw", "Total Exchange": "Total Exchange", "Rejected": "Rejected", "KYC Document Rejected": "KYC Document Rejected", "The submitted KYC document has been rejected. Please review the document for discrepancies or invalid details and request the user to submit accurate information for verification.": "The submitted KYC document has been rejected. Please review the document for discrepancies or invalid details and request the user to submit accurate information for verification.", "User have not submitted documents yet": "User have not submitted documents yet", "KYC Documents not submitted yet": "KYC Documents not submitted yet", "Permission": "Permission", "Blacklisted Methods": "Blacklisted Methods", "Logo": "Logo", "Blacklisted Gateways": "Blacklisted Gateways", "Permitted Actions": "Permitted Actions", "Add account": "Add account", "User services": "User services", "Add/Remove balance": "Add/Remove balance", "Withdraw money": "Withdraw money", "No Data": "No Data", "Keep in record": "Keep in record", "Fees": "Fees", "Suspended": "Suspended", "Merchant access": "Merchant access", "Merchant status": "Merchant status", "Access granted": "Access granted", "Grant Access": "Grant Access", "Awaiting Status": "Awaiting Status", "Payment Fee": "Payment Fee", "Exchange Fee": "Exchange Fee", "Withdrawal Fee": "<PERSON><PERSON><PERSON>", "Deposit Fee": "Deposit Fee", "Transfer Fee": "Transfer Fee", "Block List": "Block List", "Add Customer": "Add Customer", "Supported Currencies": "Supported Currencies", "Update gateway": "Update gateway", "Active API": "Active API", "Charge": "Charge", "Limit": "Limit", "How would you like to pay?": "How would you like to pay?", "For any issues please contact at": "For any issues please contact at", "Pay": "Pay", "Go back": "Go back", "You can go back to the home page.": "You can go back to the home page.", "This page isn't here anymore.": "This page isn't here anymore.", "Select your preferred method": "Select your preferred method", "Country of deposit": "Country of deposit", "Deposit pending": "Deposit pending", "Deposit successful": "Deposit successful", "Deposit again": "Deposit again", "Amount not allowed (Min:) 200": "Amount not allowed (Min:) 200", "Wallet ID": "Wallet ID", "Withdraw submitted": "Withdraw submitted", "Withdraw successful": "Withdraw successful", "Investment status updated successfully": "Investment status updated successfully", "Are you sure you want to close this card?": "Are you sure you want to close this card?", "Create new investment plan": "Create new investment plan", "Currency is required": "Currency is required", "Minimum amount is required": "Minimum amount is required", "Name is required": "Name is required", "Interest rate is required": "Interest rate is required", "Duration is required": "Duration is required", "Withdraw after matured is required": "Withdraw after matured is required", "Duration type is required": "Duration type is required", "Write description here...": "Write description here...", "Remove": "Remove", "All countries": "All countries", "Maximum amount": "Maximum amount", "Label": "Label", "Minimum amount": "Minimum amount", "Percentage charge (%)": "Percentage charge (%)", "Webhook Secret (Optional)": "Webhook Secret (Optional)", "Update plugin": "Update plugin", "Read Documentation": "Read Documentation", "Enter minimum transaction amount": "Enter minimum transaction amount", "Enter currency name": "Enter currency name", "Update Currency": "Update C<PERSON><PERSON>cy", "Updating...": "Updating...", "Enter maximum transaction amount": "Enter maximum transaction amount", "Enter currency code": "Enter currency code", "Front image": "Front image", "Back image": "Back image", "Agent access": "Agent access", "Granted": "Granted", "Enter your name": "Enter your name", "Agent status": "Agent status", "Use default instead": "Use default instead", "Pay Commission": "Pay Commission", "Phone number": "Phone number", "No preview": "No preview", "Investment updated successfully": "Investment updated successfully"}