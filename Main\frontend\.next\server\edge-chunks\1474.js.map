{"version": 3, "file": "edge-chunks/1474.js", "mappings": "2FAEAA,EAAAC,UAAkB,IAElBD,EAAA,OAAe,UAAAE,CAAA,CAAAC,CAAA,EACf,GAAAD,GAAAC,EAAA,CACA,IAAAC,EAAAC,MAAAC,OAAA,CAAAH,GAAAA,EAAAA,EAAAI,KAAA,MAEA,GAAAH,IAAAA,EAAAI,MAAA,CACA,SAGA,IAAAC,EAAAP,EAAAQ,IAAA,KACAC,EAAA,CAAAT,EAAAU,IAAA,MAAAC,WAAA,GACAC,EAAAH,EAAAI,OAAA,aACA,OAAAX,EAAAY,IAAA,UAAAJ,CAAA,EACA,IAAAK,EAAAL,EAAAM,IAAA,GAAAL,WAAA,SAEA,MAAAI,EAAAE,MAAA,IACAV,EAAAI,WAAA,GAAAO,QAAA,CAAAH,GACQA,EAAAG,QAAA,OAERN,IAAAG,EAAAF,OAAA,aAGAJ,IAAAM,CACA,EACA,CAEA,QACA,0EC9BO,IAAAI,EAAA,IAAAC,IAAA,CAEP,uDACA,8BACA,sBACA,sBACA,oBACA,sBACA,oBACA,qCACA,uCACA,sCACA,uCACA,sBACA,uCACA,uCACA,gCACA,6CACA,oBACA,+CACA,uCACA,mCACA,oCACA,sBACA,qCACA,mCACA,qCACA,wCACA,yBACA,uBACA,wBACA,wBACA,sEACA,kCACA,oCACA,oBACA,kDACA,sBACA,mCACA,+CACA,yCACA,gCACA,4BACA,oCACA,yBACA,qBACA,4CACA,yBACA,kCACA,gCACA,sCACA,8CACA,sCACA,+CACA,oBACA,0BACA,sBACA,gCACA,gDACA,gDACA,2CACA,uCACA,4BACA,mCACA,gCACA,iCACA,0CACA,8BACA,sCACA,2CACA,mCACA,8BACA,gCACA,8BACA,6CACA,oBACA,sCACA,6CACA,8BACA,mCACA,oCACA,8CACA,0BACA,sCACA,4BACA,8BACA,iBACA,wCACA,wCACA,wCACA,wCACA,wCACA,0DACA,8DACA,4CACA,sBACA,uCACA,mCACA,wCACA,4BACA,4BACA,4BACA,4BACA,4BACA,kBACA,8BACA,iCACA,kCACA,2CACA,4BACA,+BACA,gCACA,+CACA,wCACA,uCACA,oCACA,oCACA,mCACA,0BACA,yBACA,yCACA,qCACA,gCACA,uCACA,oBACA,8BACA,sCACA,sCACA,yBACA,iEACA,sCACA,2BACA,mCACA,qCACA,kDACA,iDACA,kDACA,kDACA,yCACA,+BACA,sCACA,2BACA,yBACA,kDACA,sBACA,kCACA,+BACA,mCACA,sBACA,8BACA,mBACA,qCACA,mCACA,+BACA,qCACA,yCACA,gDACA,4BACA,mDACA,2BACA,sCACA,mCACA,mBACA,iCACA,mBACA,8BACA,yBACA,8BACA,iCACA,mBACA,gCACA,qCACA,gCACA,yCACA,wCACA,8BACA,kCACA,iCACA,gCACA,sCACA,wCACA,2CACA,2BACA,uCACA,qBACA,sCACA,qCACA,wCACA,uCACA,mBACA,iCACA,qCACA,gEACA,oCACA,qCACA,yBACA,0BACA,mCACA,wCACA,mCACA,uCACA,mCACA,8BACA,6BACA,4DACA,mFACA,6BACA,4DACA,mFACA,iCACA,kCACA,wBACA,2BACA,6BACA,gCACA,mCACA,8BACA,wBACA,6BACA,oCACA,6BACA,4BACA,mCACA,wBACA,wBACA,wBACA,uCACA,iCACA,mCACA,0CACA,0CACA,0CACA,kCACA,uCACA,uCACA,kCACA,oCACA,mCACA,oBACA,yBACA,gCACA,0CACA,mCACA,kCACA,wCACA,iCACA,gCACA,gCACA,uCACA,yCACA,oCACA,uCACA,wBACA,4BACA,8BACA,mCACA,0BACA,8BACA,qBACA,uCACA,kCACA,sCACA,wCACA,uBACA,oBACA,yBACA,yBACA,iCACA,mDACA,mCACA,8BACA,8BACA,uDACA,0CACA,iCACA,0BACA,2BACA,2BACA,2BACA,2BACA,6BACA,sBACA,wBACA,sBACA,yCACA,sBACA,oCACA,gCACA,uBACA,oCACA,sCACA,qDACA,yBACA,wBACA,uCACA,wCACA,wBACA,wCACA,8DACA,wBACA,oCACA,qCACA,qCACA,kCACA,qBACA,mCACA,yCACA,6BACA,wCACA,uCACA,wBACA,gDACA,kCACA,mCACA,4CACA,wCACA,wCACA,sCACA,oBACA,kDACA,4BACA,2BACA,8BACA,8BACA,sCACA,+BACA,qCACA,8BACA,iCACA,iCACA,4BACA,sCACA,4CACA,0CACA,iCACA,yCACA,qDACA,uDACA,8BACA,8CACA,wBACA,2BACA,0BACA,kCACA,0BACA,4BACA,iBACA,sBACA,sBACA,sBACA,kCACA,gCACA,qCACA,uCACA,4BACA,sBACA,gCACA,sBACA,gCACA,uBACA,qCACA,kBACA,8BACA,6BACA,mCACA,mCACA,iCACA,mCACA,sBACA,2BACA,sCACA,oBACA,qBACA,wCACA,0CACA,2CACA,mCACA,qCACA,kCACA,qCACA,uBACA,wBACA,oBACA,wBACA,kDACA,sBACA,mCACA,qCACA,qBACA,yCACA,qDACA,mCACA,4CACA,iCACA,oBACA,qBACA,gCACA,kCACA,iDACA,iDACA,8BACA,iDACA,gDACA,kDACA,sCACA,sDACA,8BACA,0CACA,0CACA,2CACA,qBACA,8BACA,mCACA,8CACA,8BACA,qBACA,gCACA,oBACA,kCACA,sBACA,wCACA,8CACA,oBACA,qBACA,sBACA,oBACA,qBACA,qBACA,qBACA,sBACA,oBACA,oBACA,oBACA,gCACA,4BACA,8BACA,iCAEA,8BACA,qCACA,mBACA,oBACA,sBACA,sBACA,oBACA,sBACA,sBACA,sBACA,qBACA,wCACA,mCACA,kCACA,6CACA,uCACA,uCACA,+CACA,2CACA,gCACA,gCACA,sCACA,yCACA,yCACA,uCACA,sCACA,kCACA,oBACA,sBACA,kCACA,oCACA,oCACA,yCACA,gCACA,6DACA,kEACA,4CACA,qBACA,8BACA,mCACA,gDACA,sBACA,0CACA,yCACA,kCACA,oCACA,qBACA,mCACA,mCACA,iCACA,sCACA,qBACA,sCACA,iCACA,wCACA,mCACA,qBACA,qBACA,qBACA,qBACA,qBACA,yCACA,sBACA,0BACA,4BACA,kCACA,sBACA,oCACA,oCACA,2BACA,iCACA,gCACA,mCACA,uCACA,uCACA,qBACA,mCACA,2BACA,mCACA,6BACA,oCACA,iCACA,qCACA,4BACA,sCACA,8BACA,gCACA,uBACA,iCACA,4BACA,mBACA,oBACA,sBACA,sCACA,wCACA,gCACA,+BACA,oCACA,iDACA,2CACA,qBACA,sBACA,4BACA,8BACA,0BACA,oBACA,qBACA,iCACA,4BACA,2BACA,0BACA,2BACA,2BACA,oCACA,+CACA,+BACA,sBACA,yCACA,sBACA,gCACA,0CACA,gCACA,0BACA,8BACA,qBACA,sBACA,qBACA,oBACA,qBACA,2BACA,qBACA,4BACA,6CACA,+BACA,qBACA,sBACA,qBACA,qBACA,sBACA,+CACA,4CACA,6CACA,qCACA,qCACA,sCACA,qCACA,2BACA,mCACA,oBACA,+CACA,uCACA,gCACA,oCACA,qCACA,qBACA,mCACA,qCACA,mCACA,mCACA,uCACA,oBACA,wBACA,mCACA,mCACA,sDACA,oCACA,6CACA,+BACA,0BACA,6CACA,4BACA,8BACA,uCACA,4BACA,0DACA,iBACA,iCACA,yCACA,8BACA,mCACA,qBACA,8CACA,gCACA,4CACA,kCACA,6CACA,0CACA,uCACA,4BACA,6BACA,mCACA,sCACA,+BACA,+BACA,qDACA,4BACA,yCACA,yCACA,wCACA,iCACA,6CACA,oBACA,0BACA,sDACA,mDACA,qDACA,+DACA,sDACA,mDACA,yDACA,0DACA,yDACA,kDACA,oBACA,6BACA,oBACA,oBACA,0BACA,kCACA,iCACA,iCACA,iCACA,kCACA,wCACA,uBACA,gCACA,qBACA,qBACA,iDACA,+DACA,iDACA,4DACA,mBACA,+DACA,sDACA,4DACA,mEACA,kEACA,2DACA,uCACA,uCACA,8BACA,4BACA,kDACA,sBACA,wCACA,2CACA,iCACA,iCACA,0CACA,sCACA,2BACA,+BACA,+BACA,4CACA,+CACA,wBACA,oCACA,wCACA,kCACA,wCACA,iCACA,iCACA,qCACA,uBACA,uCACA,sBACA,8BACA,4BACA,0BACA,uCACA,mCACA,mCACA,mCACA,iCACA,+BACA,mCACA,kCACA,0BACA,kCACA,mCACA,mCACA,0CACA,oCACA,uBACA,mCACA,8BACA,uCACA,0CACA,4BACA,4CACA,qCACA,sCACA,8BACA,4BACA,oCACA,oBACA,kCACA,+CACA,wCACA,sEACA,iFACA,wCACA,+DACA,mCACA,kCACA,wCACA,mEACA,kFACA,iCACA,sEACA,qFACA,+BACA,8BACA,0CACA,iCACA,uCACA,gCACA,4CACA,kCACA,uCACA,mCACA,wBACA,qCACA,oCACA,0CACA,2CACA,2CACA,2CACA,2CACA,mCACA,mCACA,gDACA,yBACA,4CACA,4CACA,4CACA,4CACA,4CACA,4CACA,2BACA,+BACA,iCACA,qCACA,4BACA,6BACA,sDACA,8BACA,0CACA,uCACA,0CACA,wCACA,sBACA,kCACA,wBACA,8CACA,wCACA,yCACA,8CACA,8BACA,qBACA,sCACA,gDACA,4CACA,8CACA,0BACA,+BACA,sBACA,sCACA,sCACA,+CACA,8CACA,kCACA,sCACA,8BACA,qCACA,8BACA,0CACA,8BACA,mBACA,wBACA,iCACA,qCACA,gCACA,mBACA,oBACA,4CACA,uBACA,gCACA,8CACA,mCACA,0CACA,sCACA,uCACA,uBACA,gCACA,4CACA,4CACA,+CACA,2CACA,2CACA,0BACA,8CACA,mCACA,kCACA,qCACA,gCACA,gCACA,gCACA,mCACA,qCACA,6CACA,gDACA,qDACA,qDACA,uCACA,qBACA,oBACA,qDACA,oBACA,qBACA,0BACA,8BACA,qBACA,8BACA,sBACA,8BACA,8BACA,oCACA,qBACA,sBACA,0CACA,2CACA,gCACA,kCACA,4BACA,+BACA,+BACA,+BACA,+BACA,+DACA,8EACA,qBACA,oBACA,uCACA,qCACA,6CACA,4CACA,2BACA,4BACA,sBACA,8CACA,sBACA,iCACA,kCACA,2CACA,qBACA,6CACA,qCACA,8BACA,uCACA,sCACA,oBACA,4BACA,oCACA,+BACA,8BACA,yCACA,gCACA,2CACA,oCACA,gCACA,mCACA,8CACA,gDACA,gDACA,iCACA,mDACA,kCACA,oBACA,0BACA,+BACA,0BACA,oCACA,kDACA,uBACA,yBACA,gCACA,uCACA,wCACA,oCACA,kCACA,sCACA,8BACA,wBACA,yBACA,iCACA,wCACA,6CACA,mCACA,uCACA,uCACA,gDACA,0CACA,uCACA,yCACA,mBACA,kCACA,oBACA,mCACA,oDACA,gCACA,4BACA,sCACA,4BACA,wCACA,4CACA,8BACA,oCACA,4BACA,iCACA,oCACA,sBACA,iCACA,gCACA,wBACA,sBACA,4BACA,0CACA,qBACA,sBACA,2BACA,yCACA,4BACA,uCACA,+CACA,mCACA,oBACA,kCACA,4BACA,mCACA,oBACA,uCACA,oCACA,0BACA,mBACA,sBACA,gCACA,6CACA,8CACA,2CACA,qCACA,qBACA,2CACA,mCACA,oDACA,2BACA,uCACA,6BACA,wCACA,+BACA,gCACA,8BACA,iCACA,qCACA,oCACA,wBACA,yBACA,yBACA,8BACA,gCACA,oCACA,yBACA,+BACA,oCACA,oCACA,iCACA,4BACA,iCACA,gCACA,4BACA,4BACA,wCACA,6BACA,+BACA,gCACA,qCACA,qCACA,kCACA,6BACA,kCACA,iCACA,6BACA,6BACA,yCACA,8BACA,gCACA,4CACA,oCACA,2CACA,mCACA,yCACA,yDACA,uBACA,+BACA,uBACA,uCACA,2BACA,8BACA,uCACA,4BACA,uCACA,oCACA,yBACA,+BACA,yCACA,yBACA,8CACA,uCACA,sBACA,gCACA,8BACA,gCACA,gCACA,gCACA,yCACA,mBACA,wBACA,oCACA,iCACA,6BACA,wCACA,mCACA,4BACA,sBACA,yBACA,8BACA,gDACA,8BACA,mCACA,mCACA,6BACA,sBACA,iDACA,sBACA,4CACA,sBACA,oCACA,6BACA,mCACA,uBACA,yBACA,+BACA,oBACA,2BACA,4BACA,kCACA,2CACA,yBACA,yBACA,mCACA,qBACA,uBACA,8BACA,sCACA,iCACA,mCACA,8BACA,gCACA,qBACA,8BACA,gCACA,wCACA,mCACA,yBACA,wBACA,iCACA,6BACA,0BACA,2BACA,yBACA,uCACA,8CACA,4CACA,gCACA,wCACA,+BACA,mCACA,iCACA,qDACA,0BACA,oCACA,mCACA,oCACA,wCACA,wCACA,iCACA,8CACA,kCACA,gCACA,0CACA,sCACA,gCACA,gCACA,kCACA,+BACA,yBACA,2BACA,mCACA,0DACA,mCACA,gCACA,mCACA,mCACA,iEACA,0DACA,6EACA,mCACA,6DACA,gFACA,mCACA,kBACA,0BACA,kCACA,oCACA,8BACA,kCACA,gCACA,0BACA,iCACA,yCACA,2CACA,2CACA,0BACA,0BACA,gCACA,qCACA,gCACA,0CACA,6BACA,8BACA,8BACA,yBACA,0BACA,qBACA,4BACA,8BACA,oBACA,0BACA,+BACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,yCACA,0BACA,8BACA,+BACA,qDACA,2BACA,EACO,SAAAC,EAAArB,CAAA,CAAAsB,CAAA,CAAAC,CAAA,EACP,IAAAC,EAAAC,SAyBAzB,CAAA,EACA,IAAYQ,KAAAA,CAAA,EAAOR,EAEnB,GAAA0B,GADAlB,KAAAA,EAAAmB,WAAA,OACA,CAAA3B,EAAAU,IAAA,EACA,IAAAkB,EAAApB,EAAAH,KAAA,MACAwB,GAAA,GAAAlB,WAAA,GACAD,EAAAS,EAAAW,GAAA,CAAAF,GACAlB,GACAqB,OAAAC,cAAA,CAAAhC,EAAA,QACAiC,MAAAvB,EACAwB,SAAA,GACAC,aAAA,GACAC,WAAA,EACA,EAEA,CACA,OAAApC,CACA,EA1CAA,GACA,CAAYqC,mBAAAA,CAAA,EAAqBrC,EACjCsC,EAAA,iBAAAhB,EACAA,EAIA,iBAAAe,GAAAA,EAAA/B,MAAA,GACA+B,EACA,KAAmBrC,EAAAQ,IAAA,CAAU,EAc7B,MAbA,iBAAAgB,EAAAF,IAAA,EACAiB,EAAAf,EAAA,OAAAc,GAEAE,KAAAA,IAAAjB,GACAQ,OAAAC,cAAA,CAAAR,EAAA,UACAS,MAAAV,EACAW,SAAA,GACAC,aAAA,GACAC,WAAA,EACA,GAGAG,EAAAf,EAAA,eAAAc,GACAd,CACA,CAmBA,SAAAe,EAAAf,CAAA,CAAAiB,CAAA,CAAAR,CAAA,EACAF,OAAAC,cAAA,CAAAR,EAAAiB,EAAA,CACAR,MAAAA,EACAC,SAAA,GACAC,aAAA,GACAC,WAAA,EACA,EACA,CCpuCA,IAAAM,EAAA,CAEA,YACA,YACA,CA+BA,SAAAC,EAAAC,CAAA,EACA,uBAAAA,GAAAA,OAAAA,CACA,CA8BA,SAAAC,EAAAC,CAAA,EACA,OAAAA,EAAAC,MAAA,CAAA/C,GAAA0C,KAAAA,EAAAM,OAAA,CAAAhD,EAAAQ,IAAA,EACA,CAKA,SAAAyC,EAAAC,CAAA,EACA,GAAAA,OAAAA,EACA,SAEA,IAAAJ,EAAA,GAEA,QAAAK,EAAA,EAAoBA,EAAAD,EAAA5C,MAAA,CAAkB6C,IAAA,CACtC,IAAAnD,EAAAkD,CAAA,CAAAC,EAAA,CACAL,EAAAM,IAAA,CAAApD,EACA,CACA,OAAA8C,CACA,CAEA,SAAAO,EAAAC,CAAA,EACA,sBAAAA,EAAAC,gBAAA,CACA,OAAAC,EAAAF,GAEA,IAAAG,EAAAH,EAAAC,gBAAA,UAIA,GAAAE,EAAAC,WAAA,CACAC,EAAAF,GAEAD,EAAAF,EAAAG,EACA,CAOA,SAAAD,EAAAF,CAAA,CAAAG,CAAA,EACA,MAAW,GAAAG,EAAAC,EAAA,EAAS,+BACpB,IAAAC,EAOA,GAAAC,WAAAC,eAAA,qBAAAV,EAAAW,qBAAA,EACA,IAAA1C,EAAA,MAAA+B,EAAAW,qBAAA,GACA,GAAA1C,OAAAA,EACA,eAAmC+B,EAAA,cAAM,GAIzC,GAAA/B,KAAAiB,IAAAjB,EAAA,CACA,IAAAvB,EAAA,MAAAuB,EAAA2C,OAAA,GAEA,OADAlE,EAAAmE,MAAA,CAAA5C,EACuBF,EAAcrB,EACrC,CACA,CACA,IAAAA,EAAAsD,EAAAc,SAAA,GACA,IAAApE,EACA,eAA+BsD,EAAA,cAAM,GAGrC,OADoBjC,EAAcrB,EAAA,OAAA8D,CAAAA,EAAAL,MAAAA,EAAA,OAAAA,EAAAY,QAAA,GAAAP,KAAA,IAAAA,EAAAA,EAAAtB,KAAAA,EAElC,EACA,CAEA,SAAA8B,EAAAb,CAAA,EACA,MAAW,GAAAG,EAAAC,EAAA,EAAS,+BACpB,OAAAJ,EAAAC,WAAA,CAAAC,EAAAF,GAAAc,SAoCAd,CAAA,EACA,MAAW,GAAAG,EAAAC,EAAA,EAAS,+BACpB,WAAAW,QAAA,CAAAC,EAAAC,KACAjB,EAAAzD,IAAA,KAEAyE,EAD4BpD,EAAcrB,EAAAyD,EAAAY,QAAA,EAE1C,EAAa,IACbK,EAAAC,EACA,EACA,EACA,EACA,EA/CAlB,EACA,EACA,CAEA,SAAAE,EAAAF,CAAA,EACA,IAAAmB,EAAAnB,EAAAoB,YAAA,GACA,WAAAL,QAAA,CAAAC,EAAAC,KACA,IAAAI,EAAA,IAyBAC,SAxBAA,IAGAH,EAAAG,WAAA,IAA0C,GAAAnB,EAAAC,EAAA,EAAS,+BACnD,GAAAmB,EAAA1E,MAAA,CAUA,CACA,IAAA4C,EAAAsB,QAAAS,GAAA,CAAAD,EAAAE,GAAA,CAAAZ,IACAQ,EAAA1B,IAAA,CAAAF,GAEA6B,GACA,MAbA,IACA,IAAAjC,EAAA,MAAA0B,QAAAS,GAAA,CAAAH,GACAL,EAAA3B,EACA,CACA,MAAA6B,EAAA,CACAD,EAAAC,EACA,CAQA,GAAa,IACbD,EAAAC,EACA,EACA,GAEA,EACA,gBE/KA,SAAAQ,EAAAC,CAAA,EAAmC,OAAAC,SAMnCD,CAAA,EAAmC,GAAAjF,MAAAC,OAAA,CAAAgF,GAAA,OAAAE,EAAAF,EAAA,EANAA,IAAAG,SAInCC,CAAA,EAAkC,uBAAAC,QAAAD,MAAAA,CAAA,CAAAC,OAAAC,QAAA,GAAAF,MAAAA,CAAA,sBAAArF,MAAAwF,IAAA,CAAAH,EAAA,EAJCJ,IAAAQ,EAAAR,IAAAS,WAEH,0JAFG,CAQnC,SAAAC,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAAlE,OAAAkE,IAAA,CAAAF,GAAgC,GAAAhE,OAAAmE,qBAAA,EAAoC,IAAAC,EAAApE,OAAAmE,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAApD,MAAA,UAAAqD,CAAA,EAA6D,OAAArE,OAAAsE,wBAAA,CAAAN,EAAAK,GAAAhE,UAAA,EAAiE,EAAA6D,EAAA7C,IAAA,CAAAkD,KAAA,CAAAL,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAAM,EAAAC,CAAA,EAAiC,QAAArD,EAAA,EAAgBA,EAAAsD,UAAAnG,MAAA,CAAsB6C,IAAA,CAAO,IAAAuD,EAAA,MAAAD,SAAA,CAAAtD,EAAA,CAAAsD,SAAA,CAAAtD,EAAA,GAAuDA,CAAAA,EAAA,EAAA2C,EAAA/D,OAAA2E,GAAA,IAAAC,OAAA,UAAAlE,CAAA,EAA6DmE,EAAAJ,EAAA/D,EAAAiE,CAAA,CAAAjE,EAAA,IAA4CV,OAAA8E,yBAAA,CAAA9E,OAAA+E,gBAAA,CAAAN,EAAAzE,OAAA8E,yBAAA,CAAAH,IAAAZ,EAAA/D,OAAA2E,IAAAC,OAAA,UAAAlE,CAAA,EAAoKV,OAAAC,cAAA,CAAAwE,EAAA/D,EAAAV,OAAAsE,wBAAA,CAAAK,EAAAjE,GAAA,EAAmF,CAAK,OAAA+D,CAAA,CAE1e,SAAAI,EAAAG,CAAA,CAAAtE,CAAA,CAAAR,CAAA,EAAoM,OAAxJQ,KAAAsE,EAAkBhF,OAAAC,cAAA,CAAA+E,EAAAtE,EAAA,CAAkCR,MAAAA,EAAAG,WAAA,GAAAD,aAAA,GAAAD,SAAA,KAAgF6E,CAAA,CAAAtE,EAAA,CAAAR,EAAoB8E,CAAA,CAIpM,SAAAC,EAAA5B,CAAA,CAAAjC,CAAA,EAAkC,OAAA8D,SAUlC7B,CAAA,EAAgC,GAAAjF,MAAAC,OAAA,CAAAgF,GAAA,OAAAA,CAAA,EAVEA,IAAA8B,SAQlC9B,CAAA,CAAAjC,CAAA,EAAyC,IAAgLgE,EAAAC,EAAhLC,EAAAjC,MAAAA,EAAA,yBAAAK,QAAAL,CAAA,CAAAK,OAAAC,QAAA,GAAAN,CAAA,eAA0G,GAAAiC,MAAAA,GAAwB,IAAAC,EAAA,GAAeC,EAAA,GAAeC,EAAA,GAA4B,IAAM,IAAAH,EAAAA,EAAAI,IAAA,CAAArC,GAAwB,CAAAmC,CAAAA,EAAA,CAAAJ,EAAAE,EAAAK,IAAA,IAAAC,IAAA,IAA4CL,EAAAlE,IAAA,CAAA+D,EAAAlF,KAAA,EAAqBkB,CAAAA,GAAAmE,EAAAhH,MAAA,GAAA6C,GAAlCoE,EAAA,IAAkC,CAAuC,MAAA5C,EAAA,CAAc6C,EAAA,GAAWJ,EAAAzC,CAAA,QAAY,CAAU,IAAM4C,GAAAF,MAAAA,EAAA,QAAAA,EAAA,gBAAmD,CAAU,GAAAG,EAAA,MAAAJ,CAAA,EAAsB,OAAAE,EAAA,EARjdlC,EAAAjC,IAAAyC,EAAAR,EAAAjC,IAAAyE,WAEJ,+JAFI,CAIlC,SAAAhC,EAAAiC,CAAA,CAAAC,CAAA,EAAkD,GAAAD,GAAgB,oBAAAA,EAAA,OAAAvC,EAAAuC,EAAAC,GAAgE,IAAAC,EAAAhG,OAAAiG,SAAA,CAAAC,QAAA,CAAAR,IAAA,CAAAI,GAAAK,KAAA,OAAqH,GAA7D,WAAAH,GAAAF,EAAAM,WAAA,EAAAJ,CAAAA,EAAAF,EAAAM,WAAA,CAAA3H,IAAA,EAA6DuH,QAAAA,GAAAA,QAAAA,EAAA,OAAA5H,MAAAwF,IAAA,CAAAkC,GAAsD,GAAAE,cAAAA,GAAA,2CAAAK,IAAA,CAAAL,GAAA,OAAAzC,EAAAuC,EAAAC,GAAA,CAE7S,SAAAxC,EAAAF,CAAA,CAAAiD,CAAA,EAAuCA,CAAAA,MAAAA,GAAAA,EAAAjD,EAAA9E,MAAA,GAAA+H,CAAAA,EAAAjD,EAAA9E,MAAA,EAAuD,QAAA6C,EAAA,EAAAmF,EAAA,MAAAD,GAAuClF,EAAAkF,EAASlF,IAAOmF,CAAA,CAAAnF,EAAA,CAAAiC,CAAA,CAAAjC,EAAA,CAAoB,OAAAmF,CAAA,CAOzK,IAAAC,EAAA,mBAAqBC,EAA0BA,EAAWA,EAAA,OAAgB,CAiBnEC,EAAA,WACP,IAAAC,EAAAjC,UAAAnG,MAAA,IAAAmG,KAAAjE,IAAAiE,SAAA,IAAAA,SAAA,OACAkC,EAAAD,EAAArI,KAAA,MACAuI,EAAAD,EAAArI,MAAA,aAAAuI,MAAA,CAAAF,EAAAG,IAAA,QAAAH,CAAA,IACA,OACAI,KApBO,oBAqBPC,QAAA,qBAAAH,MAAA,CAAAD,EACA,CACA,EACOK,EAAA,SAAAC,CAAA,EACP,OACAH,KAzBO,iBA0BPC,QAAA,uBAAAH,MAAA,CAAAK,EAAA,KAAAL,MAAA,CAAAK,IAAAA,EAAA,eACA,CACA,EACOC,EAAA,SAAAC,CAAA,EACP,OACAL,KA9BO,iBA+BPC,QAAA,wBAAAH,MAAA,CAAAO,EAAA,KAAAP,MAAA,CAAAO,IAAAA,EAAA,eACA,CACA,EACOC,EAAA,CACPN,KAlCO,iBAmCPC,QAAA,gBACA,EAYO,SAAAM,EAAAtJ,CAAA,CAAA0I,CAAA,EACP,IAAAa,EAAAvJ,2BAAAA,EAAAU,IAAA,EAAA6H,EAAAvI,EAAA0I,GACA,OAAAa,EAAAA,EAAA,KAAAd,EAAAC,GAAA,CAEO,SAAAc,EAAAxJ,CAAA,CAAAoJ,CAAA,CAAAF,CAAA,EACP,GAAAO,EAAAzJ,EAAA0J,IAAA,GACA,GAAAD,EAAAL,IAAAK,EAAAP,GAAA,CACA,GAAAlJ,EAAA0J,IAAA,CAAAR,EAAA,UAAAD,EAAAC,GAAA,CACA,GAAAlJ,EAAA0J,IAAA,CAAAN,EAAA,UAAAD,EAAAC,GAAA,MACM,GAAAK,EAAAL,IAAApJ,EAAA0J,IAAA,CAAAN,EAAA,UAAAD,EAAAC,GAAA,MAAqG,GAAAK,EAAAP,IAAAlJ,EAAA0J,IAAA,CAAAR,EAAA,UAAAD,EAAAC,GAAA,CAG3G,gBAGA,SAAAO,EAAAxH,CAAA,EACA,OAAAA,MAAAA,CACA,CA4CO,SAAA0H,EAAAC,CAAA,QACP,mBAAAA,EAAAD,oBAAA,CACAC,EAAAD,oBAAA,GACI,SAAAC,EAAAC,YAAA,EACJD,EAAAC,YAAA,CAKO,SAAAC,EAAAF,CAAA,SACP,EAAAG,YAAA,CAMA5J,MAAA6H,SAAA,CAAAlH,IAAA,CAAA2G,IAAA,CAAAmC,EAAAG,YAAA,CAAAC,KAAA,UAAAtJ,CAAA,EACA,MAAAA,UAAAA,GAAAA,2BAAAA,CACA,GAPA,EAAAkJ,EAAApD,MAAA,IAAAoD,EAAApD,MAAA,CAAA1D,KAAA,CAaO,SAAAmH,EAAAL,CAAA,EACPA,EAAAM,cAAA,EACA,CAyBO,SAAAC,IACP,QAAAC,EAAA3D,UAAAnG,MAAA,CAAA+J,EAAA,MAAAD,GAAAE,EAAA,EAAqEA,EAAAF,EAAaE,IAClFD,CAAA,CAAAC,EAAA,CAAA7D,SAAA,CAAA6D,EAAA,CAGA,gBAAAV,CAAA,EACA,QAAAW,EAAA9D,UAAAnG,MAAA,CAAAkK,EAAA,MAAAD,EAAA,EAAAA,EAAA,KAAAE,EAAA,EAA+FA,EAAAF,EAAeE,IAC9GD,CAAA,CAAAC,EAAA,GAAAhE,SAAA,CAAAgE,EAAA,CAGA,OAAAJ,EAAAvJ,IAAA,UAAA4J,CAAA,EAKA,MAJA,CAAAf,EAAAC,IAAAc,GACAA,EAAApE,KAAA,SAAAsD,EAAA,CAAAf,MAAA,CAAA2B,IAGAb,EAAAC,EACA,EACA,CACA,CA0GO,SAAAe,EAAA/H,CAAA,EACP,MAAAA,YAAAA,GAAAA,YAAAA,GAAAA,YAAAA,GAAAA,WAAAA,GAAAA,kBAAAA,GAAA,iBAAAwF,IAAA,CAAAxF,EACA,CAMO,SAAAgI,EAAAhI,CAAA,EACP,oBAAAwF,IAAA,CAAAxF,EACA,CCxUA,IAAAiI,EAAA,aACAC,EAAA,SACAC,EAAA,6GACAC,EAAA,gCAUA,SAASC,EAAc7F,CAAA,CAAAjC,CAAA,EAAW,OAAO+H,SAUjB9F,CAAA,EAAQ,GAAAjF,MAAAC,OAAA,CAAAgF,GAAA,OAAAA,CAAA,EAVwBA,IAAS+F,SAQnC/F,CAAA,CAAAjC,CAAA,EAAW,IAAgLgE,EAAAC,EAAhLC,EAAAjC,MAAAA,EAAA,yBAAAK,QAAAL,CAAA,CAAAK,OAAAC,QAAA,GAAAN,CAAA,eAA0G,GAAAiC,MAAAA,GAAwB,IAAAC,EAAA,GAAeC,EAAA,GAAeC,EAAA,GAA4B,IAAM,IAAAH,EAAAA,EAAAI,IAAA,CAAArC,GAAwB,CAAAmC,CAAAA,EAAA,CAAAJ,EAAAE,EAAAK,IAAA,IAAAC,IAAA,IAA4CL,EAAAlE,IAAA,CAAA+D,EAAAlF,KAAA,EAAqBkB,CAAAA,GAAAmE,EAAAhH,MAAA,GAAA6C,GAAlCoE,EAAA,IAAkC,CAAuC,MAAA5C,EAAA,CAAc6C,EAAA,GAAWJ,EAAAzC,CAAA,QAAY,CAAU,IAAM4C,GAAAF,MAAAA,EAAA,QAAAA,EAAA,gBAAmD,CAAU,GAAAG,EAAA,MAAAJ,CAAA,EAAsB,OAAAE,EAAA,EAR7ZlC,EAAAjC,IAAYiI,EAA2BhG,EAAAjC,IAAYkI,WAE3G,+JAF2H,CAIzJ,SAASD,EAA2BvD,CAAA,CAAAC,CAAA,EAAc,GAAAD,GAAgB,oBAAAA,EAAA,OAAkCyD,EAAiBzD,EAAAC,GAAa,IAAAC,EAAAhG,OAAAiG,SAAA,CAAAC,QAAA,CAAAR,IAAA,CAAAI,GAAAK,KAAA,OAAqH,GAA7D,WAAAH,GAAAF,EAAAM,WAAA,EAAAJ,CAAAA,EAAAF,EAAAM,WAAA,CAAA3H,IAAA,EAA6DuH,QAAAA,GAAAA,QAAAA,EAAA,OAAA5H,MAAAwF,IAAA,CAAAkC,GAAsD,GAAAE,cAAAA,GAAA,2CAAAK,IAAA,CAAAL,GAAA,OAAoFuD,EAAiBzD,EAAAC,GAAA,CAElZ,SAASwD,EAAiBlG,CAAA,CAAAiD,CAAA,EAAaA,CAAAA,MAAAA,GAAAA,EAAAjD,EAAA9E,MAAA,GAAA+H,CAAAA,EAAAjD,EAAA9E,MAAA,EAAuD,QAAA6C,EAAA,EAAAmF,EAAA,MAAAD,GAAuClF,EAAAkF,EAASlF,IAAOmF,CAAA,CAAAnF,EAAA,CAAAiC,CAAA,CAAAjC,EAAA,CAAoB,OAAAmF,CAAA,CAMzK,SAASiD,EAAOxF,CAAA,CAAAC,CAAA,EAA2B,IAAAC,EAAAlE,OAAAkE,IAAA,CAAAF,GAAgC,GAAAhE,OAAAmE,qBAAA,EAAoC,IAAAC,EAAApE,OAAAmE,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAApD,MAAA,UAAAqD,CAAA,EAA6D,OAAArE,OAAAsE,wBAAA,CAAAN,EAAAK,GAAAhE,UAAA,EAAiE,EAAA6D,EAAA7C,IAAA,CAAAkD,KAAA,CAAAL,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAASuF,EAAahF,CAAA,EAAW,QAAArD,EAAA,EAAgBA,EAAAsD,UAAAnG,MAAA,CAAsB6C,IAAA,CAAO,IAAAuD,EAAA,MAAAD,SAAA,CAAAtD,EAAA,CAAAsD,SAAA,CAAAtD,EAAA,GAAuDA,CAAAA,EAAA,EAAQoI,EAAOxJ,OAAA2E,GAAA,IAAAC,OAAA,UAAAlE,CAAA,EAA8CgJ,EAAejF,EAAA/D,EAAAiE,CAAA,CAAAjE,EAAA,IAA6BV,OAAA8E,yBAAA,CAAA9E,OAAA+E,gBAAA,CAAAN,EAAAzE,OAAA8E,yBAAA,CAAAH,IAAmH6E,EAAOxJ,OAAA2E,IAAAC,OAAA,UAAAlE,CAAA,EAA0CV,OAAAC,cAAA,CAAAwE,EAAA/D,EAAAV,OAAAsE,wBAAA,CAAAK,EAAAjE,GAAA,EAAmF,CAAK,OAAA+D,CAAA,CAE1e,SAASiF,EAAe1E,CAAA,CAAAtE,CAAA,CAAAR,CAAA,EAA4K,OAAxJQ,KAAAsE,EAAkBhF,OAAAC,cAAA,CAAA+E,EAAAtE,EAAA,CAAkCR,MAAAA,EAAAG,WAAA,GAAAD,aAAA,GAAAD,SAAA,KAAgF6E,CAAA,CAAAtE,EAAA,CAAAR,EAAoB8E,CAAA,CAEpM,SAAA2E,EAAAhF,CAAA,CAAAiF,CAAA,EAAsD,GAAAjF,MAAAA,EAAA,SAA+B,IAA8DjE,EAAAU,EAA9DqD,EAAAoF,SAErFlF,CAAA,CAAAiF,CAAA,EAA2D,GAAAjF,MAAAA,EAAA,SAA+B,IAAuDjE,EAAAU,EAAvDqD,EAAA,GAAiBqF,EAAA9J,OAAAkE,IAAA,CAAAS,GAAkD,IAAAvD,EAAA,EAAYA,EAAA0I,EAAAvL,MAAA,CAAuB6C,IAAOV,EAAAoJ,CAAA,CAAA1I,EAAA,CAAqBwI,EAAA3I,OAAA,CAAAP,IAAA,GAA0C+D,CAAAA,CAAA,CAAA/D,EAAA,CAAAiE,CAAA,CAAAjE,EAAA,EAA6B,OAAA+D,CAAA,EAF9ME,EAAAiF,GAA0E,GAAA5J,OAAAmE,qBAAA,EAAoC,IAAA4F,EAAA/J,OAAAmE,qBAAA,CAAAQ,GAA6D,IAAAvD,EAAA,EAAYA,EAAA2I,EAAAxL,MAAA,CAA6B6C,IAAOV,EAAAqJ,CAAA,CAAA3I,EAAA,EAA2BwI,CAAAA,EAAA3I,OAAA,CAAAP,IAAA,IAA0CV,OAAAiG,SAAA,CAAA+D,oBAAA,CAAAtE,IAAA,CAAAf,EAAAjE,IAAwE+D,CAAAA,CAAA,CAAA/D,EAAA,CAAAiE,CAAA,CAAAjE,EAAA,EAA+B,OAAA+D,CAAA,CAwB5d,IAAAwF,EAA4B,GAAAC,EAAAC,UAAA,EAAU,SAAAC,CAAA,CAAAC,CAAA,EACtC,IAAAC,EAAAF,EAAAE,QAAA,CAGAC,EAAAC,EAFAb,EAAAS,EAAAtB,IAGA2B,EAAAF,EAAAE,IAAA,CACAC,EAAAf,EAAAY,EAAAxB,GAQA,MANE,GAAAmB,EAAAS,mBAAA,EAAmBN,EAAA,WACrB,OACAI,KAAAA,CACA,CACA,EAAG,CAAAA,EAAA,EAEmBP,EAAAU,aAAmB,CAACV,EAAAW,QAAQ,MAAAP,EAAiBb,EAAcA,EAAa,GAAGiB,GAAA,GAAY,CAC7GD,KAAAA,CACA,IACA,EACAR,CAAAA,EAAAa,WAAA,YAEA,IAAAC,EAAA,CACAC,SAAA,GACAC,kBH5DO,SAAAC,CAAA,EACP,MAAW,GAAArJ,EAAAC,EAAA,EAAS,sCACpB,EAAAoJ,IAaAtK,EAbAsK,EAAAlD,YAAA,EACAmD,SA8BAC,CAAA,CAAAzM,CAAA,EACA,MAAW,GAAAkD,EAAAC,EAAA,EAAS,+BAGpB,GAAAsJ,EAAAjK,KAAA,EACA,IAAAA,EAAAD,EAAAkK,EAAAjK,KAAA,EACAH,MAAA,CAAAO,GAAAA,SAAAA,EAAA8J,IAAA,QAGA,SAAA1M,EACAwC,EAGAL,EAAAwK,SAuCAA,EAAAnK,CAAA,EACA,OAAAA,EAAAoK,MAAA,EAAAC,EAAAzK,IAAA,IACAyK,KACApN,MAAAC,OAAA,CAAA0C,GAAAuK,EAAAvK,GAAA,CAAAA,EAAA,CACA,IACA,EA7CA,OAAA0B,QAAAS,GAAA,CAAA/B,EAAAgC,GAAA,CAAA7B,GAAA,GAEA,CACA,OAAAR,EAAAI,EAAAkK,EAAArK,KAAA,EACAoC,GAAA,CAAAlF,GAAyBqB,EAAcrB,IACvC,EACA,EAhDAiN,EAAAlD,YAAA,CAAAkD,EAAAvM,IAAA,EAeAiC,EAbAsK,IAaAtK,EAAAV,EAAAuE,MAAA,EAMAvD,EAAAgK,EAAAzG,MAAA,CAAA1D,KAAA,EAAAoC,GAAA,CAAAlF,GAAkDqB,EAAcrB,IAhBhE,MAAAI,OAAA,CAAA6M,IAAAA,EAAAO,KAAA,CAAAlK,GAAA,YAAAA,GAAA,mBAAAA,EAAAY,OAAA,EACAuJ,SAkBAC,CAAA,EACA,MAAW,GAAA9J,EAAAC,EAAA,EAAS,+BAEpB,MAAAf,CADA,MAAA0B,QAAAS,GAAA,CAAAyI,EAAAxI,GAAA,CAAA3D,GAAAA,EAAA2C,OAAA,MACAgB,GAAA,CAAAlF,GAAiCqB,EAAcrB,GAC/C,EACA,EAvBAiN,GAEA,IAEA,EGgDA/D,QAAAyE,IACAvE,QAAA,EACAwE,SAAA,GACAC,SAAA,EACAC,sBAAA,GACAC,QAAA,GACAC,WAAA,GACAC,OAAA,GACAC,qBAAA,GACAC,UAAA,KACAC,eAAA,GACAC,UAAA,EACA,CACArC,CAAAA,EAAAc,YAAA,CAAAA,EACAd,EAAAsC,SAAA,EAgBAjC,SAAYkC,EAAAC,IAAc,CAS1B9F,OAAU6F,EAAAE,QAAkB,CAACF,EAAAG,OAAiB,CAACH,EAAAI,MAAgB,GAK/Df,SAAYW,EAAAK,IAAc,CAK1Bd,sBAAyBS,EAAAK,IAAc,CAKvCb,QAAWQ,EAAAK,IAAc,CAMzBZ,WAAcO,EAAAK,IAAc,CAK5BX,OAAUM,EAAAK,IAAc,CAKxBV,qBAAwBK,EAAAK,IAAc,CAKtCxF,QAAWmF,EAAAM,MAAgB,CAK3B3F,QAAWqF,EAAAM,MAAgB,CAM3BhB,SAAYU,EAAAM,MAAgB,CAK5B9B,SAAYwB,EAAAK,IAAc,CAO1B5B,kBAAqBuB,EAAAC,IAAc,CAKnCM,mBAAsBP,EAAAC,IAAc,CAKpCO,iBAAoBR,EAAAC,IAAc,CAMlCJ,eAAkBG,EAAAK,IAAc,CAKhCP,UAAaE,EAAAK,IAAc,CAO3BI,YAAeT,EAAAC,IAAc,CAO7BS,YAAeV,EAAAC,IAAc,CAO7BU,WAAcX,EAAAC,IAAc,CAgC5BW,OAAUZ,EAAAC,IAAc,CASxBY,eAAkBb,EAAAC,IAAc,CAShCa,eAAkBd,EAAAC,IAAc,CAOhCc,QAAWf,EAAAC,IAAc,CAOzBL,UAAaI,EAAAC,IAAc,EAwE3B,IAAAe,EAAA,CACAC,UAAA,GACAC,mBAAA,GACAC,aAAA,GACAC,aAAA,GACAC,aAAA,GACA3P,cAAA,GACA4P,eAAA,IA+EO,SAAAtD,IACP,IAAAE,EAAAhG,UAAAnG,MAAA,IAAAmG,KAAAjE,IAAAiE,SAAA,IAAAA,SAAA,OAEAqJ,EAA4BtE,EAAcA,EAAa,GAAGsB,GAAAL,GAC1D/D,EAAAoH,EAAApH,MAAA,CACAqE,EAAA+C,EAAA/C,QAAA,CACAC,EAAA8C,EAAA9C,iBAAA,CACA9D,EAAA4G,EAAA5G,OAAA,CACAE,EAAA0G,EAAA1G,OAAA,CACAwE,EAAAkC,EAAAlC,QAAA,CACAC,EAAAiC,EAAAjC,QAAA,CACAmB,EAAAc,EAAAd,WAAA,CACAC,EAAAa,EAAAb,WAAA,CACAC,EAAAY,EAAAZ,UAAA,CACAC,EAAAW,EAAAX,MAAA,CACAC,EAAAU,EAAAV,cAAA,CACAC,EAAAS,EAAAT,cAAA,CACAP,EAAAgB,EAAAhB,kBAAA,CACAC,EAAAe,EAAAf,gBAAA,CACAX,EAAA0B,EAAA1B,cAAA,CACAC,EAAAyB,EAAAzB,SAAA,CACAP,EAAAgC,EAAAhC,qBAAA,CACAC,EAAA+B,EAAA/B,OAAA,CACAC,EAAA8B,EAAA9B,UAAA,CACAC,EAAA6B,EAAA7B,MAAA,CACAC,EAAA4B,EAAA5B,oBAAA,CACAoB,EAAAQ,EAAAR,OAAA,CACAnB,EAAA2B,EAAA3B,SAAA,CAEA4B,EAAmB,GAAA9D,EAAA+D,OAAA,EAAO,WAC1B,OAAWC,SDtMJvH,CAAA,EACP,GAAAe,EAAAf,GACA,OAAA3G,OAAA+C,OAAA,CAAA4D,GAAA4E,MAAA,UAAA4C,CAAA,CAAAC,CAAA,EACA,IAAAC,EAAApJ,EAAAmJ,EAAA,GACA1P,EAAA2P,CAAA,IACAxO,EAAAwO,CAAA,IAEA,SAAAvH,MAAA,CAAA1D,EAAA+K,GAAA,CAAAzP,EAAA,CAAA0E,EAAAvD,GACA,EAAK,IACLmB,MAAA,UAAAH,CAAA,EACA,OAAA+H,EAAA/H,IAAAgI,EAAAhI,EACA,GAAKkG,IAAA,KAIL,ECuLiCJ,EACjC,EAAG,CAAAA,EAAA,EACH2H,EAAoB,GAAApE,EAAA+D,OAAA,EAAO,WAC3B,ODlPA,ECkPkCtH,GDxNlC,EAEA4H,YAAA,QACA5H,OA5BA3G,OAAA+C,OAAA,CCiPkC4D,GDjPlC3F,MAAA,UAAAwN,CAAA,EACA,IAAAC,EAAAxJ,EAAAuJ,EAAA,GACA9P,EAAA+P,CAAA,IACA5O,EAAA4O,CAAA,IAEAC,EAAA,GAYA,OAVA9F,EAAAlK,KACAiQ,QAAAC,IAAA,aAAA9H,MAAA,CAAApI,EAAA,0KACAgQ,EAAA,IAGAtQ,MAAAC,OAAA,CAAAwB,IAAAA,EAAA4L,KAAA,CAAA5C,KACA8F,QAAAC,IAAA,aAAA9H,MAAA,CAAApI,EAAA,sDACAgQ,EAAA,IAGAA,CACA,GAAKnD,MAAA,UAAAsD,CAAA,CAAAC,CAAA,EACL,IAAAC,EAAA9J,EAAA6J,EAAA,GACApQ,EAAAqQ,CAAA,IACAlP,EAAAkP,CAAA,IAEA,OAAAvK,EAAAA,EAAA,GAA2CqK,GAAA,GAAUhK,EAAA,GAAoBnG,EAAAmB,GACzE,EAAK,GAKL,EAAK,CCoN6B8G,CAClC,EAAG,CAAAA,EAAA,EACHqI,EAA2B,GAAA9E,EAAA+D,OAAA,EAAO,WAClC,yBAAAjB,EAAAA,EAAAiC,EACA,EAAG,CAAAjC,EAAA,EACHkC,GAA6B,GAAAhF,EAAA+D,OAAA,EAAO,WACpC,yBAAAlB,EAAAA,EAAAkC,EACA,EAAG,CAAAlC,EAAA,EAMHoC,GAAgB,GAAAjF,EAAAkF,MAAA,EAAM,MACtBC,GAAiB,GAAAnF,EAAAkF,MAAA,EAAM,MAGvBE,GAAqBpG,EADD,GAAAgB,EAAAqF,UAAA,EAAUC,GAAAhC,GACK,GACnCiC,GAAAH,EAAA,IACAI,GAAAJ,EAAA,IAEA7B,GAAAgC,GAAAhC,SAAA,CACAC,GAAA+B,GAAA/B,kBAAA,CACAiC,GAA4B,GAAAzF,EAAAkF,MAAA,EAAM,oBAAAQ,QAAAA,OAAA3N,eAAA,EAAAoK,GDpRlC,uBAAAuD,QCsRAC,GAAA,WAEA,CAAAF,GAAAG,OAAA,EAAApC,IACAqC,WAAA,WACAV,GAAAS,OAAA,EAGA,CAAA/O,GAFA+O,OAAA,CAAA/O,KAAA,CAEAxC,MAAA,GACAmR,GAAA,CACA/Q,KAAA,aACA,GACAuQ,KAGA,EAAO,IAEP,EAEE,GAAAhF,EAAA8F,SAAA,EAAS,WAEX,OADAJ,OAAAK,gBAAA,SAAAJ,GAAA,IACA,WACAD,OAAAM,mBAAA,SAAAL,GAAA,GACA,CACA,EAAG,CAAAR,GAAA3B,GAAAwB,GAAAS,GAAA,EACH,IAAAQ,GAAuB,GAAAjG,EAAAkF,MAAA,EAAM,IAE7BgB,GAAA,SAAAvI,CAAA,EACAsH,GAAAW,OAAA,EAAAX,GAAAW,OAAA,CAAAO,QAAA,CAAAxI,EAAApD,MAAA,IAKAoD,EAAAM,cAAA,GACAgI,GAAAL,OAAA,IACA,EAEE,GAAA5F,EAAA8F,SAAA,EAAS,WAMX,OALAjE,IACAuE,SAAAL,gBAAA,YAA4C/H,EAAkB,IAC9DoI,SAAAL,gBAAA,QAAAG,GAAA,KAGA,WACArE,IACAuE,SAAAJ,mBAAA,YAAiDhI,GACjDoI,SAAAJ,mBAAA,QAAAE,IAEA,CACA,EAAG,CAAAjB,GAAApD,EAAA,EAED,GAAA7B,EAAA8F,SAAA,EAAS,WAKX,MAJA,CAAAhF,GAAAsB,GAAA6C,GAAAW,OAAA,EACAX,GAAAW,OAAA,CAAAS,KAAA,GAGA,YACA,EAAG,CAAApB,GAAA7C,EAAAtB,EAAA,EACH,IAAAwF,GAAgB,GAAAtG,EAAAuG,WAAA,EAAW,SAAAC,CAAA,EAC3BnD,EACAA,EAAAmD,GAGA/B,QAAAgC,KAAA,CAAAD,EAEA,EAAG,CAAAnD,EAAA,EACHqD,GAAsB,GAAA1G,EAAAuG,WAAA,EAAW,SAAA5I,CAAA,MA9iBNxE,EA+iB3BwE,EAAAM,cAAA,GAEAN,EAAAgJ,OAAA,GACAC,GAAAjJ,GACAsI,GAAAL,OAAA,IAAAhJ,MAAA,CAnjB0CiK,SAMf1N,CAAA,EAAQ,GAAAjF,MAAAC,OAAA,CAAAgF,GAAA,OAA+BkG,EAAiBlG,EAAA,EANxDA,EAmjB8B8M,GAAAL,OAAA,GAnjBYkB,SAI5CvN,CAAA,EAAS,uBAAAC,QAAAD,MAAAA,CAAA,CAAAC,OAAAC,QAAA,GAAAF,MAAAA,CAAA,sBAAArF,MAAAwF,IAAA,CAAAH,EAAA,EAJmDJ,IAASgG,EAA2BhG,IAAS4N,WAElG,2JAijByB,CAAApJ,EAAApD,MAAA,GAEjDsD,EAAcF,IACtBpF,QAAAC,OAAA,CAAAuI,EAAApD,IAAAqJ,IAAA,UAAAnQ,CAAA,EACA,GAAY6G,CAAAA,EAAoBC,IAAAsE,GAIhC,ID9cO/B,EACPrJ,EACA4F,EACAU,EACAF,EACA0E,EACAC,EACAM,ECucA+E,EAAApQ,EAAAxC,MAAA,CACAqP,EAAAuD,EAAA,ID9cApQ,EAAAqJ,CADOA,EC+cqD,CAC5DrJ,MAAAA,EACA4F,OAAAqH,EACA3G,QAAAA,EACAF,QAAAA,EACA0E,SAAAA,EACAC,SAAAA,EACAM,UAAAA,CACA,GDtdArL,KAAA,CACA4F,EAAAyD,EAAAzD,MAAA,CACAU,EAAA+C,EAAA/C,OAAA,CACAF,EAAAiD,EAAAjD,OAAA,CACA0E,EAAAzB,EAAAyB,QAAA,CACAC,EAAA1B,EAAA0B,QAAA,CACAM,EAAAhC,EAAAgC,SAAA,CAEA,GAAAP,IAAA9K,CAAAA,EAAAxC,MAAA,MAAAsN,CAAAA,CAAAA,IAAAC,CAAAA,GAAA,KAAA/K,CAAAA,EAAAxC,MAAA,CAAAuN,CAAA,IAIA/K,EAAA0K,KAAA,UAAAxN,CAAA,EACA,IAEAmT,EAAAC,EAFA9J,EAAAtJ,EAAA0I,GACA,EACA,IAIA2K,EAAAC,EAFA9J,EAAAxJ,EAAAoJ,EAAAF,GACA,EACA,IAEAqK,EAAApF,EAAAA,EAAAnO,GAAA,KACA,OAAAmT,GAAAE,GAAA,CAAAE,CACA,ICicA9B,GAAA,CACA9B,aAAAA,EACAC,aAHAsD,EAAA,IAAAvD,EAIAD,aAAA,GACAhP,KAAA,iBACA,GAEAsO,GACAA,EAAApF,GAEA,GAAO4J,KAAA,UAAAf,CAAA,EACP,OAAAF,GAAAE,EACA,EAEA,EAAG,CAAAzF,EAAAgC,EAAAuD,GAAArE,EAAA6B,EAAA3G,EAAAF,EAAA0E,EAAAC,EAAAM,EAAA,EACHsF,GAAqB,GAAAxH,EAAAuG,WAAA,EAAW,SAAA5I,CAAA,EAChCA,EAAAM,cAAA,GACAN,EAAAgJ,OAAA,GACAC,GAAAjJ,GACA,IAAA8J,EAAmB5J,EAAcF,GAEjC,GAAA8J,GAAA9J,EAAAG,YAAA,CACA,IACAH,EAAAG,YAAA,CAAA4J,UAAA,OACA,CAAQ,MAAAC,EAAA,EASR,OAJAF,GAAAxE,GACAA,EAAAtF,GAGA,EACA,EAAG,CAAAsF,EAAAhB,EAAA,EACH2F,GAAsB,GAAA5H,EAAAuG,WAAA,EAAW,SAAA5I,CAAA,EACjCA,EAAAM,cAAA,GACAN,EAAAgJ,OAAA,GACAC,GAAAjJ,GAEA,IAAAkK,EAAA5B,GAAAL,OAAA,CAAA9O,MAAA,UAAAyD,CAAA,EACA,OAAA0K,GAAAW,OAAA,EAAAX,GAAAW,OAAA,CAAAO,QAAA,CAAA5L,EACA,GAGAuN,EAAAD,EAAA9Q,OAAA,CAAA4G,EAAApD,MAAA,CAEA,MAAAuN,GACAD,EAAAE,MAAA,CAAAD,EAAA,GAGA7B,GAAAL,OAAA,CAAAiC,GAEAA,CAAAA,EAAAxT,MAAA,MAIAmR,GAAA,CACA/Q,KAAA,kBACAgP,aAAA,GACAC,aAAA,GACAC,aAAA,EACA,GAEQ9F,EAAcF,IAAAqF,GACtBA,EAAArF,GAEA,EAAG,CAAAsH,GAAAjC,EAAAf,EAAA,EACH+F,GAAiB,GAAAhI,EAAAuG,WAAA,EAAW,SAAA1P,CAAA,CAAA8G,CAAA,EAC5B,IAAA3J,EAAA,GACA4P,EAAA,GACA/M,EAAA6D,OAAA,UAAA3G,CAAA,EACA,IACAoT,EAA2BnI,EADD3B,EAAYtJ,EAAA+P,GACG,GACzCoD,EAAAC,CAAA,IACAc,EAAAd,CAAA,IAGAE,EAA4BrI,EADDzB,EAAaxJ,EAAAoJ,EAAAF,GACE,GAC1CmK,EAAAC,CAAA,IACAa,EAAAb,CAAA,IAEAC,EAAApF,EAAAA,EAAAnO,GAAA,KAEA,GAAAmT,GAAAE,GAAA,CAAAE,EACAtT,EAAAmD,IAAA,CAAApD,OACQ,CACR,IAAAoU,EAAA,CAAAF,EAAAC,EAAA,CAEAZ,GACAa,CAAAA,EAAAA,EAAAvL,MAAA,CAAA0K,EAAA,EAGA1D,EAAAzM,IAAA,EACApD,KAAAA,EACAoU,OAAAA,EAAArR,MAAA,UAAA0P,CAAA,EACA,OAAAA,CACA,EACA,EACA,CACA,GAEA,EAAA7E,GAAA3N,EAAAK,MAAA,IAAAsN,GAAAC,GAAA,GAAA5N,EAAAK,MAAA,CAAAuN,CAAA,IAEA5N,EAAA0G,OAAA,UAAA3G,CAAA,EACA6P,EAAAzM,IAAA,EACApD,KAAAA,EACAoU,OAAA,CAAmB/K,EAAwB,EAE3C,GACApJ,EAAA+T,MAAA,KAGAvC,GAAA,CACAxR,cAAAA,EACA4P,eAAAA,EACAD,aAAAC,EAAAvP,MAAA,GACAI,KAAA,UACA,GAEAyO,GACAA,EAAAlP,EAAA4P,EAAAjG,GAGAiG,EAAAvP,MAAA,IAAA+O,GACAA,EAAAQ,EAAAjG,GAGA3J,EAAAK,MAAA,IAAA8O,GACAA,EAAAnP,EAAA2J,EAEA,EAAG,CAAA6H,GAAA7D,EAAAmC,EAAA3G,EAAAF,EAAA2E,EAAAsB,EAAAC,EAAAC,EAAAlB,EAAA,EACHkG,GAAiB,GAAApI,EAAAuG,WAAA,EAAW,SAAA5I,CAAA,EAC5BA,EAAAM,cAAA,GAEAN,EAAAgJ,OAAA,GACAC,GAAAjJ,GACAsI,GAAAL,OAAA,IAEQ/H,EAAcF,IACtBpF,QAAAC,OAAA,CAAAuI,EAAApD,IAAAqJ,IAAA,UAAAnQ,CAAA,EACY6G,CAAAA,CAAAA,EAAoBC,IAAAsE,CAAA,GAIhC+F,GAAAnR,EAAA8G,EACA,GAAO4J,KAAA,UAAAf,CAAA,EACP,OAAAF,GAAAE,EACA,GAGAhB,GAAA,CACA/Q,KAAA,OACA,EACA,EAAG,CAAAsM,EAAAiH,GAAA1B,GAAArE,EAAA,EAEHoG,GAAuB,GAAArI,EAAAuG,WAAA,EAAW,WAGlC,GAAAd,GAAAG,OAAA,EACAJ,GAAA,CACA/Q,KAAA,YACA,GACAqQ,IAMAY,OAAA4C,kBAAA,CAJA,CACA3G,SAAAA,EACA5D,MAAAqG,CACA,GACA4C,IAAA,UAAAvF,CAAA,EACA,OAAAV,EAAAU,EACA,GAAOuF,IAAA,UAAAnQ,CAAA,EACPmR,GAAAnR,EAAA,MACA2O,GAAA,CACA/Q,KAAA,aACA,EACA,GAAO8S,KAAA,UAAAf,CAAA,EDldP7P,aAAA4R,cAAA5R,CAAAA,eAAAA,EAAApC,IAAA,EAAAoC,EAAAmG,IAAA,GAAAnG,EAAA6R,SAAA,GCqdAxD,GAAAwB,GACAhB,GAAA,CACA/Q,KAAA,aACA,ID7cAkC,aAAA4R,cAAA5R,CAAAA,kBAAAA,EAAApC,IAAA,EAAAoC,EAAAmG,IAAA,GAAAnG,EAAA8R,YAAA,GC+cAhD,GAAAG,OAAA,IAGAT,GAAAS,OAAA,EACAT,GAAAS,OAAA,CAAA5P,KAAA,MACAmP,GAAAS,OAAA,CAAA8C,KAAA,IAEApC,GAAA,yKAGAA,GAAAE,EAEA,GACA,MACA,CAEArB,GAAAS,OAAA,GACAJ,GAAA,CACA/Q,KAAA,YACA,GACAqQ,IACAK,GAAAS,OAAA,CAAA5P,KAAA,MACAmP,GAAAS,OAAA,CAAA8C,KAAA,GAEA,EAAG,CAAAlD,GAAAV,EAAAE,GAAA7C,EAAA6F,GAAA1B,GAAAlC,EAAAzC,EAAA,EAEHgH,GAAoB,GAAA3I,EAAAuG,WAAA,EAAW,SAAA5I,CAAA,EAE/BsH,GAAAW,OAAA,EAAAX,GAAAW,OAAA,CAAAgD,WAAA,CAAAjL,EAAApD,MAAA,GAIAoD,CAAAA,MAAAA,EAAAnH,GAAA,EAAAmH,UAAAA,EAAAnH,GAAA,EAAAmH,KAAAA,EAAAkL,OAAA,EAAAlL,KAAAA,EAAAkL,OAAA,IACAlL,EAAAM,cAAA,GACAoK,KAEA,EAAG,CAAApD,GAAAoD,GAAA,EAEHS,GAAkB,GAAA9I,EAAAuG,WAAA,EAAW,WAC7Bf,GAAA,CACA/Q,KAAA,OACA,EACA,EAAG,IACHsU,GAAiB,GAAA/I,EAAAuG,WAAA,EAAW,WAC5Bf,GAAA,CACA/Q,KAAA,MACA,EACA,EAAG,IAEHuU,GAAkB,GAAAhJ,EAAAuG,WAAA,EAAW,WAC7BzE,IAOQmH,WDxoBR,IAAAC,EAAA1O,UAAAnG,MAAA,IAAAmG,KAAAjE,IAAAiE,SAAA,IAAAA,SAAA,IAAAkL,OAAAyD,SAAA,CAAAD,SAAA,CACA,OAAAE,KATAF,EAAAnS,OAAA,UAAAmS,KAAAA,EAAAnS,OAAA,cAIAmS,KAAAA,EAAAnS,OAAA,SAMA,ICuoBA8O,WAAAwC,GAAA,GAEAA,KAEA,EAAG,CAAAvG,EAAAuG,GAAA,EAEHgB,GAAA,SAAA5K,CAAA,EACA,OAAAqC,EAAA,KAAArC,CACA,EAEA6K,GAAA,SAAA7K,CAAA,EACA,OAAAsD,EAAA,KAAAsH,GAAA5K,EACA,EAEA8K,GAAA,SAAA9K,CAAA,EACA,OAAAuD,EAAA,KAAAqH,GAAA5K,EACA,EAEAmI,GAAA,SAAAjJ,CAAA,EACAsE,GACAtE,EAAAiJ,eAAA,EAEA,EAEA4C,GAAqB,GAAAxJ,EAAA+D,OAAA,EAAO,WAC5B,kBACA,IAAAO,EAAA9J,UAAAnG,MAAA,IAAAmG,KAAAjE,IAAAiE,SAAA,IAAAA,SAAA,OACAiP,EAAAnF,EAAAoF,MAAA,CAEAC,EAAArF,EAAAqF,IAAA,CACAC,EAAAtF,EAAAsF,SAAA,CACAC,EAAAvF,EAAAuF,OAAA,CACAC,EAAAxF,EAAAwF,MAAA,CACAC,EAAAzF,EAAAyF,OAAA,CACAhH,EAAAuB,EAAAvB,WAAA,CACAE,EAAAqB,EAAArB,UAAA,CACAD,EAAAsB,EAAAtB,WAAA,CACAE,EAAAoB,EAAApB,MAAA,CACA8G,EAAAvK,EAAA6E,EAAAxF,GAEA,OAAaS,EAAcA,EAAcC,EAAe,CACxDoK,UAAAN,GAA0CpL,EAAoB0L,EAAAjB,KAC9DkB,QAAAP,GAAwCpL,EAAoB2L,EAAAf,KAC5DgB,OAAAR,GAAuCpL,EAAoB4L,EAAAf,KAC3DgB,QAAAV,GAAgCnL,EAAoB6L,EAAAf,KACpDjG,YAAAwG,GAAwCrL,EAAoB6E,EAAA2D,KAC5DzD,WAAAsG,GAAuCrL,EAAoB+E,EAAAuE,KAC3DxE,YAAAuG,GAAwCrL,EAAoB8E,EAAA4E,KAC5D1E,OAAAqG,GAAmCrL,EAAoBgF,EAAAkF,KACvDuB,KAAA,iBAAAA,GAAAA,KAAAA,EAAAA,EAAA,cACA,EAtBAF,KAAA,IAAAA,EAAA,MAAAA,EAsBOxE,IAAA,GAAAlD,EAEC,GAFD,CACPkI,SAAA,CACA,GAAYD,EACZ,CACA,EAAG,CAAA/E,GAAA0D,GAAAG,GAAAC,GAAAC,GAAAtC,GAAAc,GAAAI,GAAAQ,GAAArG,EAAAC,EAAAlB,EAAA,EACHoJ,GAA4B,GAAAlK,EAAAuG,WAAA,EAAW,SAAA5I,CAAA,EACvCA,EAAAiJ,eAAA,EACA,EAAG,IACHuD,GAAsB,GAAAnK,EAAA+D,OAAA,EAAO,WAC7B,kBACA,IAAAQ,EAAA/J,UAAAnG,MAAA,IAAAmG,KAAAjE,IAAAiE,SAAA,IAAAA,SAAA,OACA4P,EAAA7F,EAAAmF,MAAA,CAEAW,EAAA9F,EAAA8F,QAAA,CACAN,EAAAxF,EAAAwF,OAAA,CACAC,EAAAvK,EAAA8E,EAAAxF,GAuBA,OAAaQ,EAAcA,EAAa,GArBjBC,EAAe,CACtC/C,OAAAqH,EACAnC,SAAAA,EACAlN,KAAA,OACA6V,MAAA,CACAC,OAAA,EACAC,KAAA,mBACAC,SAAA,aACAC,OAAA,MACAC,OAAA,gBACAC,SAAA,SACAC,QAAA,EACAC,SAAA,WACAC,MAAA,MACAC,WAAA,QACA,EACAX,SAAAhB,GAAiCnL,EAAoBmM,EAAAjC,KACrD2B,QAAAV,GAAgCnL,EAAoB6L,EAAAG,KACpDD,SAAA,EACA,EAxBAG,KAAA,IAAAA,EAAA,MAAAA,EAwBOjF,KAEoC6E,EAC3C,CACA,EAAG,CAAA7E,GAAA1I,EAAAkF,EAAAyG,GAAAtH,EAAA,EACH,OAASvB,EAAcA,EAAa,GAAGgG,IAAA,GAAY,CACnDhC,UAAAA,IAAA,CAAAzC,EACA0I,aAAAA,GACAW,cAAAA,GACAlF,QAAAA,GACAE,SAAAA,GACA5E,KAAA8I,GAAAhB,GACA,EACA,CAOA,SAAA/C,GAAAC,CAAA,CAAA0F,CAAA,EAEA,OAAAA,EAAAxW,IAAA,EACA,YACA,OAAa8K,EAAcA,EAAa,GAAGgG,GAAA,GAAY,CACvDhC,UAAA,EACA,EAEA,YACA,OAAahE,EAAcA,EAAa,GAAGgG,GAAA,GAAY,CACvDhC,UAAA,EACA,EAEA,kBACA,OAAahE,EAAcA,EAAa,GAAG+D,GAAA,GAAmB,CAC9DE,mBAAA,EACA,EAEA,mBACA,OAAajE,EAAcA,EAAa,GAAGgG,GAAA,GAAY,CACvD/B,mBAAA,EACA,EAEA,uBACA,OAAajE,EAAcA,EAAa,GAAGgG,GAAA,GAAY,CACvD9B,aAAAwH,EAAAxH,YAAA,CACAC,aAAAuH,EAAAvH,YAAA,CACAC,aAAAsH,EAAAtH,YAAA,EAGA,gBACA,OAAapE,EAAcA,EAAa,GAAGgG,GAAA,GAAY,CACvDvR,cAAAiX,EAAAjX,aAAA,CACA4P,eAAAqH,EAAArH,cAAA,CACAD,aAAAsH,EAAAtH,YAAA,EAGA,aACA,OAAapE,EAAa,GAAG+D,EAE7B,SACA,OAAAiC,CACA,CACA,CAEA,SAAAR,KAAA", "sources": ["webpack://_N_E/./node_modules/attr-accept/dist/es/index.js", "webpack://_N_E/./node_modules/file-selector/dist/es2015/file.js", "webpack://_N_E/./node_modules/file-selector/dist/es2015/file-selector.js", "webpack://_N_E/./node_modules/file-selector/dist/es2015/index.js", "webpack://_N_E/./node_modules/react-dropzone/dist/es/utils/index.js", "webpack://_N_E/./node_modules/react-dropzone/dist/es/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n\n    if (acceptedFilesArray.length === 0) {\n      return true;\n    }\n\n    var fileName = file.name || '';\n    var mimeType = (file.type || '').toLowerCase();\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim().toLowerCase();\n\n      if (validType.charAt(0) === '.') {\n        return fileName.toLowerCase().endsWith(validType);\n      } else if (validType.endsWith('/*')) {\n        // This is something like a image/* mime type\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n\n      return mimeType === validType;\n    });\n  }\n\n  return true;\n};", "export const COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nexport function toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map", "import { __awaiter } from \"tslib\";\nimport { toFileWithPath } from './file';\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => toFileWithPath(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => toFileWithPath(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return __awaiter(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => toFileWithPath(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return toFileWithPath(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = toFileWithPath(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => __awaiter(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = toFileWithPath(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map", "export { fromEvent } from './file-selector';\n//# sourceMappingURL=index.js.map", "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport _accepts from \"attr-accept\";\nvar accepts = typeof _accepts === \"function\" ? _accepts : _accepts.default; // Error codes\n\nexport var FILE_INVALID_TYPE = \"file-invalid-type\";\nexport var FILE_TOO_LARGE = \"file-too-large\";\nexport var FILE_TOO_SMALL = \"file-too-small\";\nexport var TOO_MANY_FILES = \"too-many-files\";\nexport var ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nexport var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nexport var getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nexport function fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nexport function fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nexport function allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nexport function isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nexport function isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nexport function isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nexport function onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nexport function isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nexport function composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nexport function canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nexport function pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nexport function acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nexport function isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nexport function isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nexport function isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nexport function isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */", "var _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\nimport React, { forwardRef, Fragment, useCallback, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { fromEvent } from \"file-selector\";\nimport { acceptPropAsAcceptAttr, allFilesAccepted, composeEventHandlers, fileAccepted, fileMatchSize, canUseFileSystemAccessAPI, isAbort, isEvtWithFiles, isIeOrEdge, isPropagationStopped, isSecurityError, onDocumentDragOver, pickerOptionsFromAccept, TOO_MANY_FILES_REJECTION } from \"./utils/index.js\";\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/forwardRef(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  useImperativeHandle(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/React.createElement(Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: PropTypes.func,\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: PropTypes.bool,\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: PropTypes.bool,\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: PropTypes.bool,\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: PropTypes.bool,\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: PropTypes.bool,\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: PropTypes.bool,\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: PropTypes.number,\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: PropTypes.number,\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: PropTypes.number,\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: PropTypes.func,\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: PropTypes.func,\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: PropTypes.func,\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: PropTypes.bool,\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: PropTypes.bool,\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: PropTypes.func,\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: PropTypes.func,\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: PropTypes.func,\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: PropTypes.func,\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: PropTypes.func\n};\nexport default Dropzone;\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nexport function useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = useMemo(function () {\n    return acceptPropAsAcceptAttr(accept);\n  }, [accept]);\n  var pickerTypes = useMemo(function () {\n    return pickerOptionsFromAccept(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = useMemo(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = useMemo(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = useRef(null);\n  var inputRef = useRef(null);\n\n  var _useReducer = useReducer(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = useRef(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && canUseFileSystemAccessAPI()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  useEffect(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = useRef([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  useEffect(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  useEffect(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = useCallback(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && allFilesAccepted({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = isEvtWithFiles(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if (isEvtWithFiles(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = useCallback(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = fileAccepted(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = useCallback(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if (isAbort(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if (isSecurityError(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = useCallback(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = useCallback(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = useCallback(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = useCallback(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if (isIeOrEdge()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = useMemo(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler(composeEventHandlers(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler(composeEventHandlers(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler(composeEventHandlers(onBlur, onBlurCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onClickCb)),\n        onDragEnter: composeDragHandler(composeEventHandlers(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler(composeEventHandlers(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler(composeEventHandlers(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler(composeEventHandlers(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = useCallback(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = useMemo(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler(composeEventHandlers(onChange, onDropCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\nexport { ErrorCode } from \"./utils/index.js\";"], "names": ["exports", "__esModule", "file", "acceptedFiles", "acceptedFilesArray", "Array", "isArray", "split", "length", "fileName", "name", "mimeType", "type", "toLowerCase", "baseMimeType", "replace", "some", "validType", "trim", "char<PERSON>t", "endsWith", "COMMON_MIME_TYPES", "Map", "toFileWithPath", "path", "h", "f", "withMimeType", "hasExtension", "lastIndexOf", "ext", "pop", "get", "Object", "defineProperty", "value", "writable", "configurable", "enumerable", "webkitRelativePath", "p", "setObjProp", "undefined", "key", "FILES_TO_IGNORE", "isObject", "v", "noIgnoredFiles", "files", "filter", "indexOf", "fromList", "items", "i", "push", "toFilePromises", "item", "webkitGetAsEntry", "fromDataTransferItem", "entry", "isDirectory", "fromDirEntry", "tslib_es6", "mG", "_a", "globalThis", "isSecureContext", "getAsFileSystemHandle", "getFile", "handle", "getAsFile", "fullPath", "fromEntry", "fromFileEntry", "Promise", "resolve", "reject", "err", "reader", "createReader", "entries", "readEntries", "batch", "all", "map", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_arrayLikeToArray", "_iterableToArray", "iter", "Symbol", "iterator", "from", "_unsupportedIterableToArray", "_nonIterableSpread", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "sym", "getOwnPropertyDescriptor", "apply", "_objectSpread", "target", "arguments", "source", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "obj", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_s", "_e", "_i", "_arr", "_n", "_d", "call", "next", "done", "_nonIterableRest", "o", "minLen", "n", "prototype", "toString", "slice", "constructor", "test", "len", "arr2", "accepts", "es", "getInvalidTypeRejectionErr", "accept", "acceptArr", "msg", "concat", "join", "code", "message", "getTooLargeRejectionErr", "maxSize", "getTooSmallRejectionErr", "minSize", "TOO_MANY_FILES_REJECTION", "fileAccepted", "isAcceptable", "fileMatchSize", "isDefined", "size", "isPropagationStopped", "event", "cancelBubble", "isEvtWithFiles", "dataTransfer", "types", "onDocumentDragOver", "preventDefault", "composeEventHandlers", "_len", "fns", "_key", "_len2", "args", "_key2", "fn", "isMIMEType", "isExt", "_excluded", "_excluded2", "_excluded3", "_excluded4", "es_slicedToArray", "es_arrayWithHoles", "es_iterableToArrayLimit", "es_unsupportedIterableToArray", "es_nonIterableRest", "es_arrayLikeToArray", "es_ownKeys", "es_objectSpread", "es_defineProperty", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceKeys", "sourceSymbolKeys", "propertyIsEnumerable", "Dropzone", "react", "forwardRef", "_ref", "ref", "children", "_useDropzone", "useDropzone", "open", "props", "useImperativeHandle", "createElement", "Fragment", "displayName", "defaultProps", "disabled", "getFilesFromEvent", "evt", "getDataTransferFiles", "dt", "kind", "flatten", "reduce", "acc", "every", "getFsHandleFiles", "handles", "Infinity", "multiple", "maxFiles", "preventDropOnDocument", "noClick", "noKeyboard", "noDrag", "noDragEventsBubbling", "validator", "useFsAccessApi", "autoFocus", "propTypes", "prop_types", "func", "objectOf", "arrayOf", "string", "bool", "number", "onFileDialogCancel", "onFileDialogOpen", "onDragEnter", "onDragLeave", "onDragOver", "onDrop", "onDropAccepted", "onDropRejected", "onError", "initialState", "isFocused", "isFileDialogActive", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "_defaultProps$props", "acceptAttr", "useMemo", "acceptPropAsAcceptAttr", "a", "_ref6", "_ref7", "pickerTypes", "description", "_ref2", "_ref3", "ok", "console", "warn", "agg", "_ref4", "_ref5", "onFileDialogOpenCb", "noop", "onFileDialogCancelCb", "rootRef", "useRef", "inputRef", "_useReducer2", "useReducer", "reducer", "state", "dispatch", "fsAccessApiWorksRef", "window", "onWindowFocus", "current", "setTimeout", "useEffect", "addEventListener", "removeEventListener", "dragTargetsRef", "onDocumentDrop", "contains", "document", "focus", "onErrCb", "useCallback", "e", "error", "onDragEnterCb", "persist", "stopPropagation", "es_arrayWithoutHoles", "es_iterableToArray", "es_nonIterableSpread", "then", "fileCount", "accepted", "_fileAccepted2", "sizeMatch", "_fileMatchSize2", "customErrors", "catch", "onDragOverCb", "hasFiles", "dropEffect", "_unused", "onDragLeaveCb", "targets", "targetIdx", "splice", "setFiles", "acceptError", "sizeError", "errors", "onDropCb", "openFileDialog", "showOpenFilePicker", "DOMException", "ABORT_ERR", "SECURITY_ERR", "click", "onKeyDownCb", "isEqualNode", "keyCode", "onFocusCb", "onBlurCb", "onClickCb", "isIeOrEdge", "userAgent", "navigator", "isIe", "<PERSON><PERSON><PERSON><PERSON>", "composeKeyboardHandler", "composeDragHandler", "getRootProps", "_ref2$refKey", "refKey", "role", "onKeyDown", "onFocus", "onBlur", "onClick", "rest", "tabIndex", "onInputElementClick", "getInputProps", "_ref3$refKey", "onChange", "style", "border", "clip", "clipPath", "height", "margin", "overflow", "padding", "position", "width", "whiteSpace", "action"], "sourceRoot": ""}