{"version": 3, "file": "app/(protected)/@agent/deposit-request/[trxId]/page.js", "mappings": "qFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,2CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,kGEAAC,gxBDKA,IAAAC,EACA,CACA,GACA,CACAC,SAAA,CACA,cACA,CACAC,MAAA,CACA,WACA,CACAD,SAAA,CACA,kBACA,CACAA,SAAA,CACA,UACA,CACAA,SAAA,eAAiC,CACjCE,KAAA,KAAuBC,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,MAAiK,iIAE/K,EAET,CACA,aAA0BH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoK,oIAG9L,EAEA,CACA,aAA0BH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAA2J,2HAGrL,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAyI,wGAClK,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0I,0GAG5J,CACAN,SAAA,CACA,cACA,GACA,CACAQ,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAG,MAAA,CACA,cACA,GACA,CACAD,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAI,SAAA,CACA,cACA,GACA,CACAF,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAK,SAAA,CACA,cACA,GACA,CACAH,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAiI,gGAC1J,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkI,kGAGpJ,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoH,mFAC7I,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqH,oFACvI,iBAAoBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAAuH,uFAG3I,CAEAM,EAAA,iIAKOC,EAAA,mDACAC,EAAA,CACPjB,QAJ6BS,EAK7BS,UAJA,IAAAZ,QAAAC,OAAA,EAKA,EAGOY,EAAA,IAAwBC,EAAAC,kBAAkB,EACjDC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,QAAA,CACvBrB,KAAA,mDACAsB,SAAA,2BAEAC,WAAA,GACAC,SAAA,GACAC,SAAA,IAEAC,SAAA,CACAC,WAAA9B,CACA,CACA,uCC7FA,IAAA+B,EAAA,GAAAC,EAAAC,KAAAC,KAAA,CAAAF,GAAAG,KAAAA,EACAC,EAAAC,KAAAC,gBAAA,CACAC,EAAAR,EAAAM,KAAAG,yBAAA,EACAC,EAAA,MAAA1C,CAAAA,EAAAsC,KAAAK,cAAA,SAAA3C,CAAA,qDACA4C,EAAAZ,EAAAM,KAAAO,qBAAA,EAEAC,EAAAd,EAAAM,KAAAS,oBAAA,EACAC,EAAAhB,EAAAM,KAAAW,qCAAA,MACAP,GAAAE,GACI,GAAAM,EAAAC,EAAA,EAA8B,CAClCC,wBAAAV,EACAW,sBAAAT,EACAU,gBAAyB,GAAAC,EAAAC,CAAA,EAAqB,CAC9CH,sBAAAT,EACAa,SAAA,kDACA,EACA,GAEA,IAAMC,EAAS,GAAAC,EAAAC,CAAA,EAAS,CACxBC,UAAeC,EAAAC,CAAU,CAAAC,GAAA,CACzBC,IAvBA,GAwBA7D,KAAA,mDACA8D,OA9BA,KA+BAC,QAAWC,EACXC,SA/BA,KAgCAC,YA/BA,KAgCAC,SAnCA,KAoCAlC,cAAAA,EACAmC,aAAgBC,EAAAC,CAAA,CAChBlC,sBAAAA,EACAY,wBAAAV,EACAW,sBAAAT,EACA+B,cAlCAvC,KAAAA,EAmCAwC,6BA5BAxC,KAAAA,EA6BAyC,OAnCA,CAAoB,OAAQ,qBAA0B,uBAA2B,YAAe,mDAAyD,qPAAwR,wTAAkV,iDAAuD,sCAAyC,CAAE,gCAAmC,iBAAsB,eAAkB,uDAA4D,iBAAoB,wCAA6C,KAAQ,kBAAmB,2BAA+B,iIAAuJ,uBAAyB,mEAA6E,cAAiB,oFAAiG,uBAAuB,0CAA2C,EAAE,QAAW,6BAA8B,GAAG,cAAiB,w6BAA4lC,uBAA0B,o9BAAs9B,+HAoCnqHC,QAAAC,QAAAC,GAAA,CAAAC,eAAA,CACAnC,iBAAAA,EACAoC,wBA/CA,KAgDAlC,0BAAAA,CACA,GACOmC,EAAqBf,EACb,SAAAgB,EAAAC,CAAA,EACf,MAAW,GAAAC,EAAAC,CAAA,EAAO,CAClB,GAAAF,CAAA,CACAG,iBAAwBC,EAAAC,CAAA,CACxBC,QAAiBjC,CACjB,EACA,mBCnEArD,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,2KCGO,eAAeoF,EAAqBC,CAAmB,EAC5D,GAAI,CACF,IAAMC,EAAM,MAAMC,EAAAA,CAAKA,CAACC,GAAG,CAAC,CAAC,yBAAyB,EAAEH,EAAG,CAAC,CAAE,CAAEA,GAAAA,CAAG,GACnE,MAAOI,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBH,EAC3B,CAAE,MAAOI,EAAO,CACd,MAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAuBD,EAChC,CACF,CCPO,eAAeE,EAAqBP,CAAmB,EAC5D,GAAI,CACF,IAAMC,EAAM,MAAMC,EAAAA,CAAKA,CAACC,GAAG,CAAC,CAAC,0BAA0B,EAAEH,EAAG,CAAC,CAAE,CAAEA,GAAAA,CAAG,GACpE,MAAOI,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBH,EAC3B,CAAE,MAAOI,EAAO,CACd,MAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAuBD,EAChC,CACF,wICcO,IAAMG,EAAU,OAER,SAASC,IACtB,GAAM,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACRC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACT,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,OAAAA,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,EAClC,CAAC,kBAAkB,EAAEL,EAAOM,KAAK,CAAC,CAAC,EAIrC,GAAIH,EACF,MACE,GAAAI,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,kDACb,GAAAH,EAAAC,GAAA,EAACG,EAAAA,MAAMA,CAAAA,CAAAA,KAMb,IAAMC,EAA6B,IACjCC,EAAAA,KAAKA,CAACC,OAAO,CAAC3B,EAAqBC,GAAK,CACtC2B,QAASjB,EAAE,cACXkB,QAAS,IACP,GAAI,CAAC3B,EAAI4B,MAAM,CAAE,MAAM,MAAU5B,EAAI6B,OAAO,EAE5C,OADAd,EAAOF,GACAb,EAAI6B,OAAO,EAEpBzB,MAAO,GAAS0B,EAAID,OAAO,EAE/B,EAEME,EAA6B,IACjCP,EAAAA,KAAKA,CAACC,OAAO,CAACnB,EAAqBP,GAAK,CACtC2B,QAASjB,EAAE,cACXkB,QAAS,IACP,GAAI,CAAC3B,EAAI4B,MAAM,CAAE,MAAM,MAAU5B,EAAI6B,OAAO,EAE5C,OADAd,EAAOF,GACAb,EAAI6B,OAAO,EAEpBzB,MAAO,GAAS0B,EAAID,OAAO,EAE/B,EAEMG,EAAUnB,GAAMA,KAAO,IAAIoB,EAAAA,CAAeA,CAACpB,GAAMA,MAAQ,KACzDqB,EAAW,IAAIC,EAAAA,CAAQA,QAE7B,EAUE,GAAAjB,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,oCACb,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,oCAEb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACb,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,sEACb,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,4DACb,GAAAH,EAAAC,GAAA,EAACkB,EAAAA,CAAIA,CAAAA,CAACC,UAAWN,cAAAA,EAAQJ,MAAM,UAC7B,GAAAV,EAAAC,GAAA,EAACoB,EAAAA,CAAUA,CAAAA,CAACC,QAAQ,OAAOC,KAAM,GAAIpB,UAAU,mBAEjD,GAAAH,EAAAC,GAAA,EAACkB,EAAAA,CAAIA,CAAAA,CAACC,UAAWN,WAAAA,EAAQJ,MAAM,UAC7B,GAAAV,EAAAC,GAAA,EAACuB,EAAAA,CAAWA,CAAAA,CACVF,QAAQ,OACRC,KAAM,GACNpB,UAAU,uBAGd,GAAAH,EAAAC,GAAA,EAACkB,EAAAA,CAAIA,CAAAA,CAACC,UAAWN,YAAAA,EAAQJ,MAAM,UAC7B,GAAAV,EAAAC,GAAA,EAACwB,EAAAA,CAAUA,CAAAA,CAACH,QAAQ,OAAOC,KAAM,GAAIpB,UAAU,mBAEjD,GAAAH,EAAAkB,IAAA,EAACQ,KAAAA,CAAGvB,UAAU,0BACXZ,EAAE,WAAW,IAAEE,EAAOkC,SAAS,OAKpC,GAAA3B,EAAAC,GAAA,EAAC2B,EAAAA,CAAmBA,CAAAA,CAEhBC,aAAcC,CAAAA,EAAAA,EAAAA,EAAAA,EAAShB,EAAQiB,IAAI,CAACC,KAAK,EACzCC,WAAYnB,EAAQiB,IAAI,CAACG,KAAK,CAC9BC,WAAY,CAACrB,EAAQiB,IAAI,EAAEK,MAAOtB,GAASiB,MAAMM,MAAM,CAEvDC,eAAgBR,CAAAA,EAAAA,EAAAA,EAAAA,EAAShB,GAASyB,IAAIP,OACtCQ,aAAc1B,GAASyB,IAAIL,MAC3BO,aAAc,CAAC3B,GAASyB,IAAIH,MAAOtB,GAASyB,IAAIF,MAAM,CAExDlC,UAAU,0BAGZ,GAAAH,EAAAC,GAAA,EAACyC,EAAAA,CAASA,CAAAA,CAACvC,UAAU,4BAErB,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,0BAEb,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,4DACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,UAEL,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZW,GAAS6B,UACNC,CAAAA,EAAAA,EAAAA,EAAAA,EAAO9B,EAAQ6B,SAAS,CAAE,wBAC1B,QAKR,GAAA3C,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,4DACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,YAEL,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZa,EAAS6B,QAAQ,CAAC/B,EAAQgC,MAAM,CAAEhC,EAAQiC,QAAQ,CAAC/B,QAAQ,OAKhE,GAAAhB,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,4DACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,oBAEL,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZa,EAAS6B,QAAQ,CAAC/B,EAAQkC,GAAG,CAAElC,EAAQiC,QAAQ,CAAC/B,QAAQ,OAK7D,GAAAhB,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,4DACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,eAEL,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,yDACZa,EAAS6B,QAAQ,CAAC/B,EAAQmC,KAAK,CAAEnC,EAAQiC,QAAQ,CAAC/B,QAAQ,UAKjE,GAAAhB,EAAAC,GAAA,EAACyC,EAAAA,CAASA,CAAAA,CAACvC,UAAU,4BAErB,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,yBAEb,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,sDACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,oBAEL,GAAAS,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,gFACZW,EAAQf,KAAK,CACd,GAAAC,EAAAC,GAAA,EAACiD,EAAAA,CAAMA,CAAAA,CACLC,KAAK,SACLC,QAAS,IAAMC,CAAAA,EAAAA,EAAAA,EAAAA,EAAYvC,EAAQf,KAAK,EACxCuB,QAAQ,UACRC,KAAK,KACLpB,UAAU,6CAEV,GAAAH,EAAAC,GAAA,EAACqD,EAAAA,CAAYA,CAAAA,CAAC/B,KAAK,iBAM3B,GAAAvB,EAAAC,GAAA,EAACyC,EAAAA,CAASA,CAAAA,CAACvC,UAAU,4BAEpBW,YAAAA,EAAQJ,MAAM,CACb,GAAAV,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,mDACb,GAAAH,EAAAkB,IAAA,EAACgC,EAAAA,CAAMA,CAAAA,CACLC,KAAK,SACLC,QAAS,IAAM/C,EAA2BS,GAASjC,IACnDsB,UAAU,6HAEV,GAAAH,EAAAC,GAAA,EAACoB,EAAAA,CAAUA,CAAAA,CAAAA,GACV9B,EAAE,cAGL,GAAAS,EAAAkB,IAAA,EAACgC,EAAAA,CAAMA,CAAAA,CACLC,KAAK,SACLC,QAAS,IAAMvC,EAA2BC,GAASjC,IACnDsB,UAAU,+GAEV,GAAAH,EAAAC,GAAA,EAACuB,EAAAA,CAAWA,CAAAA,CAAAA,GACXjC,EAAE,gBAGL,UAKR,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACb,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,kEACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,4CACb,GAAAH,EAAAC,GAAA,EAACyB,KAAAA,UAAInC,EAAE,mBAGT,GAAAS,EAAAC,GAAA,EAACyC,EAAAA,CAASA,CAAAA,CAACvC,UAAU,4BAErB,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,0BAEb,GAAAH,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,sDACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,iBAEL,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZW,GAASiC,UAAUQ,aAAe,UAKvC,GAAAvD,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,sDACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,YAEL,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZW,GAASiC,UAAUS,OAAS,UAKjC,GAAAxD,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,sDACb,GAAAH,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZZ,EAAE,YAEL,GAAAS,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,uDACZW,GAASiC,UAAU/B,UAAY,6BAjL5C,GAAAhB,EAAAkB,IAAA,EAAChB,MAAAA,CAAIC,UAAU,yDACb,GAAAH,EAAAC,GAAA,EAACwD,EAAAA,CAAKA,CAAAA,CAAAA,GACLlE,EAAE,mBAwLX,wFChQe,SAASmE,IACtB,MACE,GAAAC,EAAA1D,GAAA,EAACC,MAAAA,CAAIC,UAAU,kDACb,GAAAwD,EAAA1D,GAAA,EAACG,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,gdCNe,SAASwD,IACtB,MACE,GAAAD,EAAA1D,GAAA,EAACC,MAAAA,CAAIC,UAAU,kDACb,GAAAwD,EAAA1D,GAAA,EAACG,EAAAA,CAAMA,CAAAA,CAAAA,IAGb", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/(protected)/@agent/deposit-request/[trxId]/page.tsx?6cf6", "webpack://_N_E/|ssr?d66f", "webpack://_N_E/?2c1c", "webpack://_N_E/./data/deposit/acceptDepositRequest.ts", "webpack://_N_E/./data/deposit/rejectDepositRequest.ts", "webpack://_N_E/./app/(protected)/@agent/deposit-request/[trxId]/page.tsx", "webpack://_N_E/./app/(protected)/@agent/deposit-request/[trxId]/loading.tsx", "webpack://_N_E/./app/(protected)/@agent/deposit-request/loading.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "\"TURBOPACK { transition: next-ssr }\";\nimport { AppPageRouteModule } from \"next/dist/server/future/route-modules/app-page/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(protected)',\n        {\n        agent: [\n        'children',\n        {\n        children: [\n        'deposit-request',\n        {\n        children: [\n        '[trxId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\[trxId]\\\\page.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\[trxId]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\[trxId]\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\[trxId]\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\loading.tsx\"],\n        \n      }\n      ],\nchildren: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nadmin: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\ncustomer: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nmerchant: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\[trxId]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const originalPathname = \"/(protected)/@agent/deposit-request/[trxId]/page\";\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(protected)/@agent/deposit-request/[trxId]/page\",\n        pathname: \"/deposit-request/[trxId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2F(protected)%2F%40agent%2Fdeposit-request%2F%5BtrxId%5D%2Fpage&page=%2F(protected)%2F%40agent%2Fdeposit-request%2F%5BtrxId%5D%2Fpage&pagePath=private-next-app-dir%2F(protected)%2F%40agent%2Fdeposit-request%2F%5BtrxId%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CIDEES%20SQQS%5CPAYSNAPo%5CMain%5Cfrontend%5Capp&appPaths=%2F(protected)%2F%40agent%2Fdeposit-request%2F%5BtrxId%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/(protected)/@agent/deposit-request/[trxId]/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"webpack\":null,\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"analyticsId\":\"\",\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":200,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"inline\",\"remotePatterns\":[{\"protocol\":\"http\",\"hostname\":\"localhost\"},{\"protocol\":\"https\",\"hostname\":\"**\"}],\"unoptimized\":false},\"devIndicators\":{\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"optimizeFonts\":true,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"httpAgentOptions\":{\"keepAlive\":true},\"outputFileTracing\":true,\"staticPageGenerationTimeout\":60,\"swcMinify\":true,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"experimental\":{\"multiZoneDraftMode\":false,\"prerenderEarlyExit\":false,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"outputFileTracingRoot\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\",\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"adjustFontFallbacks\":false,\"adjustFontFallbacksWithSizeAdjust\":false,\"typedRoutes\":false,\"instrumentationHook\":false,\"bundlePagesExternals\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"missingSuspenseWithCSRBailout\":true,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":30,\"static\":300},\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"configFile\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/(protected)/@agent/deposit-request/[trxId]/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest,\n            pageName: \"/(protected)/@agent/deposit-request/[trxId]/page\"\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/(protected)/@agent/deposit-request/[trxId]/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@agent\\\\deposit-request\\\\[trxId]\\\\page.tsx\");\n", "import axios from \"@/lib/axios\";\r\nimport { ErrorResponseGenerator, ResponseGenerator } from \"@/data/response\";\r\n\r\nexport async function acceptDepositRequest(id: string | number) {\r\n  try {\r\n    const res = await axios.put(`/deposit-requests/accept/${id}`, { id });\r\n    return ResponseGenerator(res);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n}\r\n", "import axios from \"@/lib/axios\";\r\nimport { ErrorResponseGenerator, ResponseGenerator } from \"@/data/response\";\r\n\r\nexport async function rejectDepositRequest(id: string | number) {\r\n  try {\r\n    const res = await axios.put(`/deposit-requests/decline/${id}`, { id });\r\n    return ResponseGenerator(res);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n}\r\n", "\"use client\";\r\n\r\nimport { Case } from \"@/components/common/Case\";\r\nimport { Loader } from \"@/components/common/Loader\";\r\nimport { TransferProfileStep } from \"@/components/common/TransferProfileStep\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Separator from \"@/components/ui/separator\";\r\nimport { acceptDepositRequest } from \"@/data/deposit/acceptDepositRequest\";\r\nimport { rejectDepositRequest } from \"@/data/deposit/rejectDepositRequest\";\r\nimport { useSWR } from \"@/hooks/useSWR\";\r\nimport { copyContent, Currency, imageURL } from \"@/lib/utils\";\r\nimport { TransactionData } from \"@/types/transaction-data\";\r\nimport { format } from \"date-fns\";\r\nimport {\r\n  CloseCircle,\r\n  DocumentCopy,\r\n  InfoCircle,\r\n  Slash,\r\n  TickCircle,\r\n} from \"iconsax-react\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport default function WithdrawDetails() {\r\n  const { t } = useTranslation();\r\n  const params = useParams();\r\n  const { data, isLoading, mutate } = useSWR(\r\n    `/transactions/trx/${params.trxId}`,\r\n  );\r\n\r\n  // return loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-10\">\r\n        <Loader />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // handle deposit request\r\n  const handleAcceptDepositRequest = (id: number | string) => {\r\n    toast.promise(acceptDepositRequest(id), {\r\n      loading: t(\"Loading...\"),\r\n      success: (res) => {\r\n        if (!res.status) throw new Error(res.message);\r\n        mutate(data);\r\n        return res.message;\r\n      },\r\n      error: (err) => err.message,\r\n    });\r\n  };\r\n\r\n  const handleRejectDepositRequest = (id: number | string) => {\r\n    toast.promise(rejectDepositRequest(id), {\r\n      loading: t(\"Loading...\"),\r\n      success: (res) => {\r\n        if (!res.status) throw new Error(res.message);\r\n        mutate(data);\r\n        return res.message;\r\n      },\r\n      error: (err) => err.message,\r\n    });\r\n  };\r\n\r\n  const deposit = data?.data ? new TransactionData(data?.data) : null;\r\n  const currency = new Currency();\r\n\r\n  if (!deposit) {\r\n    return (\r\n      <div className=\"flex items-center justify-center gap-4 py-10\">\r\n        <Slash />\r\n        {t(\"No data found\")}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"mb-10 p-2 sm:mb-0 sm:p-4\">\r\n      <div className=\"grid grid-cols-12 gap-4\">\r\n        {/* Left section */}\r\n        <div className=\"col-span-12 lg:col-span-7\">\r\n          <div className=\"flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14\">\r\n            <div className=\"inline-flex items-center justify-center gap-2.5\">\r\n              <Case condition={deposit.status === \"completed\"}>\r\n                <TickCircle variant=\"Bulk\" size={32} className=\"text-success\" />\r\n              </Case>\r\n              <Case condition={deposit.status === \"failed\"}>\r\n                <CloseCircle\r\n                  variant=\"Bulk\"\r\n                  size={32}\r\n                  className=\"text-destructive\"\r\n                />\r\n              </Case>\r\n              <Case condition={deposit.status === \"pending\"}>\r\n                <InfoCircle variant=\"Bulk\" size={32} className=\"text-primary\" />\r\n              </Case>\r\n              <h2 className=\"font-semibold\">\r\n                {t(\"Deposit\")}#{params.depositId}\r\n              </h2>\r\n            </div>\r\n\r\n            {/* step */}\r\n            <TransferProfileStep\r\n              {...{\r\n                senderAvatar: imageURL(deposit.from.image),\r\n                senderName: deposit.from.label,\r\n                senderInfo: [deposit.from?.email, deposit?.from?.phone],\r\n\r\n                receiverAvatar: imageURL(deposit?.to?.image),\r\n                receiverName: deposit?.to?.label,\r\n                receiverInfo: [deposit?.to?.email, deposit?.to?.phone],\r\n              }}\r\n              className=\"px-3 sm:gap-4 sm:px-8\"\r\n            />\r\n\r\n            <Separator className=\"mb-1 mt-[5px] bg-border\" />\r\n\r\n            <div className=\"flex flex-col\">\r\n              {/* row */}\r\n              <div className=\"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"Date\")}\r\n                </div>\r\n                <div className=\"col-span-6 text-sm font-medium sm:text-base\">\r\n                  {deposit?.createdAt\r\n                    ? format(deposit.createdAt, \"dd MMM yyyy; hh:mm a\")\r\n                    : \"\"}\r\n                </div>\r\n              </div>\r\n\r\n              {/* row */}\r\n              <div className=\"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"Amount\")}\r\n                </div>\r\n                <div className=\"col-span-6 text-sm font-medium sm:text-base\">\r\n                  {currency.formatVC(deposit.amount, deposit.metaData.currency)}\r\n                </div>\r\n              </div>\r\n\r\n              {/* row */}\r\n              <div className=\"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"Service charge\")}\r\n                </div>\r\n                <div className=\"col-span-6 text-sm font-medium sm:text-base\">\r\n                  {currency.formatVC(deposit.fee, deposit.metaData.currency)}\r\n                </div>\r\n              </div>\r\n\r\n              {/* row */}\r\n              <div className=\"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"User gets\")}\r\n                </div>\r\n                <div className=\"col-span-6 text-sm font-semibold sm:text-base\">\r\n                  {currency.formatVC(deposit.total, deposit.metaData.currency)}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Separator className=\"mb-1 mt-[5px] bg-border\" />\r\n\r\n            <div className=\"flex flex-col\">\r\n              {/* row */}\r\n              <div className=\"grid grid-cols-12 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"Transaction ID\")}\r\n                </div>\r\n                <div className=\"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base\">\r\n                  {deposit.trxId}\r\n                  <Button\r\n                    type=\"button\"\r\n                    onClick={() => copyContent(deposit.trxId)}\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className=\"bg-background hover:bg-background\"\r\n                  >\r\n                    <DocumentCopy size=\"20\" />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Separator className=\"mb-1 mt-[5px] bg-border\" />\r\n\r\n            {deposit.status === \"pending\" ? (\r\n              <div className=\"flex items-center justify-center gap-4\">\r\n                <Button\r\n                  type=\"button\"\r\n                  onClick={() => handleAcceptDepositRequest(deposit?.id)}\r\n                  className=\"gap-1 rounded-lg bg-spacial-green px-4 py-2 font-medium text-background hover:bg-[#219621] hover:text-background\"\r\n                >\r\n                  <TickCircle />\r\n                  {t(\"Approve\")}\r\n                </Button>\r\n\r\n                <Button\r\n                  type=\"button\"\r\n                  onClick={() => handleRejectDepositRequest(deposit?.id)}\r\n                  className=\"gap-1 rounded-lg bg-[#D13438] px-4 py-2 font-medium text-white hover:bg-[#a5272b] hover:text-white\"\r\n                >\r\n                  <CloseCircle />\r\n                  {t(\"Reject\")}\r\n                </Button>\r\n              </div>\r\n            ) : null}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Section */}\r\n        <div className=\"col-span-12 lg:col-span-5\">\r\n          <div className=\"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <h2>{t(\"Method info\")}</h2>\r\n            </div>\r\n\r\n            <Separator className=\"mb-1 mt-[5px] bg-border\" />\r\n\r\n            <div className=\"flex flex-col\">\r\n              {/* Row */}\r\n              <div className=\"grid grid-cols-12 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"Method used\")}\r\n                </div>\r\n                <div className=\"col-span-6 text-sm font-medium sm:text-base\">\r\n                  {deposit?.metaData?.agentMethod ?? \"--\"}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Row */}\r\n              <div className=\"grid grid-cols-12 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"Number\")}\r\n                </div>\r\n                <div className=\"col-span-6 text-sm font-medium sm:text-base\">\r\n                  {deposit?.metaData?.value ?? \"--\"}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Row */}\r\n              <div className=\"grid grid-cols-12 px-6 py-3 odd:bg-accent\">\r\n                <div className=\"col-span-6 text-sm font-normal sm:text-base\">\r\n                  {t(\"Wallet\")}\r\n                </div>\r\n                <div className=\"col-span-6 text-sm font-medium sm:text-base\">\r\n                  {deposit?.metaData?.currency ?? \"undefine\"}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function PageLoading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "_self___RSC_MANIFEST", "tree", "children", "agent", "page", "Promise", "resolve", "then", "__webpack_require__", "bind", "defaultPage", "admin", "customer", "merchant", "pages", "originalPathname", "__next_app__", "loadChunk", "routeModule", "module_compiled", "AppPageRouteModule", "definition", "kind", "route_kind", "x", "APP_PAGE", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "self", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "encryption_utils", "Mo", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "action_utils", "w", "pageName", "page_stringifiedConfig_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_pagesType_app_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGKHByb3RlY3RlZCklMkYlNDBhZ2VudCUyRmRlcG9zaXQtcmVxdWVzdCUyRiU1QnRyeElkJTVEJTJGcGFnZSZwYWdlPSUyRihwcm90ZWN0ZWQpJTJGJTQwYWdlbnQlMkZkZXBvc2l0LXJlcXVlc3QlMkYlNUJ0cnhJZCU1RCUyRnBhZ2UmcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkYocHJvdGVjdGVkKSUyRiU0MGFnZW50JTJGZGVwb3NpdC1yZXF1ZXN0JTJGJTVCdHJ4SWQlNUQlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rlc2t0b3AlNUNJREVFUyUyMFNRUVMlNUNQQVlTTkFQbyU1Q01haW4lNUNmcm9udGVuZCU1Q2FwcCZhcHBQYXRocz0lMkYocHJvdGVjdGVkKSUyRiU0MGFnZW50JTJGZGVwb3NpdC1yZXF1ZXN0JTJGJTVCdHJ4SWQlNUQlMkZwYWdlJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEIQ_sriEnabled_false_middlewareConfig_e30_render", "render", "d", "pagesType", "page_types", "s", "APP", "dev", "appMod", "pageMod", "page_next_edge_ssr_entry_namespaceObject", "errorMod", "error500Mod", "Document", "renderToHTML", "app_render", "f", "serverActions", "subresourceIntegrityManifest", "config", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "adapter", "C", "IncrementalCache", "incremental_cache", "k", "handler", "acceptDepositRequest", "id", "res", "axios", "put", "ResponseGenerator", "error", "ErrorResponseGenerator", "rejectDepositRequest", "runtime", "WithdrawDetails", "t", "useTranslation", "params", "useParams", "data", "isLoading", "mutate", "useSWR", "trxId", "jsx_runtime", "jsx", "div", "className", "Loader", "handleAcceptDepositRequest", "toast", "promise", "loading", "success", "status", "message", "err", "handleRejectDepositRequest", "deposit", "TransactionData", "currency", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "Case", "condition", "TickCircle", "variant", "size", "CloseCircle", "InfoCircle", "h2", "depositId", "TransferProfileStep", "senderAvatar", "imageURL", "from", "image", "sender<PERSON>ame", "label", "senderInfo", "email", "phone", "receiverAvatar", "to", "<PERSON><PERSON><PERSON>", "receiverInfo", "Separator", "createdAt", "format", "formatVC", "amount", "metaData", "fee", "total", "<PERSON><PERSON>", "type", "onClick", "copyContent", "DocumentCopy", "agentMethod", "value", "Slash", "Loading", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "PageLoading"], "sourceRoot": ""}