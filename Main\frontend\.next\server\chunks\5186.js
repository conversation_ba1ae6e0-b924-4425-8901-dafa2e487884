exports.id=5186,exports.ids=[5186],exports.modules={96998:(e,s,t)=>{Promise.resolve().then(t.bind(t,99936))},49663:(e,s,t)=>{Promise.resolve().then(t.bind(t,11421))},1705:(e,s,t)=>{Promise.resolve().then(t.bind(t,36656))},47782:(e,s,t)=>{Promise.resolve().then(t.bind(t,46722))},99936:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(10326),r=t(56140),l=t(63761),i=t(99440),n=t(28758),d=t(90772),c=t(8281),o=t(33261),x=t(94487),m=t(28707),u=t(75584),h=t(77863),f=t(48444),p=t(54033),g=t(71305),j=t(9489),b=t(75138),N=t(72871),v=t(90434),y=t(35047),w=t(17577),S=t(70012),Z=t(85999);function P(){let{t:e}=(0,S.$G)(),s=(0,y.useSearchParams)(),[t,i]=w.useState(!1),[x,N]=w.useState([]),[P,k]=w.useState(s.get("search")??""),$=(0,y.useRouter)(),I=(0,y.usePathname)(),{data:R,meta:z,isLoading:D,refresh:L}=(0,u.Z)(`/admin/cards?${s.toString()}`),M=({cardId:s,status:t})=>{Z.toast.promise((0,m.a)({cardId:s,dataList:{status:t},isAdmin:!0}),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return L(),e.message},error:e=>e.message})};return a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[a.jsx("div",{className:"flex h-12 items-center",children:a.jsx("div",{className:"flex items-center gap-4",children:a.jsx(l.R,{value:P,onChange:e=>{e.preventDefault();let s=(0,h.w4)(e.target.value);k(e.target.value),$.replace(`${I}?${s.toString()}`)},iconPlacement:"end",placeholder:e("Search...")})})}),a.jsx(c.Z,{className:"my-4"}),a.jsx(r.Z,{data:R?R.map(e=>new f.Z(e)):[],sorting:x,setSorting:N,isLoading:D,pagination:{total:z?.total,page:z?.currentPage,limit:z?.perPage},structure:[{id:"id",header:e("Card ID"),cell:({row:e})=>(0,a.jsxs)("p",{className:"font-normal",children:["#",e.original?.id]})},{id:"brand",header:e("Type"),cell:({row:e})=>a.jsx("p",{className:"font-normal",children:e.original?.brand})},{id:"number",header:e("Number"),meta:{className:"w-[22%]"},cell:({row:e})=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("p",{className:"font-normal",children:t?e.original?.number.replace(/(\d{4})(?=\d)/g,"$1 "):`**** **** **** ${e.original?.lastFour}`}),a.jsx(d.z,{"aria-label":"VisibilityToggler",variant:"secondary",size:"icon",type:"button",className:"rounded-md",onClick:()=>i(e=>!e),children:t?a.jsx(j.Z,{size:16}):a.jsx(b.Z,{size:16})})]})},{id:"cvc",header:e("CVV"),cell:({row:e})=>a.jsx("p",{className:"font-normal",children:e.original?.cvc})},{id:"expYear",header:e("Exp. Date"),cell:({row:e})=>(0,a.jsxs)("p",{className:"font-normal",children:[e.original?.expMonth.toString().padStart(2,"0"),"/",e.original?.expYear.toString().length===4?e.original?.expYear.toString().slice(2):e.original?.expYear.toString()]})},{id:"status",header:e("Status"),cell:({row:e})=>a.jsx(o.Z,{defaultChecked:e.original?.status==="active",onCheckedChange:s=>{M({cardId:e.original?.id,status:s?"active":"inactive"})}})},{id:"user",header:e("User"),cell:({row:e})=>(0,a.jsxs)(v.default,{href:`/customers/${e.original.user.customer?.id}?name=${e.original.user?.customer?.name}&active=${e?.original?.user?.status}`,className:"flex min-w-[80px] items-center gap-2 font-normal text-secondary-text hover:text-foreground",children:[(0,a.jsxs)(n.qE,{children:[a.jsx(n.F$,{src:e.original.user.customer.profileImage}),a.jsx(n.Q5,{children:(0,p.v)(e.original.user.customer.name)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-normal",children:e.original.user.customer.name}),e.original?.user.email?a.jsx("p",{className:"text-xs font-normal",children:e.original?.user.email}):null]})]})},{id:"createdAt",header:e("Issue date"),cell:({row:e})=>(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-normal",children:(0,g.WU)(e.original?.createdAt,"dd MMM yyyy;")}),a.jsx("p",{className:"font-normal",children:(0,g.WU)(e.original?.createdAt,"hh:mm a")})]})},{id:"actions",header:e("Actions"),cell:({row:e})=>a.jsx("div",{className:"flex items-center gap-2",children:a.jsx(C,{cardId:e.original?.id,onMutate:L})})}]})]})})}function C({cardId:e,onMutate:s}){let{t}=(0,S.$G)(),r=async()=>{(await (0,x.f)({cardId:e,isAdmin:!0})).status&&(s(),Z.toast.success(t("Investment deleted successfully")))};return(0,a.jsxs)(i.aR,{children:[a.jsx(i.vW,{asChild:!0,children:a.jsx(d.z,{size:"icon",variant:"outline",color:"danger",className:"h-8 w-8 rounded-md bg-background text-danger hover:bg-background",children:a.jsx(N.Z,{size:20})})}),(0,a.jsxs)(i._T,{children:[(0,a.jsxs)(i.fY,{children:[a.jsx(i.f$,{children:t("Close Card")}),a.jsx(i.yT,{children:t("Are you sure you want to close this card?")})]}),(0,a.jsxs)(i.xo,{className:"mt-2",children:[a.jsx(i.le,{children:t("No")}),a.jsx(i.OL,{type:"button",onClick:r,className:"action:bg-destructive/80 bg-destructive text-destructive-foreground hover:bg-destructive/90",children:t("Yes")})]})]})]})}},11421:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(10326),r=t(36656);function l(){return a.jsx(r.default,{})}},36656:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(10326),r=t(90772),l=t(62288),i=t(8281),n=t(33261),d=t(94487),c=t(28707),o=t(19395),x=t(4066),m=t(77863),u=t(31112),h=t(46680),f=t(29169),p=t(54639),g=t(90434),j=t(70012),b=t(85999);function N({card:e,balance:s,currency:t,onMutate:r}){let{t:l}=(0,j.$G)(),{auth:i}=(0,o.a)(),{cardBg:n}=(0,x.T)();return(0,a.jsxs)("div",{style:{backgroundImage:`url(${(0,m.qR)(n)})`},className:"mb-5 flex min-h-[280px] w-full max-w-[450px] flex-col justify-end rounded-3xl bg-cover p-5",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:["active"===e.status?a.jsx("div",{className:"h-3 w-3 rounded-full bg-success"}):a.jsx("div",{className:"h-3 w-3 rounded-full bg-danger"}),a.jsx("p",{className:"text-sm capitalize text-white",children:e.status})]}),a.jsx("p",{className:"mb-5 text-2xl font-semibold text-white",children:e?.number.replace(/(\d{4})(?=\d)/g,"$1 ")}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-5",children:[(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:l("Card holder name")}),a.jsx("p",{className:"text-base font-semibold",children:i?.customer?.name})]}),(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:l("Expiry date")}),(0,a.jsxs)("p",{className:"text-base font-semibold",children:[e.expMonth.toString().padStart(2,"0"),"/",4===e.expYear.toString().length?e.expYear.toString().slice(2):e.expYear.toString()]})]}),(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:l("CVV")}),a.jsx("p",{className:"text-base font-semibold",children:e.cvc})]})]}),a.jsx(v,{card:e,balance:s,currency:t,onMutate:r})]})]})}function v({card:e,balance:s,currency:t,onMutate:N=()=>{}}){let{t:v}=(0,j.$G)(),{auth:y}=(0,o.a)(),{cardBg:w}=(0,x.T)(),S=s=>{b.toast.promise((0,c.a)({cardId:e.id,dataList:{status:s}}),{loading:v("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return N(),e.message},error:e=>e.message})},Z=()=>{b.toast.promise((0,d.f)({cardId:e.id}),{loading:v("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return N(),e.message},error:e=>e.message})};return(0,a.jsxs)(l.Vq,{children:[a.jsx(l.hg,{asChild:!0,children:a.jsx(r.z,{variant:"secondary",size:"icon",type:"button",className:"rounded-md",children:a.jsx(u.Z,{size:20})})}),(0,a.jsxs)(l.cZ,{className:"sm:max-w-[525px]",children:[a.jsx(l.$N,{children:v("Card Details")}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{style:{backgroundImage:`url(${(0,m.qR)(w)})`},className:"mb-5 flex min-h-[280px] w-full max-w-[450px] flex-col justify-end gap-7 rounded-3xl bg-cover p-7",children:[a.jsx("p",{className:"text-[28px] font-semibold text-white",children:e.number.replace(/(\d{4})(?=\d)/g,"$1 ")}),(0,a.jsxs)("div",{className:"flex items-center gap-8",children:[(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:v("Card holder name")}),a.jsx("p",{className:"text-xl font-semibold",children:y?.customer?.name})]}),(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:v("Expiry date")}),(0,a.jsxs)("p",{className:"text-xl font-semibold",children:[e.expMonth.toString().padStart(2,"0"),"/",4===e.expYear.toString().length?e.expYear.toString().slice(2):e.expYear.toString()]})]}),(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("p",{className:"text-sm",children:v("CVV")}),a.jsx("p",{className:"text-xl font-semibold",children:e.cvc})]})]})]}),(0,a.jsxs)("div",{className:"mb-5 flex gap-8",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>(0,m.Fp)(e.number),className:"flex flex-col items-center justify-center gap-2",children:[a.jsx("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:a.jsx(h.Z,{size:"24",color:"#000"})}),a.jsx("span",{className:"text-xs font-semibold",children:v("Copy Number")})]}),(0,a.jsxs)(g.default,{href:"/deposit",className:"flex flex-col items-center justify-center gap-2",children:[a.jsx("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500",children:a.jsx(f.Z,{size:"24",color:"#000"})}),a.jsx("span",{className:"text-xs font-semibold",children:v("Deposit Money")})]}),(0,a.jsxs)("button",{type:"button",onClick:()=>Z(),className:"flex flex-col items-center justify-center gap-2",children:[a.jsx("div",{className:"flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary text-black transition duration-300 ease-in-out hover:bg-spacial-red-foreground hover:text-danger",children:a.jsx(p.Z,{size:"24"})}),a.jsx("span",{className:"text-xs font-semibold",children:v("Close Card")})]})]}),a.jsx(i.Z,{className:"mb-5 border-b bg-transparent"}),(0,a.jsxs)("div",{className:"w-full space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm",children:v("Status")}),a.jsx(n.Z,{defaultChecked:"active"===e.status,onCheckedChange:e=>{S(e?"active":"inactive")}})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:v("Balance")}),(0,a.jsxs)("span",{className:"text-sm font-semibold",children:[s," ",t]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:v("Card Type")}),a.jsx("span",{className:"text-sm font-semibold",children:e?.brand})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-sm",children:v("Expiry Date")}),(0,a.jsxs)("span",{className:"text-sm font-semibold",children:[e.expMonth.toString().padStart(2,"0"),"/",4===e.expYear.toString().length?e.expYear.toString().slice(2):e.expYear.toString()]})]})]})]})]})]})}var y=t(92392),w=t(90799),S=t(48444),Z=t(77132);function P(){let{data:e,isLoading:s,mutate:t}=(0,w.d)("/cards"),{t:r}=(0,j.$G)();if(s)return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(y.Loader,{})});let l=e?.data?.map(e=>new S.Z(e));return s||0!==l.length?a.jsx("div",{className:"w-full bg-background p-4",children:a.jsx("div",{className:"flex flex-wrap gap-4",children:l.map(e=>a.jsx(N,{balance:e.wallet.balance,currency:e.brand,card:e,onMutate:t},e.id))})}):a.jsx("div",{className:"h-full w-full bg-background p-4",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(Z.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),r("No cards found!")]})})}},46722:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(10326),r=t(36656);function l(){return a.jsx(r.default,{})}},56140:(e,s,t)=>{"use strict";t.d(s,{Z:()=>y});var a=t(10326),r=t(77863),l=t(86508),i=t(11798),n=t(77132),d=t(6216),c=t(75817),o=t(40420),x=t(35047),m=t(93327),u=t(17577),h=t(70012),f=t(90772);let p=u.forwardRef(({className:e,...s},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:(0,r.ZP)("w-full caption-bottom text-sm",e),...s})}));p.displayName="Table";let g=u.forwardRef(({className:e,...s},t)=>a.jsx("thead",{ref:t,className:(0,r.ZP)("",e),...s}));g.displayName="TableHeader";let j=u.forwardRef(({className:e,...s},t)=>a.jsx("tbody",{ref:t,className:(0,r.ZP)("[&_tr:last-child]:border-0",e),...s}));j.displayName="TableBody",u.forwardRef(({className:e,...s},t)=>a.jsx("tfoot",{ref:t,className:(0,r.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let b=u.forwardRef(({className:e,...s},t)=>a.jsx("tr",{ref:t,className:(0,r.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));b.displayName="TableRow";let N=u.forwardRef(({className:e,...s},t)=>a.jsx("th",{ref:t,className:(0,r.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));N.displayName="TableHead";let v=u.forwardRef(({className:e,...s},t)=>a.jsx("td",{ref:t,className:(0,r.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));function y({data:e,isLoading:s=!1,structure:t,sorting:y,setSorting:w,padding:S=!1,className:Z,onRefresh:P,pagination:C}){let k=(0,u.useMemo)(()=>t,[t]),$=(0,x.useRouter)(),I=(0,x.usePathname)(),R=(0,x.useSearchParams)(),{t:z}=(0,h.$G)(),D=(0,l.b7)({data:e||[],columns:k,state:{sorting:y,onRefresh:P},onSortingChange:w,getCoreRowModel:(0,i.sC)(),getSortedRowModel:(0,i.tj)(),debugTable:!1});return s?a.jsx("div",{className:"rounded-md bg-background p-10",children:a.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:z("Loading...")})}):e?.length?(0,a.jsxs)("div",{className:(0,r.ZP)(`${S?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,Z),children:[(0,a.jsxs)(p,{children:[a.jsx(g,{children:D.getHeaderGroups().map(e=>a.jsx(b,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>a.jsx(N,{className:(0,r.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,a.jsxs)(f.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[z((0,l.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:a.jsx(d.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:a.jsx(d.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??a.jsx(d.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),a.jsx(j,{children:D.getRowModel().rows.map(e=>a.jsx(b,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>a.jsx(v,{className:"py-3 text-sm font-semibold",children:(0,l.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),C&&C.total>10&&a.jsx("div",{className:"pb-2 pt-6",children:a.jsx(m.Z,{showTotal:(e,s)=>z("Showing {{start}}-{{end}} of {{total}}",{start:s[0],end:s[1],total:e}),align:"start",current:C?.page,total:C?.total,pageSize:C?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let s=new URLSearchParams(R);s.set("page",e.toString()),$.push(`${I}?${s.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>a.jsx("a",{...e,children:a.jsx(c.Z,{size:"18"})}),nextIcon:e=>a.jsx("a",{...e,children:a.jsx(o.Z,{size:"18"})})})})]}):a.jsx("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(n.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),z("No data found!")]})})}v.displayName="TableCell",u.forwardRef(({className:e,...s},t)=>a.jsx("caption",{ref:t,className:(0,r.ZP)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},63761:(e,s,t)=>{"use strict";t.d(s,{R:()=>n});var a=t(10326);t(17577);var r=t(54432),l=t(77863),i=t(32894);function n({iconPlacement:e="start",className:s,containerClass:t,...n}){return(0,a.jsxs)("div",{className:(0,l.ZP)("relative flex items-center",t),children:[a.jsx(i.Z,{size:"20",className:(0,l.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),a.jsx(r.I,{type:"text",className:(0,l.ZP)("h-10","end"===e?"pr-10":"pl-10",s),...n})]})}},54432:(e,s,t)=>{"use strict";t.d(s,{I:()=>i});var a=t(10326),r=t(17577),l=t(77863);let i=r.forwardRef(({className:e,type:s,...t},r)=>a.jsx("input",{type:s,className:(0,l.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:r,...t}));i.displayName="Input"},33261:(e,s,t)=>{"use strict";t.d(s,{Z:()=>d});var a=t(10326),r=t(41959),l=t(17577),i=t(77863);let n=l.forwardRef(({className:e,...s},t)=>a.jsx(r.fC,{className:(0,i.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",e),...s,ref:t,children:a.jsx(r.bU,{className:(0,i.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})}));n.displayName=r.fC.displayName;let d=n},94487:(e,s,t)=>{"use strict";t.d(s,{f:()=>l});var a=t(49547),r=t(10734);async function l({cardId:e,isAdmin:s=!1}){try{let t=await a.Z.delete(`${s?"/admin/cards/":"/cards/"}${e}`);return(0,r.B)(t)}catch(e){return(0,r.D)(e)}}},28707:(e,s,t)=>{"use strict";t.d(s,{a:()=>l});var a=t(49547),r=t(10734);async function l({cardId:e,dataList:s,isAdmin:t=!1}){try{let l=await a.Z.put(`${t?"/admin/cards/change-status/":"/cards/change-status/"}${e}`,s);return(0,r.B)(l)}catch(e){return(0,r.D)(e)}}},75584:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});var a=t(90799),r=t(35047);function l(e,s){let t=(0,r.usePathname)(),l=(0,r.useSearchParams)(),i=(0,r.useRouter)(),[n,d]=e.split("?"),c=new URLSearchParams(d);c.has("page")||c.set("page","1"),c.has("limit")||c.set("limit","10");let o=`${n}?${c.toString()}`,{data:x,error:m,isLoading:u,mutate:h,...f}=(0,a.d)(o,s);return{refresh:()=>h(x),data:x?.data?.data??[],meta:x?.data?.meta,filter:(e,s,a)=>{let r=new URLSearchParams(l.toString());s?r.set(e,s.toString()):r.delete(e),i.replace(`${t}?${r.toString()}`),a?.()},isLoading:u,error:m,...f}}},48444:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});class a{constructor(e){this.id=e?.id,this.cardId=e?.cardId,this.userId=e?.userId,this.walletId=e?.walletId,this.number=e?.number,this.cvc=e?.cvc,this.lastFour=e?.lastFour,this.brand=e?.brand,this.expMonth=e?.expMonth,this.expYear=e?.expYear,this.status=e?.status,this.type=e?.type,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.wallet=e?.wallet,this.user=e?.user}}},96026:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(19510),r=t(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},53974:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\cards\page.tsx#default`)},90716:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\cards\page.tsx#default`)},84514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(40099),l=t(76609);function i({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(l.Z,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},18406:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(19510),r=t(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},5732:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(19510),r=t(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},22445:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\cards\page.tsx#default`)},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(19510),r=t(40099),l=t(76609);function i({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(l.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(19510),r=t(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},82190:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@merchant\cards\page.tsx#default`)}};