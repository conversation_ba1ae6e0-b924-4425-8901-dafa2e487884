(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[75017],{32188:function(a,s,l){Promise.resolve().then(l.bind(l,13834))},13834:function(a,s,l){"use strict";l.r(s),l.d(s,{default:function(){return b}});var e=l(57437),t=l(40506),n=l(85539),c=l(66465),o=l(27186),i=l(85017),d=l(18384),r=l(6512),u=l(31117),x=l(75730),p=l(94508),m=l(8877),f=l(89340),g=l(61756),v=l(3512),h=l(99376),j=l(2265),w=l(43949);function b(){var a,s,l,b;let N=(0,h.useSearchParams)(),C=(0,h.useRouter)(),Z=(0,h.useParams)(),_=(0,h.usePathname)(),{t:k}=(0,w.$G)(),[y,L]=j.useState(""),{data:P,meta:S,isLoading:T,filter:E}=(0,x.Z)("/admin/transactions/".concat(Z.customerId,"?").concat(N.toString())),{data:B,isLoading:F}=(0,u.d)("/admin/transactions/counts/".concat(Z.customerId));return(0,e.jsxs)("div",{className:"h-full p-4",children:[(0,e.jsxs)("div",{className:"mb-4 grid grid-cols-12 gap-4",children:[(0,e.jsx)(c.x,{value:null==B?void 0:null===(a=B.data)||void 0===a?void 0:a.deposit,title:k("Total Deposit"),icon:a=>(0,e.jsx)(m.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",statusClass:"text-spacial-green",iconClass:"bg-spacial-green-foreground",isLoading:F}),(0,e.jsx)(c.x,{value:null==B?void 0:null===(s=B.data)||void 0===s?void 0:s.withdraw,title:k("Total Withdraw"),icon:a=>(0,e.jsx)(f.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-red-foreground text-spacial-red",statusClass:"text-spacial-red",isLoading:F}),(0,e.jsx)(c.x,{value:null==B?void 0:null===(l=B.data)||void 0===l?void 0:l.transfer,title:k("Total Transfers"),icon:a=>(0,e.jsx)(g.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",iconClass:"bg-spacial-blue-foreground text-spacial-blue",statusClass:"text-spacial-blue",isLoading:F}),(0,e.jsx)(c.x,{value:null==B?void 0:null===(b=B.data)||void 0===b?void 0:b.exchange,title:k("Total Exchange"),icon:a=>(0,e.jsx)(v.Z,{...a}),status:"",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3",isLoading:F})]}),(0,e.jsxs)("div",{className:"h-fit w-full overflow-auto rounded-xl bg-background p-6 shadow-default",children:[(0,e.jsxs)("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:[(0,e.jsx)("div",{className:"flex h-full w-full flex-col items-center gap-4 rounded-md bg-accent p-1 sm:w-fit sm:flex-row",children:(0,e.jsx)(d.Z,{filter:E})}),(0,e.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,e.jsx)(n.R,{value:y,onChange:a=>{a.preventDefault();let s=(0,p.w4)(a.target.value);L(a.target.value),C.replace("".concat(_,"?").concat(s.toString()))},iconPlacement:"end",placeholder:k("Search..."),containerClass:"w-full sm:w-auto"}),(0,e.jsx)(i.k,{canFilterByAgent:!0,canFilterByMethod:!0,canFilterByGateway:!0}),(0,e.jsx)(o._,{url:"/admin/transactions/export/".concat(Z.userId)})]})]}),(0,e.jsx)(r.Z,{className:"my-4"}),(0,e.jsx)(t.Z,{data:P,meta:S,isLoading:T})]})]})}}},function(a){a.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,64009,93396,227,56993,85017,18628,92971,95030,1744],function(){return a(a.s=32188)}),_N_E=a.O()}]);