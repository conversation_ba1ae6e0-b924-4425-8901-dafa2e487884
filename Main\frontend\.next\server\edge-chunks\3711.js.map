{"version": 3, "file": "edge-chunks/3711.js", "mappings": "gFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAEAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,uJC2BO,SAASE,EAAY,CAAEC,YAAAA,CAAW,CAAU,EACjD,GAAM,CAACC,EAAYC,EAAgB,CAAGC,EAAAA,QAAc,CAAC,eAC/C,CAACC,EAAYC,EAAc,CAAGF,EAAAA,QAAc,CAAC,IAE7C,CAAEG,cAAeC,CAAa,CAAEC,OAAAA,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IAC3CC,EAAgBC,CAAAA,EAAAA,EAAAA,EAAAA,IAWtB,OATAR,EAAAA,SAAe,CAAC,KACdD,EAAgBQ,EAElB,EAAG,EAAE,EAELP,EAAAA,SAAe,CAAC,KACdE,EAAcL,EAAYY,OAAO,GAAKF,EACxC,EAAG,CAACA,EAAeV,EAAYY,OAAO,CAAC,EAGrC,GAAAC,EAAAC,IAAA,EAACC,MAAAA,CACCC,gBAAeZ,EACfa,UAAU,8HAEV,GAAAJ,EAAAC,IAAA,EAACI,EAAAA,CAAIA,CAAAA,CACHC,KAAMnB,EAAYoB,IAAI,CACtBC,QAAS,KACPnB,EAAgBF,EAAYY,OAAO,EAC9BZ,EAAYsB,QAAQ,EAAEC,QACrBf,YAAAA,GACFD,EAAc,GAGpB,EACAiB,cAAad,IAAkBV,EAAYY,OAAO,CAClDK,UAAU,sIAEV,GAAAJ,EAAAY,GAAA,EAACC,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAAC,CAAC3B,EAAY4B,IAAI,UACjC,GAAAf,EAAAY,GAAA,EAACV,MAAAA,CACCS,cAAad,IAAkBV,EAAYY,OAAO,CAClDK,UAAU,8IAETjB,GAAa4B,SAIlB,GAAAf,EAAAY,GAAA,EAACI,OAAAA,CAAKZ,UAAU,kBAAUjB,EAAY8B,IAAI,GAE1C,GAAAjB,EAAAY,GAAA,EAACC,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAAC,CAAC3B,EAAYsB,QAAQ,EAAEC,gBACvC,GAAAV,EAAAY,GAAA,EAACM,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,QACRC,KAAK,SACLC,KAAK,OACLlB,gBAAeZ,EACfa,UAAU,kCACVI,QAAS,IACPc,EAAEC,eAAe,GACjBD,EAAEE,cAAc,GAChBhC,EAAc,CAACD,EACjB,WAEA,GAAAS,EAAAY,GAAA,EAACa,EAAAA,CAAUA,CAAAA,CACTJ,KAAM,GACNjB,UAAU,iDAMlB,GAAAJ,EAAAY,GAAA,EAACC,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAAC,CAAC3B,EAAYsB,QAAQ,EAAEC,gBACvC,GAAAV,EAAAY,GAAA,EAACc,KAAAA,CACCvB,gBAAeZ,EACfa,UAAU,mFACVuB,MAAO,CACLC,OACErC,GAAcJ,EAAYsB,QAAQ,EAAEC,OAChCvB,GAAAA,EAAYsB,QAAQ,CAACC,MAAM,CAAQ,GACnC,KACR,WAECvB,EAAYsB,QAAQ,EAAEoB,IAAI,GACzB,EAAAjB,GAAA,CAACkB,KAAAA,UACC,EAAA7B,IAAA,CAACI,EAAAA,CAAIA,CAAAA,CACHC,KAAMyB,EAAKxB,IAAI,CACfI,cAAavB,IAAe2C,EAAKhC,OAAO,CACxCS,QAAS,KACPnB,EAAgB0C,EAAKhC,OAAO,EACb,YAAXJ,GACFD,EAAc,GAElB,EACAU,UAAU,kJAEV,EAAAQ,GAAA,CAACI,OAAAA,CAAKZ,UAAU,2GACf2B,EAAKd,IAAI,KAbLc,EAAKC,GAAG,SAqB7B,mNCpGe,SAASC,IACtB,GAAM,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACR,CAAEC,WAAAA,CAAU,CAAE3C,cAAAA,CAAa,CAAE,CAAGG,CAAAA,EAAAA,EAAAA,CAAAA,IAChC,CAAEyC,KAAAA,CAAI,CAAEC,SAAAA,CAAQ,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IAErBC,EAAe,CACnB,CACEC,GAAI,eACJC,MAAO,GACPC,MAAO,CACL,CACEX,IAAK,YACLf,KAAMiB,EAAE,aACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACgC,EAAAA,CAAIA,CAAAA,CAACvB,KAAK,OACjBd,KAAM,IACNR,QAAS,aACX,EACA,CACEiC,IAAK,WACLf,KAAMiB,EAAE,YACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACiC,EAAAA,CAAGA,CAAAA,CAACxB,KAAK,OAChBd,KAAM,YACNR,QAAS,WACTU,SAAU,CACR,CACEuB,IAAK,mBACLf,KAAMiB,EAAE,WACR3B,KAAM,YACNR,QAAS,UACX,EACA,CACEiC,IAAK,mBACLf,KAAMiB,EAAE,WACR3B,KAAM,oBACNR,QAAS,SACX,EACD,EAEH,CACEiC,IAAK,YACLf,KAAMiB,EAAE,aACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACkC,EAAAA,CAAUA,CAAAA,CAACzB,KAAK,OACvBd,KAAM,aACNR,QAAS,YACTU,SAAU,CACR,CACEuB,IAAK,oBACLjC,QAAS,YACTkB,KAAMiB,EAAE,WACR3B,KAAM,YACR,EACA,CACEyB,IAAK,oBACLjC,QAAS,qBACTkB,KAAMiB,EAAE,WACR3B,KAAM,oBACR,EACD,EAEH,CACEyB,IAAK,YACLf,KAAMiB,EAAE,aACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACmC,EAAAA,CAAOA,CAAAA,CAAC1B,KAAK,OACpBd,KAAM,aACNR,QAAS,YACTU,SAAU,CACR,CACEuB,IAAK,oBACLjC,QAAS,YACTkB,KAAMiB,EAAE,WACR3B,KAAM,YACR,EACA,CACEyB,IAAK,oBACLjC,QAAS,oBACTkB,KAAMiB,EAAE,WACR3B,KAAM,oBACR,EACD,EAEH,CACEyB,IAAK,YACLf,KAAMiB,EAAE,aACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACoC,EAAAA,CAAMA,CAAAA,CAAC3B,KAAK,OACnBd,KAAM,aACNR,QAAS,YACTU,SAAU,CACR,CACEuB,IAAK,oBACLjC,QAAS,YACTkB,KAAMiB,EAAE,WACR3B,KAAM,YACR,EACA,CACEyB,IAAK,iBACLjC,QAAS,oBACTkB,KAAMiB,EAAE,WACR3B,KAAM,oBACR,EACD,EAEH,CACEyB,IAAK,WACLf,KAAMiB,EAAE,YACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACqC,EAAAA,CAAWA,CAAAA,CAAC5B,KAAK,OACxBd,KAAM,YACNR,QAAS,UACX,EACA,CACEiC,IAAK,QACLjC,QAAS,QACTkB,KAAMiB,EAAE,SACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACsC,EAAAA,CAAKA,CAAAA,CAAC7B,KAAK,OAClBd,KAAM,QACR,EACA,CACEyB,IAAK,cACLf,KAAMiB,EAAE,eACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACuC,EAAAA,CAAIA,CAAAA,CAAC9B,KAAK,OACjBd,KAAM,eACNR,QAAS,aACX,EACD,EAEH,CACE0C,GAAI,eACJE,MAAO,CACL,CACEX,IAAK,YACLjC,QAAS,YACTkB,KAAMiB,EAAE,aACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACwC,EAAAA,CAAYA,CAAAA,CAAC/B,KAAK,OACzBd,KAAM,aACNE,SAAU,CACR,CACEuB,IAAK,YACLjC,QAAS,YACTkB,KAAMiB,EAAE,eACR3B,KAAM,YACR,EACA,CACEyB,IAAK,iBACLjC,QAAS,iBACTkB,KAAMiB,EAAE,iBACR3B,KAAM,iBACR,EACA,CACEyB,IAAK,aACLjC,QAAS,aACTkB,KAAMiB,EAAE,cACR3B,KAAM,uBACR,EACD,EAEH,CACEyB,IAAK,YACLjC,QAAS,YACTkB,KAAMiB,EAAE,aACRnB,KAAM,GAAAf,EAAAY,GAAA,EAACyC,EAAAA,CAAYA,CAAAA,CAAChC,KAAK,OACzBd,KAAM,aACNE,SAAU,CACR,CACEuB,IAAK,YACLjC,QAAS,YACTkB,KAAMiB,EAAE,WACR3B,KAAM,YACR,EACA,CACEyB,IAAK,gBACLjC,QAAS,iBACTkB,KAAMiB,EAAE,iBACR3B,KAAM,iBACR,EACA,CACEyB,IAAK,kBACLjC,QAAS,kBACTkB,KAAMiB,EAAE,mBACR3B,KAAM,4BACR,EACA,CACEyB,IAAK,aACLjC,QAAS,aACTkB,KAAMiB,EAAE,cACR3B,KAAM,uBACR,EACD,EAEH,CACEyB,IAAK,SACLjC,QAAS,SACTkB,KAAMiB,EAAE,UACRnB,KAAM,GAAAf,EAAAY,GAAA,EAAC0C,EAAAA,CAAOA,CAAAA,CAACjC,KAAK,OACpBd,KAAM,UACNE,SAAU,CACR,CACEuB,IAAK,SACLjC,QAAS,SACTkB,KAAMiB,EAAE,WACR3B,KAAM,SACR,EACA,CACEyB,IAAK,aACLjC,QAAS,cACTkB,KAAMiB,EAAE,cACR3B,KAAM,cACR,EACA,CACEyB,IAAK,aACLjC,QAAS,aACTkB,KAAMiB,EAAE,cACR3B,KAAM,oBACR,EACD,EAEH,CACEyB,IAAK,SACLjC,QAAS,SACTkB,KAAMiB,EAAE,UACRnB,KAAM,GAAAf,EAAAY,GAAA,EAAC2C,EAAAA,CAAWA,CAAAA,CAAClC,KAAK,OACxBd,KAAM,SACR,EACA,CACEyB,IAAK,WACLjC,QAAS,WACTkB,KAAMiB,EAAE,YACRnB,KAAM,GAAAf,EAAAY,GAAA,EAAC4C,EAAAA,CAAQA,CAAAA,CAACnC,KAAK,OACrBd,KAAM,WACR,EACD,EAEJ,CAED,MACE,GAAAP,EAAAC,IAAA,EAACC,MAAAA,CACCuD,gBAAerB,EACfhC,UAAU,0OAEV,GAAAJ,EAAAY,GAAA,EAACM,EAAAA,CAAMA,CAAAA,CACLG,KAAK,OACLF,QAAQ,UACRX,QAAS,IAAMf,EAAc,IAC7BW,UAAW,CAAC,mDAAmD,EAAE,EAAyB,GAAX,SAAc,UAAU,CAAC,UAExG,GAAAJ,EAAAY,GAAA,EAAC8C,EAAAA,CAAUA,CAAAA,CAAAA,KAIb,GAAA1D,EAAAY,GAAA,EAACV,MAAAA,CAAIE,UAAU,4FACb,GAAAJ,EAAAY,GAAA,EAACP,EAAAA,CAAIA,CAAAA,CAACC,KAAK,IAAIF,UAAU,4CACvB,GAAAJ,EAAAY,GAAA,EAAC+C,EAAAA,CAAKA,CAAAA,CACJC,IAAKC,CAAAA,EAAAA,EAAAA,EAAAA,EAASxB,GACdyB,MAAO,IACPlC,OAAQ,GACRmC,IAAKzB,EACLlC,UAAU,gCAIhB,GAAAJ,EAAAY,GAAA,EAACV,MAAAA,CAAIE,UAAU,4EACZoC,EAAaX,GAAG,CAAC,GAChB,GAAA7B,EAAAC,IAAA,EAACC,MAAAA,WACE8D,KAAAA,EAAQtB,KAAK,CACZ,GAAA1C,EAAAY,GAAA,EAACV,MAAAA,UACC,GAAAF,EAAAY,GAAA,EAACqD,EAAAA,CAASA,CAAAA,CAAC7D,UAAU,WAErB,KACJ,GAAAJ,EAAAY,GAAA,EAACc,KAAAA,CAAGtB,UAAU,+BACX4D,EAAQrB,KAAK,EAAEd,IAAI,GAClB,EAAAjB,GAAA,CAACkB,KAAAA,UACC,EAAAlB,GAAA,CAAC1B,EAAWA,CAACC,YAAa4C,KADnBA,EAAKC,GAAG,OARbgC,EAAQvB,EAAE,OAkB9B,2ICjRO,SAASyB,EAAa,CAC3BC,KAAAA,CAAI,CACJC,UAAAA,EAAY,EAAI,CAChBC,eAAAA,CAAc,CACP,EACP,GAAM,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAqB,IACnDL,EAAKtC,GAAG,CAAC,GAAQ,EAAE,GAAGK,CAAC,CAAEuC,QAAS,KAAM,KAEpC,CAACC,EAAaC,EAAe,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,EAAiB,IACvC1E,CAAAA,EAAAA,EAAAA,EAAAA,IAQhB,IAAM8E,EAAiBC,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,CAACpC,EAAYgC,KAC9CF,EAAY,GACVO,EAAUjD,GAAG,CAAC,GAAWE,EAAKU,EAAE,GAAKA,EAAK,CAAE,GAAGV,CAAI,CAAE0C,QAAAA,CAAQ,EAAI1C,GAErE,EAAG,EAAE,EAEL,MACE,GAAAgD,EAAA9E,IAAA,EAACC,MAAAA,CACCE,UAAW,CAAC,6EAA6E,EAAEgE,EAAY,SAAW,GAAG,CAAC,WAErHE,EAASzC,GAAG,CAAC,GACZmD,QAAAA,EAAIP,OAAO,CACT,GAAAM,EAAAnE,GAAA,EAACqE,EAAAA,CAEE,GAAGD,CAAG,CACPE,SAAUR,IAAgBM,EAAIvC,EAAE,CAChCjC,QAAS,IAAMmE,EAAeK,EAAIvC,EAAE,EACpCmC,eAAgBA,GAJXI,EAAIvC,EAAE,EAMX,MAEN,GAAAsC,EAAAnE,GAAA,EAACV,MAAAA,CAAIE,UAAU,mBACb,GAAA2E,EAAAnE,GAAA,EAACgC,EAAAA,CAAK0B,SAAUA,EAAUI,YAAaA,QAI/C,CAEA,SAASO,EAAI,CACXvC,MAAAA,CAAK,CACLD,GAAAA,CAAE,CACF1B,KAAAA,CAAI,CACJT,KAAAA,CAAI,CACJ4E,SAAAA,CAAQ,CACR1E,QAAAA,CAAO,CACPoE,eAAAA,CAAc,CASf,EACC,IAAMO,EAAMC,CAAAA,EAAAA,EAAAA,MAAAA,EAA0B,MAsBtC,MApBqBP,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,KAC/B,IAAMQ,EAAOF,EAAIG,OAAO,EAAEC,wBACpBC,EAAaC,QAAQD,UAEvBH,CAAAA,GAAQG,EAAaH,EAAKK,KAAK,CAAG,IACpCd,EAAenC,EAAI,QAEnBmC,EAAenC,EAAI,MAEvB,EAAG,CAACA,EAAImC,EAAe,EAYrB,GAAAG,EAAA9E,IAAA,EAACI,EAAAA,CAAIA,CAAAA,CACHC,KAAMA,EACNqF,aAAYT,EAAW,SAAW,GAClC1E,QAASA,EACToF,SAAU,GACVT,IAAKA,EACL/E,UAAU,kgBAETW,EACD,GAAAgE,EAAAnE,GAAA,EAACI,OAAAA,UAAM0B,MAGb,CAEA,SAASE,EAAK,CACZ0B,SAAAA,CAAQ,CACRI,YAAAA,CAAW,CAIZ,EACC,GAAM,CAACmB,EAAMC,EAAQ,CAAGxG,EAAAA,QAAc,CAAC,IACjCyG,EAAYzB,EAAS0B,MAAM,CAAC,GAAUjE,SAAAA,EAAK0C,OAAO,EAElD,CAAEvC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,WAEd,IAAI4D,EAAUrF,MAAM,CACX,KAIP,GAAAqE,EAAA9E,IAAA,EAACgG,EAAAA,EAAYA,CAAAA,CAACJ,KAAMA,EAAMK,aAAcJ,YACtC,GAAAf,EAAAnE,GAAA,EAACuF,EAAAA,EAAmBA,CAAAA,CAACC,QAAO,YAC1B,GAAArB,EAAA9E,IAAA,EAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUf,UAAU,qCACjC8B,EAAE,QACH,GAAA6C,EAAAnE,GAAA,EAACa,EAAAA,CAAUA,CAAAA,CAACJ,KAAM,UAItB,GAAA0D,EAAAnE,GAAA,EAACyF,EAAAA,EAAmBA,CAAAA,UACjBN,EAAUlE,GAAG,CAAC,GACb,GAAAkD,EAAAnE,GAAA,EAAC0F,EAAAA,EAAgBA,CAAAA,CACf3F,cAAa+D,IAAgB3C,EAAKU,EAAE,CAEpCrC,UAAU,wCAEV,GAAA2E,EAAA9E,IAAA,EAACI,EAAAA,CAAIA,CAAAA,CACHC,KAAMyB,EAAKzB,IAAI,CACfsF,SAAU,GACVpF,QAAS,IAAMsF,EAAQ,IACvB1F,UAAU,kDAET2B,EAAKhB,IAAI,CACV,GAAAgE,EAAAnE,GAAA,EAACI,OAAAA,UAAMe,EAAKW,KAAK,OAVdX,EAAKU,EAAE,OAiBxB,iGC3KA,IAAM8D,EAASjH,EAAAA,UAAgB,CAG7B,CAAC,CAAEc,UAAAA,CAAS,CAAE,GAAGoG,EAAO,CAAErB,IAC1B,GAAAJ,EAAAnE,GAAA,EAAC6F,EAAAA,EAAqB,EACpBrG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,qXACAtG,GAED,GAAGoG,CAAK,CACTrB,IAAKA,WAEL,GAAAJ,EAAAnE,GAAA,EAAC6F,EAAAA,EAAsB,EACrBrG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,sLAKRH,CAAAA,EAAOI,WAAW,CAAGF,EAAAA,EAAqB,CAACE,WAAW,CAEtD,IAAAC,EAAeL,oOCxBA,eAAeM,EAAW,CACvCpG,SAAAA,CAAQ,CAGR,EACA,MACE,GAAAT,EAAAC,IAAA,EAACC,MAAAA,CAAIE,UAAU,0BACb,GAAAJ,EAAAY,GAAA,EAACqB,EAAYA,CAAAA,GACb,GAAAjC,EAAAC,IAAA,EAACC,MAAAA,CAAIE,UAAU,mDACb,GAAAJ,EAAAY,GAAA,EAACkG,EAAAA,CAAMA,CAAAA,CAAAA,GACP,GAAA9G,EAAAY,GAAA,EAACV,MAAAA,CAAIE,UAAU,gFACZK,SAKX,gGClBe,SAASsG,IACtB,MACE,GAAAhC,EAAAnE,GAAA,EAACV,MAAAA,CAAIE,UAAU,kDACb,GAAA2E,EAAAnE,GAAA,EAACoG,EAAAA,CAAMA,CAAAA,CAAAA,IAGb", "sources": ["webpack://_N_E/?2465", "webpack://_N_E/./components/common/layout/SidenavItem.tsx", "webpack://_N_E/./components/common/layout/AdminSidenav.tsx", "webpack://_N_E/./components/common/layout/SecondaryNav.tsx", "webpack://_N_E/./components/ui/switch.tsx", "webpack://_N_E/./app/(protected)/@admin/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/loading.tsx"], "sourcesContent": ["import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\components\\\\common\\\\Header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\components\\\\common\\\\layout\\\\AdminSidenav.tsx\");\n", "\"use client\";\r\n\r\nimport { Case } from \"@/components/common/Case\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useApp } from \"@/hooks/useApp\";\r\nimport { ArrowDown2 } from \"iconsax-react\";\r\nimport Link from \"next/link\";\r\nimport { useSelectedLayoutSegment } from \"next/navigation\";\r\nimport * as React from \"react\";\r\n\r\ntype TSidebarItem = {\r\n  key: string;\r\n  name: string;\r\n  icon: React.ReactElement;\r\n  link: string;\r\n  segment: string;\r\n  color?: string;\r\n  children?: {\r\n    key: string;\r\n    link: string;\r\n    name: string;\r\n    segment: string;\r\n  }[];\r\n};\r\n\r\ninterface IProps {\r\n  sidebarItem: TSidebarItem;\r\n}\r\n\r\nexport function SidenavItem({ sidebarItem }: IProps) {\r\n  const [activeSlug, setIsActiveSlug] = React.useState(\"(dashboard)\");\r\n  const [isExtended, setIsExtended] = React.useState(false);\r\n\r\n  const { setIsExpanded: handleSidebar, device } = useApp();\r\n  const layoutSegment = useSelectedLayoutSegment();\r\n\r\n  React.useEffect(() => {\r\n    setIsActiveSlug(layoutSegment as string);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  React.useEffect(() => {\r\n    setIsExtended(sidebarItem.segment === layoutSegment);\r\n  }, [layoutSegment, sidebarItem.segment]);\r\n\r\n  return (\r\n    <div\r\n      data-extended={isExtended}\r\n      className=\"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent\"\r\n    >\r\n      <Link\r\n        href={sidebarItem.link}\r\n        onClick={() => {\r\n          setIsActiveSlug(sidebarItem.segment);\r\n          if (!sidebarItem.children?.length) {\r\n            if (device !== \"Desktop\") {\r\n              handleSidebar(false);\r\n            }\r\n          }\r\n        }}\r\n        data-active={layoutSegment === sidebarItem.segment}\r\n        className=\"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent\"\r\n      >\r\n        <Case condition={!!sidebarItem.icon}>\r\n          <div\r\n            data-active={layoutSegment === sidebarItem.segment}\r\n            className=\"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white\"\r\n          >\r\n            {sidebarItem?.icon}\r\n          </div>\r\n        </Case>\r\n\r\n        <span className=\"flex-1\">{sidebarItem.name}</span>\r\n\r\n        <Case condition={!!sidebarItem.children?.length}>\r\n          <Button\r\n            variant=\"ghost\"\r\n            type=\"button\"\r\n            size=\"icon\"\r\n            data-extended={isExtended}\r\n            className=\"group rounded-xl hover:bg-muted\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              e.preventDefault();\r\n              setIsExtended(!isExtended);\r\n            }}\r\n          >\r\n            <ArrowDown2\r\n              size={16}\r\n              className=\"group-data-[extended=true]:rotate-180\"\r\n            />\r\n          </Button>\r\n        </Case>\r\n      </Link>\r\n\r\n      <Case condition={!!sidebarItem.children?.length}>\r\n        <ul\r\n          data-extended={isExtended}\r\n          className=\"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2\"\r\n          style={{\r\n            height:\r\n              isExtended && sidebarItem.children?.length\r\n                ? sidebarItem.children.length * 32 + 20\r\n                : \"0px\",\r\n          }}\r\n        >\r\n          {sidebarItem.children?.map((item) => (\r\n            <li key={item.key}>\r\n              <Link\r\n                href={item.link}\r\n                data-active={activeSlug === item.segment}\r\n                onClick={() => {\r\n                  setIsActiveSlug(item.segment);\r\n                  if (device !== \"Desktop\") {\r\n                    handleSidebar(false);\r\n                  }\r\n                }}\r\n                className=\"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary\"\r\n              >\r\n                <span className=\"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary\" />\r\n                {item.name}\r\n              </Link>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </Case>\r\n    </div>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport { SidenavItem } from \"@/components/common/layout/SidenavItem\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Separator from \"@/components/ui/separator\";\r\nimport { useApp } from \"@/hooks/useApp\";\r\nimport { useBranding } from \"@/hooks/useBranding\";\r\nimport { imageURL } from \"@/lib/utils\";\r\nimport {\r\n  Add,\r\n  ArrowLeft2,\r\n  ArrowRight,\r\n  Cards,\r\n  Menu,\r\n  Profile2User,\r\n  Receive,\r\n  Repeat,\r\n  Setting2,\r\n  ShoppingBag,\r\n  ShoppingCart,\r\n  TagUser,\r\n  Tree,\r\n  UserOctagon,\r\n} from \"iconsax-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nexport default function AdminSidenav() {\r\n  const { t } = useTranslation();\r\n  const { isExpanded, setIsExpanded } = useApp();\r\n  const { logo, siteName } = useBranding();\r\n\r\n  const sidebarItems = [\r\n    {\r\n      id: \"sidebarItem1\",\r\n      title: \"\",\r\n      items: [\r\n        {\r\n          key: \"dashboard\",\r\n          name: t(\"Dashboard\"),\r\n          icon: <Menu size=\"20\" />,\r\n          link: \"/\",\r\n          segment: \"(dashboard)\",\r\n        },\r\n        {\r\n          key: \"deposits\",\r\n          name: t(\"Deposits\"),\r\n          icon: <Add size=\"20\" />,\r\n          link: \"/deposits\",\r\n          segment: \"deposits\",\r\n          children: [\r\n            {\r\n              key: \"deposits-pending\",\r\n              name: t(\"Pending\"),\r\n              link: \"/deposits\",\r\n              segment: \"deposits\",\r\n            },\r\n            {\r\n              key: \"deposits-history\",\r\n              name: t(\"History\"),\r\n              link: \"/deposits/history\",\r\n              segment: \"history\",\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          key: \"transfers\",\r\n          name: t(\"Transfers\"),\r\n          icon: <ArrowRight size=\"20\" />,\r\n          link: \"/transfers\",\r\n          segment: \"transfers\",\r\n          children: [\r\n            {\r\n              key: \"transfers-pending\",\r\n              segment: \"transfers\",\r\n              name: t(\"Pending\"),\r\n              link: \"/transfers\",\r\n            },\r\n            {\r\n              key: \"transfers-history\",\r\n              segment: \"transfers-history \",\r\n              name: t(\"History\"),\r\n              link: \"/transfers/history\",\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          key: \"withdraws\",\r\n          name: t(\"Withdraws\"),\r\n          icon: <Receive size=\"20\" />,\r\n          link: \"/withdraws\",\r\n          segment: \"withdraws\",\r\n          children: [\r\n            {\r\n              key: \"withdraws-pending\",\r\n              segment: \"withdraws\",\r\n              name: t(\"Pending\"),\r\n              link: \"/withdraws\",\r\n            },\r\n            {\r\n              key: \"withdraws-history\",\r\n              segment: \"withdraws-history\",\r\n              name: t(\"History\"),\r\n              link: \"/withdraws/history\",\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          key: \"exchanges\",\r\n          name: t(\"Exchanges\"),\r\n          icon: <Repeat size=\"20\" />,\r\n          link: \"/exchanges\",\r\n          segment: \"exchanges\",\r\n          children: [\r\n            {\r\n              key: \"exchanges-pending\",\r\n              segment: \"exchanges\",\r\n              name: t(\"Pending\"),\r\n              link: \"/exchanges\",\r\n            },\r\n            {\r\n              key: \"exchanges-list\",\r\n              segment: \"exchanges-history\",\r\n              name: t(\"History\"),\r\n              link: \"/exchanges/history\",\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          key: \"payments\",\r\n          name: t(\"Payments\"),\r\n          icon: <ShoppingBag size=\"20\" />,\r\n          link: \"/payments\",\r\n          segment: \"payments\",\r\n        },\r\n        {\r\n          key: \"cards\",\r\n          segment: \"cards\",\r\n          name: t(\"Cards\"),\r\n          icon: <Cards size=\"20\" />,\r\n          link: \"/cards\",\r\n        },\r\n        {\r\n          key: \"investments\",\r\n          name: t(\"Investments\"),\r\n          icon: <Tree size=\"20\" />,\r\n          link: \"/investments\",\r\n          segment: \"investments\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      id: \"sidebarItem2\",\r\n      items: [\r\n        {\r\n          key: \"customers\",\r\n          segment: \"customers\",\r\n          name: t(\"Customers\"),\r\n          icon: <Profile2User size=\"20\" />,\r\n          link: \"/customers\",\r\n          children: [\r\n            {\r\n              key: \"customers\",\r\n              segment: \"customers\",\r\n              name: t(\"Pending Kyc\"),\r\n              link: \"/customers\",\r\n            },\r\n            {\r\n              key: \"customers-list\",\r\n              segment: \"customers-list\",\r\n              name: t(\"Customer List\"),\r\n              link: \"/customers/list\",\r\n            },\r\n            {\r\n              key: \"bulk-email\",\r\n              segment: \"bulk-email\",\r\n              name: t(\"Bulk Email\"),\r\n              link: \"/customers/bulk-email\",\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          key: \"merchants\",\r\n          segment: \"merchants\",\r\n          name: t(\"Merchants\"),\r\n          icon: <ShoppingCart size=\"20\" />,\r\n          link: \"/merchants\",\r\n          children: [\r\n            {\r\n              key: \"merchants\",\r\n              segment: \"merchants\",\r\n              name: t(\"Pending\"),\r\n              link: \"/merchants\",\r\n            },\r\n            {\r\n              key: \"merchant-list\",\r\n              segment: \"merchants-list\",\r\n              name: t(\"Merchant List\"),\r\n              link: \"/merchants/list\",\r\n            },\r\n            {\r\n              key: \"payment-request\",\r\n              segment: \"payment-request\",\r\n              name: t(\"Payment Request\"),\r\n              link: \"/merchants/payment-request\",\r\n            },\r\n            {\r\n              key: \"bulk-email\",\r\n              segment: \"bulk-email\",\r\n              name: t(\"Bulk Email\"),\r\n              link: \"/merchants/bulk-email\",\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          key: \"agents\",\r\n          segment: \"agents\",\r\n          name: t(\"Agents\"),\r\n          icon: <TagUser size=\"20\" />,\r\n          link: \"/agents\",\r\n          children: [\r\n            {\r\n              key: \"agents\",\r\n              segment: \"agents\",\r\n              name: t(\"Pending\"),\r\n              link: \"/agents\",\r\n            },\r\n            {\r\n              key: \"agent-list\",\r\n              segment: \"agents-list\",\r\n              name: t(\"Agent List\"),\r\n              link: \"/agents/list\",\r\n            },\r\n            {\r\n              key: \"bulk-email\",\r\n              segment: \"bulk-email\",\r\n              name: t(\"Bulk Email\"),\r\n              link: \"/agents/bulk-email\",\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          key: \"staffs\",\r\n          segment: \"staffs\",\r\n          name: t(\"Staffs\"),\r\n          icon: <UserOctagon size=\"20\" />,\r\n          link: \"/staffs\",\r\n        },\r\n        {\r\n          key: \"settings\",\r\n          segment: \"settings\",\r\n          name: t(\"Settings\"),\r\n          icon: <Setting2 size=\"20\" />,\r\n          link: \"/settings\",\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div\r\n      data-expanded={isExpanded}\r\n      className=\"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto\"\r\n    >\r\n      <Button\r\n        size=\"icon\"\r\n        variant=\"outline\"\r\n        onClick={() => setIsExpanded(false)}\r\n        className={`absolute -right-5 top-4 rounded-full bg-background ${!isExpanded ? \"hidden\" : \"\"} lg:hidden`}\r\n      >\r\n        <ArrowLeft2 />\r\n      </Button>\r\n\r\n      {/* Logo */}\r\n      <div className=\"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4\">\r\n        <Link href=\"/\" className=\"flex items-center justify-center\">\r\n          <Image\r\n            src={imageURL(logo)}\r\n            width={160}\r\n            height={40}\r\n            alt={siteName}\r\n            className=\"max-h-10 object-contain\"\r\n          />\r\n        </Link>\r\n      </div>\r\n      <div className=\"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4\">\r\n        {sidebarItems.map((sidebar) => (\r\n          <div key={sidebar.id}>\r\n            {sidebar.title !== \"\" ? (\r\n              <div>\r\n                <Separator className=\"my-4\" />\r\n              </div>\r\n            ) : null}\r\n            <ul className=\"flex flex-col gap-1\">\r\n              {sidebar.items?.map((item) => (\r\n                <li key={item.key}>\r\n                  <SidenavItem sidebarItem={item} />\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { ArrowDown2 } from \"iconsax-react\";\r\nimport Link from \"next/link\";\r\nimport { useSelectedLayoutSegment } from \"next/navigation\";\r\nimport React, { useCallback, useEffect, useRef, useState } from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\ninterface TTabs {\r\n  title: string;\r\n  icon: React.JSX.Element;\r\n  href: string;\r\n  id: string;\r\n  className?: string;\r\n}\r\n\r\ninterface IProps {\r\n  tabs: TTabs[];\r\n  fullWidth?: boolean;\r\n  defaultSegment?: string;\r\n}\r\n\r\ninterface TabState extends TTabs {\r\n  placeTo: \"nav\" | \"menu\";\r\n}\r\n\r\nexport function SecondaryNav({\r\n  tabs,\r\n  fullWidth = true,\r\n  defaultSegment,\r\n}: IProps) {\r\n  const [navItems, setNavItems] = useState<TabState[]>(() =>\r\n    tabs.map((t) => ({ ...t, placeTo: \"nav\" })),\r\n  );\r\n  const [activeTabId, setActiveTabId] = useState<string>(\"\");\r\n  const segment = useSelectedLayoutSegment();\r\n\r\n  useEffect(() => {\r\n    setActiveTabId(\r\n      segment && segment !== defaultSegment ? segment : \"__DEFAULT__\",\r\n    );\r\n  }, [segment, defaultSegment]);\r\n\r\n  const updateTabPlace = useCallback((id: string, placeTo: \"nav\" | \"menu\") => {\r\n    setNavItems((prevItems) =>\r\n      prevItems.map((item) => (item.id === id ? { ...item, placeTo } : item)),\r\n    );\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      className={`inline-flex h-12 items-center rounded-lg bg-accent p-1 text-muted-foreground ${fullWidth ? \"w-full\" : \"\"}`}\r\n    >\r\n      {navItems.map((tab) =>\r\n        tab.placeTo === \"nav\" ? (\r\n          <Tab\r\n            key={tab.id}\r\n            {...tab}\r\n            isActive={activeTabId === tab.id}\r\n            onClick={() => setActiveTabId(tab.id)}\r\n            updateTabPlace={updateTabPlace}\r\n          />\r\n        ) : null,\r\n      )}\r\n      <div className=\"ml-auto\">\r\n        <Menu navItems={navItems} activeTabId={activeTabId} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction Tab({\r\n  title,\r\n  id,\r\n  icon,\r\n  href,\r\n  isActive,\r\n  onClick,\r\n  updateTabPlace,\r\n}: {\r\n  title: string;\r\n  id: string;\r\n  icon: React.ReactNode;\r\n  href: string;\r\n  isActive: boolean;\r\n  onClick: () => void;\r\n  updateTabPlace: (id: string, placeTo: \"nav\" | \"menu\") => void;\r\n}) {\r\n  const ref = useRef<HTMLAnchorElement>(null);\r\n\r\n  const handleResize = useCallback(() => {\r\n    const rect = ref.current?.getBoundingClientRect();\r\n    const innerWidth = window?.innerWidth;\r\n\r\n    if (rect && innerWidth < rect.right + 150) {\r\n      updateTabPlace(id, \"menu\");\r\n    } else {\r\n      updateTabPlace(id, \"nav\");\r\n    }\r\n  }, [id, updateTabPlace]);\r\n\r\n  useEffect(() => {\r\n    handleResize();\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, [handleResize]);\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      data-state={isActive ? \"active\" : \"\"}\r\n      onClick={onClick}\r\n      prefetch={false}\r\n      ref={ref}\r\n      className=\"inline-flex h-10 w-56 items-center justify-center gap-1 whitespace-nowrap rounded-md px-4 py-1.5 text-sm font-medium text-secondary-text ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite [&>svg]:text-secondary-text [&>svg]:data-[state=active]:text-primary\"\r\n    >\r\n      {icon}\r\n      <span>{title}</span>\r\n    </Link>\r\n  );\r\n}\r\n\r\nfunction Menu({\r\n  navItems,\r\n  activeTabId,\r\n}: {\r\n  navItems: TabState[];\r\n  activeTabId: string;\r\n}) {\r\n  const [open, setOpen] = React.useState(false);\r\n  const menuItems = navItems.filter((item) => item.placeTo === \"menu\");\r\n\r\n  const { t } = useTranslation();\r\n\r\n  if (menuItems.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <DropdownMenu open={open} onOpenChange={setOpen}>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"outline\" className=\"h-10 text-sm font-medium\">\r\n          {t(\"More\")}\r\n          <ArrowDown2 size={16} />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n\r\n      <DropdownMenuContent>\r\n        {menuItems.map((item) => (\r\n          <DropdownMenuItem\r\n            data-active={activeTabId === item.id}\r\n            key={item.id}\r\n            className=\"data-[active=true]:bg-accent\"\r\n          >\r\n            <Link\r\n              href={item.href}\r\n              prefetch={false}\r\n              onClick={() => setOpen(false)}\r\n              className=\"flex h-full w-full items-center gap-2\"\r\n            >\r\n              {item.icon}\r\n              <span>{item.title}</span>\r\n            </Link>\r\n          </DropdownMenuItem>\r\n        ))}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted\",\r\n      className,\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]\",\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n));\r\nSwitch.displayName = SwitchPrimitives.Root.displayName;\r\n\r\nexport default Switch;\r\n", "import Header from \"@/components/common/Header\";\r\nimport AdminSidenav from \"@/components/common/layout/AdminSidenav\";\r\nimport React from \"react\";\r\n\r\nexport default async function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <div className=\"flex h-screen\">\r\n      <AdminSidenav />\r\n      <div className=\"relative h-full w-full overflow-hidden\">\r\n        <Header />\r\n        <div className=\"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0\">\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "SidenavItem", "sidebarItem", "activeSlug", "setIsActiveSlug", "React", "isExtended", "setIsExtended", "setIsExpanded", "handleSidebar", "device", "useApp", "layoutSegment", "useSelectedLayoutSegment", "segment", "jsx_runtime", "jsxs", "div", "data-extended", "className", "Link", "href", "link", "onClick", "children", "length", "data-active", "jsx", "Case", "condition", "icon", "span", "name", "<PERSON><PERSON>", "variant", "type", "size", "e", "stopPropagation", "preventDefault", "ArrowDown2", "ul", "style", "height", "map", "li", "item", "key", "AdminSidenav", "t", "useTranslation", "isExpanded", "logo", "siteName", "useBranding", "sidebarItems", "id", "title", "items", "<PERSON><PERSON>", "Add", "ArrowRight", "Receive", "Repeat", "ShoppingBag", "Cards", "Tree", "Profile2User", "ShoppingCart", "TagUser", "UserOctagon", "Setting2", "data-expanded", "ArrowLeft2", "Image", "src", "imageURL", "width", "alt", "sidebar", "Separator", "SecondaryNav", "tabs", "fullWidth", "defaultSegment", "navItems", "setNavItems", "useState", "placeTo", "activeTabId", "setActiveTabId", "updateTabPlace", "useCallback", "prevItems", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "tab", "Tab", "isActive", "ref", "useRef", "rect", "current", "getBoundingClientRect", "innerWidth", "window", "right", "data-state", "prefetch", "open", "<PERSON><PERSON><PERSON>", "menuItems", "filter", "DropdownMenu", "onOpenChange", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "DropdownMenuContent", "DropdownMenuItem", "Switch", "props", "SwitchPrimitives", "cn", "displayName", "__WEBPACK_DEFAULT_EXPORT__", "RootLayout", "Header", "Loading", "Loader"], "sourceRoot": ""}