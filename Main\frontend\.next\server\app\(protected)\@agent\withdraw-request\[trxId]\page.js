(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2978],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},94122:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>k,default:()=>M});var s,a={};r.r(a),r.d(a,{AppRouter:()=>p.WY,ClientPageRoot:()=>p.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>p.yO,NotFoundBoundary:()=>p.O4,Postpone:()=>p.hQ,RenderFromTemplateContext:()=>p.b5,__next_app__:()=>h,actionAsyncStorage:()=>p.Wz,createDynamicallyTrackedSearchParams:()=>p.rL,createUntrackedSearchParams:()=>p.S5,decodeAction:()=>p.Hs,decodeFormState:()=>p.dH,decodeReply:()=>p.kf,originalPathname:()=>g,pages:()=>x,patchFetch:()=>p.XH,preconnect:()=>p.$P,preloadFont:()=>p.C5,preloadStyle:()=>p.oH,renderToReadableStream:()=>p.aW,requestAsyncStorage:()=>p.Fg,routeModule:()=>f,serverHooks:()=>p.GP,staticGenerationAsyncStorage:()=>p.AT,taintObjectReference:()=>p.nr,tree:()=>u}),r(67206);var n=r(79319),i=r(20518),o=r(61902),c=r(62042),l=r(44630),d=r(44828),m=r(65505),p=r(13839);let u=["",{children:["(protected)",{agent:["children",{children:["withdraw-request",{children:["[trxId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38862)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\withdraw-request\\[trxId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,86852)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\withdraw-request\\[trxId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,51009)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\withdraw-request\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,44491)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,20626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],admin:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],x=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\withdraw-request\\[trxId]\\page.tsx"],g="/(protected)/@agent/withdraw-request/[trxId]/page",h={require:r,loadChunk:()=>Promise.resolve()},f=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@agent/withdraw-request/[trxId]/page",pathname:"/withdraw-request/[trxId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}});var v=r(69094),E=r(5787),S=r(90527);let b=e=>e?JSON.parse(e):void 0,N=self.__BUILD_MANIFEST,P=b(self.__REACT_LOADABLE_MANIFEST),j=null==(s=self.__RSC_MANIFEST)?void 0:s["/(protected)/@agent/withdraw-request/[trxId]/page"],y=b(self.__RSC_SERVER_MANIFEST),D=b(self.__NEXT_FONT_MANIFEST),w=b(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];j&&y&&(0,E.Mo)({clientReferenceManifest:j,serverActionsManifest:y,serverModuleMap:(0,S.w)({serverActionsManifest:y,pageName:"/(protected)/@agent/withdraw-request/[trxId]/page"})});let A=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@agent/withdraw-request/[trxId]/page",appMod:null,pageMod:a,errorMod:null,error500Mod:null,Document:null,buildManifest:N,renderToHTML:c.f,reactLoadableManifest:P,clientReferenceManifest:j,serverActionsManifest:y,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:D,incrementalCacheHandler:null,interceptionRouteRewrites:w}),k=a;function M(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:A})}},4792:(e,t,r)=>{Promise.resolve().then(r.bind(r,75192))},75192:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y,runtime:()=>j});var s=r(60926),a=r(29411),n=r(62797),i=r(36162),o=r(74988),c=r(1181),l=r(25694);async function d(e){try{if(!e)throw Error("Withdraw id is required");let t=await c.Z.put(`/withdraw-requests/accept/${e}`,{id:e});return(0,l.B)(t)}catch(e){return(0,l.D)(e)}}async function m(e){try{if(!e)throw Error("Withdraw id is required");let t=await c.Z.put(`/withdraw-requests/decline/${e}`,{id:e});return(0,l.B)(t)}catch(e){return(0,l.D)(e)}}var p=r(43291),u=r(65091),x=r(3632),g=r(14455),h=r(37988),f=r(55071),v=r(31949),E=r(90543),S=r(51018),b=r(64947),N=r(39228),P=r(32167);let j="edge";function y(){let{t:e}=(0,N.$G)(),t=(0,b.UO)(),{data:r,isLoading:c,mutate:l}=(0,p.d)(`/transactions/trx/${t.trxId}`);if(c)return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(a.Loader,{})});let j=t=>{P.toast.promise(d(t),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return l(r),e.message},error:e=>e.message})},y=t=>{P.toast.promise(m(t),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return l(r),e.message},error:e=>e.message})},D=r?.data?new x.C(r?.data):null,w=new u.F;return D?(0,s.jsx)("div",{className:"mb-10 p-2 sm:mb-0 sm:p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,s.jsx)("div",{className:"col-span-12 lg:col-span-7",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,s.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,s.jsx)(f.Z,{variant:"Bulk",size:32,className:"text-primary"}),(0,s.jsxs)("h2",{className:"font-semibold",children:[e("Withdraw"),"#",t.withdrawId]})]}),(0,s.jsx)(n.z,{senderAvatar:(0,u.qR)(D.from.image),senderName:D.from.label,senderInfo:[D.from?.email,D?.from?.phone],receiverAvatar:(0,u.qR)(D?.to?.image),receiverName:D?.to?.label,receiverInfo:[D?.to?.email,D?.to?.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,s.jsx)(o.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Date")}),(0,s.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:D?.createdAt?(0,g.WU)(D.createdAt,"dd MMM yyyy; hh:mm a"):""})]}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Amount")}),(0,s.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:w.formatVC(D.amount,D.metaData.currency)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Service charge")}),(0,s.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:w.formatVC(D.fee,D.metaData.currency)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("User gets")}),(0,s.jsx)("div",{className:"col-span-6 text-sm font-semibold sm:text-base",children:w.formatVC(D.total,D.metaData.currency)})]})]}),(0,s.jsx)(o.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,s.jsx)("div",{className:"flex flex-col",children:(0,s.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Transaction ID")}),(0,s.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[D.trxId,(0,s.jsx)(i.z,{type:"button",onClick:()=>(0,u.Fp)(D.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,s.jsx)(v.Z,{size:"20"})})]})]})}),(0,s.jsx)(o.Z,{className:"mb-1 mt-[5px] bg-border"}),"pending"===D.status?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,s.jsxs)(i.z,{type:"button",onClick:()=>j(D?.id),className:"gap-1 rounded-lg bg-spacial-green px-4 py-2 font-medium text-background hover:bg-[#219621] hover:text-background",children:[(0,s.jsx)(E.Z,{}),e("Approve")]}),(0,s.jsxs)(i.z,{type:"button",onClick:()=>y(D?.id),className:"gap-1 rounded-lg bg-[#D13438] px-4 py-2 font-medium text-white hover:bg-[#a5272b] hover:text-white",children:[(0,s.jsx)(S.Z,{}),e("Reject")]})]}):null]})}),(0,s.jsx)("div",{className:"col-span-12 lg:col-span-5",children:(0,s.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("h2",{children:e("Method info")})}),(0,s.jsx)(o.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Method used")}),(0,s.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:D?.metaData?.agentMethod??"--"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Number")}),(0,s.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:D?.metaData?.value??"--"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,s.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Wallet")}),(0,s.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:D?.metaData?.currency??"undefine"})]})]})]})})]})}):(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,s.jsx)(h.Z,{}),e("No data found")]})}},55071:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var s=r(61394),a=r(29220),n=r(31036),i=r.n(n),o=["variant","color","size"],c=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10 10-4.49 10-10S17.51 2 12 2Zm3.92 10.75h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8a.749.749 0 1 1 0 1.5Z",fill:t}))},l=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M14.99 12H16M8 12h4M4 6c-1.25 1.67-2 3.75-2 6 0 5.52 4.48 10 10 10s10-4.48 10-10S17.52 2 12 2c-1.43 0-2.8.3-4.03.85",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{opacity:".4",d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z",fill:t}),a.createElement("path",{d:"M15.92 12.75h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8a.749.749 0 1 1 0 1.5Z",fill:t}))},m=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.92 22c5.5 0 10-4.5 10-10s-4.5-10-10-10-10 4.5-10 10 4.5 10 10 10ZM7.92 12h8",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.92 22.75C6 22.75 1.17 17.93 1.17 12S6 1.25 11.92 1.25 22.67 6.07 22.67 12s-4.82 10.75-10.75 10.75Zm0-20c-5.1 0-9.25 4.15-9.25 9.25s4.15 9.25 9.25 9.25 9.25-4.15 9.25-9.25-4.15-9.25-9.25-9.25Z",fill:t}),a.createElement("path",{d:"M15.92 12.75h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8a.749.749 0 1 1 0 1.5Z",fill:t}))},u=function(e){var t=e.color;return a.createElement(a.Fragment,null,a.createElement("path",{d:"M11.92 22c5.5 0 10-4.5 10-10s-4.5-10-10-10-10 4.5-10 10 4.5 10 10 10Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),a.createElement("path",{opacity:".34",d:"M7.92 12h8",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},x=function(e,t){switch(e){case"Bold":return a.createElement(c,{color:t});case"Broken":return a.createElement(l,{color:t});case"Bulk":return a.createElement(d,{color:t});case"Linear":default:return a.createElement(m,{color:t});case"Outline":return a.createElement(p,{color:t});case"TwoTone":return a.createElement(u,{color:t})}},g=(0,a.forwardRef)(function(e,t){var r=e.variant,n=e.color,i=e.size,c=(0,s._)(e,o);return a.createElement("svg",(0,s.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),x(r,n))});g.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},g.defaultProps={variant:"Linear",color:"currentColor",size:"24"},g.displayName="MinusCirlce"},86852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(42416),a=r(21237);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(a.a,{})})}},38862:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,runtime:()=>a});var s=r(18264);let a=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\withdraw-request\[trxId]\page.tsx#runtime`),n=(0,s.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\withdraw-request\[trxId]\page.tsx#default`)},51009:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(42416),a=r(21237);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,s.jsx)(a.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,2682,7827,7283,5089,7839],()=>t(94122));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@agent/withdraw-request/[trxId]/page"]=r}]);
//# sourceMappingURL=page.js.map