{"version": 3, "middleware": {}, "functions": {"/(auth)/register/email-verification-status/page": {"files": ["server/server-reference-manifest.js", "server/app/(auth)/register/email-verification-status/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/7283.js", "server/app/(auth)/register/email-verification-status/page.js"], "name": "app/(auth)/register/email-verification-status/page", "page": "/(auth)/register/email-verification-status/page", "matchers": [{"regexp": "^/register/email\\-verification\\-status$", "originalSource": "/register/email-verification-status"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/deposits/[depositId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/deposits/[depositId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/app/(protected)/@admin/deposits/[depositId]/page.js"], "name": "app/(protected)/@admin/deposits/[depositId]/page", "page": "/(protected)/@admin/deposits/[depositId]/page", "matchers": [{"regexp": "^/deposits/(?<depositId>[^/]+?)$", "originalSource": "/deposits/[depositId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/withdraws/[withdrawId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/withdraws/[withdrawId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/2682.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/app/(protected)/@admin/withdraws/[withdrawId]/page.js"], "name": "app/(protected)/@admin/withdraws/[withdrawId]/page", "page": "/(protected)/@admin/withdraws/[withdrawId]/page", "matchers": [{"regexp": "^/withdraws/(?<withdrawId>[^/]+?)$", "originalSource": "/withdraws/[withdrawId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@agent/deposit-request/[trxId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@agent/deposit-request/[trxId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/2682.js", "server/edge-chunks/7827.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/7839.js", "server/app/(protected)/@agent/deposit-request/[trxId]/page.js"], "name": "app/(protected)/@agent/deposit-request/[trxId]/page", "page": "/(protected)/@agent/deposit-request/[trxId]/page", "matchers": [{"regexp": "^/deposit\\-request/(?<trxId>[^/]+?)$", "originalSource": "/deposit-request/[trxId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@agent/withdraw-request/[trxId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@agent/withdraw-request/[trxId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/2682.js", "server/edge-chunks/7827.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/7839.js", "server/app/(protected)/@agent/withdraw-request/[trxId]/page.js"], "name": "app/(protected)/@agent/withdraw-request/[trxId]/page", "page": "/(protected)/@agent/withdraw-request/[trxId]/page", "matchers": [{"regexp": "^/withdraw\\-request/(?<trxId>[^/]+?)$", "originalSource": "/withdraw-request/[trxId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/exchanges/[exchangeId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/exchanges/[exchangeId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/app/(protected)/@admin/exchanges/[exchangeId]/page.js"], "name": "app/(protected)/@admin/exchanges/[exchangeId]/page", "page": "/(protected)/@admin/exchanges/[exchangeId]/page", "matchers": [{"regexp": "^/exchanges/(?<exchangeId>[^/]+?)$", "originalSource": "/exchanges/[exchangeId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/settings/gateways/[gatewayId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/settings/gateways/[gatewayId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/4969.js", "server/edge-chunks/1474.js", "server/edge-chunks/8583.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/3020.js", "server/app/(protected)/@admin/settings/gateways/[gatewayId]/page.js"], "name": "app/(protected)/@admin/settings/gateways/[gatewayId]/page", "page": "/(protected)/@admin/settings/gateways/[gatewayId]/page", "matchers": [{"regexp": "^/settings/gateways/(?<gatewayId>[^/]+?)$", "originalSource": "/settings/gateways/[gatewayId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/settings/plugins/[pluginId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/settings/plugins/[pluginId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/4969.js", "server/edge-chunks/8583.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/settings/plugins/[pluginId]/page.js"], "name": "app/(protected)/@admin/settings/plugins/[pluginId]/page", "page": "/(protected)/@admin/settings/plugins/[pluginId]/page", "matchers": [{"regexp": "^/settings/plugins/(?<pluginId>[^/]+?)$", "originalSource": "/settings/plugins/[pluginId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/4969.js", "server/edge-chunks/1474.js", "server/edge-chunks/7848.js", "server/edge-chunks/8583.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/4656.js", "server/edge-chunks/3020.js", "server/app/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page.js"], "name": "app/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page", "page": "/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page", "matchers": [{"regexp": "^/settings/withdraw\\-methods/(?<withdrawId>[^/]+?)$", "originalSource": "/settings/withdraw-methods/[withdrawId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/transfers/[transferId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/transfers/[transferId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/app/(protected)/@admin/transfers/[transferId]/page.js"], "name": "app/(protected)/@admin/transfers/[transferId]/page", "page": "/(protected)/@admin/transfers/[transferId]/page", "matchers": [{"regexp": "^/transfers/(?<transferId>[^/]+?)$", "originalSource": "/transfers/[transferId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/transactions/[trxId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/transactions/[trxId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/2682.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/app/(protected)/@admin/transactions/[trxId]/page.js"], "name": "app/(protected)/@admin/transactions/[trxId]/page", "page": "/(protected)/@admin/transactions/[trxId]/page", "matchers": [{"regexp": "^/transactions/(?<trxId>[^/]+?)$", "originalSource": "/transactions/[trxId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/agents/[userId]/[agentId]/commissions/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/commissions/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/4774.js", "server/edge-chunks/7848.js", "server/edge-chunks/6147.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/4656.js", "server/edge-chunks/8748.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/commissions/page.js"], "name": "app/(protected)/@admin/agents/[userId]/[agentId]/commissions/page", "page": "/(protected)/@admin/agents/[userId]/[agentId]/commissions/page", "matchers": [{"regexp": "^/agents/(?<userId>[^/]+?)/(?<agentId>[^/]+?)/commissions$", "originalSource": "/agents/[userId]/[agentId]/commissions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/agents/[userId]/[agentId]/fees/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/fees/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/fees/page.js"], "name": "app/(protected)/@admin/agents/[userId]/[agentId]/fees/page", "page": "/(protected)/@admin/agents/[userId]/[agentId]/fees/page", "matchers": [{"regexp": "^/agents/(?<userId>[^/]+?)/(?<agentId>[^/]+?)/fees$", "originalSource": "/agents/[userId]/[agentId]/fees"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/agents/[userId]/[agentId]/kyc/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/870.js", "server/edge-chunks/2607.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page.js"], "name": "app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page", "page": "/(protected)/@admin/agents/[userId]/[agentId]/kyc/page", "matchers": [{"regexp": "^/agents/(?<userId>[^/]+?)/(?<agentId>[^/]+?)/kyc$", "originalSource": "/agents/[userId]/[agentId]/kyc"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/agents/[userId]/[agentId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/4774.js", "server/edge-chunks/870.js", "server/edge-chunks/1474.js", "server/edge-chunks/3099.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/3214.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/page.js"], "name": "app/(protected)/@admin/agents/[userId]/[agentId]/page", "page": "/(protected)/@admin/agents/[userId]/[agentId]/page", "matchers": [{"regexp": "^/agents/(?<userId>[^/]+?)/(?<agentId>[^/]+?)$", "originalSource": "/agents/[userId]/[agentId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/agents/[userId]/[agentId]/permissions/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/permissions/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/permissions/page.js"], "name": "app/(protected)/@admin/agents/[userId]/[agentId]/permissions/page", "page": "/(protected)/@admin/agents/[userId]/[agentId]/permissions/page", "matchers": [{"regexp": "^/agents/(?<userId>[^/]+?)/(?<agentId>[^/]+?)/permissions$", "originalSource": "/agents/[userId]/[agentId]/permissions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/agents/[userId]/[agentId]/send-email/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/send-email/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/7066.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/send-email/page.js"], "name": "app/(protected)/@admin/agents/[userId]/[agentId]/send-email/page", "page": "/(protected)/@admin/agents/[userId]/[agentId]/send-email/page", "matchers": [{"regexp": "^/agents/(?<userId>[^/]+?)/(?<agentId>[^/]+?)/send\\-email$", "originalSource": "/agents/[userId]/[agentId]/send-email"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/agents/[userId]/[agentId]/transactions/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/transactions/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4774.js", "server/edge-chunks/7848.js", "server/edge-chunks/6147.js", "server/edge-chunks/1991.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/4656.js", "server/edge-chunks/8748.js", "server/app/(protected)/@admin/agents/[userId]/[agentId]/transactions/page.js"], "name": "app/(protected)/@admin/agents/[userId]/[agentId]/transactions/page", "page": "/(protected)/@admin/agents/[userId]/[agentId]/transactions/page", "matchers": [{"regexp": "^/agents/(?<userId>[^/]+?)/(?<agentId>[^/]+?)/transactions$", "originalSource": "/agents/[userId]/[agentId]/transactions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/customers/[customerId]/kyc/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/customers/[customerId]/kyc/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/870.js", "server/edge-chunks/2607.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/customers/[customerId]/kyc/page.js"], "name": "app/(protected)/@admin/customers/[customerId]/kyc/page", "page": "/(protected)/@admin/customers/[customerId]/kyc/page", "matchers": [{"regexp": "^/customers/(?<customerId>[^/]+?)/kyc$", "originalSource": "/customers/[customerId]/kyc"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/customers/[customerId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/customers/[customerId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/4774.js", "server/edge-chunks/870.js", "server/edge-chunks/1474.js", "server/edge-chunks/3099.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/3214.js", "server/app/(protected)/@admin/customers/[customerId]/page.js"], "name": "app/(protected)/@admin/customers/[customerId]/page", "page": "/(protected)/@admin/customers/[customerId]/page", "matchers": [{"regexp": "^/customers/(?<customerId>[^/]+?)$", "originalSource": "/customers/[customerId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/customers/[customerId]/permissions/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/customers/[customerId]/permissions/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/customers/[customerId]/permissions/page.js"], "name": "app/(protected)/@admin/customers/[customerId]/permissions/page", "page": "/(protected)/@admin/customers/[customerId]/permissions/page", "matchers": [{"regexp": "^/customers/(?<customerId>[^/]+?)/permissions$", "originalSource": "/customers/[customerId]/permissions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/customers/[customerId]/send-email/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/customers/[customerId]/send-email/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/7066.js", "server/app/(protected)/@admin/customers/[customerId]/send-email/page.js"], "name": "app/(protected)/@admin/customers/[customerId]/send-email/page", "page": "/(protected)/@admin/customers/[customerId]/send-email/page", "matchers": [{"regexp": "^/customers/(?<customerId>[^/]+?)/send\\-email$", "originalSource": "/customers/[customerId]/send-email"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/customers/[customerId]/transactions/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/customers/[customerId]/transactions/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4774.js", "server/edge-chunks/7848.js", "server/edge-chunks/6147.js", "server/edge-chunks/1991.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/4656.js", "server/edge-chunks/8748.js", "server/edge-chunks/4153.js", "server/app/(protected)/@admin/customers/[customerId]/transactions/page.js"], "name": "app/(protected)/@admin/customers/[customerId]/transactions/page", "page": "/(protected)/@admin/customers/[customerId]/transactions/page", "matchers": [{"regexp": "^/customers/(?<customerId>[^/]+?)/transactions$", "originalSource": "/customers/[customerId]/transactions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page.js"], "name": "app/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page", "page": "/(protected)/@admin/merchants/[userId]/[merchantId]/fees/page", "matchers": [{"regexp": "^/merchants/(?<userId>[^/]+?)/(?<merchantId>[^/]+?)/fees$", "originalSource": "/merchants/[userId]/[merchantId]/fees"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/870.js", "server/edge-chunks/2607.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page.js"], "name": "app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page", "page": "/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page", "matchers": [{"regexp": "^/merchants/(?<userId>[^/]+?)/(?<merchantId>[^/]+?)/kyc$", "originalSource": "/merchants/[userId]/[merchantId]/kyc"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/merchants/[userId]/[merchantId]/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/4774.js", "server/edge-chunks/870.js", "server/edge-chunks/1474.js", "server/edge-chunks/3099.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/3214.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/page.js"], "name": "app/(protected)/@admin/merchants/[userId]/[merchantId]/page", "page": "/(protected)/@admin/merchants/[userId]/[merchantId]/page", "matchers": [{"regexp": "^/merchants/(?<userId>[^/]+?)/(?<merchantId>[^/]+?)$", "originalSource": "/merchants/[userId]/[merchantId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page.js"], "name": "app/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page", "page": "/(protected)/@admin/merchants/[userId]/[merchantId]/permissions/page", "matchers": [{"regexp": "^/merchants/(?<userId>[^/]+?)/(?<merchantId>[^/]+?)/permissions$", "originalSource": "/merchants/[userId]/[merchantId]/permissions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4774.js", "server/edge-chunks/7848.js", "server/edge-chunks/6147.js", "server/edge-chunks/1991.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/4656.js", "server/edge-chunks/8748.js", "server/edge-chunks/4153.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page.js"], "name": "app/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page", "page": "/(protected)/@admin/merchants/[userId]/[merchantId]/transactions/page", "matchers": [{"regexp": "^/merchants/(?<userId>[^/]+?)/(?<merchantId>[^/]+?)/transactions$", "originalSource": "/merchants/[userId]/[merchantId]/transactions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}, "/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page": {"files": ["server/server-reference-manifest.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/529.js", "server/edge-chunks/6578.js", "server/edge-chunks/3390.js", "server/edge-chunks/6165.js", "server/edge-chunks/4969.js", "server/edge-chunks/7283.js", "server/edge-chunks/5089.js", "server/edge-chunks/3711.js", "server/edge-chunks/7066.js", "server/app/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page.js"], "name": "app/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page", "page": "/(protected)/@admin/merchants/[userId]/[merchantId]/send-email/page", "matchers": [{"regexp": "^/merchants/(?<userId>[^/]+?)/(?<merchantId>[^/]+?)/send\\-email$", "originalSource": "/merchants/[userId]/[merchantId]/send-email"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bnx8tGjv9838m2TnsQXN6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zCXBNtxMw3N00Moz73c9LPajtLZmuVS6jxOKprWmwVw=", "__NEXT_PREVIEW_MODE_ID": "fb57c266ae15320503d4727174537daa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a3463854769153ed7a80c4d2cd79801b69b984405bb59d3af86ab361a615c4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d9ecfa523616ce8d6faaba71a76cb2741d61d7887914b574eeea722d450f93f"}}}, "sortedMiddleware": []}