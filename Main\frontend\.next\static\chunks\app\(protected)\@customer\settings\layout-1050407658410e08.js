(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[76603],{64065:function(e,t,n){Promise.resolve().then(n.bind(n,64441))},64441:function(e,t,n){"use strict";n.d(t,{Tabbar:function(){return s}});var r=n(57437),c=n(65448),o=n(32293),i=n(74337),l=n(22076),a=n(43949);function s(){let{t:e}=(0,a.$G)(),t=[{title:e("Account Settings"),icon:(0,r.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:"/settings/",id:"__DEFAULT__"},{title:e("KYC Verification"),icon:(0,r.jsx)(i.Z,{size:"24",variant:"Bulk"}),href:"/settings/kyc-verification-settings",id:"kyc-verification-settings"},{title:e("Login Sessions"),icon:(0,r.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return(0,r.jsx)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-black/[8%] bg-white p-4",children:(0,r.jsx)(c.a,{tabs:t})})}},22076:function(e,t,n){"use strict";n.d(t,{Z:function(){return k}});var r=n(74677),c=n(2265),o=n(40718),i=n.n(o),l=["variant","color","size"],a=function(e){var t=e.color;return c.createElement(c.Fragment,null,c.createElement("path",{fill:t,d:"M16.8 2h-2.6C11 2 9 4 9 7.2v4.05h4.44l-2.07-2.07a.742.742 0 01-.22-.53c0-.19.07-.38.22-.53.29-.29.77-.29 1.06 0l3.35 3.35c.29.29.29.77 0 1.06l-3.35 3.35c-.29.29-.77.29-1.06 0a.754.754 0 010-1.06l2.07-2.07H9v4.05C9 20 11 22 14.2 22h2.59c3.2 0 5.2-2 5.2-5.2V7.2C22 4 20 2 16.8 2zM2.75 11.25c-.41 0-.75.34-.75.75s.34.75.75.75H9v-1.5H2.75z"}))},s=function(e){var t=e.color;return c.createElement(c.Fragment,null,c.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M2 12h12.88M12.65 8.65L16 12l-3.35 3.35M21.5 13v2.26c0 4.47-1.79 6.26-6.26 6.26h-.13c-4.02 0-5.87-1.45-6.2-4.99M8.9 7.56c.31-3.6 2.16-5.07 6.21-5.07h.13c4.47 0 6.26 1.79 6.26 6.26"}))},u=function(e){var t=e.color;return c.createElement(c.Fragment,null,c.createElement("path",{fill:t,d:"M9 7.2v9.59C9 20 11 22 14.2 22h2.59c3.2 0 5.2-2 5.2-5.2V7.2C22 4 20 2 16.8 2h-2.6C11 2 9 4 9 7.2z",opacity:".4"}),c.createElement("path",{fill:t,d:"M12.43 8.12l3.35 3.35c.29.29.29.77 0 1.06l-3.35 3.35c-.29.29-.77.29-1.06 0a.754.754 0 010-1.06l2.07-2.07H2.75c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h10.69l-2.07-2.07a.742.742 0 01-.22-.53c0-.19.07-.38.22-.53.29-.3.76-.3 1.06 0z"}))},h=function(e){var t=e.color;return c.createElement(c.Fragment,null,c.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M8.9 7.56c.31-3.6 2.16-5.07 6.21-5.07h.13c4.47 0 6.26 1.79 6.26 6.26v6.52c0 4.47-1.79 6.26-6.26 6.26h-.13c-4.02 0-5.87-1.45-6.2-4.99M2 12h12.88"}),c.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M12.65 8.65L16 12l-3.35 3.35"}))},d=function(e){var t=e.color;return c.createElement(c.Fragment,null,c.createElement("path",{fill:t,d:"M15.24 22.27h-.13c-4.44 0-6.58-1.75-6.95-5.67-.04-.41.26-.78.68-.82.41-.04.78.27.82.68.29 3.14 1.77 4.31 5.46 4.31h.13c4.07 0 5.51-1.44 5.51-5.51V8.74c0-4.07-1.44-5.51-5.51-5.51h-.13c-3.71 0-5.19 1.19-5.46 4.39-.05.41-.39.72-.82.68a.751.751 0 01-.69-.81c.34-3.98 2.49-5.76 6.96-5.76h.13c4.91 0 7.01 2.1 7.01 7.01v6.52c0 4.91-2.1 7.01-7.01 7.01z"}),c.createElement("path",{fill:t,d:"M14.88 12.75H2c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h12.88a.749.749 0 110 1.5z"}),c.createElement("path",{fill:t,d:"M12.65 16.1c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06L14.94 12l-2.82-2.82a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l3.35 3.35c.29.29.29.77 0 1.06l-3.35 3.35c-.15.15-.34.22-.53.22z"}))},f=function(e){var t=e.color;return c.createElement(c.Fragment,null,c.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M8.9 7.56c.31-3.6 2.16-5.07 6.21-5.07h.13c4.47 0 6.26 1.79 6.26 6.26v6.52c0 4.47-1.79 6.26-6.26 6.26h-.13c-4.02 0-5.87-1.45-6.2-4.99"}),c.createElement("g",{opacity:".4"},c.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M2 12h12.88M12.65 8.65L16 12l-3.35 3.35"})))},m=function(e,t){switch(e){case"Bold":return c.createElement(a,{color:t});case"Broken":return c.createElement(s,{color:t});case"Bulk":return c.createElement(u,{color:t});case"Linear":default:return c.createElement(h,{color:t});case"Outline":return c.createElement(d,{color:t});case"TwoTone":return c.createElement(f,{color:t})}},k=(0,c.forwardRef)(function(e,t){var n=e.variant,o=e.color,i=e.size,a=(0,r._)(e,l);return c.createElement("svg",(0,r.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),m(n,o))});k.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},k.defaultProps={variant:"Linear",color:"currentColor",size:"24"},k.displayName="LoginCurve"}},function(e){e.O(0,[14438,31304,5062,80566,93909,28453,27648,48248,24872,65448,92971,95030,1744],function(){return e(e.s=64065)}),_N_E=e.O()}]);