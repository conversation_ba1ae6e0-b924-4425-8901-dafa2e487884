exports.id=9176,exports.ids=[9176],exports.modules={6625:(e,t,r)=>{Promise.resolve().then(r.bind(r,13986))},70489:(e,t,r)=>{Promise.resolve().then(r.bind(r,13986))},13986:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(10326),s=r(56140),l=r(63761),n=r(567),o=r(8281),i=r(75584),d=r(77863),c=r(71305),u=r(35047),m=r(17577),f=r(70012);function h(){let{t:e}=(0,f.$G)(),t=(0,u.useSearchParams)(),[r,h]=m.useState(t.get("search")??""),[x,p]=m.useState([]),g=(0,u.useRouter)(),v=(0,u.usePathname)(),{data:j,meta:w,isLoading:b}=(0,i.Z)(`/investments/history?${t.toString()}`,{keepPreviousData:!0});return a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[a.jsx("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:a.jsx("div",{className:"flex w-full flex-wrap items-center gap-4 md:flex-nowrap md:justify-end",children:a.jsx(l.R,{value:r,onChange:e=>{e.preventDefault();let t=(0,d.w4)(e.target.value);h(e.target.value),g.replace(`${v}?${t.toString()}`)},iconPlacement:"end",placeholder:e("Search..."),containerClass:"w-full sm:w-auto"})})}),a.jsx(o.Z,{className:"my-4"}),a.jsx(s.Z,{data:j,sorting:x,isLoading:b,setSorting:p,pagination:{total:w?.total,page:w?.currentPage,limit:w?.perPage},structure:[{id:"createdAt",header:e("Date"),cell:({row:e})=>{let t=e.original;return t.createdAt?a.jsx("span",{className:"block w-[100px] text-sm font-normal leading-5 text-foreground",children:(0,c.WU)(t.createdAt,"dd MMM yyyy; \n hh:mm a")}):a.jsx("span",{className:"text-sm font-normal",children:"N/A"})}},{id:"name",header:e("Name"),cell:({row:e})=>a.jsx("p",{children:e.original?.name})},{id:"status",header:e("Status"),cell:({row:e})=>{let t=e?.original;return"completed"===t.status?a.jsx(n.C,{variant:"success",children:(0,d.fl)(t.status)}):"withdrawn"===t.status?a.jsx(n.C,{variant:"default",children:(0,d.fl)(t.status)}):"on_hold"===t.status?a.jsx(n.C,{variant:"warning",children:(0,d.fl)(t.status)}):a.jsx(n.C,{variant:"secondary",children:(0,d.fl)(t.status)})}},{id:"amountInvested",header:e("Amount Invested"),cell:({row:e})=>(0,a.jsxs)("p",{className:"whitespace-nowrap",children:[e.original?.amountInvested," ",e.original?.currency?.toUpperCase()]})},{id:"interestRate",header:e("Interest Rate"),cell:({row:e})=>(0,a.jsxs)("p",{children:[e.original?.interestRate,"%"]})},{id:"profit",header:e("Profit"),cell:({row:e})=>(0,a.jsxs)("p",{className:"whitespace-nowrap",children:[e.original?.profit," ",e.original?.currency?.toUpperCase()]})},{id:"duration",header:e("Duration"),cell:({row:t})=>(0,a.jsxs)("p",{className:"font-normal",children:[t.original?.duration," ",t.original?.duration>1?e("Days"):e("Day")]})},{id:"durationType",header:e("Duration Type"),cell:({row:e})=>a.jsx("p",{className:"font-normal",children:(0,d.fl)(e.original?.durationType)})},{id:"withdrawAfterMatured",header:e("Withdraw Type"),cell:({row:t})=>t.original?.withdrawAfterMatured?a.jsx(n.C,{variant:"important",children:e("Yes")}):a.jsx(n.C,{variant:"secondary",children:e("No")})}]})]})})}},56140:(e,t,r)=>{"use strict";r.d(t,{Z:()=>N});var a=r(10326),s=r(77863),l=r(86508),n=r(11798),o=r(77132),i=r(6216),d=r(75817),c=r(40420),u=r(35047),m=r(93327),f=r(17577),h=r(70012),x=r(90772);let p=f.forwardRef(({className:e,...t},r)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:r,className:(0,s.ZP)("w-full caption-bottom text-sm",e),...t})}));p.displayName="Table";let g=f.forwardRef(({className:e,...t},r)=>a.jsx("thead",{ref:r,className:(0,s.ZP)("",e),...t}));g.displayName="TableHeader";let v=f.forwardRef(({className:e,...t},r)=>a.jsx("tbody",{ref:r,className:(0,s.ZP)("[&_tr:last-child]:border-0",e),...t}));v.displayName="TableBody",f.forwardRef(({className:e,...t},r)=>a.jsx("tfoot",{ref:r,className:(0,s.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let j=f.forwardRef(({className:e,...t},r)=>a.jsx("tr",{ref:r,className:(0,s.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));j.displayName="TableRow";let w=f.forwardRef(({className:e,...t},r)=>a.jsx("th",{ref:r,className:(0,s.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));w.displayName="TableHead";let b=f.forwardRef(({className:e,...t},r)=>a.jsx("td",{ref:r,className:(0,s.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));function N({data:e,isLoading:t=!1,structure:r,sorting:N,setSorting:y,padding:Z=!1,className:P,onRefresh:S,pagination:k}){let R=(0,f.useMemo)(()=>r,[r]),E=(0,u.useRouter)(),C=(0,u.usePathname)(),M=(0,u.useSearchParams)(),{t:T}=(0,h.$G)(),L=(0,l.b7)({data:e||[],columns:R,state:{sorting:N,onRefresh:S},onSortingChange:y,getCoreRowModel:(0,n.sC)(),getSortedRowModel:(0,n.tj)(),debugTable:!1});return t?a.jsx("div",{className:"rounded-md bg-background p-10",children:a.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:T("Loading...")})}):e?.length?(0,a.jsxs)("div",{className:(0,s.ZP)(`${Z?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,P),children:[(0,a.jsxs)(p,{children:[a.jsx(g,{children:L.getHeaderGroups().map(e=>a.jsx(j,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>a.jsx(w,{className:(0,s.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,a.jsxs)(x.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[T((0,l.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:a.jsx(i.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:a.jsx(i.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??a.jsx(i.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),a.jsx(v,{children:L.getRowModel().rows.map(e=>a.jsx(j,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>a.jsx(b,{className:"py-3 text-sm font-semibold",children:(0,l.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),k&&k.total>10&&a.jsx("div",{className:"pb-2 pt-6",children:a.jsx(m.Z,{showTotal:(e,t)=>T("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:k?.page,total:k?.total,pageSize:k?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(M);t.set("page",e.toString()),E.push(`${C}?${t.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>a.jsx("a",{...e,children:a.jsx(d.Z,{size:"18"})}),nextIcon:e=>a.jsx("a",{...e,children:a.jsx(c.Z,{size:"18"})})})})]}):a.jsx("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(o.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),T("No data found!")]})})}b.displayName="TableCell",f.forwardRef(({className:e,...t},r)=>a.jsx("caption",{ref:r,className:(0,s.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},63761:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var a=r(10326);r(17577);var s=r(54432),l=r(77863),n=r(32894);function o({iconPlacement:e="start",className:t,containerClass:r,...o}){return(0,a.jsxs)("div",{className:(0,l.ZP)("relative flex items-center",r),children:[a.jsx(n.Z,{size:"20",className:(0,l.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),a.jsx(s.I,{type:"text",className:(0,l.ZP)("h-10","end"===e?"pr-10":"pl-10",t),...o})]})}},54432:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var a=r(10326),s=r(17577),l=r(77863);let n=s.forwardRef(({className:e,type:t,...r},s)=>a.jsx("input",{type:t,className:(0,l.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...r}));n.displayName="Input"},75584:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(90799),s=r(35047);function l(e,t){let r=(0,s.usePathname)(),l=(0,s.useSearchParams)(),n=(0,s.useRouter)(),[o,i]=e.split("?"),d=new URLSearchParams(i);d.has("page")||d.set("page","1"),d.has("limit")||d.set("limit","10");let c=`${o}?${d.toString()}`,{data:u,error:m,isLoading:f,mutate:h,...x}=(0,a.d)(c,t);return{refresh:()=>h(u),data:u?.data?.data??[],meta:u?.data?.meta,filter:(e,t,a)=>{let s=new URLSearchParams(l.toString());t?s.set(e,t.toString()):s.delete(e),n.replace(`${r}?${s.toString()}`),a?.()},isLoading:f,error:m,...x}}},32894:(e,t,r)=>{"use strict";r.d(t,{Z:()=>p});var a=r(52920),s=r(17577),l=r.n(s),n=r(78439),o=r.n(n),i=["variant","color","size"],d=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},c=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{opacity:".4",d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",fill:t}),l().createElement("path",{d:"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},m=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},h=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{opacity:".4",d:"m22 22-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},x=function(e,t){switch(e){case"Bold":return l().createElement(d,{color:t});case"Broken":return l().createElement(c,{color:t});case"Bulk":return l().createElement(u,{color:t});case"Linear":default:return l().createElement(m,{color:t});case"Outline":return l().createElement(f,{color:t});case"TwoTone":return l().createElement(h,{color:t})}},p=(0,s.forwardRef)(function(e,t){var r=e.variant,s=e.color,n=e.size,o=(0,a._)(e,i);return l().createElement("svg",(0,a.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:n,height:n,viewBox:"0 0 24 24",fill:"none"}),x(r,s))});p.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},p.defaultProps={variant:"Linear",color:"currentColor",size:"24"},p.displayName="SearchNormal1"},21492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),s=r(72305);function l(){return a.jsx(s.default,{})}},84514:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(19510),s=r(40099),l=r(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(l.Z,{userRole:"agent"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(s.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},18406:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),s=r(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(s.a,{})})}},65914:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),s=r(48413);function l(){return a.jsx("div",{className:"flex justify-center",children:a.jsx(s.a,{})})}},72305:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\investments-history\page.tsx#default`)},88728:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(19510),s=r(40099),l=r(76609);function n({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(l.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(s.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},80549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),s=r(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(s.a,{})})}},9036:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),s=r(72305);function l(){return a.jsx(s.default,{})}}};