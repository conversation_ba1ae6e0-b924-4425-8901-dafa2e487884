(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[73377],{8587:function(e,d,s){Promise.resolve().then(s.bind(s,2498))},2498:function(e,d,s){"use strict";s.r(d),s.d(d,{default:function(){return g}});var a=s(57437),n=s(85539),i=s(85487),l=s(35974),r=s(93022),o=s(1828),t=s(73578),c=s(26812),v=s(31117),u=s(52789),x=s(45348),h=s(28456),m=s(99376),j=s(2265),p=s(43949),b=s(14438);function g(){var e,d,s,g,N,y,C,S,k,w,A,L,R,P,_,M,D,E,G,I,B,O,Y,Z,z;let $=(0,m.useParams)(),[F,T]=j.useState(""),[U,W]=j.useState(""),{t:q}=(0,p.$G)(),{data:H,isLoading:J}=(0,v.d)("/admin/users/permission/".concat($.merchantId)),{data:K,isLoading:Q}=(0,v.d)("/admin/users/blacklisted-methods/".concat($.merchantId,"&search=").concat(F)),{data:V,isLoading:X}=(0,v.d)("/admin/users/blacklisted-gateways/".concat($.merchantId,"&search=").concat(U)),ee=(e,d)=>{b.toast.promise((0,c.Y)(e,d),{loading:q("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return e.message},error:e=>e.message})};return(0,a.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsx)("div",{className:"flex items-center gap-1",children:(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:q("Permitted Actions")})})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsx)("div",{className:"max-w-[900px]",children:(0,a.jsxs)(t.iA,{children:[(0,a.jsx)(t.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(t.SC,{children:[(0,a.jsx)(t.ss,{children:q("Actions")}),(0,a.jsx)(t.ss,{children:q("Permission")})]})}),(0,a.jsx)(t.RM,{children:J?Array.from({length:8}).map((e,d)=>(0,a.jsxs)(t.SC,{children:[(0,a.jsx)(t.pj,{className:"w-full",children:(0,a.jsx)(r.O,{className:"h-4 w-2/3"})}),(0,a.jsx)(t.pj,{children:(0,a.jsx)(r.O,{className:"h-5 w-16"})})]},d)):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f,{title:q("Deposit money"),type:"deposit",defaultStatus:!!(null==H?void 0:null===(d=H.data)||void 0===d?void 0:null===(e=d.permission)||void 0===e?void 0:e.deposit),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}}),(0,a.jsx)(f,{title:q("Withdraw money"),type:"withdraw",defaultStatus:!!(null==H?void 0:null===(g=H.data)||void 0===g?void 0:null===(s=g.permission)||void 0===s?void 0:s.withdraw),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}}),(0,a.jsx)(f,{title:q("Payment"),type:"payment",defaultStatus:!!(null==H?void 0:null===(y=H.data)||void 0===y?void 0:null===(N=y.permission)||void 0===N?void 0:N.payment),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}}),(0,a.jsx)(f,{title:q("Exchange"),type:"exchange",defaultStatus:!!(null==H?void 0:null===(S=H.data)||void 0===S?void 0:null===(C=S.permission)||void 0===C?void 0:C.exchange),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}}),(0,a.jsx)(f,{title:q("Transfer"),type:"transfer",defaultStatus:!!(null==H?void 0:null===(w=H.data)||void 0===w?void 0:null===(k=w.permission)||void 0===k?void 0:k.transfer),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}}),(0,a.jsx)(f,{title:q("Add account"),type:"addAccount",defaultStatus:!!(null==H?void 0:null===(L=H.data)||void 0===L?void 0:null===(A=L.permission)||void 0===A?void 0:A.addAccount),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}}),(0,a.jsx)(f,{title:q("Add/Remove balance"),type:"addRemoveBalance",defaultStatus:!!(null==H?void 0:null===(P=H.data)||void 0===P?void 0:null===(R=P.permission)||void 0===R?void 0:R.addRemoveBalance),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}}),(0,a.jsx)(f,{title:q("User services"),type:"services",defaultStatus:!!(null==H?void 0:null===(M=H.data)||void 0===M?void 0:null===(_=M.permission)||void 0===_?void 0:_.services),onChange:e=>{var d,s;return ee(e,null==H?void 0:null===(s=H.data)||void 0===s?void 0:null===(d=s.permission)||void 0===d?void 0:d.id)}})]})})]})})})]})}),(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center justify-between gap-1 gap-y-2 sm:gap-y-0",children:[(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:q("Blacklisted Gateways")}),(0,a.jsx)(n.R,{value:F,onChange:e=>T(e.target.value),placeholder:q("Search"),iconPlacement:"end"})]})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsxs)(t.iA,{children:[(0,a.jsx)(t.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(t.SC,{children:[(0,a.jsx)(t.ss,{children:q("Logo")}),(0,a.jsx)(t.ss,{children:q("Name")}),(0,a.jsx)(t.ss,{children:q("Status")}),(0,a.jsx)(t.ss,{children:q("Recommended")}),(0,a.jsx)(t.ss,{children:q("Permission")})]})}),(0,a.jsx)(t.RM,{children:Q?(0,a.jsx)(t.SC,{children:(0,a.jsx)(t.pj,{colSpan:5,children:(0,a.jsx)(i.Loader,{})})}):(null==K?void 0:null===(E=K.data)||void 0===E?void 0:null===(D=E.blackListedMethods)||void 0===D?void 0:D.length)===0?(0,a.jsx)(t.SC,{children:(0,a.jsx)(t.pj,{colSpan:5,className:"bg-accent/50",children:q("No Data")})}):null==K?void 0:null===(I=K.data)||void 0===I?void 0:null===(G=I.blackListedMethods.map(e=>new x.n(e)))||void 0===G?void 0:G.map(e=>(0,a.jsxs)(t.SC,{className:"odd:bg-accent",children:[(0,a.jsx)(t.pj,{children:(0,a.jsx)("div",{className:"flex size-10 items-center justify-center rounded-full bg-muted",children:(0,a.jsx)(h.Z,{size:20})})}),(0,a.jsx)(t.pj,{className:"w-[420px]",children:e.name}),(0,a.jsx)(t.pj,{children:e.active?(0,a.jsx)(l.C,{variant:"success",children:q("Active")}):(0,a.jsx)(l.C,{variant:"secondary",children:q("Inactive")})}),(0,a.jsx)(t.pj,{children:e.recommended?(0,a.jsx)(l.C,{variant:"important",children:q("Yes")}):(0,a.jsx)(l.C,{variant:"secondary",children:q("No")})}),(0,a.jsx)(t.pj,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("span",{children:q("No")}),(0,a.jsx)(o.Z,{defaultChecked:!1,disabled:!0,className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]},e.id))})]})})]})}),(0,a.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,a.jsxs)("div",{className:"border-none px-4 py-0",children:[(0,a.jsx)("div",{className:"py-4 hover:no-underline",children:(0,a.jsxs)("div",{className:"flex w-full flex-wrap items-center justify-between gap-1 gap-y-2 sm:gap-y-0",children:[(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:q("Blacklisted Methods")}),(0,a.jsx)(n.R,{value:U,onChange:e=>W(e.target.value),placeholder:q("Search"),iconPlacement:"end"})]})}),(0,a.jsx)("div",{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:(0,a.jsxs)(t.iA,{children:[(0,a.jsx)(t.xD,{className:"[&_tr]:border-b-0",children:(0,a.jsxs)(t.SC,{children:[(0,a.jsx)(t.ss,{className:"w-2/5",children:q("Name")}),(0,a.jsx)(t.ss,{children:q("Status")}),(0,a.jsx)(t.ss,{children:q("Recommended")}),(0,a.jsx)(t.ss,{children:q("Permission")})]})}),(0,a.jsx)(t.RM,{children:X?(0,a.jsx)(t.SC,{children:(0,a.jsx)(t.pj,{colSpan:5,children:(0,a.jsx)(i.Loader,{})})}):(null==V?void 0:null===(O=V.data)||void 0===O?void 0:null===(B=O.blackListedGateways)||void 0===B?void 0:B.length)===0?(0,a.jsx)(t.SC,{children:(0,a.jsx)(t.pj,{colSpan:5,className:"bg-accent/50",children:q("No Data")})}):null==V?void 0:null===(z=V.data)||void 0===z?void 0:null===(Z=z.blackListedGateways)||void 0===Z?void 0:null===(Y=Z.map(e=>new u.M(e)))||void 0===Y?void 0:Y.map(e=>(0,a.jsxs)(t.SC,{className:"odd:bg-accent",children:[(0,a.jsx)(t.pj,{className:"w-2/5",children:null==e?void 0:e.name}),(0,a.jsx)(t.pj,{children:e.active?(0,a.jsx)(l.C,{variant:"success",children:q("Active")}):(0,a.jsx)(l.C,{variant:"secondary",children:q("Inactive")})}),(0,a.jsx)(t.pj,{children:e.recommended?(0,a.jsx)(l.C,{variant:"important",children:q("Yes")}):(0,a.jsx)(l.C,{variant:"secondary",children:q("No")})}),(0,a.jsx)(t.pj,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("span",{children:q("No")}),(0,a.jsx)(o.Z,{defaultChecked:!1,disabled:!0,className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]},null==e?void 0:e.id))})]})})]})})]})}function f(e){let{title:d,type:s,defaultStatus:n,onChange:i}=e,{t:l}=(0,p.$G)();return(0,a.jsxs)(t.SC,{className:"odd:bg-accent",children:[(0,a.jsx)(t.pj,{children:l(d)}),(0,a.jsx)(t.pj,{children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("span",{children:l(n?"Yes":"No")}),(0,a.jsx)(o.Z,{defaultChecked:n,onCheckedChange:e=>i({permission:s,status:e}),className:"border border-secondary-text data-[state=checked]:border-transparent [&>span]:bg-secondary-text [&>span]:data-[state=checked]:bg-background"})]})})]})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,67690,92971,95030,1744],function(){return e(e.s=8587)}),_N_E=e.O()}]);