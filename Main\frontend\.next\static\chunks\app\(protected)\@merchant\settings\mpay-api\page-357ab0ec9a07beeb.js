(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[90939],{1990:function(e,t,n){Promise.resolve().then(n.bind(n,26925))},26925:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return L}});var r=n(57437),s=n(25833),a=n(21251),i=n(43949);function o(){let{t:e}=(0,i.$G)(),t=(0,a.T)();return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-base font-medium leading-[22px]",children:e("Check Payment Status")}),(0,r.jsx)("p",{children:e("This operation is used to get the status of a payment request. TrxId is the id received after request to payment has been successful.")})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Request URL")}),(0,r.jsx)("p",{children:(0,r.jsxs)("code",{className:"rounded-[4px] border border-border bg-background px-2 py-1 text-sm",children:["GET ",null==t?void 0:t.apiUrl,"/mapi/payment/:trxId"]})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Headers")}),(0,r.jsx)(s.Z,{className:"border",data:[1],structure:[{id:"name",header:e("Name"),cell:()=>(0,r.jsx)("p",{className:"font-normal",children:e("Authorization")})},{id:"value",header:e("Value"),cell:()=>(0,r.jsx)("p",{className:"font-normal",children:e("Bearer API_KEY (replace API_KEY with your actual API key).")})}]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Responses")}),(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:"200 OK"}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:'{\n  "trxId": string,\n  "type": "payment",\n  "paymentAmount": number,\n  "paymentFee": number,\n  "amountAfterProcessing": number,\n  "status": "pending" | "completed" | "failed",\n  "currency": string,\n  "logo": string,\n  "successUrl": string,\n  "cancelUrl": string,\n  "sandbox": boolean,\n  "custom": object,\n  "createdAt": DateTime,\n  "merchant": {\n    "name": string,\n    "email": string\n  }\n}\n'})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("401 Unauthorized")}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:'{\n  "success": boolean,\n  "message": string\n}\n'})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("500 Internal Server Error")}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:'{\n  "message": "string"\n}\n'})})]})]})}function l(){let{t:e}=(0,i.$G)(),t=(0,a.T)(),n=[{name:"amount",type:"number",mandatory:e("Yes"),description:e("amount to be received")},{name:"currency",type:"string",mandatory:e("Yes"),description:e("a value compatible with {{name}} supported currencies",{name:null==t?void 0:t.siteName})},{name:"logo",type:"string (URL)",mandatory:e("Yes"),description:e("An URL to your logo. It'll be shown on the payment page.")},{name:"callbackUrl",type:"string (URL)",mandatory:e("Yes"),description:e("An URL to receive webhook callback about payment status.")},{name:"successUrl",type:"string (URL)",mandatory:e("Yes"),description:e("An URL to redirect the customer after payment is completed.")},{name:"cancelUrl",type:"string (URL)",mandatory:e("Yes"),description:e("An URL to redirect the customer after payment is cancelled/failed.")},{name:"sandbox",type:"boolean",mandatory:e("Yes"),description:e('Set it to "true" to test payment integration during development phase. Otherwise keep it "false" to take real payments.')},{name:"custom",type:"object",mandatory:e("No"),description:e("A custom object where you can add your custom data like customer email or transaction ID to verify the payment from your side. This field will be sent during webhook callbacks.")},{name:"customerName",type:"string",mandatory:e("Yes"),description:"Customer name for identification purpose."},{name:"customerEmail",type:"string",mandatory:e("Yes"),description:e("Customer email for identification purpose.")},{name:"feeByCustomer",type:"boolean",mandatory:e("Yes"),description:e('If set to "true" the payment will take the fee from the customer directly.')}];return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-base font-medium leading-[22px]",children:e("Create Payment")}),(0,r.jsx)("p",{children:e("This operation is used to take payments directly from your customers in your account.")})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Request URL")}),(0,r.jsx)("p",{children:(0,r.jsxs)("code",{className:"rounded-[4px] border border-border bg-background px-2 py-1 text-sm",children:["POST ",null==t?void 0:t.apiUrl,"/mapi/payment"]})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Headers")}),(0,r.jsx)(s.Z,{className:"border",data:[1],structure:[{id:"name",header:e("Name"),cell:()=>(0,r.jsx)("p",{className:"font-normal",children:e("Authorization")})},{id:"value",header:e("Value"),cell:()=>(0,r.jsx)("p",{className:"font-normal",children:e("Bearer API_KEY (replace API_KEY with your actual API key).")})}]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Body Parameters")}),(0,r.jsx)(s.Z,{className:"border",data:n,structure:[{id:"name",header:e("Name"),cell:e=>{var t;let{row:n}=e;return(0,r.jsx)("p",{className:"font-normal",children:null===(t=n.original)||void 0===t?void 0:t.name})}},{id:"type",header:e("Type"),cell:e=>{var t;let{row:n}=e;return(0,r.jsx)("p",{className:"font-normal",children:null===(t=n.original)||void 0===t?void 0:t.type})}},{id:"mandatory",header:e("Mandatory"),cell:e=>{var t;let{row:n}=e;return(0,r.jsx)("p",{className:"font-normal",children:null===(t=n.original)||void 0===t?void 0:t.mandatory})}},{id:"description",header:e("Description"),cell:e=>{var t;let{row:n}=e;return(0,r.jsx)("p",{className:"font-normal",children:null===(t=n.original)||void 0===t?void 0:t.description})}}]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:[e("Example")," (curl)"]}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:"curl -X POST ".concat(null==t?void 0:t.apiUrl,'/mapi/payment \\\n  -H "Authorization: Bearer API_KEY"\n  -H "Content-type: application/json"\n  -d \'{\n    "amount": 200,\n    "currency": "USD",\n    "amount": 100,\n    "logo": "https://example.com/logo.png",\n    "callbackUrl": "https://example.com/callback",\n    "successUrl": "https://example.com/success",\n    "cancelUrl": "https://example.com/cancel",\n    "sandbox": true,\n    "custom": {\n      "ref": "TRX100"\n    },\n    "customerName": "John Doe",\n    "customerEmail": "<EMAIL>",\n    "feeByCustomer": false\n  }\'')})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Responses")}),(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:"200 OK"}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:'{\n  "success": boolean,\n  "message": string,\n  "redirectUrl": string,\n  "data": object\n}\n'})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("400 Bad Request")}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:'{\n  "success": boolean,\n  "error": object/string\n}\n'})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("500 Internal Server Error")}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:'{\n  "message": "string"\n}\n'})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"mb-2 text-sm font-medium leading-[22px]",children:e("Webhook (POST)")}),(0,r.jsx)("pre",{className:"mb-2 overflow-x-auto rounded-[4px] border border-border bg-background p-4",children:(0,r.jsx)("code",{children:'{\n  "trxId": string,\n  "type": "payment",\n  "paymentAmount": number,\n  "paymentFee": number,\n  "amountAfterProcessing": number,\n  "status": "pending" | "completed" | "failed",\n  "currency": string,\n  "logo": string,\n  "successUrl": string,\n  "cancelUrl": string,\n  "sandbox": boolean,\n  "custom": object,\n  "createdAt": DateTime,\n  "merchant": {\n    "name": string,\n    "email": string\n  }\n}\n'})}),(0,r.jsxs)("p",{children:[e("Redirect your customer to")," [redirectUrl]"]})]})]})}function d(){let{t:e}=(0,i.$G)();return(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"mb-2 text-base font-medium leading-[22px]",children:e("Introduction")}),(0,r.jsx)("p",{children:e("Learn how to integrate the Merchant Payment API to receive payments from your customer in your own platform/system.")})]})}var c=n(6596),u=n(62869),m=n(28152),p=n(83074),h=n(90433),f=n(2265);function x(){let[e,t]=f.useState(!0),[n,s]=f.useState("INTRODUCTION"),{t:a}=(0,i.$G)(),{width:h}=(0,m.B)(),x=[{title:a("Introduction"),key:"INTRODUCTION"},{title:a("Create Payment"),key:"CREATE_PAYMENT"},{title:a("Check Payment Status"),key:"CHECK_PAYMENT_STATUS"}],g={INTRODUCTION:(0,r.jsx)(d,{}),CREATE_PAYMENT:(0,r.jsx)(l,{}),CHECK_PAYMENT_STATUS:(0,r.jsx)(o,{})};return(0,r.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(c.Qd,{value:"API_DOCUMENTATION",className:"border-none px-4 py-0",children:[(0,r.jsx)(c.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("div",{className:"flex items-center gap-1",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:a("API Documentation")})})}),(0,r.jsxs)(c.vF,{className:"flex flex-col gap-4 border-t border-divider px-1 pt-4",children:[(0,r.jsx)("div",{className:"mb-4 flex items-center justify-start xl:hidden",children:(0,r.jsxs)(u.z,{onClick:()=>t(e=>!e),variant:"outline",size:"sm",type:"button",className:"text-sm",children:[(0,r.jsx)(p.Z,{size:"20"}),a("Menu")]})}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(v,{sidebar:e,setSidebar:t,width:h,children:x.map(e=>(0,r.jsx)("button",{"data-active":n===e.key,onClick:()=>{s(e.key),t(!1)},type:"button",className:"flex w-full rounded-lg px-2 py-2 text-left transition-all duration-150 ease-in-out hover:bg-secondary data-[active=true]:bg-secondary",children:e.title},e.key))}),(0,r.jsx)("div",{className:"w-full px-2 py-2 md:w-[75%] md:px-4",children:g[n]})]})]})]})})}function v(e){let{children:t,sidebar:n,setSidebar:s,width:a}=e,{t:o}=(0,i.$G)();return(0,r.jsxs)("div",{"data-expanded":a>=1280||a<1280&&n,className:"absolute inset-y-0 right-0 top-0 z-50 w-full max-w-96 translate-x-full border-r bg-white p-6 pl-0 pt-0 transition-all duration-300 ease-in-out data-[expanded=true]:translate-x-0 xl:relative",children:[(0,r.jsxs)(u.z,{variant:"outline",size:"sm",type:"button",onClick:()=>s(!1),className:"mb-4 gap-[2px] bg-background text-sm hover:bg-background xl:hidden",children:[(0,r.jsx)(h.Z,{size:14}),o("Hide menu")]}),t]})}var g=n(41709),b=n(85487),y=n(65613),j=n(95186),N=n(79981),w=n(97751);async function k(){try{let e=await N.Z.delete("/merchants/delete-api-key");return(0,w.B)(e)}catch(e){return(0,w.D)(e)}}async function A(){try{let e=await N.Z.put("/merchants/generate-api-key");return(0,w.B)(e)}catch(e){return(0,w.D)(e)}}var C=n(94508),P=n(89063),I=n(7949),R=n(43271),S=n(27168),T=n(15641),U=n(14438),E=n(2602);function Z(e){let{data:t}=e,[n,s]=f.useState(!1),[a,o]=f.useState(""),[l,d]=f.useTransition(),{t:m}=(0,i.$G)();f.useEffect(()=>{if(t){var e;o(null!==(e=null==t?void 0:t.apiKey)&&void 0!==e?e:"")}},[t]);let p=async()=>{let e=await k();e&&e.status?((0,E.j)("/merchants/detail"),U.toast.success(e.message)):U.toast.error(m(e.message))};return(0,r.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(c.Qd,{value:"API_KEY",className:"border-none px-4 py-0",children:[(0,r.jsx)(c.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("div",{className:"flex items-center gap-1",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:m("API Key")})})}),(0,r.jsxs)(c.vF,{className:"flex flex-col gap-4 border-t border-divider px-1 pt-4",children:[(0,r.jsxs)(g.J,{condition:!n,children:[(0,r.jsxs)(y.bZ,{className:"border-none bg-transparent shadow-default",children:[(0,r.jsx)(P.Z,{size:"32",variant:"Bulk",color:"#0B6A0B",className:"-mt-1"}),(0,r.jsx)(y.Cd,{className:"ml-2.5 block text-sm font-semibold leading-5",children:m("Introducing MPay API")}),(0,r.jsx)(y.X,{className:"ml-2.5 block text-sm font-normal",children:m("Generate an API Key to start implementing our gateway. See documentation below for more.")})]}),(0,r.jsx)("div",{className:"flex flex-wrap items-center gap-3",children:(0,r.jsxs)(u.z,{type:"button",onClick:()=>s(!0),className:"h-10 gap-2 rounded-lg px-4 py-2 text-base font-medium leading-[22px]",children:[(0,r.jsx)(I.Z,{size:"24"}),m("Show Key")]})})]}),(0,r.jsx)(g.J,{condition:n,children:(0,r.jsxs)("div",{className:"inline-flex items-center gap-3",children:[(0,r.jsx)(j.I,{type:"text",readOnly:!0,value:a,className:"h-10 flex-1"}),(0,r.jsxs)(u.z,{type:"button",onClick:()=>(0,C.Fp)(a),className:"h-10 gap-2 rounded-lg px-4 py-2 text-base font-medium leading-[22px]",children:[(0,r.jsx)(R.Z,{size:"24"}),m("Copy")]}),(0,r.jsxs)(u.z,{variant:"outline",onClick:()=>{d(async()=>{let e=await A();e&&e.status?((0,E.j)("/merchants/detail"),U.toast.success(e.message)):U.toast.error(m(e.message))})},disabled:l,className:"h-10 gap-2 rounded-lg px-4 py-2 text-base font-medium leading-[22px]",children:[(0,r.jsxs)(g.J,{condition:!l,children:[(0,r.jsx)(S.Z,{size:"24"}),m("Re-generate key")]}),(0,r.jsx)(g.J,{condition:l,children:(0,r.jsx)(b.Loader,{title:m("Generating...")})})]}),(0,r.jsxs)(u.z,{variant:"outline",type:"button",onClick:p,className:"h-10 gap-2 rounded-lg px-4 py-2 text-base font-medium leading-[22px] text-foreground",children:[(0,r.jsx)(T.Z,{size:"24"}),m("Delete Key")]})]})})]})]})})}var z=n(1409);function L(){let{t:e}=(0,i.$G)(),{data:t,isLoading:n,error:s}=(0,z.f)();return!t&&s?(0,r.jsx)("div",{className:"w-full bg-danger py-2.5 text-danger-foreground",children:(0,r.jsx)("p",{children:e("We encountered an issue while retrieving the requested data. Please try again later or contact support if the problem persists.")})}):!t&&n?(0,r.jsx)("div",{className:"flex w-full items-center justify-center py-10",children:(0,r.jsx)(b.Loader,{})}):(0,r.jsx)(c.UQ,{type:"multiple",defaultValue:["API_KEY","API_DOCUMENTATION"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)(Z,{data:t}),(0,r.jsx)(x,{})]})})}},41709:function(e,t,n){"use strict";function r(e){let{condition:t,children:n}=e;return t?n:null}n.d(t,{J:function(){return r}}),n(2265)},25833:function(e,t,n){"use strict";n.d(t,{Z:function(){return v}});var r=n(57437),s=n(94508),a=n(71594),i=n(24525),o=n(73490),l=n(36887),d=n(64394),c=n(61756),u=n(99376),m=n(4751),p=n(2265),h=n(43949),f=n(62869),x=n(73578);function v(e){let{data:t,isLoading:n=!1,structure:v,sorting:g,setSorting:b,padding:y=!1,className:j,onRefresh:N,pagination:w}=e,k=(0,p.useMemo)(()=>v,[v]),A=(0,u.useRouter)(),C=(0,u.usePathname)(),P=(0,u.useSearchParams)(),{t:I}=(0,h.$G)(),R=(0,a.b7)({data:t||[],columns:k,state:{sorting:g,onRefresh:N},onSortingChange:b,getCoreRowModel:(0,i.sC)(),getSortedRowModel:(0,i.tj)(),debugTable:!1});return n?(0,r.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,r.jsx)("div",{className:"flex h-32 w-full items-center justify-center",children:I("Loading...")})}):(null==t?void 0:t.length)?(0,r.jsxs)("div",{className:(0,s.ZP)("".concat(y?"p-3":"p-0"," overflow-x-hidden rounded-md bg-background"),j),children:[(0,r.jsxs)(x.iA,{children:[(0,r.jsx)(x.xD,{children:R.getHeaderGroups().map(e=>(0,r.jsx)(x.SC,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>{var t,n,i,o;return(0,r.jsx)(x.ss,{className:(0,s.ZP)("",null==e?void 0:null===(i=e.column)||void 0===i?void 0:null===(n=i.columnDef)||void 0===n?void 0:null===(t=n.meta)||void 0===t?void 0:t.className),children:e.isPlaceholder?null:(0,r.jsxs)(f.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[I((0,a.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(null!==(o=({asc:(0,r.jsx)(l.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:(0,r.jsx)(l.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()])&&void 0!==o?o:(0,r.jsx)(l.Z,{size:"16",className:"text-transparent"}))]})},e.id)})},e.id))}),(0,r.jsx)(x.RM,{children:R.getRowModel().rows.map(e=>(0,r.jsx)(x.SC,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>(0,r.jsx)(x.pj,{className:"py-3 text-sm font-semibold",children:(0,a.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),w&&w.total>10&&(0,r.jsx)("div",{className:"pb-2 pt-6",children:(0,r.jsx)(m.Z,{showTotal:(e,t)=>I("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:null==w?void 0:w.page,total:null==w?void 0:w.total,pageSize:null==w?void 0:w.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(P);t.set("page",e.toString()),A.push("".concat(C,"?").concat(t.toString()))},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>(0,r.jsx)("a",{...e,children:(0,r.jsx)(d.Z,{size:"18"})}),nextIcon:e=>(0,r.jsx)("a",{...e,children:(0,r.jsx)(c.Z,{size:"18"})})})})]}):(0,r.jsx)("div",{className:"rounded-md bg-background p-10",children:(0,r.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[(0,r.jsx)(o.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),I("No data found!")]})})}},80114:function(e,t,n){"use strict";n.d(t,{default:function(){return o}});var r=n(57437),s=n(85487),a=n(94508),i=n(43949);function o(e){let{className:t}=e,{t:n}=(0,i.$G)();return(0,r.jsx)("div",{className:(0,a.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,r.jsx)(s.Loader,{title:n("Loading..."),className:"text-foreground"})})}},85487:function(e,t,n){"use strict";n.d(t,{Loader:function(){return i}});var r=n(57437),s=n(94508),a=n(43949);function i(e){let{title:t="Loading...",className:n}=e,{t:i}=(0,a.$G)();return(0,r.jsxs)("div",{className:(0,s.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-inherit",children:i(t)})]})}},6596:function(e,t,n){"use strict";n.d(t,{Qd:function(){return d},UQ:function(){return l},o4:function(){return c},vF:function(){return u}});var r=n(57437),s=n(13134),a=n(2265),i=n(94508),o=n(36887);let l=s.fC,d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(s.ck,{ref:t,className:(0,i.ZP)("border-b",n),...a})});d.displayName="AccordionItem";let c=a.forwardRef((e,t)=>{let{className:n,children:a,...l}=e;return(0,r.jsx)(s.h4,{className:"flex",children:(0,r.jsxs)(s.xz,{ref:t,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",n),...l,children:[a,(0,r.jsx)(o.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=s.xz.displayName;let u=a.forwardRef((e,t)=>{let{className:n,children:a,...o}=e;return(0,r.jsx)(s.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...o,children:(0,r.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",n),children:a})})});u.displayName=s.VY.displayName},65613:function(e,t,n){"use strict";n.d(t,{Cd:function(){return d},X:function(){return c},bZ:function(){return l}});var r=n(57437),s=n(90535),a=n(2265),i=n(94508);let o=(0,s.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=a.forwardRef((e,t)=>{let{className:n,variant:s,...a}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,i.ZP)(o({variant:s}),n),...a})});l.displayName="Alert";let d=a.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("h5",{ref:t,className:(0,i.ZP)("mb-1 font-medium leading-none tracking-tight",n),...s})});d.displayName="AlertTitle";let c=a.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.ZP)("text-sm [&_p]:leading-relaxed",n),...s})});c.displayName="AlertDescription"},62869:function(e,t,n){"use strict";n.d(t,{d:function(){return l},z:function(){return d}});var r=n(57437),s=n(37053),a=n(90535),i=n(2265),o=n(94508);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef((e,t)=>{let{className:n,variant:a,size:i,asChild:d=!1,...c}=e,u=d?s.g7:"button";return(0,r.jsx)(u,{className:(0,o.ZP)(l({variant:a,size:i,className:n})),ref:t,...c})});d.displayName="Button"},95186:function(e,t,n){"use strict";n.d(t,{I:function(){return i}});var r=n(57437),s=n(2265),a=n(94508);let i=s.forwardRef((e,t)=>{let{className:n,type:s,...i}=e;return(0,r.jsx)("input",{type:s,className:(0,a.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",n),ref:t,...i})});i.displayName="Input"},73578:function(e,t,n){"use strict";n.d(t,{RM:function(){return l},SC:function(){return d},iA:function(){return i},pj:function(){return u},ss:function(){return c},xD:function(){return o}});var r=n(57437),s=n(2265),a=n(94508);let i=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,a.ZP)("w-full caption-bottom text-sm",n),...s})})});i.displayName="Table";let o=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("thead",{ref:t,className:(0,a.ZP)("",n),...s})});o.displayName="TableHeader";let l=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,a.ZP)("[&_tr:last-child]:border-0",n),...s})});l.displayName="TableBody",s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,a.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",n),...s})}).displayName="TableFooter";let d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("tr",{ref:t,className:(0,a.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",n),...s})});d.displayName="TableRow";let c=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("th",{ref:t,className:(0,a.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",n),...s})});c.displayName="TableHead";let u=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("td",{ref:t,className:(0,a.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",n),...s})});u.displayName="TableCell",s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("caption",{ref:t,className:(0,a.ZP)("mt-4 text-sm text-muted-foreground",n),...s})}).displayName="TableCaption"},17062:function(e,t,n){"use strict";n.d(t,{Z:function(){return f},O:function(){return h}});var r=n(57437),s=n(80114);n(83079);var a=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),i=n(31117),o=n(79981),l=n(78040),d=n(83130);class c{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var u=n(99376),m=n(2265);let p=m.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),h=()=>m.useContext(p);function f(e){let{children:t}=e,[n,h]=m.useState("Desktop"),[f,x]=m.useState(!1),[v,g]=m.useState(),{data:b,isLoading:y,error:j,mutate:N}=(0,i.d)("/auth/check",{revalidateOnFocus:!1}),{data:w,isLoading:k}=(0,i.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:A,isLoading:C}=(0,i.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),P=(0,u.useRouter)(),I=(0,u.usePathname)();m.useEffect(()=>{(async()=>{h((await a()).deviceType)})()},[]),m.useEffect(()=>{let e=()=>{let e=window.innerWidth;h(e<768?"Mobile":e<1024?"Tablet":"Desktop"),x(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),m.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");g(new c(e))}catch(e){}})()},[]),m.useLayoutEffect(()=>{j&&!l.sp.includes(I)&&P.push("/signin")},[j]);let R=m.useMemo(()=>{var e,t,r;return{isAuthenticate:!!(null==b?void 0:null===(e=b.data)||void 0===e?void 0:e.login),auth:(null==b?void 0:null===(t=b.data)||void 0===t?void 0:t.user)?new d.n(null==b?void 0:null===(r=b.data)||void 0===r?void 0:r.user):null,isLoading:y,deviceLocation:v,refreshAuth:()=>N(b),isExpanded:f,device:n,setIsExpanded:x,branding:null==w?void 0:w.data,googleAnalytics:(null==A?void 0:A.data)?{active:null==A?void 0:A.data.active,apiKey:null==A?void 0:A.data.apiKey}:{active:!1,apiKey:""}}},[b,v,f,n]),S=!y&&!k&&!C;return(0,r.jsx)(p.Provider,{value:R,children:S?t:(0,r.jsx)(s.default,{})})}},97751:function(e,t,n){"use strict";n.d(t,{B:function(){return s},D:function(){return a}});var r=n(43577);function s(e){var t,n,r;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(r=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==r?r:"",data:null===(n=e.data)||void 0===n?void 0:n.data}}function a(e){let t=500,n="Internal Server Error",s="An unknown error occurred";if((0,r.IZ)(e)){var a,i,o,l,d,c,u,m,p,h,f,x;t=null!==(p=null===(a=e.response)||void 0===a?void 0:a.status)&&void 0!==p?p:500,n=null!==(h=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==h?h:"Internal Server Error",s=null!==(x=null!==(f=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(l=d.messages)||void 0===l?void 0:null===(o=l[0])||void 0===o?void 0:o.message)&&void 0!==f?f:null===(m=e.response)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:u.message)&&void 0!==x?x:e.message}else e instanceof Error&&(s=e.message);return{statusCode:t,statusText:n,status:!1,message:s,data:void 0,error:e}}},21251:function(e,t,n){"use strict";n.d(t,{T:function(){return s}});var r=n(17062);let s=()=>{let{branding:e}=(0,r.O)();return e}},28152:function(e,t,n){"use strict";n.d(t,{B:function(){return s}});var r=n(2265);let s=()=>{let[e,t]=r.useState(0),[n,s]=r.useState(0);function a(){window&&(t(window.innerWidth),s(window.innerHeight))}return r.useEffect(()=>(a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)),[]),{width:e,height:n}}},1409:function(e,t,n){"use strict";n.d(t,{f:function(){return a}});var r=n(79981),s=n(85323);function a(){let{data:e,error:t,isLoading:n}=(0,s.ZP)("/merchants/detail",e=>r.Z.get(e));return{error:t,data:null==e?void 0:e.data,isLoading:n}}},31117:function(e,t,n){"use strict";n.d(t,{d:function(){return a}});var r=n(79981),s=n(85323);let a=(e,t)=>(0,s.ZP)(e||null,e=>r.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,n){"use strict";var r=n(78040),s=n(83464);t.Z=s.default.create({baseURL:r.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,n){"use strict";n.d(t,{rH:function(){return r},sp:function(){return s}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},s=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){"use strict";n.d(t,{F:function(){return c},Fg:function(){return p},Fp:function(){return d},Qp:function(){return m},ZP:function(){return o},fl:function(){return l},qR:function(){return u},w4:function(){return h}});var r=n(78040),s=n(61994),a=n(14438),i=n(53335);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.m6)((0,s.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class c{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let s;let a=void 0===t?this.currencyCode:t;try{s=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(r=null===(n=s.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:a,o=s.format(e),l=o.substring(i.length).trim();return{currencyCode:a,currencySymbol:i,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",p=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",h=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",s=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?s.set(r,e):s.delete(r),s}},74539:function(e,t,n){"use strict";n.d(t,{k:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){"use strict";n.d(t,{n:function(){return l}});class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var s=n(84937);class a{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var i=n(66419),o=n(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new i.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new s.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new a(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new r(e.agent):void 0}}},84937:function(e,t,n){"use strict";n.d(t,{O:function(){return s}});var r=n(74539);class s{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new r.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){"use strict";n.d(t,{u:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,93909,85210,50279,92971,95030,1744],function(){return e(e.s=1990)}),_N_E=e.O()}]);