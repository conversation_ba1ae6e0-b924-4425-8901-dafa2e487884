{"version": 3, "file": "edge-chunks/5089.js", "mappings": "gFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,oHCMe,SAASE,EAAgB,CACtCC,MAAAA,CAAK,CACLC,SAAAA,CAAQ,CACRC,MAAAA,CAAK,CACLC,SAAAA,CAAQ,CAMT,MAGKC,EAFJ,GAAM,CAAEC,KAAAA,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IAGjB,OAAQD,GAAME,KAAKC,IACjB,KAAK,EACHJ,EAAOJ,EACP,KACF,MAAK,EACHI,EAAOH,EACP,KACF,MAAK,EACHG,EAAOD,EACP,KACF,MAAK,EACHC,EAAOF,EACP,KACF,SACEE,EAAO,GAAAK,EAAAC,GAAA,EAACC,EAAAA,OAAYA,CAAAA,CAAAA,EACxB,CAEA,MAAO,GAAAF,EAAAC,GAAA,EAACE,EAAAA,QAAQA,CAAAA,CAACC,SAAU,GAAAJ,EAAAC,GAAA,EAACC,EAAAA,OAAYA,CAAAA,CAAAA,YAAMP,GAChD,uKClCO,eAAeU,IACpB,GAAI,CACF,IAAMC,EAAM,MAAMC,EAAAA,CAAKA,CAACC,IAAI,CAAC,kCAAmC,CAAC,GACjE,MAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBH,EAC3B,CAAE,MAAOI,EAAO,CACd,MAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAuBD,EAChC,CACF,CAGO,eAAeE,EAAqBC,CAAsB,EAC/D,GAAI,CACF,IAAMP,EAAM,MAAMC,EAAAA,CAAKA,CAACC,IAAI,CAC1B,CAAC,eAAe,EAAEK,EAAe,aAAa,CAAC,CAC/C,CAAC,GAEH,MAAOJ,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBH,EAC3B,CAAE,MAAOI,EAAO,CACd,MAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAuBD,EAChC,CACF,iDErBwB,GAAII,WAAAA,CAAQA,CAAC,CACnCC,QAASC,EAAAA,EAAOA,CAACC,OAAO,ECJnB,OAAMC,EAYXC,YAAYC,CAAM,CAAE,CAClB,IAAI,CAACrB,EAAE,CAAGqB,GAAGrB,GACb,IAAI,CAACsB,IAAI,CAAGD,GAAGC,KACf,IAAI,CAACC,KAAK,CAAGF,GAAGE,MAChB,IAAI,CAACC,IAAI,CAAGH,GAAGG,KACf,IAAI,CAACC,MAAM,CAAGC,CAAAA,CAAQL,GAAGI,OACzB,IAAI,CAACE,QAAQ,CAAGD,CAAAA,CAAQL,GAAGM,SAC3B,IAAI,CAACC,QAAQ,CAAGP,GAAGO,SACnB,IAAI,CAACC,MAAM,CAAGR,GAAGQ,OACjB,IAAI,CAACC,SAAS,CAAGT,GAAGS,UACpB,IAAI,CAACC,SAAS,CAAGV,GAAGU,SACtB,CACF,kFEce,SAASC,IACtB,GAAM,CAAEC,OAAAA,CAAM,CAAEC,cAAAA,CAAa,CAAE,CAAGC,WJlClC,GAAM,CACJC,KAAMH,CAAM,CACZI,UAAWC,CAAe,CAC1BC,OAAQL,CAAa,CACtB,CAAGM,CAAAA,EAAAA,EAAAA,CAAAA,EAAO,mCAEX,MAAO,CACLP,OAAQA,GAAQG,KAChBE,gBAAAA,EACAJ,cAAAA,CACF,CACF,IIwBQ,CAAEO,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACR,CAAEC,MAAAA,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IACZ,CAAE/C,KAAAA,CAAI,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IACX,CAAC+C,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAE3B,CAAEX,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEW,KAAAA,CAAI,CAAEC,QAAAA,CAAO,CAAEV,OAAAA,CAAM,CAAE,CAAGW,CAAAA,EAAAA,EAAAA,EAAAA,EACjD,GACS,uBAAuBC,EAAQ,YAA4B,CAEpE,GAAiB3C,EAAAA,CAAKA,CAAC4C,GAAG,CAACC,IAsCvBC,EAAkC,UACtC,IAAM/C,EAAM,MAAMD,IACdC,GAAKgD,QACPhB,EAAOH,GACPF,IACAsB,EAAAA,KAAKA,CAACC,OAAO,CAAClD,EAAImD,OAAO,GAEzBF,EAAAA,KAAKA,CAAC7C,KAAK,CAACJ,EAAImD,OAAO,CAE3B,EAEA,MACE,GAAAC,EAAAC,IAAA,EAACC,EAAAA,EAAMA,CAAAA,CACLhB,KAAMA,EACNiB,aAAchB,EACdiB,UAAWpB,EAAQ,IAAM,SAAW,kBAEpC,GAAAgB,EAAAzD,GAAA,EAAC8D,EAAAA,EAAaA,CAAAA,CAACC,UAAU,yJACvB,GAAAN,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,qBACb,GAAAN,EAAAzD,GAAA,EAACiE,OAAAA,CAAKF,UAAU,0KACb,OAAQG,KAAK,CAACnC,GAAQoC,QAAUC,IAAAA,OAAOrC,GAAQoC,OAE5C,KADApC,GAAQoC,QAGd,GAAAV,EAAAzD,GAAA,EAACqE,EAAAA,CAAgBA,CAAAA,CAACvB,KAAK,YAI3B,GAAAW,EAAAC,IAAA,EAACY,EAAAA,EAAaA,CAAAA,CAACP,UAAU,qLACvB,GAAAN,EAAAzD,GAAA,EAACiE,OAAAA,CAAKF,UAAU,4EAEhB,GAAAN,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,8CACb,GAAAN,EAAAzD,GAAA,EAACuE,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACR1B,KAAK,OACLiB,UAAU,iBACVU,QAAO,YAEP,GAAAhB,EAAAzD,GAAA,EAAC0E,EAAAA,EAAWA,CAAAA,UACV,GAAAjB,EAAAzD,GAAA,EAAC2E,EAAAA,CAAUA,CAAAA,CAAC7B,KAAM,SAGtB,GAAAW,EAAAC,IAAA,EAACkB,EAAAA,EAAYA,CAAAA,CAACb,UAAU,uBACtB,GAAAN,EAAAzD,GAAA,EAAC6E,EAAAA,EAAWA,CAAAA,CAACd,UAAU,4DACpBxB,EAAE,mBAEL,GAAAkB,EAAAzD,GAAA,EAAC8E,EAAAA,EAAiBA,CAAAA,CAACf,UAAU,sEAC1BxB,EACC,2EAKN,GAAAkB,EAAAzD,GAAA,EAACuE,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRO,QAAS3B,EACTW,UAAU,gFAETxB,EAAE,yBAIP,GAAAkB,EAAAzD,GAAA,EAACgE,MAAAA,CACClE,GAAG,mBACHiE,UAAU,oDAEV,GAAAN,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,yCACb,GAAAN,EAAAzD,GAAA,EAACgF,EAAAA,CAAIA,CAAAA,CAACC,UAAW9C,WACf,GAAAsB,EAAAzD,GAAA,EAACgE,MAAAA,CAAID,UAAU,kDACb,GAAAN,EAAAzD,GAAA,EAACkF,EAAAA,MAAMA,CAAAA,CAAAA,OAIX,GAAAzB,EAAAzD,GAAA,EAACgF,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAAC9C,GAAa,CAAC,CAACD,GAAMiD,gBACrC,GAAA1B,EAAAzD,GAAA,EAACoF,EAAAA,CAAcA,CAAAA,CACbC,WAA0BnD,GA3FzBoD,OAAO,CAACC,EAAWC,IACvBD,EAAInB,OAAOoB,EAAEtD,IAAI,EAAEA,MAAMiD,QAAU,GACzC,GA0FSM,KAAM,IAAM1C,EAAQD,EAAO,GAC3B4C,QAAS,CAAC,CAACxD,GAAM,CAACA,EAAKiD,MAAM,CAAG,EAAE,EAAEjD,MAAMyD,MAAMC,YAChDC,OAAQ,GAAApC,EAAAzD,GAAA,EAACkF,EAAAA,MAAMA,CAAAA,CAACnB,UAAU,6BAC1B+B,WACE,GAAArC,EAAAzD,GAAA,EAAC+F,IAAAA,CAAEhC,UAAU,OAAOiC,MAAO,CAAEC,UAAW,QAAS,WAC/C,GAAAxC,EAAAzD,GAAA,EAACkG,IAAAA,UAAG3D,EAAE,eAGV4D,iBAAiB,4BAENjE,GA/FVoD,OAAO,CAACC,EAAOC,IAC1B,GAAOtD,MAAMA,MAAMiD,OACV,IAAII,KAAMC,EAAEtD,IAAI,CAACA,IAAI,CAAC,CAExBqD,EACN,EAAE,GA2FWa,IAAI,GAAgC,IAAInF,EAAaoF,KACrDD,IAAI,GACJ,EAAA1C,IAAA,CAAC4C,EAAAA,QAAc,YACb,EAAAtG,GAAA,CAACuG,EAAAA,CACCrE,KAAMf,EACNkB,OAAQ,IAAMA,EAAOH,GACrBsE,MAAO,IAAM5D,EAAQ,IACrBZ,cAAeA,IAEjB,EAAAhC,GAAA,CAACyG,EAAAA,CAASA,CAAAA,CAAC1C,UAAU,+BAPF5C,EAAErB,EAAE,iBAiB7C,CAEA,SAASyG,EAAqB,CAC5BrE,KAAAA,CAAI,CACJG,OAAAA,CAAM,CACNL,cAAAA,CAAa,CACbwE,MAAAA,CAAK,CAMN,EACC,IAAME,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IAEf,MACE,GAAAlD,EAAAzD,GAAA,EAACgE,MAAAA,CACC4C,YAAW1E,EAAKX,MAAM,CACtB1B,KAAK,eACLkF,QAAS,UACP,GAAI,CAAC7C,EAAKX,MAAM,CAAE,CAChB,IAAMlB,EAAM,MAAMM,EAAqBuB,EAAKpC,EAAE,EAC1CO,GAAKgD,SACPrB,IACAK,IAEJ,CACAqE,EAAOG,IAAI,CAAC,CAAC,CAAC,EAAE3E,GAAMR,SAAS,CAAC,EAChC8E,GACF,EACAzC,UAAU,iKAEV,GAAAN,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,mBACb,GAAAN,EAAAzD,GAAA,EAAC8G,KAAAA,CAAG/C,UAAU,gDAAwC7B,EAAKb,KAAK,GAChE,GAAAoC,EAAAzD,GAAA,EAAC+F,IAAAA,CAAEhC,UAAU,kEACV7B,EAAKZ,IAAI,GAEXY,GAAMN,UACL,GAAA6B,EAAAC,IAAA,EAACqC,IAAAA,CAAEhC,UAAU,sEACVgD,CAAAA,EAAAA,EAAAA,EAAAA,EAAO7E,EAAKN,SAAS,CAAE,WACxB,GAAA6B,EAAAzD,GAAA,EAACiE,OAAAA,CAAKF,UAAU,gDAChB,GAAAN,EAAAzD,GAAA,EAACiE,OAAAA,UAAM8C,CAAAA,EAAAA,EAAAA,EAAAA,EAAO7E,EAAKN,SAAS,CAAE,qBAE9B,SAIZ,gBC7OO,eAAeoF,IAMpB,GAAI,CACF,IAAMC,EAAW,MAAM3G,EAAAA,CAAKA,CAACC,IAAI,CAAC,eAAgB,CAAC,GAKnD,OAJA,MAAM2G,MAAM,oBAAqB,CAC/BC,OAAQ,QACV,GAEO,CACLC,WAAYH,EAAS5D,MAAM,CAC3BgE,WAAYJ,EAASI,UAAU,CAC/BhE,OAAQ4D,MAAAA,EAAS5D,MAAM,CACvBG,QAASyD,EAAS/E,IAAI,EAAEsB,SAAW,EACrC,CACF,CAAE,MAAO/C,EAAO,CACd,IAAI2G,EAAa,IACbC,EAAa,wBACb7D,EAAU,4BAUd,MARI8D,CAAAA,EAAAA,EAAAA,EAAAA,EAAa7G,IACf2G,EAAa3G,EAAMwG,QAAQ,EAAE5D,QAAU,IACvCgE,EAAa5G,EAAMwG,QAAQ,EAAEI,YAAc,wBAC3C7D,EAAU/C,EAAMwG,QAAQ,EAAE/E,MAAMsB,SAAW/C,EAAM+C,OAAO,EAC/C/C,aAAiB8G,OAC1B/D,CAAAA,EAAU/C,EAAM+C,OAAO,EAGlB,CACL4D,WAAAA,EACAC,WAAAA,EACAhE,OAAQ,GACRG,QAAAA,CACF,CACF,CACF,4HCXe,SAASgE,EAAO,CAC7BC,mBAAAA,EAAqB,WAAW,CAGjC,EACC,GAAM,CAAElF,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACRkF,EAAUC,CAAAA,EAAAA,EAAAA,EAAAA,IACVC,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,EAChBH,EAAUA,EAAQI,OAAO,CAAC,QAAS,IAAMvF,EAAE,cAEvCmE,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACT,CAAEhH,KAAAA,CAAI,CAAEoI,YAAAA,CAAW,CAAE5F,UAAW6F,CAAW,CAAE,CAAGpI,CAAAA,EAAAA,EAAAA,CAAAA,IAChD,CAAEqI,cAAAA,CAAa,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,IAGpBC,EAAe,KACnB7E,EAAAA,KAAKA,CAAC8E,OAAO,CAACpB,EAAQ,CACpBqB,QAAS9F,EAAE,cACXgB,QAAS,KACP+E,OAAOC,QAAQ,CAACC,IAAI,CAAG,UACvBT,IACOxF,EAAE,yBAEX9B,MAAO,IACE8B,EAAE,0CAEb,EACF,SAEA,EAAwB,GAAAkB,EAAAzD,GAAA,EAACgE,MAAAA,CAAID,UAAU,uBAGrC,GAAAN,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,gIACb,GAAAN,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,iDACb,GAAAN,EAAAzD,GAAA,EAACuE,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,QACR1B,KAAK,OACLiB,UAAU,YACVgB,QAAS,IAAMkD,EAAc,GAAO,CAAClC,YAErC,GAAAtC,EAAAzD,GAAA,EAACyI,EAAAA,CAAaA,CAAAA,CAAC3F,KAAM,OAEvB,GAAAW,EAAAzD,GAAA,EAAC+F,IAAAA,CAAEhC,UAAU,+DACVxB,EAAEqF,QAIP,GAAAnE,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,oCAEb,GAAAN,EAAAzD,GAAA,EAAC8B,EAAkBA,CAAAA,GAGnB,GAAA2B,EAAAzD,GAAA,EAAC0I,EAAAA,CAAYA,CAAAA,CACXC,iBAAiB,wIACjBC,eAAe,WAIjB,GAAAnF,EAAAC,IAAA,EAACmF,EAAAA,EAAYA,CAAAA,WACX,GAAApF,EAAAC,IAAA,EAACoF,EAAAA,EAAmBA,CAAAA,CAAC/E,UAAU,kIAC7B,GAAAN,EAAAC,IAAA,EAACqF,EAAAA,EAAMA,CAAAA,CAAChF,UAAU,iCAChB,GAAAN,EAAAzD,GAAA,EAACgJ,EAAAA,EAAWA,CAAAA,CAACC,IAAKC,CAAAA,EAAAA,EAAAA,EAAAA,EAASvJ,GAAMJ,UAAU4J,UAC3C,GAAA1F,EAAAzD,GAAA,EAACoJ,EAAAA,EAAcA,CAAAA,UACZC,CAAAA,EAAAA,EAAAA,CAAAA,EAAkB1J,GAAMJ,UAAU+J,WAIvC,GAAA7F,EAAAC,IAAA,EAACM,MAAAA,CAAID,UAAU,8CACb,GAAAN,EAAAzD,GAAA,EAACiE,OAAAA,CAAKF,UAAU,uCACbpE,GAAMJ,UAAU+J,OAEnB,GAAA7F,EAAAzD,GAAA,EAACuJ,EAAAA,CAAUA,CAAAA,CAACzG,KAAK,aAIrB,GAAAW,EAAAC,IAAA,EAAC8F,EAAAA,EAAmBA,CAAAA,CAACzF,UAAU,eAAe0F,MAAM,gBACjD9J,GAAM+J,SAAW,GAChB,GAAAjG,EAAAC,IAAA,EAAAD,EAAAkG,QAAA,YAEE,GAAAlG,EAAAC,IAAA,EAACkG,EAAAA,EAAgBA,CAAAA,CACfC,SAAU,IAAMnD,EAAOG,IAAI,CAACY,GAC5B1D,UAAU,oCAEV,GAAAN,EAAAzD,GAAA,EAAC8J,EAAAA,CAAQA,CAAAA,CAAChH,KAAK,OACdP,EAAE,uBAIL,GAAAkB,EAAAC,IAAA,EAACkG,EAAAA,EAAgBA,CAAAA,CACfC,SAAU,IAAMnD,EAAOG,IAAI,CAAC,qBAC5B9C,UAAU,oCAEV,GAAAN,EAAAzD,GAAA,EAAC+J,EAAAA,CAAeA,CAAAA,CAACjH,KAAK,OACrBP,EAAE,cAGL,GAAAkB,EAAAzD,GAAA,EAACgK,EAAAA,EAAqBA,CAAAA,CAACjG,UAAU,4BAKrC,GAAAN,EAAAC,IAAA,EAACkG,EAAAA,EAAgBA,CAAAA,CACfC,SAAU,IAAM1B,IAChBpE,UAAU,oCAEV,GAAAN,EAAAzD,GAAA,EAACiK,EAAAA,CAAMA,CAAAA,CAACnH,KAAK,OACZP,EAAE,yBAOjB,oHCxIA,IAAMwG,EAASzC,EAAAA,UAAgB,CAG7B,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACoK,EAAAA,EAAoB,EACnBD,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,gEACAtG,GAED,GAAGmG,CAAK,GAGbnB,CAAAA,EAAOuB,WAAW,CAAGF,EAAAA,EAAoB,CAACE,WAAW,CAErD,IAAMtB,EAAc1C,EAAAA,UAAgB,CAGlC,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACoK,EAAAA,EAAqB,EACpBD,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,8BAA+BtG,GAC5C,GAAGmG,CAAK,GAGblB,CAAAA,EAAYsB,WAAW,CAAGF,EAAAA,EAAqB,CAACE,WAAW,CAE3D,IAAMlB,EAAiB9C,EAAAA,UAAgB,CAGrC,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACoK,EAAAA,EAAwB,EACvBD,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,uEACAtG,GAED,GAAGmG,CAAK,GAGbd,CAAAA,EAAekB,WAAW,CAAGF,EAAAA,EAAwB,CAACE,WAAW,sJCxCjE,IAAM3G,EAAS,CAAC,CACd4G,sBAAAA,EAAwB,EAAK,CAC7B,GAAGL,EAC+C,GAClD,GAAAnK,EAAAC,GAAA,EAACwK,EAAAA,EAAeA,CAACC,IAAI,EACnBF,sBAAuBA,EACtB,GAAGL,CAAK,EAGbvG,CAAAA,EAAO2G,WAAW,CAAG,SAErB,IAAMxG,EAAgB0G,EAAAA,EAAeA,CAACE,OAAO,CAEvCC,EAAeH,EAAAA,EAAeA,CAACI,MAAM,CAErClG,EAAc8F,EAAAA,EAAeA,CAACK,KAAK,CAEnCC,EAAgBxE,EAAAA,UAAgB,CAGpC,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACwK,EAAAA,EAAeA,CAACO,OAAO,EACtBZ,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,oCAAqCtG,GAClD,GAAGmG,CAAK,GAGbY,CAAAA,EAAcR,WAAW,CAAGE,EAAAA,EAAeA,CAACO,OAAO,CAACT,WAAW,CAE/D,IAAMhG,EAAgBgC,EAAAA,UAAgB,CAGpC,CAAC,CAAEvC,UAAAA,CAAS,CAAEiH,SAAAA,CAAQ,CAAE,GAAGd,EAAO,CAAEC,IACpC,GAAApK,EAAA2D,IAAA,EAACiH,EAAAA,WACC,GAAA5K,EAAAC,GAAA,EAAC8K,EAAAA,CAAAA,GACD,GAAA/K,EAAAC,GAAA,EAACwK,EAAAA,EAAeA,CAACS,OAAO,EACtBd,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,qGACAtG,GAED,GAAGmG,CAAK,UAERc,OAIP1G,CAAAA,EAAcgG,WAAW,CAAG,gBAE5B,IAAM1F,EAAe,CAAC,CACpBb,UAAAA,CAAS,CACT,GAAGmG,EACkC,GACrC,GAAAnK,EAAAC,GAAA,EAACgE,MAAAA,CACCD,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,4CAA6CtG,GAC1D,GAAGmG,CAAK,EAGbtF,CAAAA,EAAa0F,WAAW,CAAG,eAa3B,IAAMzF,EAAcyB,EAAAA,UAAgB,CAGlC,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACwK,EAAAA,EAAeA,CAACU,KAAK,EACpBf,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,oDACAtG,GAED,GAAGmG,CAAK,GAGbrF,CAAAA,EAAYyF,WAAW,CAAGE,EAAAA,EAAeA,CAACU,KAAK,CAACZ,WAAW,CAE3D,IAAMxF,EAAoBwB,EAAAA,UAAgB,CAGxC,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACwK,EAAAA,EAAeA,CAACW,WAAW,EAC1BhB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,gCAAiCtG,GAC9C,GAAGmG,CAAK,GAGbpF,CAAAA,EAAkBwF,WAAW,CAAGE,EAAAA,EAAeA,CAACW,WAAW,CAACb,WAAW,oKC/FvE,IAAMzB,EAAeuC,EAAAA,EAA0B,CAEzCtC,EAAsBsC,EAAAA,EAA6B,CAE/BA,EAAAA,EAA2B,CAE1BA,EAAAA,EAA4B,CAE/BA,EAAAA,EAAyB,CAElBA,EAAAA,EAAgC,CAsB/DC,EApB+B/E,UAAgB,CAM7C,CAAC,CAAEvC,UAAAA,CAAS,CAAEuH,MAAAA,CAAK,CAAEN,SAAAA,CAAQ,CAAE,GAAGd,EAAO,CAAEC,IAC3C,GAAApK,EAAA2D,IAAA,EAAC0H,EAAAA,EAAgC,EAC/BjB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,4LACAiB,GAAS,OACTvH,GAED,GAAGmG,CAAK,WAERc,EACD,GAAAjL,EAAAC,GAAA,EAACuL,EAAAA,CAAYA,CAAAA,CAACxH,UAAU,wBAGLuG,WAAW,CAChCc,EAAAA,EAAgC,CAACd,WAAW,CAe9CkB,EAb+BlF,UAAgB,CAG7C,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACoL,EAAAA,EAAgC,EAC/BjB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,wbACAtG,GAED,GAAGmG,CAAK,IAGUI,WAAW,CAChCc,EAAAA,EAAgC,CAACd,WAAW,CAE9C,IAAMd,EAAsBlD,EAAAA,UAAgB,CAG1C,CAAC,CAAEvC,UAAAA,CAAS,CAAE0H,WAAAA,EAAa,CAAC,CAAE,GAAGvB,EAAO,CAAEC,IAC1C,GAAApK,EAAAC,GAAA,EAACoL,EAAAA,EAA4B,WAC3B,GAAArL,EAAAC,GAAA,EAACoL,EAAAA,EAA6B,EAC5BjB,IAAKA,EACLsB,WAAYA,EACZ1H,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,ubACAtG,GAED,GAAGmG,CAAK,KAIfV,CAAAA,EAAoBc,WAAW,CAAGc,EAAAA,EAA6B,CAACd,WAAW,CAE3E,IAAMV,EAAmBtD,EAAAA,UAAgB,CAKvC,CAAC,CAAEvC,UAAAA,CAAS,CAAEgB,QAAAA,CAAO,CAAEuG,MAAAA,CAAK,CAAE,GAAGpB,EAAO,CAAEC,IAC1C,GAAApK,EAAAC,GAAA,EAACoL,EAAAA,EAA0B,EACzBjB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,2RACAiB,GAAS,OACTvH,GAED,GAAGmG,CAAK,GAGbN,CAAAA,EAAiBU,WAAW,CAAGc,EAAAA,EAA0B,CAACd,WAAW,CAwBrEoB,EAtBgCpF,UAAgB,CAK9C,CAAC,CAAEvC,UAAAA,CAAS,CAAEgB,QAAAA,CAAO,CAAEuG,MAAAA,CAAK,CAAE,GAAGpB,EAAO,CAAEC,IAC1C,GAAApK,EAAAC,GAAA,EAACoL,EAAAA,EAA0B,EACzBjB,IAAKA,EACLpF,QAAS,IACP4G,EAAEC,cAAc,GACZ7G,GACFA,EAAQ4G,EAEZ,EACA5H,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,yPACAiB,GAAS,OACTvH,GAED,GAAGmG,CAAK,IAGWI,WAAW,CAAGc,EAAAA,EAA0B,CAACd,WAAW,CAuB5EuB,EArBiCvF,UAAgB,CAG/C,CAAC,CAAEvC,UAAAA,CAAS,CAAEiH,SAAAA,CAAQ,CAAEc,QAAAA,CAAO,CAAE,GAAG5B,EAAO,CAAEC,IAC7C,GAAApK,EAAA2D,IAAA,EAAC0H,EAAAA,EAAkC,EACjCjB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,uOACAtG,GAEF+H,QAASA,EACR,GAAG5B,CAAK,WAET,GAAAnK,EAAAC,GAAA,EAACiE,OAAAA,CAAKF,UAAU,wEACd,GAAAhE,EAAAC,GAAA,EAACoL,EAAAA,EAAmC,WAClC,GAAArL,EAAAC,GAAA,EAAC+L,EAAAA,CAAKA,CAAAA,CAAChI,UAAU,gBAGpBiH,MAGoBV,WAAW,CAClCc,EAAAA,EAAkC,CAACd,WAAW,CAsBhD0B,EApB8B1F,UAAgB,CAG5C,CAAC,CAAEvC,UAAAA,CAAS,CAAEiH,SAAAA,CAAQ,CAAE,GAAGd,EAAO,CAAEC,IACpC,GAAApK,EAAA2D,IAAA,EAAC0H,EAAAA,EAA+B,EAC9BjB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,uOACAtG,GAED,GAAGmG,CAAK,WAET,GAAAnK,EAAAC,GAAA,EAACiE,OAAAA,CAAKF,UAAU,wEACd,GAAAhE,EAAAC,GAAA,EAACoL,EAAAA,EAAmC,WAClC,GAAArL,EAAAC,GAAA,EAACiM,EAAAA,CAAMA,CAAAA,CAAClI,UAAU,6BAGrBiH,MAGiBV,WAAW,CAAGc,EAAAA,EAA+B,CAACd,WAAW,CAkB/E4B,EAhB0B5F,UAAgB,CAKxC,CAAC,CAAEvC,UAAAA,CAAS,CAAEuH,MAAAA,CAAK,CAAE,GAAGpB,EAAO,CAAEC,IACjC,GAAApK,EAAAC,GAAA,EAACoL,EAAAA,EAA2B,EAC1BjB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,oCACAiB,GAAS,OACTvH,GAED,GAAGmG,CAAK,IAGKI,WAAW,CAAGc,EAAAA,EAA2B,CAACd,WAAW,CAEvE,IAAMN,EAAwB1D,EAAAA,UAAgB,CAG5C,CAAC,CAAEvC,UAAAA,CAAS,CAAE,GAAGmG,EAAO,CAAEC,IAC1B,GAAApK,EAAAC,GAAA,EAACoL,EAAAA,EAA+B,EAC9BjB,IAAKA,EACLpG,UAAWsG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,2BAA4BtG,GACzC,GAAGmG,CAAK,GAGbF,CAAAA,EAAsBM,WAAW,CAAGc,EAAAA,EAA+B,CAACd,WAAW,uEC9LxE,SAAS9J,EAAkByG,CAAuB,EACvD,MAAO,CACL,GAAGA,EAAS/E,IAAI,CAChBkF,WAAYH,EAAS5D,MAAM,CAC3BgE,WAAYJ,EAASI,UAAU,CAC/BhE,OAAQ4D,MAAAA,EAAS5D,MAAM,EAAY4D,MAAAA,EAAS5D,MAAM,CAClDG,QAASyD,EAAS/E,IAAI,EAAEsB,SAAW,GACnCtB,KAAM+E,EAAS/E,IAAI,EAAEA,IACvB,CACF,CAEO,SAASxB,EAAuBD,CAAU,EAC/C,IAAI2G,EAAa,IACbC,EAAa,wBACb7D,EAAU,4BAYd,MAVI8D,CAAAA,EAAAA,EAAAA,EAAAA,EAAa7G,IACf2G,EAAa3G,EAAMwG,QAAQ,EAAE5D,QAAU,IACvCgE,EAAa5G,EAAMwG,QAAQ,EAAEI,YAAc,wBAC3C7D,EACE/C,EAAMwG,QAAQ,EAAE/E,MAAMiK,UAAU,CAAC,EAAE,EAAE3I,SACrC/C,EAAMwG,QAAQ,EAAE/E,MAAMsB,SACtB/C,EAAM+C,OAAO,EACN/C,aAAiB8G,OAC1B/D,CAAAA,EAAU/C,EAAM+C,OAAO,EAElB,CACL4D,WAAAA,EACAC,WAAAA,EACAhE,OAAQ,GACRG,QAAAA,EACAtB,KAAMkK,KAAAA,EACN3L,MAAAA,CACF,CACF,gECnCO,IAAMyH,EAAS,KACpB,IAAMmE,EAASC,CAAAA,EAAAA,EAAAA,CAAAA,IAEf,MAAO,CACLC,WAAYF,EAAOE,UAAU,CAC7BC,OAAQH,EAAOG,MAAM,CACrBvE,cAAeoE,EAAOpE,aAAa,CAEvC,+BCVO,SAASoB,EAAkBC,CAAY,EAC5C,GAAI,CAACA,EAAM,MAAO,GAElB,IAAMmD,EAAWnD,EAAKoD,KAAK,CAAC,KAe5B,MAAOC,CAZHF,EAAStH,MAAM,CAAG,EAChBsH,CAAQ,CAAC,EAAE,CAACtH,MAAM,CAAG,EACZsH,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAGA,CAAQ,CAACA,EAAStH,MAAM,CAAG,EAAE,CAAC,EAAE,CAEjDsH,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAGA,CAAQ,CAACA,EAAStH,MAAM,CAAG,EAAE,CAAC,EAAE,CAErDsH,IAAAA,EAAStH,MAAM,CACbsH,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAGA,CAAQ,CAAC,EAAE,CAAC,EAAE,CAE/BA,CAAQ,CAAC,EAAE,CAAC,EAAE,EAGXG,WAAW,EAC7B,iSCjBe,SAASC,IACtB,MACE,GAAA9M,EAAAC,GAAA,EAACgE,MAAAA,CAAID,UAAU,kDACb,GAAAhE,EAAAC,GAAA,EAACC,EAAAA,CAAYA,CAAAA,CAAAA,IAGnB", "sources": ["webpack://_N_E/?c062", "webpack://_N_E/./app/(protected)/layout.tsx", "webpack://_N_E/./data/notifications/index.ts", "webpack://_N_E/./hooks/useNotification.tsx", "webpack://_N_E/./lib/tansmit.ts", "webpack://_N_E/./types/notification.ts", "webpack://_N_E/./utils/notificationToast.ts", "webpack://_N_E/./components/common/notifications/NotificationButton.tsx", "webpack://_N_E/./data/auth/logout.ts", "webpack://_N_E/./components/common/Header.tsx", "webpack://_N_E/./components/ui/avatar.tsx", "webpack://_N_E/./components/ui/drawer.tsx", "webpack://_N_E/./components/ui/dropdown-menu.tsx", "webpack://_N_E/./data/response.ts", "webpack://_N_E/./hooks/useApp.tsx", "webpack://_N_E/./utils/getAvatarFallback.ts", "webpack://_N_E/./app/(protected)/loading.tsx"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\");\n", "\"use client\";\r\n\r\nimport GlobalLoader from \"@/components/common/GlobalLoader\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport React, { Suspense } from \"react\";\r\n\r\nexport default function ProtectedLayout({\r\n  admin,\r\n  customer,\r\n  agent,\r\n  merchant,\r\n}: {\r\n  admin: React.ReactNode;\r\n  customer: React.ReactNode;\r\n  agent: React.ReactNode;\r\n  merchant: React.ReactNode;\r\n}) {\r\n  const { auth } = useAuth();\r\n\r\n  let page;\r\n  switch (auth?.role.id) {\r\n    case 1:\r\n      page = admin;\r\n      break;\r\n    case 2:\r\n      page = customer;\r\n      break;\r\n    case 3:\r\n      page = merchant;\r\n      break;\r\n    case 4:\r\n      page = agent;\r\n      break;\r\n    default:\r\n      page = <GlobalLoader />;\r\n  }\r\n\r\n  return <Suspense fallback={<GlobalLoader />}>{page}</Suspense>;\r\n}\r\n", "import axios from \"@/lib/axios\";\r\nimport { ErrorResponseGenerator, ResponseGenerator } from \"@/data/response\";\r\n\r\n// Make all notification as read\r\nexport async function makeAllNotificationRead() {\r\n  try {\r\n    const res = await axios.post(\"/notifications/mark-all-as-read\", {});\r\n    return ResponseGenerator(res);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n}\r\n\r\n// make a notification as read\r\nexport async function makeNotificationRead(notificationId: number) {\r\n  try {\r\n    const res = await axios.post(\r\n      `/notifications/${notificationId}/mark-as-read`,\r\n      {},\r\n    );\r\n    return ResponseGenerator(res);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n}\r\n", "\"use client\";\r\n\r\nimport { useSWR } from \"./useSWR\";\r\n\r\nexport function useNotification() {\r\n  const {\r\n    data: unread,\r\n    isLoading: isLoadingUnread,\r\n    mutate: refreshUnread,\r\n  } = useSWR(\"/notifications/count/unread-all\");\r\n\r\n  return {\r\n    unread: unread?.data,\r\n    isLoadingUnread,\r\n    refreshUnread,\r\n  };\r\n}\r\n", "import { configs } from \"@/lib/configs\";\r\nimport { Transmit } from \"@adonisjs/transmit-client\";\r\n\r\nexport const transmit = new Transmit({\r\n  baseUrl: configs.API_URL as string,\r\n});\r\n", "export class Notification {\r\n  id: number;\r\n  type: string;\r\n  title: string;\r\n  body: string;\r\n  isRead: boolean;\r\n  isSystem: boolean;\r\n  navigate: string;\r\n  userId: number;\r\n  createdAt: Date | null;\r\n  updatedAt: Date | null;\r\n\r\n  constructor(n: any) {\r\n    this.id = n?.id;\r\n    this.type = n?.type;\r\n    this.title = n?.title;\r\n    this.body = n?.body;\r\n    this.isRead = Boolean(n?.isRead);\r\n    this.isSystem = Boolean(n?.isSystem);\r\n    this.navigate = n?.navigate;\r\n    this.userId = n?.userId;\r\n    this.createdAt = n?.createdAt;\r\n    this.updatedAt = n?.updatedAt;\r\n  }\r\n}\r\n", "import { toast } from \"sonner\";\r\n\r\nexport function notificationToast(res: Record<string, string | number>) {\r\n  switch (res.type as string) {\r\n    case \"deposit_failed\":\r\n      return toast.error(res.title, { description: res.body });\r\n    case \"deposit_completed\":\r\n      return toast.success(res.title, { description: res.body });\r\n    case \"new_device_login\":\r\n      return toast.info(res.title, { description: res.body });\r\n    default:\r\n      return toast.message(res.title, { description: res.body });\r\n  }\r\n}\r\n", "\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Drawer,\r\n  DrawerClose,\r\n  DrawerContent,\r\n  DrawerD<PERSON><PERSON>,\r\n  DrawerHeader,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  DrawerTrigger,\r\n} from \"@/components/ui/drawer\";\r\nimport { ArrowLeft2, Notification as NotificationIcon } from \"iconsax-react\";\r\n\r\nimport { Case } from \"@/components/common/Case\";\r\nimport { Loader } from \"@/components/common/Loader\";\r\nimport Separator from \"@/components/ui/separator\";\r\nimport {\r\n  makeAllNotificationRead,\r\n  makeNotificationRead,\r\n} from \"@/data/notifications\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { useDeviceSize } from \"@/hooks/useDeviceSize\";\r\nimport { useNotification } from \"@/hooks/useNotification\";\r\nimport axios from \"@/lib/axios\";\r\nimport { transmit } from \"@/lib/tansmit\";\r\nimport { Notification } from \"@/types/notification\";\r\nimport { notificationToast } from \"@/utils/notificationToast\";\r\nimport { format } from \"date-fns\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport React, { useState } from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport InfiniteScroll from \"react-infinite-scroll-component\";\r\nimport { toast } from \"sonner\";\r\nimport useSWRInfinite from \"swr/infinite\";\r\n\r\nconst PAGE_DATA_LIMIT = 25;\r\n\r\nexport default function NotificationButton() {\r\n  const { unread, refreshUnread } = useNotification();\r\n  const { t } = useTranslation();\r\n  const { width } = useDeviceSize();\r\n  const { auth } = useAuth();\r\n  const [open, setOpen] = useState(false);\r\n\r\n  const { data, isLoading, size, setSize, mutate } = useSWRInfinite(\r\n    (index) => {\r\n      return `/notifications?page=${index + 1}&limit=${PAGE_DATA_LIMIT}`;\r\n    },\r\n    (url: string) => axios.get(url),\r\n  );\r\n\r\n  // create ws instance\r\n  React.useEffect(() => {\r\n    (async () => {\r\n      const subscription = transmit.subscription(`users/${auth?.id}`);\r\n      await subscription.create();\r\n      subscription.onMessage((res: Record<string, string | number>) => {\r\n        refreshUnread();\r\n        notificationToast(res);\r\n      });\r\n\r\n      return () => {\r\n        subscription.delete();\r\n      };\r\n    })();\r\n\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [auth?.id]);\r\n\r\n  const getDateLength = (data: any) => {\r\n    return data?.reduce((a: number, c: any) => {\r\n      return a + Number(c.data?.data?.length ?? 0);\r\n    }, 0);\r\n  };\r\n\r\n  // get flat array\r\n  const flatArray = (data: any) => {\r\n    return data?.reduce((a: [], c: any) => {\r\n      if (c?.data?.data?.length) {\r\n        return [...a, ...c.data.data];\r\n      }\r\n      return a;\r\n    }, []);\r\n  };\r\n\r\n  // handle mark all notification as read\r\n  const handleMarkAllNotificationAsRead = async () => {\r\n    const res = await makeAllNotificationRead();\r\n    if (res?.status) {\r\n      mutate(data);\r\n      refreshUnread();\r\n      toast.success(res.message);\r\n    } else {\r\n      toast.error(res.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Drawer\r\n      open={open}\r\n      onOpenChange={setOpen}\r\n      direction={width < 640 ? \"bottom\" : \"right\"}\r\n    >\r\n      <DrawerTrigger className=\"relative flex h-12 w-12 items-center justify-center gap-2 rounded-2xl bg-secondary transition duration-300 ease-in-out hover:bg-secondary-500\">\r\n        <div className=\"relative\">\r\n          <span className=\"absolute -right-1.5 -top-1 flex h-4 w-4 origin-top-right items-center justify-center rounded-full bg-primary px-1 text-xs text-primary-foreground empty:hidden\">\r\n            {!Number.isNaN(unread?.total) && Number(unread?.total) !== 0\r\n              ? unread?.total\r\n              : null}\r\n          </span>\r\n          <NotificationIcon size=\"24\" />\r\n        </div>\r\n      </DrawerTrigger>\r\n\r\n      <DrawerContent className=\"inset-x-auto bottom-auto left-auto right-0 top-0 m-0 mt-20 flex h-full w-full max-w-[540px] flex-col rounded-t-none bg-background px-0 pt-4 sm:inset-y-0 sm:mt-0 sm:pt-8\">\r\n        <span className=\"mx-auto mb-8 block h-2.5 w-20 rounded-lg bg-divider-secondary sm:hidden\" />\r\n\r\n        <div className=\"flex items-center gap-4 px-6 pb-6\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"hidden sm:flex\"\r\n            asChild\r\n          >\r\n            <DrawerClose>\r\n              <ArrowLeft2 size={16} />\r\n            </DrawerClose>\r\n          </Button>\r\n          <DrawerHeader className=\"flex-1 p-0\">\r\n            <DrawerTitle className=\"text-left text-base font-semibold leading-[22px]\">\r\n              {t(\"Notifications\")}\r\n            </DrawerTitle>\r\n            <DrawerDescription className=\"invisible absolute text-xs font-normal text-secondary-text\">\r\n              {t(\r\n                \"Stay updated with alerts and offers. Customize your notifications.\",\r\n              )}\r\n            </DrawerDescription>\r\n          </DrawerHeader>\r\n\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={handleMarkAllNotificationAsRead}\r\n            className=\"rounded-lg px-4 py-2 text-sm font-medium leading-[22px] sm:text-base\"\r\n          >\r\n            {t(\"Mark all as read\")}\r\n          </Button>\r\n        </div>\r\n\r\n        <div\r\n          id=\"scrollbarTrigger\"\r\n          className=\"flex-1 overflow-y-auto overflow-x-hidden\"\r\n        >\r\n          <div className=\"flex flex-col gap-2 p-6 pt-0\">\r\n            <Case condition={isLoading}>\r\n              <div className=\"flex items-center justify-center py-10\">\r\n                <Loader />\r\n              </div>\r\n            </Case>\r\n\r\n            <Case condition={!isLoading && !!data?.length}>\r\n              <InfiniteScroll\r\n                dataLength={getDateLength(data)}\r\n                next={() => setSize(size + 1)}\r\n                hasMore={!!data?.[data.length - 1]?.data?.meta?.nextPageUrl}\r\n                loader={<Loader className=\"flex justify-center py-4\" />}\r\n                endMessage={\r\n                  <p className=\"py-4\" style={{ textAlign: \"center\" }}>\r\n                    <b>{t(\"No more\")}</b>\r\n                  </p>\r\n                }\r\n                scrollableTarget=\"scrollbarTrigger\"\r\n              >\r\n                {flatArray(data)\r\n                  ?.map((d: Record<string, unknown>) => new Notification(d))\r\n                  ?.map((n: Notification) => (\r\n                    <React.Fragment key={n.id}>\r\n                      <NotificationRenderer\r\n                        data={n}\r\n                        mutate={() => mutate(data)}\r\n                        close={() => setOpen(false)}\r\n                        refreshUnread={refreshUnread}\r\n                      />\r\n                      <Separator className=\"mb-1 mt-[5px] bg-divider\" />\r\n                    </React.Fragment>\r\n                  ))}\r\n              </InfiniteScroll>\r\n            </Case>\r\n          </div>\r\n        </div>\r\n      </DrawerContent>\r\n    </Drawer>\r\n  );\r\n}\r\n\r\nfunction NotificationRenderer({\r\n  data,\r\n  mutate,\r\n  refreshUnread,\r\n  close,\r\n}: {\r\n  data: Notification;\r\n  mutate: () => void;\r\n  refreshUnread: () => void;\r\n  close: () => void;\r\n}) {\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <div\r\n      data-read={data.isRead}\r\n      role=\"presentation\"\r\n      onClick={async () => {\r\n        if (!data.isRead) {\r\n          const res = await makeNotificationRead(data.id);\r\n          if (res?.status) {\r\n            refreshUnread();\r\n            mutate();\r\n          }\r\n        }\r\n        router.push(`/${data?.navigate}`);\r\n        close();\r\n      }}\r\n      className=\"flex w-full cursor-pointer items-center gap-2 border-l-4 border-primary bg-background py-1.5 pl-2 hover:bg-accent data-[read=true]:border-transparent\"\r\n    >\r\n      <div className=\"flex-1\">\r\n        <h6 className=\"mb-1 text-sm font-semibold leading-5\">{data.title}</h6>\r\n        <p className=\"mb-1 p-0 text-xs font-normal leading-4 text-foreground\">\r\n          {data.body}\r\n        </p>\r\n        {data?.createdAt ? (\r\n          <p className=\"flex items-center gap-0.5 p-0 text-xs text-secondary-text\">\r\n            {format(data.createdAt, \"hh:mm a\")}\r\n            <span className=\"block size-1 rounded-full bg-secondary-text\" />\r\n            <span>{format(data.createdAt, \"MMM dd, yyyy\")}</span>\r\n          </p>\r\n        ) : null}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "import axios from \"@/lib/axios\";\r\nimport { isAxiosError } from \"axios\";\r\n\r\nexport async function logout(): Promise<{\r\n  status: boolean;\r\n  statusCode: number;\r\n  statusText: string;\r\n  message: string;\r\n}> {\r\n  try {\r\n    const response = await axios.post(\"/auth/logout\", {});\r\n    await fetch(\"/api/auth/session\", {\r\n      method: \"DELETE\",\r\n    });\r\n\r\n    return {\r\n      statusCode: response.status,\r\n      statusText: response.statusText,\r\n      status: response.status === 200,\r\n      message: response.data?.message ?? \"\",\r\n    };\r\n  } catch (error) {\r\n    let statusCode = 500;\r\n    let statusText = \"Internal Server Error\";\r\n    let message = \"An unknown error occurred\";\r\n\r\n    if (isAxiosError(error)) {\r\n      statusCode = error.response?.status ?? 500;\r\n      statusText = error.response?.statusText ?? \"Internal Server Error\";\r\n      message = error.response?.data?.message ?? error.message;\r\n    } else if (error instanceof Error) {\r\n      message = error.message;\r\n    }\r\n\r\n    return {\r\n      statusCode,\r\n      statusText,\r\n      status: false,\r\n      message,\r\n    };\r\n  }\r\n}\r\n", "\"use client\";\r\n\r\nimport NotificationButton from \"@/components/common/notifications/NotificationButton\";\r\nimport { logout } from \"@/data/auth/logout\";\r\nimport { useApp } from \"@/hooks/useApp\";\r\nimport { imageURL, startCase } from \"@/lib/utils\";\r\nimport {\r\n  ArrowDown2,\r\n  HambergerMenu,\r\n  Logout,\r\n  MessageQuestion,\r\n  Setting2,\r\n} from \"iconsax-react\";\r\nimport { useRouter, useSelectedLayoutSegment } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { LangSwitcher } from \"@/components/common/LangSwitcher\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { getAvatarFallback } from \"@/utils/getAvatarFallback\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { Button } from \"../ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"../ui/dropdown-menu\";\r\n\r\nexport default function Header({\r\n  accountSettingLink = \"/settings\",\r\n}: {\r\n  accountSettingLink?: string;\r\n}) {\r\n  const { t } = useTranslation();\r\n  const segment = useSelectedLayoutSegment();\r\n  const pageTitle = startCase(\r\n    segment ? segment.replace(/[()]/g, \"\") : t(\"Dashboard\"),\r\n  );\r\n  const router = useRouter();\r\n  const { auth, refreshAuth, isLoading: authLoading } = useAuth();\r\n  const { setIsExpanded } = useApp();\r\n\r\n  // handle logout\r\n  const handleLogout = () => {\r\n    toast.promise(logout, {\r\n      loading: t(\"Loading...\"),\r\n      success: () => {\r\n        window.location.href = \"/signin\";\r\n        refreshAuth();\r\n        return t(\"Successfully logout.\");\r\n      },\r\n      error: () => {\r\n        return t(\"Something went wrong. Please try again.\");\r\n      },\r\n    });\r\n  };\r\n\r\n  if (authLoading) return <div className=\"h-20 bg-background\" />;\r\n\r\n  return (\r\n    <div className=\"inline-container flex min-h-[76px] w-full items-center justify-between gap-2 border-b border-black/10 bg-white px-4\">\r\n      <div className=\"flex items-center sm:gap-4 md:gap-10\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"lg:hidden\"\r\n          onClick={() => setIsExpanded((p) => !p)}\r\n        >\r\n          <HambergerMenu size={24} />\r\n        </Button>\r\n        <p className=\"hidden text-lg font-medium text-foreground sm:block\">\r\n          {t(pageTitle)}\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        {/* Notification */}\r\n        <NotificationButton />\r\n\r\n        {/* language */}\r\n        <LangSwitcher\r\n          triggerClassName=\"flex min-w-fit w-fit items-center gap-2 rounded-2xl bg-secondary px-4 py-2 transition duration-300 ease-in-out hover:bg-secondary-500\"\r\n          arrowClassName=\"size-4\"\r\n        />\r\n\r\n        {/* Profile */}\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger className=\"flex items-center gap-2 rounded-2xl bg-secondary px-4 py-2 transition duration-300 ease-in-out hover:bg-secondary-500\">\r\n            <Avatar className=\"h-8 w-8 rounded-full\">\r\n              <AvatarImage src={imageURL(auth?.customer?.avatar)} />\r\n              <AvatarFallback>\r\n                {getAvatarFallback(auth?.customer?.name as string)}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n\r\n            <div className=\"hidden items-center gap-1 md:flex\">\r\n              <span className=\"hidden font-medium md:block\">\r\n                {auth?.customer?.name}\r\n              </span>\r\n              <ArrowDown2 size=\"16\" />\r\n            </div>\r\n          </DropdownMenuTrigger>\r\n\r\n          <DropdownMenuContent className=\"mx-auto py-0\" align=\"end\">\r\n            {auth?.roleId !== 5 && (\r\n              <>\r\n                {/* Account setting */}\r\n                <DropdownMenuItem\r\n                  onSelect={() => router.push(accountSettingLink)}\r\n                  className=\"flex items-center gap-2\"\r\n                >\r\n                  <Setting2 size=\"20\" />\r\n                  {t(\"Account settings\")}\r\n                </DropdownMenuItem>\r\n\r\n                {/* Support */}\r\n                <DropdownMenuItem\r\n                  onSelect={() => router.push(\"/contact-supports\")}\r\n                  className=\"flex items-center gap-2\"\r\n                >\r\n                  <MessageQuestion size=\"20\" />\r\n                  {t(\"Support\")}\r\n                </DropdownMenuItem>\r\n\r\n                <DropdownMenuSeparator className=\"bg-divider-secondary\" />\r\n              </>\r\n            )}\r\n\r\n            {/* Logout  */}\r\n            <DropdownMenuItem\r\n              onSelect={() => handleLogout()}\r\n              className=\"flex items-center gap-2\"\r\n            >\r\n              <Logout size=\"20\" />\r\n              {t(\"Log out\")}\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n", "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Drawer as DrawerPrimitive } from \"vaul\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst Drawer = ({\r\n  shouldScaleBackground = false,\r\n  ...props\r\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (\r\n  <DrawerPrimitive.Root\r\n    shouldScaleBackground={shouldScaleBackground}\r\n    {...props}\r\n  />\r\n);\r\nDrawer.displayName = \"Drawer\";\r\n\r\nconst DrawerTrigger = DrawerPrimitive.Trigger;\r\n\r\nconst DrawerPortal = DrawerPrimitive.Portal;\r\n\r\nconst DrawerClose = DrawerPrimitive.Close;\r\n\r\nconst DrawerOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DrawerPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\"fixed inset-0 z-[999] bg-black/80\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName;\r\n\r\nconst DrawerContent = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DrawerPortal>\r\n    <DrawerOverlay />\r\n    <DrawerPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed inset-x-0 bottom-0 z-[9999] mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </DrawerPrimitive.Content>\r\n  </DrawerPortal>\r\n));\r\nDrawerContent.displayName = \"DrawerContent\";\r\n\r\nconst DrawerHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\"grid gap-1.5 p-4 text-center sm:text-left\", className)}\r\n    {...props}\r\n  />\r\n);\r\nDrawerHeader.displayName = \"DrawerHeader\";\r\n\r\nconst DrawerFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n    {...props}\r\n  />\r\n);\r\nDrawerFooter.displayName = \"DrawerFooter\";\r\n\r\nconst DrawerTitle = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DrawerPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName;\r\n\r\nconst DrawerDescription = React.forwardRef<\r\n  React.ElementRef<typeof DrawerPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DrawerPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Drawer,\r\n  DrawerClose,\r\n  DrawerContent,\r\n  DrawerDescription,\r\n  DrawerFooter,\r\n  DrawerHeader,\r\n  DrawerOverlay,\r\n  DrawerPortal,\r\n  DrawerTitle,\r\n  DrawerTrigger,\r\n};\r\n", "\"use client\";\r\n\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n    noArrow?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"items-left my-1 flex h-8 cursor-pointer select-none rounded-sm px-3 py-1 text-sm font-bold text-secondary-800 outline-none hover:bg-secondary focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 mx-5 min-w-[253px] overflow-hidden rounded-md border bg-white p-1 text-secondary-800 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, onClick, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative my-1 flex h-8 cursor-pointer select-none items-center rounded px-3 py-1 text-sm font-medium text-secondary-800 outline-none transition-colors hover:bg-secondary focus:bg-secondary focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuItemNoClose = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, onClick, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    onClick={(e) => {\r\n      e.preventDefault();\r\n      if (onClick) {\r\n        onClick(e);\r\n      }\r\n    }}\r\n    className={cn(\r\n      \"focus:secondary relative my-1 flex h-8 cursor-pointer select-none items-center rounded px-3 py-1 text-sm outline-none transition-colors hover:bg-secondary focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItemNoClose.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n    {...props}\r\n  />\r\n);\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuItemNoClose,\r\n  DropdownMenuLabel,\r\n  DropdownMenuPortal,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuTrigger,\r\n};\r\n", "import { ReturnType } from \"@/types/return-type\";\r\nimport { AxiosResponse, isAxiosError } from \"axios\";\r\n\r\nexport function ResponseGenerator(response: AxiosResponse): ReturnType {\r\n  return {\r\n    ...response.data,\r\n    statusCode: response.status,\r\n    statusText: response.statusText,\r\n    status: response.status === 200 || response.status === 201,\r\n    message: response.data?.message ?? \"\",\r\n    data: response.data?.data,\r\n  };\r\n}\r\n\r\nexport function ErrorResponseGenerator(error: any): ReturnType {\r\n  let statusCode = 500;\r\n  let statusText = \"Internal Server Error\";\r\n  let message = \"An unknown error occurred\";\r\n\r\n  if (isAxiosError(error)) {\r\n    statusCode = error.response?.status ?? 500;\r\n    statusText = error.response?.statusText ?? \"Internal Server Error\";\r\n    message =\r\n      error.response?.data?.messages?.[0]?.message ??\r\n      error.response?.data?.message ??\r\n      error.message;\r\n  } else if (error instanceof Error) {\r\n    message = error.message;\r\n  }\r\n  return {\r\n    statusCode,\r\n    statusText,\r\n    status: false,\r\n    message,\r\n    data: undefined,\r\n    error,\r\n  };\r\n}\r\n", "import { useGlobal } from \"@/contexts/GlobalProvider\";\r\n\r\nexport const useApp = () => {\r\n  const global = useGlobal();\r\n\r\n  return {\r\n    isExpanded: global.isExpanded,\r\n    device: global.device,\r\n    setIsExpanded: global.setIsExpanded,\r\n  };\r\n};\r\n", "export function getAvatarFallback(name: string): string {\r\n  if (!name) return \"\";\r\n\r\n  const nameList = name.split(\" \");\r\n  let initials = \"\";\r\n\r\n  if (nameList.length > 2) {\r\n    if (nameList[0].length > 3) {\r\n      initials = nameList[0][0] + nameList[nameList.length - 1][0];\r\n    } else {\r\n      initials = nameList[1][0] + nameList[nameList.length - 1][0];\r\n    }\r\n  } else if (nameList.length === 2) {\r\n    initials = nameList[0][0] + nameList[1][0];\r\n  } else {\r\n    initials = nameList[0][0];\r\n  }\r\n\r\n  return initials.toUpperCase();\r\n}\r\n", "import GlobalLoader from \"@/components/common/GlobalLoader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <GlobalLoader />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "ProtectedLayout", "admin", "customer", "agent", "merchant", "page", "auth", "useAuth", "role", "id", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "GlobalLoader", "Suspense", "fallback", "makeAllNotificationRead", "res", "axios", "post", "ResponseGenerator", "error", "ErrorResponseGenerator", "makeNotificationRead", "notificationId", "Transmit", "baseUrl", "configs", "API_URL", "Notification", "constructor", "n", "type", "title", "body", "isRead", "Boolean", "isSystem", "navigate", "userId", "createdAt", "updatedAt", "NotificationButton", "unread", "refreshUnread", "useNotification", "data", "isLoading", "isLoadingUnread", "mutate", "useSWR", "t", "useTranslation", "width", "useDeviceSize", "open", "<PERSON><PERSON><PERSON>", "useState", "size", "setSize", "useSWRInfinite", "index", "get", "url", "handleMarkAllNotificationAsRead", "status", "toast", "success", "message", "jsx_runtime", "jsxs", "Drawer", "onOpenChange", "direction", "Drawer<PERSON><PERSON><PERSON>", "className", "div", "span", "isNaN", "total", "Number", "NotificationIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "<PERSON><PERSON><PERSON><PERSON>", "DrawerClose", "ArrowLeft2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Drawer<PERSON><PERSON><PERSON>", "DrawerDescription", "onClick", "Case", "condition", "Loader", "length", "InfiniteScroll", "dataLength", "reduce", "a", "c", "next", "hasMore", "meta", "nextPageUrl", "loader", "endMessage", "p", "style", "textAlign", "b", "scrollableTarget", "map", "d", "React", "NotificationR<PERSON><PERSON>", "close", "Separator", "router", "useRouter", "data-read", "push", "h6", "format", "logout", "response", "fetch", "method", "statusCode", "statusText", "isAxiosError", "Error", "Header", "accountSettingLink", "segment", "useSelectedLayoutSegment", "pageTitle", "startCase", "replace", "refreshAuth", "authLoading", "setIsExpanded", "useApp", "handleLogout", "promise", "loading", "window", "location", "href", "HambergerMenu", "LangSwitcher", "triggerClassName", "arrowClassName", "DropdownMenu", "DropdownMenuTrigger", "Avatar", "AvatarImage", "src", "imageURL", "avatar", "AvatarFallback", "getAvatar<PERSON><PERSON><PERSON>", "name", "ArrowDown2", "DropdownMenuContent", "align", "roleId", "Fragment", "DropdownMenuItem", "onSelect", "Setting2", "MessageQuestion", "DropdownMenuSeparator", "Logout", "props", "ref", "AvatarPrimitive", "cn", "displayName", "shouldScaleBackground", "DrawerPrimitive", "Root", "<PERSON><PERSON>", "<PERSON>er<PERSON><PERSON><PERSON>", "Portal", "Close", "Drawer<PERSON><PERSON><PERSON>", "Overlay", "children", "Content", "Title", "Description", "DropdownMenuPrimitive", "DropdownMenuSubTrigger", "inset", "ChevronRight", "DropdownMenuSubContent", "sideOffset", "DropdownMenuItemNoClose", "e", "preventDefault", "DropdownMenuCheckboxItem", "checked", "Check", "DropdownMenuRadioItem", "Circle", "DropdownMenuLabel", "messages", "undefined", "global", "useGlobal", "isExpanded", "device", "nameList", "split", "initials", "toUpperCase", "Loading"], "sourceRoot": ""}