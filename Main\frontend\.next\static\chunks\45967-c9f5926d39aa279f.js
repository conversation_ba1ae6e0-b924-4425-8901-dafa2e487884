"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[45967],{41709:function(e,t,r){function s(e){let{condition:t,children:r}=e;return t?r:null}r.d(t,{J:function(){return s}}),r(2265)},52323:function(e,t,r){r.d(t,{g:function(){return x}});var s=r(57437),n=r(2265),a=r(85487),l=r(41062),i=r(23518),o=r(57054),d=r(40593),c=r(94508),u=r(36887),m=r(43949);function x(e){var t,r;let{allCountry:x=!1,defaultValue:f,defaultCountry:p,onSelectChange:h,disabled:v=!1,triggerClassName:g,arrowClassName:j,flagClassName:b,display:y,placeholderClassName:N,align:w="start",side:C="bottom"}=e,{t:z}=(0,m.$G)(),{countries:P,getCountryByCode:I,isLoading:Z}=(0,d.F)(),[S,F]=n.useState(!1),[_,k]=n.useState(f);return n.useEffect(()=>{f&&k(f)},[f]),n.useEffect(()=>{(async()=>{p&&await I(p,e=>{e&&(k(e),h(e))})})()},[p]),(0,s.jsxs)(o.J2,{open:S,onOpenChange:F,children:[(0,s.jsxs)(o.xo,{disabled:v,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",g),children:[_?(0,s.jsx)("div",{className:"flex flex-1 items-center",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,s.jsx)(l.W,{className:b,countryCode:(null===(t=_.code)||void 0===t?void 0:t.cca2)==="*"?"UN":null===(r=_.code)||void 0===r?void 0:r.cca2}),void 0!==y?y(_):(0,s.jsx)("span",{children:_.name})]})}):(0,s.jsx)("span",{className:(0,c.ZP)("text-placeholder",N),children:z("Select country")}),(0,s.jsx)(u.Z,{className:(0,c.ZP)("size-6",j)})]}),(0,s.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:w,side:C,children:(0,s.jsxs)(i.mY,{children:[(0,s.jsx)(i.sZ,{placeholder:z("Search...")}),(0,s.jsx)(i.e8,{children:(0,s.jsxs)(i.fu,{children:[Z&&(0,s.jsx)(a.Loader,{}),x&&(0,s.jsxs)(i.di,{value:z("All countries"),onSelect:()=>{k({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),h({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),F(!1)},children:[(0,s.jsx)(l.W,{countryCode:"UN"}),(0,s.jsx)("span",{className:"pl-1.5",children:z("All countries")})]}),null==P?void 0:P.map(e=>"officially-assigned"===e.status?(0,s.jsxs)(i.di,{value:e.name,onSelect:()=>{k(e),h(e),F(!1)},children:[(0,s.jsx)(l.W,{countryCode:e.code.cca2}),(0,s.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},39785:function(e,t,r){r.d(t,{M:function(){return u}});var s=r(57437),n=r(1098),a=r(57054),l=r(94508),i=r(2901),o=r(76534),d=r(2265),c=r(43949);let u=d.forwardRef((e,t)=>{let{value:r,onChange:u,className:m,placeholderClassName:x,options:f}=e,{t:p}=(0,c.$G)(),[h,v]=d.useState(!1);return(0,s.jsxs)(a.J2,{open:h,onOpenChange:v,children:[(0,s.jsxs)(a.xo,{disabled:!!(null==f?void 0:f.disabled),className:(0,l.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",m),children:[(0,s.jsx)("div",{ref:t,className:"flex flex-1 items-center",children:(0,s.jsx)("div",{className:"flex flex-1 items-center gap-2 text-left",children:r?(0,i.WU)(r,"dd/MM/yyyy"):(0,s.jsx)("span",{className:(0,l.ZP)("text-placeholder",x),children:p("Pick a Date")})})}),(0,s.jsx)(o.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),(0,s.jsx)(a.yk,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(n.f,{...f,mode:"single",initialFocus:!0,selected:null!=r?r:void 0,onSelect:e=>{u(e),v(!1)}})})]})})},78939:function(e,t,r){r.d(t,{S:function(){return o}});var s=r(57437),n=r(94508),a=r(33145),l=r(2265),i=r(85598);function o(e){let{defaultValue:t,onChange:r,className:o,children:d,disabled:c=!1,id:u}=e,[m,x]=l.useState(t);l.useEffect(()=>{x(t)},[t]);let{getRootProps:f,getInputProps:p}=(0,i.uI)({onDrop:e=>{let t=null==e?void 0:e[0];t&&(r(t),x(URL.createObjectURL(t)))},disabled:c});return(0,s.jsxs)("div",{...f({className:(0,n.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o)}),children:[!!m&&(0,s.jsx)(a.default,{src:m,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,s.jsx)("input",{id:u,...p()}),!m&&(0,s.jsx)("div",{children:d})]})}},18629:function(e,t,r){r.d(t,{E:function(){return z}});var s=r(57437),n=r(85487),a=r(41062),l=r(23518),i=r(95186),o=r(57054),d=r(40593),c=r(94508),u=r(95550),m=r(36887),x=r(58414),f=r(78286),p=r(19368),h=r(68953),v=r(56555),g=r(5874),j=r(19615),b=r(93781),y=r(83057),N=r(43949),w=r(2265);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function z(e){let{value:t,defaultValue:r="",onChange:n,onBlur:a,disabled:l,inputClassName:o,options:d}=e,[u,m]=(0,w.useState)(null!=r?r:""),[b,N]=(0,w.useState)(""),[z,I]=(0,w.useState)(null==d?void 0:d.initialCountry),Z=e=>{if(e)try{let t=x.S(e,z);t?(I(t.country),N("+".concat(t.countryCallingCode)),m(t.formatNational())):m(e)}catch(t){m(e)}else m(e)};(0,w.useEffect)(()=>{t&&Z(t)},[t]);let S=f.L(z||(null==d?void 0:d.initialCountry)||"US",y.Z);return(0,s.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(P,{country:z,disabled:l,initialCountry:null==d?void 0:d.initialCountry,onSelect:e=>{var t;let r=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase(),s=p.G(r);N("+".concat(s)),I(r)}}),(0,s.jsx)("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:b||"+".concat(null==S?void 0:S.countryCallingCode)})]}),(0,s.jsx)(i.I,{type:"tel",className:(0,c.ZP)("rounded-l-none pl-2",o),value:u,onChange:e=>{let{value:t}=e.target,r=x.S(t,z);null==a||a(""),r&&h.t(t,z)&&v.q(t,z)?(I(r.country),N("+".concat(r.countryCallingCode)),null==n||n(r.number),m(t)):(r?m(r.nationalNumber):m(t),null==n||n(t))},onPaste:e=>{let t=e.clipboardData.getData("Text"),r=x.S(t);if(r&&h.t(t))Z(r.formatNational()),I(r.country),N("+".concat(r.countryCallingCode)),null==n||n(r.number),null==a||a("");else{let e=x.S(t,z);e&&h.t(t,z)&&(Z(e.formatNational()),null==n||n(e.number),null==a||a(""))}},onBlur:()=>{if(u&&!g.y(u,z)){let e=j.d(u,z);e&&(null==a||a(C[e]))}},placeholder:null==S?void 0:S.formatNational(),disabled:l})]})}function P(e){let{initialCountry:t,country:r,onSelect:n,disabled:l}=e,[i,d]=(0,w.useState)(!1);return(0,s.jsxs)(o.J2,{open:i,onOpenChange:d,children:[(0,s.jsxs)(o.xo,{disabled:l,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[t||r?(0,s.jsx)(a.W,{countryCode:r||t,className:"aspect-auto h-[18px] w-7 flex-1"}):(0,s.jsx)(u.Z,{}),(0,s.jsx)(m.Z,{variant:"Bold",size:16})]}),(0,s.jsx)(o.yk,{align:"start",className:"h-fit p-0",children:(0,s.jsx)(I,{defaultValue:r||t,onSelect:e=>{n(e),d(!1)}})})]})}function I(e){var t;let{defaultValue:r,onSelect:i}=e,{countries:o,isLoading:c}=(0,d.F)(),{t:u}=(0,N.$G)();return(0,s.jsxs)(l.mY,{children:[(0,s.jsx)(l.sZ,{placeholder:u("Search country by name"),className:"placeholder:text-input-placeholder"}),(0,s.jsx)(l.e8,{children:(0,s.jsx)(l.fu,{children:c?(0,s.jsx)(l.di,{children:(0,s.jsx)(n.Loader,{})}):null===(t=o.filter(e=>{var t;let r=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase();return b.o().includes(r)}))||void 0===t?void 0:t.map(e=>(0,s.jsxs)(l.di,{value:e.name,"data-active":e.code.cca2===r,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>i(e),children:[(0,s.jsx)(a.W,{countryCode:e.code.cca2}),e.name]},e.code.ccn3))})})]})}},56353:function(e,t,r){r.d(t,{W:function(){return c}});var s=r(57437),n=r(2265),a=r(62869),l=r(95186),i=r(94508),o=r(93824),d=r(32706);let c=n.forwardRef((e,t)=>{let{className:r,type:c,...u}=e,[m,x]=n.useState(!1);return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.I,{type:m?"text":"password",className:(0,i.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...u}),(0,s.jsx)(a.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),x(e=>!e)},children:m?(0,s.jsx)(o.Z,{}):(0,s.jsx)(d.Z,{})})]})});c.displayName="PasswordInput"},25429:function(e,t,r){r.d(t,{X:function(){return a}});var s=r(57437),n=r(94508);function a(e){let{className:t}=e;return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,n.ZP)("fill-primary",t),children:[(0,s.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},45702:function(e,t,r){r.d(t,{h:function(){return C}});var s=r(57437),n=r(41709),a=r(52323),l=r(85487),i=r(6596),o=r(62869),d=r(15681),c=r(95186),u=r(26815),m=r(79981),x=r(43577);async function f(e){var t,r,s,n,a,l,i,o,d;try{let s=await m.Z.put("/customers/update-address",{addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city});return{statusCode:s.status,statusText:s.statusText,status:200===s.status||201===s.status,message:null!==(r=null===(t=s.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(c){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,x.IZ)(c)?(e=null!==(i=null===(s=c.response)||void 0===s?void 0:s.status)&&void 0!==i?i:500,t=null!==(o=null===(n=c.response)||void 0===n?void 0:n.statusText)&&void 0!==o?o:"Internal Server Error",r=null!==(d=null===(l=c.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)&&void 0!==d?d:c.message):c instanceof Error&&(r=c.message),{statusCode:e,statusText:t,status:!1,message:r}}}var p=r(40593),h=r(13590),v=r(22291),g=r(2265),j=r(29501),b=r(43949),y=r(14438),N=r(31229);let w=N.z.object({street:N.z.string({required_error:"Street is required."}),country:N.z.string({required_error:"Country is required."}),city:N.z.string({required_error:"city is required."}),zipCode:N.z.string({required_error:"Zip code is required."})});function C(e){let{address:t}=e,[r,m]=(0,g.useTransition)(),[x,N]=g.useState(null),{getCountryByCode:C}=(0,p.F)(),{t:z}=(0,b.$G)(),P=(0,j.cI)({resolver:(0,h.F)(w),defaultValues:{street:"",city:"",country:"",zipCode:""}});return g.useEffect(()=>{if(t){var e;C(null===(e=t.countryCode)||void 0===e?void 0:e.toLowerCase(),N),P.reset({street:t.addressLine,city:t.city,country:t.countryCode,zipCode:t.zipCode})}},[t]),(0,s.jsx)(d.l0,{...P,children:(0,s.jsx)("form",{onSubmit:P.handleSubmit(e=>{m(async()=>{let t=await f(e);t&&t.status?y.toast.success(t.message):y.toast.error(z(t.message))})}),className:"rounded-xl border border-border bg-background",children:(0,s.jsxs)(i.Qd,{value:"ADDRESS_INFORMATION",className:"border-none px-4 py-0",children:[(0,s.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:z("Address")})}),(0,s.jsxs)(i.vF,{className:"flex flex-col gap-2 border-t border-divider px-1 pt-4",children:[(0,s.jsx)(u.Z,{children:z("Full mailing address")}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-2.5",children:[(0,s.jsx)(d.Wi,{control:P.control,name:"street",render:e=>{let{field:t}=e;return(0,s.jsxs)(d.xJ,{className:"col-span-12",children:[(0,s.jsx)(d.NI,{children:(0,s.jsx)(c.I,{type:"text",placeholder:z("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,s.jsx)(d.zG,{})]})}}),(0,s.jsx)(d.Wi,{control:P.control,name:"country",render:e=>{let{field:t}=e;return(0,s.jsxs)(d.xJ,{className:"col-span-12",children:[(0,s.jsx)(d.NI,{children:(0,s.jsx)(a.g,{defaultValue:x,onSelectChange:e=>t.onChange(e.code.cca2)})}),(0,s.jsx)(d.zG,{})]})}}),(0,s.jsx)(d.Wi,{control:P.control,name:"city",render:e=>{let{field:t}=e;return(0,s.jsxs)(d.xJ,{className:"col-span-12 md:col-span-6",children:[(0,s.jsx)(d.NI,{children:(0,s.jsx)("div",{className:"relative flex items-center gap-2.5",children:(0,s.jsx)(c.I,{type:"text",placeholder:z("City name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})})}),(0,s.jsx)(d.zG,{})]})}}),(0,s.jsx)(d.Wi,{control:P.control,name:"zipCode",render:e=>{let{field:t}=e;return(0,s.jsxs)(d.xJ,{className:"col-span-12 md:col-span-6",children:[(0,s.jsx)(d.NI,{children:(0,s.jsx)(c.I,{type:"text",placeholder:z("Zip code"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,s.jsx)(d.zG,{})]})}})]}),(0,s.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,s.jsxs)(o.z,{disabled:r,children:[(0,s.jsxs)(n.J,{condition:!r,children:[z("Save"),(0,s.jsx)(v.Z,{size:20})]}),(0,s.jsx)(n.J,{condition:r,children:(0,s.jsx)(l.Loader,{title:z("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}},68332:function(e,t,r){r.d(t,{G:function(){return z}});var s=r(57437),n=r(41709),a=r(56353),l=r(6596),i=r(62869),o=r(15681),d=r(95186),c=r(13590),u=r(2265),m=r(29501),x=r(31229),f=r(85487),p=r(65613),h=r(79981),v=r(43577);async function g(e){var t,r,s,n,a,l,i,o,d;try{let s=await h.Z.post("/auth/change-password",{currentPassword:null==e?void 0:e.currentPassword,newPassword:null==e?void 0:e.newPassword,newPasswordConfirmation:null==e?void 0:e.confirmPassword});return{statusCode:s.status,statusText:s.statusText,status:200===s.status,message:null!==(r=null===(t=s.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(c){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,v.IZ)(c)?(e=null!==(i=null===(s=c.response)||void 0===s?void 0:s.status)&&void 0!==i?i:500,t=null!==(o=null===(n=c.response)||void 0===n?void 0:n.statusText)&&void 0!==o?o:"Internal Server Error",r=null!==(d=null===(l=c.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)&&void 0!==d?d:c.message):c instanceof Error&&(r=c.message),{statusCode:e,statusText:t,status:!1,message:r}}}var j=r(73376),b=r(22291),y=r(90433),N=r(43949),w=r(14438);let C=x.z.object({currentPassword:x.z.string({required_error:"Current password is required."}),newPassword:x.z.string({required_error:"Choose a new password"}),confirmPassword:x.z.string({required_error:"Confirm password is required."})}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}).refine(e=>e.newPassword!==e.currentPassword,{message:"New Password must be different",path:["newPassword"]});function z(){let{t:e}=(0,N.$G)(),[t,r]=u.useState(!1),[x,h]=(0,u.useTransition)(),v=(0,m.cI)({resolver:(0,c.F)(C),defaultValues:{currentPassword:"",newPassword:"",confirmPassword:""}});return(0,s.jsx)(o.l0,{...v,children:(0,s.jsx)("form",{onSubmit:v.handleSubmit(t=>{h(async()=>{let r=await g(t);r&&r.status?w.toast.success(e("Password successfully updated")):w.toast.error(e(r.message))})}),className:"rounded-xl border border-border bg-background",children:(0,s.jsxs)(l.Qd,{value:"PASSWORD_INFORMATION",className:"border-none px-4 py-0",children:[(0,s.jsx)(l.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:e("Privacy & Security")})}),(0,s.jsxs)(l.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[(0,s.jsxs)(p.bZ,{className:"border-none bg-transparent shadow-default",children:[(0,s.jsx)(j.Z,{color:"#0B6A0B",variant:"Bulk"}),(0,s.jsx)(p.Cd,{className:"pl-2 text-sm font-semibold leading-5",children:e("Two-factor authentication is on.")}),(0,s.jsx)(p.X,{className:"pl-2 text-sm font-normal",children:e("To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.")})]}),(0,s.jsx)(n.J,{condition:!t,children:(0,s.jsx)(o.Wi,{control:v.control,name:"currentPassword",render:()=>(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:e("Password")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(d.I,{type:"password",defaultValue:"akdjfkjsdfjkdsf",readOnly:!0,disabled:!t,placeholder:e("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-80"})}),(0,s.jsx)(o.zG,{})]})})}),(0,s.jsxs)(n.J,{condition:t,children:[(0,s.jsx)(o.Wi,{control:v.control,name:"currentPassword",render:r=>{let{field:n}=r;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:e("Current password")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(a.W,{placeholder:e("Enter current password"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",disabled:!t,...n})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:v.control,name:"newPassword",render:r=>{let{field:n}=r;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:e("New Password")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(a.W,{...n,disabled:!t,className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",placeholder:e("Create a strong password")})}),(0,s.jsx)(o.zG,{})]})}}),(0,s.jsx)(o.Wi,{control:v.control,name:"confirmPassword",render:r=>{let{field:n}=r;return(0,s.jsxs)(o.xJ,{children:[(0,s.jsx)(o.lX,{children:e("Confirm New Password")}),(0,s.jsx)(o.NI,{children:(0,s.jsx)(a.W,{...n,disabled:!t,className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",placeholder:e("Enter the password again")})}),(0,s.jsx)(o.zG,{})]})}})]}),(0,s.jsxs)("div",{className:"flex flex-row items-center justify-end gap-4",children:[(0,s.jsx)(n.J,{condition:!t,children:(0,s.jsxs)(i.z,{type:"button",onClick:()=>r(!0),children:[e("Edit Password"),(0,s.jsx)(b.Z,{size:20})]})}),(0,s.jsx)(n.J,{condition:t,children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.z,{type:"button",variant:"outline",onClick:()=>r(!1),children:[(0,s.jsx)(y.Z,{size:20}),e("Cancel")]}),(0,s.jsxs)(i.z,{type:"submit",disabled:x,children:[(0,s.jsxs)(n.J,{condition:!x,children:[e("Save"),(0,s.jsx)(b.Z,{size:20})]}),(0,s.jsx)(n.J,{condition:x,children:(0,s.jsx)(f.Loader,{title:e("Processing..."),className:"text-primary-foreground"})})]})]})})]})]})]})})})}},61149:function(e,t,r){r.d(t,{O:function(){return F}});var s=r(57437),n=r(41709),a=r(39785),l=r(78939),i=r(18629),o=r(85487),d=r(25429),c=r(6596),u=r(62869),m=r(15681),x=r(95186),f=r(26815),p=r(74991),h=r(79981),v=r(43577),g=r(2901);async function j(e){var t,r,s,n,a,l,i,o,d;try{let s=new FormData;s.append("firstName",e.firstName),s.append("lastName",e.lastName),s.append("phone",e.phone),s.append("gender",e.gender),s.append("dob",(0,g.WU)(e.dateOfBirth,"yyyy-MM-dd")),s.append("profileImage",e.profileImage);let n=await h.Z.put("/customers/update",s,{headers:{"Content-Type":"multipart/form-data"}});return{statusCode:n.status,statusText:n.statusText,status:200===n.status||201===n.status,message:null!==(r=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(c){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,v.IZ)(c)?(e=null!==(i=null===(s=c.response)||void 0===s?void 0:s.status)&&void 0!==i?i:500,t=null!==(o=null===(n=c.response)||void 0===n?void 0:n.statusText)&&void 0!==o?o:"Internal Server Error",r=null!==(d=null===(l=c.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message)&&void 0!==d?d:c.message):c instanceof Error&&(r=c.message),{statusCode:e,statusText:t,status:!1,message:r}}}var b=r(94508),y=r(54995),N=r(13590),w=r(22291),C=r(2265),z=r(29501),P=r(43949),I=r(14438),Z=r(31229);let S=Z.z.object({profileImage:y.K,firstName:Z.z.string({required_error:"First name is required."}),lastName:Z.z.string({required_error:"Last name is required."}),email:Z.z.string({required_error:"Email is required."}),phone:Z.z.string({required_error:"Phone is required."}),dateOfBirth:Z.z.date({required_error:"Date of Birth is required."}),gender:Z.z.string({required_error:"Sex is required"})});function F(e){let{user:t,isLoading:r}=e,[h,v]=(0,C.useTransition)(),{t:g}=(0,P.$G)(),y=(0,z.cI)({resolver:(0,N.F)(S),defaultValues:{profileImage:"",firstName:"",lastName:"",email:"",phone:"",dateOfBirth:void 0,gender:""}}),Z=(0,C.useMemo)(()=>t,[t]),F=C.useCallback(()=>{Z&&y.reset({firstName:(null==Z?void 0:Z.firstName)||"",lastName:(null==Z?void 0:Z.lastName)||"",email:(null==Z?void 0:Z.email)||"",phone:(null==Z?void 0:Z.phone)||"",dateOfBirth:(null==Z?void 0:Z.dateOfBirth)||void 0,gender:(null==Z?void 0:Z.gender)||""})},[r]);return C.useEffect(()=>{F()},[F]),(0,s.jsx)(m.l0,{...y,children:(0,s.jsx)("form",{onSubmit:y.handleSubmit(e=>{v(async()=>{var t;let r=await j({...e,gender:null===(t=e.gender)||void 0===t?void 0:t.toLowerCase()});r&&r.status?I.toast.success(r.message):I.toast.error(g(r.message))})}),className:"rounded-xl border border-border bg-background",children:(0,s.jsxs)(c.Qd,{value:"PROFILE_INFORMATION",className:"border-none px-4 py-0",children:[(0,s.jsx)(c.o4,{className:"py-6 hover:no-underline",children:(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:g("Profile")})}),(0,s.jsxs)(c.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 py-4",children:[(0,s.jsx)(m.Wi,{control:y.control,name:"profileImage",render:e=>{let{field:r}=e;return(0,s.jsxs)(m.xJ,{children:[(0,s.jsx)(m.lX,{children:g("Profile picture")}),(0,s.jsx)(m.NI,{children:(0,s.jsx)(l.S,{id:"profileImage",defaultValue:(0,b.qR)(null==t?void 0:t.avatar),onChange:e=>{r.onChange(e)},className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,s.jsx)(d.X,{}),(0,s.jsx)("p",{className:"text-sm font-normal text-primary",children:g("Upload photo")})]})})}),(0,s.jsx)(m.zG,{})]})}}),(0,s.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,s.jsx)(m.Wi,{control:y.control,name:"firstName",render:e=>{let{field:t}=e;return(0,s.jsxs)(m.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(m.lX,{children:g("First name")}),(0,s.jsx)(m.NI,{children:(0,s.jsx)(x.I,{type:"text",placeholder:g("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,s.jsx)(m.zG,{})]})}}),(0,s.jsx)(m.Wi,{control:y.control,name:"lastName",render:e=>{let{field:t}=e;return(0,s.jsxs)(m.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,s.jsx)(m.lX,{children:g("Last name")}),(0,s.jsx)(m.NI,{children:(0,s.jsx)(x.I,{type:"text",placeholder:g("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",...t})}),(0,s.jsx)(m.zG,{})]})}})]}),(0,s.jsx)(m.Wi,{control:y.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsxs)(m.xJ,{children:[(0,s.jsx)(m.lX,{children:g("Email")}),(0,s.jsx)(m.NI,{children:(0,s.jsx)(x.I,{type:"email",disabled:!0,value:t.value,placeholder:g("Enter your email"),className:"text-base font-normal disabled:cursor-auto disabled:bg-input disabled:opacity-100",readOnly:!0})}),(0,s.jsx)(m.zG,{})]})}}),(0,s.jsx)(m.Wi,{control:y.control,name:"phone",render:e=>{let{field:t}=e;return(0,s.jsxs)(m.xJ,{children:[(0,s.jsx)(m.lX,{children:g("Phone")}),(0,s.jsx)(m.NI,{children:(0,s.jsx)(i.E,{value:t.value,onChange:e=>t.onChange(e),onBlur:e=>{e?y.setError("phone",{type:"custom",message:g(e)}):y.clearErrors("phone")}})}),(0,s.jsx)(m.zG,{})]})}}),(0,s.jsx)(m.Wi,{control:y.control,name:"dateOfBirth",render:e=>{let{field:t}=e;return(0,s.jsxs)(m.xJ,{children:[(0,s.jsx)(m.lX,{children:g("Date of birth")}),(0,s.jsx)(m.NI,{children:(0,s.jsx)(a.M,{...t})}),(0,s.jsx)(m.zG,{})]})}}),(0,s.jsx)(m.Wi,{control:y.control,name:"gender",render:e=>{let{field:t}=e;return(0,s.jsxs)(m.xJ,{children:[(0,s.jsx)(m.lX,{children:g("Sex")}),(0,s.jsx)(m.NI,{children:(0,s.jsxs)(p.E,{defaultValue:t.value,onValueChange:t.onChange,className:"flex",children:[(0,s.jsxs)(f.Z,{htmlFor:"GenderMale","data-selected":"Male"===(0,b.fl)(t.value),className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,s.jsx)(p.m,{id:"GenderMale",value:"Male",className:"absolute opacity-0"}),(0,s.jsx)("span",{children:g("Male")})]}),(0,s.jsxs)(f.Z,{htmlFor:"GenderFemale","data-selected":"Female"===(0,b.fl)(t.value),className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,s.jsx)(p.m,{id:"GenderFemale",value:"Female",className:"absolute opacity-0"}),(0,s.jsx)("span",{children:g("Female")})]})]})}),(0,s.jsx)(m.zG,{})]})}}),(0,s.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,s.jsxs)(u.z,{disabled:h,children:[(0,s.jsxs)(n.J,{condition:!h,children:[g("Save"),(0,s.jsx)(w.Z,{size:20})]}),(0,s.jsx)(n.J,{condition:h,children:(0,s.jsx)(o.Loader,{title:g("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}},6596:function(e,t,r){r.d(t,{Qd:function(){return d},UQ:function(){return o},o4:function(){return c},vF:function(){return u}});var s=r(57437),n=r(13134),a=r(2265),l=r(94508),i=r(36887);let o=n.fC,d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.ck,{ref:t,className:(0,l.ZP)("border-b",r),...a})});d.displayName="AccordionItem";let c=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,s.jsx)(n.h4,{className:"flex",children:(0,s.jsxs)(n.xz,{ref:t,className:(0,l.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",r),...o,children:[a,(0,s.jsx)(i.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=n.xz.displayName;let u=a.forwardRef((e,t)=>{let{className:r,children:a,...i}=e;return(0,s.jsx)(n.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...i,children:(0,s.jsx)("div",{className:(0,l.ZP)("pb-4 pt-0",r),children:a})})});u.displayName=n.VY.displayName},65613:function(e,t,r){r.d(t,{Cd:function(){return d},X:function(){return c},bZ:function(){return o}});var s=r(57437),n=r(90535),a=r(2265),l=r(94508);let i=(0,n.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:n,...a}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,l.ZP)(i({variant:n}),r),...a})});o.displayName="Alert";let d=a.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h5",{ref:t,className:(0,l.ZP)("mb-1 font-medium leading-none tracking-tight",r),...n})});d.displayName="AlertTitle";let c=a.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.ZP)("text-sm [&_p]:leading-relaxed",r),...n})});c.displayName="AlertDescription"},1098:function(e,t,r){r.d(t,{f:function(){return c}});var s=r(57437),n=r(92451),a=r(10407),l=r(40875);r(2265);var i=r(90827),o=r(62869),d=r(94508);function c(e){let{className:t,classNames:r,showOutsideDays:c=!0,...u}=e;return(0,s.jsx)(i._W,{showOutsideDays:c,className:(0,d.ZP)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,d.ZP)((0,o.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.ZP)((0,o.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...r},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:e=>{let{...t}=e;return(0,s.jsx)(n.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...t}=e;return(0,s.jsx)(a.Z,{className:"h-4 w-4"})},Dropdown:e=>{let{...t}=e;return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("select",{...t,style:{opacity:0,position:"absolute"}}),(0,s.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm",children:t.caption}),(0,s.jsx)(l.Z,{className:"size-3"})]})]})}},...u})}c.displayName="Calendar"},15681:function(e,t,r){r.d(t,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return p},xJ:function(){return f},zG:function(){return v}});var s=r(57437),n=r(37053),a=r(2265),l=r(29501),i=r(26815),o=r(94508);let d=l.RV,c=a.createContext({}),u=e=>{let{...t}=e;return(0,s.jsx)(c.Provider,{value:{name:t.name},children:(0,s.jsx)(l.Qr,{...t})})},m=()=>{let e=a.useContext(c),t=a.useContext(x),{getFieldState:r,formState:s}=(0,l.Gc)(),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},x=a.createContext({}),f=a.forwardRef((e,t)=>{let{className:r,...n}=e,l=a.useId();return(0,s.jsx)(x.Provider,{value:{id:l},children:(0,s.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",r),...n})})});f.displayName="FormItem";let p=a.forwardRef((e,t)=>{let{className:r,required:n,...a}=e,{error:l,formItemId:d}=m();return(0,s.jsx)("span",{children:(0,s.jsx)(i.Z,{ref:t,className:(0,o.ZP)(l&&"text-base font-medium text-destructive",r),htmlFor:d,...a})})});p.displayName="FormLabel";let h=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:l,formDescriptionId:i,formMessageId:o}=m();return(0,s.jsx)(n.g7,{ref:t,id:l,"aria-describedby":a?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!a,...r})});h.displayName="FormControl",a.forwardRef((e,t)=>{let{className:r,...n}=e,{formDescriptionId:a}=m();return(0,s.jsx)("p",{ref:t,id:a,className:(0,o.ZP)("text-sm text-muted-foreground",r),...n})}).displayName="FormDescription";let v=a.forwardRef((e,t)=>{let{className:r,children:n,...a}=e,{error:l,formMessageId:i}=m(),d=l?String(null==l?void 0:l.message):n;return d?(0,s.jsx)("p",{ref:t,id:i,className:(0,o.ZP)("text-sm font-medium text-destructive",r),...a,children:d}):null});v.displayName="FormMessage"},57054:function(e,t,r){r.d(t,{J2:function(){return i},xo:function(){return o},yk:function(){return d}});var s=r(57437),n=r(2265),a=r(27312),l=r(94508);let i=a.fC,o=a.xz,d=n.forwardRef((e,t)=>{let{className:r,align:n="center",sideOffset:i=4,...o}=e;return(0,s.jsx)(a.h_,{children:(0,s.jsx)(a.VY,{ref:t,align:n,sideOffset:i,className:(0,l.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})})});d.displayName=a.VY.displayName},74991:function(e,t,r){r.d(t,{E:function(){return o},m:function(){return d}});var s=r(57437),n=r(2265),a=r(42325),l=r(40519),i=r(94508);let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.fC,{className:(0,i.ZP)("grid gap-2",r),...n,ref:t})});o.displayName=a.fC.displayName;let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.ck,{ref:t,className:(0,i.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...n,children:(0,s.jsx)(a.z$,{className:"flex items-center justify-center",children:(0,s.jsx)(l.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=a.ck.displayName},54995:function(e,t,r){r.d(t,{K:function(){return l},S:function(){return i}});var s=r(31229);let n=["image/jpeg","image/jpg","image/png","image/svg+xml"],a=["image/x-icon","image/vnd.microsoft.icon","image/png"],l=s.z.union([s.z.string(),s.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&n.includes(e.type),"Invalid file format. Please upload a .jpg, .jpeg, .png or .svg file."),i=s.z.union([s.z.string(),s.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&a.includes(e.type),"Invalid file format. Please upload a .ico or .png file.")},74539:function(e,t,r){r.d(t,{k:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,r){r.d(t,{u:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},70517:function(e,t,r){r.d(t,{n:function(){return l}});var s=r(74539),n=r(66419),a=r(94508);class l{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.firstName=null==e?void 0:e.firstName,this.lastName=null==e?void 0:e.lastName,this.avatar=null==e?void 0:e.avatar,this.roleId=null==e?void 0:e.roleId,this.phone=(0,a.Fg)(null==e?void 0:e.phone),this.email=null==e?void 0:e.email,this.isEmailVerified=null==e?void 0:e.isEmailVerified,this.status=null==e?void 0:e.status,this.kycStatus=null==e?void 0:e.kycStatus,this.lastIpAddress=null==e?void 0:e.lastIpAddress,this.lastCountryName=null==e?void 0:e.lastCountryName,this.passwordUpdated=null==e?void 0:e.passwordUpdated,this.referredBy=null==e?void 0:e.referredBy,this.otpCode=null==e?void 0:e.otpCode,this.createdAt=(null==e?void 0:e.createdAt)?new Date(null==e?void 0:e.createdAt):void 0,this.updatedAt=(null==e?void 0:e.updatedAt)?new Date(null==e?void 0:e.updatedAt):void 0,this.role=new n.u(null==e?void 0:e.role),this.dateOfBirth=(null==e?void 0:e.dob)?new Date(null==e?void 0:e.dob):void 0,this.gender=null==e?void 0:e.gender,this.address=(null==e?void 0:e.address)?new s.k(null==e?void 0:e.address):null}}}}]);