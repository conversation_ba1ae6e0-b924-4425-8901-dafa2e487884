"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[99786],{85487:function(e,t,r){r.d(t,{Loader:function(){return i}});var n=r(57437),o=r(94508),a=r(43949);function i(e){let{title:t="Loading...",className:r}=e,{t:i}=(0,a.$G)();return(0,n.jsxs)("div",{className:(0,o.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:i(t)})]})}},6596:function(e,t,r){r.d(t,{Qd:function(){return c},UQ:function(){return l},o4:function(){return u},vF:function(){return d}});var n=r(57437),o=r(13134),a=r(2265),i=r(94508),s=r(36887);let l=o.fC,c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.ck,{ref:t,className:(0,i.ZP)("border-b",r),...a})});c.displayName="AccordionItem";let u=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,n.jsx)(o.h4,{className:"flex",children:(0,n.jsxs)(o.xz,{ref:t,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",r),...l,children:[a,(0,n.jsx)(s.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});u.displayName=o.xz.displayName;let d=a.forwardRef((e,t)=>{let{className:r,children:a,...s}=e;return(0,n.jsx)(o.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...s,children:(0,n.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",r),children:a})})});d.displayName=o.VY.displayName},62869:function(e,t,r){r.d(t,{d:function(){return l},z:function(){return c}});var n=r(57437),o=r(37053),a=r(90535),i=r(2265),s=r(94508);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...u}=e,d=c?o.g7:"button";return(0,n.jsx)(d,{className:(0,s.ZP)(l({variant:a,size:i,className:r})),ref:t,...u})});c.displayName="Button"},15681:function(e,t,r){r.d(t,{NI:function(){return g},Wi:function(){return d},l0:function(){return c},lX:function(){return v},xJ:function(){return p},zG:function(){return x}});var n=r(57437),o=r(37053),a=r(2265),i=r(29501),s=r(26815),l=r(94508);let c=i.RV,u=a.createContext({}),d=e=>{let{...t}=e;return(0,n.jsx)(u.Provider,{value:{name:t.name},children:(0,n.jsx)(i.Qr,{...t})})},f=()=>{let e=a.useContext(u),t=a.useContext(m),{getFieldState:r,formState:n}=(0,i.Gc)(),o=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:"".concat(s,"-form-item"),formDescriptionId:"".concat(s,"-form-item-description"),formMessageId:"".concat(s,"-form-item-message"),...o}},m=a.createContext({}),p=a.forwardRef((e,t)=>{let{className:r,...o}=e,i=a.useId();return(0,n.jsx)(m.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:t,className:(0,l.ZP)("space-y-2",r),...o})})});p.displayName="FormItem";let v=a.forwardRef((e,t)=>{let{className:r,required:o,...a}=e,{error:i,formItemId:c}=f();return(0,n.jsx)("span",{children:(0,n.jsx)(s.Z,{ref:t,className:(0,l.ZP)(i&&"text-base font-medium text-destructive",r),htmlFor:c,...a})})});v.displayName="FormLabel";let g=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:i,formDescriptionId:s,formMessageId:l}=f();return(0,n.jsx)(o.g7,{ref:t,id:i,"aria-describedby":a?"".concat(s," ").concat(l):"".concat(s),"aria-invalid":!!a,...r})});g.displayName="FormControl",a.forwardRef((e,t)=>{let{className:r,...o}=e,{formDescriptionId:a}=f();return(0,n.jsx)("p",{ref:t,id:a,className:(0,l.ZP)("text-sm text-muted-foreground",r),...o})}).displayName="FormDescription";let x=a.forwardRef((e,t)=>{let{className:r,children:o,...a}=e,{error:i,formMessageId:s}=f(),c=i?String(null==i?void 0:i.message):o;return c?(0,n.jsx)("p",{ref:t,id:s,className:(0,l.ZP)("text-sm font-medium text-destructive",r),...a,children:c}):null});x.displayName="FormMessage"},95186:function(e,t,r){r.d(t,{I:function(){return i}});var n=r(57437),o=r(2265),a=r(94508);let i=o.forwardRef((e,t)=>{let{className:r,type:o,...i}=e;return(0,n.jsx)("input",{type:o,className:(0,a.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...i})});i.displayName="Input"},26815:function(e,t,r){var n=r(57437),o=r(6394),a=r(90535),i=r(2265),s=r(94508);let l=(0,a.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(o.f,{ref:t,className:(0,s.ZP)(l(),r),...a})});c.displayName=o.f.displayName,t.Z=c},76818:function(e,t,r){r.d(t,{g:function(){return i}});var n=r(57437),o=r(2265),a=r(94508);let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("textarea",{className:(0,a.ZP)("placeholder:text-placeholder flex min-h-[80px] w-full rounded-md border border-input bg-input px-3 py-2 text-base ring-offset-background placeholder:font-normal focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...o})});i.displayName="Textarea"},32299:function(e,t,r){r.d(t,{Y:function(){return a}});var n=r(79981),o=r(97751);async function a(e,t){try{let r=await n.Z.post("/admin/users/send-email/".concat(t),e);return(0,o.B)(r)}catch(e){return(0,o.D)(e)}}},97751:function(e,t,r){r.d(t,{B:function(){return o},D:function(){return a}});var n=r(43577);function o(e){var t,r,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function a(e){let t=500,r="Internal Server Error",o="An unknown error occurred";if((0,n.IZ)(e)){var a,i,s,l,c,u,d,f,m,p,v,g;t=null!==(m=null===(a=e.response)||void 0===a?void 0:a.status)&&void 0!==m?m:500,r=null!==(p=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==p?p:"Internal Server Error",o=null!==(g=null!==(v=null===(u=e.response)||void 0===u?void 0:null===(c=u.data)||void 0===c?void 0:null===(l=c.messages)||void 0===l?void 0:null===(s=l[0])||void 0===s?void 0:s.message)&&void 0!==v?v:null===(f=e.response)||void 0===f?void 0:null===(d=f.data)||void 0===d?void 0:d.message)&&void 0!==g?g:e.message}else e instanceof Error&&(o=e.message);return{statusCode:t,statusText:r,status:!1,message:o,data:void 0,error:e}}},79981:function(e,t,r){var n=r(78040),o=r(83464);t.Z=o.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){r.d(t,{rH:function(){return n},sp:function(){return o}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},o=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){r.d(t,{F:function(){return u},Fg:function(){return m},Fp:function(){return c},Qp:function(){return f},ZP:function(){return s},fl:function(){return l},qR:function(){return d},w4:function(){return p}});var n=r(78040),o=r(61994),a=r(14438),i=r(53335);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,o.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let o;let a=void 0===t?this.currencyCode:t;try{o=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){o=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=o.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:a,s=o.format(e),l=s.substring(i.length).trim();return{currencyCode:a,currencySymbol:i,formattedAmount:s,amountText:l}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",p=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",o=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?o.set(n,e):o.delete(n),o}}}]);