{"version": 3, "file": "app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page.js", "mappings": "qFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,2CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,kGEAAC,gxBDKA,IAAAC,EACA,CACA,GACA,CACAC,SAAA,CACA,cACA,CACAC,MAAA,CACA,WACA,CACAD,SAAA,CACA,SACA,CACAA,SAAA,CACA,WACA,CACAA,SAAA,CACA,YACA,CACAA,SAAA,CACA,MACA,CACAA,SAAA,eAAiC,CACjCE,KAAA,KAAuBC,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAyK,yIAEvL,EAET,CACA,aAA0BH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA4K,4IAGtM,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAsK,qIAC/L,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAuK,uIAGzL,EAEA,CAGA,EACA,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAiJ,gHAC1K,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkJ,kHAGpK,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAyI,wGAClK,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0I,0GAG5J,CACAN,SAAA,CACA,cACA,GACA,CACAQ,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAG,MAAA,CACA,cACA,GACA,CACAD,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAI,SAAA,CACA,cACA,GACA,CACAF,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAK,SAAA,CACA,cACA,GACA,CACAH,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAiI,gGAC1J,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkI,kGAGpJ,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoH,mFAC7I,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqH,oFACvI,iBAAoBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAAuH,uFAG3I,CAEAM,EAAA,yIAKOC,EAAA,yDACAC,EAAA,CACPjB,QAJ6BS,EAK7BS,UAJA,IAAAZ,QAAAC,OAAA,EAKA,EAGOY,EAAA,IAAwBC,EAAAC,kBAAkB,EACjDC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,QAAA,CACvBrB,KAAA,yDACAsB,SAAA,iCAEAC,WAAA,GACAC,SAAA,GACAC,SAAA,IAEAC,SAAA,CACAC,WAAA9B,CACA,CACA,uCCjHA,IAAA+B,EAAA,GAAAC,EAAAC,KAAAC,KAAA,CAAAF,GAAAG,KAAAA,EACAC,EAAAC,KAAAC,gBAAA,CACAC,EAAAR,EAAAM,KAAAG,yBAAA,EACAC,EAAA,MAAA1C,CAAAA,EAAAsC,KAAAK,cAAA,SAAA3C,CAAA,2DACA4C,EAAAZ,EAAAM,KAAAO,qBAAA,EAEAC,EAAAd,EAAAM,KAAAS,oBAAA,EACAC,EAAAhB,EAAAM,KAAAW,qCAAA,MACAP,GAAAE,GACI,GAAAM,EAAAC,EAAA,EAA8B,CAClCC,wBAAAV,EACAW,sBAAAT,EACAU,gBAAyB,GAAAC,EAAAC,CAAA,EAAqB,CAC9CH,sBAAAT,EACAa,SAAA,wDACA,EACA,GAEA,IAAMC,EAAS,GAAAC,EAAAC,CAAA,EAAS,CACxBC,UAAeC,EAAAC,CAAU,CAAAC,GAAA,CACzBC,IAvBA,GAwBA7D,KAAA,yDACA8D,OA9BA,KA+BAC,QAAWC,EACXC,SA/BA,KAgCAC,YA/BA,KAgCAC,SAnCA,KAoCAlC,cAAAA,EACAmC,aAAgBC,EAAAC,CAAA,CAChBlC,sBAAAA,EACAY,wBAAAV,EACAW,sBAAAT,EACA+B,cAlCAvC,KAAAA,EAmCAwC,6BA5BAxC,KAAAA,EA6BAyC,OAnCA,CAAoB,OAAQ,qBAA0B,uBAA2B,YAAe,mDAAyD,qPAAwR,wTAAkV,iDAAuD,sCAAyC,CAAE,gCAAmC,iBAAsB,eAAkB,uDAA4D,iBAAoB,wCAA6C,KAAQ,kBAAmB,2BAA+B,iIAAuJ,uBAAyB,mEAA6E,cAAiB,oFAAiG,uBAAuB,0CAA2C,EAAE,QAAW,6BAA8B,GAAG,cAAiB,w6BAA4lC,uBAA0B,o9BAAs9B,+HAoCnqHC,QAAAC,QAAAC,GAAA,CAAAC,eAAA,CACAnC,iBAAAA,EACAoC,wBA/CA,KAgDAlC,0BAAAA,CACA,GACOmC,EAAqBf,EACb,SAAAgB,EAAAC,CAAA,EACf,MAAW,GAAAC,EAAAC,CAAA,EAAO,CAClB,GAAAF,CAAA,CACAG,iBAAwBC,EAAAC,CAAA,CACxBC,QAAiBjC,CACjB,EACA,mBCnEArD,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,yBCAAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,0PCyBO,SAASoF,IACd,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTpE,EAAWqE,CAAAA,EAAAA,EAAAA,EAAAA,IACXC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTC,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,IAEf,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAERC,EAAO,CACX,CACEC,MAAOH,EAAE,mBACTI,KAAM,GAAAC,EAAAC,GAAA,EAACC,EAAAA,CAAQA,CAAAA,CAACC,KAAK,KAAKC,QAAQ,SAClCC,KAAM,CAAC,QAAQ,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,QAAQ,CAAC,EAAEd,EAAae,QAAQ,GAAG,CAAC,CAC/EC,GAAI,aACN,EACA,CACEX,MAAOH,EAAE,uBACTI,KAAM,GAAAC,EAAAC,GAAA,EAACS,EAAAA,CAAgBA,CAAAA,CAACP,KAAK,KAAKC,QAAQ,SAC1CC,KAAM,CAAC,QAAQ,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,QAAQ,aAAa,EAAEd,EAAae,QAAQ,GAAG,CAAC,CAC3FC,GAAI,aACN,EACA,CACEX,MAAOH,EAAE,QACTI,KAAM,GAAAC,EAAAC,GAAA,EAACS,EAAAA,CAAgBA,CAAAA,CAACP,KAAK,KAAKC,QAAQ,SAC1CC,KAAM,CAAC,QAAQ,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,QAAQ,MAAM,EAAEd,EAAae,QAAQ,GAAG,CAAC,CACpFC,GAAI,MACN,EACA,CACEX,MAAOH,EAAE,gBACTI,KAAM,GAAAC,EAAAC,GAAA,EAACU,EAAAA,CAAKA,CAAAA,CAACR,KAAK,KAAKC,QAAQ,SAC/BC,KAAM,CAAC,QAAQ,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,QAAQ,cAAc,EAAEd,EAAae,QAAQ,GAAG,CAAC,CAC5FC,GAAI,cACN,EAEA,CACEX,MAAOH,EAAE,OACTI,KAAM,GAAAC,EAAAC,GAAA,EAACW,EAAAA,CAAcA,CAAAA,CAACT,KAAK,KAAKC,QAAQ,SACxCC,KAAM,CAAC,QAAQ,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,QAAQ,KAAK,EAAEd,EAAae,QAAQ,GAAG,CAAC,CACnFC,GAAI,KACN,EACA,CACEX,MAAOH,EAAE,eACTI,KAAM,GAAAC,EAAAC,GAAA,EAACY,EAAAA,CAAOA,CAAAA,CAACV,KAAK,KAAKC,QAAQ,SACjCC,KAAM,CAAC,QAAQ,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,QAAQ,aAAa,EAAEd,EAAae,QAAQ,GAAG,CAAC,CAC3FC,GAAI,aACN,EACA,CACEX,MAAOH,EAAE,cACTI,KAAM,GAAAC,EAAAC,GAAA,EAACa,EAAAA,CAAGA,CAAAA,CAACX,KAAK,KAAKC,QAAQ,SAC7BC,KAAM,CAAC,QAAQ,EAAEjB,GAAQkB,OAAO,CAAC,EAAElB,GAAQmB,QAAQ,YAAY,EAAEd,EAAae,QAAQ,GAAG,CAAC,CAC1FC,GAAI,YACN,EACD,CAED,MACE,GAAAT,EAAAe,IAAA,EAACC,MAAAA,CAAIC,UAAU,+FACb,GAAAjB,EAAAe,IAAA,EAACC,MAAAA,CAAIC,UAAU,mEACb,GAAAjB,EAAAe,IAAA,EAACG,KAAAA,CAAGD,UAAU,iJACZ,GAAAjB,EAAAC,GAAA,EAACkB,KAAAA,UACC,GAAAnB,EAAAe,IAAA,EAACK,EAAAA,CAAIA,CAAAA,CACHf,KAAK,eACLY,UAAU,0FAEV,GAAAjB,EAAAC,GAAA,EAACoB,EAAAA,CAAUA,CAAAA,CAACJ,UAAU,qBACrBtB,EAAE,aAGP,GAAAK,EAAAe,IAAA,EAACI,KAAAA,CAAGF,UAAU,2CAAiC,KAC1CxB,EAAa6B,GAAG,CAAC,QAAS,OAE/B,GAAAtB,EAAAe,IAAA,EAACI,KAAAA,CAAGF,UAAU,2CAAiC,KAC1CtB,EAAE,UAAU,KAAGP,EAAOmB,OAAO,OAGpC,GAAAP,EAAAe,IAAA,EAACC,MAAAA,CAAIC,UAAU,wEACb,GAAAjB,EAAAC,GAAA,EAACsB,OAAAA,UAAM5B,EAAE,YACT,GAAAK,EAAAC,GAAA,EAACuB,EAAAA,CAAMA,CAAAA,CACLC,eAAgBhC,MAAAA,EAAa6B,GAAG,CAAC,UACjCL,UAAU,kCACVS,gBAAiB,IACfC,EAAAA,KAAKA,CAACC,OAAO,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,EAAezC,EAAOkB,MAAM,EAAa,CACrDwB,QAASnC,EAAE,cACXoC,QAAS,IACP,GAAI,CAACC,EAAIC,MAAM,CAAE,MAAM,MAAUD,EAAIE,OAAO,EAC5C,IAAMC,EAAK,IAAIC,gBAAgB3C,GAI/B,MAHA4C,CAAAA,EAAAA,EAAAA,CAAAA,EAAO,CAAC,cAAc,EAAEjD,EAAOmB,OAAO,CAAC,CAAC,EACxC4B,EAAGG,GAAG,CAAC,SAAUC,EAAU,IAAM,KACjChD,EAAOiD,IAAI,CAAC,CAAC,EAAEvH,EAAS,CAAC,EAAEkH,EAAG3B,QAAQ,GAAG,CAAC,EACnCwB,EAAIE,OAAO,EAEpBO,MAAO,GAASC,EAAIR,OAAO,EAE/B,UAKN,GAAAlC,EAAAC,GAAA,EAAC0C,EAAAA,CAAYA,CAAAA,CAAC9C,KAAMA,MAG1B,0UC7Ee,SAAS+C,IACtB,GAAM,CAAEjD,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IACRR,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IAET,CAAEwD,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAET,OAAAA,CAAM,CAAE,CAAGU,CAAAA,EAAAA,EAAAA,CAAAA,EAAO,CAAC,aAAa,EAAE3D,EAAOkB,MAAM,CAAC,CAAC,EAGpE0C,EAAY,CAACvC,EAAqBwC,KACtCtB,EAAAA,KAAKA,CAACC,OAAO,CAACsB,CAAAA,EAAAA,EAAAA,CAAAA,EAAiBzC,EAAIwC,GAAO,CACxCnB,QAASnC,EAAE,cACXoC,QAAS,IACP,GAAI,CAACC,EAAIC,MAAM,CAAE,MAAM,MAAUD,EAAIE,OAAO,EAE5C,OADAG,EAAOQ,GACAb,EAAIE,OAAO,EAEpBO,MAAO,GAASC,EAAIR,OAAO,EAE/B,EAEMiB,EAAMN,GAAMA,MAAMM,IAAM,IAAIC,EAAAA,CAAGA,CAACP,EAAKA,IAAI,CAACM,GAAG,EAAI,KAEvD,MACE,GAAAnD,EAAAC,GAAA,EAACoD,EAAAA,EAASA,CAAAA,CACRJ,KAAK,WACLK,aAAc,CAAC,aAAc,uBAAuB,UAEpD,GAAAtD,EAAAe,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACb,GAAAjB,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,yDACb,GAAAjB,EAAAe,IAAA,EAACwC,EAAAA,EAAaA,CAAAA,CAACC,MAAM,aAAavC,UAAU,kCAC1C,GAAAjB,EAAAC,GAAA,EAACwD,EAAAA,EAAgBA,CAAAA,CAACxC,UAAU,mCAC1B,GAAAjB,EAAAe,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACb,GAAAjB,EAAAC,GAAA,EAACyD,IAAAA,CAAEzC,UAAU,gDACVtB,EAAE,gBAIL,GAAAK,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAACT,WAChB,GAAAnD,EAAAC,GAAA,EAAC4D,EAAAA,CAAKA,CAAAA,CAAC5C,UAAU,yDACdtB,EAAE,2BAKP,GAAAK,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAWT,GAAKlB,SAAW,mBAC/B,GAAAjC,EAAAC,GAAA,EAAC4D,EAAAA,CAAKA,CAAAA,CAAC5C,UAAU,8DACdtB,EAAE,eAIP,GAAAK,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAWT,GAAKlB,SAAW,oBAC/B,GAAAjC,EAAAC,GAAA,EAAC4D,EAAAA,CAAKA,CAAAA,CAAC5C,UAAU,0EACdtB,EAAE,gBAIP,GAAAK,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAWT,GAAKlB,SAAW,kBAC/B,GAAAjC,EAAAC,GAAA,EAAC4D,EAAAA,CAAKA,CAAAA,CAAC5C,UAAU,iEACdtB,EAAE,qBAKX,GAAAK,EAAAe,IAAA,EAAC+C,EAAAA,EAAgBA,CAAAA,CAAC7C,UAAU,kEAE1B,GAAAjB,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAACT,WAChB,GAAAnD,EAAAe,IAAA,EAACgD,EAAAA,EAAKA,CAAAA,CAAC9C,UAAU,8EACf,GAAAjB,EAAAC,GAAA,EAAC+D,EAAAA,CAAMA,CAAAA,CAAC7D,KAAK,KAAKC,QAAQ,SAC1B,GAAAJ,EAAAC,GAAA,EAACgE,EAAAA,EAAUA,CAAAA,CAAChD,UAAU,kDACnBtB,EAAE,2CAEL,GAAAK,EAAAC,GAAA,EAACiE,EAAAA,CAAgBA,CAAAA,CAACjD,UAAU,sCACzBtB,EACC,kJAMR,GAAAK,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAWT,GAAKlB,SAAW,mBAE/B,GAAAjC,EAAAe,IAAA,EAACgD,EAAAA,EAAKA,CAAAA,CAAC9C,UAAU,2EACf,GAAAjB,EAAAC,GAAA,EAACkE,EAAAA,CAAYA,CAAAA,CAAChE,KAAK,KAAKC,QAAQ,SAChC,GAAAJ,EAAAC,GAAA,EAACgE,EAAAA,EAAUA,CAAAA,CAAChD,UAAU,kDACnBtB,EAAE,0BAEL,GAAAK,EAAAC,GAAA,EAACiE,EAAAA,CAAgBA,CAAAA,CAACjD,UAAU,sCACzBtB,EACC,kJAMR,GAAAK,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAWT,GAAKlB,SAAW,oBAC/B,GAAAjC,EAAAe,IAAA,EAACgD,EAAAA,EAAKA,CAAAA,CAAC9C,UAAU,iFACf,GAAAjB,EAAAC,GAAA,EAACmE,EAAAA,CAAUA,CAAAA,CAACjE,KAAK,KAAKC,QAAQ,SAC9B,GAAAJ,EAAAC,GAAA,EAACgE,EAAAA,EAAUA,CAAAA,CAAChD,UAAU,kDACnBtB,EAAE,8BAEL,GAAAK,EAAAC,GAAA,EAACiE,EAAAA,CAAgBA,CAAAA,CAACjD,UAAU,sCACzBtB,EACC,kJAMR,GAAAK,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAWT,GAAKlB,SAAW,kBAC/B,GAAAjC,EAAAe,IAAA,EAACgD,EAAAA,EAAKA,CAAAA,CAAC9C,UAAU,0EACf,GAAAjB,EAAAC,GAAA,EAACoE,EAAAA,CAAWA,CAAAA,CAAClE,KAAK,KAAKC,QAAQ,SAC/B,GAAAJ,EAAAC,GAAA,EAACgE,EAAAA,EAAUA,CAAAA,CAAChD,UAAU,kDACnBtB,EAAE,2BAEL,GAAAK,EAAAC,GAAA,EAACiE,EAAAA,CAAgBA,CAAAA,CAACjD,UAAU,sCACzBtB,EACC,sMASd,GAAAK,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,yDACb,GAAAjB,EAAAe,IAAA,EAACwC,EAAAA,EAAaA,CAAAA,CACZC,MAAM,uBACNvC,UAAU,kCAEV,GAAAjB,EAAAC,GAAA,EAACwD,EAAAA,EAAgBA,CAAAA,CAACxC,UAAU,mCAC1B,GAAAjB,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,mCACb,GAAAjB,EAAAC,GAAA,EAACyD,IAAAA,CAAEzC,UAAU,gDACVtB,EAAE,mBAIT,GAAAK,EAAAC,GAAA,EAAC6D,EAAAA,EAAgBA,CAAAA,CAAC7C,UAAU,iEACzB,EAOC,GAAAjB,EAAAe,IAAA,EAAAf,EAAAsE,QAAA,YACE,GAAAtE,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,yBACb,GAAAjB,EAAAe,IAAA,EAACwD,EAAAA,EAAKA,CAAAA,CAACtD,UAAU,wBACf,GAAAjB,EAAAC,GAAA,EAACuE,EAAAA,EAAWA,CAAAA,CAACvD,UAAU,6BACrB,GAAAjB,EAAAe,IAAA,EAAC0D,EAAAA,EAAQA,CAAAA,WACP,GAAAzE,EAAAC,GAAA,EAACyE,EAAAA,EAASA,CAAAA,UAAE/E,EAAE,SACd,GAAAK,EAAAC,GAAA,EAACyE,EAAAA,EAASA,CAAAA,UAAE/E,EAAE,eAIlB,GAAAK,EAAAC,GAAA,EAAC0E,EAAAA,EAASA,CAAAA,UACP7B,EACC,GAAA9C,EAAAC,GAAA,EAACwE,EAAAA,EAAQA,CAAAA,UACP,GAAAzE,EAAAC,GAAA,EAAC2E,EAAAA,EAASA,CAAAA,CAACC,QAAS,WAClB,GAAA7E,EAAAC,GAAA,EAAC6E,EAAAA,MAAMA,CAAAA,CAAAA,OAIX,GAAA9E,EAAAe,IAAA,EAAAf,EAAAsE,QAAA,YACE,GAAAtE,EAAAe,IAAA,EAAC0D,EAAAA,EAAQA,CAAAA,CAACxD,UAAU,0BAClB,GAAAjB,EAAAC,GAAA,EAAC2E,EAAAA,EAASA,CAAAA,UAAEjF,EAAE,mBACd,GAAAK,EAAAC,GAAA,EAAC2E,EAAAA,EAASA,CAAAA,UACR,GAAA5E,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,6CACb,GAAAjB,EAAAC,GAAA,EAACsB,OAAAA,CAAKN,UAAU,yCACbkC,GAAK4B,sBAKd,GAAA/E,EAAAC,GAAA,EAAC+E,EAAAA,CACClF,MAAOH,EAAE,eACTsF,QAAS9B,GAAK+B,QAGhB,GAAAlF,EAAAC,GAAA,EAAC+E,EAAAA,CACClF,MAAOH,EAAE,cACTsF,QAAS9B,GAAKgC,OAGhB,GAAAnF,EAAAC,GAAA,EAAC+E,EAAAA,CACClF,MAAOH,EAAE,UACTsF,QAAS9B,GAAKiC,mBAQ1B,GAAApF,EAAAC,GAAA,EAAC0D,EAAAA,CAAIA,CAAAA,CAACC,UAAWT,EAAIlB,MAAM,EAAEoD,gBAAkB,mBAC7C,GAAArF,EAAAe,IAAA,EAACC,MAAAA,CAAIC,UAAU,yDACb,GAAAjB,EAAAe,IAAA,EAACuE,EAAAA,CAAMA,CAAAA,CACLrC,KAAK,SACLsC,QAAS,IAAMvC,EAAUG,EAAI1C,EAAE,CAAE,UACjCQ,UAAU,uDAEV,GAAAjB,EAAAC,GAAA,EAACuF,EAAAA,CAAUA,CAAAA,CAAAA,GACV7F,EAAE,cAEL,GAAAK,EAAAe,IAAA,EAACuE,EAAAA,CAAMA,CAAAA,CACLrC,KAAK,SACLsC,QAAS,IAAMvC,EAAUG,EAAI1C,EAAE,CAAE,WACjCQ,UAAU,uDAEV,GAAAjB,EAAAC,GAAA,EAACwF,EAAAA,CAAWA,CAAAA,CAAAA,GACX9F,EAAE,qBAvEX,GAAAK,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,gBACb,GAAAjB,EAAAC,GAAA,EAACyD,IAAAA,CAAEzC,UAAU,+BACVtB,EAAE,kDAiFvB,CAEA,SAASqF,EAAY,CAAElF,MAAAA,CAAK,CAAEmF,QAAAA,CAAO,CAAsC,EACzE,GAAM,CAAEtF,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAEd,MACE,GAAAI,EAAAe,IAAA,EAAC0D,EAAAA,EAAQA,CAAAA,CAACxD,UAAU,0BAClB,GAAAjB,EAAAC,GAAA,EAAC2E,EAAAA,EAASA,CAAAA,UAAE9E,IACZ,GAAAE,EAAAC,GAAA,EAAC2E,EAAAA,EAASA,CAAAA,UACR,GAAA5E,EAAAe,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAjB,EAAAe,IAAA,EAAC2E,EAAAA,EAAMA,CAAAA,WACL,GAAA1F,EAAAC,GAAA,EAAC0F,EAAAA,EAAaA,CAAAA,CAACC,QAAO,YACpB,GAAA5F,EAAAe,IAAA,EAACuE,EAAAA,CAAMA,CAAAA,CACLrC,KAAK,SACL7C,QAAQ,UACRa,UAAU,yCAEV,GAAAjB,EAAAC,GAAA,EAAC4F,EAAAA,CAAGA,CAAAA,CAAAA,GACJ,GAAA7F,EAAAC,GAAA,EAACsB,OAAAA,CAAKN,UAAU,2BAAmBtB,EAAE,eAIzC,GAAAK,EAAAe,IAAA,EAAC+E,EAAAA,EAAaA,CAAAA,CAAC7E,UAAU,sBACvB,GAAAjB,EAAAe,IAAA,EAACgF,EAAAA,EAAYA,CAAAA,WACX,GAAA/F,EAAAe,IAAA,EAACiF,EAAAA,EAAWA,CAAAA,WAAC,IAAElG,EAAM,OACrB,GAAAE,EAAAC,GAAA,EAACgG,EAAAA,EAAiBA,CAAAA,CAAChF,UAAU,SAASiF,cAAW,QAGnD,GAAAlG,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,0DACZgE,EACC,GAAAjF,EAAAC,GAAA,EAACkG,EAAAA,CAAKA,CAAAA,CACJC,IAAKnB,EACLoB,IAAKvG,EACLwG,KAAI,GACJC,MAAM,QACNC,MAAO,CAAEC,MAAO,OAAQC,OAAQ,MAAO,EACvCC,QAAQ,KACR7E,QAAQ,OACR8E,YAAY,OACZ3F,UAAU,iBACV4F,YAAY,uHAGd,GAAA7G,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,+EACb,GAAAjB,EAAAC,GAAA,EAACsB,OAAAA,CAAKN,UAAU,oCACbtB,EAAE,0BAQf,GAAAK,EAAAC,GAAA,EAACqF,EAAAA,CAAMA,CAAAA,CACLrC,KAAK,SACL7C,QAAQ,UACRa,UAAU,+BACV2E,QAAO,YAEP,GAAA5F,EAAAe,IAAA,EAAC+F,IAAAA,CAAEzG,KAAM4E,EAAS8B,SAAQ,aACxB,GAAA/G,EAAAC,GAAA,EAAC+G,EAAAA,CAAgBA,CAAAA,CAAAA,GACjB,GAAAhH,EAAAC,GAAA,EAACsB,OAAAA,CAAKN,UAAU,kCAA0BtB,EAAE,0BAO1D,wICzUA,IAAM0D,EAAY4D,EAAAA,EAAuB,CAEnC1D,EAAgB2D,EAAAA,UAAgB,CAGpC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAACgH,EAAAA,EAAuB,EACtBG,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,WAAYpG,GACzB,GAAGkG,CAAK,GAGb5D,CAAAA,EAAc+D,WAAW,CAAG,gBAE5B,IAAM7D,EAAmByD,EAAAA,UAAgB,CAGvC,CAAC,CAAEjG,UAAAA,CAAS,CAAExH,SAAAA,CAAQ,CAAE,GAAG0N,EAAO,CAAEC,IACpC,GAAApH,EAAAC,GAAA,EAACgH,EAAAA,EAAyB,EAAChG,UAAU,gBACnC,GAAAjB,EAAAe,IAAA,EAACkG,EAAAA,EAA0B,EACzBG,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,+HACApG,GAED,GAAGkG,CAAK,WAER1N,EACD,GAAAuG,EAAAC,GAAA,EAACsH,EAAAA,CAAUA,CAAAA,CAACtG,UAAU,4DAI5BwC,CAAAA,EAAiB6D,WAAW,CAAGL,EAAAA,EAA0B,CAACK,WAAW,CAErE,IAAMxD,EAAmBoD,EAAAA,UAAgB,CAGvC,CAAC,CAAEjG,UAAAA,CAAS,CAAExH,SAAAA,CAAQ,CAAE,GAAG0N,EAAO,CAAEC,IACpC,GAAApH,EAAAC,GAAA,EAACgH,EAAAA,EAA0B,EACzBG,IAAKA,EACLnG,UAAU,2HACT,GAAGkG,CAAK,UAET,GAAAnH,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,YAAapG,YAAaxH,MAIjDqK,CAAAA,EAAiBwD,WAAW,CAAGL,EAAAA,EAA0B,CAACK,WAAW,iHClDrE,IAAME,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,EACpB,4JACA,CACEC,SAAU,CACRtH,QAAS,CACPuH,QAAS,gCACTC,YACE,yFACJ,CACF,EACAC,gBAAiB,CACfzH,QAAS,SACX,CACF,GAGI2D,EAAQmD,EAAAA,UAAgB,CAG5B,CAAC,CAAEjG,UAAAA,CAAS,CAAEb,QAAAA,CAAO,CAAE,GAAG+G,EAAO,CAAEC,IACnC,GAAApH,EAAAC,GAAA,EAACe,MAAAA,CACCoG,IAAKA,EACLU,KAAK,QACL7G,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAGG,EAAc,CAAEpH,QAAAA,CAAQ,GAAIa,GACzC,GAAGkG,CAAK,GAGbpD,CAAAA,EAAMuD,WAAW,CAAG,QAEpB,IAAMrD,EAAaiD,EAAAA,UAAgB,CAGjC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAAC8H,KAAAA,CACCX,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,+CAAgDpG,GAC7D,GAAGkG,CAAK,GAGblD,CAAAA,EAAWqD,WAAW,CAAG,aAEzB,IAAMpD,EAAmBgD,EAAAA,UAAgB,CAGvC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAACe,MAAAA,CACCoG,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,gCAAiCpG,GAC9C,GAAGkG,CAAK,GAGbjD,CAAAA,EAAiBoD,WAAW,CAAG,mHCnD/B,IAAMU,EAAgBP,CAAAA,EAAAA,EAAAA,CAAAA,EACpB,uLACA,CACEC,SAAU,CACRtH,QAAS,CACPuH,QAAS,wDACTM,UAAW,wDACXlG,QAAS,wDACTmG,UAAW,4DACXzF,MAAO,gEACP0F,QAAS,wDACTP,YACE,gEACFQ,QAAS,iBACX,CACF,EACAP,gBAAiB,CACfzH,QAAS,SACX,CACF,GAOF,SAASyD,EAAM,CAAE5C,UAAAA,CAAS,CAAEb,QAAAA,CAAO,CAAE,GAAG+G,EAAmB,EACzD,MACE,GAAAnH,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAGW,EAAc,CAAE5H,QAAAA,CAAQ,GAAIa,GAAa,GAAGkG,CAAK,EAExE,oIC/BA,IAAM5C,EAAQ2C,EAAAA,UAAgB,CAG5B,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,yCACb,GAAAjB,EAAAC,GAAA,EAACoI,QAAAA,CACCjB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,gCAAiCpG,GAC9C,GAAGkG,CAAK,KAIf5C,CAAAA,EAAM+C,WAAW,CAAG,QAEpB,IAAM9C,EAAc0C,EAAAA,UAAgB,CAGlC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAACqI,QAAAA,CAAMlB,IAAKA,EAAKnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,GAAIpG,GAAa,GAAGkG,CAAK,GAE1D3C,CAAAA,EAAY8C,WAAW,CAAG,cAE1B,IAAM3C,EAAYuC,EAAAA,UAAgB,CAGhC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAACsI,QAAAA,CACCnB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,6BAA8BpG,GAC3C,GAAGkG,CAAK,GAGbxC,CAAAA,EAAU2C,WAAW,CAAG,YAexBkB,EAboBtB,UAAgB,CAGlC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAACwI,QAAAA,CACCrB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,0DACApG,GAED,GAAGkG,CAAK,IAGDG,WAAW,CAAG,cAE1B,IAAM7C,EAAWyC,EAAAA,UAAgB,CAG/B,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAACyI,KAAAA,CACCtB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,qEACApG,GAED,GAAGkG,CAAK,GAGb1C,CAAAA,EAAS6C,WAAW,CAAG,WAEvB,IAAM5C,EAAYwC,EAAAA,UAAgB,CAGhC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAAC0I,KAAAA,CACCvB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,mGACApG,GAED,GAAGkG,CAAK,GAGbzC,CAAAA,EAAU4C,WAAW,CAAG,YAExB,IAAM1C,EAAYsC,EAAAA,UAAgB,CAGhC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAAC2I,KAAAA,CACCxB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,iDAAkDpG,GAC/D,GAAGkG,CAAK,GAGbvC,CAAAA,EAAU0C,WAAW,CAAG,YAYxBuB,EAVqB3B,UAAgB,CAGnC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAApH,EAAAC,GAAA,EAAC6I,UAAAA,CACC1B,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,qCAAsCpG,GACnD,GAAGkG,CAAK,IAGAG,WAAW,CAAG,uFCrGpB,eAAezF,EACpBkH,CAA2B,EAE3B,GAAI,CACF,IAAMC,EAAW,MAAMC,EAAAA,CAAKA,CAACC,GAAG,CAC9B,CAAC,2BAA2B,EAAEH,EAAW,CAAC,CAC1C,CAAC,GAGH,MAAOI,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBH,EAC3B,CAAE,MAAOvG,EAAO,CACd,MAAO2G,CAAAA,EAAAA,EAAAA,CAAAA,EAAuB3G,EAChC,CACF,0ECdO,IAAMS,EAAmB,MAC9BzC,EACAwC,KAEA,GAAI,CACF,IAAMjB,EAAM,MAAMiH,EAAAA,CAAKA,CAACC,GAAG,CAAC,CAAC,YAAY,EAAEjG,EAAK,CAAC,EAAExC,EAAG,CAAC,CAAE,CAAC,GAC1D,MAAO0I,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBnH,EAC3B,CAAE,MAAOS,EAAO,CACd,MAAO2G,CAAAA,EAAAA,EAAAA,CAAAA,EAAuB3G,EAChC,CACF,+FCTA4G,EAAA,2BAEAC,EAAA,SAAAC,CAAA,EACA,IAAAC,EAAAD,EAAAC,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAnF,QAAc,MAAqBmF,EAAAC,aAAmB,SAChGvM,EAAA,+dACAmJ,KAAAkD,CACA,GACA,EAEAG,EAAA,SAAAC,CAAA,EACA,IAAAJ,EAAAI,EAAAJ,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAnF,QAAc,MAAqBmF,EAAAC,aAAmB,SAChGvM,EAAA,yFACA0M,OAAAL,EACAM,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBP,EAAAC,aAAmB,SACtCvM,EAAA,0HACA0M,OAAAL,EACAM,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAC,EAAA,SAAAC,CAAA,EACA,IAAAV,EAAAU,EAAAV,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAnF,QAAc,MAAqBmF,EAAAC,aAAmB,SAChGS,QAAA,KACAhN,EAAA,+IACAmJ,KAAAkD,CACA,GAAmBC,EAAAC,aAAmB,SACtCvM,EAAA,2WACAmJ,KAAAkD,CACA,GACA,EAEAY,EAAA,SAAAC,CAAA,EACA,IAAAb,EAAAa,EAAAb,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAnF,QAAc,MAAqBmF,EAAAC,aAAmB,SAChGvM,EAAA,sFACA0M,OAAAL,EACAM,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBP,EAAAC,aAAmB,SACtCvM,EAAA,wHACA0M,OAAAL,EACAM,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAf,EAAAe,EAAAf,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAnF,QAAc,MAAqBmF,EAAAC,aAAmB,SAChGvM,EAAA,6PACAmJ,KAAAkD,CACA,GAAmBC,EAAAC,aAAmB,SACtCvM,EAAA,2hBACAmJ,KAAAkD,CACA,GACA,EAEAgB,EAAA,SAAAC,CAAA,EACA,IAAAjB,EAAAiB,EAAAjB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAnF,QAAc,MAAqBmF,EAAAC,aAAmB,SAChGvM,EAAA,iEACA0M,OAAAL,EACAM,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBP,EAAAC,aAAmB,SACtCS,QAAA,KACAhN,EAAA,+IACA0M,OAAAL,EACAM,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAU,EAAA,SAAAtK,CAAA,CAAAoJ,CAAA,EACA,OAAApJ,GACA,WACA,OAA0BqJ,EAAAC,aAAmB,CAAAJ,EAAA,CAC7CE,MAAAA,CACA,EAEA,cACA,OAA0BC,EAAAC,aAAmB,CAAAC,EAAA,CAC7CH,MAAAA,CACA,EAEA,YACA,OAA0BC,EAAAC,aAAmB,CAAAO,EAAA,CAC7CT,MAAAA,CACA,EAEA,cAeA,QAdA,OAA0BC,EAAAC,aAAmB,CAAAU,EAAA,CAC7CZ,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAY,EAAA,CAC7Cd,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAc,EAAA,CAC7ChB,MAAAA,CACA,EAMA,CACA,EAEA9I,EAAoC,GAAA+I,EAAAkB,UAAA,EAAU,SAAAC,CAAA,CAAAxD,CAAA,EAC9C,IAAAhH,EAAAwK,EAAAxK,OAAA,CACAoJ,EAAAoB,EAAApB,KAAA,CACArJ,EAAAyK,EAAAzK,IAAA,CACA0K,EAAa,GAAAC,EAAAC,CAAA,EAAwBH,EAAAvB,GAErC,OAAsBI,EAAAC,aAAmB,OAAQ,GAAAoB,EAAAhE,CAAA,EAAQ,GAAG+D,EAAA,CAC5DG,MAAA,6BACA5D,IAAAA,EACAX,MAAAtG,EACAuG,OAAAvG,EACA8K,QAAA,YACA3E,KAAA,MACA,GAAGoE,EAAAtK,EAAAoJ,GACH,EACA9I,CAAAA,EAAAwK,SAAA,EACA9K,QAAW+K,IAAAC,KAAe,wDAC1B5B,MAAS2B,IAAAE,MAAA,CACTlL,KAAQgL,IAAAG,SAAmB,EAAEH,IAAAE,MAAA,CAAkBF,IAAAI,MAAA,CAAgB,CAC/D,EACA7K,EAAA8K,YAAA,EACApL,QAAA,SACAoJ,MAAA,eACArJ,KAAA,IACA,EACAO,EAAA4G,WAAA,iFCvJO,OAAMlE,EAWXqI,YAAY5I,CAAyB,CAAE,CACrC,IAAI,CAACpC,EAAE,CAAGoC,GAAMpC,GAChB,IAAI,CAACH,MAAM,CAAGuC,GAAMvC,OACpB,IAAI,CAACyE,YAAY,CAAGlC,GAAMkC,cAAc2G,cACxC,IAAI,CAACtG,MAAM,CAAGuG,CAAAA,EAAAA,EAAAA,EAAAA,EAAS9I,GAAMuC,QAC7B,IAAI,CAACF,KAAK,CAAGyG,CAAAA,EAAAA,EAAAA,EAAAA,EAAS9I,GAAMqC,OAC5B,IAAI,CAACC,IAAI,CAAGwG,CAAAA,EAAAA,EAAAA,EAAAA,EAAS9I,GAAMsC,MAC3B,IAAI,CAAClD,MAAM,CAAGY,GAAMZ,OACpB,IAAI,CAAC2J,SAAS,CAAG,IAAIC,KAAKhJ,EAAK+I,SAAS,EACxC,IAAI,CAACE,SAAS,CAAG,IAAID,KAAKhJ,EAAKiJ,SAAS,CAC1C,CACF,wFCtBe,SAASC,IACtB,MACE,GAAA/L,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,kDACb,GAAAjB,EAAAC,GAAA,EAAC6E,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,yeCLakH,EAAU,OAER,SAASC,EAAsB,CAC5CxS,SAAAA,CAAQ,CAGT,EACC,MACE,GAAAyS,EAAAnL,IAAA,EAAAmL,EAAA5H,QAAA,YACE,GAAA4H,EAAAjM,GAAA,EAACd,EAAMA,CAAAA,GACN1F,IAGP,wFCde,SAASsS,IACtB,MACE,GAAA/L,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,kDACb,GAAAjB,EAAAC,GAAA,EAAC6E,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,gCCNe,SAASqH,EAAe,CACrC1S,SAAAA,CAAQ,CAGT,EACC,OAAOA,CACT,8HCNe,SAASsS,IACtB,MACE,GAAA/L,EAAAC,GAAA,EAACe,MAAAA,CAAIC,UAAU,kDACb,GAAAjB,EAAAC,GAAA,EAAC6E,EAAAA,CAAMA,CAAAA,CAAAA,IAGb", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page.tsx?85ff", "webpack://_N_E/|ssr?6901", "webpack://_N_E/?eb39", "webpack://_N_E/?1203", "webpack://_N_E/./app/(protected)/@admin/agents/[userId]/[agentId]/_components/Tabbar.tsx", "webpack://_N_E/./app/(protected)/@admin/agents/[userId]/[agentId]/kyc/page.tsx", "webpack://_N_E/./components/ui/accordion.tsx", "webpack://_N_E/./components/ui/alert.tsx", "webpack://_N_E/./components/ui/badge.tsx", "webpack://_N_E/./components/ui/table.tsx", "webpack://_N_E/./data/admin/toggleActivity.ts", "webpack://_N_E/./data/settings/kyc-settings.ts", "webpack://_N_E/./node_modules/iconsax-react/dist/esm/PercentageSquare.js", "webpack://_N_E/./types/kyc.ts", "webpack://_N_E/./app/(protected)/@admin/agents/[userId]/[agentId]/kyc/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/agents/[userId]/[agentId]/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/agents/[userId]/[agentId]/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/agents/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/agents/loading.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "\"TURBOPACK { transition: next-ssr }\";\nimport { AppPageRouteModule } from \"next/dist/server/future/route-modules/app-page/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(protected)',\n        {\n        admin: [\n        'children',\n        {\n        children: [\n        'agents',\n        {\n        children: [\n        '[userId]',\n        {\n        children: [\n        '[agentId]',\n        {\n        children: [\n        'kyc',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\kyc\\\\page.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\kyc\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\kyc\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\kyc\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"],\n        \n      }\n      ],\nchildren: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nagent: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\ncustomer: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nmerchant: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\kyc\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const originalPathname = \"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page\";\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page\",\n        pathname: \"/agents/[userId]/[agentId]/kyc\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2F(protected)%2F%40admin%2Fagents%2F%5BuserId%5D%2F%5BagentId%5D%2Fkyc%2Fpage&page=%2F(protected)%2F%40admin%2Fagents%2F%5BuserId%5D%2F%5BagentId%5D%2Fkyc%2Fpage&pagePath=private-next-app-dir%2F(protected)%2F%40admin%2Fagents%2F%5BuserId%5D%2F%5BagentId%5D%2Fkyc%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CIDEES%20SQQS%5CPAYSNAPo%5CMain%5Cfrontend%5Capp&appPaths=%2F(protected)%2F%40admin%2Fagents%2F%5BuserId%5D%2F%5BagentId%5D%2Fkyc%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/(protected)/@admin/agents/[userId]/[agentId]/kyc/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"webpack\":null,\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"analyticsId\":\"\",\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":200,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"inline\",\"remotePatterns\":[{\"protocol\":\"http\",\"hostname\":\"localhost\"},{\"protocol\":\"https\",\"hostname\":\"**\"}],\"unoptimized\":false},\"devIndicators\":{\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"optimizeFonts\":true,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"httpAgentOptions\":{\"keepAlive\":true},\"outputFileTracing\":true,\"staticPageGenerationTimeout\":60,\"swcMinify\":true,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"experimental\":{\"multiZoneDraftMode\":false,\"prerenderEarlyExit\":false,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"outputFileTracingRoot\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\",\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"adjustFontFallbacks\":false,\"adjustFontFallbacksWithSizeAdjust\":false,\"typedRoutes\":false,\"instrumentationHook\":false,\"bundlePagesExternals\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"missingSuspenseWithCSRBailout\":true,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":30,\"static\":300},\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"configFile\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest,\n            pageName: \"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page\"\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/(protected)/@admin/agents/[userId]/[agentId]/kyc/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\", webpackExports: [\"Tabbar\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\_components\\\\Tabbar.tsx\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\agents\\\\[userId]\\\\[agentId]\\\\kyc\\\\page.tsx\");\n", "\"use client\";\r\n\r\nimport { SecondaryNav } from \"@/components/common/layout/SecondaryNav\";\r\nimport Switch from \"@/components/ui/switch\";\r\nimport { toggleActivity } from \"@/data/admin/toggleActivity\";\r\nimport {\r\n  ArrowLeft2,\r\n  Candle2,\r\n  Clock,\r\n  PercentageSquare,\r\n  ShieldSecurity,\r\n  Sms,\r\n  UserEdit,\r\n} from \"iconsax-react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  useParams,\r\n  usePathname,\r\n  useRouter,\r\n  useSearchParams,\r\n} from \"next/navigation\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\nimport { mutate } from \"swr\";\r\n\r\nexport function Tabbar() {\r\n  const params = useParams();\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n\r\n  const { t } = useTranslation();\r\n\r\n  const tabs = [\r\n    {\r\n      title: t(\"Account Details\"),\r\n      icon: <UserEdit size=\"24\" variant=\"Bulk\" />,\r\n      href: `/agents/${params?.userId}/${params?.agentId}?${searchParams.toString()}`,\r\n      id: \"__DEFAULT__\",\r\n    },\r\n    {\r\n      title: t(\"Charges/Commissions\"),\r\n      icon: <PercentageSquare size=\"24\" variant=\"Bulk\" />,\r\n      href: `/agents/${params?.userId}/${params?.agentId}/commissions?${searchParams.toString()}`,\r\n      id: \"commissions\",\r\n    },\r\n    {\r\n      title: t(\"Fees\"),\r\n      icon: <PercentageSquare size=\"24\" variant=\"Bulk\" />,\r\n      href: `/agents/${params?.userId}/${params?.agentId}/fees?${searchParams.toString()}`,\r\n      id: \"fees\",\r\n    },\r\n    {\r\n      title: t(\"Transactions\"),\r\n      icon: <Clock size=\"24\" variant=\"Bulk\" />,\r\n      href: `/agents/${params?.userId}/${params?.agentId}/transactions?${searchParams.toString()}`,\r\n      id: \"transactions\",\r\n    },\r\n\r\n    {\r\n      title: t(\"KYC\"),\r\n      icon: <ShieldSecurity size=\"24\" variant=\"Bulk\" />,\r\n      href: `/agents/${params?.userId}/${params?.agentId}/kyc?${searchParams.toString()}`,\r\n      id: \"kyc\",\r\n    },\r\n    {\r\n      title: t(\"Permissions\"),\r\n      icon: <Candle2 size=\"24\" variant=\"Bulk\" />,\r\n      href: `/agents/${params?.userId}/${params?.agentId}/permissions?${searchParams.toString()}`,\r\n      id: \"permissions\",\r\n    },\r\n    {\r\n      title: t(\"Send Email\"),\r\n      icon: <Sms size=\"24\" variant=\"Bulk\" />,\r\n      href: `/agents/${params?.userId}/${params?.agentId}/send-email?${searchParams.toString()}`,\r\n      id: \"send-email\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4\">\r\n      <div className=\"mb-4 flex flex-wrap items-center justify-between gap-2\">\r\n        <ul className=\"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center\">\r\n          <li>\r\n            <Link\r\n              href=\"/agents/list\"\r\n              className=\"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4\"\r\n            >\r\n              <ArrowLeft2 className=\"size-4 sm:size-6\" />\r\n              {t(\"Back\")}\r\n            </Link>\r\n          </li>\r\n          <li className=\"line-clamp-1 whitespace-nowrap\">\r\n            / {searchParams.get(\"name\")}{\" \"}\r\n          </li>\r\n          <li className=\"line-clamp-1 whitespace-nowrap\">\r\n            / {t(\"Agents\")} #{params.agentId}\r\n          </li>\r\n        </ul>\r\n        <div className=\"ml-auto inline-flex items-center gap-2 text-sm sm:text-base\">\r\n          <span>{t(\"Active\")}</span>\r\n          <Switch\r\n            defaultChecked={searchParams.get(\"active\") === \"1\"}\r\n            className=\"data-[state=unchecked]:bg-muted\"\r\n            onCheckedChange={(checked) => {\r\n              toast.promise(toggleActivity(params.userId as string), {\r\n                loading: t(\"Loading...\"),\r\n                success: (res) => {\r\n                  if (!res.status) throw new Error(res.message);\r\n                  const sp = new URLSearchParams(searchParams);\r\n                  mutate(`/admin/agents/${params.agentId}`);\r\n                  sp.set(\"active\", checked ? \"1\" : \"0\");\r\n                  router.push(`${pathname}?${sp.toString()}`);\r\n                  return res.message;\r\n                },\r\n                error: (err) => err.message,\r\n              });\r\n            }}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <SecondaryNav tabs={tabs} />\r\n    </div>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport { Case } from \"@/components/common/Case\";\r\nimport { Loader } from \"@/components/common/Loader\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { kycAcceptDecline } from \"@/data/settings/kyc-settings\";\r\nimport { useSWR } from \"@/hooks/useSWR\";\r\nimport { KYC } from \"@/types/kyc\";\r\nimport {\r\n  CloseCircle,\r\n  DocumentDownload,\r\n  Eye,\r\n  Shield,\r\n  ShieldCross,\r\n  ShieldSearch,\r\n  ShieldTick,\r\n  TickCircle,\r\n} from \"iconsax-react\";\r\nimport Image from \"next/image\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport default function KFCSettings() {\r\n  const { t } = useTranslation();\r\n  const params = useParams(); // get customerId from params\r\n  // fetch user by id\r\n  const { data, isLoading, mutate } = useSWR(`/admin/users/${params.userId}`);\r\n\r\n  // toggling kyc\r\n  const handleKYC = (id: string | number, type: \"accept\" | \"decline\") => {\r\n    toast.promise(kycAcceptDecline(id, type), {\r\n      loading: t(\"Loading...\"),\r\n      success: (res) => {\r\n        if (!res.status) throw new Error(res.message);\r\n        mutate(data);\r\n        return res.message;\r\n      },\r\n      error: (err) => err.message,\r\n    });\r\n  };\r\n\r\n  const kyc = data?.data?.kyc ? new KYC(data.data.kyc) : null;\r\n\r\n  return (\r\n    <Accordion\r\n      type=\"multiple\"\r\n      defaultValue={[\"KYC_STATUS\", \"DOCUMENT_INFORMATION\"]}\r\n    >\r\n      <div className=\"flex flex-col gap-4 p-4\">\r\n        <div className=\"rounded-xl border border-border bg-background\">\r\n          <AccordionItem value=\"KYC_STATUS\" className=\"border-none px-4 py-0\">\r\n            <AccordionTrigger className=\"py-6 hover:no-underline\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <p className=\"text-base font-medium leading-[22px]\">\r\n                  {t(\"KYC Status\")}\r\n                </p>\r\n\r\n                {/* If KYC data is not found, display 'Awaiting Status' */}\r\n                <Case condition={!kyc}>\r\n                  <Badge className=\"h-5 bg-foreground text-[10px] text-background\">\r\n                    {t(\"Awaiting submission\")}\r\n                  </Badge>\r\n                </Case>\r\n\r\n                {/* If KYC status is pending, display 'Pending Status' */}\r\n                <Case condition={kyc?.status === \"pending\"}>\r\n                  <Badge className=\"h-5 bg-primary text-[10px] text-primary-foreground\">\r\n                    {t(\"Pending\")}\r\n                  </Badge>\r\n                </Case>\r\n\r\n                <Case condition={kyc?.status === \"verified\"}>\r\n                  <Badge className=\"h-5 bg-spacial-green text-[10px] text-spacial-green-foreground\">\r\n                    {t(\"Verified\")}\r\n                  </Badge>\r\n                </Case>\r\n\r\n                <Case condition={kyc?.status === \"failed\"}>\r\n                  <Badge className=\"h-5 bg-danger text-[10px] text-destructive-foreground\">\r\n                    {t(\"Rejected\")}\r\n                  </Badge>\r\n                </Case>\r\n              </div>\r\n            </AccordionTrigger>\r\n            <AccordionContent className=\"flex flex-col gap-6 border-t border-divider px-1 pt-4\">\r\n              {/* Awaiting alert type */}\r\n              <Case condition={!kyc}>\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-foreground\">\r\n                  <Shield size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"User have not submitted documents yet\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n\r\n              <Case condition={kyc?.status === \"pending\"}>\r\n                {/* Pending alert type */}\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-primary\">\r\n                  <ShieldSearch size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"Pending verification\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n\r\n              <Case condition={kyc?.status === \"verified\"}>\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-spacial-green\">\r\n                  <ShieldTick size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"Your account is verified\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n\r\n              <Case condition={kyc?.status === \"failed\"}>\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-danger\">\r\n                  <ShieldCross size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"KYC Document Rejected\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"The submitted KYC document has been rejected. Please review the document for discrepancies or invalid details and request the user to submit accurate information for verification.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </div>\r\n\r\n        <div className=\"rounded-xl border border-border bg-background\">\r\n          <AccordionItem\r\n            value=\"DOCUMENT_INFORMATION\"\r\n            className=\"border-none px-4 py-0\"\r\n          >\r\n            <AccordionTrigger className=\"py-6 hover:no-underline\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <p className=\"text-base font-medium leading-[22px]\">\r\n                  {t(\"Documents\")}\r\n                </p>\r\n              </div>\r\n            </AccordionTrigger>\r\n            <AccordionContent className=\"flex flex-col gap-6 border-t border-divider px-1 pt-4\">\r\n              {!kyc ? (\r\n                <div className=\"py-4\">\r\n                  <p className=\"text-secondary-text\">\r\n                    {t(\"KYC Documents not submitted yet\")}\r\n                  </p>\r\n                </div>\r\n              ) : (\r\n                <>\r\n                  <div className=\"max-w-[900px]\">\r\n                    <Table className=\"table-fixed\">\r\n                      <TableHeader className=\"[&_tr]:border-b-0\">\r\n                        <TableRow>\r\n                          <TableHead>{t(\"KYC\")}</TableHead>\r\n                          <TableHead>{t(\"Menu\")}</TableHead>\r\n                        </TableRow>\r\n                      </TableHeader>\r\n\r\n                      <TableBody>\r\n                        {isLoading ? (\r\n                          <TableRow>\r\n                            <TableCell colSpan={2}>\r\n                              <Loader />\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        ) : (\r\n                          <>\r\n                            <TableRow className=\"odd:bg-accent\">\r\n                              <TableCell>{t(\"Document type\")}</TableCell>\r\n                              <TableCell>\r\n                                <div className=\"flex items-center gap-1 sm:gap-10\">\r\n                                  <span className=\"hidden font-semibold sm:block\">\r\n                                    {kyc?.documentType as string}\r\n                                  </span>\r\n                                </div>\r\n                              </TableCell>\r\n                            </TableRow>\r\n                            <KYCTableRow\r\n                              title={t(\"Front image\")}\r\n                              preview={kyc?.front as string}\r\n                            />\r\n\r\n                            <KYCTableRow\r\n                              title={t(\"Back image\")}\r\n                              preview={kyc?.back as string}\r\n                            />\r\n\r\n                            <KYCTableRow\r\n                              title={t(\"Selfie\")}\r\n                              preview={kyc?.selfie as string}\r\n                            />\r\n                          </>\r\n                        )}\r\n                      </TableBody>\r\n                    </Table>\r\n                  </div>\r\n\r\n                  <Case condition={kyc.status?.toLowerCase() === \"pending\"}>\r\n                    <div className=\"flex flex-wrap items-center gap-2.5 sm:gap-4\">\r\n                      <Button\r\n                        type=\"button\"\r\n                        onClick={() => handleKYC(kyc.id, \"accept\")}\r\n                        className=\"bg-[#0B6A0B] text-white hover:bg-[#208c20]\"\r\n                      >\r\n                        <TickCircle />\r\n                        {t(\"Approve\")}\r\n                      </Button>\r\n                      <Button\r\n                        type=\"button\"\r\n                        onClick={() => handleKYC(kyc.id, \"decline\")}\r\n                        className=\"bg-[#D13438] text-white hover:bg-[#c32d32]\"\r\n                      >\r\n                        <CloseCircle />\r\n                        {t(\"Reject\")}\r\n                      </Button>\r\n                    </div>\r\n                  </Case>\r\n                </>\r\n              )}\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </div>\r\n      </div>\r\n    </Accordion>\r\n  );\r\n}\r\n\r\nfunction KYCTableRow({ title, preview }: { title: string; preview: string }) {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <TableRow className=\"odd:bg-accent\">\r\n      <TableCell>{title}</TableCell>\r\n      <TableCell>\r\n        <div className=\"flex items-center gap-1 sm:gap-10\">\r\n          <Dialog>\r\n            <DialogTrigger asChild>\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                className=\"bg-background hover:bg-muted\"\r\n              >\r\n                <Eye />\r\n                <span className=\"hidden sm:block\">{t(\"View\")}</span>\r\n              </Button>\r\n            </DialogTrigger>\r\n\r\n            <DialogContent className=\"max-w-7xl\">\r\n              <DialogHeader>\r\n                <DialogTitle> {title} </DialogTitle>\r\n                <DialogDescription className=\"hidden\" aria-hidden />\r\n              </DialogHeader>\r\n\r\n              <div className=\"relative mx-auto aspect-square w-full max-w-xl\">\r\n                {preview ? (\r\n                  <Image\r\n                    src={preview}\r\n                    alt={title}\r\n                    fill\r\n                    sizes=\"500px\"\r\n                    style={{ width: \"100%\", height: \"100%\" }}\r\n                    quality=\"90\"\r\n                    loading=\"lazy\"\r\n                    placeholder=\"blur\"\r\n                    className=\"object-contain\"\r\n                    blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mN8Vw8AAmEBb87E6jIAAAAASUVORK5CYII=\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"flex h-full w-full items-center justify-center rounded-md bg-accent\">\r\n                    <span className=\"font-semibold opacity-70\">\r\n                      {t(\"No preview\")}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </DialogContent>\r\n          </Dialog>\r\n\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            className=\"bg-background hover:bg-muted\"\r\n            asChild\r\n          >\r\n            <a href={preview} download>\r\n              <DocumentDownload />\r\n              <span className=\"hidden sm:inline-block\">{t(\"Download\")}</span>\r\n            </a>\r\n          </Button>\r\n        </div>\r\n      </TableCell>\r\n    </TableRow>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\nimport { ArrowDown2 } from \"iconsax-react\";\r\n\r\nconst Accordion = AccordionPrimitive.Root;\r\n\r\nconst AccordionItem = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\r\n>(({ className, ...props }, ref) => (\r\n  <AccordionPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\"border-b\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAccordionItem.displayName = \"AccordionItem\";\r\n\r\nconst AccordionTrigger = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Header className=\"flex\">\r\n    <AccordionPrimitive.Trigger\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ArrowDown2 className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\r\n    </AccordionPrimitive.Trigger>\r\n  </AccordionPrimitive.Header>\r\n));\r\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;\r\n\r\nconst AccordionContent = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Content\r\n    ref={ref}\r\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\r\n    {...props}\r\n  >\r\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\r\n  </AccordionPrimitive.Content>\r\n));\r\n\r\nAccordionContent.displayName = AccordionPrimitive.Content.displayName;\r\n\r\nexport { Accordion, AccordionContent, AccordionItem, AccordionTrigger };\r\n", "import { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n));\r\nAlert.displayName = \"Alert\";\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAlertTitle.displayName = \"AlertTitle\";\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAlertDescription.displayName = \"AlertDescription\";\r\n\r\nexport { Alert, AlertDescription, AlertTitle };\r\n", "import { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border-transparent bg-primary text-primary-foreground\",\r\n        secondary: \"border-transparent bg-muted text-secondary-foreground\",\r\n        success: \"border-transparent bg-success text-success-foreground\",\r\n        important: \"border-transparent bg-important text-important-foreground\",\r\n        error: \"border-transparent bg-destructive text-destructive-foreground\",\r\n        warning: \"border-transparent bg-warning text-warning-foreground\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n", "import * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = \"Table\";\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"\", className)} {...props} />\r\n));\r\nTableHeader.displayName = \"TableHeader\";\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = \"TableBody\";\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = \"TableFooter\";\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = \"TableRow\";\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = \"TableHead\";\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = \"TableCell\";\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = \"TableCaption\";\r\n\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n};\r\n", "import axios from \"@/lib/axios\";\r\nimport { ErrorResponseGenerator, ResponseGenerator } from \"@/data/response\";\r\nimport type { ReturnType } from \"@/types/return-type\";\r\n\r\nexport async function toggleActivity(\r\n  customerId: string | number,\r\n): Promise<ReturnType> {\r\n  try {\r\n    const response = await axios.put(\r\n      `/admin/users/toggle-active/${customerId}`,\r\n      {},\r\n    );\r\n\r\n    return ResponseGenerator(response);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n}\r\n", "import axios from \"@/lib/axios\";\r\nimport { ErrorResponseGenerator, ResponseGenerator } from \"@/data/response\";\r\n\r\nexport const kycAcceptDecline = async (\r\n  id: string | number,\r\n  type: \"accept\" | \"decline\",\r\n) => {\r\n  try {\r\n    const res = await axios.put(`/admin/kycs/${type}/${id}`, {});\r\n    return ResponseGenerator(res);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n};\r\n", "import { _ as _objectWithoutProperties, a as _extends } from './_rollupPluginBabelHelpers-3bc641ae.js';\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\n\nvar _excluded = [\"variant\", \"color\", \"size\"];\n\nvar Bold = function Bold(_ref) {\n  var color = _ref.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7.75 9.14c0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48 0 .81-.66 1.48-1.48 1.48a1.49 1.49 0 0 1-1.48-1.48Zm1.07 6.88c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.35 15.8c-.15.15-.34.22-.53.22Zm6.95.32c-.81 0-1.48-.66-1.48-1.48 0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48s-.66 1.48-1.48 1.48Z\",\n    fill: color\n  }));\n};\n\nvar Broken = function Broken(_ref2) {\n  var color = _ref2.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2 13.05V15c0 5 2 7 7 7h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9M8.32 15.27l6.54-6.54\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Bulk = function Bulk(_ref3) {\n  var color = _ref3.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M16.192 2h-8.37c-3.64 0-5.81 2.17-5.81 5.81v8.37c0 3.64 2.17 5.81 5.81 5.81h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81c0-3.64-2.17-5.81-5.81-5.81Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.822 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM9.23 7.66c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48a1.49 1.49 0 0 0-1.48-1.48ZM15.769 13.39c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48s-.66-1.48-1.48-1.48Z\",\n    fill: color\n  }));\n};\n\nvar Linear = function Linear(_ref4) {\n  var color = _ref4.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7ZM8.57 15.27l6.54-6.54\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.98 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.52 16.09a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Outline = function Outline(_ref5) {\n  var color = _ref5.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h6c5.43 0 7.75 2.32 7.75 7.75v6c0 5.43-2.32 7.75-7.75 7.75Zm-6-20C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25V9c0-4.61-1.64-6.25-6.25-6.25H9Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.568 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM8.98 11.108c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48a.48.48 0 0 0-.48-.48ZM15.519 16.839c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48-.21-.48-.48-.48Z\",\n    fill: color\n  }));\n};\n\nvar TwoTone = function TwoTone(_ref6) {\n  var color = _ref6.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"m8.32 15.27 6.54-6.54M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar chooseVariant = function chooseVariant(variant, color) {\n  switch (variant) {\n    case 'Bold':\n      return /*#__PURE__*/React.createElement(Bold, {\n        color: color\n      });\n\n    case 'Broken':\n      return /*#__PURE__*/React.createElement(Broken, {\n        color: color\n      });\n\n    case 'Bulk':\n      return /*#__PURE__*/React.createElement(Bulk, {\n        color: color\n      });\n\n    case 'Linear':\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n\n    case 'Outline':\n      return /*#__PURE__*/React.createElement(Outline, {\n        color: color\n      });\n\n    case 'TwoTone':\n      return /*#__PURE__*/React.createElement(TwoTone, {\n        color: color\n      });\n\n    default:\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n  }\n};\n\nvar PercentageSquare = /*#__PURE__*/forwardRef(function (_ref7, ref) {\n  var variant = _ref7.variant,\n      color = _ref7.color,\n      size = _ref7.size,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: ref,\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\"\n  }), chooseVariant(variant, color));\n});\nPercentageSquare.propTypes = {\n  variant: PropTypes.oneOf(['Linear', 'Bold', 'Broken', 'Bulk', 'Outline', 'TwoTone']),\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nPercentageSquare.defaultProps = {\n  variant: 'Linear',\n  color: 'currentColor',\n  size: '24'\n};\nPercentageSquare.displayName = 'PercentageSquare';\n\nexport { PercentageSquare as default };\n", "import { imageURL } from \"@/lib/utils\";\r\n\r\nexport class KYC {\r\n  id: number;\r\n  userId: number;\r\n  documentType: \"NID\" | \"PASSPORT\" | \"DRIVING\";\r\n  selfie?: string | undefined;\r\n  front: string;\r\n  back?: string | undefined;\r\n  status: string;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n\r\n  constructor(data: Record<string, any>) {\r\n    this.id = data?.id;\r\n    this.userId = data?.userId;\r\n    this.documentType = data?.documentType?.toUpperCase();\r\n    this.selfie = imageURL(data?.selfie);\r\n    this.front = imageURL(data?.front);\r\n    this.back = imageURL(data?.back);\r\n    this.status = data?.status;\r\n    this.createdAt = new Date(data.createdAt);\r\n    this.updatedAt = new Date(data.updatedAt);\r\n  }\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import React from \"react\";\r\nimport { Tabbar } from \"./_components/Tabbar\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport default function CustomerDetailsLayout({\r\n  children,\r\n}: {\r\n  children: Readonly<React.ReactNode>;\r\n}) {\r\n  return (\r\n    <>\r\n      <Tabbar />\r\n      {children}\r\n    </>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import React from \"react\";\r\n\r\nexport default function CustomerLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return children;\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "_self___RSC_MANIFEST", "tree", "children", "admin", "page", "Promise", "resolve", "then", "__webpack_require__", "bind", "defaultPage", "agent", "customer", "merchant", "pages", "originalPathname", "__next_app__", "loadChunk", "routeModule", "module_compiled", "AppPageRouteModule", "definition", "kind", "route_kind", "x", "APP_PAGE", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "self", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "encryption_utils", "Mo", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "action_utils", "w", "pageName", "page_stringifiedConfig_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_pagesType_app_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRmFnZW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1QmFnZW50SWQlNUQlMkZreWMlMkZwYWdlJnBhZ2U9JTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRmFnZW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1QmFnZW50SWQlNUQlMkZreWMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRmFnZW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1QmFnZW50SWQlNUQlMkZreWMlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rlc2t0b3AlNUNJREVFUyUyMFNRUVMlNUNQQVlTTkFQbyU1Q01haW4lNUNmcm9udGVuZCU1Q2FwcCZhcHBQYXRocz0lMkYocHJvdGVjdGVkKSUyRiU0MGFkbWluJTJGYWdlbnRzJTJGJTVCdXNlcklkJTVEJTJGJTVCYWdlbnRJZCU1RCUyRmt5YyUyRnBhZ2UmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0Qh_sriEnabled_false_middlewareConfig_e30_render", "render", "d", "pagesType", "page_types", "s", "APP", "dev", "appMod", "pageMod", "page_next_edge_ssr_entry_namespaceObject", "errorMod", "error500Mod", "Document", "renderToHTML", "app_render", "f", "serverActions", "subresourceIntegrityManifest", "config", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "adapter", "C", "IncrementalCache", "incremental_cache", "k", "handler", "Ta<PERSON><PERSON>", "params", "useParams", "usePathname", "router", "useRouter", "searchParams", "useSearchParams", "t", "useTranslation", "tabs", "title", "icon", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "UserEdit", "size", "variant", "href", "userId", "agentId", "toString", "id", "PercentageSquare", "Clock", "ShieldSecurity", "Candle2", "Sms", "jsxs", "div", "className", "ul", "li", "Link", "ArrowLeft2", "get", "span", "Switch", "defaultChecked", "onCheckedChange", "toast", "promise", "toggleActivity", "loading", "success", "res", "status", "message", "sp", "URLSearchParams", "mutate", "set", "checked", "push", "error", "err", "SecondaryNav", "KFCSettings", "data", "isLoading", "useSWR", "handleKYC", "type", "kycAcceptDecline", "kyc", "KYC", "Accordion", "defaultValue", "AccordionItem", "value", "AccordionTrigger", "p", "Case", "condition", "Badge", "Accordi<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shield", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDescription", "ShieldSearch", "ShieldTick", "ShieldCross", "Fragment", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "colSpan", "Loader", "documentType", "KYCTableRow", "preview", "front", "back", "selfie", "toLowerCase", "<PERSON><PERSON>", "onClick", "TickCircle", "CloseCircle", "Dialog", "DialogTrigger", "<PERSON><PERSON><PERSON><PERSON>", "Eye", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "aria-hidden", "Image", "src", "alt", "fill", "sizes", "style", "width", "height", "quality", "placeholder", "blurDataURL", "a", "download", "DocumentDownload", "AccordionPrimitive", "React", "props", "ref", "cn", "displayName", "ArrowDown2", "alertVariants", "cva", "variants", "default", "destructive", "defaultVariants", "role", "h5", "badgeVariants", "secondary", "important", "warning", "outline", "table", "thead", "tbody", "TableFooter", "tfoot", "tr", "th", "td", "TableCaption", "caption", "customerId", "response", "axios", "put", "ResponseGenerator", "ErrorResponseGenerator", "_excluded", "Bold", "_ref", "color", "react__WEBPACK_IMPORTED_MODULE_0__", "createElement", "Broken", "_ref2", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "Bulk", "_ref3", "opacity", "Linear", "_ref4", "Outline", "_ref5", "TwoTone", "_ref6", "choose<PERSON><PERSON><PERSON>", "forwardRef", "_ref7", "rest", "_rollupPluginBabelHelpers_3bc641ae_js__WEBPACK_IMPORTED_MODULE_1__", "_", "xmlns", "viewBox", "propTypes", "prop_types__WEBPACK_IMPORTED_MODULE_2___default", "oneOf", "string", "oneOfType", "number", "defaultProps", "constructor", "toUpperCase", "imageURL", "createdAt", "Date", "updatedAt", "Loading", "runtime", "CustomerDetailsLayout", "jsx_runtime", "CustomerLayout"], "sourceRoot": ""}