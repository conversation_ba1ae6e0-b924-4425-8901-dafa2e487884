(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3020],{20216:(e,t,r)=>{Promise.resolve().then(r.bind(r,80312))},80312:(e,t,r)=>{"use strict";r.d(t,{Tabbar:()=>v});var i=r(60926),a=r(29220),s=r(58387),n=r(14579),o=r(30655),l=r(34870),d=r(20852),c=r(50201),u=r(89450),f=r(12870),m=r(81584),p=r(47020),h=r(737),x=r(64947),g=r(39228);function v(){let[e,t]=a.useState(""),r=(0,x.BT)(),v=(0,x.wm)(),b=(0,x.lr)(),{t:y}=(0,g.$G)(),w=[{title:y("Deposit Gateways"),icon:(0,i.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:"/settings",id:"__DEFAULT__"},{title:y("Withdraw Methods"),icon:(0,i.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:"/settings/withdraw-methods",id:"withdraw-methods"},{title:y("Plugins"),icon:(0,i.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:"/settings/plugins",id:"plugins"},{title:y("Services"),icon:(0,i.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:"/settings/services",id:"services"},{title:y("Currency"),icon:(0,i.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:"/settings/currencies",id:"currencies"},{title:y("Site Settings"),icon:(0,i.jsx)(f.Z,{size:"24",variant:"Bulk"}),href:"/settings/site-settings",id:"site-settings"},{title:y("Login Sessions"),icon:(0,i.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:"/settings/login-sessions",id:"login-sessions"}];return a.useLayoutEffect(()=>{r?t("gateways"===r?"__DEFAULT__":r):t("__DEFAULT__")},[r]),(0,i.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,i.jsx)(s.J,{condition:v.length>1,children:(0,i.jsxs)("div",{className:"line-clamp-1 inline-flex max-w-full items-center gap-2 px-0 pb-4 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,i.jsxs)(h.Z,{href:"__DEFAULT__"===e?"/settings":`/settings/${e}`,className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,i.jsx)(p.Z,{className:"size-4 sm:size-6"}),y("Back")]}),(0,i.jsxs)("span",{className:"line-clamp-1 flex items-center gap-1 whitespace-nowrap text-sm font-semibold text-secondary-text",children:["/"," ","create"===v[1]?"Create withdraw method":b.get("name")]})]})}),(0,i.jsx)(n.a,{tabs:w,defaultSegment:"gateways"})]})}},7602:(e,t,r)=>{"use strict";r.d(t,{S:()=>l});var i=r(60926),a=r(65091),s=r(28277),n=r(29220),o=r(51474);function l({defaultValue:e,onChange:t,className:r,children:l,disabled:d=!1,id:c}){let[u,f]=n.useState(e),{getRootProps:m,getInputProps:p}=(0,o.uI)({onDrop:e=>{let r=e?.[0];r&&(t(r),f(URL.createObjectURL(r)))},disabled:d});return(0,i.jsxs)("div",{...m({className:(0,a.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r)}),children:[!!u&&(0,i.jsx)(s.Z,{src:u,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,i.jsx)("input",{id:c,...p()}),!u&&(0,i.jsx)("div",{children:l})]})}},5670:(e,t,r)=>{"use strict";r.d(t,{X:()=>s});var i=r(60926),a=r(65091);function s({className:e}){return(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,a.ZP)("fill-primary",e),children:[(0,i.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},59571:(e,t,r)=>{"use strict";r.d(t,{Qd:()=>d,UQ:()=>l,o4:()=>c,vF:()=>u});var i=r(60926),a=r(73837),s=r(29220),n=r(65091),o=r(86059);let l=a.fC,d=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)(a.ck,{ref:r,className:(0,n.ZP)("border-b",e),...t}));d.displayName="AccordionItem";let c=s.forwardRef(({className:e,children:t,...r},s)=>(0,i.jsx)(a.h4,{className:"flex",children:(0,i.jsxs)(a.xz,{ref:s,className:(0,n.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...r,children:[t,(0,i.jsx)(o.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));c.displayName=a.xz.displayName;let u=s.forwardRef(({className:e,children:t,...r},s)=>(0,i.jsx)(a.VY,{ref:s,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:(0,i.jsx)("div",{className:(0,n.ZP)("pb-4 pt-0",e),children:t})}));u.displayName=a.VY.displayName},34451:(e,t,r)=>{"use strict";r.d(t,{NI:()=>x,Wi:()=>u,l0:()=>d,lX:()=>h,xJ:()=>p,zG:()=>g});var i=r(60926),a=r(62001),s=r(29220),n=r(45475),o=r(66817),l=r(65091);let d=n.RV,c=s.createContext({}),u=({...e})=>(0,i.jsx)(c.Provider,{value:{name:e.name},children:(0,i.jsx)(n.Qr,{...e})}),f=()=>{let e=s.useContext(c),t=s.useContext(m),{getFieldState:r,formState:i}=(0,n.Gc)(),a=r(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...a}},m=s.createContext({}),p=s.forwardRef(({className:e,...t},r)=>{let a=s.useId();return(0,i.jsx)(m.Provider,{value:{id:a},children:(0,i.jsx)("div",{ref:r,className:(0,l.ZP)("space-y-2",e),...t})})});p.displayName="FormItem";let h=s.forwardRef(({className:e,required:t,...r},a)=>{let{error:s,formItemId:n}=f();return(0,i.jsx)("span",{children:(0,i.jsx)(o.Z,{ref:a,className:(0,l.ZP)(s&&"text-base font-medium text-destructive",e),htmlFor:n,...r})})});h.displayName="FormLabel";let x=s.forwardRef(({...e},t)=>{let{error:r,formItemId:s,formDescriptionId:n,formMessageId:o}=f();return(0,i.jsx)(a.g7,{ref:t,id:s,"aria-describedby":r?`${n} ${o}`:`${n}`,"aria-invalid":!!r,...e})});x.displayName="FormControl",s.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:a}=f();return(0,i.jsx)("p",{ref:r,id:a,className:(0,l.ZP)("text-sm text-muted-foreground",e),...t})}).displayName="FormDescription";let g=s.forwardRef(({className:e,children:t,...r},a)=>{let{error:s,formMessageId:n}=f(),o=s?String(s?.message):t;return o?(0,i.jsx)("p",{ref:a,id:n,className:(0,l.ZP)("text-sm font-medium text-destructive",e),...r,children:o}):null});g.displayName="FormMessage"},87198:(e,t,r)=>{"use strict";r.d(t,{O:()=>s});var i=r(60926),a=r(65091);function s({className:e,...t}){return(0,i.jsx)("div",{className:(0,a.ZP)("animate-pulse rounded-md bg-muted",e),...t})}},75643:(e,t,r)=>{"use strict";r.d(t,{O:()=>s});var i=r(1181),a=r(25694);let s=async(e,t)=>{try{let r=await i.Z.post(`/admin/${t}/add-to-blacklist`,e);return(0,a.B)(r)}catch(e){return(0,a.D)(e)}}},9885:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var i=r(1181),a=r(25694);let s=async(e,t)=>{try{let r=await i.Z.post(`/admin/${t}/remove-from-blacklist`,e);return(0,a.B)(r)}catch(e){return(0,a.D)(e)}}},4825:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});var i=r(35114),a=r(1181),s=r(32898);function n(){let{data:e,isLoading:t,error:r,mutate:n}=(0,s.ZP)("/currencies",e=>a.Z.get(e)),o=e?.data;return{currencies:o?o.map(e=>new i.F(e)):[],isLoading:t,error:r,mutate:n}}},72382:(e,t,r)=>{"use strict";r.d(t,{K:()=>n});var i=r(93633);let a=["image/jpeg","image/jpg","image/png","image/svg+xml"],s=["image/x-icon","image/vnd.microsoft.icon","image/png"],n=i.z.union([i.z.string(),i.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&a.includes(e.type),"Invalid file format. Please upload a .jpg, .jpeg, .png or .svg file.");i.z.union([i.z.string(),i.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&s.includes(e.type),"Invalid file format. Please upload a .ico or .png file.")},35114:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});class i{constructor(e){this.formatter=e=>{let t=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),r=t.formatToParts(e),i=r.find(e=>"currency"===e.type)?.value??this.code,a=t.format(e),s=a.substring(i.length).trim();return{currencyCode:this.code,currencySymbol:i,formattedAmount:a,amountText:s}},this.id=e?.id,this.name=e?.name,this.code=e?.code,this.logo=e?.logo??"",this.usdRate=e?.usdRate,this.acceptApiRate=!!e?.acceptApiRate,this.isCrypto=!!e?.isCrypto,this.active=!!e?.active,this.metaData=e?.metaData,this.minAmount=e?.minAmount,this.kycLimit=e?.kycLimit,this.maxAmount=e?.maxAmount,this.dailyTransferAmount=e?.dailyTransferAmount,this.dailyTransferLimit=e?.dailyTransferLimit,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt)}format(e){let{currencySymbol:t,amountText:r}=this.formatter(e);return`${r} ${t}`}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}}},15171:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(42416);r(87908);let a=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\_components\Tabbar.tsx#Tabbar`);function s({children:e}){return(0,i.jsxs)("div",{className:"overflow-y-auto",children:[(0,i.jsx)(a,{}),(0,i.jsx)("div",{className:"p-4",children:e})]})}},5897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(42416),a=r(21237);function s(){return(0,i.jsx)("div",{className:"flex justify-center py-10",children:(0,i.jsx)(a.a,{})})}},73837:(e,t,r)=>{"use strict";r.d(t,{VY:()=>en,h4:()=>ea,ck:()=>ei,fC:()=>er,xz:()=>es});var i=r(29220),a=r(16769),s=r(56556),n=r(19677),o=r(58408),l=r(68878),d=r(22316),c=r(57730),u=r(90027),f=r(72814),m=r(60926),p="Collapsible",[h,x]=(0,a.b)(p),[g,v]=h(p),b=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:a,defaultOpen:s,disabled:n,onOpenChange:o,...c}=e,[u,h]=(0,l.T)({prop:a,defaultProp:s??!1,onChange:o,caller:p});return(0,m.jsx)(g,{scope:r,disabled:n,contentId:(0,f.M)(),open:u,onOpenToggle:i.useCallback(()=>h(e=>!e),[h]),children:(0,m.jsx)(d.WV.div,{"data-state":A(u),"data-disabled":n?"":void 0,...c,ref:t})})});b.displayName=p;var y="CollapsibleTrigger",w=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,...i}=e,a=v(y,r);return(0,m.jsx)(d.WV.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":A(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...i,ref:t,onClick:(0,o.M)(e.onClick,a.onOpenToggle)})});w.displayName=y;var j="CollapsibleContent",C=i.forwardRef((e,t)=>{let{forceMount:r,...i}=e,a=v(j,e.__scopeCollapsible);return(0,m.jsx)(u.z,{present:r||a.open,children:({present:e})=>(0,m.jsx)(N,{...i,ref:t,present:e})})});C.displayName=j;var N=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:a,children:s,...o}=e,l=v(j,r),[u,f]=i.useState(a),p=i.useRef(null),h=(0,n.e)(t,p),x=i.useRef(0),g=x.current,b=i.useRef(0),y=b.current,w=l.open||u,C=i.useRef(w),N=i.useRef(void 0);return i.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,c.b)(()=>{let e=p.current;if(e){N.current=N.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();x.current=t.height,b.current=t.width,C.current||(e.style.transitionDuration=N.current.transitionDuration,e.style.animationName=N.current.animationName),f(a)}},[l.open,a]),(0,m.jsx)(d.WV.div,{"data-state":A(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!w,...o,ref:h,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":y?`${y}px`:void 0,...e.style},children:w&&s})});function A(e){return e?"open":"closed"}var R=r(3237),k="Accordion",_=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[D,F,Z]=(0,s.B)(k),[z,I]=(0,a.b)(k,[Z,x]),P=x(),L=i.forwardRef((e,t)=>{let{type:r,...i}=e;return(0,m.jsx)(D.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,m.jsx)($,{...i,ref:t}):(0,m.jsx)(M,{...i,ref:t})})});L.displayName=k;var[S,T]=z(k),[B,E]=z(k,{collapsible:!1}),M=i.forwardRef((e,t)=>{let{value:r,defaultValue:a,onValueChange:s=()=>{},collapsible:n=!1,...o}=e,[d,c]=(0,l.T)({prop:r,defaultProp:a??"",onChange:s,caller:k});return(0,m.jsx)(S,{scope:e.__scopeAccordion,value:i.useMemo(()=>d?[d]:[],[d]),onItemOpen:c,onItemClose:i.useCallback(()=>n&&c(""),[n,c]),children:(0,m.jsx)(B,{scope:e.__scopeAccordion,collapsible:n,children:(0,m.jsx)(O,{...o,ref:t})})})}),$=i.forwardRef((e,t)=>{let{value:r,defaultValue:a,onValueChange:s=()=>{},...n}=e,[o,d]=(0,l.T)({prop:r,defaultProp:a??[],onChange:s,caller:k}),c=i.useCallback(e=>d((t=[])=>[...t,e]),[d]),u=i.useCallback(e=>d((t=[])=>t.filter(t=>t!==e)),[d]);return(0,m.jsx)(S,{scope:e.__scopeAccordion,value:o,onItemOpen:c,onItemClose:u,children:(0,m.jsx)(B,{scope:e.__scopeAccordion,collapsible:!0,children:(0,m.jsx)(O,{...n,ref:t})})})}),[V,U]=z(k),O=i.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:a,dir:s,orientation:l="vertical",...c}=e,u=i.useRef(null),f=(0,n.e)(u,t),p=F(r),h="ltr"===(0,R.gm)(s),x=(0,o.M)(e.onKeyDown,e=>{if(!_.includes(e.key))return;let t=e.target,r=p().filter(e=>!e.ref.current?.disabled),i=r.findIndex(e=>e.ref.current===t),a=r.length;if(-1===i)return;e.preventDefault();let s=i,n=a-1,o=()=>{(s=i+1)>n&&(s=0)},d=()=>{(s=i-1)<0&&(s=n)};switch(e.key){case"Home":s=0;break;case"End":s=n;break;case"ArrowRight":"horizontal"===l&&(h?o():d());break;case"ArrowDown":"vertical"===l&&o();break;case"ArrowLeft":"horizontal"===l&&(h?d():o());break;case"ArrowUp":"vertical"===l&&d()}let c=s%a;r[c].ref.current?.focus()});return(0,m.jsx)(V,{scope:r,disabled:a,direction:s,orientation:l,children:(0,m.jsx)(D.Slot,{scope:r,children:(0,m.jsx)(d.WV.div,{...c,"data-orientation":l,ref:f,onKeyDown:a?void 0:x})})})}),W="AccordionItem",[H,Q]=z(W),G=i.forwardRef((e,t)=>{let{__scopeAccordion:r,value:i,...a}=e,s=U(W,r),n=T(W,r),o=P(r),l=(0,f.M)(),d=i&&n.value.includes(i)||!1,c=s.disabled||e.disabled;return(0,m.jsx)(H,{scope:r,open:d,disabled:c,triggerId:l,children:(0,m.jsx)(b,{"data-orientation":s.orientation,"data-state":et(d),...o,...a,ref:t,disabled:c,open:d,onOpenChange:e=>{e?n.onItemOpen(i):n.onItemClose(i)}})})});G.displayName=W;var Y="AccordionHeader",K=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...i}=e,a=U(k,r),s=Q(Y,r);return(0,m.jsx)(d.WV.h3,{"data-orientation":a.orientation,"data-state":et(s.open),"data-disabled":s.disabled?"":void 0,...i,ref:t})});K.displayName=Y;var J="AccordionTrigger",X=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...i}=e,a=U(k,r),s=Q(J,r),n=E(J,r),o=P(r);return(0,m.jsx)(D.ItemSlot,{scope:r,children:(0,m.jsx)(w,{"aria-disabled":s.open&&!n.collapsible||void 0,"data-orientation":a.orientation,id:s.triggerId,...o,...i,ref:t})})});X.displayName=J;var q="AccordionContent",ee=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...i}=e,a=U(k,r),s=Q(q,r),n=P(r);return(0,m.jsx)(C,{role:"region","aria-labelledby":s.triggerId,"data-orientation":a.orientation,...n,...i,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=q;var er=L,ei=G,ea=K,es=X,en=ee}}]);
//# sourceMappingURL=3020.js.map