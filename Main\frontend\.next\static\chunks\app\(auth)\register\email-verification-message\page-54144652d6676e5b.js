(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39315],{2538:function(e,t,n){Promise.resolve().then(n.bind(n,90153))},15054:function(e,t,n){"use strict";n.d(t,{h:function(){return s}});var r=n(57437);function s(e){let{title:t,subTitle:n}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{className:"mb-2.5 text-sm text-secondary-text",children:n}),(0,r.jsx)("h1",{className:"text-[28px] font-medium leading-10 md:text-[32px]",children:t})]})}},90153:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return b}});var r=n(57437),s=n(15054),i=n(41709),a=n(3882),o=n(65613),l=n(62869),d=n(6512),u=n(56766),c=n(21251),v=n(19571),h=n(90433),m=n(27168),f=n(22291),p=n(27648),g=n(99376),y=n(2265),x=n(43949),w=n(14438);function b(){let[e,t]=(0,y.useTransition)(),n=(0,g.useSearchParams)().get("email"),{t:b}=(0,x.$G)(),{siteName:C}=(0,c.T)();return(0,y.useLayoutEffect)(()=>{n||(0,g.permanentRedirect)("/signin")},[]),(0,r.jsxs)("div",{className:"container mt-10 max-w-[716px] px-4 py-6",children:[(0,r.jsx)(s.h,{title:b("Please verify your email"),subTitle:b("Welcome to {{siteName}}, let's get start",{siteName:C})}),(0,r.jsx)("div",{className:"my-6 flex h-[5px] items-center",children:(0,r.jsx)(d.Z,{className:"bg-divider"})}),(0,r.jsxs)(o.bZ,{className:"border-0 bg-background p-3 shadow-default",children:[(0,r.jsx)(v.Z,{size:"16",color:"#107C10",variant:"Bold",className:"-mt-0.5"}),(0,r.jsx)(o.Cd,{className:"text-sm font-semibold leading-5 text-foreground",children:b("Verification link sent to your email")}),(0,r.jsx)(o.X,{className:"py-1.5 text-sm font-normal leading-5",children:b("We’ve sent you a link to the email address you provided, please click on the link to verify your identity.")})]}),(0,r.jsxs)("div",{className:"mt-6 flex flex-col items-center justify-between gap-4 sm:flex-row",children:[(0,r.jsx)(l.z,{variant:"outline",type:"button",className:"order-2 h-10 w-full text-base font-medium leading-[22px] text-foreground sm:order-1 sm:max-w-[102px]",asChild:!0,children:(0,r.jsxs)(p.default,{href:"/register",children:[(0,r.jsx)(h.Z,{size:"24"}),b("Back")]})}),(0,r.jsxs)("div",{className:"order-1 flex w-full flex-col items-center justify-end gap-4 sm:order-2 sm:flex-row",children:[(0,r.jsxs)(l.z,{type:"button",variant:"outline",onClick:()=>{t(async()=>{let e=await (0,u.Pr)(null!=n?n:"");e&&e.status&&w.toast.success("Verification Email Sent")})},disabled:e,className:"w-full rounded-lg px-4 py-2 text-foreground sm:w-auto",children:[(0,r.jsxs)(i.J,{condition:!e,children:[(0,r.jsx)(m.Z,{size:15}),b("Resend code")]}),(0,r.jsxs)(i.J,{condition:e,children:[(0,r.jsx)(a.Z,{}),b("Processing...")]})]}),(0,r.jsx)(l.z,{type:"button",className:"w-full rounded-[8px] px-4 py-2 text-base font-medium leading-[22px] sm:max-w-[286px]",asChild:!0,children:(0,r.jsxs)(p.default,{href:"/signin",prefetch:!1,children:[b("Sign in"),(0,r.jsx)(f.Z,{size:"16"})]})})]})]})]})}},41709:function(e,t,n){"use strict";function r(e){let{condition:t,children:n}=e;return t?n:null}n.d(t,{J:function(){return r}}),n(2265)},80114:function(e,t,n){"use strict";n.d(t,{default:function(){return o}});var r=n(57437),s=n(85487),i=n(94508),a=n(43949);function o(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,r.jsx)("div",{className:(0,i.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,r.jsx)(s.Loader,{title:n("Loading..."),className:"text-foreground"})})}},85487:function(e,t,n){"use strict";n.d(t,{Loader:function(){return a}});var r=n(57437),s=n(94508),i=n(43949);function a(e){let{title:t="Loading...",className:n}=e,{t:a}=(0,i.$G)();return(0,r.jsxs)("div",{className:(0,s.ZP)("flex items-center gap-1 text-sm text-foreground",n),children:[(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-inherit",children:a(t)})]})}},3882:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var r=n(57437);function s(){return(0,r.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}},65613:function(e,t,n){"use strict";n.d(t,{Cd:function(){return d},X:function(){return u},bZ:function(){return l}});var r=n(57437),s=n(90535),i=n(2265),a=n(94508);let o=(0,s.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=i.forwardRef((e,t)=>{let{className:n,variant:s,...i}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,a.ZP)(o({variant:s}),n),...i})});l.displayName="Alert";let d=i.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("h5",{ref:t,className:(0,a.ZP)("mb-1 font-medium leading-none tracking-tight",n),...s})});d.displayName="AlertTitle";let u=i.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.ZP)("text-sm [&_p]:leading-relaxed",n),...s})});u.displayName="AlertDescription"},62869:function(e,t,n){"use strict";n.d(t,{d:function(){return l},z:function(){return d}});var r=n(57437),s=n(37053),i=n(90535),a=n(2265),o=n(94508);let l=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:n,variant:i,size:a,asChild:d=!1,...u}=e,c=d?s.g7:"button";return(0,r.jsx)(c,{className:(0,o.ZP)(l({variant:i,size:a,className:n})),ref:t,...u})});d.displayName="Button"},6512:function(e,t,n){"use strict";var r=n(57437),s=n(55156),i=n(2265),a=n(94508);let o=i.forwardRef((e,t)=>{let{className:n,orientation:i="horizontal",decorative:o=!0,...l}=e;return(0,r.jsx)(s.f,{ref:t,decorative:o,orientation:i,className:(0,a.ZP)("shrink-0 bg-divider","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",n),...l})});o.displayName=s.f.displayName,t.Z=o},17062:function(e,t,n){"use strict";n.d(t,{Z:function(){return f},O:function(){return m}});var r=n(57437),s=n(80114);n(83079);var i=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=n(31117),o=n(79981),l=n(78040),d=n(83130);class u{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var c=n(99376),v=n(2265);let h=v.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),m=()=>v.useContext(h);function f(e){let{children:t}=e,[n,m]=v.useState("Desktop"),[f,p]=v.useState(!1),[g,y]=v.useState(),{data:x,isLoading:w,error:b,mutate:C}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:A,isLoading:N}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:j,isLoading:I}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),k=(0,c.useRouter)(),S=(0,c.usePathname)();v.useEffect(()=>{(async()=>{m((await i()).deviceType)})()},[]),v.useEffect(()=>{let e=()=>{let e=window.innerWidth;m(e<768?"Mobile":e<1024?"Tablet":"Desktop"),p(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),v.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");y(new u(e))}catch(e){}})()},[]),v.useLayoutEffect(()=>{b&&!l.sp.includes(S)&&k.push("/signin")},[b]);let L=v.useMemo(()=>{var e,t,r;return{isAuthenticate:!!(null==x?void 0:null===(e=x.data)||void 0===e?void 0:e.login),auth:(null==x?void 0:null===(t=x.data)||void 0===t?void 0:t.user)?new d.n(null==x?void 0:null===(r=x.data)||void 0===r?void 0:r.user):null,isLoading:w,deviceLocation:g,refreshAuth:()=>C(x),isExpanded:f,device:n,setIsExpanded:p,branding:null==A?void 0:A.data,googleAnalytics:(null==j?void 0:j.data)?{active:null==j?void 0:j.data.active,apiKey:null==j?void 0:j.data.apiKey}:{active:!1,apiKey:""}}},[x,g,f,n]),E=!w&&!N&&!I;return(0,r.jsx)(h.Provider,{value:L,children:E?t:(0,r.jsx)(s.default,{})})}},56766:function(e,t,n){"use strict";n.d(t,{Pq:function(){return d},Pr:function(){return c},jd:function(){return l},o8:function(){return u},s8:function(){return v}});var r=n(2901),s=n(79981),i=n(78040),a=n(43577);let o=e=>{let t={...e,email:e.email,password:e.password,passwordConfirmation:e.confirmPassword,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,firstName:e.firstName,lastName:e.lastName,phone:e.phone,gender:e.title.toLowerCase(),dob:(0,r.WU)(e.dateOfBirth,"yyyy-MM-dd"),roleId:e.accountType,acceptTermsCondition:e.termAndCondition};if(void 0!==e.merchant)return{...t,merchant:{...e.merchant,name:e.merchant.name,email:e.merchant.email,proof:e.merchant.license,addressLine:e.merchant.street,zipCode:e.merchant.zipCode,countryCode:e.merchant.country,city:e.merchant.city}};if(void 0!==e.agent){var n,s,i;return{...t,agent:{...e.agent,proof:"agent",occupation:e.agent.occupation,email:e.email,name:e.agent.name,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,whatsapp:e.agent.whatsapp,agreeFundingCustomer:(null===(n=e.agent.fundingByAgentAccount)||void 0===n?void 0:n.toLowerCase())==="yes",agreeHonest:(null===(s=e.agent.honestyAgreement)||void 0===s?void 0:s.toLowerCase())==="yes",agreeRechargeCustomer:(null===(i=e.agent.rechargeAgreement)||void 0===i?void 0:i.toLowerCase())==="yes"}}}return t};async function l(e){var t,n,r,l,d,u,c,v,h,m,f,p,g,y;try{let r=await s.Z.post("".concat(i.rH.API_URL,"/auth/register"),o(e));return{statusCode:r.status,statusText:r.statusText,status:201===r.status||200===r.status,message:null!==(n=null===(t=r.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:{email:e.email}}}catch(s){let e=500,t="Internal Server Error",n="An unknown error occurred";return(0,a.IZ)(s)&&(e=null!==(f=null===(r=s.response)||void 0===r?void 0:r.status)&&void 0!==f?f:500,t=null!==(p=null===(l=s.response)||void 0===l?void 0:l.statusText)&&void 0!==p?p:"Internal Server Error",n=null!==(y=null!==(g=null===(u=s.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.message)&&void 0!==g?g:null===(m=s.response)||void 0===m?void 0:null===(h=m.data)||void 0===h?void 0:null===(v=h.messages)||void 0===v?void 0:null===(c=v[0])||void 0===c?void 0:c.message)&&void 0!==y?y:s.message),{statusCode:e,statusText:t,status:!1,message:n}}}async function d(e){var t,n,r,l,d,u,c,v,h;try{let r=await s.Z.post("".concat(i.rH.API_URL,"/auth/register"),o(e));return{statusCode:r.status,statusText:r.statusText,status:201===r.status||200===r.status,message:null!==(n=null===(t=r.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:{email:e.email}}}catch(s){let e=500,t="Internal Server Error",n="An unknown error occurred";return(0,a.IZ)(s)?(e=null!==(c=null===(r=s.response)||void 0===r?void 0:r.status)&&void 0!==c?c:500,t=null!==(v=null===(l=s.response)||void 0===l?void 0:l.statusText)&&void 0!==v?v:"Internal Server Error",n=null!==(h=null===(u=s.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.message)&&void 0!==h?h:s.message):s instanceof Error&&(n=s.message),{statusCode:e,statusText:t,status:!1,message:n}}}async function u(e){var t,n,r,l,d,u,c,v,h;try{let r=await s.Z.post("".concat(i.rH.API_URL,"/auth/register"),o(e));return{statusCode:r.status,statusText:r.statusText,status:201===r.status||200===r.status,message:null!==(n=null===(t=r.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:{email:e.email}}}catch(s){let e=500,t="Internal Server Error",n="An unknown error occurred";return(0,a.IZ)(s)?(e=null!==(c=null===(r=s.response)||void 0===r?void 0:r.status)&&void 0!==c?c:500,t=null!==(v=null===(l=s.response)||void 0===l?void 0:l.statusText)&&void 0!==v?v:"Internal Server Error",n=null!==(h=null===(u=s.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.message)&&void 0!==h?h:s.message):s instanceof Error&&(n=s.message),{statusCode:e,statusText:t,status:!1,message:n}}}async function c(e){var t,n,r,o,l,d,u,c,v;try{let r=await s.Z.post("".concat(i.rH.API_URL,"/auth/resend-verify-email"),{email:e});return{statusCode:r.status,statusText:r.statusText,status:201===r.status||200===r.status,message:null!==(n=null===(t=r.data)||void 0===t?void 0:t.message)&&void 0!==n?n:""}}catch(s){let e=500,t="Internal Server Error",n="An unknown error occurred";return(0,a.IZ)(s)?(e=null!==(u=null===(r=s.response)||void 0===r?void 0:r.status)&&void 0!==u?u:500,t=null!==(c=null===(o=s.response)||void 0===o?void 0:o.statusText)&&void 0!==c?c:"Internal Server Error",n=null!==(v=null===(d=s.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.message)&&void 0!==v?v:s.message):s instanceof Error&&(n=s.message),{statusCode:e,statusText:t,status:!1,message:n}}}async function v(e){var t,n,r,o,l,d,u,c,v;let{token:h}=e;try{let e=await s.Z.post("".concat(i.rH.API_URL,"/auth/verify-email"),{token:h});return{statusCode:e.status,statusText:e.statusText,status:201===e.status||200===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:""}}catch(s){let e=500,t="Internal Server Error",n="An unknown error occurred";return(0,a.IZ)(s)?(e=null!==(u=null===(r=s.response)||void 0===r?void 0:r.status)&&void 0!==u?u:500,t=null!==(c=null===(o=s.response)||void 0===o?void 0:o.statusText)&&void 0!==c?c:"Internal Server Error",n=null!==(v=null===(d=s.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.message)&&void 0!==v?v:s.message):s instanceof Error&&(n=s.message),{statusCode:e,statusText:t,status:!1,message:n}}}},21251:function(e,t,n){"use strict";n.d(t,{T:function(){return s}});var r=n(17062);let s=()=>{let{branding:e}=(0,r.O)();return e}},31117:function(e,t,n){"use strict";n.d(t,{d:function(){return i}});var r=n(79981),s=n(85323);let i=(e,t)=>(0,s.ZP)(e||null,e=>r.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,n){"use strict";var r=n(78040),s=n(83464);t.Z=s.default.create({baseURL:r.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,n){"use strict";n.d(t,{rH:function(){return r},sp:function(){return s}});let r={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:n(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},s=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,n){"use strict";n.d(t,{F:function(){return u},Fg:function(){return h},Fp:function(){return d},Qp:function(){return v},ZP:function(){return o},fl:function(){return l},qR:function(){return c},w4:function(){return m}});var r=n(78040),s=n(61994),i=n(14438),a=n(53335);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,s.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>i.toast.success("Copied to clipboard!")).catch(()=>{i.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n)}formatVC(e,t){let{currencyCode:n,amountText:r}=this.formatter(e,t);return"".concat(r," ").concat(n," ")}constructor(e){this.formatter=(e,t)=>{var n,r;let s;let i=void 0===t?this.currencyCode:t;try{s=new Intl.NumberFormat("en-US",{style:"currency",currency:i,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let a=null!==(r=null===(n=s.formatToParts(e).find(e=>"currency"===e.type))||void 0===n?void 0:n.value)&&void 0!==r?r:i,o=s.format(e),l=o.substring(a.length).trim();return{currencyCode:i,currencySymbol:a,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let c=e=>e?"".concat(r.rH.STATIC_URL,"/").concat(e):"",v=e=>e?"".concat(r.rH.API_URL,"/").concat(e):"",h=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",m=function(e){var t,n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",s=new URLSearchParams(null===(n=window)||void 0===n?void 0:null===(t=n.location)||void 0===t?void 0:t.search);return e?s.set(r,e):s.delete(r),s}},74539:function(e,t,n){"use strict";n.d(t,{k:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){"use strict";n.d(t,{n:function(){return l}});class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var s=n(84937);class i{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=n(66419),o=n(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new s.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new i(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new r(e.agent):void 0}}},84937:function(e,t,n){"use strict";n.d(t,{O:function(){return s}});var r=n(74539);class s{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new r.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,n){"use strict";n.d(t,{u:function(){return r}});class r{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,27648,2901,32733,92971,95030,1744],function(){return e(e.s=2538)}),_N_E=e.O()}]);