{"name": "prettier-edgejs", "version": "0.2.36", "deprecated": "This package has been renamed to prettier-plugin-edgejs. Please install that instead.", "description": "This package has been deprecated. Use prettier-plugin-edgejs instead.", "keywords": ["edge", "edgejs", "prettier", "plugin"], "author": {"name": "<PERSON><PERSON>", "email": "sajan<PERSON><PERSON><EMAIL>", "url": "https://sajansharma.com"}, "repository": {"type": "git", "url": "https://github.com/sajansharmanz/prettier-plugin-edgejs.git"}, "license": "MIT", "type": "module", "files": ["dist", "index.d.ts"], "main": "./dist/main.umd.cjs", "module": "./dist/main.js", "types": "./index.d.ts", "exports": {"types": "./index.d.ts", "import": "./dist/main.js", "require": "./dist/main.umd.cjs"}, "scripts": {"postinstall": "echo 'This package has been deprecated. Please install prettier-plugin-edgejs instead.'"}, "dependencies": {"prettier-plugin-edgejs": "latest"}}