(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4289],{42297:function(e,s,l){Promise.resolve().then(l.bind(l,78835))},78835:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return f}});var a=l(57437),n=l(41169),t=l(62869),i=l(66070),r=l(6512),c=l(97054),o=l(75730),u=l(99376);l(2265);var m=l(43949),d=l(14438);function f(){var e,s;let{t:l}=(0,m.$G)(),f=(0,u.useSearchParams)(),x=(0,u.useRouter)(),{data:h,meta:g,isLoading:p}=(0,o.Z)("/login-sessions?page=".concat(null!==(e=f.get("page"))&&void 0!==e?e:1,"&limit=").concat(null!==(s=f.get("limit"))&&void 0!==s?s:10));return(0,a.jsx)("div",{className:"flex flex-col gap-4",children:(0,a.jsxs)(i.Zb,{className:"flex flex-col gap-4 rounded-xl p-4 shadow-default",children:[(0,a.jsx)(i.Ol,{className:"justify-center p-0 sm:h-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 sm:flex-row sm:items-center",children:[(0,a.jsx)(i.ll,{className:"text-base font-medium leading-[22px]",children:l("Login Sessions")}),(0,a.jsx)(t.z,{onClick:e=>{e.preventDefault(),d.toast.promise(c.x,{loading:l("Loading..."),success:e=>{if(!(null==e?void 0:e.status))throw Error(e.message);return x.refresh(),e.message},error:e=>e.message})},variant:"outline",size:"sm",type:"button",className:"ml-2.5 cursor-pointer text-sm",asChild:!0,children:(0,a.jsx)("div",{children:l("Logout from all device")})})]})}),(0,a.jsx)(r.Z,{className:"mb-1 mt-[5px]"}),(0,a.jsx)(i.aY,{className:"p-0",children:(0,a.jsx)(n.Z,{data:h,isLoading:p,meta:g})})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,2901,85210,68211,81527,92971,95030,1744],function(){return e(e.s=42297)}),_N_E=e.O()}]);