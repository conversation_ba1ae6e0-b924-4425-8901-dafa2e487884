(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[731],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},41144:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>A,default:()=>D});var r,s={};a.r(s),a.d(s,{AppRouter:()=>m.WY,ClientPageRoot:()=>m.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>m.yO,NotFoundBoundary:()=>m.O4,Postpone:()=>m.hQ,RenderFromTemplateContext:()=>m.b5,__next_app__:()=>g,actionAsyncStorage:()=>m.Wz,createDynamicallyTrackedSearchParams:()=>m.rL,createUntrackedSearchParams:()=>m.S5,decodeAction:()=>m.Hs,decodeFormState:()=>m.dH,decodeReply:()=>m.kf,originalPathname:()=>x,pages:()=>f,patchFetch:()=>m.XH,preconnect:()=>m.$P,preloadFont:()=>m.C5,preloadStyle:()=>m.oH,renderToReadableStream:()=>m.aW,requestAsyncStorage:()=>m.Fg,routeModule:()=>h,serverHooks:()=>m.GP,staticGenerationAsyncStorage:()=>m.AT,taintObjectReference:()=>m.nr,tree:()=>p}),a(67206);var n=a(79319),o=a(20518),l=a(61902),i=a(62042),c=a(44630),d=a(44828),u=a(65505),m=a(13839);let p=["",{children:["(protected)",{admin:["children",{children:["settings",{children:["gateways",{children:["[gatewayId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,66539)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\gateways\\[gatewayId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(a.bind(a,74794)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\gateways\\[gatewayId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,15171)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,5897)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(a.bind(a,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(a.bind(a,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(a.bind(a,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(a.bind(a,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],f=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\gateways\\[gatewayId]\\page.tsx"],x="/(protected)/@admin/settings/gateways/[gatewayId]/page",g={require:a,loadChunk:()=>Promise.resolve()},h=new c.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/settings/gateways/[gatewayId]/page",pathname:"/settings/gateways/[gatewayId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var b=a(69094),y=a(5787),j=a(90527);let v=e=>e?JSON.parse(e):void 0,w=self.__BUILD_MANIFEST,N=v(self.__REACT_LOADABLE_MANIFEST),E=null==(r=self.__RSC_MANIFEST)?void 0:r["/(protected)/@admin/settings/gateways/[gatewayId]/page"],S=v(self.__RSC_SERVER_MANIFEST),P=v(self.__NEXT_FONT_MANIFEST),k=v(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];E&&S&&(0,y.Mo)({clientReferenceManifest:E,serverActionsManifest:S,serverModuleMap:(0,j.w)({serverActionsManifest:S,pageName:"/(protected)/@admin/settings/gateways/[gatewayId]/page"})});let C=(0,o.d)({pagesType:b.s.APP,dev:!1,page:"/(protected)/@admin/settings/gateways/[gatewayId]/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:w,renderToHTML:i.f,reactLoadableManifest:N,clientReferenceManifest:E,serverActionsManifest:S,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:P,incrementalCacheHandler:null,interceptionRouteRewrites:k}),A=s;function D(e){return(0,n.C)({...e,IncrementalCache:l.k,handler:C})}},61155:(e,t,a)=>{Promise.resolve().then(a.bind(a,61051))},61051:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>H,runtime:()=>V});var r=a(60926),s=a(29411),n=a(59571),o=a(15185),l=a(36162),i=a(74988),c=a(92207),d=a(9885),u=a(1181),m=a(65091),p=a(9172),f=a(64947),x=a(39228),g=a(32167),h=a(32898);a(87198),a(4825),a(7643),a(98019),a(68870),a(92773);var b=a(7602),y=a(5670),j=a(34451),v=a(18662),w=a(66817),N=a(26734),E=a(30417),S=a(25694);let P=["uploadLogo","active","activeApi","recommended","allowedCurrencies"];async function k(e,t){try{let a=function(e){let t=new FormData;return P.forEach(a=>{void 0!==e[a]&&null!==e[a]&&t.append(a,e[a]instanceof File?e[a]:e[a].toString())}),Object.entries(e).forEach(([e,a])=>{P.includes(e)||null==a||t.append(e,a.toString())}),t}(e),r=await u.Z.put(`/admin/gateways/${t}`,a,{headers:{"Content-Type":"multipart/form-data"}});return(0,S.B)(r)}catch(e){return(0,S.D)(e)}}var C=a(43291),A=a(72382),D=a(15487),L=a(14761),I=a(29220),R=a(45475),M=a(93633);let _=e=>{let t={};return e?.forEach(e=>{t[e.key]=e.required?M.z.string().min(1,{message:`${e.label} is required`}):M.z.string().optional()}),M.z.object({uploadLogo:A.K,active:M.z.boolean().default(!1),activeApi:M.z.boolean().default(!1),recommended:M.z.boolean().default(!1),allowedCurrencies:M.z.string(),...t})};function T({gateway:e,onMutate:t}){let a=(0,f.UO)(),o=(0,f.lr)().get("name"),[i,c]=(0,I.useTransition)(),{data:d,isLoading:u}=(0,C.d)("/admin/gateways/config"),{t:p}=(0,x.$G)(),h=d?.data?.[o],S=_(h),P=(0,R.cI)({resolver:(0,D.F)(S),defaultValues:{uploadLogo:e?.logoImage||"",active:!!e?.active,activeApi:!!e?.activeApi,recommended:!!e?.recommended,allowedCurrencies:e?.allowedCurrencies||""}});if(u)return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(s.Loader,{})});let A=e=>e?.type==="select"?(0,r.jsx)(j.Wi,{control:P.control,name:e?.key,render:({field:t})=>(0,r.jsxs)(j.xJ,{children:[(0,r.jsx)(j.lX,{children:p(e?.label)}),(0,r.jsx)(j.NI,{children:(0,r.jsx)(N.E,{defaultValue:t.value,onValueChange:t.onChange,className:"grid-cols-12 gap-4",children:e.options.map(e=>(0,r.jsxs)(w.Z,{htmlFor:e.value,"data-active":t.value===e.value,className:"col-span-12 flex h-12 cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 text-sm font-semibold leading-5 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[(0,r.jsx)(N.m,{id:e.value,value:e.value,className:"absolute left-0 top-0 opacity-0"}),(0,r.jsx)("span",{children:p(e.label)})]},e.value))})}),(0,r.jsx)(j.zG,{})]})}):(0,r.jsx)(j.Wi,{name:e?.key,control:P.control,render:({field:t})=>(0,r.jsxs)(j.xJ,{className:"mt-2",children:[(0,r.jsx)(j.lX,{children:p(e?.label)}),(0,r.jsx)(j.NI,{children:(0,r.jsx)(v.I,{type:e?.type,placeholder:p("Enter {{label}}",{label:e?.label}),...t})}),(0,r.jsx)(j.zG,{})]})},e?.key);return(0,r.jsxs)(n.Qd,{value:"GatewayDetails",className:"mb-4 rounded-xl border border-border bg-background px-4 py-0",children:[(0,r.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:(0,m.fl)(e?.name)})}),(0,r.jsx)(n.vF,{className:"gap-4 border-t pt-4",children:(0,r.jsx)(j.l0,{...P,children:(0,r.jsxs)("form",{onSubmit:P.handleSubmit(r=>{let s={...r,allowedCurrencies:JSON.stringify([r.allowedCurrencies]),name:e?.name,variables:e?.variables||JSON.stringify({}),value:e?.value,isCrypto:e?.isCrypto,allowedCountries:e?.allowedCountries};c(async()=>{let e=await k(s,a?.gatewayId);e.status?(t(),g.toast.success(e.message)):g.toast.error(p(e.message))})}),className:"flex flex-col gap-6 px-1",children:[(0,r.jsx)(j.Wi,{control:P.control,name:"uploadLogo",render:({field:t})=>(0,r.jsxs)(j.xJ,{children:[(0,r.jsx)(j.lX,{children:p("Gateway logo")}),(0,r.jsx)(j.NI,{children:(0,r.jsx)(b.S,{defaultValue:(0,m.qR)(e?.logoImage),id:"uploadLogo",onChange:e=>t.onChange(e),className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,r.jsx)(y.X,{}),(0,r.jsx)("p",{className:"text-sm font-normal text-primary",children:p("Upload logo")})]})})})]})}),h?.map(e=>A(e)),(0,r.jsx)(j.Wi,{name:"active",control:P.control,render:({field:e})=>(0,r.jsxs)(j.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,r.jsx)(w.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:p("Active")}),(0,r.jsx)(j.NI,{children:(0,r.jsx)(E.Z,{defaultChecked:e.value,onCheckedChange:e.onChange})}),(0,r.jsx)(j.zG,{})]})}),(0,r.jsx)(j.Wi,{name:"activeApi",control:P.control,render:({field:e})=>(0,r.jsxs)(j.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,r.jsx)(w.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:p("Active API")}),(0,r.jsx)(j.NI,{children:(0,r.jsx)(E.Z,{defaultChecked:e.value,onCheckedChange:e.onChange})}),(0,r.jsx)(j.zG,{})]})}),(0,r.jsx)(j.Wi,{name:"recommended",control:P.control,render:({field:e})=>(0,r.jsxs)(j.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,r.jsx)(w.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:p("Recommended")}),(0,r.jsx)(j.NI,{children:(0,r.jsx)(E.Z,{defaultChecked:e.value,onCheckedChange:e.onChange})}),(0,r.jsx)(j.zG,{})]})}),(0,r.jsx)(j.Wi,{name:"allowedCurrencies",control:P.control,render:({field:e})=>(0,r.jsxs)(j.xJ,{className:"mt-2",children:[(0,r.jsx)(j.lX,{children:p("Supported Currencies")}),(0,r.jsx)(j.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:"USD, EUR, GBP, AUD, CAD, SGD",...e})}),(0,r.jsx)(j.zG,{})]})}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(l.z,{className:"rounded-lg",children:i?(0,r.jsx)(s.Loader,{title:p("Updating..."),className:"text-primary-foreground"}):(0,r.jsxs)(r.Fragment,{children:[p("Update gateway"),(0,r.jsx)(L.Z,{size:20})]})})})]})})})]})}var F=a(58387),Z=a(78133),z=a(7680),U=a(75643),Q=a(23065),B=a(12393),O=a(32917),G=a(47020),W=a(56402),Y=a(11230);function $({gatewayId:e,onMutate:t,blackListedUsers:a}){let{t:n}=(0,x.$G)(),{width:o}=(0,Q.B)(),i=(0,f.lr)(),[d,m]=(0,I.useState)(!1),[p,g]=I.useState(i.get("search")??""),{data:h,isLoading:b,size:y,setSize:j,mutate:v}=(0,Y.ZP)(e=>`/admin/users?page=${e+1}&limit=25&search=${p}`,e=>u.Z.get(e));return(0,r.jsxs)(z.dy,{open:d,onOpenChange:m,direction:o<640?"bottom":"right",children:[(0,r.jsx)(z.Qz,{asChild:!0,children:(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsxs)(l.z,{variant:"outline",className:"gap-1 rounded-lg",children:[(0,r.jsx)(O.Z,{}),n("Add Customer")]})})}),(0,r.jsxs)(z.sc,{className:"inset-x-auto bottom-auto left-auto right-0 top-0 m-0 mt-20 flex h-full w-full max-w-[540px] flex-col rounded-t-none bg-background px-0 pt-4 sm:inset-y-0 sm:mt-0 sm:pt-8",children:[(0,r.jsx)("span",{className:"mx-auto mb-8 block h-2.5 w-20 rounded-lg bg-divider-secondary sm:hidden"}),(0,r.jsxs)("div",{className:"flex items-center gap-4 px-6 pb-6",children:[(0,r.jsx)(l.z,{variant:"outline",size:"icon",className:"hidden sm:flex",asChild:!0,children:(0,r.jsx)(z.uh,{children:(0,r.jsx)(G.Z,{size:16})})}),(0,r.jsxs)(z.OX,{className:"flex-1 p-0",children:[(0,r.jsx)(z.iI,{className:"text-left text-base font-semibold leading-[22px]",children:n("Customers")}),(0,r.jsx)(z.u6,{className:"invisible absolute text-xs font-normal text-secondary-text",children:n("You can add customers to the block list to prevent them from using the platform.")})]})]}),(0,r.jsx)("div",{className:"flex flex-col p-6 pt-0",children:(0,r.jsx)(Z.R,{value:p,onChange:e=>{e.preventDefault(),g(e.target.value)},iconPlacement:"end",placeholder:n("Search..."),className:"w-full"})}),(0,r.jsx)("div",{id:"scrollbarTrigger",className:"flex-1 overflow-y-auto overflow-x-hidden",children:(0,r.jsxs)("div",{className:"flex flex-col gap-2 p-6 pt-0",children:[(0,r.jsx)(F.J,{condition:b,children:(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(s.Loader,{})})}),(0,r.jsx)(F.J,{condition:!b&&!!h?.length,children:(0,r.jsx)(W.Z,{dataLength:h?.reduce((e,t)=>e+Number(t.data?.data?.length??0),0),next:()=>j(y+1),hasMore:!!h?.[h.length-1]?.data?.meta?.nextPageUrl,loader:(0,r.jsx)(s.Loader,{className:"flex justify-center py-4"}),endMessage:(0,r.jsx)("p",{className:"py-4",style:{textAlign:"center"},children:(0,r.jsx)("b",{children:n("No more")})}),scrollableTarget:"scrollbarTrigger",children:(0,r.jsxs)(c.iA,{children:[(0,r.jsx)(c.xD,{children:(0,r.jsxs)(c.SC,{children:[(0,r.jsxs)(c.ss,{className:"w-full",children:[" ",n("Name")," "]}),(0,r.jsxs)(c.ss,{children:[" ",n("Action")," "]})]})}),(0,r.jsx)(c.RM,{children:h?.reduce((e,t)=>t?.data?.data?.length?[...e,...t.data.data]:e,[])?.map(e=>new B.n(e))?.map(s=>r.jsx(I.Fragment,{children:r.jsx(q,{data:s,gatewayId:e,blackListedUsers:a,onMutate:()=>{v(),t()}})},s.id))})]})})})]})})]})]})}function q({data:e,gatewayId:t,blackListedUsers:a,onMutate:s}){let{t:n}=(0,x.$G)(),i=e?.customer;if(!i)return null;let u=e=>{g.toast.promise((0,U.O)({gatewayId:t,userId:e},"gateways"),{loading:n("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return s(),e.message},error:e=>e.message})},f=e=>{g.toast.promise((0,d.E)({gatewayId:t,userId:e},"gateways"),{loading:n("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return s(),e.message},error:e=>e.message})},h=a.includes(i.id);return(0,r.jsxs)(c.SC,{className:"border-b border-border-primary",children:[(0,r.jsxs)(c.pj,{className:"flex w-full items-center gap-2.5 py-2",children:[(0,r.jsxs)(o.qE,{children:[(0,r.jsx)(o.F$,{src:(0,m.qR)(i.avatar)}),(0,r.jsxs)(o.Q5,{children:[" ",(0,p.v)(i.name)," "]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"block font-medium",children:i.name}),(0,r.jsx)("span",{className:"block text-xs",children:e.email})]})]}),(0,r.jsx)(c.pj,{className:"py-2",children:(0,r.jsx)(l.z,{variant:"outline",onClick:h?()=>f(i.id):()=>u(i.id),size:"sm",className:"rounded-lg",children:h?n("Unblock user"):n("Add to blacklist")})})]})}let V="edge";function H(){let e=(0,f.UO)(),{t}=(0,x.$G)(),{data:a,isLoading:b,mutate:y}=(0,h.ZP)(`/admin/gateways/${e.gatewayId}`,e=>(0,u.Z)(e));if(b)return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(s.Loader,{})});let j=a?.data,v=a=>{let r={gatewayId:Number(e.gatewayId),userId:a};g.toast.promise((0,d.E)(r,"gateways"),{loading:t("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return y(),e.message},error:e=>e.message})},w=j?.blackListedUsers?.map(e=>e.customer.userId)||[];return(0,r.jsxs)(n.UQ,{type:"multiple",defaultValue:["GatewayDetails","GatewayDetailsAllowed","BlockList"],children:[(0,r.jsx)(T,{gateway:j,onMutate:y}),(0,r.jsxs)(n.Qd,{value:"BlockList",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,r.jsx)(n.o4,{className:"flex items-center justify-between py-6 hover:no-underline",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:t("Block List")})}),(0,r.jsxs)(n.vF,{className:"border-t pt-4",children:[(0,r.jsx)("div",{className:"w-full max-w-[700px]",children:(0,r.jsxs)(c.iA,{children:[(0,r.jsx)(c.xD,{children:(0,r.jsxs)(c.SC,{children:[(0,r.jsxs)(c.ss,{className:"w-full",children:[" ",t("Name")," "]}),(0,r.jsxs)(c.ss,{children:[" ",t("Action")," "]})]})}),(0,r.jsx)(c.RM,{children:j?.blackListedUsers.map(e=>r.jsxs(c.SC,{children:[r.jsxs(c.pj,{className:"flex w-full items-center gap-2.5 py-2",children:[r.jsxs(o.qE,{children:[r.jsx(o.F$,{src:m.qR(e?.customer.profileImage)}),r.jsxs(o.Q5,{children:[p.v(e?.customer.name)," "]})]}),r.jsxs("div",{children:[r.jsx("span",{className:"block font-medium",children:e?.customer.name}),r.jsx("span",{className:"block text-xs",children:e.email})]})]}),r.jsx(c.pj,{className:"py-2",children:r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>v(e?.customer.userId),className:"rounded-lg",children:t("Unblock")})})]},e?.id))})]})}),(0,r.jsx)(i.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,r.jsx)($,{gatewayId:Number(e.gatewayId),onMutate:()=>y(a),blackListedUsers:w})]})]})]})}},78133:(e,t,a)=>{"use strict";a.d(t,{R:()=>l});var r=a(60926);a(29220);var s=a(18662),n=a(65091),o=a(51670);function l({iconPlacement:e="start",className:t,containerClass:a,...l}){return(0,r.jsxs)("div",{className:(0,n.ZP)("relative flex items-center",a),children:[(0,r.jsx)(o.Z,{size:"20",className:(0,n.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),(0,r.jsx)(s.I,{type:"text",className:(0,n.ZP)("h-10","end"===e?"pr-10":"pl-10",t),...l})]})}},7643:(e,t,a)=>{"use strict";a.d(t,{W:()=>o});var r=a(60926),s=a(65091),n=a(28277);function o({countryCode:e,className:t,url:a}){return e||a?(0,r.jsx)(n.Z,{src:a??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,s.ZP)("rounded-[2px]",t)}):null}},18662:(e,t,a)=>{"use strict";a.d(t,{I:()=>o});var r=a(60926),s=a(29220),n=a(65091);let o=s.forwardRef(({className:e,type:t,...a},s)=>(0,r.jsx)("input",{type:t,className:(0,n.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:s,...a}));o.displayName="Input"},66817:(e,t,a)=>{"use strict";a.d(t,{Z:()=>d});var r=a(60926),s=a(11537),n=a(8206),o=a(29220),l=a(65091);let i=(0,n.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef(({className:e,...t},a)=>(0,r.jsx)(s.f,{ref:a,className:(0,l.ZP)(i(),e),...t}));c.displayName=s.f.displayName;let d=c},26734:(e,t,a)=>{"use strict";a.d(t,{E:()=>i,m:()=>c});var r=a(60926),s=a(29220),n=a(40441),o=a(63608),l=a(65091);let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.fC,{className:(0,l.ZP)("grid gap-2",e),...t,ref:a}));i.displayName=n.fC.displayName;let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.ck,{ref:a,className:(0,l.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(n.z$,{className:"flex items-center justify-center",children:(0,r.jsx)(o.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));c.displayName=n.ck.displayName},92207:(e,t,a)=>{"use strict";a.d(t,{RM:()=>i,SC:()=>c,iA:()=>o,pj:()=>u,ss:()=>d,xD:()=>l});var r=a(60926),s=a(29220),n=a(65091);let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,n.ZP)("w-full caption-bottom text-sm",e),...t})}));o.displayName="Table";let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("thead",{ref:a,className:(0,n.ZP)("",e),...t}));l.displayName="TableHeader";let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tbody",{ref:a,className:(0,n.ZP)("[&_tr:last-child]:border-0",e),...t}));i.displayName="TableBody",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tfoot",{ref:a,className:(0,n.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tr",{ref:a,className:(0,n.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));c.displayName="TableRow";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("th",{ref:a,className:(0,n.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));d.displayName="TableHead";let u=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("td",{ref:a,className:(0,n.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("caption",{ref:a,className:(0,n.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},92773:(e,t,a)=>{"use strict";a.d(t,{F:()=>c});class r{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var s=a(82844),n=a(32167),o=a(32898);let l=s.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),i="name,cca2,ccn3,cca3,status,flag,flags";function c(){let{data:e,isLoading:t,...a}=(0,o.ZP)(`/all?fields=${i}`,e=>l.get(e)),c=e?.data,d=async(e,t)=>{try{let a=await l.get(`/alpha/${e.toLowerCase()}?fields=${i}`),s=a.data?new r(a.data):null;t(s)}catch(e){s.default.isAxiosError(e)&&n.toast.error("Failed to fetch country")}};return{countries:c?c.map(e=>new r(e)):[],isLoading:t,getCountryByCode:d,...a}}},51670:(e,t,a)=>{"use strict";a.d(t,{Z:()=>x});var r=a(61394),s=a(29220),n=a(31036),o=a.n(n),l=["variant","color","size"],i=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",fill:t}),s.createElement("path",{d:"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"m22 22-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e,t){switch(e){case"Bold":return s.createElement(i,{color:t});case"Broken":return s.createElement(c,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(u,{color:t});case"Outline":return s.createElement(m,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},x=(0,s.forwardRef)(function(e,t){var a=e.variant,n=e.color,o=e.size,i=(0,r._)(e,l);return s.createElement("svg",(0,r.a)({},i,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:o,height:o,viewBox:"0 0 24 24",fill:"none"}),f(a,n))});x.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="SearchNormal1"},74794:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var r=a(42416),s=a(21237);function n(){return(0,r.jsx)("div",{className:"flex justify-center py-10",children:(0,r.jsx)(s.a,{})})}},66539:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n,runtime:()=>s});var r=a(18264);let s=(0,r.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\gateways\[gatewayId]\page.tsx#runtime`),n=(0,r.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\gateways\[gatewayId]\page.tsx#default`)},40441:(e,t,a)=>{"use strict";a.d(t,{ck:()=>Z,fC:()=>F,z$:()=>z});var r=a(29220),s=a(58408),n=a(19677),o=a(16769),l=a(22316),i=a(12239),c=a(68878),d=a(3237),u=a(17526),m=a(43263),p=a(90027),f=a(60926),x="Radio",[g,h]=(0,o.b)(x),[b,y]=g(x),j=r.forwardRef((e,t)=>{let{__scopeRadio:a,name:o,checked:i=!1,required:c,disabled:d,value:u="on",onCheck:m,form:p,...x}=e,[g,h]=r.useState(null),y=(0,n.e)(t,e=>h(e)),j=r.useRef(!1),v=!g||p||!!g.closest("form");return(0,f.jsxs)(b,{scope:a,checked:i,disabled:d,children:[(0,f.jsx)(l.WV.button,{type:"button",role:"radio","aria-checked":i,"data-state":E(i),"data-disabled":d?"":void 0,disabled:d,value:u,...x,ref:y,onClick:(0,s.M)(e.onClick,e=>{i||m?.(),v&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),v&&(0,f.jsx)(N,{control:g,bubbles:!j.current,name:o,value:u,checked:i,required:c,disabled:d,form:p,style:{transform:"translateX(-100%)"}})]})});j.displayName=x;var v="RadioIndicator",w=r.forwardRef((e,t)=>{let{__scopeRadio:a,forceMount:r,...s}=e,n=y(v,a);return(0,f.jsx)(p.z,{present:r||n.checked,children:(0,f.jsx)(l.WV.span,{"data-state":E(n.checked),"data-disabled":n.disabled?"":void 0,...s,ref:t})})});w.displayName=v;var N=r.forwardRef(({__scopeRadio:e,control:t,checked:a,bubbles:s=!0,...o},i)=>{let c=r.useRef(null),d=(0,n.e)(c,i),p=(0,m.D)(a),x=(0,u.t)(t);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&t){let r=new Event("click",{bubbles:s});t.call(e,a),e.dispatchEvent(r)}},[p,a,s]),(0,f.jsx)(l.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...o,tabIndex:-1,ref:d,style:{...o.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function E(e){return e?"checked":"unchecked"}N.displayName="RadioBubbleInput";var S=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],P="RadioGroup",[k,C]=(0,o.b)(P,[i.Pc,h]),A=(0,i.Pc)(),D=h(),[L,I]=k(P),R=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,name:r,defaultValue:s,value:n,required:o=!1,disabled:u=!1,orientation:m,dir:p,loop:x=!0,onValueChange:g,...h}=e,b=A(a),y=(0,d.gm)(p),[j,v]=(0,c.T)({prop:n,defaultProp:s??"",onChange:g,caller:P});return(0,f.jsx)(L,{scope:a,name:r,required:o,disabled:u,value:j,onValueChange:v,children:(0,f.jsx)(i.fC,{asChild:!0,...b,orientation:m,dir:y,loop:x,children:(0,f.jsx)(l.WV.div,{role:"radiogroup","aria-required":o,"aria-orientation":m,"data-disabled":u?"":void 0,dir:y,...h,ref:t})})})});R.displayName=P;var M="RadioGroupItem",_=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,disabled:o,...l}=e,c=I(M,a),d=c.disabled||o,u=A(a),m=D(a),p=r.useRef(null),x=(0,n.e)(t,p),g=c.value===l.value,h=r.useRef(!1);return r.useEffect(()=>{let e=e=>{S.includes(e.key)&&(h.current=!0)},t=()=>h.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,f.jsx)(i.ck,{asChild:!0,...u,focusable:!d,active:g,children:(0,f.jsx)(j,{disabled:d,required:c.required,checked:g,...m,...l,name:c.name,ref:x,onCheck:()=>c.onValueChange(l.value),onKeyDown:(0,s.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,s.M)(l.onFocus,()=>{h.current&&p.current?.click()})})})});_.displayName=M;var T=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,...r}=e,s=D(a);return(0,f.jsx)(w,{...s,...r,ref:t})});T.displayName="RadioGroupIndicator";var F=R,Z=_,z=T}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,4969,1474,8583,7283,5089,3711,3020],()=>t(41144));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/settings/gateways/[gatewayId]/page"]=a}]);
//# sourceMappingURL=page.js.map