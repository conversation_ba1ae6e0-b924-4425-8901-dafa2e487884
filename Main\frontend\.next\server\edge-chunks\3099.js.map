{"version": 3, "file": "edge-chunks/3099.js", "mappings": "4JAIAA,EAAA,2BAEAC,EAAA,SAAAC,CAAA,EACA,IAAAC,EAAAD,EAAAC,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,gyCACAC,KAAAL,CACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAP,EAAAO,EAAAP,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,2EACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBV,EAAAC,aAAmB,SACtCE,EAAA,iJACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBV,EAAAC,aAAmB,SACtCE,EAAA,uEACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAC,EAAA,SAAAC,CAAA,EACA,IAAAb,EAAAa,EAAAb,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGY,QAAA,KACAV,EAAA,4bACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,u2BACAC,KAAAL,CACA,GACA,EAEAe,EAAA,SAAAC,CAAA,EACA,IAAAhB,EAAAgB,EAAAhB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,2EACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBV,EAAAC,aAAmB,SACtCE,EAAA,kEACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GAAmBV,EAAAC,aAAmB,SACtCE,EAAA,mEACAI,OAAAR,EACAS,YAAA,MACAC,cAAA,QACAC,eAAA,OACA,GACA,EAEAM,EAAA,SAAAC,CAAA,EACA,IAAAlB,EAAAkB,EAAAlB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGE,EAAA,+LACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,2WACAC,KAAAL,CACA,GAAmBC,EAAAC,aAAmB,SACtCE,EAAA,qaACAC,KAAAL,CACA,GACA,EAEAmB,EAAA,SAAAC,CAAA,EACA,IAAApB,EAAAoB,EAAApB,KAAA,CACA,OAAsBC,EAAAC,aAAmB,CAACD,EAAAE,QAAc,MAAqBF,EAAAC,aAAmB,SAChGM,OAAAR,EACAU,cAAA,QACAC,eAAA,QACAF,YAAA,MACAL,EAAA,0EACA,GAAmBH,EAAAC,aAAmB,MACtCY,QAAA,IACA,EAAkBb,EAAAC,aAAmB,SACrCM,OAAAR,EACAU,cAAA,QACAC,eAAA,QACAF,YAAA,MACAL,EAAA,6DACA,GAAmBH,EAAAC,aAAmB,SACtCM,OAAAR,EACAU,cAAA,QACAC,eAAA,QACAF,YAAA,MACAL,EAAA,8DACA,IACA,EAEAiB,EAAA,SAAAC,CAAA,CAAAtB,CAAA,EACA,OAAAsB,GACA,WACA,OAA0BrB,EAAAC,aAAmB,CAAAJ,EAAA,CAC7CE,MAAAA,CACA,EAEA,cACA,OAA0BC,EAAAC,aAAmB,CAAAI,EAAA,CAC7CN,MAAAA,CACA,EAEA,YACA,OAA0BC,EAAAC,aAAmB,CAAAU,EAAA,CAC7CZ,MAAAA,CACA,EAEA,cAeA,QAdA,OAA0BC,EAAAC,aAAmB,CAAAa,EAAA,CAC7Cf,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAe,EAAA,CAC7CjB,MAAAA,CACA,EAEA,eACA,OAA0BC,EAAAC,aAAmB,CAAAiB,EAAA,CAC7CnB,MAAAA,CACA,EAMA,CACA,EAEAuB,EAA0B,GAAAtB,EAAAuB,UAAA,EAAU,SAAAC,CAAA,CAAAC,CAAA,EACpC,IAAAJ,EAAAG,EAAAH,OAAA,CACAtB,EAAAyB,EAAAzB,KAAA,CACA2B,EAAAF,EAAAE,IAAA,CACAC,EAAa,GAAAC,EAAAC,CAAA,EAAwBL,EAAA5B,GAErC,OAAsBI,EAAAC,aAAmB,OAAQ,GAAA2B,EAAAE,CAAA,EAAQ,GAAGH,EAAA,CAC5DI,MAAA,6BACAN,IAAAA,EACAO,MAAAN,EACAO,OAAAP,EACAQ,QAAA,YACA9B,KAAA,MACA,GAAGgB,EAAAC,EAAAtB,GACH,EACAuB,CAAAA,EAAAa,SAAA,EACAd,QAAWe,IAAAC,KAAe,wDAC1BtC,MAASqC,IAAAE,MAAA,CACTZ,KAAQU,IAAAG,SAAmB,EAAEH,IAAAE,MAAA,CAAkBF,IAAAI,MAAA,CAAgB,CAC/D,EACAlB,EAAAmB,YAAA,EACApB,QAAA,SACAtB,MAAA,eACA2B,KAAA,IACA,EACAJ,EAAAoB,WAAA,wKC/JMC,EAAgB,WAGhB,CAACC,EAAuBC,EAAmB,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,EAAmBH,GASlE,CAACI,EAAkBC,EAAkB,CACzCJ,EAA4CD,GAWxCM,EAAiBC,EAAAA,UAAA,CACrB,CAACC,EAAmCC,KAClC,GAAM,CACJC,gBAAAA,CAAA,CACAC,KAAAA,CAAA,CACAC,QAASC,CAAA,CACTC,eAAAA,CAAA,CACAC,SAAAA,CAAA,CACAC,SAAAA,CAAA,CACAC,MAAAA,EAAQ,KACRC,gBAAAA,CAAA,CACAC,KAAAA,CAAA,CACA,GAAGC,EACL,CAAIZ,EACE,CAACa,EAAQC,EAAS,CAAUf,EAAAA,QAAA,CAAmC,MAC/DgB,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgBf,EAAc,GAAUa,EAAUG,IACjEC,EAAyCnB,EAAAA,MAAA,CAAO,IAEhDoB,EAAgBN,CAAAA,GAASF,GAAQ,CAAC,CAACE,EAAOO,OAAA,CAAQ,QAClD,CAAChB,EAASiB,EAAU,CAAIC,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB,CACjDC,KAAMlB,EACNmB,YAAalB,GAAkB,GAC/BmB,SAAUf,EACVgB,OAAQlC,CACV,GACMmC,EAA+B5B,EAAAA,MAAA,CAAOK,GAU5C,OATML,EAAAA,SAAA,CAAU,KACd,IAAMY,EAAOE,GAAQF,KACrB,GAAIA,EAAM,CACR,IAAMiB,EAAQ,IAAMP,EAAWM,EAAuBE,OAAO,EAE7D,OADAlB,EAAKmB,gBAAA,CAAiB,QAASF,GACxB,IAAMjB,EAAKoB,mBAAA,CAAoB,QAASH,EACjD,CACF,EAAG,CAACf,EAAQQ,EAAW,EAGrBW,CAAAA,EAAAA,EAAAA,IAAAA,EAACpC,EAAA,CAAiBqC,MAAO/B,EAAiBgC,MAAO9B,EAASI,SAAAA,EACxD2B,SAAA,CAAAC,CAAAA,EAAAA,EAAAA,GAAAA,EAACC,EAAAA,EAASA,CAACxB,MAAA,CAAV,CACCyB,KAAK,SACLC,KAAK,WACL,eAAcC,EAAgBpC,GAAW,QAAUA,EACnD,gBAAeG,EACf,aAAYkC,EAASrC,GACrB,gBAAeI,EAAW,GAAK,OAC/BA,SAAAA,EACAC,MAAAA,EACC,GAAGG,CAAA,CACJtC,IAAKyC,EACL2B,UAAWC,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB3C,EAAM0C,SAAA,CAAW,IAE7B,UAAdE,EAAMC,GAAA,EAAiBD,EAAME,cAAA,EACnC,GACAC,QAASJ,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB3C,EAAM+C,OAAA,CAAS,IAC3C1B,EAAW,GAAkBmB,EAAAA,EAAgBQ,IAAsB,CAACA,GAChE7B,IACFD,EAAiCW,OAAA,CAAUe,EAAMK,oBAAA,GAI5C/B,EAAiCW,OAAA,EAASe,EAAMM,eAAA,GAEzD,EAAC,GAEF/B,GACCiB,CAAAA,EAAAA,EAAAA,GAAAA,EAACe,EAAA,CACCC,QAASvC,EACTwC,QAAS,CAACnC,EAAiCW,OAAA,CAC3C1B,KAAAA,EACAM,MAAAA,EACAL,QAAAA,EACAG,SAAAA,EACAC,SAAAA,EACAG,KAAAA,EAIA2C,MAAO,CAAEC,UAAW,mBAAoB,EACxCjD,eAAgBkC,CAAAA,EAAgBlC,IAA0BA,CAAA,GAC5D,EAIR,EAGFR,CAAAA,EAASP,WAAA,CAAcC,EAMvB,IAAMgE,EAAiB,oBAYjBC,EAA0B1D,EAAAA,UAAA,CAC9B,CAACC,EAA4CC,KAC3C,GAAM,CAAEC,gBAAAA,CAAA,CAAiBwD,WAAAA,CAAA,CAAY,GAAGC,EAAe,CAAI3D,EACrD4D,EAAU/D,EAAmB2D,EAAgBtD,GACnD,MACEkC,CAAAA,EAAAA,EAAAA,GAAAA,EAACyB,EAAAA,CAAQA,CAAR,CAASC,QAASJ,GAAclB,EAAgBoB,EAAQ1B,KAAK,GAAK0B,CAAkB,IAAlBA,EAAQ1B,KAAA,CACzEC,SAAAC,CAAAA,EAAAA,EAAAA,GAAAA,EAACC,EAAAA,EAASA,CAAC0B,IAAA,CAAV,CACC,aAAYtB,EAASmB,EAAQ1B,KAAK,EAClC,gBAAe0B,EAAQpD,QAAA,CAAW,GAAK,OACtC,GAAGmD,CAAA,CACJrF,IAAK2B,EACLqD,MAAO,CAAEU,cAAe,OAAQ,GAAGhE,EAAMsD,KAAA,CAAM,EACjD,EAGN,EAGFG,CAAAA,EAAkBlE,WAAA,CAAciE,EAehC,IAAML,EAA4BpD,EAAAA,UAAA,CAChC,CACE,CACEG,gBAAAA,CAAA,CACAkD,QAAAA,CAAA,CACAhD,QAAAA,CAAA,CACAiD,QAAAA,EAAU,GACV/C,eAAAA,CAAA,CACA,GAAGN,EACL,CACAC,KAEA,IAAM3B,EAAYyB,EAAAA,MAAA,CAAyB,MACrCgB,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB1C,EAAK2B,GACpC+C,EAAciB,CAAAA,EAAAA,EAAAA,CAAAA,EAAY7D,GAC1B8D,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,EAAQf,GAGtBrD,EAAAA,SAAA,CAAU,KACd,IAAMqE,EAAQ9F,EAAIuD,OAAA,CAClB,GAAI,CAACuC,EAAO,OAOZ,IAAM/C,EAAagD,OAJOC,wBAAA,CADPC,OAAOC,gBAAA,CAAiBC,SAAA,CAGzC,WAE4BC,GAAA,CAE9B,GAAI1B,IAAgB5C,GAAWiB,EAAY,CACzC,IAAMuB,EAAQ,IAAI+B,MAAM,QAAS,CAAEtB,QAAAA,CAAQ,EAC3Ce,CAAAA,EAAMQ,aAAA,CAAgBpC,EAAgBpC,GACtCiB,EAAWwD,IAAA,CAAKT,EAAO5B,CAAAA,EAAgBpC,IAAmBA,GAC1DgE,EAAMU,aAAA,CAAclC,EACtB,CACF,EAAG,CAACI,EAAa5C,EAASiD,EAAQ,EAElC,IAAM0B,EAA0BhF,EAAAA,MAAA,CAAOyC,CAAAA,EAAgBpC,IAAmBA,GAC1E,MACEgC,CAAAA,EAAAA,EAAAA,GAAAA,EAACC,EAAAA,EAASA,CAAC+B,KAAA,CAAV,CACC9B,KAAK,WACL,cAAW,GACXhC,eAAgBA,GAAkByE,EAAkBlD,OAAA,CACnD,GAAG7B,CAAA,CACJgF,SAAU,GACV1G,IAAKyC,EACLuC,MAAO,CACL,GAAGtD,EAAMsD,KAAA,CACT,GAAGY,CAAA,CACHe,SAAU,WACVjB,cAAe,OACftG,QAAS,EACTwH,OAAQ,CACV,GAGN,GAOF,SAAS1C,EAAgBpC,CAAA,EACvB,MAAOA,kBAAAA,CACT,CAEA,SAASqC,EAASrC,CAAA,EAChB,OAAOoC,EAAgBpC,GAAW,gBAAkBA,EAAU,UAAY,WAC5E,CAVA+C,EAAoB5D,WAAA,CApEM,sBAgF1B,IAAM4F,EAAOrF,EACPsF,EAAY3B,8LEzOZ4B,EAAa,QAGb,CAACC,EAAoBC,EAAgB,CAAI5F,CAAAA,EAAAA,EAAAA,CAAAA,EAAmB0F,GAG5D,CAACG,EAAeC,EAAe,CAAIH,EAAsCD,GAUzEK,EAAc3F,EAAAA,UAAA,CAClB,CAACC,EAAgCC,KAC/B,GAAM,CACJ0F,aAAAA,CAAA,CACAxF,KAAAA,CAAA,CACAC,QAAAA,EAAU,GACVG,SAAAA,CAAA,CACAC,SAAAA,CAAA,CACAC,MAAAA,EAAQ,KACRmF,QAAAA,CAAA,CACAjF,KAAAA,CAAA,CACA,GAAGkF,EACL,CAAI7F,EACE,CAACa,EAAQC,EAAS,CAAUf,EAAAA,QAAA,CAAmC,MAC/DgB,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgBf,EAAc,GAAUa,EAAUG,IACjEC,EAAyCnB,EAAAA,MAAA,CAAO,IAEhDoB,EAAgBN,CAAAA,GAASF,GAAQ,CAAC,CAACE,EAAOO,OAAA,CAAQ,QAExD,MACEY,CAAAA,EAAAA,EAAAA,IAAAA,EAACwD,EAAA,CAAcvD,MAAO0D,EAAcvF,QAAAA,EAAkBI,SAAAA,EACpD2B,SAAA,CAAAC,CAAAA,EAAAA,EAAAA,GAAAA,EAACC,EAAAA,EAASA,CAACxB,MAAA,CAAV,CACCyB,KAAK,SACLC,KAAK,QACL,eAAcnC,EACd,aAAYqC,EAASrC,GACrB,gBAAeI,EAAW,GAAK,OAC/BA,SAAAA,EACAC,MAAAA,EACC,GAAGoF,CAAA,CACJvH,IAAKyC,EACLgC,QAASJ,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB3C,EAAM+C,OAAA,CAAS,IAEtC3C,GAASwF,MACVzE,IACFD,EAAiCW,OAAA,CAAUe,EAAMK,oBAAA,GAI5C/B,EAAiCW,OAAA,EAASe,EAAMM,eAAA,GAEzD,EAAC,GAEF/B,GACCiB,CAAAA,EAAAA,EAAAA,GAAAA,EAAC0D,EAAA,CACC1C,QAASvC,EACTwC,QAAS,CAACnC,EAAiCW,OAAA,CAC3C1B,KAAAA,EACAM,MAAAA,EACAL,QAAAA,EACAG,SAAAA,EACAC,SAAAA,EACAG,KAAAA,EAIA2C,MAAO,CAAEC,UAAW,mBAAoB,IAC1C,EAIR,EAGFmC,CAAAA,EAAMnG,WAAA,CAAc8F,EAMpB,IAAM7B,EAAiB,iBAYjBuC,EAAuBhG,EAAAA,UAAA,CAC3B,CAACC,EAAyCC,KACxC,GAAM,CAAE0F,aAAAA,CAAA,CAAcjC,WAAAA,CAAA,CAAY,GAAGC,EAAe,CAAI3D,EAClD4D,EAAU6B,EAAgBjC,EAAgBmC,GAChD,MACEvD,CAAAA,EAAAA,EAAAA,GAAAA,EAACyB,EAAAA,CAAQA,CAAR,CAASC,QAASJ,GAAcE,EAAQxD,OAAA,CACvC+B,SAAAC,CAAAA,EAAAA,EAAAA,GAAAA,EAACC,EAAAA,EAASA,CAAC0B,IAAA,CAAV,CACC,aAAYtB,EAASmB,EAAQxD,OAAO,EACpC,gBAAewD,EAAQpD,QAAA,CAAW,GAAK,OACtC,GAAGmD,CAAA,CACJrF,IAAK2B,CAAA,EACP,EAGN,EAGF8F,CAAAA,EAAexG,WAAA,CAAciE,EAe7B,IAAMsC,EAAyB/F,EAAAA,UAAA,CAC7B,CACE,CACE4F,aAAAA,CAAA,CACAvC,QAAAA,CAAA,CACAhD,QAAAA,CAAA,CACAiD,QAAAA,EAAU,GACV,GAAGrD,EACL,CACAC,KAEA,IAAM3B,EAAYyB,EAAAA,MAAA,CAAyB,MACrCgB,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgB1C,EAAK2B,GACpC+C,EAAciB,CAAAA,EAAAA,EAAAA,CAAAA,EAAY7D,GAC1B8D,EAAcC,CAAAA,EAAAA,EAAAA,CAAAA,EAAQf,GAoB5B,OAjBMrD,EAAAA,SAAA,CAAU,KACd,IAAMqE,EAAQ9F,EAAIuD,OAAA,CAClB,GAAI,CAACuC,EAAO,OAOZ,IAAM/C,EAAagD,OAJOC,wBAAA,CADPC,OAAOC,gBAAA,CAAiBC,SAAA,CAGzC,WAE4BC,GAAA,CAC9B,GAAI1B,IAAgB5C,GAAWiB,EAAY,CACzC,IAAMuB,EAAQ,IAAI+B,MAAM,QAAS,CAAEtB,QAAAA,CAAQ,GAC3ChC,EAAWwD,IAAA,CAAKT,EAAOhE,GACvBgE,EAAMU,aAAA,CAAclC,EACtB,CACF,EAAG,CAACI,EAAa5C,EAASiD,EAAQ,EAGhCjB,CAAAA,EAAAA,EAAAA,GAAAA,EAACC,EAAAA,EAASA,CAAC+B,KAAA,CAAV,CACC9B,KAAK,QACL,cAAW,GACXhC,eAAgBF,EACf,GAAGJ,CAAA,CACJgF,SAAU,GACV1G,IAAKyC,EACLuC,MAAO,CACL,GAAGtD,EAAMsD,KAAA,CACT,GAAGY,CAAA,CACHe,SAAU,WACVjB,cAAe,OACftG,QAAS,EACTwH,OAAQ,CACV,GAGN,GAOF,SAASzC,EAASrC,CAAA,EAChB,OAAOA,EAAU,UAAY,WAC/B,CANA0F,EAAiBvG,WAAA,CAhES,mBD3H1B,IAAMyG,EAAa,CAAC,UAAW,YAAa,YAAa,aAAY,CAK/DC,EAAmB,aAGnB,CAACC,EAAyBC,EAAqB,CAAIxG,CAAAA,EAAAA,EAAAA,CAAAA,EAAmBsG,EAAkB,CAC5FG,EAAAA,EAA2BA,CAC3Bb,EACD,EACKc,EAA2BD,CAAAA,EAAAA,EAAAA,EAAAA,IAC3BE,EAAgBf,IAUhB,CAACgB,EAAoBC,EAAoB,CAC7CN,EAAgDD,GAiB5CQ,EAAmBC,EAAAA,UAAA,CACvB,CAAC1G,EAAqCC,KACpC,GAAM,CACJ0G,kBAAAA,CAAA,CACAxG,KAAAA,CAAA,CACAyG,aAAAA,CAAA,CACAnG,MAAOoG,CAAA,CACPtG,SAAAA,EAAW,GACXC,SAAAA,EAAW,GACXsG,YAAAA,CAAA,CACAC,IAAAA,CAAA,CACAC,KAAAA,EAAO,GACPC,cAAAA,CAAA,CACA,GAAGC,EACL,CAAIlH,EACEmH,EAAwBd,EAAyBM,GACjDS,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,EAAaN,GACzB,CAACtG,EAAO6G,EAAQ,CAAIhG,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB,CAC7CC,KAAMsF,EACNrF,YAAaoF,GAAgB,GAC7BnF,SAAUwF,EACVvF,OAAQuE,CACV,GAEA,MACE7D,CAAAA,EAAAA,EAAAA,GAAAA,EAACmE,EAAA,CACCtE,MAAO0E,EACPxG,KAAAA,EACAI,SAAAA,EACAC,SAAAA,EACAC,MAAAA,EACAwG,cAAeK,EAEfnF,SAAAC,CAAAA,EAAAA,EAAAA,GAAAA,EAAkBmF,EAAAA,EAAA,CAAjB,CACCC,QAAO,GACN,GAAGL,CAAA,CACJL,YAAAA,EACAC,IAAKK,EACLJ,KAAAA,EAEA7E,SAAAC,CAAAA,EAAAA,EAAAA,GAAAA,EAACC,EAAAA,EAAAA,CAAUoF,GAAA,CAAV,CACClF,KAAK,aACL,gBAAehC,EACf,mBAAkBuG,EAClB,gBAAetG,EAAW,GAAK,OAC/BuG,IAAKK,EACJ,GAAGF,CAAA,CACJ5I,IAAK2B,CAAA,EACP,EACF,EAGN,EAGFwG,CAAAA,EAAWlH,WAAA,CAAc0G,EAMzB,IAAMyB,EAAY,iBAQZC,EAAuBjB,EAAAA,UAAA,CAC3B,CAAC1G,EAAyCC,KACxC,GAAM,CAAE0G,kBAAAA,CAAA,CAAmBnG,SAAAA,CAAA,CAAU,GAAGoH,EAAU,CAAI5H,EAChD4D,EAAU4C,EAAqBkB,EAAWf,GAC1CkB,EAAajE,EAAQpD,QAAA,EAAYA,EACjC2G,EAAwBd,EAAyBM,GACjDmB,EAAaxB,EAAcK,GAC3BrI,EAAYoI,EAAAA,MAAA,CAAuC,MACnD3F,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,EAAgBf,EAAc3B,GAC7C8B,EAAUwD,EAAQnD,KAAA,GAAUmH,EAAUnH,KAAA,CACtCsH,EAA6BrB,EAAAA,MAAA,CAAO,IAiB1C,OAfMA,EAAAA,SAAA,CAAU,KACd,IAAMsB,EAAgB,IAChBhC,EAAWiC,QAAA,CAASrF,EAAMC,GAAG,GAC/BkF,CAAAA,EAAqBlG,OAAA,CAAU,GAEnC,EACMqG,EAAc,IAAOH,EAAqBlG,OAAA,CAAU,GAG1D,OAFAsG,SAASrG,gBAAA,CAAiB,UAAWkG,GACrCG,SAASrG,gBAAA,CAAiB,QAASoG,GAC5B,KACLC,SAASpG,mBAAA,CAAoB,UAAWiG,GACxCG,SAASpG,mBAAA,CAAoB,QAASmG,EACxC,CACF,EAAG,EAAE,EAGH9F,CAAAA,EAAAA,EAAAA,GAAAA,EAAkBmF,EAAAA,EAAA,CAAjB,CACCC,QAAO,GACN,GAAGL,CAAA,CACJiB,UAAW,CAACP,EACZQ,OAAQjI,EAER+B,SAAAC,CAAAA,EAAAA,EAAAA,GAAAA,EAACsD,EAAA,CACClF,SAAUqH,EACVtH,SAAUqD,EAAQrD,QAAA,CAClBH,QAAAA,EACC,GAAG0H,CAAA,CACH,GAAGF,CAAA,CACJzH,KAAMyD,EAAQzD,IAAA,CACd7B,IAAKyC,EACL6E,QAAS,IAAMhC,EAAQqD,aAAA,CAAcW,EAAUnH,KAAK,EACpDiC,UAAWC,CAAAA,EAAAA,EAAAA,CAAAA,EAAqB,IAEZ,UAAdC,EAAMC,GAAA,EAAiBD,EAAME,cAAA,EACnC,GACAwF,QAAS3F,CAAAA,EAAAA,EAAAA,CAAAA,EAAqBiF,EAAUU,OAAA,CAAS,KAM3CP,EAAqBlG,OAAA,EAASvD,EAAIuD,OAAA,EAAS0G,OACjD,EAAC,EACH,EAGN,EAGFZ,CAAAA,EAAepI,WAAA,CAAcmI,EAY7B,IAAMc,EAA4B9B,EAAAA,UAAA,CAChC,CAAC1G,EAA8CC,KAC7C,GAAM,CAAE0G,kBAAAA,CAAA,CAAmB,GAAGhD,EAAe,CAAI3D,EAC3C8H,EAAaxB,EAAcK,GACjC,MAAOvE,CAAAA,EAAAA,EAAAA,GAAAA,EAAC2D,EAAA,CAAgB,GAAG+B,CAAA,CAAa,GAAGnE,CAAA,CAAgBrF,IAAK2B,CAAA,EAClE,EAGFuI,CAAAA,EAAoBjJ,WAAA,CAdG,sBAkBvB,IAAM4F,EAAOsB,EACPgC,EAAOd,EACPvC,EAAYoD,mBElNlB,SAAAE,EAAAC,CAAA,EAAmD,MAA4QD,CAA5QA,EAAA,mBAAAE,QAAA,iBAAAA,OAAAC,QAAA,UAAAF,CAAA,EAAsG,cAAAA,CAAA,EAAqB,SAAAA,CAAA,EAAmB,OAAAA,GAAA,mBAAAC,QAAAD,EAAAG,WAAA,GAAAF,QAAAD,IAAAC,OAAAnE,SAAA,iBAAAkE,CAAA,GAA8HA,EAAA,CAc/T,SAAAI,EAAAC,CAAA,EAAwC,GAAAA,KAAA,IAAAA,EAAuB,kFAAyF,OAAAA,CAAA,CAExJ,SAAAC,EAAAC,CAAA,EAAmC,IAAAC,EAAA,mBAAAC,IAAA,IAAAA,IAAAC,KAAAA,EAAmrB,MAAAJ,CAAnnBA,EAAA,SAAAC,CAAA,EAAsD,GAAAA,OAAAA,GAMxHI,KAAAA,SAAAC,QAAA,CAAA1E,IAAA,CANwHqE,GAMxHM,OAAA,kBANwH,OAAAN,EAA+D,sBAAAA,EAAmC,sEAA6E,YAAAC,EAAA,CAAqC,GAAAA,EAAAM,GAAA,CAAAP,GAAA,OAAAC,EAAAO,GAAA,CAAAR,GAAiDC,EAAAzE,GAAA,CAAAwE,EAAAS,EAAA,CAA8B,SAAAA,IAAqB,OAAAC,EAAAV,EAAAW,UAAAC,EAAA,MAAAhB,WAAA,EAA0N,OAAhJa,EAAAlF,SAAA,CAAAsF,OAAAC,MAAA,CAAAd,EAAAzE,SAAA,EAAqDqE,YAAA,CAAerI,MAAAkJ,EAAAM,WAAA,GAAAC,SAAA,GAAAC,aAAA,MAA4EC,EAAAT,EAAAT,EAAA,GAA2CA,EAAA,CAEttB,SAAAU,EAAAS,CAAA,CAAAC,CAAA,CAAApB,CAAA,EAAuX,MAAAU,CAAzSA,EAAnCW,IAAmCC,QAAAC,SAAA,CAAyC,SAAAJ,CAAA,CAAAC,CAAA,CAAApB,CAAA,EAAwD,IAAAvK,EAAA,OAAgBA,EAAA+L,IAAA,CAAAC,KAAA,CAAAhM,EAAA2L,GAAyE,IAAAM,EAAA,GAAlDtB,CAAAA,SAAAuB,IAAA,CAAAF,KAAA,CAAAN,EAAA1L,EAAA,EAA2I,OAAvDuK,GAAAkB,EAAAQ,EAAA1B,EAAAzE,SAAA,EAAuDmG,CAAA,GAAsBD,KAAA,MAAAd,UAAA,CAEvX,SAAAU,IAAuC,uBAAAC,SAAA,CAAAA,QAAAC,SAAA,EAAwED,QAAAC,SAAA,CAAAK,IAAA,CAAxE,SAAkH,sBAAAC,MAAA,SAA8C,IAAsF,OAAhFC,QAAAvG,SAAA,CAAAwG,OAAA,CAAApG,IAAA,CAAA2F,QAAAC,SAAA,CAAAO,QAAA,kBAAgF,GAAe,MAAAE,EAAA,CAAY,UAIxT,SAAAd,EAAAe,CAAA,CAAAC,CAAA,EAA2I,MAAAhB,CAA1GA,EAAAL,OAAAsB,cAAA,WAAAF,CAAA,CAAAC,CAAA,EAA6F,OAAjBD,EAAAG,SAAA,CAAAF,EAAiBD,CAAA,GAAaA,EAAAC,EAAA,CAE3I,SAAAtB,EAAAqB,CAAA,EAAiL,MAAArB,CAAnJA,EAAAC,OAAAsB,cAAA,CAAAtB,OAAAwB,cAAA,UAAAJ,CAAA,EAAgG,OAAAA,EAAAG,SAAA,EAAAvB,OAAAwB,cAAA,CAAAJ,EAAA,GAAmDA,EAAA,kBAOjL,IAAAK,EAAA,SAAAC,CAAA,GACAC,SA1BAC,CAAA,CAAAC,CAAA,EAA2C,sBAAAA,GAAAA,OAAAA,EAA+D,qEAA6ED,CAAAA,EAAAlH,SAAA,CAAAsF,OAAAC,MAAA,CAAA4B,GAAAA,EAAAnH,SAAA,EAAyEqE,YAAA,CAAerI,MAAAkL,EAAAzB,SAAA,GAAAC,aAAA,MAA0DJ,OAAA8B,cAAA,CAAAF,EAAA,aAA+CzB,SAAA,KAAoB0B,GAAAxB,EAAAuB,EAAAC,EAAA,EA0B5YJ,EAAAC,GAEA,IA1BiCK,EA0BjCC,GA1BiCD,EAAAvB,IAA6D,WAAyC,IAAAyB,EAAAC,EAAAnC,EA0BvI0B,GA1BoX,OAA3GQ,EAApFF,EAAoFtB,QAAAC,SAAA,CAAAwB,EAAApC,UAAnDC,EAAA,MAAAhB,WAAA,EAAqHmD,EAAAtB,KAAA,MAAAd,WAAyCqC,SAEpXlD,CAAA,CAAAnE,CAAA,EAAkD,GAAAA,GAAA6D,CAAAA,WAAAA,EAAA7D,IAAA,mBAAAA,CAAA,EAA0E,OAAAA,EAAe,GAAAA,KAAA,IAAAA,EAA4B,4EAAmF,OAAAkE,EAAAC,EAAA,EAF0H,KAAAgD,EAAA,GA4BpX,SAAAR,EAAAW,CAAA,EACA,IAAAC,EASA,OAPAC,SAnCAzB,CAAA,CAAA0B,CAAA,EAAkD,IAAA1B,CAAAA,aAAA0B,CAAA,EAA0C,sDAmC5F,KAAAd,GAKAzB,OAAAsB,cAAA,CAAAtC,EAHAqD,EAAAL,EAAAlH,IAAA,MAAAsH,IAGAX,EAAA/G,SAAA,EACA2H,EAAAjM,IAAA,CAAAiM,EAAAtD,WAAA,CAAA3I,IAAA,CACAiM,CACA,CAEA,OA/CkMrC,OAAA8B,cAAA,CA+ClML,EA/CkM,aAAkDtB,SAAA,KA+CpPsB,CACA,EAACvC,EAAAsD,sIG/CMC,EAAA,SCMPC,EAAA,yCEPA,SAASC,EAAiBC,CAAA,CAAAC,CAAA,EAAaA,CAAAA,MAAAA,GAAAA,EAAAD,EAAAE,MAAA,GAAAD,CAAAA,EAAAD,EAAAE,MAAA,EAAuD,QAAAC,EAAA,EAAAC,EAAA,MAAAH,GAAuCE,EAAAF,EAASE,IAAOC,CAAA,CAAAD,EAAA,CAAAH,CAAA,CAAAG,EAAA,CAAoB,OAAAC,CAAA,CAEzK,SAAAC,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAAK,EAAAC,CAAA,EAAiC,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAAE,EAAAjD,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAErIpC,EAAAA,EAFkMiN,CAAA,CAAA7K,EAAA,CAEtJA,KAFsJ4K,EAEpI1D,OAAA8B,cAAA,CAFoI4B,EAAA5K,EAEpI,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFkB9F,EAElB,CAAApC,CAFkB,GAA4CsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAAV,EAAAjD,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAAoKkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,CAa1e,IAAAK,EAAA,CACAC,gBAAA,SAAAC,CAAA,CAAAC,CAAA,CAAAC,CAAA,EACA,SAAAC,MAAA,CAAAH,GAAAG,MAAA,CAAAD,EAAAE,GAAA,IAAAD,MAAA,CAAAF,EACA,CACA,EA6FA,SAAAI,EAAAhP,CAAA,CAAAiP,CAAA,CAAAC,CAAA,CAAAL,CAAA,CAAAM,CAAA,EACA,IHjHe7R,EACf8R,EACAC,EAGAV,EG4GAW,EAAAC,SAcOC,CAAA,CAAAC,CAAA,EACP,QAAsDC,EAAtDC,EAAuBC,SAtIiB9D,CAAA,CAAA+D,CAAA,EAAsB,IAAAC,EAAA,oBAAAvG,QAAAuC,CAAA,CAAAvC,OAAAC,QAAA,GAAAsC,CAAA,eAAiF,GAAAgE,EAAA,OAAAA,EAAAA,EAAAtK,IAAA,CAAAsG,EAAA,EAAAiE,IAAA,CAAAvE,IAAA,CAAAsE,GAAgD,GAAAE,MAAAC,OAAA,CAAAnE,IAAAgE,CAAAA,EAA8BI,SAEzLpE,CAAA,CAAAqE,CAAA,EAAc,GAAArE,GAAgB,oBAAAA,EAAA,OAAkCuB,EAAiBvB,EAAjFqE,KAAAA,GAA8F,IAAAC,EAAA1F,OAAAtF,SAAA,CAAA8E,QAAA,CAAA1E,IAAA,CAAAsG,GAAAuE,KAAA,OAAqH,GAA7D,WAAAD,GAAAtE,EAAArC,WAAA,EAAA2G,CAAAA,EAAAtE,EAAArC,WAAA,CAAA3I,IAAA,EAA6DsP,QAAAA,GAAAA,QAAAA,EAAA,OAAAJ,MAAAM,IAAA,CAAAxE,GAAsD,GAAAsE,cAAAA,GAAA,2CAAAG,IAAA,CAAAH,GAAA,OAAoF/C,EAAiBvB,EAA9WqE,KAAAA,GAA8W,EAF1JrE,EAAA,GAA+DgE,GAAAhE,CAAAA,EAAAgE,CAAA,EAAgB,IAAArC,EAAA,EAAW,yBAAqB,GAAA3B,EAAA0B,MAAA,EAA4BgD,KAAA,IAAc,CAASA,KAAA,GAAApP,MAAA0K,CAAA,CAAA2B,IAAA,GAAmC,0JAsIvY+B,GAA2B,EAAAE,EAAAC,GAAA,EAAAa,IAAA,EAA4B,CAC7G,IAAAlB,EAAAI,EAAAtO,KAAA,CAKA,GAAAkO,EAAAmB,qBAAA,GAAAjD,MAAA,IAEA,IAAAkD,EAAApB,EAAAmB,qBAAA,GAAAnB,EAAAmB,qBAAA,GAAAjD,MAAA,IAEA,GAAAiC,IAAAA,EAAAkB,MAAA,CAAAD,GACA,QAEA,CAGA,GAAQ,GAAAE,EAAAC,CAAA,EAAepB,EAAAH,EAAAwB,OAAA,IACvB,OAAAxB,CAEA,CACA,EAnCAT,EAAAkC,OAAA,GAAA/Q,UAEA,GHlHAoP,EAAA9R,CADeA,EGuHyB,CACxC8R,uBAAAF,kBAAAA,EACAG,mBAAAC,CAAAA,EAAA0B,sDAAA,KAAA7B,GAAAA,CAAA,IAAAA,EAAA8B,cAAA,CACAhC,YAAAA,EACAJ,SAAAA,CACA,GH3HAO,sBAAA,CACAC,EAAA/R,EAAA+R,kBAAA,CACA/R,EAAA2R,WAAA,CACA3R,EAAAuR,QAAA,CACAF,EAAA3O,EAAAkR,OAAA,KAAAC,OAAA7B,EAAAwB,OAAA,IAAA1B,EAAAE,EAAA8B,mBAAA,GAaA/B,GAAAC,EAAA+B,4BAAA,GAAA/B,EAAAA,MAAA,GAAA4B,OAAA,CAAA/D,EAAAmC,EAAA+B,4BAAA,IAAA/B,EAAAA,MAAA,IAEA,EDMAX,EAAAuC,OAAA,YAAApC,MAAA,CAAuDwC,EAAAC,EAAiB,iBAAAC,IAAA,GCFxE7C,GG4FA3O,CASA,CCnIA,SAASyR,EAAO7D,CAAA,CAAAC,CAAA,EAA2B,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAS4D,EAAatD,CAAA,EAAW,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAQgE,EAAO/G,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAE5HpC,EAAAA,EAFyLiN,CAAA,CAAA7K,EAAA,CAErKA,KAFqK4K,EAEnJ1D,OAAA8B,cAAA,CAFmJ4B,EAAA5K,EAEnJ,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFiC9F,EAEjC,CAAApC,CAFiC,GAA6BsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAmHoD,EAAO/G,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAA0CkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,CAqB1e,IAAAuD,EAAA,eAbAC,EAoBA,SAAAD,EAAAE,CAAA,CAAAC,CAAA,CAAAjD,CAAA,EAIA,IAHA7B,SAzBAzB,CAAA,CAAA0B,CAAA,EAAkD,IAAA1B,CAAAA,aAAA0B,CAAA,EAA0C,sDAyB5F,KAAA0E,GAGA,CAAAE,EACA,8CAGA,oBAAAA,EACA,mDAMA,oBAAAA,EAAA,CACA,GAAAA,MAAAA,CAAA,MAAAC,EACA,kDAGA,GAAU,GAAAC,EAAAlB,CAAA,EAAQiB,IAAoB,GAAAC,EAAAlB,CAAA,EAAQiB,EAAAE,SAAA,GAC9CnD,EAAAiD,EACA,IA+IAD,EAAAI,EACAC,EACAC,EACAtD,EAlJAuD,EAAAP,EAEA,IAAAQ,EAAA9B,IAAA,CAAA6B,GACA,0FAGA,IAAAE,EAAoC,GAAAC,EAAA1B,CAAA,EAAyBuB,EAAApI,KAAAA,EAAAA,KAAAA,EAAA6E,GAC7D2D,EAAAF,EAAAH,kBAAA,CAMA,GAHAL,EAFAQ,EAAAtS,MAAA,CAGA6R,EAAAW,EAEA,CAAAV,EACA,0DAEA,CACA,CAGA,IAAAA,EACA,yDAGA,oBAAAA,EACA,8DAII,GAAAW,EAAAC,EAAA,EAAgB7D,GAEpB,IAAA8D,GAgHAd,EAhHAA,EAgHAI,EAhHApD,EAmHAA,EAAA,IAAqB4D,EAAAG,EAAQ,CAAAX,GAN7B,aAAmB1B,IAAA,CASnBsB,IACAK,EAAAL,EACAhD,EAAAgE,mBAAA,CAAAX,GACAC,EAAAtD,EAAAsD,kBAAA,IAEAA,EAAAN,EAUA,CACAK,QAAAA,EACAC,mBAAAA,CACA,GAvIAD,EAAAS,EAAAT,OAAA,CACAC,EAAAQ,EAAAR,kBAAA,CAEA,KAAAD,OAAA,CAAAA,EACA,KAAAC,kBAAA,CAAAA,EACA,KAAAL,cAAA,CAAAA,EACA,KAAA9R,MAAA,UAAAmS,kBAAA,MAAAL,cAAA,CAKA,KAAAgB,WAAA,YACA,OAAAjE,CACA,CACA,CAwFA,OAhLA+C,EA0FA,EACApO,IAAA,SACApC,MAAA,SAAA2N,CAAA,EACA,KAAAA,GAAA,CAAAA,CACA,CACA,EAAG,CACHvL,IAAA,uBACApC,MAAA,eNlGe2R,EAAAjB,EAAAjD,EAGfmE,SMgGA,KAAAd,OAAA,CACA,MAAAA,OAAA,GNpGea,EMuG2B,KAAAZ,kBAAA,CNvG3BL,EMuG2B,KAAAA,cAAA,CNvG3BjD,EMuG2B,KAAAiE,WAAA,GNlG1C,CAFAE,EAAAC,IAFsBR,EAAAG,EAAQ,CAAA/D,GAE9BqE,6BAAA,CAAAH,IAMAC,EAAA/E,MAAA,UAAAiE,CAAA,MAMAe,EALA,MAOAA,CAFAA,EAAA,IAAsBR,EAAAG,EAAQ,CAL9B/D,IAOAgE,mBAAA,CAPAX,GASAe,EAAAE,aAAA,CAAAC,eAAA,GAAAjJ,OAAA,CAAA2H,EAAAtE,MAAA,IARA,GALA,GMkGA,CACA,EAAG,CACHhK,IAAA,aACApC,MAAA,WACA,MAAa,GAAAiS,EAAAxC,CAAA,EAAgB,MAC7ByC,GAAA,EACA,EAAO,KAAAR,WAAA,GACP,CACA,EAAG,CACHtP,IAAA,UACApC,MAAA,WACA,MAAa,GAAAmS,EAAA1C,CAAA,EAAa,MAC1ByC,GAAA,EACA,EAAO,KAAAR,WAAA,GACP,CACA,EAAG,CACHtP,IAAA,kBACApC,MAAA,WAEA,OAAAyN,IADyB4D,EAAAG,EAAQ,MAAAE,WAAA,IACjCU,0BAAA,MAAArB,kBAAA,CACA,CACA,EAAG,CACH3O,IAAA,UACApC,MAAA,SAAAqS,CAAA,EACA,YAAAzT,MAAA,GAAAyT,EAAAzT,MAAA,OAAA+O,GAAA,GAAA0E,EAAA1E,GAAA,CAgBA,EAAG,CACHvL,IAAA,UACApC,MAAA,WACA,MAAa,GAAAsS,EAAA7C,CAAA,EAAa,MAC1ByC,GAAA,EACA,EAAO,KAAAR,WAAA,GACP,CACA,EAAG,CACHtP,IAAA,SACApC,MAAA,SAAAuS,CAAA,CAAAxE,CAAA,EACA,OAAayE,SDzHE7O,CAAA,CAAAuK,CAAA,CAAAH,CAAA,CAAAN,CAAA,EAUf,GAPAM,EADAA,EACAhB,EAAAA,EAAA,GAA4CM,GAAAU,GAE5CV,EAGAI,EAAA,IAAiB4D,EAAAG,EAAQ,CAAA/D,GAEzB9J,EAAAmN,OAAA,EAAAnN,QAAAA,EAAAmN,OAAA,EAEA,IAAArD,EAAAgF,UAAA,CAAA9O,EAAAmN,OAAA,EACA,gCAAApD,MAAA,CAAA/J,EAAAmN,OAAA,GAGArD,EAAAqD,OAAA,CAAAnN,EAAAmN,OAAA,CACA,MAAI,IAAAnN,EAAAoN,kBAAA,CAEA,OAAApN,EAAA+O,KAAA,KADJjF,EAAAgE,mBAAA,CAAA9N,EAAAoN,kBAAA,EAGA,IA6FAxD,EAAAI,EAAAF,EAAAH,EAAAC,EAAAI,EAAAF,EAAAH,EAAAC,EAAAI,EAAAF,EAAAH,EAzFA1O,EAJAmS,EAAAtD,EAAAsD,kBAAA,GACAL,EAAA3C,EAAAmE,EAAA,CAAAvO,EAAA+M,cAAA,CAAA/M,EAAA+O,KAAA,CAKA,OAAAxE,GACA,eAGA,IAAAwC,EACA,SAIA,OA8EAnD,EA/EA3O,EAAAgP,EAAA8C,EAAA/M,EAAAkK,WAAA,YAAAJ,EAAAM,GA+EAJ,EA9EAhK,EAAAgK,GAAA,CA8EAF,EA9EAA,EA8EAH,EA9EAS,EAAAT,eAAA,CA+EAK,EAAAL,EAAAC,EAAAI,EAAAF,GAAAF,CA7EA,qBAGA,IAAAmD,EACA,UAAAhD,MAAA,CAAAqD,GAKA,OAFAnS,EAAAgP,EAAA8C,EAAA,qBAAAjD,EAAAM,GAqEAR,EApEA3O,EAAA,IAAA8O,MAAA,CAAAqD,EAAA,KAAArD,MAAA,CAAA9O,GAoEA+O,EAnEAhK,EAAAgK,GAAA,CAmEAF,EAnEAA,EAmEAH,EAnEAS,EAAAT,eAAA,CAoEAK,EAAAL,EAAAC,EAAAI,EAAAF,GAAAF,CAlEA,aAEA,UAAAG,MAAA,CAAAqD,GAAArD,MAAA,CAAAgD,EAEA,eACA,OAAaiC,SDrBNzW,CAAA,EACP,IAAA0C,EAAA1C,EAAA0C,MAAA,CACA+O,EAAAzR,EAAAyR,GAAA,CAEA,IAAA/O,EACA,SAGA,GAAAA,MAAAA,CAAA,IACA,yEAGA,aAAA8O,MAAA,CAAA9O,GAAA8O,MAAA,CAAAC,EAAA,QAA8CA,EAAA,GAC9C,ECQ0B,CAC1B/O,OAAA,IAAA8O,MAAA,CAAAqD,GAAArD,MAAA,CAAAgD,GACA/C,IAAAhK,EAAAgK,GAAA,EAOA,WACA,IAAAI,EAAA6E,WAAA,CACA,OAIA,OA6CArF,EA9CAsF,SAkDAnC,CAAA,CAAA7C,CAAA,CAAAkD,CAAA,CAAA6B,CAAA,CAAAnF,CAAA,EAGA,GAAAqF,CAF+B,EAAAzB,EAAA0B,EAAA,EAAqBH,EAAAnF,EAAAA,QAAA,IAEpDsD,EAAA,CACA,IFtJeY,EAAAlE,EACfuF,EEqJAzF,EAAAK,EAAA8C,EAAA7C,EAAA,WAAAJ,SAGA,MAAAsD,EACAA,EAAA,IAAAxD,EAYAA,CACA,CAEA,IAAA0F,GFzKetB,EEyKe/I,KAAAA,EFzKf6E,EEyKeA,EAAAA,QAAA,CFrK9B,CAFAuF,CADAA,EAAA,IAA4B3B,EAAAG,EAAQ,CAAA/D,IACpCgE,mBAAA,CEuK8BmB,EFvK9BjB,GAEAqB,EAAAE,gBAAA,IACAF,EAAAE,gBAAA,GAGAlH,EAAAmD,IAAA,CAAA6D,EAAAG,SAAA,IACAH,EAAAG,SAAA,WEkKA,GAAAF,EACA,SAAAvF,MAAA,CAAAuF,EAAA,KAAAvF,MAAA,CAAAqD,EAAA,KAAArD,MAAA,CAAAE,EAAA8C,EAAA,qBAAAjD,GAEA,EA9EAiD,EAAA/M,EAAAkK,WAAA,CAAAkD,EAAAhD,EAAA6E,WAAA,CAAAnF,GA8CAE,EA7CAhK,EAAAgK,GAAA,CA6CAF,EA7CAA,EA6CAH,EA7CAS,EAAAT,eAAA,CA8CAK,EAAAL,EAAAC,EAAAI,EAAAF,GAAAF,CA5CA,SACA,sEAAAG,MAAA,CAAAQ,EAAA,KACA,CACA,EC+CyB,KAAAqE,EAAAxE,EAA0BuC,EAAcA,EAAa,GAAGvC,GAAA,GAAc,CAC/FmE,GAAA,EACA,GAAO,CACPA,GAAA,EACA,EAAO,KAAAR,WAAA,GACP,CACA,EAAG,CACHtP,IAAA,iBACApC,MAAA,SAAA+N,CAAA,EACA,YAAAG,MAAA,YAAAH,EACA,CACA,EAAG,CACH3L,IAAA,sBACApC,MAAA,SAAA+N,CAAA,EACA,YAAAG,MAAA,iBAAAH,EACA,CACA,EAAG,CACH3L,IAAA,SACApC,MAAA,SAAA+N,CAAA,EACA,YAAAG,MAAA,WAAAH,EACA,CACA,EAAG,CA9K2DqF,SAF9DpG,CAAA,CAAAzN,CAAA,EAA4C,QAAA8M,EAAA,EAAgBA,EAAA9M,EAAA6M,MAAA,CAAkBC,IAAA,CAAO,IAAAzI,EAAArE,CAAA,CAAA8M,EAAA,CAA2BzI,EAAA4F,UAAA,CAAA5F,EAAA4F,UAAA,KAAwD5F,EAAA8F,YAAA,IAAgC,UAAA9F,GAAAA,CAAAA,EAAA6F,QAAA,KAAuDH,OAAA8B,cAAA,CAAA4B,EAAApJ,EAAAxB,GAAA,CAAAwB,EAAA,GAEjMiI,EAAA7H,SAAA,CAAAwM,GAAoIlH,OAAA8B,cAAA,CA0FlMmF,EA1FkM,aAAkD9G,SAAA,KAgLpP8G,CACA,IAmCAU,EAAA,0FC7NO,IAAAoC,EAAA,EAGAC,EAAA,GAEAC,EAAA,EAGAC,EAAA,eAaAC,EAAA,GAAA/F,MAAA,CAXP,WAWOA,MAAA,CAVP,MAUOA,MAAA,CATP,MASOA,MAAA,CARA,gBAQAA,MAAA,CAPP,gBAOOA,MAAA,CALP,QAMOgG,EAAA,sBCnBP,SAAAC,EAAAzH,CAAA,CAAAC,CAAA,EAAuCA,CAAAA,MAAAA,GAAAA,EAAAD,EAAAE,MAAA,GAAAD,CAAAA,EAAAD,EAAAE,MAAA,EAAuD,QAAAC,EAAA,EAAAC,EAAA,MAAAH,GAAuCE,EAAAF,EAASE,IAAOC,CAAA,CAAAD,EAAA,CAAAH,CAAA,CAAAG,EAAA,CAAoB,OAAAC,CAAA,CCH1J,SAAAsH,EAAAlD,CAAA,CAAAjD,CAAA,EACf,OAAAoG,SAIOA,EAAAnD,CAAA,CAAA7O,CAAA,CAAA4L,CAAA,EACP,IAAAqG,EAAArG,EAAA5L,IAAA,CAAAA,GAQAkS,EAAAD,GAAAA,EAAA9B,eAAA,IAAAvE,EAAAuE,eAAA,GAGA,IAAA+B,EACA,oBAGA,GAAAlS,yBAAAA,EAAA,CAIA,IAAA4L,EAAA5L,IAAA,eAGA,OAAAgS,EAAAnD,EAAA,SAAAjD,GAGA,IAAAuG,EAAAvG,EAAA5L,IAAA,WAEAmS,GAMAD,CAAAA,EAAyBE,SD5BV/V,CAAA,CAAAgW,CAAA,EAGf,QAAA5F,EAFA6F,EAAAjW,EAAA+Q,KAAA,GAEAV,EAAA6F,SAfA1J,CAAA,CAAA+D,CAAA,EAA8D,IAAAC,EAAA,oBAAAvG,QAAAuC,CAAA,CAAAvC,OAAAC,QAAA,GAAAsC,CAAA,eAAiF,GAAAgE,EAAA,OAAAA,EAAAA,EAAAtK,IAAA,CAAAsG,EAAA,EAAAiE,IAAA,CAAAvE,IAAA,CAAAsE,GAAgD,GAAAE,MAAAC,OAAA,CAAAnE,IAAAgE,CAAAA,EAAA2F,SAE/L3J,CAAA,CAAAqE,CAAA,EAAkD,GAAArE,GAAgB,oBAAAA,EAAA,OAAAiJ,EAAAjJ,EAAlEqE,KAAAA,GAAkI,IAAAC,EAAA1F,OAAAtF,SAAA,CAAA8E,QAAA,CAAA1E,IAAA,CAAAsG,GAAAuE,KAAA,OAAqH,GAA7D,WAAAD,GAAAtE,EAAArC,WAAA,EAAA2G,CAAAA,EAAAtE,EAAArC,WAAA,CAAA3I,IAAA,EAA6DsP,QAAAA,GAAAA,QAAAA,EAAA,OAAAJ,MAAAM,IAAA,CAAAxE,GAAsD,GAAAsE,cAAAA,GAAA,2CAAAG,IAAA,CAAAH,GAAA,OAAA2E,EAAAjJ,EAA7SqE,KAAAA,GAA6S,EAF9GrE,EAAA,GAAwHgE,GAAAhE,CAAAA,EAAAgE,CAAA,EAAgB,IAAArC,EAAA,EAAW,yBAAqB,GAAA3B,EAAA0B,MAAA,EAA4BgD,KAAA,IAAc,CAASA,KAAA,GAAApP,MAAA0K,CAAA,CAAA2B,IAAA,GAAmC,0JAe7b6H,GAAkE,EAAA5F,EAAAC,GAAA,EAAAa,IAAA,EAA4B,CAC9F,IAAAkF,EAAAhG,EAAAtO,KAAA,CAEA,EAAA9B,EAAA6K,OAAA,CAAAuL,IACAH,EAAAlK,IAAA,CAAAqK,EAEA,CAEA,OAAAH,EAAAI,IAAA,UAAArW,CAAA,CAAAgW,CAAA,EACA,OAAAhW,EAAAgW,CACA,EAMA,ECSoCH,EAAAC,EAAAhC,eAAA,IASpC,MACA,GAAAnQ,GAAA,CAAAiS,EACA,uBAGA,IAAAU,EAAA9D,EAAAtE,MAAA,CAQAqI,EAAAV,CAAA,WAEA,IAAAS,EACA,cAGAC,EAAAD,EACA,YAGAT,CAAA,CAAAA,EAAA3H,MAAA,IAAAoI,EACA,WAIAT,EAAAhL,OAAA,CAAAyL,EAAA,oCACA,EA5EA9D,EAAA9H,KAAAA,EAAA6E,EACA,mECMAiH,EAAA,SAAAC,CAAA,EACA,WAAAjH,MAAA,CAAqBkH,EAAAC,EAAY,SAAKnH,MAAA,CAAAiH,EAAA,KACtC,EAUe,SAAAG,EAAAC,CAAA,EAoBf,IAAAC,EAAA,eAIAC,EAAA,wBAsBAC,EAAA,cAsCA,MAAAC,QA5BAT,EAhDA,MA4EA,IAzBAM,CAAAA,EA3BA,mDA2BAC,CAAA,EAAAP,EAnDA,MA4EAS,MAtBAH,CAAAA,EAzBA,uBAyBAC,CAAA,EAAAP,EAhDA,KAsEAS,WAnBAT,EAhDA,KAmEAS,KAhBAD,CAAAA,EAfA,aAeAD,CAAA,EAAAP,EAzDA,MAyEAS,MAbAD,CAAAA,EAAA,SAAAD,CAAA,EAAAP,EAzDA,KAaA,IA0DA,8DCxGAU,EAAA,YAAgDlF,EAAA2E,EAAY,wCEqB7C,SAAA1D,EAAAvS,CAAA,CAAAkS,CAAA,CAAAa,CAAA,CAAAlE,CAAA,EACf,IAAA7O,EACA,SAWA,GAAAA,MAAAA,CAAA,KAGA,IAXAyW,EAWAC,EAA2BC,SFpCZ3W,CAAA,CAAAkS,CAAA,CAAAa,CAAA,CAAAlE,CAAA,EACf,GAAAqD,GAKA,IAAAkC,EAAA,IAA4B3B,EAAAG,EAAQ,CAAA/D,GACpCuF,EAAAvB,mBAAA,CAAAX,EAAAa,GACA,IAAA6D,EAAA,IAAAzF,OAAAiD,EAAAG,SAAA,IAEA,GAAAvU,IAAAA,EAAA2Q,MAAA,CAAAiG,IAUA,IAAAC,EAAA7W,CALAA,EAAAA,EAAAqQ,KAAA,CAAArQ,EAAA8W,KAAA,CAAAF,EAAA,IAAApJ,MAAA,GAKAsJ,KAAA,CAAAN,GAEA,GAAAK,CAAAA,GAAAA,MAAAA,CAAA,MAAAA,CAAAA,CAAA,IAAArJ,MAAA,KACAqJ,MAAAA,CAAA,IAKA,OAAA7W,GACA,EEOyCA,EAAAkS,EAAAa,EAAAlE,GAIzC,GAAA6H,GAAAA,IAAA1W,EACAyW,EAAA,GACAzW,EAAA,IAAA0W,MACM,CAKN,GAAAxE,GAAAa,EAAA,CACA,IAAAT,EAAoCyE,SDrCrB/W,CAAA,CAAAkS,CAAA,CAAAa,CAAA,CAAAlE,CAAA,EACf,IAAAsD,EAAAD,EAAqC,GAAAO,EAAA0B,EAAA,EAAqBjC,EAAArD,GAAAkE,EAE1D,GAAA/S,IAAAA,EAAAmK,OAAA,CAAAgI,GAAA,CAEAtD,CADAA,EAAA,IAAmB4D,EAAAG,EAAQ,CAAA/D,EAAA,EAC3BgE,mBAAA,CAAAX,EAAAa,GACA,IAAAiE,EAAAhX,EAAAqQ,KAAA,CAAA8B,EAAA3E,MAAA,EAGAyJ,EAAAC,CADgC,EAAAC,EAAAtG,CAAA,EAAqBmG,EAAAnI,GACrDiD,cAAA,CAGAA,EAAAsF,CADiC,EAAAD,EAAAtG,CAAA,EAAqB7Q,EAAA6O,GACtDiD,cAAA,CAUA,IAAS,GAAAlB,EAAAC,CAAA,EAAeiB,EAAAjD,EAAAwI,qBAAA,KAAsD,GAAAzG,EAAAC,CAAA,EAAeoG,EAAApI,EAAAwI,qBAAA,KAAqE,gBAAArC,EAAAnE,CAAA,EAAiBiB,EAAAjD,GACnL,OACAsD,mBAAAA,EACAnS,OAAAgX,CACA,CAEA,CAEA,OACAhX,OAAAA,CACA,CACA,ECImGA,EAAAkS,EAAAa,EAAAlE,GACnGsD,EAAAG,EAAAH,kBAAA,CACAmF,EAAAhF,EAAAtS,MAAA,CAEA,GAAAmS,EACA,OACAoF,yBAAA,gCACApF,mBAAAA,EACAnS,OAAAsX,CACA,CAEA,CAEA,OAGAtX,OAAAA,CACA,CACA,CACA,CAGA,GAAAA,MAAAA,CAAA,IACA,SAGA6O,EAAA,IAAiB4D,EAAAG,EAAQ,CAAA/D,GAYzB,IAFA,IAAApB,EAAA,EAEAA,EAAA,GAAkB6D,EAAAkG,EAAuB,EAAA/J,GAAAzN,EAAAwN,MAAA,GACzC,IAAAgF,EAAAxS,EAAAqQ,KAAA,GAAA5C,GAEA,GAAAoB,EAAA4I,cAAA,CAAAjF,GAEA,OADA3D,EAAAgE,mBAAA,CAAAL,GACA,CACA+E,yBAAAd,EAAA,oDACAtE,mBAAAK,EACAxS,OAAAA,EAAAqQ,KAAA,CAAA5C,EACA,CAGAA,CAAAA,GACA,CAEA,QACA,8DE7Fe,SAAA0J,EAAAnX,CAAA,CAAA6O,CAAA,EAUf,IAAAqI,EAA8BQ,SDVf1X,CAAA,CAAA6O,CAAA,EACf,GAAA7O,GAAA6O,EAAAsE,aAAA,CAAAwE,wBAAA,IAIA,IAAAC,EAAA,cAAA/I,EAAAsE,aAAA,CAAAwE,wBAAA,QACAE,EAAAD,EAAAE,IAAA,CAAA9X,GAEA,GAAA6X,EAAA,CAmBA,IAlBA/F,EACA7C,EAqDAgC,EApCA8G,EAAAF,EAAArK,MAAA,GACAwK,EAAAD,EAAA,GAAAF,CAAA,CAAAE,EAAA,CAEA,GAAAlJ,EAAAoJ,2BAAA,IAAAD,EACAlG,EAAA9R,EAAAkR,OAAA,CAAA0G,EAAA/I,EAAAoJ,2BAAA,IAGAF,EAAA,GACA9I,CAAAA,EAAA4I,CAAA,SAQA,CAMA,IAAAK,EAAAL,CAAA,IACA/F,EAAA9R,EAAAqQ,KAAA,CAAA6H,EAAA1K,MAAA,EAGAwK,GACA/I,CAAAA,EAAA4I,CAAA,IAEA,CASA,GAAAG,EAAA,CACA,IAAAG,EAAAnY,EAAAmK,OAAA,CAAA0N,CAAA,KACA7X,EAAAqQ,KAAA,GAAA8H,KAOAtJ,EAAAsE,aAAA,CAAAlC,cAAA,IACAA,CAAAA,EAAApC,EAAAsE,aAAA,CAAAlC,cAAA,GAEA,MACAA,EAAA4G,CAAA,IAGA,OACA/F,eAAAA,EACAb,eAAAA,EACAhC,YAAAA,CACA,CACA,CACA,CAEA,OACA6C,eAAA9R,CACA,CACA,EClF+EA,EAAA6O,GAC/EI,EAAAiI,EAAAjI,WAAA,CACA6C,EAAAoF,EAAApF,cAAA,QAEA,IAAA9R,KAkDM,KAAA4Q,EAAAC,CAAA,EAjDN7Q,EAiDqB6O,EAAAwI,qBAAA,KAA6D,GAAAzG,EAAAC,CAAA,EAjDlFiB,EAiDiGjD,EAAAwI,qBAAA,MAxCjGxI,EAAAuE,eAAA,IASA,CAAAgF,SAmDAtG,CAAA,CAAAjD,CAAA,EACA,OAAU,GAAAmG,EAAAnE,CAAA,EAAiBiB,EAAAjD,IAC3B,gBACA,qBAIA,QAEA,SACA,QACA,CACA,EA/DAiD,EAAAjD,IAhBA,CACAiD,eAAA9R,CACA,EAuBA,CACA8R,eAAAA,EACA7C,YAAAA,CACA,CACA,8DCvDA,SAAA8F,EAAAzH,CAAA,CAAAC,CAAA,EAAuCA,CAAAA,MAAAA,GAAAA,EAAAD,EAAAE,MAAA,GAAAD,CAAAA,EAAAD,EAAAE,MAAA,EAAuD,QAAAC,EAAA,EAAAC,EAAA,MAAAH,GAAuCE,EAAAF,EAASE,IAAOC,CAAA,CAAAD,EAAA,CAAAH,CAAA,CAAAG,EAAA,CAAoB,OAAAC,CAAA,CAIzK,IAAA2K,EAAA,uGAEe,SAAA3E,EAAA3O,CAAA,CAAAoK,CAAA,CAAAN,CAAA,EAOf,GAJAM,EAAAA,GAAA,GAIA,EAAA+C,OAAA,EAAAnN,EAAAoN,kBAAA,EAKAtD,CADAA,EAAA,IAAiByJ,EAAA1F,EAAQ,CAAA/D,EAAA,EACzBgE,mBAAA,CAAA9N,EAAAmN,OAAA,CAAAnN,EAAAoN,kBAAA,EACA,IAAAL,EAAA3C,EAAAmE,EAAA,CAAAvO,EAAA+M,cAAA,CAAA/M,EAAA+O,KAAA,CAIA,GAAO,GAAAyE,EAAA1H,CAAA,EAAeiB,EAAAjD,EAAAwI,qBAAA,KAKtB,GAAAmB,EAAA1G,EAAA,aAAAjD,UAKA,EAAA5L,IAAA,YAAA4L,KAAAA,EAAA5L,IAAA,WAAA6N,OAAA,IAWA,CAAAjC,EAAA5L,IAAA,YAQAuV,EAAA1G,EAAA,SAAAjD,GAlBA,uBAsBA,aAGA,QAAAa,EAAAC,EAAA6F,SA/DA1J,CAAA,CAAA+D,CAAA,EAA8D,IAAAC,EAAA,oBAAAvG,QAAAuC,CAAA,CAAAvC,OAAAC,QAAA,GAAAsC,CAAA,eAAiF,GAAAgE,EAAA,OAAAA,EAAAA,EAAAtK,IAAA,CAAAsG,EAAA,EAAAiE,IAAA,CAAAvE,IAAA,CAAAsE,GAAgD,GAAAE,MAAAC,OAAA,CAAAnE,IAAAgE,CAAAA,EAAA2F,SAE/L3J,CAAA,CAAAqE,CAAA,EAAkD,GAAArE,GAAgB,oBAAAA,EAAA,OAAAiJ,EAAAjJ,EAAlEqE,KAAAA,GAAkI,IAAAC,EAAA1F,OAAAtF,SAAA,CAAA8E,QAAA,CAAA1E,IAAA,CAAAsG,GAAAuE,KAAA,OAAqH,GAA7D,WAAAD,GAAAtE,EAAArC,WAAA,EAAA2G,CAAAA,EAAAtE,EAAArC,WAAA,CAAA3I,IAAA,EAA6DsP,QAAAA,GAAAA,QAAAA,EAAA,OAAAJ,MAAAM,IAAA,CAAAxE,GAAsD,GAAAsE,cAAAA,GAAA,2CAAAG,IAAA,CAAAH,GAAA,OAAA2E,EAAAjJ,EAA7SqE,KAAAA,GAA6S,EAF9GrE,EAAA,GAAwHgE,GAAAhE,CAAAA,EAAAgE,CAAA,EAAgB,IAAArC,EAAA,EAAW,yBAAqB,GAAA3B,EAAA0B,MAAA,EAA4BgD,KAAA,IAAc,CAASA,KAAA,GAAApP,MAAA0K,CAAA,CAAA2B,IAAA,GAAmC,0JA+D7b4K,GAA2F,EAAA3I,EAAAC,GAAA,EAAAa,IAAA,EAA4B,CACvH,IAAAvN,EAAAyM,EAAAtO,KAAA,CAEA,GAAAoX,EAAA1G,EAAA7O,EAAA4L,GACA,OAAA5L,CAEA,GACA,CACO,SAAAuV,EAAA1G,CAAA,CAAA7O,CAAA,CAAA4L,CAAA,QAGP,GAFA5L,CAAAA,EAAA4L,EAAA5L,IAAA,CAAAA,EAAA,GAEA,CAAAA,EAAA6N,OAAA,IAUA7N,EAAAmQ,eAAA,IAAAnQ,EAAAA,EAAAmQ,eAAA,GAAAjJ,OAAA,CAAA2H,EAAAtE,MAAA,IAIS,GAAA+K,EAAA1H,CAAA,EAAeiB,EAAA7O,EAAA6N,OAAA,GACxB,oCCzFA,IAAA2H,EAAA,GAA0BhP,WAAA,CACX,SAAAsI,EAAAnE,CAAA,EACf,OAAAA,MAAAA,GAAAA,EAAAnE,WAAA,GAAAgP,CACA,wECuBAC,EAAA,IAA4C1C,EAAAC,EAAY,MAAUD,EAAA2C,EAAkB,KAK7EC,EAAA,IAA+B5C,EAAA6C,EAAU,CAAzC,aAAqE7C,EAAAzE,EAAiB,CAAtF,MAAsGyE,EAAAC,EAAY,CAAlH,UAA2ID,EAAAzE,EAAiB,CAAGyE,EAAAC,EAAY,MAUlL6C,EAAA,YAA8D9C,EAAA6C,EAAU,CAAxE,aAAoG7C,EAAAzE,EAAiB,CAArH,MAAqIyE,EAAAC,EAAY,CAAjJ,WAAiK,KAKjK8C,EAAA,OACA,IAAAL,EAAA,MALOE,CAAAA,EACP,OAAQ,GAAAI,EAAAnI,CAAA,IAIR,MACA,KAQe,SAAAoI,EAAAjZ,CAAA,EACf,OAAAA,EAAAwN,MAAA,EAA0BwI,EAAA2C,EAAkB,EAAAI,EAAAxI,IAAA,CAAAvQ,EAC5C,CAOO,SAAAkZ,EAAAlZ,CAAA,EACP,OAAA8Y,EAAAvI,IAAA,CAAAvQ,EACA,oCC9De,SAAA4Q,EAAAuI,CAAA,CAAAC,CAAA,EAIf,OADAD,EAAAA,GAAA,GACA,cAAAC,EAAA,MAAA7I,IAAA,CAAA4I,EACA,sECMe,SAAAE,EAAAtU,CAAA,CAAAoK,CAAA,CAAAN,CAAA,EAQf,GANA7E,KAAAA,IAAAmF,GACAA,CAAAA,EAAA,IAGAN,EAAA,IAAiByJ,EAAA1F,EAAQ,CAAA/D,GAEzBM,EAAAmE,EAAA,EACA,IAAAvO,EAAAoN,kBAAA,CACA,kDAGAtD,EAAAgE,mBAAA,CAAA9N,EAAAoN,kBAAA,CACA,KAAI,CACJ,IAAApN,EAAA+O,KAAA,CACA,SAGA,GAAA/O,EAAAmN,OAAA,EACA,IAAArD,EAAAgF,UAAA,CAAA9O,EAAAmN,OAAA,EACA,gCAAApD,MAAA,CAAA/J,EAAAmN,OAAA,GAGArD,EAAAqD,OAAA,CAAAnN,EAAAmN,OAAA,CACA,KAAM,CACN,IAAAnN,EAAAoN,kBAAA,CACA,kDAGAtD,EAAAgE,mBAAA,CAAA9N,EAAAoN,kBAAA,CACA,CACA,CAGA,GAAAtD,EAAAuE,eAAA,GACA,OAAAkG,EAAAvU,EAAA+O,KAAA,EAAA/O,EAAA+M,cAAA,CAAAjD,GAQA,GAAA9J,EAAAoN,kBAAA,EAAAtD,EAAA2E,0BAAA,CAAAzO,EAAAoN,kBAAA,EAGA,QAEA,8GAGA,CACO,SAAAmH,EAAAxH,CAAA,CAAAjD,CAAA,QAGP,gBADU,GAAA0K,EAAA1I,CAAA,EAAiBiB,EAAAjD,EAW3B,yEC5Ce,SAAA2K,EAAAzU,CAAA,CAAAoK,CAAA,CAAAN,CAAA,EAQf,GALAM,EAAAA,GAAA,GAEAN,CADAA,EAAA,IAAiByJ,EAAA1F,EAAQ,CAAA/D,EAAA,EACzBgE,mBAAA,CAAA9N,EAAAmN,OAAA,CAAAnN,EAAAoN,kBAAA,EAGAtD,EAAA4K,QAAA,GACA,OAAW,KAAazP,IAAb,GAAA0P,EAAA7I,CAAA,EAAa9L,EAAAoK,EAAAN,EAAAA,QAAA,EAKxB,IAAAiD,EAAA3C,EAAAmE,EAAA,CAAAvO,EAAA+M,cAAA,CAAA/M,EAAA+O,KAAA,CACA,MAAS,GAAA6F,EAAA9I,CAAA,EAAeiB,EAAAjD,EAAAwI,qBAAA,GACxB,mBC9Ce,SAASuC,EAACta,CAAA,CAAAgW,CAAA,EACzBhW,EAAAA,EAAAua,KAAA,MACAvE,EAAAA,EAAAuE,KAAA,MAIA,QAHAC,EAAAxa,CAAA,IAAAua,KAAA,MACAE,EAAAzE,CAAA,IAAAuE,KAAA,MAEApM,EAAA,EAAkBA,EAAA,EAAOA,IAAA,CACzB,IAAAuM,EAAAC,OAAAH,CAAA,CAAArM,EAAA,EACAyM,EAAAD,OAAAF,CAAA,CAAAtM,EAAA,EACA,GAAAuM,EAAAE,EAAA,SACA,GAAAA,EAAAF,EAAA,UACA,IAAAG,MAAAH,IAAAG,MAAAD,GAAA,SACA,GAAAC,MAAAH,IAAA,CAAAG,MAAAD,GAAA,SACA,QAEA,MAAA5E,CAAA,IACAhW,CAAA,IAAAgW,CAAA,MAAAhW,CAAA,IAAAgW,CAAA,SAGA,CAAAhW,CAAA,KAAAgW,CAAA,MAAAhW,CAAA,MAAAgW,CAAA,QACA,6DC5BA,SAAAjM,EAAAC,CAAA,EAAmD,MAA4QD,CAA5QA,EAAA,mBAAAE,QAAA,iBAAAA,OAAAC,QAAA,UAAAF,CAAA,EAAsG,cAAAA,CAAA,EAAqB,SAAAA,CAAA,EAAmB,OAAAA,GAAA,mBAAAC,QAAAD,EAAAG,WAAA,GAAAF,QAAAD,IAAAC,OAAAnE,SAAA,iBAAAkE,CAAA,GAA8HA,EAAA,CAE/T,SAAA0D,EAAAzB,CAAA,CAAA0B,CAAA,EAAkD,IAAA1B,CAAAA,aAAA0B,CAAA,EAA0C,qDAE5F,SAAAuH,EAAApG,CAAA,CAAAzN,CAAA,EAA4C,QAAA8M,EAAA,EAAgBA,EAAA9M,EAAA6M,MAAA,CAAkBC,IAAA,CAAO,IAAAzI,EAAArE,CAAA,CAAA8M,EAAA,CAA2BzI,EAAA4F,UAAA,CAAA5F,EAAA4F,UAAA,KAAwD5F,EAAA8F,YAAA,IAAgC,UAAA9F,GAAAA,CAAAA,EAAA6F,QAAA,KAAuDH,OAAA8B,cAAA,CAAA4B,EAAApJ,EAAAxB,GAAA,CAAAwB,EAAA,EAE/P,SAAAoV,EAAAnN,CAAA,CAAA2E,CAAA,CAAAyI,CAAA,EAAwQ,OAA1MzI,GAAA4C,EAAAvH,EAAA7H,SAAA,CAAAwM,GAAsEyI,GAAA7F,EAAAvH,EAAAoN,GAA8D3P,OAAA8B,cAAA,CAAAS,EAAA,aAAkDpC,SAAA,KAAoBoC,CAAA,CAWxQ,IAAAqN,EAAA,SACAC,EAAA,QAKAC,EAAA,WACA,SAAAA,EAAA3L,CAAA,EACA7B,EAAA,KAAAwN,GAEAC,EAAA5L,GACA,KAAAA,QAAA,CAAAA,EACA6L,EAAAlV,IAAA,MAAAqJ,EACA,CAgPA,OA9OAuL,EAAAI,EAAA,EACAhX,IAAA,eACApC,MAAA,WACA,OAAAsJ,OAAAoD,IAAA,MAAAe,QAAA,CAAAmD,SAAA,EAAA/D,MAAA,UAAA5O,CAAA,EACA,MAAAA,QAAAA,CACA,EACA,CACA,EAAG,CACHmE,IAAA,qBACApC,MAAA,SAAAuZ,CAAA,EACA,YAAA9L,QAAA,CAAAmD,SAAA,CAAA2I,EAAA,CAEA,EAAG,CACHnX,IAAA,gBACApC,MAAA,WACA,SAAAwZ,EAAA,QAAAtH,EAAA,QAAAuH,EAAA,CAIA,YAAAhM,QAAA,CAAAiM,aAAA,OAAAjM,QAAA,CAAAkM,eAAA,CAEA,EAAG,CACHvX,IAAA,aACApC,MAAA,SAAA8Q,CAAA,EACA,YAAAlI,IAAA,KAAAgR,kBAAA,CAAA9I,EACA,CACA,EAAG,CACH1O,IAAA,iBACApC,MAAA,SAAA2R,CAAA,EACA,QAAAG,6BAAA,CAAAH,GACA,SAGA,QAAA+H,aAAA,GACA,SAAAA,aAAA,GAAA/H,EAAA,CACA,QACA,KACQ,CAER,IAAAkI,EAAA,KAAAC,mBAAA,GAAAnI,EAAA,CAEA,GAAAkI,GAAAA,IAAAA,EAAAzN,MAAA,EAAAyN,QAAAA,CAAA,IACA,QAEA,CACA,CACA,EAAG,CACHzX,IAAA,6BACApC,MAAA,SAAA2R,CAAA,SACA,KAAA+H,aAAA,KACA,KAAAA,aAAA,GAAA/H,EAAA,EAEA,KAAAG,6BAAA,CAAAH,EAEA,CAEA,EAAG,CACHvP,IAAA,UACApC,MAAA,SAAAuZ,CAAA,EACA,YAAA9H,mBAAA,CAAA8H,EACA,CACA,EAAG,CACHnX,IAAA,sBACApC,MAAA,SAAAuZ,CAAA,CAAA5H,CAAA,EAOA,GALA4H,GAAAJ,EAAAhK,IAAA,CAAAoK,KACA5H,EAAA4H,EACAA,EAAA,MAGAA,GAAAA,QAAAA,EAAA,CACA,SAAA9G,UAAA,CAAA8G,GACA,gCAAA7L,MAAA,CAAA6L,GAGA,MAAAxH,aAAA,KAAAgI,EAAA,KAAAH,kBAAA,CAAAL,GAAA,KACA,MAAQ,GAAA5H,EAAA,CACR,SAAA0E,cAAA,CAAA1E,GACA,qCAAAjE,MAAA,CAAAiE,GAGA,MAAAI,aAAA,KAAAgI,EAAA,KAAAC,wBAAA,CAAArI,GAAA,KACA,MACA,KAAAI,aAAA,CAAAnJ,KAAAA,EAGA,YAEA,EAAG,CACHxG,IAAA,gCACApC,MAAA,SAAA2R,CAAA,EACA,IAAAkI,EAAA,KAAAC,mBAAA,GAAAnI,EAAA,CAEA,GAAAkI,EAAA,CAUA,GAAAA,IAAAA,EAAAzN,MAAA,EAAAyN,IAAAA,CAAA,IAAAzN,MAAA,CACA,OAGA,OAAAyN,CACA,CACA,CACA,EAAG,CACHzX,IAAA,+BACApC,MAAA,SAAA2R,CAAA,EACA,IAAAkI,EAAA,KAAA/H,6BAAA,CAAAH,GAEA,GAAAkI,EACA,OAAAA,CAAA,IAGA,EAAG,CACHzX,IAAA,2BACApC,MAAA,SAAA2R,CAAA,EACA,IAAA4H,EAAA,KAAAU,4BAAA,CAAAtI,GAEA,GAAA4H,EACA,YAAAK,kBAAA,CAAAL,GAGA,QAAAG,aAAA,IACA,IAAAjM,EAAA,KAAAiM,aAAA,GAAA/H,EAAA,CAEA,GAAAlE,EACA,OAAAA,CAEA,KAAQ,CAMR,IAAAoM,EAAA,KAAAC,mBAAA,GAAAnI,EAAA,CAEA,GAAAkI,GAAAA,IAAAA,EAAAzN,MAAA,EAAAyN,QAAAA,CAAA,IACA,YAAApM,QAAA,CAAAmD,SAAA,QAGA,CAEA,EAAG,CACHxO,IAAA,qBACApC,MAAA,WACA,YAAA+R,aAAA,CAAAJ,WAAA,EACA,CAEA,EAAG,CACHvP,IAAA,YACApC,MAAA,WACA,YAAA+R,aAAA,CAAAoB,SAAA,EACA,CAEA,EAAG,CACH/Q,IAAA,mBACApC,MAAA,WACA,YAAA+R,aAAA,CAAAmB,gBAAA,EACA,CAEA,EAAG,CACH9Q,IAAA,wBACApC,MAAA,WACA,YAAA+R,aAAA,CAAAkE,qBAAA,EACA,CAEA,EAAG,CACH7T,IAAA,kBACApC,MAAA,WACA,YAAA+R,aAAA,CAAAC,eAAA,EACA,CAEA,EAAG,CACH5P,IAAA,UACApC,MAAA,WACA,YAAA+R,aAAA,CAAApC,OAAA,EACA,CAEA,EAAG,CACHvN,IAAA,2BACApC,MAAA,WACA,YAAA+R,aAAA,CAAAwE,wBAAA,EACA,CAEA,EAAG,CACHnU,IAAA,8BACApC,MAAA,WACA,YAAA+R,aAAA,CAAA8E,2BAAA,EACA,CAEA,EAAG,CACHzU,IAAA,gBACApC,MAAA,WACA,YAAA+R,aAAA,CAAAmI,aAAA,EACA,CAEA,EAAG,CACH9X,IAAA,WACApC,MAAA,WACA,YAAA+R,aAAA,CAAAsG,QAAA,EACA,CAEA,EAAG,CACHjW,IAAA,OACApC,MAAA,SAAAma,CAAA,EACA,YAAApI,aAAA,CAAAlQ,IAAA,CAAAsY,EACA,CAEA,EAAG,CACH/X,IAAA,MACApC,MAAA,WACA,YAAA+R,aAAA,CAAApE,GAAA,EACA,CACA,EAAG,CACHvL,IAAA,sBACApC,MAAA,kBACA,KAAAwZ,EAAA,MAAA/L,QAAA,CAAA2M,+BAAA,CACA,KAAA3M,QAAA,CAAA4M,qBAAA,CAGA,EAAG,CACHjY,IAAA,oCACApC,MAAA,SAAA2R,CAAA,EACA,YAAAF,mBAAA,CAAAE,EACA,CACA,EAAG,CACHvP,IAAA,2BACApC,MAAA,WACA,YAAA4I,IAAA,KAAAmJ,aAAA,CAEA,EAAG,EAEHqH,CACA,IAIAW,EAAA,WACA,SAAAA,EAAAtM,CAAA,CAAA6M,CAAA,EACA1O,EAAA,KAAAmO,GAEA,KAAAO,oBAAA,CAAAA,EACA,KAAA7M,QAAA,CAAAA,EACA6L,EAAAlV,IAAA,MAAAkW,EAAA7M,QAAA,CACA,CAuJA,OArJAuL,EAAAe,EAAA,EACA3X,IAAA,cACApC,MAAA,WACA,YAAAyN,QAAA,IAQA,EAAG,CACHrL,IAAA,qCACApC,MAAA,WACA,YAAAsa,oBAAA,CAAAN,wBAAA,MAAArI,WAAA,GACA,CAEA,EAAG,CACHvP,IAAA,YACApC,MAAA,WACA,SAAAwZ,EAAA,QAAAtH,EAAA,CACA,YAAAzE,QAAA,IAGA,EAAG,CACHrL,IAAA,mBACApC,MAAA,WACA,SAAAwZ,EAAA,QAAAtH,EAAA,CACA,YAAAzE,QAAA,KAEA,EAAG,CACHrL,IAAA,wBACApC,MAAA,kBACA,KAAAwZ,EAAA,OAAAtH,EAAA,MAAAzE,QAAA,IACA,KAAAA,QAAA,IAGA,EAAG,CACHrL,IAAA,kBACApC,MAAA,WACA,SAAAwZ,EAAA,CACA,YAAA/L,QAAA,MAAAyE,EAAA,MAEA,EAAG,CACH9P,IAAA,cACApC,MAAA,SAAAyN,CAAA,EACA,OAAAA,CAAA,MAAA+L,EAAA,QAAAtH,EAAA,MAKA,EAAG,CACH9P,IAAA,UACApC,MAAA,WACA,IAAA2L,EAAA,KAGA,MAAAgE,CADA,KAAA4K,WAAA,MAAA9M,QAAA,QAAA8M,WAAA,MAAAC,kCAAA,SACAC,GAAA,UAAAxc,CAAA,EACA,WAAAyc,EAAAzc,EAAA0N,EACA,EACA,CACA,EAAG,CACHvJ,IAAA,iBACApC,MAAA,WACA,YAAAyN,QAAA,MAAA+L,EAAA,QAAAtH,EAAA,MAEA,EAAG,CACH9P,IAAA,mCACApC,MAAA,SAAAyN,CAAA,EACA,OAAAA,CAAA,MAAA+L,EAAA,QAAAtH,EAAA,MAKA,EAAG,CACH9P,IAAA,+BACApC,MAAA,WACA,YAAA2a,gCAAA,MAAAlN,QAAA,QAAAkN,gCAAA,MAAAH,kCAAA,GACA,CACA,EAAG,CACHpY,IAAA,4BACApC,MAAA,WACA,YAAAyN,QAAA,MAAA+L,EAAA,QAAAtH,EAAA,MAEA,EAAG,CACH9P,IAAA,2BACApC,MAAA,WAGA,YAAA4a,yBAAA,SAAA/K,cAAA,EACA,CACA,EAAG,CACHzN,IAAA,8BACApC,MAAA,WACA,YAAAyN,QAAA,MAAA+L,EAAA,QAAAtH,EAAA,MAEA,EAAG,CACH9P,IAAA,6CACApC,MAAA,WACA,aAAAyN,QAAA,MAAA+L,EAAA,QAAAtH,EAAA,MAMA,EAAG,CACH9P,IAAA,yDACApC,MAAA,WACA,YAAA6a,0CAAA,MAAApN,QAAA,QAAAoN,0CAAA,MAAAL,kCAAA,GACA,CACA,EAAG,CACHpY,IAAA,gBACApC,MAAA,WACA,YAAAyN,QAAA,MAAA+L,EAAA,QAAAtH,EAAA,OAEA,EAAG,CACH9P,IAAA,QACApC,MAAA,WACA,YAAAyN,QAAA,MAAA+L,EAAA,QAAAtH,EAAA,QAEA,EAAG,CACH9P,IAAA,WACApC,MAAA,iBAIA,OAAA8a,KAAA,aAAAA,KAAA,GAAA1O,MAAA,GAMA,OAAA0O,KAAA,EACA,CACA,EAAG,CACH1Y,IAAA,OACApC,MAAA,SAAA+a,CAAA,EACA,QAAA1C,QAAA,IAAA2C,EAAA,KAAAF,KAAA,GAAAC,GACA,WAAAE,EAAAD,EAAA,KAAAF,KAAA,GAAAC,GAAA,KAEA,CACA,EAAG,CACH3Y,IAAA,MACApC,MAAA,kBACA,KAAAwZ,EAAA,OAAAtH,EAAA,CAAAgH,EACA,KAAAzL,QAAA,MAAAyL,CACA,CACA,EAAG,EAEHa,CACA,IAEAW,EAAA,WACA,SAAAA,EAAAxM,CAAA,CAAAT,CAAA,EACA7B,EAAA,KAAA8O,GAEA,KAAAnI,OAAA,CAAArE,EACA,KAAAT,QAAA,CAAAA,CACA,CAuDA,OArDAuL,EAAA0B,EAAA,EACAtY,IAAA,UACApC,MAAA,WACA,YAAAuS,OAAA,IAEA,EAAG,CACHnQ,IAAA,SACApC,MAAA,WACA,YAAAuS,OAAA,IAEA,EAAG,CACHnQ,IAAA,wBACApC,MAAA,WACA,YAAAuS,OAAA,QAEA,EAAG,CACHnQ,IAAA,+BACApC,MAAA,WACA,YAAAuS,OAAA,UAAA9E,QAAA,CAAAwC,4BAAA,EACA,CACA,EAAG,CACH7N,IAAA,yDACApC,MAAA,WACA,aAAAuS,OAAA,UAAA9E,QAAA,CAAAmC,sDAAA,EACA,CACA,EAAG,CACHxN,IAAA,0DACApC,MAAA,WAMA,YAAAkb,kBAAA,UAAAtL,sDAAA,EACA,CAEA,EAAG,CACHxN,IAAA,qBACApC,MAAA,WACA,aAAAiQ,4BAAA,IACA,CAAAkL,EAAAhM,IAAA,MAAAc,4BAAA,GAKA,CACA,EAAG,CACH7N,IAAA,sBACApC,MAAA,WACA,YAAAuS,OAAA,UAAArE,MAAA,EACA,CACA,EAAG,EAEHwM,CACA,IAQAS,EAAA,cAEAF,EAAA,WACA,SAAAA,EAAApZ,CAAA,CAAA4L,CAAA,EACA7B,EAAA,KAAAqP,GAEA,KAAApZ,IAAA,CAAAA,EACA,KAAA4L,QAAA,CAAAA,CACA,CAgBA,OAdAuL,EAAAiC,EAAA,EACA7Y,IAAA,UACApC,MAAA,kBACA,KAAAyN,QAAA,CAAA+L,EAAA,MAAA3X,IAAA,CACA,KAAAA,IAAA,IAEA,EAAG,CACHO,IAAA,kBACApC,MAAA,WACA,SAAAyN,QAAA,CAAA+L,EAAA,CACA,YAAA3X,IAAA,UAAA4L,QAAA,CAAAuE,eAAA,EACA,CACA,EAAG,EAEHiJ,CACA,IAEA,SAAAD,EAAAF,CAAA,CAAAjZ,CAAA,EACA,OAAAA,GACA,iBACA,OAAAiZ,CAAA,QAEA,SACA,OAAAA,CAAA,QAEA,YACA,OAAAA,CAAA,QAEA,eACA,OAAAA,CAAA,QAEA,kBACA,OAAAA,CAAA,QAEA,YACA,OAAAA,CAAA,QAEA,MACA,OAAAA,CAAA,QAEA,QACA,OAAAA,CAAA,QAEA,OACA,OAAAA,CAAA,QAEA,cACA,OAAAA,CAAA,IAEA,CAEO,SAAAzB,EAAA5L,CAAA,EACP,IAAAA,EACA,yFAMA,IAAO,GAAAkD,EAAAlB,CAAA,EAAQhC,IAAA,CAAe,GAAAkD,EAAAlB,CAAA,EAAQhC,EAAAmD,SAAA,EACtC,kKAAAlD,MAAA,CAAiL,GAAAiD,EAAAlB,CAAA,EAAQhC,GAAA,yBAAoCnE,OAAAoD,IAAA,CAAAe,GAAA2N,IAAA,YAA0C,KAAAC,EAAA5N,GAAA,KAAAA,EAAA,KAEvQ,CAKA,IAAA4N,EAAA,SAAApd,CAAA,EACA,OAAAgK,EAAAhK,EACA,EAgCO,SAAAqd,EAAAxK,CAAA,CAAArD,CAAA,EAGP,GAAAA,CAFAA,EAAA,IAAA2L,EAAA3L,EAAA,EAEAgF,UAAA,CAAA3B,GACA,OAAArD,EAAAqD,OAAA,CAAAA,GAAAC,kBAAA,EAGA,iCAAArD,MAAA,CAAAoD,GACA,CACO,SAAAyK,EAAAzK,CAAA,CAAArD,CAAA,EAGP,OAAAA,EAAAmD,SAAA,CAAA4K,cAAA,CAAA1K,EACA,CAEA,SAAAwI,EAAA7L,CAAA,EACA,IAAAgO,EAAAhO,EAAAgO,OAAA,CAEA,iBAAAA,GACA,KAAAjC,EAAA,CAAAiC,IAAAA,EACA,KAAAvJ,EAAA,CAAAuJ,IAAAA,EACA,KAAAhC,EAAA,CAAAgC,IAAAA,EACA,KAAAC,EAAA,CAAAD,IAAAA,GAEAA,EAEejD,KAAAA,EAAOiD,EArnBtB,SAsnBA,KAAAvJ,EAAA,IACesG,KAAAA,EAAOiD,EArnBtB,UAsnBA,KAAAhC,EAAA,IAEA,KAAAiC,EAAA,IANA,KAAAlC,EAAA,GASA,kDC3oBA,SAAAjN,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAYvU,SAAAiH,EAAAzH,CAAA,CAAAC,CAAA,EAAuCA,CAAAA,MAAAA,GAAAA,EAAAD,EAAAE,MAAA,GAAAD,CAAAA,EAAAD,EAAAE,MAAA,EAAuD,QAAAC,EAAA,EAAAC,EAAA,MAAAH,GAAuCE,EAAAF,EAASE,IAAOC,CAAA,CAAAD,EAAA,CAAAH,CAAA,CAAAG,EAAA,CAAoB,OAAAC,CAAA,CAW1J,SAAAqP,EAAA9R,CAAA,EACf,IAlBAqC,EAyBA6L,EACAhK,EACAN,EARAmO,EAnBkCC,SAUlC3P,CAAA,EAAgC,GAAA0C,MAAAC,OAAA,CAAA3C,GAAA,OAAAA,CAAA,EAVhCA,EAkBA0C,MAAA5K,SAAA,CAAAiL,KAAA,CAAA7K,IAAA,CAAAyF,KAlBkCiS,SAQlC5P,CAAA,CAAAG,CAAA,EAAyC,IAAgL0P,EAAAC,EAAhLC,EAAA/P,MAAAA,EAAA,yBAAA/D,QAAA+D,CAAA,CAAA/D,OAAAC,QAAA,GAAA8D,CAAA,eAA0G,GAAA+P,MAAAA,GAAwB,IAAAC,EAAA,GAAeC,EAAA,GAAeC,EAAA,GAA4B,IAAM,IAAAH,EAAAA,EAAA7X,IAAA,CAAA8H,GAAwB,CAAAiQ,CAAAA,EAAA,CAAAJ,EAAAE,EAAAtN,IAAA,IAAAS,IAAA,IAA4C8M,EAAAjS,IAAA,CAAA8R,EAAA/b,KAAA,EAAqBkc,IAAAA,EAAA9P,MAAA,EAAlC+P,EAAA,IAAkC,CAAuC,MAAAE,EAAA,CAAcD,EAAA,GAAWJ,EAAAK,CAAA,QAAY,CAAU,IAAMF,GAAAF,MAAAA,EAAA,QAAAA,EAAA,gBAAmD,CAAU,GAAAG,EAAA,MAAAJ,CAAA,EAAsB,OAAAE,EAAA,EARjdhQ,EAmBlC,IAnBkCmI,SAIlC3J,CAAA,CAAAqE,CAAA,EAAkD,GAAArE,GAAgB,oBAAAA,EAAA,OAAAiJ,EAAAjJ,EAelE,GAfkI,IAAAsE,EAAA1F,OAAAtF,SAAA,CAAA8E,QAAA,CAAA1E,IAAA,CAAAsG,GAAAuE,KAAA,OAAqH,GAA7D,WAAAD,GAAAtE,EAAArC,WAAA,EAAA2G,CAAAA,EAAAtE,EAAArC,WAAA,CAAA3I,IAAA,EAA6DsP,QAAAA,GAAAA,QAAAA,EAAA,OAAAJ,MAAAM,IAAA,CAAAxE,GAAsD,GAAAsE,cAAAA,GAAA,2CAAAG,IAAA,CAAAH,GAAA,OAAA2E,EAAAjJ,EAe7S,GAf6S,EAJ3QwB,EAmBlC,IAnBkCoQ,WAEJ,gKAkB9BC,EAAAX,CAAA,IACAY,EAAAZ,CAAA,IACAa,EAAAb,CAAA,IACAc,EAAAd,CAAA,IAOA,oBAAAW,EACAxE,EAAAwE,OACI,wDAIJ,uBAAAC,GAgBA,GAAW,GAAAG,EAAAlN,CAAA,EAAQ+M,GACnBC,GACA1O,EAAAyO,EACA/O,EAAAgP,GAEAhP,EAAA+O,OAEI,wCAAA9O,MAAA,CAAA8O,SAtBJE,GACA3O,EAAA0O,EACAhP,EAAAiP,IAEA3O,EAAAnF,KAAAA,EACA6E,EAAAgP,GAGAD,GACAzO,CAAAA,EAAAhB,SAlDAC,CAAA,EAAiC,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAAE,EAAAjD,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAErIpC,EAAAA,EAFkMiN,CAAA,CAAA7K,EAAA,CAEtJA,KAFsJ4K,EAEpI1D,OAAA8B,cAAA,CAFoI4B,EAAA5K,EAEpI,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFkB9F,EAElB,CAAApC,CAFkB,GAA4CsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAAV,EAAAjD,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAAoKkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,EAkD1e,CACA4P,eAAAJ,CACA,EAAOzO,EAAA,EAaP,OACAgK,KAAAA,EACAhK,QAAAA,EACAN,SAAAA,CACA,CACA,oFCrEAoP,EAAA,aAAsC,GAAA/H,SAAArF,CAAA,IAAsB,UCSrDqN,EAAA,CACP,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,QAEA,OAEA,EC/EA,SAASC,EAAiB7Q,CAAA,CAAAC,CAAA,EAAaA,CAAAA,MAAAA,GAAAA,EAAAD,EAAAE,MAAA,GAAAD,CAAAA,EAAAD,EAAAE,MAAA,EAAuD,QAAAC,EAAA,EAAAC,EAAA,MAAAH,GAAuCE,EAAAF,EAASE,IAAOC,CAAA,CAAAD,EAAA,CAAAH,CAAA,CAAAG,EAAA,CAAoB,OAAAC,CAAA,CAmB1J,SAAA0Q,EAAAte,CAAA,EAQf,QAAsD4P,EAPtD/C,EAAA,GAOAgD,EAAuB0O,SA/BiBvS,CAAA,CAAA+D,CAAA,EAAsB,IAAAC,EAAA,oBAAAvG,QAAAuC,CAAA,CAAAvC,OAAAC,QAAA,GAAAsC,CAAA,eAAiF,GAAAgE,EAAA,OAAAA,EAAAA,EAAAtK,IAAA,CAAAsG,EAAA,EAAAiE,IAAA,CAAAvE,IAAA,CAAAsE,GAAgD,GAAAE,MAAAC,OAAA,CAAAnE,IAAAgE,CAAAA,EAA8BwO,SAEzLxS,CAAA,CAAAqE,CAAA,EAAc,GAAArE,GAAgB,oBAAAA,EAAA,OAAkCqS,EAAiBrS,EAAjFqE,KAAAA,GAA8F,IAAAC,EAAA1F,OAAAtF,SAAA,CAAA8E,QAAA,CAAA1E,IAAA,CAAAsG,GAAAuE,KAAA,OAAqH,GAA7D,WAAAD,GAAAtE,EAAArC,WAAA,EAAA2G,CAAAA,EAAAtE,EAAArC,WAAA,CAAA3I,IAAA,EAA6DsP,QAAAA,GAAAA,QAAAA,EAAA,OAAAJ,MAAAM,IAAA,CAAAxE,GAAsD,GAAAsE,cAAAA,GAAA,2CAAAG,IAAA,CAAAH,GAAA,OAAoF+N,EAAiBrS,EAA9WqE,KAAAA,GAA8W,EAF1JrE,EAAA,GAA+DgE,GAAAhE,CAAAA,EAAAgE,CAAA,EAAgB,IAAArC,EAAA,EAAW,yBAAqB,GAAA3B,EAAA0B,MAAA,EAA4BgD,KAAA,IAAc,CAASA,KAAA,GAAApP,MAAA0K,CAAA,CAAA2B,IAAA,GAAmC,0JA+BvY3N,EAAA+Z,KAAA,MAA2B,EAAAnK,EAAAC,GAAA,EAAAa,IAAA,EAA4B,CAC7G,IAAA+N,EAAA7O,EAAAtO,KAAA,CACAuL,GAAA6R,SAiBOD,CAAA,CAAAE,CAAA,CAAAC,CAAA,EAEP,GAAAH,MAAAA,EAAA,CAGA,GAAAE,EAgBA,OAGA,SACA,CAGA,ODOAP,CAAA,CCPmBK,EDOnB,ECpDAA,EAAA5R,IAAA,EACA,CAEA,OAAAA,CACA,uECjCA,SAASgS,EAAiBrR,CAAA,CAAAC,CAAA,EAAaA,CAAAA,MAAAA,GAAAA,EAAAD,EAAAE,MAAA,GAAAD,CAAAA,EAAAD,EAAAE,MAAA,EAAuD,QAAAC,EAAA,EAAAC,EAAA,MAAAH,GAAuCE,EAAAF,EAASE,IAAOC,CAAA,CAAAD,EAAA,CAAAH,CAAA,CAAAG,EAAA,CAAoB,OAAAC,CAAA,CEKzK,IAAAkR,EAAA,KAAuCtN,EAAA2E,EAAY,CAAnD,qBAOA4I,EAAA,OANA,OAAAD,EAAA,KAAgGtN,EAAA2E,EAAY,KAAA2I,EAA5G,KAMA,KAGAE,EAAgBxN,EAAA2E,EAAY,CAU5B8I,EAAA,OANA,MAHAD,EAAA,aAAAA,EAGA,4BADAA,EACA,WAMA,KACOE,EAAA,OACAC,EAAA,kBEPPC,EAAA,WAAkD5N,EAAAuH,EAAU,CAAGvH,EAAA2E,EAAY,MAI3EkJ,EAAA,YAAuD7N,EAAA2E,EAAY,CAAnE,QA0Be,SAAAmJ,EAAAjG,CAAA,CAAAhK,CAAA,CAAAN,CAAA,EAMf,GAHAM,EAAAA,GAAA,GACAN,EAAA,IAAiB4D,EAAAG,EAAQ,CAAA/D,GAEzBM,EAAA6O,cAAA,GAAAnP,EAAAgF,UAAA,CAAA1E,EAAA6O,cAAA,GACA,GAAA7O,EAAAmE,EAAA,CACA,UAAgBnH,EAAA0E,CAAU,mBAG1B,iCAAA/B,MAAA,CAAAK,EAAA6O,cAAA,EACA,CAGA,IAoMArR,EApMA0S,EAAAC,SAwJAnG,CAAA,CAAA7F,CAAA,CAAAiM,CAAA,EAMA,IAAAvf,EAAewf,SD3NAC,CAAA,CAAAniB,CAAA,EACf,IAAAoiB,EAAApiB,EAAAoiB,2BAAA,CACAC,EAAqBC,SD+BNC,CAAA,EACf,IAAAC,EAAAD,EAAA1V,OAAA,CAAA8U,GAEA,GAAAa,EAAA,EACA,YAGA,IAAAC,EAAAD,EAAAb,EAAAzR,MAAA,CAEA,GAAAuS,GAAAF,EAAArS,MAAA,CACA,SAGA,IAAAwS,EAAAH,EAAA1V,OAAA,KAAsD4V,UAEtD,KACAF,EAAAI,SAAA,CAAAF,EAAAC,GAEAH,EAAAI,SAAA,CAAAF,EAEA,ECnDwCN,GAExC,ID0DA,QC1D0BE,GD8D1B,IAAAA,EAAAnS,MAAA,EAKAqR,CAAAA,EAAAtO,IAAA,CCnE0BoP,IDmE1BZ,EAAAxO,IAAA,CCnE0BoP,EDmE1B,CAPA,EC3DA,UAAcxT,EAAA0E,CAAU,iBAKxB,GAAA8O,OAAAA,EAGAO,EAAAR,EAAAD,IAAA,OACI,CACJS,EAAA,GDhBO,MCmBPP,EAAAQ,MAAA,KACAD,CAAAA,GAAAP,CAAA,EAQA,IAnBAO,EAoBAE,EADAC,EAAAZ,EAAAtV,OAAA,CAAqD6U,GAOrDoB,EADAC,GAAA,EACAA,EAAqDrB,EAAexR,MAAA,CAEpE,EAGA,IAAAsS,EAAAL,EAAAtV,OAAA,CAAoD8U,GACpDiB,GAAAT,EAAAQ,SAAA,CAAAG,EAAAN,EACA,CAKA,IAAAQ,EAAAJ,EAAA/V,OAAA,CDtBO,UCiCP,GATAmW,EAAA,GACAJ,CAAAA,EAAAA,EAAAD,SAAA,GAAAK,EAAA,EAQAJ,KAAAA,EACA,OAAAA,CAEA,EC+JsE/G,EAAA,CACtEuG,4BAAA,SAAAvG,CAAA,EACA,OAAAoH,SA5CApH,CAAA,CAAAoG,CAAA,CAAAiB,CAAA,EACA,GAAArH,GAIA,GAAAA,EAAA3L,MAAA,CAxKA,IAwKA,CACA,GAAAgT,EACA,UAAgBrU,EAAA0E,CAAU,aAG1B,MACA,CAEA,GAAA0O,CAAA,IAAAA,EACA,OAAApG,EAIA,IAAAsH,EAAAtH,EAAAxI,MAAA,CAAAuO,GAEA,IAAAuB,CAAAA,EAAA,GAIA,OAAAtH,EACA9I,KAAA,CAAAoQ,GACAvP,OAAA,CAAAiO,EAAA,IACA,EAiBAhG,EAAAoG,EAAAjM,EACA,CACA,GAEA,IAAAtT,EACA,SAGA,IAAO,GAAAiZ,EAAArG,EAAA,EAAmB5S,SAC1B,CAAQ,EAAAiZ,EAAAyH,EAAA,EAAwB1gB,GAChC,CACA2gB,MAAA,WACA,EAGA,GAKA,IAAAC,EAA8BC,SPlPf7gB,CAAA,EACf,IAAA8gB,EAAA9gB,EAAA2Q,MAAA,CAAAsN,GAEA,GAAA6C,EAAA,EACA,SASA,IAJA,IAAAC,EAAA/gB,EAAAqQ,KAAA,GAAAyQ,GACAE,EAAAhhB,EAAA8W,KAAA,CAAAmH,GACAxQ,EAAA,EAEAA,EAAAuT,EAAAxT,MAAA,GACA,GAAAwT,CAAA,CAAAvT,EAAA,CACA,OACAzN,OAAA+gB,EACAhS,IAAAiS,CAAA,CAAAvT,EAAA,CAIAA,CAAAA,GACA,CACA,EO2N8CzN,UAE9C,EAAA+O,GAAA,CACA6R,EAGA,CACA5gB,OAAAA,CACA,CACA,EA7LAmZ,EAAAhK,EAAAmE,EAAA,CAAAnE,EAAAoQ,OAAA,EACA0B,EAAA5B,EAAArf,MAAA,CACA+O,EAAAsQ,EAAAtQ,GAAA,CACA4R,EAAAtB,EAAAsB,KAAA,CAGA,IAAAM,EAAA,CACA,GAAA9R,EAAAmE,EAAA,EACA,GAAAqN,cAAAA,EACA,UAAkBxU,EAAA0E,CAAU,aAG5B,WAAgB1E,EAAA0E,CAAU,gBAC1B,CAEA,QACA,CAEA,IAAAqQ,EAAAC,SAuMAF,CAAA,CAAAjD,CAAA,CAAAoD,CAAA,CAAAvS,CAAA,EAEA,IH9RekE,EAAAzV,EACf+jB,EACArD,EACAnP,EASAmE,EGwRAd,EANAI,EAA8B,GAAAC,EAAA1B,CAAA,EAA0BuN,EAA0B6C,GAAAjD,EAAAoD,EAAAvS,EAAAA,QAAA,EAClF0I,EAAAjF,EAAAiF,wBAAA,CACApF,EAAAG,EAAAH,kBAAA,CACAnS,EAAAsS,EAAAtS,MAAA,CAKA,GAAAmS,EACAtD,EAAAgE,mBAAA,CAAAV,QAGA,GAAAnS,CAAAA,GAAAge,CAAAA,IAAAoD,EAeI,SAdJvS,EAAAgE,mBAAA,CAAAmL,EAAAoD,GAEApD,GACA9L,CAAAA,EAAA8L,CAAA,EAUA7L,EAAAiP,GAA+C,GAAA3O,EAAA0B,EAAA,EAAqB6J,EAAAnP,EAAAA,QAAA,EAGpE,IAAA7O,EACA,OACAuX,yBAAAA,EACApF,mBAAAA,CACA,EAGA,IAAA+E,EAA8B,GAAAC,EAAAtG,CAAA,EAAsBuN,EAA0Bpe,GAAA6O,GAC9EiD,EAAAoF,EAAApF,cAAA,CACA7C,EAAAiI,EAAAjI,WAAA,CAYAqS,GHhVevO,EGgV6BZ,EH/U5CkP,EAAA/jB,CADeA,EGgV6B,CAC5CwU,eAAAA,EACAkM,eAAAA,EACAnP,SAAAA,CACA,GHnVAiD,cAAA,CACAkM,EAAA1gB,EAAA0gB,cAAA,CAYA,CAFAhL,EAAAnE,CATAA,EAAAvR,EAAAuR,QAAA,EASAqE,6BAAA,CAAAH,IAQAC,IAAAA,EAAAxF,MAAA,CACAwF,CAAA,IAGSuO,SDlBMF,CAAA,CAAA/jB,CAAA,EACf,IAAA0U,EAAA1U,EAAA0U,SAAA,CAEAnD,GADAvR,EAAA0gB,cAAA,CACA1gB,EAAAuR,QAAA,EAEAA,EAAA,IAAiB4D,EAAAG,EAAQ,CAAA/D,GAEzB,QAAsDa,EAAtDC,EAAuB6R,SAfiB1V,CAAA,CAAA+D,CAAA,EAAsB,IAAAC,EAAA,oBAAAvG,QAAAuC,CAAA,CAAAvC,OAAAC,QAAA,GAAAsC,CAAA,eAAiF,GAAAgE,EAAA,OAAAA,EAAAA,EAAAtK,IAAA,CAAAsG,EAAA,EAAAiE,IAAA,CAAAvE,IAAA,CAAAsE,GAAgD,GAAAE,MAAAC,OAAA,CAAAnE,IAAAgE,CAAAA,EAA8B2R,SAEzL3V,CAAA,CAAAqE,CAAA,EAAc,GAAArE,GAAgB,oBAAAA,EAAA,OAAkC6S,EAAiB7S,EAAjFqE,KAAAA,GAA8F,IAAAC,EAAA1F,OAAAtF,SAAA,CAAA8E,QAAA,CAAA1E,IAAA,CAAAsG,GAAAuE,KAAA,OAAqH,GAA7D,WAAAD,GAAAtE,EAAArC,WAAA,EAAA2G,CAAAA,EAAAtE,EAAArC,WAAA,CAAA3I,IAAA,EAA6DsP,QAAAA,GAAAA,QAAAA,EAAA,OAAAJ,MAAAM,IAAA,CAAAxE,GAAsD,GAAAsE,cAAAA,GAAA,2CAAAG,IAAA,CAAAH,GAAA,OAAoFuO,EAAiB7S,EAA9WqE,KAAAA,GAA8W,EAF1JrE,EAAA,GAA+DgE,GAAAhE,CAAAA,EAAAgE,CAAA,EAAgB,IAAArC,EAAA,EAAW,yBAAqB,GAAA3B,EAAA0B,MAAA,EAA4BgD,KAAA,IAAc,CAASA,KAAA,GAAApP,MAAA0K,CAAA,CAAA2B,IAAA,GAAmC,0JAevYuE,GAAoB,EAAAtC,EAAAC,GAAA,EAAAa,IAAA,EAA4B,CACtG,IAAA0B,EAAAxC,EAAAtO,KAAA,CASA,GARAyN,EAAAqD,OAAA,CAAAA,GAQArD,EAAAyM,aAAA,GACA,IAAA+F,GAAAA,IAAAA,EAAA1Q,MAAA,CAAA9B,EAAAyM,aAAA,IACA,OAAApJ,CACA,MAGA,GAAa,GAAAwB,EAAA7C,CAAA,EAAa,CAC1BiD,MAAAuN,EACAnP,QAAAA,CACA,EAAKlI,KAAAA,EAAA6E,EAAAA,QAAA,EAIL,OAAAqD,CAUA,CAKA,EC3BmCmP,EAAA,CACnCrP,UAAAgB,EACAgL,eAAAA,EACAnP,SAAAA,EAAAA,QAAA,GAZA,QGmVA,OAZAyS,IACApP,EAAAoP,EAGA,QAAAA,GAIAzS,EAAAqD,OAAA,CAAAA,IAIA,CACAA,QAAAA,EACAC,mBAAAA,EACAoF,yBAAAA,EACAzF,eAAAA,EACA7C,YAAAA,CACA,CACA,EApRAgS,EAAA9R,EAAA6O,cAAA,CAAA7O,EAAAiS,kBAAA,CAAAvS,GACAqD,EAAAgP,EAAAhP,OAAA,CACAJ,EAAAoP,EAAApP,cAAA,CACAK,EAAA+O,EAAA/O,kBAAA,CACAoF,EAAA2J,EAAA3J,wBAAA,CACAtI,EAAAiS,EAAAjS,WAAA,CAEA,IAAAJ,EAAA6S,wBAAA,IACA,GAAAvS,EAAAmE,EAAA,CACA,UAAgBnH,EAAA0E,CAAU,oBAG1B,QACA,CAGA,IAAAiB,GAAAA,EAAAtE,MAAA,CAAiD8D,EAAAqH,EAAkB,EAInE,GAAAxJ,EAAAmE,EAAA,CACA,UAAgBnH,EAAA0E,CAAU,cAI1B,QACA,CAWA,GAAAiB,EAAAtE,MAAA,CAA8B8D,EAAAqQ,EAAkB,EAChD,GAAAxS,EAAAmE,EAAA,CACA,UAAgBnH,EAAA0E,CAAU,aAI1B,QACA,CAEA,GAAA1B,EAAAmE,EAAA,EACA,IAAAG,EAAA,IAA0B9B,EAAAd,CAAW,CAAAsB,EAAAL,EAAAjD,EAAAA,QAAA,EAerC,OAbAqD,GACAuB,CAAAA,EAAAvB,OAAA,CAAAA,CAAA,EAGAjD,GACAwE,CAAAA,EAAAxE,WAAA,CAAAA,CAAA,EAGAF,GACA0E,CAAAA,EAAA1E,GAAA,CAAAA,CAAA,EAGA0E,EAAAmO,0BAAA,CAAArK,EACA9D,CACA,CAKA,IAAAoO,EAAA,CAAA1S,EAAA2S,QAAA,GAAAjT,EAAA6S,wBAAA,KAAAxP,CAAA,GAAmF,GAAAtB,EAAAC,CAAA,EAAeiB,EAAAjD,EAAAwI,qBAAA,WAElG,EAAAyK,QAAA,CAKA,CACA5P,QAAAA,EACAC,mBAAAA,EACAlD,YAAAA,EACA4S,MAAAA,EACAE,SAAAF,EAAAA,KAAA1S,CAAAA,CAAA,IAAAA,EAAA2S,QAAA,EAAAjT,EAAAuE,eAAA,IAAwF,GAAA4O,EAAAC,CAAA,EAAgBnQ,EAAAjD,EAAA,EACxGiF,MAAAhC,EACA/C,IAAAA,CACA,EAZA8S,GA2GAlV,EAAA,CACAuF,QA5GAA,EA6GA4B,MA7GAhC,CA8GA,EA9GA/C,GAiHApC,CAAAA,EAAAoC,GAAA,CAjHAA,CAiHA,EAGApC,GApHA,EAaA,mDC3KA,SAAAgB,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAAK,EAAAC,CAAA,EAAiC,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAAE,EAAAjD,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAErIpC,EAAAA,EAFkMiN,CAAA,CAAA7K,EAAA,CAEtJA,KAFsJ4K,EAEpI1D,OAAA8B,cAAA,CAFoI4B,EAAA5K,EAEpI,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFkB9F,EAElB,CAAApC,CAFkB,GAA4CsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAAV,EAAAjD,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAAoKkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,CAK3d,SAAA8T,EAAA/I,CAAA,CAAAhK,CAAA,CAAAN,CAAA,EACf,MAAS,GAAAsT,EAAAtR,CAAA,EAAKsI,EAAAhL,EAAAA,EAAA,GAAqCgB,GAAA,GAAc,CACjEmE,GAAA,EACA,GAAGzE,EACH,yECXA,SAAAlB,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAAK,EAAAC,CAAA,EAAiC,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAAE,EAAAjD,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAErIpC,EAAAA,EAFkMiN,CAAA,CAAA7K,EAAA,CAEtJA,KAFsJ4K,EAEpI1D,OAAA8B,cAAA,CAFoI4B,EAAA5K,EAEpI,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFkB9F,EAElB,CAAApC,CAFkB,GAA4CsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAAV,EAAAjD,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAAoKkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,CAO3d,SAAA+S,EAAAhI,CAAA,CAAAhK,CAAA,CAAAN,CAAA,EAEfM,GAAAA,EAAA6O,cAAA,GAA4C,GAAA1F,EAAA8J,EAAA,EAAkBjT,EAAA6O,cAAA,CAAAnP,IAC9DM,CAAAA,EAAAhB,EAAAA,EAAA,GAA4CgB,GAAA,GAAc,CAC1D6O,eAAAhU,KAAAA,CACA,EAAK,EAIL,IACA,MAAW,GAAAqY,EAAAxR,CAAA,EAAyBsI,EAAAhK,EAAAN,EACpC,CAAI,MAAA8R,EAAA,CAEJ,GAAAA,aAAyB2B,EAAAzR,CAAU,OAEnC,MAAA8P,CAEA,CACA,oCCxBA,IAAA4B,EAAe,CAAC,mlHCGhB,SAAAxN,EAAAzH,CAAA,CAAAC,CAAA,EAAuCA,CAAAA,MAAAA,GAAAA,EAAAD,EAAAE,MAAA,GAAAD,CAAAA,EAAAD,EAAAE,MAAA,EAAuD,QAAAC,EAAA,EAAAC,EAAA,MAAAH,GAAuCE,EAAAF,EAASE,IAAOC,CAAA,CAAAD,EAAA,CAAAH,CAAA,CAAAG,EAAA,CAAoB,OAAAC,CAAA,CCH1J,SAAA8L,IACf,IAAAgJ,EAA4BzF,SD2BrB9R,CAAA,EACP,IAhCAqC,EAuCAvI,EAEA8J,EARAmO,EAjCkCC,SAUlC3P,CAAA,EAAgC,GAAA0C,MAAAC,OAAA,CAAA3C,GAAA,OAAAA,CAAA,EAVhCA,EAgCA0C,MAAA5K,SAAA,CAAAiL,KAAA,CAAA7K,IAAA,CAAAyF,KAhCkCiS,SAQlC5P,CAAA,CAAAG,CAAA,EAAyC,IAAgL0P,EAAAC,EAAhLC,EAAA/P,MAAAA,EAAA,yBAAA/D,QAAA+D,CAAA,CAAA/D,OAAAC,QAAA,GAAA8D,CAAA,eAA0G,GAAA+P,MAAAA,GAAwB,IAAAC,EAAA,GAAeC,EAAA,GAAeC,EAAA,GAA4B,IAAM,IAAAH,EAAAA,EAAA7X,IAAA,CAAA8H,GAAwB,CAAAiQ,CAAAA,EAAA,CAAAJ,EAAAE,EAAAtN,IAAA,IAAAS,IAAA,IAA4C8M,EAAAjS,IAAA,CAAA8R,EAAA/b,KAAA,EAAqBkc,IAAAA,EAAA9P,MAAA,EAAlC+P,EAAA,IAAkC,CAAuC,MAAAE,EAAA,CAAcD,EAAA,GAAWJ,EAAAK,CAAA,QAAY,CAAU,IAAMF,GAAAF,MAAAA,EAAA,QAAAA,EAAA,gBAAmD,CAAU,GAAAG,EAAA,MAAAJ,CAAA,EAAsB,OAAAE,EAAA,EARjdhQ,EAiClC,IAjCkCmI,SAIlC3J,CAAA,CAAAqE,CAAA,EAAkD,GAAArE,GAAgB,oBAAAA,EAAA,OAAAiJ,EAAAjJ,EA6BlE,GA7BkI,IAAAsE,EAAA1F,OAAAtF,SAAA,CAAA8E,QAAA,CAAA1E,IAAA,CAAAsG,GAAAuE,KAAA,OAAqH,GAA7D,WAAAD,GAAAtE,EAAArC,WAAA,EAAA2G,CAAAA,EAAAtE,EAAArC,WAAA,CAAA3I,IAAA,EAA6DsP,QAAAA,GAAAA,QAAAA,EAAA,OAAAJ,MAAAM,IAAA,CAAAxE,GAAsD,GAAAsE,cAAAA,GAAA,2CAAAG,IAAA,CAAAH,GAAA,OAAA2E,EAAAjJ,EA6B7S,GA7B6S,EAJ3QwB,EAiClC,IAjCkCoQ,WAEJ,gKAgC9BC,EAAAX,CAAA,IACAY,EAAAZ,CAAA,IACAa,EAAAb,CAAA,IACAc,EAAAd,CAAA,IAGA7N,EAAA,GAIA,oBAAAwO,EAIS,GAAA5L,EAAAlB,CAAA,EAAQ+M,IAwBjBC,GACA1O,EAAAyO,EACA/O,EAAAgP,GAEAhP,EAAA+O,EASA7Y,EADU,GAAAkU,EAAArG,EAAA,EAAmB+K,GACb,GAAAyB,EAAAvO,CAAA,EAAK8M,EAAA3T,KAAAA,EAAA6E,GAErB,KAtCAiP,GACA3O,EAAA0O,EACAhP,EAAAiP,GAEAjP,EAAAgP,EASA9Y,EADU,GAAAkU,EAAArG,EAAA,EAAmB+K,GACb,GAAAyB,EAAAvO,CAAA,EAAK8M,EAAA,CACrBK,eAAAJ,CACA,EAAS/O,GAET,SA0BA,GAAW,GAAAkD,EAAAlB,CAAA,EAAQ8M,GACnB5Y,EAAA4Y,EAEAE,GACA1O,EAAAyO,EACA/O,EAAAgP,GAEAhP,EAAA+O,OAEI,sGAEJ,OACA7Y,MAAAA,EACAoK,QAAAA,EACAN,SAAAA,CACA,CACA,ECxG8CrE,WAC9CzF,EAAAyd,EAAAzd,KAAA,CACAoK,EAAAqT,EAAArT,OAAA,CACAN,EAAA2T,EAAA3T,QAAA,OAGA,EAAA9J,EAAA+O,KAAA,EAIS,GAAA2O,EAAA5R,CAAA,EAAc9L,EAAAoK,EAAAN,EACvB,CCTO,SAAS6T,IAChB,MAAQ,GAAAC,EAAA9R,CAAA,EAAqB2I,EAAchP,UAC3C,8DCPe,SAAAoY,EAAA/T,CAAA,EACf,WAAa4D,EAAAG,EAAQ,CAAA/D,GAAA+T,YAAA,EACrB,CCAO,SAASC,IAChB,MAAQ,GAAAF,EAAA9R,CAAA,EAAqB+R,EAAapY,UAC1C,8DCFO,SAAAkS,IACP,MAAQ,GAAAoG,EAAAjS,CAAA,EAAqBkS,EAAA5O,EAAsB,CAAA3J,UACnD,6DCJe,SAAAwY,EAAA9Q,CAAA,CAAA+Q,CAAA,CAAApU,CAAA,EACf,GAAAoU,CAAA,CAAA/Q,EAAA,CACA,WAAeP,EAAAd,CAAW,CAAAqB,EAAA+Q,CAAA,CAAA/Q,EAAA,CAAArD,EAE1B,CCFO,SAASqU,IAChB,MAAQ,GAAAP,EAAA9R,CAAA,EAAqBmS,EAAiBxY,UAC9C,wECLA,SAAAmD,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAAK,EAAAC,CAAA,EAAiC,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAAE,EAAAjD,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAErIpC,EAAAA,EAFkMiN,CAAA,CAAA7K,EAAA,CAEtJA,KAFsJ4K,EAEpI1D,OAAA8B,cAAA,CAFoI4B,EAAA5K,EAEpI,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFkB9F,EAElB,CAAApC,CAFkB,GAA4CsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAAV,EAAAjD,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAAoKkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,CAM3d,SAAAiL,IACf,IAAAmJ,EAA4B,GAAAzF,EAAAlM,CAAA,EAAkBrG,WAC9C2O,EAAAqJ,EAAArJ,IAAA,CACAhK,EAAAqT,EAAArT,OAAA,CACAN,EAAA2T,EAAA3T,QAAA,CAEAM,EAAAhB,EAAAA,EAAA,GAA0CgB,GAAA,GAAc,CACxDoQ,QAAA,EACA,GACA,IAAA9L,EAAoB,GAAA0P,EAAAtS,CAAA,EAAgBsI,EAAAhK,EAAAN,GACpC,OAAA4E,GAAAA,EAAAuO,UAAA,MACA,CChBO,SAASoB,IAChB,MAAQ,GAAAT,EAAA9R,CAAA,EAAqBwI,EAAsB7O,UACnD,wECLA,SAAAmD,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAAK,EAAAC,CAAA,EAAiC,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAAE,EAAAjD,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAErIpC,EAAAA,EAFkMiN,CAAA,CAAA7K,EAAA,CAEtJA,KAFsJ4K,EAEpI1D,OAAA8B,cAAA,CAFoI4B,EAAA5K,EAEpI,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFkB9F,EAElB,CAAApC,CAFkB,GAA4CsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAAV,EAAAjD,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAAoKkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,CAM3d,SAAAiV,IACf,IAAAb,EAA4B,GAAAzF,EAAAlM,CAAA,EAAkBrG,WAC9C2O,EAAAqJ,EAAArJ,IAAA,CACAhK,EAAAqT,EAAArT,OAAA,CACAN,EAAA2T,EAAA3T,QAAA,CAEAM,EAAAhB,EAAAA,EAAA,GAA0CgB,GAAA,GAAc,CACxDoQ,QAAA,EACA,GACA,IAAA9L,EAAoB,GAAA0P,EAAAtS,CAAA,EAAgBsI,EAAAhK,EAAAN,GACpC,OAAA4E,GAAAA,EAAAgP,OAAA,MACA,CChBO,SAASa,IAChB,MAAQ,GAAAX,EAAA9R,CAAA,EAAqBwS,EAAmB7Y,UAChD,uECHe,SAAA2W,IACf,IAAAqB,EAA4B,GAAAzF,EAAAlM,CAAA,EAAkBrG,WAC9C2O,EAAAqJ,EAAArJ,IAAA,CACAhK,EAAAqT,EAAArT,OAAA,CACAN,EAAA2T,EAAA3T,QAAA,CAEA,MAAS,GAAAsU,EAAAtS,CAAA,EAAiBsI,EAAAhK,EAAAN,EAC1B,CCNO,SAAS0U,IAChB,MAAQ,GAAAZ,EAAA9R,CAAA,EAAqBsQ,EAAiB3W,UAC9C,yGCLA,SAAAmD,EAAAC,CAAA,CAAAC,CAAA,EAA2C,IAAAC,EAAApD,OAAAoD,IAAA,CAAAF,GAAgC,GAAAlD,OAAAqD,qBAAA,EAAoC,IAAAC,EAAAtD,OAAAqD,qBAAA,CAAAH,EAAoDC,CAAAA,GAAAG,CAAAA,EAAAA,EAAAC,MAAA,UAAAC,CAAA,EAA6D,OAAAxD,OAAAzF,wBAAA,CAAA2I,EAAAM,GAAAtD,UAAA,EAAiE,EAAAkD,EAAAzC,IAAA,CAAAC,KAAA,CAAAwC,EAAAE,EAAA,CAAsC,OAAAF,CAAA,CAEvU,SAAAK,EAAAC,CAAA,EAAiC,QAAAX,EAAA,EAAgBA,EAAAjD,UAAAgD,MAAA,CAAsBC,IAAA,CAAO,IAAAY,EAAA,MAAA7D,SAAA,CAAAiD,EAAA,CAAAjD,SAAA,CAAAiD,EAAA,GAAuDA,CAAAA,EAAA,EAAAE,EAAAjD,OAAA2D,GAAA,IAAAC,OAAA,UAAA9K,CAAA,MAErIpC,EAAAA,EAFkMiN,CAAA,CAAA7K,EAAA,CAEtJA,KAFsJ4K,EAEpI1D,OAAA8B,cAAA,CAFoI4B,EAAA5K,EAEpI,CAAkCpC,MAAAA,EAAAwJ,WAAA,GAAAE,aAAA,GAAAD,SAAA,KAAgFvB,CAAA,CAFkB9F,EAElB,CAAApC,CAFkB,GAA4CsJ,OAAA6D,yBAAA,CAAA7D,OAAA8D,gBAAA,CAAAJ,EAAA1D,OAAA6D,yBAAA,CAAAF,IAAAV,EAAAjD,OAAA2D,IAAAC,OAAA,UAAA9K,CAAA,EAAoKkH,OAAA8B,cAAA,CAAA4B,EAAA5K,EAAAkH,OAAAzF,wBAAA,CAAAoJ,EAAA7K,GAAA,EAAmF,CAAK,OAAA4K,CAAA,CAS3d,SAAAoV,IACf,IAAAhB,EAA4B,GAAAzF,EAAAlM,CAAA,EAAkBrG,WAC9C2O,EAAAqJ,EAAArJ,IAAA,CACAhK,EAAAqT,EAAArT,OAAA,CACAN,EAAA2T,EAAA3T,QAAA,CAEAM,EAAAhB,EAAAA,EAAA,GAA0CgB,GAAA,GAAc,CACxDoQ,QAAA,EACA,GAEA,IACA,IAAA9L,EAAsB,GAAAgQ,EAAA5S,CAAA,EAAyBsI,EAAAhK,EAAAN,GAE/CA,CADAA,EAAA,IAAmB4D,EAAAG,EAAQ,CAAA/D,EAAA,EAC3BgE,mBAAA,CAAAY,EAAAtB,kBAAA,EACA,IAAAxF,EAAiB,GAAAqI,EAAAnE,CAAA,EAAiB4C,EAAA3B,cAAA,CAAAjD,GAElC,GAAAlC,gBAAAA,EACA,OAAAA,CAEA,CAAI,MAAAgU,EAAA,CAEJ,GAAAA,aAAyBxU,EAAA0E,CAAU,CACnC,OAAA8P,EAAA+C,OAAA,OAEA/C,CAEA,CACA,CCnCO,SAASgD,IAChB,MAAQ,GAAAhB,EAAA9R,CAAA,EAAqB2S,EAA0BhZ,UACvD,oCCFA,IAAAoZ,EAAe,CAAC,iCAAqC,82EAAywF,WAAc,2CAA2C,uDAAyD,6BAA4B,yCAAyC,2CAA2C,4FAA+F,4CAA2C,2CAA2C,gEAAgE,kDAAkD,0DAA6D,iCAAgC,+EAAkF,gCAA4B,8DAAoE,iCAA6B,yFAA+F,iCAAgC,iDAAiD,yDAAyD,wDAAwD,uDAAuD,6EAAgF,iCAAgC,iDAAiD,iDAAiD,8CAA8C,2DAA8D,iCAAgC,4EAA+E,qCAAoC,2+BAA2+B,sDAAsD,uDAAuD,8DAA8D,wjCAAwjC,wEAAwE,yDAAyD,gqBAAmqB,gCAA4B,kMAAwM,+CAA8C,qDAAqD,6CAA6C,iDAAiD,0JAA0J,gEAAgE,sDAAsD,4KAA+K,yCAAwC,oDAAoD,qDAAqD,oDAAoD,2DAA2D,wgBAAygB,gHAAgH,+BAA+B,2BAA2B,2BAA2B,yDAAyD,yDAAyD,mEAAqE,yBAAwB,kLAAqL,uGAAyG,yCAAwC,gEAAgE,+HAA+H,0FAA6F,mCAAkC,+DAA+D,sEAAsE,8EAAiF,gCAA4B,kHAAwH,oCAAmC,wDAAwD,8NAA8N,mDAAmD,+DAAkE,mCAAkC,iEAAiE,yEAAyE,oEAAoE,gEAAmE,yCAAwC,6FAAgG,4CAA2C,+CAA+C,8DAA8D,sDAAsD,sFAAsF,4DAA4D,8DAA8D,6DAA6D,6DAAgE,yBAAwB,yEAA4E,yCAAwC,sEAAyE,4CAA2C,wEAAwE,sFAAyF,4DAA4D,6GAA6G,kBAAkB,yDAAyD,gDAAkD,gCAA4B,gDAAsD,yBAAwB,oFAAuF,wBAAuB,qCAAqC,2CAA2C,2EAA8E,qKAAuK,iCAAgC,kFAAkF,gEAAgE,8GAA8G,+GAA6G,mDAAwD,gCAA4B,yDAA+D,gCAA+B,kEAAkE,mGAAsG,8BAA6B,oCAAoC,wDAAwD,iDAAiD,8CAA8C,uKAA0K,qCAAoC,sDAAsD,uDAAuD,sMAAsM,uFAAuF,+DAA+D,oGAAuG,4BAA2B,oDAAoD,2DAA8D,8OAA8O,wDAAwD,wBAAwB,6PAA6P,qBAAqB,yBAAyB,4IAA8I,0CAAsC,sJAA0J,gHAAgH,+BAA+B,2BAA2B,0DAA0D,yDAAyD,kFAAoF,wCAAuC,6CAA6C,qDAAqD,oDAAoD,oDAAoD,+DAA+D,4EAA+E,yCAAwC,wEAA2E,8BAA6B,8CAA8C,4DAA+D,oCAAmC,oEAAoE,+EAA+E,gEAAmE,uCAAsC,yDAAyD,sDAAyD,yBAAwB,8HAAiI,+BAA8B,2DAA2D,4CAA4C,wDAAwD,mDAAmD,sGAAsG,iDAAiD,sDAAsD,+DAAkE,2CAA0C,+DAA+D,iMAAoM,wCAAuC,6yDAA6yD,ovCAAovC,sDAAsD,qKAAqK,4OAA4O,2CAA2C,uDAAuD,4DAA4D,2DAA2D,kKAAqK,+BAA8B,oCAAoC,6CAA6C,kDAAkD,uIAA0I,4BAA2B,sDAAsD,kIAAqI,kCAAiC,oDAAoD,wCAAwC,4CAA4C,sEAAyE,iCAAgC,4EAA+E,2BAA0B,6CAA6C,8KAAiL,0CAAsC,wKAA4K,gHAAgH,+BAA+B,2BAA2B,0DAA0D,yDAAyD,gEAAkE,yBAAwB,2EAA8E,0CAAyC,mEAAmE,0DAA0D,yDAAyD,qOAAwO,wDAAuD,yDAAyD,6PAA6P,ukBAAukB,8CAA8C,4CAA4C,mDAAmD,wDAAwD,gDAAgD,yCAAyC,kEAAkE,4CAA4C,8CAA8C,gDAAgD,mEAAmE,oDAAoD,gEAAgE,0DAA0D,kEAAqE,yCAAwC,sDAAyD,yCAAwC,wEAA2E,gCAA4B,0DAAgE,iFAAmF,2CAA0C,mEAAmE,+DAA+D,sFAAyF,sCAAqC,uEAAuE,sDAAsD,oFAAuF,8BAA6B,mKAAmK,wFAAwF,8CAA8C,2EAA8E,6BAA4B,8CAA8C,4EAA4E,+CAA+C,wDAA2D,0DAA4D,8BAA6B,8DAAiE,iCAAgC,2DAA2D,oEAAuE,iCAAgC,+KAAkL,kCAAiC,+CAA+C,mEAAmE,+DAA+D,wCAAwC,2JAA8J,4BAA2B,oDAAoD,oEAAuE,+CAAiD,yBAAwB,kDAAqD,iBAAgB,wEAA2E,yCAAwC,qEAAqE,4FAA+F,wCAAuC,mEAAmE,yDAAyD,gGAAgG,yDAA4D,8BAA6B,qFAAqF,sEAAsE,6CAA6C,wJAAwJ,sEAAsE,+FAA+F,iDAAiD,w+CAAy+C,qOAAqO,wCAAgC,gEAA8D,oBAA8B,gCAAgC,mGAAmG,kBAAkB,4DAA8D,gCAA4B,6DAAmE,iCAAgC,6DAA6D,gEAAgE,4DAA4D,iGAAoG,yCAAwC,yEAAyE,oGAAqG,qCAAmC,kCAAsC,2CAA2C,wCAAgC,+DAA6D,oBAA8B,gCAAgC,mGAAmG,kBAAkB,mDAAqD,2BAA0B,iDAAiD,wEAA2E,yBAAwB,6DAAgE,iCAAgC,wDAA2D,yBAAwB,iEAAoE,2CAA0C,yDAAyD,sFAAyF,yCAAwC,oEAAoE,+HAAgI,6GAA6G,kBAAkB,yDAAyD,sDAAwD,iCAAgC,0CAA0C,mFAAsF,wCAAuC,yCAAyC,oGAAoG,qDAAqD,sEAAyE,4BAA2B,+CAA+C,iEAAoE,gCAA4B,qDAA2D,2BAA0B,4CAA4C,oEAAuE,yBAAwB,+GAAkH,sCAAqC,4CAA4C,wEAAwE,sDAAsD,kFAAqF,4BAA2B,qFAAwF,qCAAoC,0DAA0D,iDAAiD,sDAAsD,6DAA6D,sDAAsD,yDAAyD,uEAA0E,iCAAgC,oEAAuE,gCAA+B,wDAAwD,6GAA6G,uIAA0I,0DAAyD,yCAAyC,wDAAwD,6CAA6C,0DAA0D,sDAAsD,iDAAiD,2DAA2D,0DAA0D,mGAAsG,gCAA+B,wEAAwE,sDAAsD,wDAAwD,yEAAyE,qDAAqD,uDAAuD,uDAAuD,8CAA8C,2DAA2D,2GAA8G,sCAAqC,6CAA6C,6CAA6C,0DAA0D,uDAAuD,uCAAuC,kDAAkD,gEAAgE,8EAAiF,iCAA6B,2FAAiG,+BAA8B,yJAAyJ,wDAAwD,oDAAoD,6OAA6O,m+CAAm+C,q1BAAq1B,yDAAyD,qFAAqF,yDAA4D,yBAAwB,oEAAuE,mCAAkC,sDAAsD,wDAAwD,oFAAuF,4BAA2B,yCAAyC,0HAA0H,oDAAoD,gGAAmG,2BAA0B,+CAA+C,mJAAoJ,0CAA2C,yCAAyC,iIAAiI,gEAAgE,+CAA+C,sDAAsD,qEAAqE,gEAAgE,kDAAkD,gDAAgD,+cAAgd,mDAAmD,iCAAiC,yHAAyH,mCAAmC,2BAA2B,yBAAyB,qCAAqC,2DAA6D,mCAA+B,oCAAwC,wDAAwD,iCAAiC,0GAA0G,iBAAiB,oHAAoH,8FAA8F,aAAa,gDAAkD,kFAAoF,gCAA+B,wDAAwD,4CAA4C,+CAA+C,uGAA0G,2DAA0D,6DAA6D,seAAse,kDAAkD,kHAAkH,swDAAswD,mFAAmF,sDAAsD,4EAAwD,iFAAwG,kCAAiC,8CAA8C,sDAAsD,wEAA2E,4BAA2B,mEAAmE,qEAAqE,8EAAiF,wCAAuC,wDAAwD,0FAA6F,2CAA6C,iCAAgC,kEAAqE,gCAA4B,oEAA0E,oCAAmC,iDAAiD,wDAAwD,wMAA2M,gDAA+C,oEAAoE,0CAA0C,oDAAoD,4DAA4D,gEAAgE,uDAAuD,6GAAgH,6BAA4B,+DAA+D,kEAAqE,gCAA4B,oEAA0E,mGAAqG,sCAAqC,4EAA4E,wEAAwE,+EAAkF,gCAA+B,gGAAgG,0EAA6E,gCAA4B,oEAA0E,mCAAkC,0FAA0F,8CAA8C,+CAA+C,iEAAoE,iCAAgC,oDAAoD,sGAAyG,kCAAiC,6DAA6D,oDAAoD,+EAAkF,yBAAwB,oEAAuE,8BAA6B,+DAA+D,mDAAmD,gEAAgE,qIAAwI,yCAAwC,0GAA0G,6GAA6G,gEAAgE,kEAAkE,mEAAmE,kDAAkD,gEAAgE,+EAA+E,oKAAuK,iCAAgC,2DAA8D,yBAAwB,4DAA+D,yCAAwC,mDAAmD,6EAA6E,yCAAyC,0KAA2K,8EAA8E,kBAAkB,aAAa,oDAAoD,0CAA4C,mCAAkC,4DAA4D,kEAAkE,gFAAmF,yBAAwB,oDAAoD,uDAAuD,sFAAyF,qCAAoC,8FAAiG,+EAA+E,6GAA6G,kBAAkB,yDAAyD,+BAAiC,yCAAwC,yDAAsD,0DAAgE,yBAAwB,wDAA2D,8BAA6B,+EAA+E,6DAA6D,kEAAqE,yCAAwC,8GAAiH,uCAAsC,uDAAuD,mIAAmI,uFAAuF,+DAA+D,2EAA2E,oDAAoD,yDAAyD,qDAAqD,yEAA4E,sCAAqC,gDAAgD,yCAAyC,oDAAoD,wHAAwH,+EAAkF,2BAA0B,mCAAmC,iEAAoE,gCAA4B,6EAAmF,yCAAwC,qFAAqF,4EAA+E,yCAAwC,yEAA4E,+BAA2B,wEAA8E,yBAAwB,gGAAmG,8BAA6B,+CAA+C,sCAAsC,sGAAyG,4BAA2B,iDAAiD,6FAAgG,gCAA+B,yDAAyD,4DAA4D,sEAAyE,kCAAiC,wDAAwD,0FAA6F,mCAAkC,2DAA2D,+HAA+H,yDAAyD,6DAA6D,qDAAqD,gEAAmE,qCAAoC,sDAAsD,kDAAqD,mCAAkC,uDAAuD,oDAAoD,qDAAqD,4EAA+E,iCAAgC,2DAA8D,iCAAgC,uDAAuD,sEAAyE,yBAAwB,qCAAqC,0CAAyC,kEAAsE,kDAAiD,sDAAsD,wDAAwD,0DAA0D,sDAAsD,yDAAyD,8EAAiF,yBAAwB,oHAAuH,yCAAwC,6CAA6C,uCAAuC,iDAAiD,0FAA0F,6DAA6D,sEAAyE,mCAAkC,sDAAsD,sGAAyG,4BAA2B,8CAA8C,yEAAyE,mFAAsF,yBAAwB,8DAAiE,2BAA0B,iIAAoI,sCAAqC,yDAAyD,qFAAqF,8EAA8E,qEAAqE,6DAA6D,gIAAmI,+BAA8B,sCAAsC,mCAAmC,wFAA2F,iCAAgC,0CAA0C,8CAA8C,+EAAkF,2BAA0B,yCAAyC,2CAA2C,uDAAuD,iGAAoG,qCAAoC,uDAAuD,gEAAgE,kGAAqG,2BAA0B,8CAA8C,uGAA0G,sCAAqC,6CAA6C,kOAAkO,oGAAoG,sDAAsD,iEAAiE,uDAAuD,wDAAwD,2MAA8M,8CAA6C,gDAAgD,qCAAqC,iWAAiW,oGAAoG,4CAA4C,yDAAyD,iHAAiH,8GAAiH,0BAAyB,iCAAiC,kDAAkD,+MAA+M,+CAA+C,mGAAmG,6EAA6E,sEAAyE,mCAAkC,uDAAuD,sDAAsD,2EAA8E,8EAAgF,mCAAkC,yDAAyD,oDAAoD,2EAA8E,iCAAgC,kDAAkD,iFAAoF,yBAAwB,2FAA8F,uCAAsC,8CAA8C,iFAAiF,0GAA0G,yGAAyG,uCAAuC,kEAAkE,wDAAwD,wFAA2F,gCAA+B,yCAAyC,sEAAyE,yCAAwC,wFAAyF,+GAA+G,aAAa,qBAAqB,8DAA8D,oCAAoC,sDAAwD,2BAA0B,2DAA2D,sDAAsD,wDAAwD,iLAAoL,0CAAyC,+DAA+D,uEAA0E,6CAA4C,+MAA+M,sVAAsV,iEAAiE,2FAA2F,uHAA0H,2CAA0C,iDAAiD,8CAA8C,gFAAmF,4BAA2B,2CAA2C,oDAAoD,sDAAsD,qDAAqD,oEAAuE,2BAA0B,gGAAmG,8BAA6B,6EAAgF,iCAAgC,yHAA4H,+CAA8C,0DAA0D,yEAAyE,6GAA6G,yEAAyE,sLAAsL,uFAAuF,+HAA+H,+EAA+E,+EAA+E,2JAA2J,uFAAuF,8HAAiI,+BAA8B,iEAAiE,8CAA8C,8CAA8C,0DAA6D,+FAAiG,iCAAgC,+CAA+C,oDAAoD,6EAA6E,uGAA0G,gFAAkF,oCAAmC,uDAAuD,qFAAqF,uDAAuD,gEAAgE,8EAAiF,yBAAwB,4EAA+E,4CAA2C,6CAA6C,oCAAmC,iDAAqD,yCAAwC,yDAAyD,iGAAoG,+BAA8B,gCAAgC,iCAAiC,4DAA4D,wDAAwD,qDAAqD,2FAA8F,mCAAkC,uCAAuC,uCAAuC,iDAAoD,iCAAgC,oEAAuE,yBAAwB,gFAAiF,8BAA+B,sCAAsC,+CAA+C,2EAA8E,4BAAwB,4DAAkE,qCAAoC,iEAAiE,sFAAyF,2BAA0B,wCAAwC,0CAA6C,kEAAoE,kCAA8B,8DAAoE,yCAAwC,4EAA+E,yCAAwC,2FAA8F,sCAAqC,sDAAsD,yDAAyD,yDAA4D,8BAA6B,uDAAuD,0DAA0D,8DAA8D,gFAAmF,8DAAgE,2BAA0B,4CAA4C,yDAA4D,yCAAwC,gEAAgE,sDAAsD,kFAAqF,iCAAgC,8FAAiG,2BAA0B,iEAAiE,mCAAmC,oFAAuF,0CAAyC,4EAA4E,yHAAyH,4EAA4E,0EAA6E,mCAA+B,yDAA+D,6BAA4B,mCAAmC,oCAAoC,6FAAgG,wCAAuC,wDAAwD,wDAAwD,uIAAuI,yDAAyD,gGAAmG,iCAAgC,uDAAuD,+CAA+C,2CAA2C,yEAA4E,oCAAmC,wLAAwL,yOAAyO,oEAAoE,4GAA+G,yBAAwB,kDAAkD,+DAA+D,kEAAqE,0BAAyB,iDAAiD,6xBAA8xB,8CAA8C,mBAAmB,4OAA4O,uBAAuB,iFAAmF,6CAA4C,mCAAmC,uDAAuD,4CAA4C,iDAAiD,wDAAwD,+FAAkG,yCAAwC,0HAA6H,qFAAuF,gCAA4B,sEAA4E,0BAAyB,+EAAkF,kCAA8B,6DAAmE,gCAA4B,kFAAwF,gCAA+B,8CAA8C,uDAAuD,yDAAyD,4DAA4D,0DAA0D,oFAAuF,2BAA0B,0EAA6E,mCAAkC,2DAA2D,qFAAwF,wBAAuB,6CAA6C,sCAAsC,gFAAmF,oCAAmC,oDAAoD,wDAAwD,kDAAkD,yEAA4E,oCAAmC,2EAA2E,6FAAgG,mEAAmE,iFAAiF,aAAa,+CAA+C,0CAA4C,sCAAqC,wDAAwD,yDAAyD,sDAAsD,wDAAwD,kFAAqF,iCAAgC,+CAA+C,sKAAyK,sCAAqC,qIAAqI,+CAA+C,0CAA0C,yKAAyK,sDAAsD,uIAAuI,2CAA2C,2LAA2L,4DAA4D,gDAAkD,eAAkB,qCAAqC,yBAAwB,4DAA6D,8BAAgC,yBAAwB,kEAAmE,yCAA2C,oCAAmC,gFAAiF,wBAAwB,gCAAkC,kCAAiC,6DAA8D,yCAA2C,iCAAgC,oDAAoD,4DAA6D,uFAAuF,sCAAuC,wCAAwC,4CAA4C,oDAAoD,oDAAoD,+CAA+C,uEAAuE,iDAAiD,0GAA2G,sCAAsC,2LAA2L,2CAA6C,8CAA6C,8EAA8E,gDAAgD,+CAA+C,2DAA2D,2LAA4L,0BAA4B,kCAAiC,iDAAkD,+BAAiC,8BAA6B,2DAA4D,KCC1qlF,SAAAjB,EAAAkB,CAAA,CAAAC,CAAA,EACf,IAAA7Y,EAAA+E,MAAA5K,SAAA,CAAAiL,KAAA,CAAA7K,IAAA,CAAAse,GAEA,OADA7Y,EAAAI,IAAA,CAAWuY,GACXC,EAAAvY,KAAA,MAAAL,EACA", "sources": ["webpack://_N_E/./node_modules/iconsax-react/dist/esm/Global.js", "webpack://_N_E/../src/checkbox.tsx", "webpack://_N_E/../src/radio-group.tsx", "webpack://_N_E/../src/radio.tsx", "webpack://_N_E/./node_modules/libphonenumber-js/es6/ParseError.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/getPossibleCountriesForNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/applyInternationalSeparatorStyle.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/formatNationalNumberUsingFormat.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/getIddPrefix.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/RFC3966.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/format.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/PhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/constants.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/mergeArrays.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/checkNumberLength.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extension/createExtensionPattern.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/stripIddPrefix.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extractCountryCallingCode.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extractNationalNumberFromPossiblyIncompleteNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extractNationalNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/getNumberType.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/isObject.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/isViablePhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/matchesEntirely.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/isPossible.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/isValid.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/tools/semver-compare.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/metadata.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/normalizeArguments.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extension/extractExtension.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/parseDigits.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/parseIncompletePhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/getCountryByNationalNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/getCountryByCallingCode.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extractPhoneContext.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/parse.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/parsePhoneNumberWithError_.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/parsePhoneNumber_.js", "webpack://_N_E/./node_modules/libphonenumber-js/examples.mobile.json.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/legacy/getNumberType.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/legacy/isValidNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/index.es6.exports/isValidNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/getCountries.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/getCountries.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/getCountryCallingCode.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/getExampleNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/getExampleNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/isPossiblePhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/isPossiblePhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/isValidPhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/isValidPhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/parsePhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/parsePhoneNumber.js", "webpack://_N_E/./node_modules/libphonenumber-js/es6/validatePhoneNumberLength.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/validatePhoneNumberLength.js", "webpack://_N_E/./node_modules/libphonenumber-js/metadata.min.json.js", "webpack://_N_E/./node_modules/libphonenumber-js/min/exports/withMetadataArgument.js"], "sourcesContent": ["import { _ as _objectWithoutProperties, a as _extends } from './_rollupPluginBabelHelpers-3bc641ae.js';\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\n\nvar _excluded = [\"variant\", \"color\", \"size\"];\n\nvar Bold = function Bold(_ref) {\n  var color = _ref.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.649 20.91c-.03 0-.07.02-.1.02-1.94-.96-3.52-2.55-4.49-4.49 0-.03.02-.07.02-.1 1.22.36 2.48.63 3.73.84.22 1.26.48 2.51.84 3.73ZM20.94 16.45c-.99 1.99-2.64 3.6-4.65 4.57.38-1.27.7-2.55.91-3.84 1.26-.21 2.5-.48 3.72-.84-.01.04.02.08.02.11ZM21.02 7.71c-1.26-.38-2.53-.69-3.82-.91-.21-1.29-.52-2.57-.91-3.82 2.07.99 3.74 2.66 4.73 4.73ZM7.65 3.089c-.36 1.22-.62 2.46-.83 3.72-1.29.2-2.57.52-3.84.9.97-2.01 2.58-3.66 4.57-4.65.03 0 .07.03.1.03ZM15.492 6.59c-2.32-.26-4.66-.26-6.98 0 .25-1.37.57-2.74 1.02-4.06.02-.08.01-.14.02-.22.79-.19 1.6-.31 2.45-.31.84 0 1.66.12 2.44.31.01.08.01.14.03.22.45 1.33.77 2.69 1.02 4.06ZM6.59 15.492c-1.38-.25-2.74-.57-4.06-1.02-.08-.02-.14-.01-.22-.02-.19-.79-.31-1.6-.31-2.45 0-.84.12-1.66.31-2.44.08-.01.14-.01.22-.03 1.33-.44 2.68-.77 4.06-1.02-.25 2.32-.25 4.66 0 6.98ZM22 12.002c0 .85-.12 1.66-.31 2.45-.08.01-.14 0-.22.02-1.33.44-2.69.77-4.06 1.02.26-2.32.26-4.66 0-6.98 1.37.25 2.74.57 4.06 1.02.08.02.14.03.22.03.19.79.31 1.6.31 2.44ZM15.492 17.41c-.25 1.38-.57 2.74-1.02 4.06-.02.08-.02.14-.03.22-.78.19-1.6.31-2.44.31-.85 0-1.66-.12-2.45-.31-.01-.08 0-.14-.02-.22a29.77 29.77 0 0 1-1.02-4.06c1.16.13 2.32.22 3.49.22 1.17 0 2.34-.09 3.49-.22ZM15.763 15.763a30.035 30.035 0 0 1-7.526 0 30.039 30.039 0 0 1 0-7.526 30.039 30.039 0 0 1 7.526 0 30.035 30.035 0 0 1 0 7.526Z\",\n    fill: color\n  }));\n};\n\nvar Broken = function Broken(_ref2) {\n  var color = _ref2.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.54 12c0 3.04.49 6.08 1.46 9H8M7.998 3h1c-.49 1.46-.85 2.95-1.1 4.46M16.13 16.36c-.25 1.56-.62 3.12-1.13 4.64M15 3c.97 2.92 1.46 5.96 1.46 9\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3 16v-1a28.424 28.424 0 0 0 18 0v1M3 9.002a28.424 28.424 0 0 1 18 0\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Bulk = function Bulk(_ref3) {\n  var color = _ref3.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    opacity: \".4\",\n    d: \"M7.649 20.91c-.03 0-.07.02-.1.02-1.94-.96-3.52-2.55-4.49-4.49 0-.03.02-.07.02-.1 1.22.36 2.48.63 3.73.84.22 1.26.48 2.51.84 3.73ZM20.94 16.45c-.99 1.99-2.64 3.6-4.65 4.57.38-1.27.7-2.55.91-3.84 1.26-.21 2.5-.48 3.72-.84-.01.04.02.08.02.11ZM21.02 7.71c-1.26-.38-2.53-.69-3.82-.91-.21-1.29-.52-2.57-.91-3.82 2.07.99 3.74 2.66 4.73 4.73ZM7.65 3.089c-.36 1.22-.62 2.46-.83 3.72-1.29.2-2.57.52-3.84.9.97-2.01 2.58-3.66 4.57-4.65.03 0 .07.03.1.03Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M15.492 6.59c-2.32-.26-4.66-.26-6.98 0 .25-1.37.57-2.74 1.02-4.06.02-.08.01-.14.02-.22.79-.19 1.6-.31 2.45-.31.84 0 1.66.12 2.44.31.01.08.01.14.03.22.45 1.33.77 2.69 1.02 4.06ZM6.59 15.492c-1.38-.25-2.74-.57-4.06-1.02-.08-.02-.14-.01-.22-.02-.19-.79-.31-1.6-.31-2.45 0-.84.12-1.66.31-2.44.08-.01.14-.01.22-.03 1.33-.44 2.68-.77 4.06-1.02-.25 2.32-.25 4.66 0 6.98ZM22 12.002c0 .85-.12 1.66-.31 2.45-.08.01-.14 0-.22.02-1.33.44-2.69.77-4.06 1.02.26-2.32.26-4.66 0-6.98 1.37.25 2.74.57 4.06 1.02.08.02.14.03.22.03.19.79.31 1.6.31 2.44ZM15.492 17.41c-.25 1.38-.57 2.74-1.02 4.06-.02.08-.02.14-.03.22-.78.19-1.6.31-2.44.31-.85 0-1.66-.12-2.45-.31-.01-.08 0-.14-.02-.22a29.77 29.77 0 0 1-1.02-4.06c1.16.13 2.32.22 3.49.22 1.17 0 2.34-.09 3.49-.22ZM15.763 15.763a30.035 30.035 0 0 1-7.526 0 30.039 30.039 0 0 1 0-7.526 30.039 30.039 0 0 1 7.526 0 30.035 30.035 0 0 1 0 7.526Z\",\n    fill: color\n  }));\n};\n\nvar Linear = function Linear(_ref4) {\n  var color = _ref4.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10Z\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8 3h1a28.424 28.424 0 0 0 0 18H8M15 3a28.424 28.424 0 0 1 0 18\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3 16v-1a28.424 28.424 0 0 0 18 0v1M3 9a28.424 28.424 0 0 1 18 0\",\n    stroke: color,\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }));\n};\n\nvar Outline = function Outline(_ref5) {\n  var color = _ref5.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75Zm0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.998 21.75h-1c-.41 0-.75-.34-.75-.75s.32-.74.73-.75a29.49 29.49 0 0 1 0-16.5.745.745 0 0 1-.73-.75c0-.41.34-.75.75-.75h1c.24 0 .47.12.61.31.14.2.18.45.1.68a27.948 27.948 0 0 0 0 17.53c.08.23.04.48-.1.68-.14.18-.37.3-.61.3ZM14.998 21.75a.745.745 0 0 1-.71-.99 27.948 27.948 0 0 0 0-17.53.749.749 0 1 1 1.42-.48 29.318 29.318 0 0 1 0 18.47c-.1.33-.4.53-.71.53Z\",\n    fill: color\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M12 17.2c-2.79 0-5.57-.39-8.25-1.18-.01.4-.34.73-.75.73s-.75-.34-.75-.75v-1c0-.24.12-.47.31-.61.2-.14.45-.18.68-.1a27.948 27.948 0 0 0 17.53 0 .75.75 0 0 1 .68.1c.2.14.31.37.31.61v1c0 .41-.34.75-.75.75s-.74-.32-.75-.73c-2.69.79-5.47 1.18-8.26 1.18ZM21 9.75c-.08 0-.16-.01-.24-.04a27.948 27.948 0 0 0-17.53 0c-.4.13-.82-.08-.95-.47-.12-.4.09-.82.48-.95a29.318 29.318 0 0 1 18.47 0c.39.13.61.56.47.95a.73.73 0 0 1-.7.51Z\",\n    fill: color\n  }));\n};\n\nvar TwoTone = function TwoTone(_ref6) {\n  var color = _ref6.color;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    stroke: color,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: \"1.5\",\n    d: \"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    opacity: \".4\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    stroke: color,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: \"1.5\",\n    d: \"M8 3h1a28.424 28.424 0 000 18H8M15 3a28.424 28.424 0 010 18\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    stroke: color,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: \"1.5\",\n    d: \"M3 16v-1a28.424 28.424 0 0018 0v1M3 9a28.424 28.424 0 0118 0\"\n  })));\n};\n\nvar chooseVariant = function chooseVariant(variant, color) {\n  switch (variant) {\n    case 'Bold':\n      return /*#__PURE__*/React.createElement(Bold, {\n        color: color\n      });\n\n    case 'Broken':\n      return /*#__PURE__*/React.createElement(Broken, {\n        color: color\n      });\n\n    case 'Bulk':\n      return /*#__PURE__*/React.createElement(Bulk, {\n        color: color\n      });\n\n    case 'Linear':\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n\n    case 'Outline':\n      return /*#__PURE__*/React.createElement(Outline, {\n        color: color\n      });\n\n    case 'TwoTone':\n      return /*#__PURE__*/React.createElement(TwoTone, {\n        color: color\n      });\n\n    default:\n      return /*#__PURE__*/React.createElement(Linear, {\n        color: color\n      });\n  }\n};\n\nvar Global = /*#__PURE__*/forwardRef(function (_ref7, ref) {\n  var variant = _ref7.variant,\n      color = _ref7.color,\n      size = _ref7.size,\n      rest = _objectWithoutProperties(_ref7, _excluded);\n\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: ref,\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\"\n  }), chooseVariant(variant, color));\n});\nGlobal.propTypes = {\n  variant: PropTypes.oneOf(['Linear', 'Bold', 'Broken', 'Bulk', 'Outline', 'TwoTone']),\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nGlobal.defaultProps = {\n  variant: 'Linear',\n  color: 'currentColor',\n  size: '24'\n};\nGlobal.displayName = 'Global';\n\nexport { Global as default };\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Checkbox\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_NAME = 'Checkbox';\n\ntype ScopedProps<P> = P & { __scopeCheckbox?: Scope };\nconst [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\n\ntype CheckedState = boolean | 'indeterminate';\n\ntype CheckboxContextValue = {\n  state: CheckedState;\n  disabled?: boolean;\n};\n\nconst [CheckboxProvider, useCheckboxContext] =\n  createCheckboxContext<CheckboxContextValue>(CHECKBOX_NAME);\n\ntype CheckboxElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CheckboxProps extends Omit<PrimitiveButtonProps, 'checked' | 'defaultChecked'> {\n  checked?: CheckedState;\n  defaultChecked?: CheckedState;\n  required?: boolean;\n  onCheckedChange?(checked: CheckedState): void;\n}\n\nconst Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>(\n  (props: ScopedProps<CheckboxProps>, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked ?? false,\n      onChange: onCheckedChange,\n      caller: CHECKBOX_NAME,\n    });\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = button?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [button, setChecked]);\n\n    return (\n      <CheckboxProvider scope={__scopeCheckbox} state={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"checkbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...checkboxProps}\n          ref={composedRefs}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            // According to WAI ARIA, Checkboxes don't activate on enter keypress\n            if (event.key === 'Enter') event.preventDefault();\n          })}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => (isIndeterminate(prevChecked) ? true : !prevChecked));\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if checkbox is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect checkbox updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <CheckboxBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n            defaultChecked={isIndeterminate(defaultChecked) ? false : defaultChecked}\n          />\n        )}\n      </CheckboxProvider>\n    );\n  }\n);\n\nCheckbox.displayName = CHECKBOX_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'CheckboxIndicator';\n\ntype CheckboxIndicatorElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface CheckboxIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CheckboxIndicator = React.forwardRef<CheckboxIndicatorElement, CheckboxIndicatorProps>(\n  (props: ScopedProps<CheckboxIndicatorProps>, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return (\n      <Presence present={forceMount || isIndeterminate(context.state) || context.state === true}>\n        <Primitive.span\n          data-state={getState(context.state)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n          style={{ pointerEvents: 'none', ...props.style }}\n        />\n      </Presence>\n    );\n  }\n);\n\nCheckboxIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'CheckboxBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface CheckboxBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: CheckedState;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst CheckboxBubbleInput = React.forwardRef<HTMLInputElement, CheckboxBubbleInputProps>(\n  (\n    {\n      __scopeCheckbox,\n      control,\n      checked,\n      bubbles = true,\n      defaultChecked,\n      ...props\n    }: ScopedProps<CheckboxBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        input.indeterminate = isIndeterminate(checked);\n        setChecked.call(input, isIndeterminate(checked) ? false : checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n    return (\n      <Primitive.input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={defaultChecked ?? defaultCheckedRef.current}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Checkbox;\nconst Indicator = CheckboxIndicator;\n\nexport {\n  createCheckboxScope,\n  //\n  Checkbox,\n  CheckboxIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { CheckboxProps, CheckboxIndicatorProps, CheckedState };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { Radio, RadioIndicator, createRadioScope } from './radio';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ARROW_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroup\n * -----------------------------------------------------------------------------------------------*/\nconst RADIO_GROUP_NAME = 'RadioGroup';\n\ntype ScopedProps<P> = P & { __scopeRadioGroup?: Scope };\nconst [createRadioGroupContext, createRadioGroupScope] = createContextScope(RADIO_GROUP_NAME, [\n  createRovingFocusGroupScope,\n  createRadioScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\nconst useRadioScope = createRadioScope();\n\ntype RadioGroupContextValue = {\n  name?: string;\n  required: boolean;\n  disabled: boolean;\n  value: string;\n  onValueChange(value: string): void;\n};\n\nconst [RadioGroupProvider, useRadioGroupContext] =\n  createRadioGroupContext<RadioGroupContextValue>(RADIO_GROUP_NAME);\n\ntype RadioGroupElement = React.ElementRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RadioGroupProps extends PrimitiveDivProps {\n  name?: RadioGroupContextValue['name'];\n  required?: React.ComponentPropsWithoutRef<typeof Radio>['required'];\n  disabled?: React.ComponentPropsWithoutRef<typeof Radio>['disabled'];\n  dir?: RovingFocusGroupProps['dir'];\n  orientation?: RovingFocusGroupProps['orientation'];\n  loop?: RovingFocusGroupProps['loop'];\n  defaultValue?: string;\n  value?: RadioGroupContextValue['value'];\n  onValueChange?: RadioGroupContextValue['onValueChange'];\n}\n\nconst RadioGroup = React.forwardRef<RadioGroupElement, RadioGroupProps>(\n  (props: ScopedProps<RadioGroupProps>, forwardedRef) => {\n    const {\n      __scopeRadioGroup,\n      name,\n      defaultValue,\n      value: valueProp,\n      required = false,\n      disabled = false,\n      orientation,\n      dir,\n      loop = true,\n      onValueChange,\n      ...groupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? '',\n      onChange: onValueChange,\n      caller: RADIO_GROUP_NAME,\n    });\n\n    return (\n      <RadioGroupProvider\n        scope={__scopeRadioGroup}\n        name={name}\n        required={required}\n        disabled={disabled}\n        value={value}\n        onValueChange={setValue}\n      >\n        <RovingFocusGroup.Root\n          asChild\n          {...rovingFocusGroupScope}\n          orientation={orientation}\n          dir={direction}\n          loop={loop}\n        >\n          <Primitive.div\n            role=\"radiogroup\"\n            aria-required={required}\n            aria-orientation={orientation}\n            data-disabled={disabled ? '' : undefined}\n            dir={direction}\n            {...groupProps}\n            ref={forwardedRef}\n          />\n        </RovingFocusGroup.Root>\n      </RadioGroupProvider>\n    );\n  }\n);\n\nRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RadioGroupItem';\n\ntype RadioGroupItemElement = React.ElementRef<typeof Radio>;\ntype RadioProps = React.ComponentPropsWithoutRef<typeof Radio>;\ninterface RadioGroupItemProps extends Omit<RadioProps, 'onCheck' | 'name'> {\n  value: string;\n}\n\nconst RadioGroupItem = React.forwardRef<RadioGroupItemElement, RadioGroupItemProps>(\n  (props: ScopedProps<RadioGroupItemProps>, forwardedRef) => {\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = React.useRef<React.ElementRef<typeof Radio>>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = React.useRef(false);\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (ARROW_KEYS.includes(event.key)) {\n          isArrowKeyPressedRef.current = true;\n        }\n      };\n      const handleKeyUp = () => (isArrowKeyPressedRef.current = false);\n      document.addEventListener('keydown', handleKeyDown);\n      document.addEventListener('keyup', handleKeyUp);\n      return () => {\n        document.removeEventListener('keydown', handleKeyDown);\n        document.removeEventListener('keyup', handleKeyUp);\n      };\n    }, []);\n\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!isDisabled}\n        active={checked}\n      >\n        <Radio\n          disabled={isDisabled}\n          required={context.required}\n          checked={checked}\n          {...radioScope}\n          {...itemProps}\n          name={context.name}\n          ref={composedRefs}\n          onCheck={() => context.onValueChange(itemProps.value)}\n          onKeyDown={composeEventHandlers((event) => {\n            // According to WAI ARIA, radio groups don't activate items on enter keypress\n            if (event.key === 'Enter') event.preventDefault();\n          })}\n          onFocus={composeEventHandlers(itemProps.onFocus, () => {\n            /**\n             * Our `RovingFocusGroup` will focus the radio when navigating with arrow keys\n             * and we need to \"check\" it in that case. We click it to \"check\" it (instead\n             * of updating `context.value`) so that the radio change event fires.\n             */\n            if (isArrowKeyPressedRef.current) ref.current?.click();\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nRadioGroupItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioGroupIndicator';\n\ntype RadioGroupIndicatorElement = React.ElementRef<typeof RadioIndicator>;\ntype RadioIndicatorProps = React.ComponentPropsWithoutRef<typeof RadioIndicator>;\ninterface RadioGroupIndicatorProps extends RadioIndicatorProps {}\n\nconst RadioGroupIndicator = React.forwardRef<RadioGroupIndicatorElement, RadioGroupIndicatorProps>(\n  (props: ScopedProps<RadioGroupIndicatorProps>, forwardedRef) => {\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return <RadioIndicator {...radioScope} {...indicatorProps} ref={forwardedRef} />;\n  }\n);\n\nRadioGroupIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = RadioGroup;\nconst Item = RadioGroupItem;\nconst Indicator = RadioGroupIndicator;\n\nexport {\n  createRadioGroupScope,\n  //\n  RadioGroup,\n  RadioGroupItem,\n  RadioGroupIndicator,\n  //\n  Root,\n  Item,\n  Indicator,\n};\nexport type { RadioGroupProps, RadioGroupItemProps, RadioGroupIndicatorProps };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Radio\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_NAME = 'Radio';\n\ntype ScopedProps<P> = P & { __scopeRadio?: Scope };\nconst [createRadioContext, createRadioScope] = createContextScope(RADIO_NAME);\n\ntype RadioContextValue = { checked: boolean; disabled?: boolean };\nconst [RadioProvider, useRadioContext] = createRadioContext<RadioContextValue>(RADIO_NAME);\n\ntype RadioElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface RadioProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  required?: boolean;\n  onCheck?(): void;\n}\n\nconst Radio = React.forwardRef<RadioElement, RadioProps>(\n  (props: ScopedProps<RadioProps>, forwardedRef) => {\n    const {\n      __scopeRadio,\n      name,\n      checked = false,\n      required,\n      disabled,\n      value = 'on',\n      onCheck,\n      form,\n      ...radioProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n\n    return (\n      <RadioProvider scope={__scopeRadio} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"radio\"\n          aria-checked={checked}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...radioProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            // radios cannot be unchecked so we only communicate a checked state\n            if (!checked) onCheck?.();\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if radio is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect radio updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <RadioBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </RadioProvider>\n    );\n  }\n);\n\nRadio.displayName = RADIO_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioIndicator';\n\ntype RadioIndicatorElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\nexport interface RadioIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst RadioIndicator = React.forwardRef<RadioIndicatorElement, RadioIndicatorProps>(\n  (props: ScopedProps<RadioIndicatorProps>, forwardedRef) => {\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return (\n      <Presence present={forceMount || context.checked}>\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n        />\n      </Presence>\n    );\n  }\n);\n\nRadioIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'RadioBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface RadioBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst RadioBubbleInput = React.forwardRef<HTMLInputElement, RadioBubbleInputProps>(\n  (\n    {\n      __scopeRadio,\n      control,\n      checked,\n      bubbles = true,\n      ...props\n    }: ScopedProps<RadioBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    return (\n      <Primitive.input\n        type=\"radio\"\n        aria-hidden\n        defaultChecked={checked}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nRadioBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createRadioScope,\n  //\n  Radio,\n  RadioIndicator,\n};\nexport type { RadioProps };\n", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _wrapNativeSuper(Class) { var _cache = typeof Map === \"function\" ? new Map() : undefined; _wrapNativeSuper = function _wrapNativeSuper(Class) { if (Class === null || !_isNativeFunction(Class)) return Class; if (typeof Class !== \"function\") { throw new TypeError(\"Super expression must either be null or a function\"); } if (typeof _cache !== \"undefined\") { if (_cache.has(Class)) return _cache.get(Class); _cache.set(Class, Wrapper); } function Wrapper() { return _construct(Class, arguments, _getPrototypeOf(this).constructor); } Wrapper.prototype = Object.create(Class.prototype, { constructor: { value: Wrapper, enumerable: false, writable: true, configurable: true } }); return _setPrototypeOf(Wrapper, Class); }; return _wrapNativeSuper(Class); }\n\nfunction _construct(Parent, args, Class) { if (_isNativeReflectConstruct()) { _construct = Reflect.construct; } else { _construct = function _construct(Parent, args, Class) { var a = [null]; a.push.apply(a, args); var Constructor = Function.bind.apply(Parent, a); var instance = new Constructor(); if (Class) _setPrototypeOf(instance, Class.prototype); return instance; }; } return _construct.apply(null, arguments); }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _isNativeFunction(fn) { return Function.toString.call(fn).indexOf(\"[native code]\") !== -1; }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n// https://stackoverflow.com/a/46971044/970769\n// \"Breaking changes in Typescript 2.1\"\n// \"Extending built-ins like Error, Array, and Map may no longer work.\"\n// \"As a recommendation, you can manually adjust the prototype immediately after any super(...) calls.\"\n// https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\nvar ParseError = /*#__PURE__*/function (_Error) {\n  _inherits(ParseError, _Error);\n\n  var _super = _createSuper(ParseError);\n\n  function ParseError(code) {\n    var _this;\n\n    _classCallCheck(this, ParseError);\n\n    _this = _super.call(this, code); // Set the prototype explicitly.\n    // Any subclass of FooError will have to manually set the prototype as well.\n\n    Object.setPrototypeOf(_assertThisInitialized(_this), ParseError.prototype);\n    _this.name = _this.constructor.name;\n    return _this;\n  }\n\n  return _createClass(ParseError);\n}( /*#__PURE__*/_wrapNativeSuper(Error));\n\nexport { ParseError as default };\n//# sourceMappingURL=ParseError.js.map", "import Metadata from '../metadata.js';\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\n\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\n  var _metadata = new Metadata(metadata);\n\n  var possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode);\n\n  if (!possibleCountries) {\n    return [];\n  }\n\n  return possibleCountries.filter(function (country) {\n    return couldNationalNumberBelongToCountry(nationalNumber, country, metadata);\n  });\n}\n\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\n  var _metadata = new Metadata(metadata);\n\n  _metadata.selectNumberingPlan(country);\n\n  if (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\n    return true;\n  }\n\n  return false;\n}\n//# sourceMappingURL=getPossibleCountriesForNumber.js.map", "import { VALID_PUNCTUATION } from '../constants.js'; // Removes brackets and replaces dashes with spaces.\n//\n// E.g. \"(999) 111-22-33\" -> \"999 111 22 33\"\n//\n// For some reason Google's metadata contains `<intlFormat/>`s with brackets and dashes.\n// Meanwhile, there's no single opinion about using punctuation in international phone numbers.\n//\n// For example, Google's `<intlFormat/>` for USA is `******-373-4253`.\n// And here's a quote from WikiPedia's \"North American Numbering Plan\" page:\n// https://en.wikipedia.org/wiki/North_American_Numbering_Plan\n//\n// \"The country calling code for all countries participating in the NANP is 1.\n// In international format, an NANP number should be listed as ****** 555 01 00,\n// where 301 is an area code (Maryland).\"\n//\n// I personally prefer the international format without any punctuation.\n// For example, brackets are remnants of the old age, meaning that the\n// phone number part in brackets (so called \"area code\") can be omitted\n// if dialing within the same \"area\".\n// And hyphens were clearly introduced for splitting local numbers into memorizable groups.\n// For example, remembering \"5553535\" is difficult but \"555-35-35\" is much simpler.\n// Imagine a man taking a bus from home to work and seeing an ad with a phone number.\n// He has a couple of seconds to memorize that number until it passes by.\n// If it were spaces instead of hyphens the man wouldn't necessarily get it,\n// but with hyphens instead of spaces the grouping is more explicit.\n// I personally think that hyphens introduce visual clutter,\n// so I prefer replacing them with spaces in international numbers.\n// In the modern age all output is done on displays where spaces are clearly distinguishable\n// so hyphens can be safely replaced with spaces without losing any legibility.\n//\n\nexport default function applyInternationalSeparatorStyle(formattedNumber) {\n  return formattedNumber.replace(new RegExp(\"[\".concat(VALID_PUNCTUATION, \"]+\"), 'g'), ' ').trim();\n}\n//# sourceMappingURL=applyInternationalSeparatorStyle.js.map", "import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'; // This was originally set to $1 but there are some countries for which the\n// first group is not used in the national pattern (e.g. Argentina) so the $1\n// group does not match correctly. Therefore, we use `\\d`, so that the first\n// group actually used in the pattern will be matched.\n\nexport var FIRST_GROUP_PATTERN = /(\\$\\d)/;\nexport default function formatNationalNumberUsingFormat(number, format, _ref) {\n  var useInternationalFormat = _ref.useInternationalFormat,\n      withNationalPrefix = _ref.withNationalPrefix,\n      carrierCode = _ref.carrierCode,\n      metadata = _ref.metadata;\n  var formattedNumber = number.replace(new RegExp(format.pattern()), useInternationalFormat ? format.internationalFormat() : // This library doesn't use `domestic_carrier_code_formatting_rule`,\n  // because that one is only used when formatting phone numbers\n  // for dialing from a mobile phone, and this is not a dialing library.\n  // carrierCode && format.domesticCarrierCodeFormattingRule()\n  // \t// First, replace the $CC in the formatting rule with the desired carrier code.\n  // \t// Then, replace the $FG in the formatting rule with the first group\n  // \t// and the carrier code combined in the appropriate way.\n  // \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\n  // \t: (\n  // \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\n  // \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\n  // \t\t\t: format.format()\n  // \t)\n  withNationalPrefix && format.nationalPrefixFormattingRule() ? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule()) : format.format());\n\n  if (useInternationalFormat) {\n    return applyInternationalSeparatorStyle(formattedNumber);\n  }\n\n  return formattedNumber;\n}\n//# sourceMappingURL=formatNationalNumberUsingFormat.js.map", "import Metadata from '../metadata.js';\n/**\r\n * <PERSON><PERSON> that makes it easy to distinguish whether a region has a single\r\n * international dialing prefix or not. If a region has a single international\r\n * prefix (e.g. 011 in USA), it will be represented as a string that contains\r\n * a sequence of ASCII digits, and possibly a tilde, which signals waiting for\r\n * the tone. If there are multiple available international prefixes in a\r\n * region, they will be represented as a regex string that always contains one\r\n * or more characters that are not ASCII digits or a tilde.\r\n */\n\nvar SINGLE_IDD_PREFIX_REG_EXP = /^[\\d]+(?:[~\\u2053\\u223C\\uFF5E][\\d]+)?$/; // For regions that have multiple IDD prefixes\n// a preferred IDD prefix is returned.\n\nexport default function getIddPrefix(country, callingCode, metadata) {\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n\n  if (countryMetadata.defaultIDDPrefix()) {\n    return countryMetadata.defaultIDDPrefix();\n  }\n\n  if (SINGLE_IDD_PREFIX_REG_EXP.test(countryMetadata.IDDPrefix())) {\n    return countryMetadata.IDDPrefix();\n  }\n}\n//# sourceMappingURL=getIddPrefix.js.map", "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport isViablePhoneNumber from './isViablePhoneNumber.js'; // https://www.ietf.org/rfc/rfc3966.txt\n\n/**\r\n * @param  {string} text - Phone URI (RFC 3966).\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\n\nexport function parseRFC3966(text) {\n  var number;\n  var ext; // Replace \"tel:\" with \"tel=\" for parsing convenience.\n\n  text = text.replace(/^tel:/, 'tel=');\n\n  for (var _iterator = _createForOfIteratorHelperLoose(text.split(';')), _step; !(_step = _iterator()).done;) {\n    var part = _step.value;\n\n    var _part$split = part.split('='),\n        _part$split2 = _slicedToArray(_part$split, 2),\n        name = _part$split2[0],\n        value = _part$split2[1];\n\n    switch (name) {\n      case 'tel':\n        number = value;\n        break;\n\n      case 'ext':\n        ext = value;\n        break;\n\n      case 'phone-context':\n        // Only \"country contexts\" are supported.\n        // \"Domain contexts\" are ignored.\n        if (value[0] === '+') {\n          number = value + number;\n        }\n\n        break;\n    }\n  } // If the phone number is not viable, then abort.\n\n\n  if (!isViablePhoneNumber(number)) {\n    return {};\n  }\n\n  var result = {\n    number: number\n  };\n\n  if (ext) {\n    result.ext = ext;\n  }\n\n  return result;\n}\n/**\r\n * @param  {object} - `{ ?number, ?extension }`.\r\n * @return {string} Phone URI (RFC 3966).\r\n */\n\nexport function formatRFC3966(_ref) {\n  var number = _ref.number,\n      ext = _ref.ext;\n\n  if (!number) {\n    return '';\n  }\n\n  if (number[0] !== '+') {\n    throw new Error(\"\\\"formatRFC3966()\\\" expects \\\"number\\\" to be in E.164 format.\");\n  }\n\n  return \"tel:\".concat(number).concat(ext ? ';ext=' + ext : '');\n}\n//# sourceMappingURL=RFC3966.js.map", "function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js';\nimport Metadata, { getCountryCallingCode } from './metadata.js';\nimport getIddPrefix from './helpers/getIddPrefix.js';\nimport { formatRFC3966 } from './helpers/RFC3966.js';\nvar DEFAULT_OPTIONS = {\n  formatExtension: function formatExtension(formattedNumber, extension, metadata) {\n    return \"\".concat(formattedNumber).concat(metadata.ext()).concat(extension);\n  }\n};\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\n\nexport default function formatNumber(input, format, options, metadata) {\n  // Apply default options.\n  if (options) {\n    options = _objectSpread(_objectSpread({}, DEFAULT_OPTIONS), options);\n  } else {\n    options = DEFAULT_OPTIONS;\n  }\n\n  metadata = new Metadata(metadata);\n\n  if (input.country && input.country !== '001') {\n    // Validate `input.country`.\n    if (!metadata.hasCountry(input.country)) {\n      throw new Error(\"Unknown country: \".concat(input.country));\n    }\n\n    metadata.country(input.country);\n  } else if (input.countryCallingCode) {\n    metadata.selectNumberingPlan(input.countryCallingCode);\n  } else return input.phone || '';\n\n  var countryCallingCode = metadata.countryCallingCode();\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone; // This variable should have been declared inside `case`s\n  // but Babel has a bug and it says \"duplicate variable declaration\".\n\n  var number;\n\n  switch (format) {\n    case 'NATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return '';\n      }\n\n      number = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n\n    case 'INTERNATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return \"+\".concat(countryCallingCode);\n      }\n\n      number = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options);\n      number = \"+\".concat(countryCallingCode, \" \").concat(number);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n\n    case 'E.164':\n      // `E.164` doesn't define \"phone number extensions\".\n      return \"+\".concat(countryCallingCode).concat(nationalNumber);\n\n    case 'RFC3966':\n      return formatRFC3966({\n        number: \"+\".concat(countryCallingCode).concat(nationalNumber),\n        ext: input.ext\n      });\n    // For reference, here's Google's IDD formatter:\n    // https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\n    // Not saying that this IDD formatter replicates it 1:1, but it seems to work.\n    // Who would even need to format phone numbers in IDD format anyway?\n\n    case 'IDD':\n      if (!options.fromCountry) {\n        return; // throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\n      }\n\n      var formattedNumber = formatIDD(nationalNumber, input.carrierCode, countryCallingCode, options.fromCountry, metadata);\n      return addExtension(formattedNumber, input.ext, metadata, options.formatExtension);\n\n    default:\n      throw new Error(\"Unknown \\\"format\\\" argument passed to \\\"formatNumber()\\\": \\\"\".concat(format, \"\\\"\"));\n  }\n}\n\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\n  var format = chooseFormatForNumber(metadata.formats(), number);\n\n  if (!format) {\n    return number;\n  }\n\n  return formatNationalNumberUsingFormat(number, format, {\n    useInternationalFormat: formatAs === 'INTERNATIONAL',\n    withNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && options && options.nationalPrefix === false ? false : true,\n    carrierCode: carrierCode,\n    metadata: metadata\n  });\n}\n\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\n  for (var _iterator = _createForOfIteratorHelperLoose(availableFormats), _step; !(_step = _iterator()).done;) {\n    var format = _step.value;\n\n    // Validate leading digits.\n    // The test case for \"else path\" could be found by searching for\n    // \"format.leadingDigitsPatterns().length === 0\".\n    if (format.leadingDigitsPatterns().length > 0) {\n      // The last leading_digits_pattern is used here, as it is the most detailed\n      var lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]; // If leading digits don't match then move on to the next phone number format\n\n      if (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\n        continue;\n      }\n    } // Check that the national number matches the phone number format regular expression\n\n\n    if (matchesEntirely(nationalNnumber, format.pattern())) {\n      return format;\n    }\n  }\n}\n\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\n  return ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber;\n}\n\nfunction formatIDD(nationalNumber, carrierCode, countryCallingCode, fromCountry, metadata) {\n  var fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata); // When calling within the same country calling code.\n\n  if (fromCountryCallingCode === countryCallingCode) {\n    var formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata); // For NANPA regions, return the national format for these regions\n    // but prefix it with the country calling code.\n\n    if (countryCallingCode === '1') {\n      return countryCallingCode + ' ' + formattedNumber;\n    } // If regions share a country calling code, the country calling code need\n    // not be dialled. This also applies when dialling within a region, so this\n    // if clause covers both these cases. Technically this is the case for\n    // dialling from La Reunion to other overseas departments of France (French\n    // Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\n    // this edge case for now and for those cases return the version including\n    // country calling code. Details here:\n    // http://www.petitfute.com/voyage/225-info-pratiques-reunion\n    //\n\n\n    return formattedNumber;\n  }\n\n  var iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata);\n\n  if (iddPrefix) {\n    return \"\".concat(iddPrefix, \" \").concat(countryCallingCode, \" \").concat(formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata));\n  }\n}\n//# sourceMappingURL=format.js.map", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport Metadata, { validateMetadata } from './metadata.js';\nimport isPossibleNumber from './isPossible.js';\nimport isValidNumber from './isValid.js'; // import checkNumberLength from './helpers/checkNumberLength.js'\n\nimport getNumberType from './helpers/getNumberType.js';\nimport getPossibleCountriesForNumber from './helpers/getPossibleCountriesForNumber.js';\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport isObject from './helpers/isObject.js';\nimport formatNumber from './format.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\n\nvar PhoneNumber = /*#__PURE__*/function () {\n  /**\r\n   * @param  {string} countryOrCountryCallingCode\r\n   * @param  {string} nationalNumber\r\n   * @param  {object} metadata — Metadata JSON\r\n   * @return {PhoneNumber}\r\n   */\n  function PhoneNumber(countryOrCountryCallingCode, nationalNumber, metadata) {\n    _classCallCheck(this, PhoneNumber);\n\n    // Validate `countryOrCountryCallingCode` argument.\n    if (!countryOrCountryCallingCode) {\n      throw new TypeError('First argument is required');\n    }\n\n    if (typeof countryOrCountryCallingCode !== 'string') {\n      throw new TypeError('First argument must be a string');\n    } // In case of public API use: `constructor(number, metadata)`.\n    // Transform the arguments from `constructor(number, metadata)` to\n    // `constructor(countryOrCountryCallingCode, nationalNumber, metadata)`.\n\n\n    if (typeof countryOrCountryCallingCode === 'string') {\n      if (countryOrCountryCallingCode[0] === '+' && !nationalNumber) {\n        throw new TypeError('`metadata` argument not passed');\n      }\n\n      if (isObject(nationalNumber) && isObject(nationalNumber.countries)) {\n        metadata = nationalNumber;\n        var e164Number = countryOrCountryCallingCode;\n\n        if (!E164_NUMBER_REGEXP.test(e164Number)) {\n          throw new Error('Invalid `number` argument passed: must consist of a \"+\" followed by digits');\n        }\n\n        var _extractCountryCallin = extractCountryCallingCode(e164Number, undefined, undefined, metadata),\n            _countryCallingCode = _extractCountryCallin.countryCallingCode,\n            number = _extractCountryCallin.number;\n\n        nationalNumber = number;\n        countryOrCountryCallingCode = _countryCallingCode;\n\n        if (!nationalNumber) {\n          throw new Error('Invalid `number` argument passed: too short');\n        }\n      }\n    } // Validate `nationalNumber` argument.\n\n\n    if (!nationalNumber) {\n      throw new TypeError('`nationalNumber` argument is required');\n    }\n\n    if (typeof nationalNumber !== 'string') {\n      throw new TypeError('`nationalNumber` argument must be a string');\n    } // Validate `metadata` argument.\n\n\n    validateMetadata(metadata); // Initialize properties.\n\n    var _getCountryAndCountry = getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadata),\n        country = _getCountryAndCountry.country,\n        countryCallingCode = _getCountryAndCountry.countryCallingCode;\n\n    this.country = country;\n    this.countryCallingCode = countryCallingCode;\n    this.nationalNumber = nationalNumber;\n    this.number = '+' + this.countryCallingCode + this.nationalNumber; // Exclude `metadata` property output from `PhoneNumber.toString()`\n    // so that it doesn't clutter the console output of Node.js.\n    // Previously, when Node.js did `console.log(new PhoneNumber(...))`,\n    // it would output the whole internal structure of the `metadata` object.\n\n    this.getMetadata = function () {\n      return metadata;\n    };\n  }\n\n  _createClass(PhoneNumber, [{\n    key: \"setExt\",\n    value: function setExt(ext) {\n      this.ext = ext;\n    }\n  }, {\n    key: \"getPossibleCountries\",\n    value: function getPossibleCountries() {\n      if (this.country) {\n        return [this.country];\n      }\n\n      return getPossibleCountriesForNumber(this.countryCallingCode, this.nationalNumber, this.getMetadata());\n    }\n  }, {\n    key: \"isPossible\",\n    value: function isPossible() {\n      return isPossibleNumber(this, {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"isValid\",\n    value: function isValid() {\n      return isValidNumber(this, {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"isNonGeographic\",\n    value: function isNonGeographic() {\n      var metadata = new Metadata(this.getMetadata());\n      return metadata.isNonGeographicCallingCode(this.countryCallingCode);\n    }\n  }, {\n    key: \"isEqual\",\n    value: function isEqual(phoneNumber) {\n      return this.number === phoneNumber.number && this.ext === phoneNumber.ext;\n    } // This function was originally meant to be an equivalent for `validatePhoneNumberLength()`,\n    // but later it was found out that it doesn't include the possible `TOO_SHORT` result\n    // returned from `parsePhoneNumberWithError()` in the original `validatePhoneNumberLength()`,\n    // so eventually I simply commented out this method from the `PhoneNumber` class\n    // and just left the `validatePhoneNumberLength()` function, even though that one would require\n    // and additional step to also validate the actual country / calling code of the phone number.\n    // validateLength() {\n    // \tconst metadata = new Metadata(this.getMetadata())\n    // \tmetadata.selectNumberingPlan(this.countryCallingCode)\n    // \tconst result = checkNumberLength(this.nationalNumber, metadata)\n    // \tif (result !== 'IS_POSSIBLE') {\n    // \t\treturn result\n    // \t}\n    // }\n\n  }, {\n    key: \"getType\",\n    value: function getType() {\n      return getNumberType(this, {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"format\",\n    value: function format(_format, options) {\n      return formatNumber(this, _format, options ? _objectSpread(_objectSpread({}, options), {}, {\n        v2: true\n      }) : {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"formatNational\",\n    value: function formatNational(options) {\n      return this.format('NATIONAL', options);\n    }\n  }, {\n    key: \"formatInternational\",\n    value: function formatInternational(options) {\n      return this.format('INTERNATIONAL', options);\n    }\n  }, {\n    key: \"getURI\",\n    value: function getURI(options) {\n      return this.format('RFC3966', options);\n    }\n  }]);\n\n  return PhoneNumber;\n}();\n\nexport { PhoneNumber as default };\n\nvar isCountryCode = function isCountryCode(value) {\n  return /^[A-Z]{2}$/.test(value);\n};\n\nfunction getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadataJson) {\n  var country;\n  var countryCallingCode;\n  var metadata = new Metadata(metadataJson); // If country code is passed then derive `countryCallingCode` from it.\n  // Also store the country code as `.country`.\n\n  if (isCountryCode(countryOrCountryCallingCode)) {\n    country = countryOrCountryCallingCode;\n    metadata.selectNumberingPlan(country);\n    countryCallingCode = metadata.countryCallingCode();\n  } else {\n    countryCallingCode = countryOrCountryCallingCode;\n    /* istanbul ignore if */\n\n    if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n      if (metadata.isNonGeographicCallingCode(countryCallingCode)) {\n        country = '001';\n      }\n    }\n  }\n\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode\n  };\n}\n\nvar E164_NUMBER_REGEXP = /^\\+\\d+$/;\n//# sourceMappingURL=PhoneNumber.js.map", "// The minimum length of the national significant number.\nexport var MIN_LENGTH_FOR_NSN = 2; // The ITU says the maximum length should be 15,\n// but one can find longer numbers in Germany.\n\nexport var MAX_LENGTH_FOR_NSN = 17; // The maximum length of the country calling code.\n\nexport var MAX_LENGTH_COUNTRY_CODE = 3; // Digits accepted in phone numbers\n// (ascii, fullwidth, arabic-indic, and eastern arabic digits).\n\nexport var VALID_DIGITS = \"0-9\\uFF10-\\uFF19\\u0660-\\u0669\\u06F0-\\u06F9\"; // `DASHES` will be right after the opening square bracket of the \"character class\"\n\nvar DASHES = \"-\\u2010-\\u2015\\u2212\\u30FC\\uFF0D\";\nvar SLASHES = \"\\uFF0F/\";\nvar DOTS = \"\\uFF0E.\";\nexport var WHITESPACE = \" \\xA0\\xAD\\u200B\\u2060\\u3000\";\nvar BRACKETS = \"()\\uFF08\\uFF09\\uFF3B\\uFF3D\\\\[\\\\]\"; // export const OPENING_BRACKETS = '(\\uFF08\\uFF3B\\\\\\['\n\nvar TILDES = \"~\\u2053\\u223C\\uFF5E\"; // Regular expression of acceptable punctuation found in phone numbers. This\n// excludes punctuation found as a leading character only. This consists of dash\n// characters, white space characters, full stops, slashes, square brackets,\n// parentheses and tildes. Full-width variants are also present.\n\nexport var VALID_PUNCTUATION = \"\".concat(DASHES).concat(SLASHES).concat(DOTS).concat(WHITESPACE).concat(BRACKETS).concat(TILDES);\nexport var PLUS_CHARS = \"+\\uFF0B\"; // const LEADING_PLUS_CHARS_PATTERN = new RegExp('^[' + PLUS_CHARS + ']+')\n//# sourceMappingURL=constants.js.map", "function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n/**\r\n * Merges two arrays.\r\n * @param  {*} a\r\n * @param  {*} b\r\n * @return {*}\r\n */\nexport default function mergeArrays(a, b) {\n  var merged = a.slice();\n\n  for (var _iterator = _createForOfIteratorHelperLoose(b), _step; !(_step = _iterator()).done;) {\n    var element = _step.value;\n\n    if (a.indexOf(element) < 0) {\n      merged.push(element);\n    }\n  }\n\n  return merged.sort(function (a, b) {\n    return a - b;\n  }); // ES6 version, requires Set polyfill.\n  // let merged = new Set(a)\n  // for (const element of b) {\n  // \tmerged.add(i)\n  // }\n  // return Array.from(merged).sort((a, b) => a - b)\n}\n//# sourceMappingURL=mergeArrays.js.map", "import mergeArrays from './mergeArrays.js';\nexport default function checkNumberLength(nationalNumber, metadata) {\n  return checkNumberLengthForType(nationalNumber, undefined, metadata);\n} // Checks whether a number is possible for the country based on its length.\n// Should only be called for the \"new\" metadata which has \"possible lengths\".\n\nexport function checkNumberLengthForType(nationalNumber, type, metadata) {\n  var type_info = metadata.type(type); // There should always be \"<possiblePengths/>\" set for every type element.\n  // This is declared in the XML schema.\n  // For size efficiency, where a sub-description (e.g. fixed-line)\n  // has the same \"<possiblePengths/>\" as the \"general description\", this is missing,\n  // so we fall back to the \"general description\". Where no numbers of the type\n  // exist at all, there is one possible length (-1) which is guaranteed\n  // not to match the length of any real phone number.\n\n  var possible_lengths = type_info && type_info.possibleLengths() || metadata.possibleLengths(); // let local_lengths    = type_info && type.possibleLengthsLocal() || metadata.possibleLengthsLocal()\n  // Metadata before version `1.0.18` didn't contain `possible_lengths`.\n\n  if (!possible_lengths) {\n    return 'IS_POSSIBLE';\n  }\n\n  if (type === 'FIXED_LINE_OR_MOBILE') {\n    // No such country in metadata.\n\n    /* istanbul ignore next */\n    if (!metadata.type('FIXED_LINE')) {\n      // The rare case has been encountered where no fixedLine data is available\n      // (true for some non-geographic entities), so we just check mobile.\n      return checkNumberLengthForType(nationalNumber, 'MOBILE', metadata);\n    }\n\n    var mobile_type = metadata.type('MOBILE');\n\n    if (mobile_type) {\n      // Merge the mobile data in if there was any. \"Concat\" creates a new\n      // array, it doesn't edit possible_lengths in place, so we don't need a copy.\n      // Note that when adding the possible lengths from mobile, we have\n      // to again check they aren't empty since if they are this indicates\n      // they are the same as the general desc and should be obtained from there.\n      possible_lengths = mergeArrays(possible_lengths, mobile_type.possibleLengths()); // The current list is sorted; we need to merge in the new list and\n      // re-sort (duplicates are okay). Sorting isn't so expensive because\n      // the lists are very small.\n      // if (local_lengths) {\n      // \tlocal_lengths = mergeArrays(local_lengths, mobile_type.possibleLengthsLocal())\n      // } else {\n      // \tlocal_lengths = mobile_type.possibleLengthsLocal()\n      // }\n    }\n  } // If the type doesn't exist then return 'INVALID_LENGTH'.\n  else if (type && !type_info) {\n    return 'INVALID_LENGTH';\n  }\n\n  var actual_length = nationalNumber.length; // In `libphonenumber-js` all \"local-only\" formats are dropped for simplicity.\n  // // This is safe because there is never an overlap beween the possible lengths\n  // // and the local-only lengths; this is checked at build time.\n  // if (local_lengths && local_lengths.indexOf(nationalNumber.length) >= 0)\n  // {\n  // \treturn 'IS_POSSIBLE_LOCAL_ONLY'\n  // }\n\n  var minimum_length = possible_lengths[0];\n\n  if (minimum_length === actual_length) {\n    return 'IS_POSSIBLE';\n  }\n\n  if (minimum_length > actual_length) {\n    return 'TOO_SHORT';\n  }\n\n  if (possible_lengths[possible_lengths.length - 1] < actual_length) {\n    return 'TOO_LONG';\n  } // We skip the first element since we've already checked it.\n\n\n  return possible_lengths.indexOf(actual_length, 1) >= 0 ? 'IS_POSSIBLE' : 'INVALID_LENGTH';\n}\n//# sourceMappingURL=checkNumberLength.js.map", "import { VALID_DIGITS } from '../../constants.js'; // The RFC 3966 format for extensions.\n\nvar RFC3966_EXTN_PREFIX = ';ext=';\n/**\r\n * Helper method for constructing regular expressions for parsing. Creates\r\n * an expression that captures up to max_length digits.\r\n * @return {string} RegEx pattern to capture extension digits.\r\n */\n\nvar getExtensionDigitsPattern = function getExtensionDigitsPattern(maxLength) {\n  return \"([\".concat(VALID_DIGITS, \"]{1,\").concat(maxLength, \"})\");\n};\n/**\r\n * Helper initialiser method to create the regular-expression pattern to match\r\n * extensions.\r\n * Copy-pasted from Google's `libphonenumber`:\r\n * https://github.com/google/libphonenumber/blob/55b2646ec9393f4d3d6661b9c82ef9e258e8b829/javascript/i18n/phonenumbers/phonenumberutil.js#L759-L766\r\n * @return {string} RegEx pattern to capture extensions.\r\n */\n\n\nexport default function createExtensionPattern(purpose) {\n  // We cap the maximum length of an extension based on the ambiguity of the way\n  // the extension is prefixed. As per ITU, the officially allowed length for\n  // extensions is actually 40, but we don't support this since we haven't seen real\n  // examples and this introduces many false interpretations as the extension labels\n  // are not standardized.\n\n  /** @type {string} */\n  var extLimitAfterExplicitLabel = '20';\n  /** @type {string} */\n\n  var extLimitAfterLikelyLabel = '15';\n  /** @type {string} */\n\n  var extLimitAfterAmbiguousChar = '9';\n  /** @type {string} */\n\n  var extLimitWhenNotSure = '6';\n  /** @type {string} */\n\n  var possibleSeparatorsBetweenNumberAndExtLabel = \"[ \\xA0\\\\t,]*\"; // Optional full stop (.) or colon, followed by zero or more spaces/tabs/commas.\n\n  /** @type {string} */\n\n  var possibleCharsAfterExtLabel = \"[:\\\\.\\uFF0E]?[ \\xA0\\\\t,-]*\";\n  /** @type {string} */\n\n  var optionalExtnSuffix = \"#?\"; // Here the extension is called out in more explicit way, i.e mentioning it obvious\n  // patterns like \"ext.\".\n\n  /** @type {string} */\n\n  var explicitExtLabels = \"(?:e?xt(?:ensi(?:o\\u0301?|\\xF3))?n?|\\uFF45?\\uFF58\\uFF54\\uFF4E?|\\u0434\\u043E\\u0431|anexo)\"; // One-character symbols that can be used to indicate an extension, and less\n  // commonly used or more ambiguous extension labels.\n\n  /** @type {string} */\n\n  var ambiguousExtLabels = \"(?:[x\\uFF58#\\uFF03~\\uFF5E]|int|\\uFF49\\uFF4E\\uFF54)\"; // When extension is not separated clearly.\n\n  /** @type {string} */\n\n  var ambiguousSeparator = \"[- ]+\"; // This is the same as possibleSeparatorsBetweenNumberAndExtLabel, but not matching\n  // comma as extension label may have it.\n\n  /** @type {string} */\n\n  var possibleSeparatorsNumberExtLabelNoComma = \"[ \\xA0\\\\t]*\"; // \",,\" is commonly used for auto dialling the extension when connected. First\n  // comma is matched through possibleSeparatorsBetweenNumberAndExtLabel, so we do\n  // not repeat it here. Semi-colon works in Iphone and Android also to pop up a\n  // button with the extension number following.\n\n  /** @type {string} */\n\n  var autoDiallingAndExtLabelsFound = \"(?:,{2}|;)\";\n  /** @type {string} */\n\n  var rfcExtn = RFC3966_EXTN_PREFIX + getExtensionDigitsPattern(extLimitAfterExplicitLabel);\n  /** @type {string} */\n\n  var explicitExtn = possibleSeparatorsBetweenNumberAndExtLabel + explicitExtLabels + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterExplicitLabel) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var ambiguousExtn = possibleSeparatorsBetweenNumberAndExtLabel + ambiguousExtLabels + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterAmbiguousChar) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var americanStyleExtnWithSuffix = ambiguousSeparator + getExtensionDigitsPattern(extLimitWhenNotSure) + \"#\";\n  /** @type {string} */\n\n  var autoDiallingExtn = possibleSeparatorsNumberExtLabelNoComma + autoDiallingAndExtLabelsFound + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterLikelyLabel) + optionalExtnSuffix;\n  /** @type {string} */\n\n  var onlyCommasExtn = possibleSeparatorsNumberExtLabelNoComma + \"(?:,)+\" + possibleCharsAfterExtLabel + getExtensionDigitsPattern(extLimitAfterAmbiguousChar) + optionalExtnSuffix; // The first regular expression covers RFC 3966 format, where the extension is added\n  // using \";ext=\". The second more generic where extension is mentioned with explicit\n  // labels like \"ext:\". In both the above cases we allow more numbers in extension than\n  // any other extension labels. The third one captures when single character extension\n  // labels or less commonly used labels are used. In such cases we capture fewer\n  // extension digits in order to reduce the chance of falsely interpreting two\n  // numbers beside each other as a number + extension. The fourth one covers the\n  // special case of American numbers where the extension is written with a hash\n  // at the end, such as \"- 503#\". The fifth one is exclusively for extension\n  // autodialling formats which are used when dialling and in this case we accept longer\n  // extensions. The last one is more liberal on the number of commas that acts as\n  // extension labels, so we have a strict cap on the number of digits in such extensions.\n\n  return rfcExtn + \"|\" + explicitExtn + \"|\" + ambiguousExtn + \"|\" + americanStyleExtnWithSuffix + \"|\" + autoDiallingExtn + \"|\" + onlyCommasExtn;\n}\n//# sourceMappingURL=createExtensionPattern.js.map", "import Metadata from '../metadata.js';\nimport { VALID_DIGITS } from '../constants.js';\nvar CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])');\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\n  if (!country) {\n    return;\n  } // Check if the number is IDD-prefixed.\n\n\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n  var IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix());\n\n  if (number.search(IDDPrefixPattern) !== 0) {\n    return;\n  } // Strip IDD prefix.\n\n\n  number = number.slice(number.match(IDDPrefixPattern)[0].length); // If there're any digits after an IDD prefix,\n  // then those digits are a country calling code.\n  // Since no country code starts with a `0`,\n  // the code below validates that the next digit (if present) is not `0`.\n\n  var matchedGroups = number.match(CAPTURING_DIGIT_PATTERN);\n\n  if (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\n    if (matchedGroups[1] === '0') {\n      return;\n    }\n  }\n\n  return number;\n}\n//# sourceMappingURL=stripIddPrefix.js.map", "import Metadata from '../metadata.js';\nimport matchesEntirely from './matchesEntirely.js';\nimport extractNationalNumber from './extractNationalNumber.js';\nimport checkNumberLength from './checkNumberLength.js';\nimport getCountryCallingCode from '../getCountryCallingCode.js';\n/**\r\n * Sometimes some people incorrectly input international phone numbers\r\n * without the leading `+`. This function corrects such input.\r\n * @param  {string} number — Phone number digits.\r\n * @param  {string?} country\r\n * @param  {string?} callingCode\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCode: string?, number: string }`.\r\n */\n\nexport default function extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(number, country, callingCode, metadata) {\n  var countryCallingCode = country ? getCountryCallingCode(country, metadata) : callingCode;\n\n  if (number.indexOf(countryCallingCode) === 0) {\n    metadata = new Metadata(metadata);\n    metadata.selectNumberingPlan(country, callingCode);\n    var possibleShorterNumber = number.slice(countryCallingCode.length);\n\n    var _extractNationalNumbe = extractNationalNumber(possibleShorterNumber, metadata),\n        possibleShorterNationalNumber = _extractNationalNumbe.nationalNumber;\n\n    var _extractNationalNumbe2 = extractNationalNumber(number, metadata),\n        nationalNumber = _extractNationalNumbe2.nationalNumber; // If the number was not valid before but is valid now,\n    // or if it was too long before, we consider the number\n    // with the country calling code stripped to be a better result\n    // and keep that instead.\n    // For example, in Germany (+49), `49` is a valid area code,\n    // so if a number starts with `49`, it could be both a valid\n    // national German number or an international number without\n    // a leading `+`.\n\n\n    if (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) && matchesEntirely(possibleShorterNationalNumber, metadata.nationalNumberPattern()) || checkNumberLength(nationalNumber, metadata) === 'TOO_LONG') {\n      return {\n        countryCallingCode: countryCallingCode,\n        number: possibleShorterNumber\n      };\n    }\n  }\n\n  return {\n    number: number\n  };\n}\n//# sourceMappingURL=extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js.map", "import stripIddPrefix from './stripIddPrefix.js';\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js';\nimport Metadata from '../metadata.js';\nimport { MAX_LENGTH_COUNTRY_CODE } from '../constants.js';\n/**\r\n * Converts a phone number digits (possibly with a `+`)\r\n * into a calling code and the rest phone number digits.\r\n * The \"rest phone number digits\" could include\r\n * a national prefix, carrier code, and national\r\n * (significant) number.\r\n * @param  {string} number — Phone number digits (possibly with a `+`).\r\n * @param  {string} [country] — Default country.\r\n * @param  {string} [callingCode] — Default calling code (some phone numbering plans are non-geographic).\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCodeSource: string?, countryCallingCode: string?, number: string }`\r\n * @example\r\n * // Returns `{ countryCallingCode: \"1\", number: \"2133734253\" }`.\r\n * extractCountryCallingCode('2133734253', 'US', null, metadata)\r\n * extractCountryCallingCode('2133734253', null, '1', metadata)\r\n * extractCountryCallingCode('+12133734253', null, null, metadata)\r\n * extractCountryCallingCode('+12133734253', 'RU', null, metadata)\r\n */\n\nexport default function extractCountryCallingCode(number, country, callingCode, metadata) {\n  if (!number) {\n    return {};\n  }\n\n  var isNumberWithIddPrefix; // If this is not an international phone number,\n  // then either extract an \"IDD\" prefix, or extract a\n  // country calling code from a number by autocorrecting it\n  // by prepending a leading `+` in cases when it starts\n  // with the country calling code.\n  // https://wikitravel.org/en/International_dialling_prefix\n  // https://github.com/catamphetamine/libphonenumber-js/issues/376\n\n  if (number[0] !== '+') {\n    // Convert an \"out-of-country\" dialing phone number\n    // to a proper international phone number.\n    var numberWithoutIDD = stripIddPrefix(number, country, callingCode, metadata); // If an IDD prefix was stripped then\n    // convert the number to international one\n    // for subsequent parsing.\n\n    if (numberWithoutIDD && numberWithoutIDD !== number) {\n      isNumberWithIddPrefix = true;\n      number = '+' + numberWithoutIDD;\n    } else {\n      // Check to see if the number starts with the country calling code\n      // for the default country. If so, we remove the country calling code,\n      // and do some checks on the validity of the number before and after.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/376\n      if (country || callingCode) {\n        var _extractCountryCallin = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(number, country, callingCode, metadata),\n            countryCallingCode = _extractCountryCallin.countryCallingCode,\n            shorterNumber = _extractCountryCallin.number;\n\n        if (countryCallingCode) {\n          return {\n            countryCallingCodeSource: 'FROM_NUMBER_WITHOUT_PLUS_SIGN',\n            countryCallingCode: countryCallingCode,\n            number: shorterNumber\n          };\n        }\n      }\n\n      return {\n        // No need to set it to `UNSPECIFIED`. It can be just `undefined`.\n        // countryCallingCodeSource: 'UNSPECIFIED',\n        number: number\n      };\n    }\n  } // Fast abortion: country codes do not begin with a '0'\n\n\n  if (number[1] === '0') {\n    return {};\n  }\n\n  metadata = new Metadata(metadata); // The thing with country phone codes\n  // is that they are orthogonal to each other\n  // i.e. there's no such country phone code A\n  // for which country phone code B exists\n  // where B starts with A.\n  // Therefore, while scanning digits,\n  // if a valid country code is found,\n  // that means that it is the country code.\n  //\n\n  var i = 2;\n\n  while (i - 1 <= MAX_LENGTH_COUNTRY_CODE && i <= number.length) {\n    var _countryCallingCode = number.slice(1, i);\n\n    if (metadata.hasCallingCode(_countryCallingCode)) {\n      metadata.selectNumberingPlan(_countryCallingCode);\n      return {\n        countryCallingCodeSource: isNumberWithIddPrefix ? 'FROM_NUMBER_WITH_IDD' : 'FROM_NUMBER_WITH_PLUS_SIGN',\n        countryCallingCode: _countryCallingCode,\n        number: number.slice(i)\n      };\n    }\n\n    i++;\n  }\n\n  return {};\n} // The possible values for the returned `countryCallingCodeSource` are:\n//\n// Copy-pasted from:\n// https://github.com/google/libphonenumber/blob/master/resources/phonenumber.proto\n//\n// // The source from which the country_code is derived. This is not set in the\n// // general parsing method, but in the method that parses and keeps raw_input.\n// // New fields could be added upon request.\n// enum CountryCodeSource {\n//  // Default value returned if this is not set, because the phone number was\n//  // created using parse, not parseAndKeepRawInput. hasCountryCodeSource will\n//  // return false if this is the case.\n//  UNSPECIFIED = 0;\n//\n//  // The country_code is derived based on a phone number with a leading \"+\",\n//  // e.g. the French number \"+33 1 42 68 53 00\".\n//  FROM_NUMBER_WITH_PLUS_SIGN = 1;\n//\n//  // The country_code is derived based on a phone number with a leading IDD,\n//  // e.g. the French number \"011 33 1 42 68 53 00\", as it is dialled from US.\n//  FROM_NUMBER_WITH_IDD = 5;\n//\n//  // The country_code is derived based on a phone number without a leading\n//  // \"+\", e.g. the French number \"33 1 42 68 53 00\" when defaultCountry is\n//  // supplied as France.\n//  FROM_NUMBER_WITHOUT_PLUS_SIGN = 10;\n//\n//  // The country_code is derived NOT based on the phone number itself, but\n//  // from the defaultCountry parameter provided in the parsing function by the\n//  // clients. This happens mostly for numbers written in the national format\n//  // (without country code). For example, this would be set when parsing the\n//  // French number \"01 42 68 53 00\", when defaultCountry is supplied as\n//  // France.\n//  FROM_DEFAULT_COUNTRY = 20;\n// }\n//# sourceMappingURL=extractCountryCallingCode.js.map", "/**\r\n * Strips any national prefix (such as 0, 1) present in a\r\n * (possibly incomplete) number provided.\r\n * \"Carrier codes\" are only used  in Colombia and Brazil,\r\n * and only when dialing within those countries from a mobile phone to a fixed line number.\r\n * Sometimes it won't actually strip national prefix\r\n * and will instead prepend some digits to the `number`:\r\n * for example, when number `2345678` is passed with `VI` country selected,\r\n * it will return `{ number: \"3402345678\" }`, because `340` area code is prepended.\r\n * @param {string} number — National number digits.\r\n * @param {object} metadata — Metadata with country selected.\r\n * @return {object} `{ nationalNumber: string, nationalPrefix: string? carrierCode: string? }`. Even if a national prefix was extracted, it's not necessarily present in the returned object, so don't rely on its presence in the returned object in order to find out whether a national prefix has been extracted or not.\r\n */\nexport default function extractNationalNumberFromPossiblyIncompleteNumber(number, metadata) {\n  if (number && metadata.numberingPlan.nationalPrefixForParsing()) {\n    // See METADATA.md for the description of\n    // `national_prefix_for_parsing` and `national_prefix_transform_rule`.\n    // Attempt to parse the first digits as a national prefix.\n    var prefixPattern = new RegExp('^(?:' + metadata.numberingPlan.nationalPrefixForParsing() + ')');\n    var prefixMatch = prefixPattern.exec(number);\n\n    if (prefixMatch) {\n      var nationalNumber;\n      var carrierCode; // https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\n      // If a `national_prefix_for_parsing` has any \"capturing groups\"\n      // then it means that the national (significant) number is equal to\n      // those \"capturing groups\" transformed via `national_prefix_transform_rule`,\n      // and nothing could be said about the actual national prefix:\n      // what is it and was it even there.\n      // If a `national_prefix_for_parsing` doesn't have any \"capturing groups\",\n      // then everything it matches is a national prefix.\n      // To determine whether `national_prefix_for_parsing` matched any\n      // \"capturing groups\", the value of the result of calling `.exec()`\n      // is looked at, and if it has non-undefined values where there're\n      // \"capturing groups\" in the regular expression, then it means\n      // that \"capturing groups\" have been matched.\n      // It's not possible to tell whether there'll be any \"capturing gropus\"\n      // before the matching process, because a `national_prefix_for_parsing`\n      // could exhibit both behaviors.\n\n      var capturedGroupsCount = prefixMatch.length - 1;\n      var hasCapturedGroups = capturedGroupsCount > 0 && prefixMatch[capturedGroupsCount];\n\n      if (metadata.nationalPrefixTransformRule() && hasCapturedGroups) {\n        nationalNumber = number.replace(prefixPattern, metadata.nationalPrefixTransformRule()); // If there's more than one captured group,\n        // then carrier code is the second one.\n\n        if (capturedGroupsCount > 1) {\n          carrierCode = prefixMatch[1];\n        }\n      } // If there're no \"capturing groups\",\n      // or if there're \"capturing groups\" but no\n      // `national_prefix_transform_rule`,\n      // then just strip the national prefix from the number,\n      // and possibly a carrier code.\n      // Seems like there could be more.\n      else {\n        // `prefixBeforeNationalNumber` is the whole substring matched by\n        // the `national_prefix_for_parsing` regular expression.\n        // There seem to be no guarantees that it's just a national prefix.\n        // For example, if there's a carrier code, it's gonna be a\n        // part of `prefixBeforeNationalNumber` too.\n        var prefixBeforeNationalNumber = prefixMatch[0];\n        nationalNumber = number.slice(prefixBeforeNationalNumber.length); // If there's at least one captured group,\n        // then carrier code is the first one.\n\n        if (hasCapturedGroups) {\n          carrierCode = prefixMatch[1];\n        }\n      } // Tries to guess whether a national prefix was present in the input.\n      // This is not something copy-pasted from Google's library:\n      // they don't seem to have an equivalent for that.\n      // So this isn't an \"officially approved\" way of doing something like that.\n      // But since there seems no other existing method, this library uses it.\n\n\n      var nationalPrefix;\n\n      if (hasCapturedGroups) {\n        var possiblePositionOfTheFirstCapturedGroup = number.indexOf(prefixMatch[1]);\n        var possibleNationalPrefix = number.slice(0, possiblePositionOfTheFirstCapturedGroup); // Example: an Argentinian (AR) phone number `0111523456789`.\n        // `prefixMatch[0]` is `01115`, and `$1` is `11`,\n        // and the rest of the phone number is `23456789`.\n        // The national number is transformed via `9$1` to `91123456789`.\n        // National prefix `0` is detected being present at the start.\n        // if (possibleNationalPrefix.indexOf(metadata.numberingPlan.nationalPrefix()) === 0) {\n\n        if (possibleNationalPrefix === metadata.numberingPlan.nationalPrefix()) {\n          nationalPrefix = metadata.numberingPlan.nationalPrefix();\n        }\n      } else {\n        nationalPrefix = prefixMatch[0];\n      }\n\n      return {\n        nationalNumber: nationalNumber,\n        nationalPrefix: nationalPrefix,\n        carrierCode: carrierCode\n      };\n    }\n  }\n\n  return {\n    nationalNumber: number\n  };\n}\n//# sourceMappingURL=extractNationalNumberFromPossiblyIncompleteNumber.js.map", "import extractNationalNumberFromPossiblyIncompleteNumber from './extractNationalNumberFromPossiblyIncompleteNumber.js';\nimport matchesEntirely from './matchesEntirely.js';\nimport checkNumberLength from './checkNumberLength.js';\n/**\r\n * Strips national prefix and carrier code from a complete phone number.\r\n * The difference from the non-\"FromCompleteNumber\" function is that\r\n * it won't extract national prefix if the resultant number is too short\r\n * to be a complete number for the selected phone numbering plan.\r\n * @param  {string} number — Complete phone number digits.\r\n * @param  {Metadata} metadata — Metadata with a phone numbering plan selected.\r\n * @return {object} `{ nationalNumber: string, carrierCode: string? }`.\r\n */\n\nexport default function extractNationalNumber(number, metadata) {\n  // Parsing national prefixes and carrier codes\n  // is only required for local phone numbers\n  // but some people don't understand that\n  // and sometimes write international phone numbers\n  // with national prefixes (or maybe even carrier codes).\n  // http://ucken.blogspot.ru/2016/03/trunk-prefixes-in-skype4b.html\n  // Google's original library forgives such mistakes\n  // and so does this library, because it has been requested:\n  // https://github.com/catamphetamine/libphonenumber-js/issues/127\n  var _extractNationalNumbe = extractNationalNumberFromPossiblyIncompleteNumber(number, metadata),\n      carrierCode = _extractNationalNumbe.carrierCode,\n      nationalNumber = _extractNationalNumbe.nationalNumber;\n\n  if (nationalNumber !== number) {\n    if (!shouldHaveExtractedNationalPrefix(number, nationalNumber, metadata)) {\n      // Don't strip the national prefix.\n      return {\n        nationalNumber: number\n      };\n    } // Check the national (significant) number length after extracting national prefix and carrier code.\n    // Legacy generated metadata (before `1.0.18`) didn't support the \"possible lengths\" feature.\n\n\n    if (metadata.possibleLengths()) {\n      // The number remaining after stripping the national prefix and carrier code\n      // should be long enough to have a possible length for the country.\n      // Otherwise, don't strip the national prefix and carrier code,\n      // since the original number could be a valid number.\n      // This check has been copy-pasted \"as is\" from Google's original library:\n      // https://github.com/google/libphonenumber/blob/876268eb1ad6cdc1b7b5bef17fc5e43052702d57/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L3236-L3250\n      // It doesn't check for the \"possibility\" of the original `number`.\n      // I guess it's fine not checking that one. It works as is anyway.\n      if (!isPossibleIncompleteNationalNumber(nationalNumber, metadata)) {\n        // Don't strip the national prefix.\n        return {\n          nationalNumber: number\n        };\n      }\n    }\n  }\n\n  return {\n    nationalNumber: nationalNumber,\n    carrierCode: carrierCode\n  };\n} // In some countries, the same digit could be a national prefix\n// or a leading digit of a valid phone number.\n// For example, in Russia, national prefix is `8`,\n// and also `800 555 35 35` is a valid number\n// in which `8` is not a national prefix, but the first digit\n// of a national (significant) number.\n// Same's with Belarus:\n// `82004910060` is a valid national (significant) number,\n// but `2004910060` is not.\n// To support such cases (to prevent the code from always stripping\n// national prefix), a condition is imposed: a national prefix\n// is not extracted when the original number is \"viable\" and the\n// resultant number is not, a \"viable\" national number being the one\n// that matches `national_number_pattern`.\n\nfunction shouldHaveExtractedNationalPrefix(nationalNumberBefore, nationalNumberAfter, metadata) {\n  // The equivalent in Google's code is:\n  // https://github.com/google/libphonenumber/blob/e326fa1fc4283bb05eb35cb3c15c18f98a31af33/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L2969-L3004\n  if (matchesEntirely(nationalNumberBefore, metadata.nationalNumberPattern()) && !matchesEntirely(nationalNumberAfter, metadata.nationalNumberPattern())) {\n    return false;\n  } // This \"is possible\" national number (length) check has been commented out\n  // because it's superceded by the (effectively) same check done in the\n  // `extractNationalNumber()` function after it calls `shouldHaveExtractedNationalPrefix()`.\n  // In other words, why run the same check twice if it could only be run once.\n  // // Check the national (significant) number length after extracting national prefix and carrier code.\n  // // Fixes a minor \"weird behavior\" bug: https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/57\n  // // (Legacy generated metadata (before `1.0.18`) didn't support the \"possible lengths\" feature).\n  // if (metadata.possibleLengths()) {\n  // \tif (isPossibleIncompleteNationalNumber(nationalNumberBefore, metadata) &&\n  // \t\t!isPossibleIncompleteNationalNumber(nationalNumberAfter, metadata)) {\n  // \t\treturn false\n  // \t}\n  // }\n\n\n  return true;\n}\n\nfunction isPossibleIncompleteNationalNumber(nationalNumber, metadata) {\n  switch (checkNumberLength(nationalNumber, metadata)) {\n    case 'TOO_SHORT':\n    case 'INVALID_LENGTH':\n      // This library ignores \"local-only\" phone numbers (for simplicity).\n      // See the readme for more info on what are \"local-only\" phone numbers.\n      // case 'IS_POSSIBLE_LOCAL_ONLY':\n      return false;\n\n    default:\n      return true;\n  }\n}\n//# sourceMappingURL=extractNationalNumber.js.map", "function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport Metadata from '../metadata.js';\nimport matchesEntirely from './matchesEntirely.js';\nvar NON_FIXED_LINE_PHONE_TYPES = ['MOBILE', 'PREMIUM_RATE', 'TOLL_FREE', 'SHARED_COST', 'VOIP', 'PERSONAL_NUMBER', 'PAGER', 'UAN', 'VOICEMAIL']; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function getNumberType(input, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {}; // When `parse()` returns an empty object — `{}` —\n  // that means that the phone number is malformed,\n  // so it can't possibly be valid.\n\n  if (!input.country && !input.countryCallingCode) {\n    return;\n  }\n\n  metadata = new Metadata(metadata);\n  metadata.selectNumberingPlan(input.country, input.countryCallingCode);\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone; // The following is copy-pasted from the original function:\n  // https://github.com/googlei18n/libphonenumber/blob/3ea547d4fbaa2d0b67588904dfa5d3f2557c27ff/javascript/i18n/phonenumbers/phonenumberutil.js#L2835\n  // Is this national number even valid for this country\n\n  if (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern())) {\n    return;\n  } // Is it fixed line number\n\n\n  if (isNumberTypeEqualTo(nationalNumber, 'FIXED_LINE', metadata)) {\n    // Because duplicate regular expressions are removed\n    // to reduce metadata size, if \"mobile\" pattern is \"\"\n    // then it means it was removed due to being a duplicate of the fixed-line pattern.\n    //\n    if (metadata.type('MOBILE') && metadata.type('MOBILE').pattern() === '') {\n      return 'FIXED_LINE_OR_MOBILE';\n    } // `MOBILE` type pattern isn't included if it matched `FIXED_LINE` one.\n    // For example, for \"US\" country.\n    // Old metadata (< `1.0.18`) had a specific \"types\" data structure\n    // that happened to be `undefined` for `MOBILE` in that case.\n    // Newer metadata (>= `1.0.18`) has another data structure that is\n    // not `undefined` for `MOBILE` in that case (it's just an empty array).\n    // So this `if` is just for backwards compatibility with old metadata.\n\n\n    if (!metadata.type('MOBILE')) {\n      return 'FIXED_LINE_OR_MOBILE';\n    } // Check if the number happens to qualify as both fixed line and mobile.\n    // (no such country in the minimal metadata set)\n\n    /* istanbul ignore if */\n\n\n    if (isNumberTypeEqualTo(nationalNumber, 'MOBILE', metadata)) {\n      return 'FIXED_LINE_OR_MOBILE';\n    }\n\n    return 'FIXED_LINE';\n  }\n\n  for (var _iterator = _createForOfIteratorHelperLoose(NON_FIXED_LINE_PHONE_TYPES), _step; !(_step = _iterator()).done;) {\n    var type = _step.value;\n\n    if (isNumberTypeEqualTo(nationalNumber, type, metadata)) {\n      return type;\n    }\n  }\n}\nexport function isNumberTypeEqualTo(nationalNumber, type, metadata) {\n  type = metadata.type(type);\n\n  if (!type || !type.pattern()) {\n    return false;\n  } // Check if any possible number lengths are present;\n  // if so, we use them to avoid checking\n  // the validation pattern if they don't match.\n  // If they are absent, this means they match\n  // the general description, which we have\n  // already checked before a specific number type.\n\n\n  if (type.possibleLengths() && type.possibleLengths().indexOf(nationalNumber.length) < 0) {\n    return false;\n  }\n\n  return matchesEntirely(nationalNumber, type.pattern());\n}\n//# sourceMappingURL=getNumberType.js.map", "var objectConstructor = {}.constructor;\nexport default function isObject(object) {\n  return object !== undefined && object !== null && object.constructor === objectConstructor;\n}\n//# sourceMappingURL=isObject.js.map", "import { MIN_LENGTH_FOR_NSN, VALID_DIGITS, VALID_PUNCTUATION, PLUS_CHARS } from '../constants.js';\nimport createExtensionPattern from './extension/createExtensionPattern.js'; //  Regular expression of viable phone numbers. This is location independent.\n//  Checks we have at least three leading digits, and only valid punctuation,\n//  alpha characters and digits in the phone number. Does not include extension\n//  data. The symbol 'x' is allowed here as valid punctuation since it is often\n//  used as a placeholder for carrier codes, for example in Brazilian phone\n//  numbers. We also allow multiple '+' characters at the start.\n//\n//  Corresponds to the following:\n//  [digits]{minLengthNsn}|\n//  plus_sign*\n//  (([punctuation]|[star])*[digits]){3,}([punctuation]|[star]|[digits]|[alpha])*\n//\n//  The first reg-ex is to allow short numbers (two digits long) to be parsed if\n//  they are entered as \"15\" etc, but only if there is no punctuation in them.\n//  The second expression restricts the number of digits to three or more, but\n//  then allows them to be in international form, and to have alpha-characters\n//  and punctuation. We split up the two reg-exes here and combine them when\n//  creating the reg-ex VALID_PHONE_NUMBER_PATTERN itself so we can prefix it\n//  with ^ and append $ to each branch.\n//\n//  \"Note VALID_PUNCTUATION starts with a -,\n//   so must be the first in the range\" (c) Google devs.\n//  (wtf did they mean by saying that; probably nothing)\n//\n\nvar MIN_LENGTH_PHONE_NUMBER_PATTERN = '[' + VALID_DIGITS + ']{' + MIN_LENGTH_FOR_NSN + '}'; //\n// And this is the second reg-exp:\n// (see MIN_LENGTH_PHONE_NUMBER_PATTERN for a full description of this reg-exp)\n//\n\nexport var VALID_PHONE_NUMBER = '[' + PLUS_CHARS + ']{0,1}' + '(?:' + '[' + VALID_PUNCTUATION + ']*' + '[' + VALID_DIGITS + ']' + '){3,}' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']*'; // This regular expression isn't present in Google's `libphonenumber`\n// and is only used to determine whether the phone number being input\n// is too short for it to even consider it a \"valid\" number.\n// This is just a way to differentiate between a really invalid phone\n// number like \"abcde\" and a valid phone number that a user has just\n// started inputting, like \"+1\" or \"1\": both these cases would be\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\n// library can provide a more detailed error message — whether it's\n// really \"not a number\", or is it just a start of a valid phone number.\n\nvar VALID_PHONE_NUMBER_START_REG_EXP = new RegExp('^' + '[' + PLUS_CHARS + ']{0,1}' + '(?:' + '[' + VALID_PUNCTUATION + ']*' + '[' + VALID_DIGITS + ']' + '){1,2}' + '$', 'i');\nexport var VALID_PHONE_NUMBER_WITH_EXTENSION = VALID_PHONE_NUMBER + // Phone number extensions\n'(?:' + createExtensionPattern() + ')?'; // The combined regular expression for valid phone numbers:\n//\n\nvar VALID_PHONE_NUMBER_PATTERN = new RegExp( // Either a short two-digit-only phone number\n'^' + MIN_LENGTH_PHONE_NUMBER_PATTERN + '$' + '|' + // Or a longer fully parsed phone number (min 3 characters)\n'^' + VALID_PHONE_NUMBER_WITH_EXTENSION + '$', 'i'); // Checks to see if the string of characters could possibly be a phone number at\n// all. At the moment, checks to see that the string begins with at least 2\n// digits, ignoring any punctuation commonly found in phone numbers. This method\n// does not require the number to be normalized in advance - but does assume\n// that leading non-number symbols have been removed, such as by the method\n// `extract_possible_number`.\n//\n\nexport default function isViablePhoneNumber(number) {\n  return number.length >= MIN_LENGTH_FOR_NSN && VALID_PHONE_NUMBER_PATTERN.test(number);\n} // This is just a way to differentiate between a really invalid phone\n// number like \"abcde\" and a valid phone number that a user has just\n// started inputting, like \"+1\" or \"1\": both these cases would be\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\n// library can provide a more detailed error message — whether it's\n// really \"not a number\", or is it just a start of a valid phone number.\n\nexport function isViablePhoneNumberStart(number) {\n  return VALID_PHONE_NUMBER_START_REG_EXP.test(number);\n}\n//# sourceMappingURL=isViablePhoneNumber.js.map", "/**\r\n * Checks whether the entire input sequence can be matched\r\n * against the regular expression.\r\n * @return {boolean}\r\n */\nexport default function matchesEntirely(text, regular_expression) {\n  // If assigning the `''` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  text = text || '';\n  return new RegExp('^(?:' + regular_expression + ')$').test(text);\n}\n//# sourceMappingURL=matchesEntirely.js.map", "import Metadata from './metadata.js';\nimport checkNumberLength from './helpers/checkNumberLength.js';\n/**\r\n * Checks if a phone number is \"possible\" (basically just checks its length).\r\n *\r\n * isPossible(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\n\nexport default function isPossiblePhoneNumber(input, options, metadata) {\n  /* istanbul ignore if */\n  if (options === undefined) {\n    options = {};\n  }\n\n  metadata = new Metadata(metadata);\n\n  if (options.v2) {\n    if (!input.countryCallingCode) {\n      throw new Error('Invalid phone number object passed');\n    }\n\n    metadata.selectNumberingPlan(input.countryCallingCode);\n  } else {\n    if (!input.phone) {\n      return false;\n    }\n\n    if (input.country) {\n      if (!metadata.hasCountry(input.country)) {\n        throw new Error(\"Unknown country: \".concat(input.country));\n      }\n\n      metadata.country(input.country);\n    } else {\n      if (!input.countryCallingCode) {\n        throw new Error('Invalid phone number object passed');\n      }\n\n      metadata.selectNumberingPlan(input.countryCallingCode);\n    }\n  } // Old metadata (< 1.0.18) had no \"possible length\" data.\n\n\n  if (metadata.possibleLengths()) {\n    return isPossibleNumber(input.phone || input.nationalNumber, metadata);\n  } else {\n    // There was a bug between `1.7.35` and `1.7.37` where \"possible_lengths\"\n    // were missing for \"non-geographical\" numbering plans.\n    // Just assume the number is possible in such cases:\n    // it's unlikely that anyone generated their custom metadata\n    // in that short period of time (one day).\n    // This code can be removed in some future major version update.\n    if (input.countryCallingCode && metadata.isNonGeographicCallingCode(input.countryCallingCode)) {\n      // \"Non-geographic entities\" did't have `possibleLengths`\n      // due to a bug in metadata generation process.\n      return true;\n    } else {\n      throw new Error('Missing \"possibleLengths\" in metadata. Perhaps the metadata has been generated before v1.0.18.');\n    }\n  }\n}\nexport function isPossibleNumber(nationalNumber, metadata) {\n  //, isInternational) {\n  switch (checkNumberLength(nationalNumber, metadata)) {\n    case 'IS_POSSIBLE':\n      return true;\n    // This library ignores \"local-only\" phone numbers (for simplicity).\n    // See the readme for more info on what are \"local-only\" phone numbers.\n    // case 'IS_POSSIBLE_LOCAL_ONLY':\n    // \treturn !isInternational\n\n    default:\n      return false;\n  }\n}\n//# sourceMappingURL=isPossible.js.map", "import Metadata from './metadata.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport getNumberType from './helpers/getNumberType.js';\n/**\r\n * Checks if a given phone number is valid.\r\n *\r\n * isValid(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * If the `number` is a string, it will be parsed to an object,\r\n * but only if it contains only valid phone number characters (including punctuation).\r\n * If the `number` is an object, it is used as is.\r\n *\r\n * The optional `defaultCountry` argument is the default country.\r\n * I.e. it does not restrict to just that country,\r\n * e.g. in those cases where several countries share\r\n * the same phone numbering rules (NANPA, Britain, etc).\r\n * For example, even though the number `07624 369230`\r\n * belongs to the Isle of Man (\"IM\" country code)\r\n * calling `isValidNumber('07624369230', 'GB', metadata)`\r\n * still returns `true` because the country is not restricted to `GB`,\r\n * it's just that `GB` is the default one for the phone numbering rules.\r\n * For restricting the country see `isValidNumberForRegion()`\r\n * though restricting a country might not be a good idea.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isValidNumber('+78005553535', metadata)\r\n * isValidNumber('8005553535', 'RU', metadata)\r\n * isValidNumber('88005553535', 'RU', metadata)\r\n * isValidNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\n\nexport default function isValidNumber(input, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata);\n  metadata.selectNumberingPlan(input.country, input.countryCallingCode); // By default, countries only have type regexps when it's required for\n  // distinguishing different countries having the same `countryCallingCode`.\n\n  if (metadata.hasTypes()) {\n    return getNumberType(input, options, metadata.metadata) !== undefined;\n  } // If there are no type regexps for this country in metadata then use\n  // `nationalNumberPattern` as a \"better than nothing\" replacement.\n\n\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone;\n  return matchesEntirely(nationalNumber, metadata.nationalNumberPattern());\n}\n//# sourceMappingURL=isValid.js.map", "// Copy-pasted from:\n// https://github.com/substack/semver-compare/blob/master/index.js\n//\n// Inlining this function because some users reported issues with\n// importing from `semver-compare` in a browser with ES6 \"native\" modules.\n//\n// Fixes `semver-compare` not being able to compare versions with alpha/beta/etc \"tags\".\n// https://github.com/catamphetamine/libphonenumber-js/issues/381\nexport default function (a, b) {\n  a = a.split('-');\n  b = b.split('-');\n  var pa = a[0].split('.');\n  var pb = b[0].split('.');\n\n  for (var i = 0; i < 3; i++) {\n    var na = Number(pa[i]);\n    var nb = Number(pb[i]);\n    if (na > nb) return 1;\n    if (nb > na) return -1;\n    if (!isNaN(na) && isNaN(nb)) return 1;\n    if (isNaN(na) && !isNaN(nb)) return -1;\n  }\n\n  if (a[1] && b[1]) {\n    return a[1] > b[1] ? 1 : a[1] < b[1] ? -1 : 0;\n  }\n\n  return !a[1] && b[1] ? 1 : a[1] && !b[1] ? -1 : 0;\n}\n//# sourceMappingURL=semver-compare.js.map", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport compare from './tools/semver-compare.js';\nimport isObject from './helpers/isObject.js'; // Added \"possibleLengths\" and renamed\n// \"country_phone_code_to_countries\" to \"country_calling_codes\".\n\nvar V2 = '1.0.18'; // Added \"idd_prefix\" and \"default_idd_prefix\".\n\nvar V3 = '1.2.0'; // Moved `001` country code to \"nonGeographic\" section of metadata.\n\nvar V4 = '1.7.35';\nvar DEFAULT_EXT_PREFIX = ' ext. ';\nvar CALLING_CODE_REG_EXP = /^\\d+$/;\n/**\r\n * See: https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md\r\n */\n\nvar Metadata = /*#__PURE__*/function () {\n  function Metadata(metadata) {\n    _classCallCheck(this, Metadata);\n\n    validateMetadata(metadata);\n    this.metadata = metadata;\n    setVersion.call(this, metadata);\n  }\n\n  _createClass(Metadata, [{\n    key: \"getCountries\",\n    value: function getCountries() {\n      return Object.keys(this.metadata.countries).filter(function (_) {\n        return _ !== '001';\n      });\n    }\n  }, {\n    key: \"getCountryMetadata\",\n    value: function getCountryMetadata(countryCode) {\n      return this.metadata.countries[countryCode];\n    }\n  }, {\n    key: \"nonGeographic\",\n    value: function nonGeographic() {\n      if (this.v1 || this.v2 || this.v3) return; // `nonGeographical` was a typo.\n      // It's present in metadata generated from `1.7.35` to `1.7.37`.\n      // The test case could be found by searching for \"nonGeographical\".\n\n      return this.metadata.nonGeographic || this.metadata.nonGeographical;\n    }\n  }, {\n    key: \"hasCountry\",\n    value: function hasCountry(country) {\n      return this.getCountryMetadata(country) !== undefined;\n    }\n  }, {\n    key: \"hasCallingCode\",\n    value: function hasCallingCode(callingCode) {\n      if (this.getCountryCodesForCallingCode(callingCode)) {\n        return true;\n      }\n\n      if (this.nonGeographic()) {\n        if (this.nonGeographic()[callingCode]) {\n          return true;\n        }\n      } else {\n        // A hacky workaround for old custom metadata (generated before V4).\n        var countryCodes = this.countryCallingCodes()[callingCode];\n\n        if (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\n          return true;\n        }\n      }\n    }\n  }, {\n    key: \"isNonGeographicCallingCode\",\n    value: function isNonGeographicCallingCode(callingCode) {\n      if (this.nonGeographic()) {\n        return this.nonGeographic()[callingCode] ? true : false;\n      } else {\n        return this.getCountryCodesForCallingCode(callingCode) ? false : true;\n      }\n    } // Deprecated.\n\n  }, {\n    key: \"country\",\n    value: function country(countryCode) {\n      return this.selectNumberingPlan(countryCode);\n    }\n  }, {\n    key: \"selectNumberingPlan\",\n    value: function selectNumberingPlan(countryCode, callingCode) {\n      // Supports just passing `callingCode` as the first argument.\n      if (countryCode && CALLING_CODE_REG_EXP.test(countryCode)) {\n        callingCode = countryCode;\n        countryCode = null;\n      }\n\n      if (countryCode && countryCode !== '001') {\n        if (!this.hasCountry(countryCode)) {\n          throw new Error(\"Unknown country: \".concat(countryCode));\n        }\n\n        this.numberingPlan = new NumberingPlan(this.getCountryMetadata(countryCode), this);\n      } else if (callingCode) {\n        if (!this.hasCallingCode(callingCode)) {\n          throw new Error(\"Unknown calling code: \".concat(callingCode));\n        }\n\n        this.numberingPlan = new NumberingPlan(this.getNumberingPlanMetadata(callingCode), this);\n      } else {\n        this.numberingPlan = undefined;\n      }\n\n      return this;\n    }\n  }, {\n    key: \"getCountryCodesForCallingCode\",\n    value: function getCountryCodesForCallingCode(callingCode) {\n      var countryCodes = this.countryCallingCodes()[callingCode];\n\n      if (countryCodes) {\n        // Metadata before V4 included \"non-geographic entity\" calling codes\n        // inside `country_calling_codes` (for example, `\"881\":[\"001\"]`).\n        // Now the semantics of `country_calling_codes` has changed:\n        // it's specifically for \"countries\" now.\n        // Older versions of custom metadata will simply skip parsing\n        // \"non-geographic entity\" phone numbers with new versions\n        // of this library: it's not considered a bug,\n        // because such numbers are extremely rare,\n        // and developers extremely rarely use custom metadata.\n        if (countryCodes.length === 1 && countryCodes[0].length === 3) {\n          return;\n        }\n\n        return countryCodes;\n      }\n    }\n  }, {\n    key: \"getCountryCodeForCallingCode\",\n    value: function getCountryCodeForCallingCode(callingCode) {\n      var countryCodes = this.getCountryCodesForCallingCode(callingCode);\n\n      if (countryCodes) {\n        return countryCodes[0];\n      }\n    }\n  }, {\n    key: \"getNumberingPlanMetadata\",\n    value: function getNumberingPlanMetadata(callingCode) {\n      var countryCode = this.getCountryCodeForCallingCode(callingCode);\n\n      if (countryCode) {\n        return this.getCountryMetadata(countryCode);\n      }\n\n      if (this.nonGeographic()) {\n        var metadata = this.nonGeographic()[callingCode];\n\n        if (metadata) {\n          return metadata;\n        }\n      } else {\n        // A hacky workaround for old custom metadata (generated before V4).\n        // In that metadata, there was no concept of \"non-geographic\" metadata\n        // so metadata for `001` country code was stored along with other countries.\n        // The test case can be found by searching for:\n        // \"should work around `nonGeographic` metadata not existing\".\n        var countryCodes = this.countryCallingCodes()[callingCode];\n\n        if (countryCodes && countryCodes.length === 1 && countryCodes[0] === '001') {\n          return this.metadata.countries['001'];\n        }\n      }\n    } // Deprecated.\n\n  }, {\n    key: \"countryCallingCode\",\n    value: function countryCallingCode() {\n      return this.numberingPlan.callingCode();\n    } // Deprecated.\n\n  }, {\n    key: \"IDDPrefix\",\n    value: function IDDPrefix() {\n      return this.numberingPlan.IDDPrefix();\n    } // Deprecated.\n\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function defaultIDDPrefix() {\n      return this.numberingPlan.defaultIDDPrefix();\n    } // Deprecated.\n\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function nationalNumberPattern() {\n      return this.numberingPlan.nationalNumberPattern();\n    } // Deprecated.\n\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      return this.numberingPlan.possibleLengths();\n    } // Deprecated.\n\n  }, {\n    key: \"formats\",\n    value: function formats() {\n      return this.numberingPlan.formats();\n    } // Deprecated.\n\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function nationalPrefixForParsing() {\n      return this.numberingPlan.nationalPrefixForParsing();\n    } // Deprecated.\n\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function nationalPrefixTransformRule() {\n      return this.numberingPlan.nationalPrefixTransformRule();\n    } // Deprecated.\n\n  }, {\n    key: \"leadingDigits\",\n    value: function leadingDigits() {\n      return this.numberingPlan.leadingDigits();\n    } // Deprecated.\n\n  }, {\n    key: \"hasTypes\",\n    value: function hasTypes() {\n      return this.numberingPlan.hasTypes();\n    } // Deprecated.\n\n  }, {\n    key: \"type\",\n    value: function type(_type) {\n      return this.numberingPlan.type(_type);\n    } // Deprecated.\n\n  }, {\n    key: \"ext\",\n    value: function ext() {\n      return this.numberingPlan.ext();\n    }\n  }, {\n    key: \"countryCallingCodes\",\n    value: function countryCallingCodes() {\n      if (this.v1) return this.metadata.country_phone_code_to_countries;\n      return this.metadata.country_calling_codes;\n    } // Deprecated.\n\n  }, {\n    key: \"chooseCountryByCountryCallingCode\",\n    value: function chooseCountryByCountryCallingCode(callingCode) {\n      return this.selectNumberingPlan(callingCode);\n    }\n  }, {\n    key: \"hasSelectedNumberingPlan\",\n    value: function hasSelectedNumberingPlan() {\n      return this.numberingPlan !== undefined;\n    }\n  }]);\n\n  return Metadata;\n}();\n\nexport { Metadata as default };\n\nvar NumberingPlan = /*#__PURE__*/function () {\n  function NumberingPlan(metadata, globalMetadataObject) {\n    _classCallCheck(this, NumberingPlan);\n\n    this.globalMetadataObject = globalMetadataObject;\n    this.metadata = metadata;\n    setVersion.call(this, globalMetadataObject.metadata);\n  }\n\n  _createClass(NumberingPlan, [{\n    key: \"callingCode\",\n    value: function callingCode() {\n      return this.metadata[0];\n    } // Formatting information for regions which share\n    // a country calling code is contained by only one region\n    // for performance reasons. For example, for NANPA region\n    // (\"North American Numbering Plan Administration\",\n    //  which includes USA, Canada, Cayman Islands, Bahamas, etc)\n    // it will be contained in the metadata for `US`.\n\n  }, {\n    key: \"getDefaultCountryMetadataForRegion\",\n    value: function getDefaultCountryMetadataForRegion() {\n      return this.globalMetadataObject.getNumberingPlanMetadata(this.callingCode());\n    } // Is always present.\n\n  }, {\n    key: \"IDDPrefix\",\n    value: function IDDPrefix() {\n      if (this.v1 || this.v2) return;\n      return this.metadata[1];\n    } // Is only present when a country supports multiple IDD prefixes.\n\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function defaultIDDPrefix() {\n      if (this.v1 || this.v2) return;\n      return this.metadata[12];\n    }\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function nationalNumberPattern() {\n      if (this.v1 || this.v2) return this.metadata[1];\n      return this.metadata[2];\n    } // \"possible length\" data is always present in Google's metadata.\n\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      if (this.v1) return;\n      return this.metadata[this.v2 ? 2 : 3];\n    }\n  }, {\n    key: \"_getFormats\",\n    value: function _getFormats(metadata) {\n      return metadata[this.v1 ? 2 : this.v2 ? 3 : 4];\n    } // For countries of the same region (e.g. NANPA)\n    // formats are all stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n\n  }, {\n    key: \"formats\",\n    value: function formats() {\n      var _this = this;\n\n      var formats = this._getFormats(this.metadata) || this._getFormats(this.getDefaultCountryMetadataForRegion()) || [];\n      return formats.map(function (_) {\n        return new Format(_, _this);\n      });\n    }\n  }, {\n    key: \"nationalPrefix\",\n    value: function nationalPrefix() {\n      return this.metadata[this.v1 ? 3 : this.v2 ? 4 : 5];\n    }\n  }, {\n    key: \"_getNationalPrefixFormattingRule\",\n    value: function _getNationalPrefixFormattingRule(metadata) {\n      return metadata[this.v1 ? 4 : this.v2 ? 5 : 6];\n    } // For countries of the same region (e.g. NANPA)\n    // national prefix formatting rule is stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function nationalPrefixFormattingRule() {\n      return this._getNationalPrefixFormattingRule(this.metadata) || this._getNationalPrefixFormattingRule(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"_nationalPrefixForParsing\",\n    value: function _nationalPrefixForParsing() {\n      return this.metadata[this.v1 ? 5 : this.v2 ? 6 : 7];\n    }\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function nationalPrefixForParsing() {\n      // If `national_prefix_for_parsing` is not set explicitly,\n      // then infer it from `national_prefix` (if any)\n      return this._nationalPrefixForParsing() || this.nationalPrefix();\n    }\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function nationalPrefixTransformRule() {\n      return this.metadata[this.v1 ? 6 : this.v2 ? 7 : 8];\n    }\n  }, {\n    key: \"_getNationalPrefixIsOptionalWhenFormatting\",\n    value: function _getNationalPrefixIsOptionalWhenFormatting() {\n      return !!this.metadata[this.v1 ? 7 : this.v2 ? 8 : 9];\n    } // For countries of the same region (e.g. NANPA)\n    // \"national prefix is optional when formatting\" flag is\n    // stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsOptionalWhenFormattingInNationalFormat() {\n      return this._getNationalPrefixIsOptionalWhenFormatting(this.metadata) || this._getNationalPrefixIsOptionalWhenFormatting(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"leadingDigits\",\n    value: function leadingDigits() {\n      return this.metadata[this.v1 ? 8 : this.v2 ? 9 : 10];\n    }\n  }, {\n    key: \"types\",\n    value: function types() {\n      return this.metadata[this.v1 ? 9 : this.v2 ? 10 : 11];\n    }\n  }, {\n    key: \"hasTypes\",\n    value: function hasTypes() {\n      // Versions 1.2.0 - 1.2.4: can be `[]`.\n\n      /* istanbul ignore next */\n      if (this.types() && this.types().length === 0) {\n        return false;\n      } // Versions <= 1.2.4: can be `undefined`.\n      // Version >= 1.2.5: can be `0`.\n\n\n      return !!this.types();\n    }\n  }, {\n    key: \"type\",\n    value: function type(_type2) {\n      if (this.hasTypes() && getType(this.types(), _type2)) {\n        return new Type(getType(this.types(), _type2), this);\n      }\n    }\n  }, {\n    key: \"ext\",\n    value: function ext() {\n      if (this.v1 || this.v2) return DEFAULT_EXT_PREFIX;\n      return this.metadata[13] || DEFAULT_EXT_PREFIX;\n    }\n  }]);\n\n  return NumberingPlan;\n}();\n\nvar Format = /*#__PURE__*/function () {\n  function Format(format, metadata) {\n    _classCallCheck(this, Format);\n\n    this._format = format;\n    this.metadata = metadata;\n  }\n\n  _createClass(Format, [{\n    key: \"pattern\",\n    value: function pattern() {\n      return this._format[0];\n    }\n  }, {\n    key: \"format\",\n    value: function format() {\n      return this._format[1];\n    }\n  }, {\n    key: \"leadingDigitsPatterns\",\n    value: function leadingDigitsPatterns() {\n      return this._format[2] || [];\n    }\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function nationalPrefixFormattingRule() {\n      return this._format[3] || this.metadata.nationalPrefixFormattingRule();\n    }\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsOptionalWhenFormattingInNationalFormat() {\n      return !!this._format[4] || this.metadata.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    }\n  }, {\n    key: \"nationalPrefixIsMandatoryWhenFormattingInNationalFormat\",\n    value: function nationalPrefixIsMandatoryWhenFormattingInNationalFormat() {\n      // National prefix is omitted if there's no national prefix formatting rule\n      // set for this country, or when the national prefix formatting rule\n      // contains no national prefix itself, or when this rule is set but\n      // national prefix is optional for this phone number format\n      // (and it is not enforced explicitly)\n      return this.usesNationalPrefix() && !this.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    } // Checks whether national prefix formatting rule contains national prefix.\n\n  }, {\n    key: \"usesNationalPrefix\",\n    value: function usesNationalPrefix() {\n      return this.nationalPrefixFormattingRule() && // Check that national prefix formatting rule is not a \"dummy\" one.\n      !FIRST_GROUP_ONLY_PREFIX_PATTERN.test(this.nationalPrefixFormattingRule()) // In compressed metadata, `this.nationalPrefixFormattingRule()` is `0`\n      // when `national_prefix_formatting_rule` is not present.\n      // So, `true` or `false` are returned explicitly here, so that\n      // `0` number isn't returned.\n      ? true : false;\n    }\n  }, {\n    key: \"internationalFormat\",\n    value: function internationalFormat() {\n      return this._format[5] || this.format();\n    }\n  }]);\n\n  return Format;\n}();\n/**\r\n * A pattern that is used to determine if the national prefix formatting rule\r\n * has the first group only, i.e., does not start with the national prefix.\r\n * Note that the pattern explicitly allows for unbalanced parentheses.\r\n */\n\n\nvar FIRST_GROUP_ONLY_PREFIX_PATTERN = /^\\(?\\$1\\)?$/;\n\nvar Type = /*#__PURE__*/function () {\n  function Type(type, metadata) {\n    _classCallCheck(this, Type);\n\n    this.type = type;\n    this.metadata = metadata;\n  }\n\n  _createClass(Type, [{\n    key: \"pattern\",\n    value: function pattern() {\n      if (this.metadata.v1) return this.type;\n      return this.type[0];\n    }\n  }, {\n    key: \"possibleLengths\",\n    value: function possibleLengths() {\n      if (this.metadata.v1) return;\n      return this.type[1] || this.metadata.possibleLengths();\n    }\n  }]);\n\n  return Type;\n}();\n\nfunction getType(types, type) {\n  switch (type) {\n    case 'FIXED_LINE':\n      return types[0];\n\n    case 'MOBILE':\n      return types[1];\n\n    case 'TOLL_FREE':\n      return types[2];\n\n    case 'PREMIUM_RATE':\n      return types[3];\n\n    case 'PERSONAL_NUMBER':\n      return types[4];\n\n    case 'VOICEMAIL':\n      return types[5];\n\n    case 'UAN':\n      return types[6];\n\n    case 'PAGER':\n      return types[7];\n\n    case 'VOIP':\n      return types[8];\n\n    case 'SHARED_COST':\n      return types[9];\n  }\n}\n\nexport function validateMetadata(metadata) {\n  if (!metadata) {\n    throw new Error('[libphonenumber-js] `metadata` argument not passed. Check your arguments.');\n  } // `country_phone_code_to_countries` was renamed to `country_calling_codes` in `1.0.18`.\n  // For that reason, it's not used in this detection algorithm.\n  // Instead, it detects by `countries: {}` property existence.\n\n\n  if (!isObject(metadata) || !isObject(metadata.countries)) {\n    throw new Error(\"[libphonenumber-js] `metadata` argument was passed but it's not a valid metadata. Must be an object having `.countries` child object property. Got \".concat(isObject(metadata) ? 'an object of shape: { ' + Object.keys(metadata).join(', ') + ' }' : 'a ' + typeOf(metadata) + ': ' + metadata, \".\"));\n  }\n} // Babel transforms `typeof` into some \"branches\"\n// so istanbul will show this as \"branch not covered\".\n\n/* istanbul ignore next */\n\nvar typeOf = function typeOf(_) {\n  return _typeof(_);\n};\n/**\r\n * Returns extension prefix for a country.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string?}\r\n * @example\r\n * // Returns \" ext. \"\r\n * getExtPrefix(\"US\")\r\n */\n\n\nexport function getExtPrefix(country, metadata) {\n  metadata = new Metadata(metadata);\n\n  if (metadata.hasCountry(country)) {\n    return metadata.country(country).ext();\n  }\n\n  return DEFAULT_EXT_PREFIX;\n}\n/**\r\n * Returns \"country calling code\" for a country.\r\n * Throws an error if the country doesn't exist or isn't supported by this library.\r\n * @param  {string} country\r\n * @param  {object} metadata\r\n * @return {string}\r\n * @example\r\n * // Returns \"44\"\r\n * getCountryCallingCode(\"GB\")\r\n */\n\nexport function getCountryCallingCode(country, metadata) {\n  metadata = new Metadata(metadata);\n\n  if (metadata.hasCountry(country)) {\n    return metadata.country(country).countryCallingCode();\n  }\n\n  throw new Error(\"Unknown country: \".concat(country));\n}\nexport function isSupportedCountry(country, metadata) {\n  // metadata = new Metadata(metadata)\n  // return metadata.hasCountry(country)\n  return metadata.countries.hasOwnProperty(country);\n}\n\nfunction setVersion(metadata) {\n  var version = metadata.version;\n\n  if (typeof version === 'number') {\n    this.v1 = version === 1;\n    this.v2 = version === 2;\n    this.v3 = version === 3;\n    this.v4 = version === 4;\n  } else {\n    if (!version) {\n      this.v1 = true;\n    } else if (compare(version, V3) === -1) {\n      this.v2 = true;\n    } else if (compare(version, V4) === -1) {\n      this.v3 = true;\n    } else {\n      this.v4 = true;\n    }\n  }\n} // const ISO_COUNTRY_CODE = /^[A-Z]{2}$/\n// function isCountryCode(countryCode) {\n// \treturn ISO_COUNTRY_CODE.test(countryCodeOrCountryCallingCode)\n// }\n//# sourceMappingURL=metadata.js.map", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport isObject from './helpers/isObject.js'; // Extracts the following properties from function arguments:\n// * input `text`\n// * `options` object\n// * `metadata` JSON\n\nexport default function normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n      _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 4),\n      arg_1 = _Array$prototype$slic2[0],\n      arg_2 = _Array$prototype$slic2[1],\n      arg_3 = _Array$prototype$slic2[2],\n      arg_4 = _Array$prototype$slic2[3];\n\n  var text;\n  var options;\n  var metadata; // If the phone number is passed as a string.\n  // `parsePhoneNumber('88005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    text = arg_1;\n  } else throw new TypeError('A text for parsing must be a string.'); // If \"default country\" argument is being passed then move it to `options`.\n  // `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\n\n\n  if (!arg_2 || typeof arg_2 === 'string') {\n    if (arg_4) {\n      options = arg_3;\n      metadata = arg_4;\n    } else {\n      options = undefined;\n      metadata = arg_3;\n    }\n\n    if (arg_2) {\n      options = _objectSpread({\n        defaultCountry: arg_2\n      }, options);\n    }\n  } // `defaultCountry` is not passed.\n  // Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\n  else if (isObject(arg_2)) {\n    if (arg_3) {\n      options = arg_2;\n      metadata = arg_3;\n    } else {\n      metadata = arg_2;\n    }\n  } else throw new Error(\"Invalid second argument: \".concat(arg_2));\n\n  return {\n    text: text,\n    options: options,\n    metadata: metadata\n  };\n}\n//# sourceMappingURL=normalizeArguments.js.map", "import createExtensionPattern from './createExtensionPattern.js'; // Regexp of all known extension prefixes used by different regions followed by\n// 1 or more valid digits, for use when parsing.\n\nvar EXTN_PATTERN = new RegExp('(?:' + createExtensionPattern() + ')$', 'i'); // Strips any extension (as in, the part of the number dialled after the call is\n// connected, usually indicated with extn, ext, x or similar) from the end of\n// the number, and returns it.\n\nexport default function extractExtension(number) {\n  var start = number.search(EXTN_PATTERN);\n\n  if (start < 0) {\n    return {};\n  } // If we find a potential extension, and the number preceding this is a viable\n  // number, we assume it is an extension.\n\n\n  var numberWithoutExtension = number.slice(0, start);\n  var matches = number.match(EXTN_PATTERN);\n  var i = 1;\n\n  while (i < matches.length) {\n    if (matches[i]) {\n      return {\n        number: numberWithoutExtension,\n        ext: matches[i]\n      };\n    }\n\n    i++;\n  }\n}\n//# sourceMappingURL=extractExtension.js.map", "function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n// These mappings map a character (key) to a specific digit that should\n// replace it for normalization purposes. Non-European digits that\n// may be used in phone numbers are mapped to a European equivalent.\n//\n// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\n//\nexport var DIGITS = {\n  '0': '0',\n  '1': '1',\n  '2': '2',\n  '3': '3',\n  '4': '4',\n  '5': '5',\n  '6': '6',\n  '7': '7',\n  '8': '8',\n  '9': '9',\n  \"\\uFF10\": '0',\n  // Fullwidth digit 0\n  \"\\uFF11\": '1',\n  // Fullwidth digit 1\n  \"\\uFF12\": '2',\n  // Fullwidth digit 2\n  \"\\uFF13\": '3',\n  // Fullwidth digit 3\n  \"\\uFF14\": '4',\n  // Fullwidth digit 4\n  \"\\uFF15\": '5',\n  // Fullwidth digit 5\n  \"\\uFF16\": '6',\n  // Fullwidth digit 6\n  \"\\uFF17\": '7',\n  // Fullwidth digit 7\n  \"\\uFF18\": '8',\n  // Fullwidth digit 8\n  \"\\uFF19\": '9',\n  // Fullwidth digit 9\n  \"\\u0660\": '0',\n  // Arabic-indic digit 0\n  \"\\u0661\": '1',\n  // Arabic-indic digit 1\n  \"\\u0662\": '2',\n  // Arabic-indic digit 2\n  \"\\u0663\": '3',\n  // Arabic-indic digit 3\n  \"\\u0664\": '4',\n  // Arabic-indic digit 4\n  \"\\u0665\": '5',\n  // Arabic-indic digit 5\n  \"\\u0666\": '6',\n  // Arabic-indic digit 6\n  \"\\u0667\": '7',\n  // Arabic-indic digit 7\n  \"\\u0668\": '8',\n  // Arabic-indic digit 8\n  \"\\u0669\": '9',\n  // Arabic-indic digit 9\n  \"\\u06F0\": '0',\n  // Eastern-Arabic digit 0\n  \"\\u06F1\": '1',\n  // Eastern-Arabic digit 1\n  \"\\u06F2\": '2',\n  // Eastern-Arabic digit 2\n  \"\\u06F3\": '3',\n  // Eastern-Arabic digit 3\n  \"\\u06F4\": '4',\n  // Eastern-Arabic digit 4\n  \"\\u06F5\": '5',\n  // Eastern-Arabic digit 5\n  \"\\u06F6\": '6',\n  // Eastern-Arabic digit 6\n  \"\\u06F7\": '7',\n  // Eastern-Arabic digit 7\n  \"\\u06F8\": '8',\n  // Eastern-Arabic digit 8\n  \"\\u06F9\": '9' // Eastern-Arabic digit 9\n\n};\nexport function parseDigit(character) {\n  return DIGITS[character];\n}\n/**\r\n * Parses phone number digits from a string.\r\n * Drops all punctuation leaving only digits.\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * parseDigits('8 (800) 555')\r\n * // Outputs '8800555'.\r\n * ```\r\n */\n\nexport default function parseDigits(string) {\n  var result = ''; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes) but digits\n  // (including non-European ones) don't fall into that range\n  // so such \"exotic\" characters would be discarded anyway.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    var digit = parseDigit(character);\n\n    if (digit) {\n      result += digit;\n    }\n  }\n\n  return result;\n}\n//# sourceMappingURL=parseDigits.js.map", "function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport { parseDigit } from './helpers/parseDigits.js';\n/**\r\n * Parses phone number characters from a string.\r\n * Drops all punctuation leaving only digits and the leading `+` sign (if any).\r\n * Also converts wide-ascii and arabic-indic numerals to conventional numerals.\r\n * E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n * @param  {string} string\r\n * @return {string}\r\n * @example\r\n * ```js\r\n * // Outputs '8800555'.\r\n * parseIncompletePhoneNumber('8 (800) 555')\r\n * // Outputs '+7800555'.\r\n * parseIncompletePhoneNumber('****** 555')\r\n * ```\r\n */\n\nexport default function parseIncompletePhoneNumber(string) {\n  var result = ''; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes) but digits\n  // (including non-European ones) don't fall into that range\n  // so such \"exotic\" characters would be discarded anyway.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    result += parsePhoneNumberCharacter(character, result) || '';\n  }\n\n  return result;\n}\n/**\r\n * Parses next character while parsing phone number digits (including a `+`)\r\n * from text: discards everything except `+` and digits, and `+` is only allowed\r\n * at the start of a phone number.\r\n * For example, is used in `react-phone-number-input` where it uses\r\n * [`input-format`](https://gitlab.com/catamphetamine/input-format).\r\n * @param  {string} character - Yet another character from raw input string.\r\n * @param  {string?} prevParsedCharacters - Previous parsed characters.\r\n * @param  {function?} emitEvent - An optional \"emit event\" function.\r\n * @return {string?} The parsed character.\r\n */\n\nexport function parsePhoneNumberCharacter(character, prevParsedCharacters, emitEvent) {\n  // Only allow a leading `+`.\n  if (character === '+') {\n    // If this `+` is not the first parsed character\n    // then discard it.\n    if (prevParsedCharacters) {\n      // `emitEvent` argument was added to this `export`ed function on Dec 26th, 2023.\n      // Any 3rd-party code that used to `import` and call this function before that\n      // won't be passing any `emitEvent` argument.\n      //\n      // The addition of the `emitEvent` argument was to fix the slightly-weird behavior\n      // of parsing an input string when the user inputs something like `\"2+7\"\n      // https://github.com/catamphetamine/react-phone-number-input/issues/437\n      //\n      // If the parser encounters an unexpected `+` in a string being parsed\n      // then it simply discards that out-of-place `+` and any following characters.\n      //\n      if (typeof emitEvent === 'function') {\n        emitEvent('end');\n      }\n\n      return;\n    }\n\n    return '+';\n  } // Allow digits.\n\n\n  return parseDigit(character);\n}\n//# sourceMappingURL=parseIncompletePhoneNumber.js.map", "function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nimport Metadata from '../metadata.js';\nimport getNumberType from './getNumberType.js';\nexport default function getCountryByNationalNumber(nationalPhoneNumber, _ref) {\n  var countries = _ref.countries,\n      defaultCountry = _ref.defaultCountry,\n      metadata = _ref.metadata;\n  // Re-create `metadata` because it will be selecting a `country`.\n  metadata = new Metadata(metadata); // const matchingCountries = []\n\n  for (var _iterator = _createForOfIteratorHelperLoose(countries), _step; !(_step = _iterator()).done;) {\n    var country = _step.value;\n    metadata.country(country); // \"Leading digits\" patterns are only defined for about 20% of all countries.\n    // By definition, matching \"leading digits\" is a sufficient but not a necessary\n    // condition for a phone number to belong to a country.\n    // The point of \"leading digits\" check is that it's the fastest one to get a match.\n    // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\n    // I'd suppose that \"leading digits\" patterns are mutually exclusive for different countries\n    // because of the intended use of that feature.\n\n    if (metadata.leadingDigits()) {\n      if (nationalPhoneNumber && nationalPhoneNumber.search(metadata.leadingDigits()) === 0) {\n        return country;\n      }\n    } // Else perform full validation with all of those\n    // fixed-line/mobile/etc regular expressions.\n    else if (getNumberType({\n      phone: nationalPhoneNumber,\n      country: country\n    }, undefined, metadata.metadata)) {\n      // If both the `defaultCountry` and the \"main\" one match the phone number,\n      // don't prefer the `defaultCountry` over the \"main\" one.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/154\n      return country; // // If the `defaultCountry` is among the `matchingCountries` then return it.\n      // if (defaultCountry) {\n      // \tif (country === defaultCountry) {\n      // \t\treturn country\n      // \t}\n      // \tmatchingCountries.push(country)\n      // } else {\n      // \treturn country\n      // }\n    }\n  } // // Return the first (\"main\") one of the `matchingCountries`.\n  // if (matchingCountries.length > 0) {\n  // \treturn matchingCountries[0]\n  // }\n\n}\n//# sourceMappingURL=getCountryByNationalNumber.js.map", "import getCountryByNationalNumber from './getCountryByNationalNumber.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nexport default function getCountryByCallingCode(callingCode, _ref) {\n  var nationalPhoneNumber = _ref.nationalNumber,\n      defaultCountry = _ref.defaultCountry,\n      metadata = _ref.metadata;\n\n  /* istanbul ignore if */\n  if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n    if (metadata.isNonGeographicCallingCode(callingCode)) {\n      return '001';\n    }\n  }\n\n  var possibleCountries = metadata.getCountryCodesForCallingCode(callingCode);\n\n  if (!possibleCountries) {\n    return;\n  } // If there's just one country corresponding to the country code,\n  // then just return it, without further phone number digits validation.\n\n\n  if (possibleCountries.length === 1) {\n    return possibleCountries[0];\n  }\n\n  return getCountryByNationalNumber(nationalPhoneNumber, {\n    countries: possibleCountries,\n    defaultCountry: defaultCountry,\n    metadata: metadata.metadata\n  });\n}\n//# sourceMappingURL=getCountryByCallingCode.js.map", "// When phone numbers are written in `RFC3966` format — `\"tel:+12133734253\"` —\n// they can have their \"calling code\" part written separately in a `phone-context` parameter.\n// Example: `\"tel:12133734253;phone-context=+1\"`.\n// This function parses the full phone number from the local number and the `phone-context`\n// when the `phone-context` contains a `+` sign.\nimport { VALID_DIGITS // PLUS_CHARS\n} from '../constants.js';\nexport var PLUS_SIGN = '+';\nvar RFC3966_VISUAL_SEPARATOR_ = '[\\\\-\\\\.\\\\(\\\\)]?';\nvar RFC3966_PHONE_DIGIT_ = '(' + '[' + VALID_DIGITS + ']' + '|' + RFC3966_VISUAL_SEPARATOR_ + ')';\nvar RFC3966_GLOBAL_NUMBER_DIGITS_ = '^' + '\\\\' + PLUS_SIGN + RFC3966_PHONE_DIGIT_ + '*' + '[' + VALID_DIGITS + ']' + RFC3966_PHONE_DIGIT_ + '*' + '$';\n/**\r\n * Regular expression of valid global-number-digits for the phone-context\r\n * parameter, following the syntax defined in RFC3966.\r\n */\n\nvar RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_ = new RegExp(RFC3966_GLOBAL_NUMBER_DIGITS_, 'g'); // In this port of Google's library, we don't accept alpha characters in phone numbers.\n// const ALPHANUM_ = VALID_ALPHA_ + VALID_DIGITS\n\nvar ALPHANUM_ = VALID_DIGITS;\nvar RFC3966_DOMAINLABEL_ = '[' + ALPHANUM_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*';\nvar VALID_ALPHA_ = 'a-zA-Z';\nvar RFC3966_TOPLABEL_ = '[' + VALID_ALPHA_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*';\nvar RFC3966_DOMAINNAME_ = '^(' + RFC3966_DOMAINLABEL_ + '\\\\.)*' + RFC3966_TOPLABEL_ + '\\\\.?$';\n/**\r\n * Regular expression of valid domainname for the phone-context parameter,\r\n * following the syntax defined in RFC3966.\r\n */\n\nvar RFC3966_DOMAINNAME_PATTERN_ = new RegExp(RFC3966_DOMAINNAME_, 'g');\nexport var RFC3966_PREFIX_ = 'tel:';\nexport var RFC3966_PHONE_CONTEXT_ = ';phone-context=';\nexport var RFC3966_ISDN_SUBADDRESS_ = ';isub=';\n/**\r\n * Extracts the value of the phone-context parameter of `numberToExtractFrom`,\r\n * following the syntax defined in RFC3966.\r\n *\r\n * @param {string} numberToExtractFrom\r\n * @return {string|null} the extracted string (possibly empty), or `null` if no phone-context parameter is found.\r\n */\n\nexport default function extractPhoneContext(numberToExtractFrom) {\n  var indexOfPhoneContext = numberToExtractFrom.indexOf(RFC3966_PHONE_CONTEXT_); // If no phone-context parameter is present\n\n  if (indexOfPhoneContext < 0) {\n    return null;\n  }\n\n  var phoneContextStart = indexOfPhoneContext + RFC3966_PHONE_CONTEXT_.length; // If phone-context parameter is empty\n\n  if (phoneContextStart >= numberToExtractFrom.length) {\n    return '';\n  }\n\n  var phoneContextEnd = numberToExtractFrom.indexOf(';', phoneContextStart); // If phone-context is not the last parameter\n\n  if (phoneContextEnd >= 0) {\n    return numberToExtractFrom.substring(phoneContextStart, phoneContextEnd);\n  } else {\n    return numberToExtractFrom.substring(phoneContextStart);\n  }\n}\n/**\r\n * Returns whether the value of phoneContext follows the syntax defined in RFC3966.\r\n *\r\n * @param {string|null} phoneContext\r\n * @return {boolean}\r\n */\n\nexport function isPhoneContextValid(phoneContext) {\n  if (phoneContext === null) {\n    return true;\n  }\n\n  if (phoneContext.length === 0) {\n    return false;\n  } // Does phone-context value match pattern of global-number-digits or domainname.\n\n\n  return RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_.test(phoneContext) || RFC3966_DOMAINNAME_PATTERN_.test(phoneContext);\n}\n//# sourceMappingURL=extractPhoneContext.js.map", "import extractPhoneContext, { isPhoneContextValid, PLUS_SIGN, RFC3966_PREFIX_, RFC3966_PHONE_CONTEXT_, RFC3966_ISDN_SUBADDRESS_ } from './extractPhoneContext.js';\nimport ParseError from '../ParseError.js';\n/**\r\n * @param  {string} numberToParse\r\n * @param  {string} nationalNumber\r\n * @return {}\r\n */\n\nexport default function extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(numberToParse, _ref) {\n  var extractFormattedPhoneNumber = _ref.extractFormattedPhoneNumber;\n  var phoneContext = extractPhoneContext(numberToParse);\n\n  if (!isPhoneContextValid(phoneContext)) {\n    throw new ParseError('NOT_A_NUMBER');\n  }\n\n  var phoneNumberString;\n\n  if (phoneContext === null) {\n    // Extract a possible number from the string passed in.\n    // (this strips leading characters that could not be the start of a phone number)\n    phoneNumberString = extractFormattedPhoneNumber(numberToParse) || '';\n  } else {\n    phoneNumberString = ''; // If the phone context contains a phone number prefix, we need to capture\n    // it, whereas domains will be ignored.\n\n    if (phoneContext.charAt(0) === PLUS_SIGN) {\n      phoneNumberString += phoneContext;\n    } // Now append everything between the \"tel:\" prefix and the phone-context.\n    // This should include the national number, an optional extension or\n    // isdn-subaddress component. Note we also handle the case when \"tel:\" is\n    // missing, as we have seen in some of the phone number inputs.\n    // In that case, we append everything from the beginning.\n\n\n    var indexOfRfc3966Prefix = numberToParse.indexOf(RFC3966_PREFIX_);\n    var indexOfNationalNumber; // RFC 3966 \"tel:\" prefix is preset at this stage because\n    // `isPhoneContextValid()` requires it to be present.\n\n    /* istanbul ignore else */\n\n    if (indexOfRfc3966Prefix >= 0) {\n      indexOfNationalNumber = indexOfRfc3966Prefix + RFC3966_PREFIX_.length;\n    } else {\n      indexOfNationalNumber = 0;\n    }\n\n    var indexOfPhoneContext = numberToParse.indexOf(RFC3966_PHONE_CONTEXT_);\n    phoneNumberString += numberToParse.substring(indexOfNationalNumber, indexOfPhoneContext);\n  } // Delete the isdn-subaddress and everything after it if it is present.\n  // Note extension won't appear at the same time with isdn-subaddress\n  // according to paragraph 5.3 of the RFC3966 spec.\n\n\n  var indexOfIsdn = phoneNumberString.indexOf(RFC3966_ISDN_SUBADDRESS_);\n\n  if (indexOfIsdn > 0) {\n    phoneNumberString = phoneNumberString.substring(0, indexOfIsdn);\n  } // If both phone context and isdn-subaddress are absent but other\n  // parameters are present, the parameters are left in nationalNumber.\n  // This is because we are concerned about deleting content from a potential\n  // number string when there is no strong evidence that the number is\n  // actually written in RFC3966.\n\n\n  if (phoneNumberString !== '') {\n    return phoneNumberString;\n  }\n}\n//# sourceMappingURL=extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js.map", "// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport { VALID_DIGITS, PLUS_CHARS, MIN_LENGTH_FOR_NSN, MAX_LENGTH_FOR_NSN } from './constants.js';\nimport ParseError from './ParseError.js';\nimport Metadata from './metadata.js';\nimport isViablePhoneNumber, { isViablePhoneNumberStart } from './helpers/isViablePhoneNumber.js';\nimport extractExtension from './helpers/extension/extractExtension.js';\nimport parseIncompletePhoneNumber from './parseIncompletePhoneNumber.js';\nimport getCountryCallingCode from './getCountryCallingCode.js';\nimport { isPossibleNumber } from './isPossible.js'; // import { parseRFC3966 } from './helpers/RFC3966.js'\n\nimport PhoneNumber from './PhoneNumber.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport extractNationalNumber from './helpers/extractNationalNumber.js';\nimport stripIddPrefix from './helpers/stripIddPrefix.js';\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js';\nimport extractFormattedPhoneNumberFromPossibleRfc3966NumberUri from './helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js'; // We don't allow input strings for parsing to be longer than 250 chars.\n// This prevents malicious input from consuming CPU.\n\nvar MAX_INPUT_STRING_LENGTH = 250; // This consists of the plus symbol, digits, and arabic-indic digits.\n\nvar PHONE_NUMBER_START_PATTERN = new RegExp('[' + PLUS_CHARS + VALID_DIGITS + ']'); // Regular expression of trailing characters that we want to remove.\n// A trailing `#` is sometimes used when writing phone numbers with extensions in US.\n// Example: \"+****************-910#\" number has extension \"910\".\n\nvar AFTER_PHONE_NUMBER_END_PATTERN = new RegExp('[^' + VALID_DIGITS + '#' + ']+$');\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false; // Examples:\n//\n// ```js\n// parse('8 (800) 555-35-35', 'RU')\n// parse('8 (800) 555-35-35', 'RU', metadata)\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } })\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } }, metadata)\n// parse('****** 555 35 35')\n// parse('****** 555 35 35', metadata)\n// ```\n//\n\n/**\r\n * Parses a phone number.\r\n *\r\n * parse('123456789', { defaultCountry: 'RU', v2: true }, metadata)\r\n * parse('123456789', { defaultCountry: 'RU' }, metadata)\r\n * parse('123456789', undefined, metadata)\r\n *\r\n * @param  {string} input\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {object|PhoneNumber?} If `options.v2: true` flag is passed, it returns a `PhoneNumber?` instance. Otherwise, returns an object of shape `{ phone: '...', country: '...' }` (or just `{}` if no phone number was parsed).\r\n */\n\nexport default function parse(text, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata); // Validate `defaultCountry`.\n\n  if (options.defaultCountry && !metadata.hasCountry(options.defaultCountry)) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n\n    throw new Error(\"Unknown country: \".concat(options.defaultCountry));\n  } // Parse the phone number.\n\n\n  var _parseInput = parseInput(text, options.v2, options.extract),\n      formattedPhoneNumber = _parseInput.number,\n      ext = _parseInput.ext,\n      error = _parseInput.error; // If the phone number is not viable then return nothing.\n\n\n  if (!formattedPhoneNumber) {\n    if (options.v2) {\n      if (error === 'TOO_SHORT') {\n        throw new ParseError('TOO_SHORT');\n      }\n\n      throw new ParseError('NOT_A_NUMBER');\n    }\n\n    return {};\n  }\n\n  var _parsePhoneNumber = parsePhoneNumber(formattedPhoneNumber, options.defaultCountry, options.defaultCallingCode, metadata),\n      country = _parsePhoneNumber.country,\n      nationalNumber = _parsePhoneNumber.nationalNumber,\n      countryCallingCode = _parsePhoneNumber.countryCallingCode,\n      countryCallingCodeSource = _parsePhoneNumber.countryCallingCodeSource,\n      carrierCode = _parsePhoneNumber.carrierCode;\n\n  if (!metadata.hasSelectedNumberingPlan()) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n\n    return {};\n  } // Validate national (significant) number length.\n\n\n  if (!nationalNumber || nationalNumber.length < MIN_LENGTH_FOR_NSN) {\n    // Won't throw here because the regexp already demands length > 1.\n\n    /* istanbul ignore if */\n    if (options.v2) {\n      throw new ParseError('TOO_SHORT');\n    } // Google's demo just throws an error in this case.\n\n\n    return {};\n  } // Validate national (significant) number length.\n  //\n  // A sidenote:\n  //\n  // They say that sometimes national (significant) numbers\n  // can be longer than `MAX_LENGTH_FOR_NSN` (e.g. in Germany).\n  // https://github.com/googlei18n/libphonenumber/blob/7e1748645552da39c4e1ba731e47969d97bdb539/resources/phonenumber.proto#L36\n  // Such numbers will just be discarded.\n  //\n\n\n  if (nationalNumber.length > MAX_LENGTH_FOR_NSN) {\n    if (options.v2) {\n      throw new ParseError('TOO_LONG');\n    } // Google's demo just throws an error in this case.\n\n\n    return {};\n  }\n\n  if (options.v2) {\n    var phoneNumber = new PhoneNumber(countryCallingCode, nationalNumber, metadata.metadata);\n\n    if (country) {\n      phoneNumber.country = country;\n    }\n\n    if (carrierCode) {\n      phoneNumber.carrierCode = carrierCode;\n    }\n\n    if (ext) {\n      phoneNumber.ext = ext;\n    }\n\n    phoneNumber.__countryCallingCodeSource = countryCallingCodeSource;\n    return phoneNumber;\n  } // Check if national phone number pattern matches the number.\n  // National number pattern is different for each country,\n  // even for those ones which are part of the \"NANPA\" group.\n\n\n  var valid = (options.extended ? metadata.hasSelectedNumberingPlan() : country) ? matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) : false;\n\n  if (!options.extended) {\n    return valid ? result(country, nationalNumber, ext) : {};\n  } // isInternational: countryCallingCode !== undefined\n\n\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    carrierCode: carrierCode,\n    valid: valid,\n    possible: valid ? true : options.extended === true && metadata.possibleLengths() && isPossibleNumber(nationalNumber, metadata) ? true : false,\n    phone: nationalNumber,\n    ext: ext\n  };\n}\n/**\r\n * Extracts a formatted phone number from text.\r\n * Doesn't guarantee that the extracted phone number\r\n * is a valid phone number (for example, doesn't validate its length).\r\n * @param  {string} text\r\n * @param  {boolean} [extract] — If `false`, then will parse the entire `text` as a phone number.\r\n * @param  {boolean} [throwOnError] — By default, it won't throw if the text is too long.\r\n * @return {string}\r\n * @example\r\n * // Returns \"(*************\".\r\n * extractFormattedPhoneNumber(\"Call (************* for assistance.\")\r\n */\n\nfunction _extractFormattedPhoneNumber(text, extract, throwOnError) {\n  if (!text) {\n    return;\n  }\n\n  if (text.length > MAX_INPUT_STRING_LENGTH) {\n    if (throwOnError) {\n      throw new ParseError('TOO_LONG');\n    }\n\n    return;\n  }\n\n  if (extract === false) {\n    return text;\n  } // Attempt to extract a possible number from the string passed in\n\n\n  var startsAt = text.search(PHONE_NUMBER_START_PATTERN);\n\n  if (startsAt < 0) {\n    return;\n  }\n\n  return text // Trim everything to the left of the phone number\n  .slice(startsAt) // Remove trailing non-numerical characters\n  .replace(AFTER_PHONE_NUMBER_END_PATTERN, '');\n}\n/**\r\n * @param  {string} text - Input.\r\n * @param  {boolean} v2 - Legacy API functions don't pass `v2: true` flag.\r\n * @param  {boolean} [extract] - Whether to extract a phone number from `text`, or attempt to parse the entire text as a phone number.\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\n\n\nfunction parseInput(text, v2, extract) {\n  // // Parse RFC 3966 phone number URI.\n  // if (text && text.indexOf('tel:') === 0) {\n  // \treturn parseRFC3966(text)\n  // }\n  // let number = extractFormattedPhoneNumber(text, extract, v2)\n  var number = extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(text, {\n    extractFormattedPhoneNumber: function extractFormattedPhoneNumber(text) {\n      return _extractFormattedPhoneNumber(text, extract, v2);\n    }\n  }); // If the phone number is not viable, then abort.\n\n  if (!number) {\n    return {};\n  }\n\n  if (!isViablePhoneNumber(number)) {\n    if (isViablePhoneNumberStart(number)) {\n      return {\n        error: 'TOO_SHORT'\n      };\n    }\n\n    return {};\n  } // Attempt to parse extension first, since it doesn't require region-specific\n  // data and we want to have the non-normalised number here.\n\n\n  var withExtensionStripped = extractExtension(number);\n\n  if (withExtensionStripped.ext) {\n    return withExtensionStripped;\n  }\n\n  return {\n    number: number\n  };\n}\n/**\r\n * Creates `parse()` result object.\r\n */\n\n\nfunction result(country, nationalNumber, ext) {\n  var result = {\n    country: country,\n    phone: nationalNumber\n  };\n\n  if (ext) {\n    result.ext = ext;\n  }\n\n  return result;\n}\n/**\r\n * Parses a viable phone number.\r\n * @param {string} formattedPhoneNumber — Example: \"(*************\".\r\n * @param {string} [defaultCountry]\r\n * @param {string} [defaultCallingCode]\r\n * @param {Metadata} metadata\r\n * @return {object} Returns `{ country: string?, countryCallingCode: string?, nationalNumber: string? }`.\r\n */\n\n\nfunction parsePhoneNumber(formattedPhoneNumber, defaultCountry, defaultCallingCode, metadata) {\n  // Extract calling code from phone number.\n  var _extractCountryCallin = extractCountryCallingCode(parseIncompletePhoneNumber(formattedPhoneNumber), defaultCountry, defaultCallingCode, metadata.metadata),\n      countryCallingCodeSource = _extractCountryCallin.countryCallingCodeSource,\n      countryCallingCode = _extractCountryCallin.countryCallingCode,\n      number = _extractCountryCallin.number; // Choose a country by `countryCallingCode`.\n\n\n  var country;\n\n  if (countryCallingCode) {\n    metadata.selectNumberingPlan(countryCallingCode);\n  } // If `formattedPhoneNumber` is passed in \"national\" format\n  // then `number` is defined and `countryCallingCode` is `undefined`.\n  else if (number && (defaultCountry || defaultCallingCode)) {\n    metadata.selectNumberingPlan(defaultCountry, defaultCallingCode);\n\n    if (defaultCountry) {\n      country = defaultCountry;\n    } else {\n      /* istanbul ignore if */\n      if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n        if (metadata.isNonGeographicCallingCode(defaultCallingCode)) {\n          country = '001';\n        }\n      }\n    }\n\n    countryCallingCode = defaultCallingCode || getCountryCallingCode(defaultCountry, metadata.metadata);\n  } else return {};\n\n  if (!number) {\n    return {\n      countryCallingCodeSource: countryCallingCodeSource,\n      countryCallingCode: countryCallingCode\n    };\n  }\n\n  var _extractNationalNumbe = extractNationalNumber(parseIncompletePhoneNumber(number), metadata),\n      nationalNumber = _extractNationalNumbe.nationalNumber,\n      carrierCode = _extractNationalNumbe.carrierCode; // Sometimes there are several countries\n  // corresponding to the same country phone code\n  // (e.g. NANPA countries all having `1` country phone code).\n  // Therefore, to reliably determine the exact country,\n  // national (significant) number should have been parsed first.\n  //\n  // When `metadata.json` is generated, all \"ambiguous\" country phone codes\n  // get their countries populated with the full set of\n  // \"phone number type\" regular expressions.\n  //\n\n\n  var exactCountry = getCountryByCallingCode(countryCallingCode, {\n    nationalNumber: nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  });\n\n  if (exactCountry) {\n    country = exactCountry;\n    /* istanbul ignore if */\n\n    if (exactCountry === '001') {// Can't happen with `USE_NON_GEOGRAPHIC_COUNTRY_CODE` being `false`.\n      // If `USE_NON_GEOGRAPHIC_COUNTRY_CODE` is set to `true` for some reason,\n      // then remove the \"istanbul ignore if\".\n    } else {\n      metadata.country(country);\n    }\n  }\n\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    countryCallingCodeSource: countryCallingCodeSource,\n    nationalNumber: nationalNumber,\n    carrierCode: carrierCode\n  };\n}\n//# sourceMappingURL=parse.js.map", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport parse from './parse.js';\nexport default function parsePhoneNumberWithError(text, options, metadata) {\n  return parse(text, _objectSpread(_objectSpread({}, options), {}, {\n    v2: true\n  }), metadata);\n}\n//# sourceMappingURL=parsePhoneNumberWithError_.js.map", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport parsePhoneNumberWithError from './parsePhoneNumberWithError_.js';\nimport ParseError from './ParseError.js';\nimport { isSupportedCountry } from './metadata.js';\nexport default function parsePhoneNumber(text, options, metadata) {\n  // Validate `defaultCountry`.\n  if (options && options.defaultCountry && !isSupportedCountry(options.defaultCountry, metadata)) {\n    options = _objectSpread(_objectSpread({}, options), {}, {\n      defaultCountry: undefined\n    });\n  } // Parse phone number.\n\n\n  try {\n    return parsePhoneNumberWithError(text, options, metadata);\n  } catch (error) {\n    /* istanbul ignore else */\n    if (error instanceof ParseError) {//\n    } else {\n      throw error;\n    }\n  }\n}\n//# sourceMappingURL=parsePhoneNumber_.js.map", "// This file is a workaround for a bug in web browsers' \"native\"\n// ES6 importing system which is uncapable of importing \"*.json\" files.\n// https://github.com/catamphetamine/libphonenumber-js/issues/239\nexport default {\"AC\":\"40123\",\"AD\":\"312345\",\"AE\":\"501234567\",\"AF\":\"701234567\",\"AG\":\"2684641234\",\"AI\":\"2642351234\",\"AL\":\"672123456\",\"AM\":\"77123456\",\"AO\":\"923123456\",\"AR\":\"91123456789\",\"AS\":\"6847331234\",\"AT\":\"664123456\",\"AU\":\"412345678\",\"AW\":\"5601234\",\"AX\":\"412345678\",\"AZ\":\"401234567\",\"BA\":\"61123456\",\"BB\":\"2462501234\",\"BD\":\"1812345678\",\"BE\":\"470123456\",\"BF\":\"70123456\",\"BG\":\"43012345\",\"BH\":\"36001234\",\"BI\":\"79561234\",\"BJ\":\"0195123456\",\"BL\":\"690001234\",\"BM\":\"4413701234\",\"BN\":\"7123456\",\"BO\":\"71234567\",\"BQ\":\"3181234\",\"BR\":\"11961234567\",\"BS\":\"2423591234\",\"BT\":\"17123456\",\"BW\":\"71123456\",\"BY\":\"294911911\",\"BZ\":\"6221234\",\"CA\":\"5062345678\",\"CC\":\"412345678\",\"CD\":\"991234567\",\"CF\":\"70012345\",\"CG\":\"061234567\",\"CH\":\"781234567\",\"CI\":\"0123456789\",\"CK\":\"71234\",\"CL\":\"221234567\",\"CM\":\"671234567\",\"CN\":\"13123456789\",\"CO\":\"3211234567\",\"CR\":\"83123456\",\"CU\":\"51234567\",\"CV\":\"9911234\",\"CW\":\"95181234\",\"CX\":\"412345678\",\"CY\":\"96123456\",\"CZ\":\"601123456\",\"DE\":\"15123456789\",\"DJ\":\"77831001\",\"DK\":\"34412345\",\"DM\":\"7672251234\",\"DO\":\"8092345678\",\"DZ\":\"551234567\",\"EC\":\"991234567\",\"EE\":\"51234567\",\"EG\":\"1001234567\",\"EH\":\"650123456\",\"ER\":\"7123456\",\"ES\":\"612345678\",\"ET\":\"911234567\",\"FI\":\"412345678\",\"FJ\":\"7012345\",\"FK\":\"51234\",\"FM\":\"3501234\",\"FO\":\"211234\",\"FR\":\"612345678\",\"GA\":\"06031234\",\"GB\":\"7400123456\",\"GD\":\"4734031234\",\"GE\":\"555123456\",\"GF\":\"694201234\",\"GG\":\"7781123456\",\"GH\":\"231234567\",\"GI\":\"57123456\",\"GL\":\"221234\",\"GM\":\"3012345\",\"GN\":\"601123456\",\"GP\":\"690001234\",\"GQ\":\"222123456\",\"GR\":\"6912345678\",\"GT\":\"51234567\",\"GU\":\"6713001234\",\"GW\":\"955012345\",\"GY\":\"6091234\",\"HK\":\"51234567\",\"HN\":\"91234567\",\"HR\":\"921234567\",\"HT\":\"34101234\",\"HU\":\"201234567\",\"ID\":\"812345678\",\"IE\":\"850123456\",\"IL\":\"502345678\",\"IM\":\"7924123456\",\"IN\":\"8123456789\",\"IO\":\"3801234\",\"IQ\":\"7912345678\",\"IR\":\"9123456789\",\"IS\":\"6111234\",\"IT\":\"3123456789\",\"JE\":\"7797712345\",\"JM\":\"8762101234\",\"JO\":\"790123456\",\"JP\":\"9012345678\",\"KE\":\"712123456\",\"KG\":\"700123456\",\"KH\":\"91234567\",\"KI\":\"72001234\",\"KM\":\"3212345\",\"KN\":\"8697652917\",\"KP\":\"1921234567\",\"KR\":\"1020000000\",\"KW\":\"50012345\",\"KY\":\"3453231234\",\"KZ\":\"7710009998\",\"LA\":\"2023123456\",\"LB\":\"71123456\",\"LC\":\"7582845678\",\"LI\":\"660234567\",\"LK\":\"712345678\",\"LR\":\"770123456\",\"LS\":\"50123456\",\"LT\":\"61234567\",\"LU\":\"628123456\",\"LV\":\"21234567\",\"LY\":\"912345678\",\"MA\":\"650123456\",\"MC\":\"612345678\",\"MD\":\"62112345\",\"ME\":\"67622901\",\"MF\":\"690001234\",\"MG\":\"321234567\",\"MH\":\"2351234\",\"MK\":\"72345678\",\"ML\":\"65012345\",\"MM\":\"92123456\",\"MN\":\"88123456\",\"MO\":\"66123456\",\"MP\":\"6702345678\",\"MQ\":\"696201234\",\"MR\":\"22123456\",\"MS\":\"6644923456\",\"MT\":\"96961234\",\"MU\":\"52512345\",\"MV\":\"7712345\",\"MW\":\"991234567\",\"MX\":\"2221234567\",\"MY\":\"123456789\",\"MZ\":\"821234567\",\"NA\":\"811234567\",\"NC\":\"751234\",\"NE\":\"93123456\",\"NF\":\"381234\",\"NG\":\"8021234567\",\"NI\":\"81234567\",\"NL\":\"612345678\",\"NO\":\"40612345\",\"NP\":\"9841234567\",\"NR\":\"5551234\",\"NU\":\"8884012\",\"NZ\":\"211234567\",\"OM\":\"92123456\",\"PA\":\"61234567\",\"PE\":\"912345678\",\"PF\":\"87123456\",\"PG\":\"70123456\",\"PH\":\"9051234567\",\"PK\":\"3012345678\",\"PL\":\"512345678\",\"PM\":\"551234\",\"PR\":\"7872345678\",\"PS\":\"599123456\",\"PT\":\"912345678\",\"PW\":\"6201234\",\"PY\":\"961456789\",\"QA\":\"33123456\",\"RE\":\"692123456\",\"RO\":\"712034567\",\"RS\":\"601234567\",\"RU\":\"9123456789\",\"RW\":\"720123456\",\"SA\":\"512345678\",\"SB\":\"7421234\",\"SC\":\"2510123\",\"SD\":\"911231234\",\"SE\":\"701234567\",\"SG\":\"81234567\",\"SH\":\"51234\",\"SI\":\"31234567\",\"SJ\":\"41234567\",\"SK\":\"912123456\",\"SL\":\"25123456\",\"SM\":\"66661212\",\"SN\":\"701234567\",\"SO\":\"71123456\",\"SR\":\"7412345\",\"SS\":\"977123456\",\"ST\":\"9812345\",\"SV\":\"70123456\",\"SX\":\"7215205678\",\"SY\":\"944567890\",\"SZ\":\"76123456\",\"TA\":\"8999\",\"TC\":\"6492311234\",\"TD\":\"63012345\",\"TG\":\"90112345\",\"TH\":\"812345678\",\"TJ\":\"917123456\",\"TK\":\"7290\",\"TL\":\"77212345\",\"TM\":\"66123456\",\"TN\":\"20123456\",\"TO\":\"7715123\",\"TR\":\"5012345678\",\"TT\":\"8682911234\",\"TV\":\"901234\",\"TW\":\"912345678\",\"TZ\":\"621234567\",\"UA\":\"501234567\",\"UG\":\"712345678\",\"US\":\"2015550123\",\"UY\":\"94231234\",\"UZ\":\"912345678\",\"VA\":\"3123456789\",\"VC\":\"7844301234\",\"VE\":\"4121234567\",\"VG\":\"2843001234\",\"VI\":\"3406421234\",\"VN\":\"912345678\",\"VU\":\"5912345\",\"WF\":\"821234\",\"WS\":\"7212345\",\"XK\":\"43201234\",\"YE\":\"712345678\",\"YT\":\"639012345\",\"ZA\":\"711234567\",\"ZM\":\"955123456\",\"ZW\":\"712345678\"}", "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport isViablePhoneNumber from '../helpers/isViablePhoneNumber.js';\nimport _getNumberType from '../helpers/getNumberType.js';\nimport isObject from '../helpers/isObject.js';\nimport parse from '../parse.js'; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function getNumberType() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      input = _normalizeArguments.input,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n\n  if (!input.phone) {\n    return;\n  }\n\n  return _getNumberType(input, options, metadata);\n} // Sort out arguments\n\nexport function normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n      _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 4),\n      arg_1 = _Array$prototype$slic2[0],\n      arg_2 = _Array$prototype$slic2[1],\n      arg_3 = _Array$prototype$slic2[2],\n      arg_4 = _Array$prototype$slic2[3];\n\n  var input;\n  var options = {};\n  var metadata; // If the phone number is passed as a string.\n  // `getNumberType('88005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    // If \"default country\" argument is being passed\n    // then convert it to an `options` object.\n    // `getNumberType('88005553535', 'RU', metadata)`.\n    if (!isObject(arg_2)) {\n      if (arg_4) {\n        options = arg_3;\n        metadata = arg_4;\n      } else {\n        metadata = arg_3;\n      } // `parse` extracts phone numbers from raw text,\n      // therefore it will cut off all \"garbage\" characters,\n      // while this `validate` function needs to verify\n      // that the phone number contains no \"garbage\"\n      // therefore the explicit `isViablePhoneNumber` check.\n\n\n      if (isViablePhoneNumber(arg_1)) {\n        input = parse(arg_1, {\n          defaultCountry: arg_2\n        }, metadata);\n      } else {\n        input = {};\n      }\n    } // No \"resrict country\" argument is being passed.\n    // International phone number is passed.\n    // `getNumberType('+78005553535', metadata)`.\n    else {\n      if (arg_3) {\n        options = arg_2;\n        metadata = arg_3;\n      } else {\n        metadata = arg_2;\n      } // `parse` extracts phone numbers from raw text,\n      // therefore it will cut off all \"garbage\" characters,\n      // while this `validate` function needs to verify\n      // that the phone number contains no \"garbage\"\n      // therefore the explicit `isViablePhoneNumber` check.\n\n\n      if (isViablePhoneNumber(arg_1)) {\n        input = parse(arg_1, undefined, metadata);\n      } else {\n        input = {};\n      }\n    }\n  } // If the phone number is passed as a parsed phone number.\n  // `getNumberType({ phone: '88005553535', country: 'RU' }, ...)`.\n  else if (isObject(arg_1)) {\n    input = arg_1;\n\n    if (arg_3) {\n      options = arg_2;\n      metadata = arg_3;\n    } else {\n      metadata = arg_2;\n    }\n  } else throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.');\n\n  return {\n    input: input,\n    options: options,\n    metadata: metadata\n  };\n}\n//# sourceMappingURL=getNumberType.js.map", "import _isValidNumber from '../isValid.js';\nimport { normalizeArguments } from './getNumberType.js'; // Finds out national phone number type (fixed line, mobile, etc)\n\nexport default function isValidNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      input = _normalizeArguments.input,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata; // `parseNumber()` would return `{}` when no phone number could be parsed from the input.\n\n\n  if (!input.phone) {\n    return false;\n  }\n\n  return _isValidNumber(input, options, metadata);\n}\n//# sourceMappingURL=isValidNumber.js.map", "// Deprecated.\r\n\r\nimport withMetadataArgument from '../min/exports/withMetadataArgument.js'\r\n\r\nimport _isValidNumber from '../es6/legacy/isValidNumber.js'\r\n\r\nexport function isValidNumber() {\r\n\treturn withMetadataArgument(_isValidNumber, arguments)\r\n}\r\n", "import Metadata from './metadata.js';\nexport default function getCountries(metadata) {\n  return new Metadata(metadata).getCountries();\n}\n//# sourceMappingURL=getCountries.js.map", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getCountries as _getCountries } from '../../core/index.js'\r\n\r\nexport function getCountries() {\r\n\treturn withMetadataArgument(_getCountries, arguments)\r\n}", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getCountryCallingCode as _getCountryCallingCode } from '../../core/index.js'\r\n\r\nexport function getCountryCallingCode() {\r\n\treturn withMetadataArgument(_getCountryCallingCode, arguments)\r\n}", "import PhoneNumber from './PhoneNumber.js';\nexport default function getExampleNumber(country, examples, metadata) {\n  if (examples[country]) {\n    return new PhoneNumber(country, examples[country], metadata);\n  }\n}\n//# sourceMappingURL=getExampleNumber.js.map", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getExampleNumber as _getExampleNumber } from '../../core/index.js'\r\n\r\nexport function getExampleNumber() {\r\n\treturn withMetadataArgument(_getExampleNumber, arguments)\r\n}", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumber from './parsePhoneNumber_.js';\nexport default function isPossiblePhoneNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  options = _objectSpread(_objectSpread({}, options), {}, {\n    extract: false\n  });\n  var phoneNumber = parsePhoneNumber(text, options, metadata);\n  return phoneNumber && phoneNumber.isPossible() || false;\n}\n//# sourceMappingURL=isPossiblePhoneNumber.js.map", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isPossiblePhoneNumber as _isPossiblePhoneNumber } from '../../core/index.js'\r\n\r\nexport function isPossiblePhoneNumber() {\r\n\treturn withMetadataArgument(_isPossiblePhoneNumber, arguments)\r\n}", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumber from './parsePhoneNumber_.js';\nexport default function isValidPhoneNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  options = _objectSpread(_objectSpread({}, options), {}, {\n    extract: false\n  });\n  var phoneNumber = parsePhoneNumber(text, options, metadata);\n  return phoneNumber && phoneNumber.isValid() || false;\n}\n//# sourceMappingURL=isValidPhoneNumber.js.map", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { isValidPhoneNumber as _isValidPhoneNumber } from '../../core/index.js'\r\n\r\nexport function isValidPhoneNumber() {\r\n\treturn withMetadataArgument(_isValidPhoneNumber, arguments)\r\n}", "import normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumber_ from './parsePhoneNumber_.js';\nexport default function parsePhoneNumber() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  return parsePhoneNumber_(text, options, metadata);\n}\n//# sourceMappingURL=parsePhoneNumber.js.map", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { default as _parsePhoneNumber } from '../../core/index.js'\r\n\r\nexport function parsePhoneNumber() {\r\n\treturn withMetadataArgument(_parsePhoneNumber, arguments)\r\n}", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport normalizeArguments from './normalizeArguments.js';\nimport parsePhoneNumberWithError from './parsePhoneNumberWithError_.js';\nimport ParseError from './ParseError.js';\nimport Metadata from './metadata.js';\nimport checkNumberLength from './helpers/checkNumberLength.js';\nexport default function validatePhoneNumberLength() {\n  var _normalizeArguments = normalizeArguments(arguments),\n      text = _normalizeArguments.text,\n      options = _normalizeArguments.options,\n      metadata = _normalizeArguments.metadata;\n\n  options = _objectSpread(_objectSpread({}, options), {}, {\n    extract: false\n  }); // Parse phone number.\n\n  try {\n    var phoneNumber = parsePhoneNumberWithError(text, options, metadata);\n    metadata = new Metadata(metadata);\n    metadata.selectNumberingPlan(phoneNumber.countryCallingCode);\n    var result = checkNumberLength(phoneNumber.nationalNumber, metadata);\n\n    if (result !== 'IS_POSSIBLE') {\n      return result;\n    }\n  } catch (error) {\n    /* istanbul ignore else */\n    if (error instanceof ParseError) {\n      return error.message;\n    } else {\n      throw error;\n    }\n  }\n}\n//# sourceMappingURL=validatePhoneNumberLength.js.map", "import withMetadataArgument from './withMetadataArgument.js'\r\nimport { validatePhoneNumberLength as _validatePhoneNumberLength } from '../../core/index.js'\r\n\r\nexport function validatePhoneNumberLength() {\r\n\treturn withMetadataArgument(_validatePhoneNumberLength, arguments)\r\n}", "// This file is a workaround for a bug in web browsers' \"native\"\n// ES6 importing system which is uncapable of importing \"*.json\" files.\n// https://github.com/catamphetamine/libphonenumber-js/issues/239\nexport default {\"version\":4,\"country_calling_codes\":{\"1\":[\"US\",\"AG\",\"AI\",\"AS\",\"BB\",\"BM\",\"BS\",\"CA\",\"DM\",\"DO\",\"GD\",\"GU\",\"JM\",\"KN\",\"KY\",\"LC\",\"MP\",\"MS\",\"PR\",\"SX\",\"TC\",\"TT\",\"VC\",\"VG\",\"VI\"],\"7\":[\"RU\",\"KZ\"],\"20\":[\"EG\"],\"27\":[\"ZA\"],\"30\":[\"GR\"],\"31\":[\"NL\"],\"32\":[\"BE\"],\"33\":[\"FR\"],\"34\":[\"ES\"],\"36\":[\"HU\"],\"39\":[\"IT\",\"VA\"],\"40\":[\"RO\"],\"41\":[\"CH\"],\"43\":[\"AT\"],\"44\":[\"GB\",\"GG\",\"IM\",\"JE\"],\"45\":[\"DK\"],\"46\":[\"SE\"],\"47\":[\"NO\",\"SJ\"],\"48\":[\"PL\"],\"49\":[\"DE\"],\"51\":[\"PE\"],\"52\":[\"MX\"],\"53\":[\"CU\"],\"54\":[\"AR\"],\"55\":[\"BR\"],\"56\":[\"CL\"],\"57\":[\"CO\"],\"58\":[\"VE\"],\"60\":[\"MY\"],\"61\":[\"AU\",\"CC\",\"CX\"],\"62\":[\"ID\"],\"63\":[\"PH\"],\"64\":[\"NZ\"],\"65\":[\"SG\"],\"66\":[\"TH\"],\"81\":[\"JP\"],\"82\":[\"KR\"],\"84\":[\"VN\"],\"86\":[\"CN\"],\"90\":[\"TR\"],\"91\":[\"IN\"],\"92\":[\"PK\"],\"93\":[\"AF\"],\"94\":[\"LK\"],\"95\":[\"MM\"],\"98\":[\"IR\"],\"211\":[\"SS\"],\"212\":[\"MA\",\"EH\"],\"213\":[\"DZ\"],\"216\":[\"TN\"],\"218\":[\"LY\"],\"220\":[\"GM\"],\"221\":[\"SN\"],\"222\":[\"MR\"],\"223\":[\"ML\"],\"224\":[\"GN\"],\"225\":[\"CI\"],\"226\":[\"BF\"],\"227\":[\"NE\"],\"228\":[\"TG\"],\"229\":[\"BJ\"],\"230\":[\"MU\"],\"231\":[\"LR\"],\"232\":[\"SL\"],\"233\":[\"GH\"],\"234\":[\"NG\"],\"235\":[\"TD\"],\"236\":[\"CF\"],\"237\":[\"CM\"],\"238\":[\"CV\"],\"239\":[\"ST\"],\"240\":[\"GQ\"],\"241\":[\"GA\"],\"242\":[\"CG\"],\"243\":[\"CD\"],\"244\":[\"AO\"],\"245\":[\"GW\"],\"246\":[\"IO\"],\"247\":[\"AC\"],\"248\":[\"SC\"],\"249\":[\"SD\"],\"250\":[\"RW\"],\"251\":[\"ET\"],\"252\":[\"SO\"],\"253\":[\"DJ\"],\"254\":[\"KE\"],\"255\":[\"TZ\"],\"256\":[\"UG\"],\"257\":[\"BI\"],\"258\":[\"MZ\"],\"260\":[\"ZM\"],\"261\":[\"MG\"],\"262\":[\"RE\",\"YT\"],\"263\":[\"ZW\"],\"264\":[\"NA\"],\"265\":[\"MW\"],\"266\":[\"LS\"],\"267\":[\"BW\"],\"268\":[\"SZ\"],\"269\":[\"KM\"],\"290\":[\"SH\",\"TA\"],\"291\":[\"ER\"],\"297\":[\"AW\"],\"298\":[\"FO\"],\"299\":[\"GL\"],\"350\":[\"GI\"],\"351\":[\"PT\"],\"352\":[\"LU\"],\"353\":[\"IE\"],\"354\":[\"IS\"],\"355\":[\"AL\"],\"356\":[\"MT\"],\"357\":[\"CY\"],\"358\":[\"FI\",\"AX\"],\"359\":[\"BG\"],\"370\":[\"LT\"],\"371\":[\"LV\"],\"372\":[\"EE\"],\"373\":[\"MD\"],\"374\":[\"AM\"],\"375\":[\"BY\"],\"376\":[\"AD\"],\"377\":[\"MC\"],\"378\":[\"SM\"],\"380\":[\"UA\"],\"381\":[\"RS\"],\"382\":[\"ME\"],\"383\":[\"XK\"],\"385\":[\"HR\"],\"386\":[\"SI\"],\"387\":[\"BA\"],\"389\":[\"MK\"],\"420\":[\"CZ\"],\"421\":[\"SK\"],\"423\":[\"LI\"],\"500\":[\"FK\"],\"501\":[\"BZ\"],\"502\":[\"GT\"],\"503\":[\"SV\"],\"504\":[\"HN\"],\"505\":[\"NI\"],\"506\":[\"CR\"],\"507\":[\"PA\"],\"508\":[\"PM\"],\"509\":[\"HT\"],\"590\":[\"GP\",\"BL\",\"MF\"],\"591\":[\"BO\"],\"592\":[\"GY\"],\"593\":[\"EC\"],\"594\":[\"GF\"],\"595\":[\"PY\"],\"596\":[\"MQ\"],\"597\":[\"SR\"],\"598\":[\"UY\"],\"599\":[\"CW\",\"BQ\"],\"670\":[\"TL\"],\"672\":[\"NF\"],\"673\":[\"BN\"],\"674\":[\"NR\"],\"675\":[\"PG\"],\"676\":[\"TO\"],\"677\":[\"SB\"],\"678\":[\"VU\"],\"679\":[\"FJ\"],\"680\":[\"PW\"],\"681\":[\"WF\"],\"682\":[\"CK\"],\"683\":[\"NU\"],\"685\":[\"WS\"],\"686\":[\"KI\"],\"687\":[\"NC\"],\"688\":[\"TV\"],\"689\":[\"PF\"],\"690\":[\"TK\"],\"691\":[\"FM\"],\"692\":[\"MH\"],\"850\":[\"KP\"],\"852\":[\"HK\"],\"853\":[\"MO\"],\"855\":[\"KH\"],\"856\":[\"LA\"],\"880\":[\"BD\"],\"886\":[\"TW\"],\"960\":[\"MV\"],\"961\":[\"LB\"],\"962\":[\"JO\"],\"963\":[\"SY\"],\"964\":[\"IQ\"],\"965\":[\"KW\"],\"966\":[\"SA\"],\"967\":[\"YE\"],\"968\":[\"OM\"],\"970\":[\"PS\"],\"971\":[\"AE\"],\"972\":[\"IL\"],\"973\":[\"BH\"],\"974\":[\"QA\"],\"975\":[\"BT\"],\"976\":[\"MN\"],\"977\":[\"NP\"],\"992\":[\"TJ\"],\"993\":[\"TM\"],\"994\":[\"AZ\"],\"995\":[\"GE\"],\"996\":[\"KG\"],\"998\":[\"UZ\"]},\"countries\":{\"AC\":[\"247\",\"00\",\"(?:[01589]\\\\d|[46])\\\\d{4}\",[5,6]],\"AD\":[\"376\",\"00\",\"(?:1|6\\\\d)\\\\d{7}|[135-9]\\\\d{5}\",[6,8,9],[[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"[135-9]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"1\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6\"]]]],\"AE\":[\"971\",\"00\",\"(?:[4-7]\\\\d|9[0-689])\\\\d{7}|800\\\\d{2,9}|[2-4679]\\\\d{7}\",[5,6,7,8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{2,9})\",\"$1 $2\",[\"60|8\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[236]|[479][2-8]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{5})\",\"$1 $2 $3\",[\"[479]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"5\"],\"0$1\"]],\"0\"],\"AF\":[\"93\",\"00\",\"[2-7]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-7]\"],\"0$1\"]],\"0\"],\"AG\":[\"1\",\"011\",\"(?:268|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([457]\\\\d{6})$|1\",\"268$1\",0,\"268\"],\"AI\":[\"1\",\"011\",\"(?:264|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2457]\\\\d{6})$|1\",\"264$1\",0,\"264\"],\"AL\":[\"355\",\"00\",\"(?:700\\\\d\\\\d|900)\\\\d{3}|8\\\\d{5,7}|(?:[2-5]|6\\\\d)\\\\d{7}\",[6,7,8,9],[[\"(\\\\d{3})(\\\\d{3,4})\",\"$1 $2\",[\"80|9\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"4[2-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2358][2-5]|4\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[23578]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"6\"],\"0$1\"]],\"0\"],\"AM\":[\"374\",\"00\",\"(?:[1-489]\\\\d|55|60|77)\\\\d{6}\",[8],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"[89]0\"],\"0 $1\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"2|3[12]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"1|47\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[3-9]\"],\"0$1\"]],\"0\"],\"AO\":[\"244\",\"00\",\"[29]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[29]\"]]]],\"AR\":[\"54\",\"00\",\"(?:11|[89]\\\\d\\\\d)\\\\d{8}|[2368]\\\\d{9}\",[10,11],[[\"(\\\\d{4})(\\\\d{2})(\\\\d{4})\",\"$1 $2-$3\",[\"2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9])\",\"2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8]))|2(?:2[24-9]|3[1-59]|47)\",\"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5[56][46]|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\",\"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|58|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|54(?:4|5[13-7]|6[89])|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:454|85[56])[46]|3(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"],\"0$1\",1],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2-$3\",[\"1\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[68]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2-$3\",[\"[23]\"],\"0$1\",1],[\"(\\\\d)(\\\\d{4})(\\\\d{2})(\\\\d{4})\",\"$2 15-$3-$4\",[\"9(?:2[2-469]|3[3-578])\",\"9(?:2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9]))\",\"9(?:2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8])))|92(?:2[24-9]|3[1-59]|47)\",\"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5(?:[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\",\"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|5(?:4(?:4|5[13-7]|6[89])|[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"],\"0$1\",0,\"$1 $2 $3-$4\"],[\"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$2 15-$3-$4\",[\"91\"],\"0$1\",0,\"$1 $2 $3-$4\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\",\"$1-$2-$3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$2 15-$3-$4\",[\"9\"],\"0$1\",0,\"$1 $2 $3-$4\"]],\"0\",0,\"0?(?:(11|2(?:2(?:02?|[13]|2[13-79]|4[1-6]|5[2457]|6[124-8]|7[1-4]|8[13-6]|9[1267])|3(?:02?|1[467]|2[03-6]|3[13-8]|[49][2-6]|5[2-8]|[67])|4(?:7[3-578]|9)|6(?:[0136]|2[24-6]|4[6-8]?|5[15-8])|80|9(?:0[1-3]|[19]|2\\\\d|3[1-6]|4[02568]?|5[2-4]|6[2-46]|72?|8[23]?))|3(?:3(?:2[79]|6|8[2578])|4(?:0[0-24-9]|[12]|3[5-8]?|4[24-7]|5[4-68]?|6[02-9]|7[126]|8[2379]?|9[1-36-8])|5(?:1|2[1245]|3[237]?|4[1-46-9]|6[2-4]|7[1-6]|8[2-5]?)|6[24]|7(?:[069]|1[1568]|2[15]|3[145]|4[13]|5[14-8]|7[2-57]|8[126])|8(?:[01]|2[15-7]|3[2578]?|4[13-6]|5[4-8]?|6[1-357-9]|7[36-8]?|8[5-8]?|9[124])))15)?\",\"9$1\"],\"AS\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|684|900)\\\\d{7}\",[10],0,\"1\",0,\"([267]\\\\d{6})$|1\",\"684$1\",0,\"684\"],\"AT\":[\"43\",\"00\",\"1\\\\d{3,12}|2\\\\d{6,12}|43(?:(?:0\\\\d|5[02-9])\\\\d{3,9}|2\\\\d{4,5}|[3467]\\\\d{4}|8\\\\d{4,6}|9\\\\d{4,7})|5\\\\d{4,12}|8\\\\d{7,12}|9\\\\d{8,12}|(?:[367]\\\\d|4[0-24-9])\\\\d{4,11}\",[4,5,6,7,8,9,10,11,12,13],[[\"(\\\\d)(\\\\d{3,12})\",\"$1 $2\",[\"1(?:11|[2-9])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})\",\"$1 $2\",[\"517\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,5})\",\"$1 $2\",[\"5[079]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,10})\",\"$1 $2\",[\"(?:31|4)6|51|6(?:48|5[0-3579]|[6-9])|7(?:20|32|8)|[89]\",\"(?:31|4)6|51|6(?:485|5[0-3579]|[6-9])|7(?:20|32|8)|[89]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3,9})\",\"$1 $2\",[\"[2-467]|5[2-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"5\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4,7})\",\"$1 $2 $3\",[\"5\"],\"0$1\"]],\"0\"],\"AU\":[\"61\",\"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\",\"1(?:[0-79]\\\\d{7}(?:\\\\d(?:\\\\d{2})?)?|8[0-24-9]\\\\d{7})|[2-478]\\\\d{8}|1\\\\d{4,7}\",[5,6,7,8,9,10,12],[[\"(\\\\d{2})(\\\\d{3,4})\",\"$1 $2\",[\"16\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3\",[\"16\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"14|4\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[2378]\"],\"(0$1)\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1(?:30|[89])\"]]],\"0\",0,\"(183[12])|0\",0,0,0,[[\"(?:(?:(?:2(?:[0-26-9]\\\\d|3[0-8]|4[02-9]|5[0135-9])|7(?:[013-57-9]\\\\d|2[0-8]))\\\\d|3(?:(?:[0-3589]\\\\d|6[1-9]|7[0-35-9])\\\\d|4(?:[0-578]\\\\d|90)))\\\\d\\\\d|8(?:51(?:0(?:0[03-9]|[12479]\\\\d|3[2-9]|5[0-8]|6[1-9]|8[0-7])|1(?:[0235689]\\\\d|1[0-69]|4[0-589]|7[0-47-9])|2(?:0[0-79]|[18][13579]|2[14-9]|3[0-46-9]|[4-6]\\\\d|7[89]|9[0-4])|3\\\\d\\\\d)|(?:6[0-8]|[78]\\\\d)\\\\d{3}|9(?:[02-9]\\\\d{3}|1(?:(?:[0-58]\\\\d|6[0135-9])\\\\d|7(?:0[0-24-9]|[1-9]\\\\d)|9(?:[0-46-9]\\\\d|5[0-79])))))\\\\d{3}\",[9]],[\"4(?:79[01]|83[0-389]|94[0-4])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\",[9]],[\"180(?:0\\\\d{3}|2)\\\\d{3}\",[7,10]],[\"190[0-26]\\\\d{6}\",[10]],0,0,0,[\"163\\\\d{2,6}\",[5,6,7,8,9]],[\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\",[9]],[\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\",[6,8,10,12]]],\"0011\"],\"AW\":[\"297\",\"00\",\"(?:[25-79]\\\\d\\\\d|800)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[25-9]\"]]]],\"AX\":[\"358\",\"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\",\"2\\\\d{4,9}|35\\\\d{4,5}|(?:60\\\\d\\\\d|800)\\\\d{4,6}|7\\\\d{5,11}|(?:[14]\\\\d|3[0-46-9]|50)\\\\d{4,8}\",[5,6,7,8,9,10,11,12],0,\"0\",0,0,0,0,\"18\",0,\"00\"],\"AZ\":[\"994\",\"00\",\"365\\\\d{6}|(?:[124579]\\\\d|60|88)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"90\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"1[28]|2|365|46\",\"1[28]|2|365[45]|46\",\"1[28]|2|365(?:4|5[02])|46\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[13-9]\"],\"0$1\"]],\"0\"],\"BA\":[\"387\",\"00\",\"6\\\\d{8}|(?:[35689]\\\\d|49|70)\\\\d{6}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6[1-3]|[7-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2-$3\",[\"[3-5]|6[56]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"6\"],\"0$1\"]],\"0\"],\"BB\":[\"1\",\"011\",\"(?:246|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"246$1\",0,\"246\"],\"BD\":[\"880\",\"00\",\"[1-469]\\\\d{9}|8[0-79]\\\\d{7,8}|[2-79]\\\\d{8}|[2-9]\\\\d{7}|[3-9]\\\\d{6}|[57-9]\\\\d{5}\",[6,7,8,9,10],[[\"(\\\\d{2})(\\\\d{4,6})\",\"$1-$2\",[\"31[5-8]|[459]1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,7})\",\"$1-$2\",[\"3(?:[67]|8[013-9])|4(?:6[168]|7|[89][18])|5(?:6[128]|9)|6(?:[15]|28|4[14])|7[2-589]|8(?:0[014-9]|[12])|9[358]|(?:3[2-5]|4[235]|5[2-578]|6[0389]|76|8[3-7]|9[24])1|(?:44|66)[01346-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3,6})\",\"$1-$2\",[\"[13-9]|2[23]\"],\"0$1\"],[\"(\\\\d)(\\\\d{7,8})\",\"$1-$2\",[\"2\"],\"0$1\"]],\"0\"],\"BE\":[\"32\",\"00\",\"4\\\\d{8}|[1-9]\\\\d{7}\",[8,9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"(?:80|9)0\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[239]|4[23]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[15-8]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"4\"],\"0$1\"]],\"0\"],\"BF\":[\"226\",\"00\",\"[025-7]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[025-7]\"]]]],\"BG\":[\"359\",\"00\",\"00800\\\\d{7}|[2-7]\\\\d{6,7}|[89]\\\\d{6,8}|2\\\\d{5}\",[6,7,8,9,12],[[\"(\\\\d)(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"43[1-6]|70[1-9]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\",\"$1 $2 $3\",[\"[356]|4[124-7]|7[1-9]|8[1-6]|9[1-7]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"(?:70|8)0\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1 $2 $3\",[\"43[1-7]|7\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[48]|9[08]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"9\"],\"0$1\"]],\"0\"],\"BH\":[\"973\",\"00\",\"[136-9]\\\\d{7}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[13679]|8[02-4679]\"]]]],\"BI\":[\"257\",\"00\",\"(?:[267]\\\\d|31)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2367]\"]]]],\"BJ\":[\"229\",\"00\",\"(?:01\\\\d|[24-689])\\\\d{7}\",[8,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[24-689]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"0\"]]]],\"BL\":[\"590\",\"00\",\"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\",[9],0,\"0\",0,0,0,0,0,[[\"590(?:2[7-9]|3[3-7]|5[12]|87)\\\\d{4}\"],[\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"],[\"80[0-5]\\\\d{6}\"],0,0,0,0,0,[\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\"BM\":[\"1\",\"011\",\"(?:441|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"441$1\",0,\"441\"],\"BN\":[\"673\",\"00\",\"[2-578]\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-578]\"]]]],\"BO\":[\"591\",\"00(?:1\\\\d)?\",\"8001\\\\d{5}|(?:[2-467]\\\\d|50)\\\\d{6}\",[8,9],[[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"[235]|4[46]\"]],[\"(\\\\d{8})\",\"$1\",[\"[67]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]]],\"0\",0,\"0(1\\\\d)?\"],\"BQ\":[\"599\",\"00\",\"(?:[34]1|7\\\\d)\\\\d{5}\",[7],0,0,0,0,0,0,\"[347]\"],\"BR\":[\"55\",\"00(?:1[245]|2[1-35]|31|4[13]|[56]5|99)\",\"(?:[1-46-9]\\\\d\\\\d|5(?:[0-46-9]\\\\d|5[0-46-9]))\\\\d{8}|[1-9]\\\\d{9}|[3589]\\\\d{8}|[34]\\\\d{7}\",[8,9,10,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"300|4(?:0[02]|37)\",\"4(?:02|37)0|[34]00\"]],[\"(\\\\d{3})(\\\\d{2,3})(\\\\d{4})\",\"$1 $2 $3\",[\"(?:[358]|90)0\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2-$3\",[\"(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])[2-57]\"],\"($1)\"],[\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\",\"$1 $2-$3\",[\"[16][1-9]|[2-57-9]\"],\"($1)\"]],\"0\",0,\"(?:0|90)(?:(1[245]|2[1-35]|31|4[13]|[56]5|99)(\\\\d{10,11}))?\",\"$2\"],\"BS\":[\"1\",\"011\",\"(?:242|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([3-8]\\\\d{6})$|1\",\"242$1\",0,\"242\"],\"BT\":[\"975\",\"00\",\"[17]\\\\d{7}|[2-8]\\\\d{6}\",[7,8],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-68]|7[246]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"1[67]|7\"]]]],\"BW\":[\"267\",\"00\",\"(?:0800|(?:[37]|800)\\\\d)\\\\d{6}|(?:[2-6]\\\\d|90)\\\\d{5}\",[7,8,10],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"90\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[24-6]|3[15-9]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[37]\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]]]],\"BY\":[\"375\",\"810\",\"(?:[12]\\\\d|33|44|902)\\\\d{7}|8(?:0[0-79]\\\\d{5,7}|[1-7]\\\\d{9})|8(?:1[0-489]|[5-79]\\\\d)\\\\d{7}|8[1-79]\\\\d{6,7}|8[0-79]\\\\d{5}|8\\\\d{5}\",[6,7,8,9,10,11],[[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"800\"],\"8 $1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,4})\",\"$1 $2 $3\",[\"800\"],\"8 $1\"],[\"(\\\\d{4})(\\\\d{2})(\\\\d{3})\",\"$1 $2-$3\",[\"1(?:5[169]|6[3-5]|7[179])|2(?:1[35]|2[34]|3[3-5])\",\"1(?:5[169]|6(?:3[1-3]|4|5[125])|7(?:1[3-9]|7[0-24-6]|9[2-7]))|2(?:1[35]|2[34]|3[3-5])\"],\"8 0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"1(?:[56]|7[467])|2[1-3]\"],\"8 0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"[1-4]\"],\"8 0$1\"],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"8 $1\"]],\"8\",0,\"0|80?\",0,0,0,0,\"8~10\"],\"BZ\":[\"501\",\"00\",\"(?:0800\\\\d|[2-8])\\\\d{6}\",[7,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[2-8]\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})(\\\\d{3})\",\"$1-$2-$3-$4\",[\"0\"]]]],\"CA\":[\"1\",\"011\",\"[2-9]\\\\d{9}|3\\\\d{6}\",[7,10],0,\"1\",0,0,0,0,0,[[\"(?:2(?:04|[23]6|[48]9|50|63)|3(?:06|43|54|6[578]|82)|4(?:03|1[68]|[26]8|3[178]|50|74)|5(?:06|1[49]|48|79|8[147])|6(?:04|[18]3|39|47|72)|7(?:0[59]|42|53|78|8[02])|8(?:[06]7|19|25|7[39])|9(?:0[25]|42))[2-9]\\\\d{6}\",[10]],[\"\",[10]],[\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\",[10]],[\"900[2-9]\\\\d{6}\",[10]],[\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|(?:5(?:2[125-9]|33|44|66|77|88)|6(?:22|33))[2-9]\\\\d{6}\",[10]],0,[\"310\\\\d{4}\",[7]],0,[\"600[2-9]\\\\d{6}\",[10]]]],\"CC\":[\"61\",\"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\",\"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\",[6,7,8,9,10,12],0,\"0\",0,\"([59]\\\\d{7})$|0\",\"8$1\",0,0,[[\"8(?:51(?:0(?:02|31|60|89)|1(?:18|76)|223)|91(?:0(?:1[0-2]|29)|1(?:[28]2|50|79)|2(?:10|64)|3(?:[06]8|22)|4[29]8|62\\\\d|70[23]|959))\\\\d{3}\",[9]],[\"4(?:79[01]|83[0-389]|94[0-4])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\",[9]],[\"180(?:0\\\\d{3}|2)\\\\d{3}\",[7,10]],[\"190[0-26]\\\\d{6}\",[10]],0,0,0,0,[\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\",[9]],[\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\",[6,8,10,12]]],\"0011\"],\"CD\":[\"243\",\"00\",\"(?:(?:[189]|5\\\\d)\\\\d|2)\\\\d{7}|[1-68]\\\\d{6}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"88\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"[1-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"5\"],\"0$1\"]],\"0\"],\"CF\":[\"236\",\"00\",\"(?:[27]\\\\d{3}|8776)\\\\d{4}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[278]\"]]]],\"CG\":[\"242\",\"00\",\"222\\\\d{6}|(?:0\\\\d|80)\\\\d{7}\",[9],[[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[02]\"]]]],\"CH\":[\"41\",\"00\",\"8\\\\d{11}|[2-9]\\\\d{8}\",[9,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8[047]|90\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-79]|81\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"8\"],\"0$1\"]],\"0\"],\"CI\":[\"225\",\"00\",\"[02]\\\\d{9}\",[10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d)(\\\\d{5})\",\"$1 $2 $3 $4\",[\"2\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"0\"]]]],\"CK\":[\"682\",\"00\",\"[2-578]\\\\d{4}\",[5],[[\"(\\\\d{2})(\\\\d{3})\",\"$1 $2\",[\"[2-578]\"]]]],\"CL\":[\"56\",\"(?:0|1(?:1[0-69]|2[02-5]|5[13-58]|69|7[0167]|8[018]))0\",\"12300\\\\d{6}|6\\\\d{9,10}|[2-9]\\\\d{8}\",[9,10,11],[[\"(\\\\d{5})(\\\\d{4})\",\"$1 $2\",[\"219\",\"2196\"],\"($1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"44\"]],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2[1-36]\"],\"($1)\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"9[2-9]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"3[2-5]|[47]|5[1-3578]|6[13-57]|8(?:0[1-9]|[1-9])\"],\"($1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"60|8\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"60\"]]]],\"CM\":[\"237\",\"00\",\"[26]\\\\d{8}|88\\\\d{6,7}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"88\"]],[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"[26]|88\"]]]],\"CN\":[\"86\",\"00|1(?:[12]\\\\d|79)\\\\d\\\\d00\",\"(?:(?:1[03-689]|2\\\\d)\\\\d\\\\d|6)\\\\d{8}|1\\\\d{10}|[126]\\\\d{6}(?:\\\\d(?:\\\\d{2})?)?|86\\\\d{5,6}|(?:[3-579]\\\\d|8[0-57-9])\\\\d{5,9}\",[7,8,9,10,11,12],[[\"(\\\\d{2})(\\\\d{5,6})\",\"$1 $2\",[\"(?:10|2[0-57-9])[19]|3(?:[157]|35|49|9[1-68])|4(?:1[124-9]|2[179]|6[47-9]|7|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:07|1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3|4[13]|5[1-5]|7[0-79]|9[0-35-9])|(?:4[35]|59|85)[1-9]\",\"(?:10|2[0-57-9])(?:1[02]|9[56])|8078|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))1\",\"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|80781|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))12\",\"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|807812|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))123\",\"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:078|1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))123\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5,6})\",\"$1 $2\",[\"3(?:[157]|35|49|9[1-68])|4(?:[17]|2[179]|6[47-9]|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]|4[13]|5[1-5])|(?:4[35]|59|85)[1-9]\",\"(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))[19]\",\"85[23](?:10|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:10|9[56])\",\"85[23](?:100|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:100|9[56])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"(?:4|80)0\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"10|2(?:[02-57-9]|1[1-9])\",\"10|2(?:[02-57-9]|1[1-9])\",\"10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"3(?:[3-59]|7[02-68])|4(?:[26-8]|3[3-9]|5[2-9])|5(?:3[03-9]|[468]|7[028]|9[2-46-9])|6|7(?:[0-247]|3[04-9]|5[0-4689]|6[2368])|8(?:[1-358]|9[1-7])|9(?:[013479]|5[1-5])|(?:[34]1|55|79|87)[02-9]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{7,8})\",\"$1 $2\",[\"9\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"80\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[3-578]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"1[3-9]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"[12]\"],\"0$1\",1]],\"0\",0,\"(1(?:[12]\\\\d|79)\\\\d\\\\d)|0\",0,0,0,0,\"00\"],\"CO\":[\"57\",\"00(?:4(?:[14]4|56)|[579])\",\"(?:46|60\\\\d\\\\d)\\\\d{6}|(?:1\\\\d|[39])\\\\d{9}\",[8,10,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"46\"]],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"6|90\"],\"($1)\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"3[0-357]|91\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{7})\",\"$1-$2-$3\",[\"1\"],\"0$1\",0,\"$1 $2 $3\"]],\"0\",0,\"0([3579]|4(?:[14]4|56))?\"],\"CR\":[\"506\",\"00\",\"(?:8\\\\d|90)\\\\d{8}|(?:[24-8]\\\\d{3}|3005)\\\\d{4}\",[8,10],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2-7]|8[3-9]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[89]\"]]],0,0,\"(19(?:0[0-2468]|1[09]|20|66|77|99))\"],\"CU\":[\"53\",\"119\",\"(?:[2-7]|8\\\\d\\\\d)\\\\d{7}|[2-47]\\\\d{6}|[34]\\\\d{5}\",[6,7,8,10],[[\"(\\\\d{2})(\\\\d{4,6})\",\"$1 $2\",[\"2[1-4]|[34]\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{6,7})\",\"$1 $2\",[\"7\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"[56]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"8\"],\"0$1\"]],\"0\"],\"CV\":[\"238\",\"0\",\"(?:[2-59]\\\\d\\\\d|800)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[2-589]\"]]]],\"CW\":[\"599\",\"00\",\"(?:[34]1|60|(?:7|9\\\\d)\\\\d)\\\\d{5}\",[7,8],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[3467]\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"9[4-8]\"]]],0,0,0,0,0,\"[69]\"],\"CX\":[\"61\",\"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\",\"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\",[6,7,8,9,10,12],0,\"0\",0,\"([59]\\\\d{7})$|0\",\"8$1\",0,0,[[\"8(?:51(?:0(?:01|30|59|88)|1(?:17|46|75)|2(?:22|35))|91(?:00[6-9]|1(?:[28]1|49|78)|2(?:09|63)|3(?:12|26|75)|4(?:56|97)|64\\\\d|7(?:0[01]|1[0-2])|958))\\\\d{3}\",[9]],[\"4(?:79[01]|83[0-389]|94[0-4])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\",[9]],[\"180(?:0\\\\d{3}|2)\\\\d{3}\",[7,10]],[\"190[0-26]\\\\d{6}\",[10]],0,0,0,0,[\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\",[9]],[\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\",[6,8,10,12]]],\"0011\"],\"CY\":[\"357\",\"00\",\"(?:[279]\\\\d|[58]0)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[257-9]\"]]]],\"CZ\":[\"420\",\"00\",\"(?:[2-578]\\\\d|60)\\\\d{7}|9\\\\d{8,11}\",[9,10,11,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-8]|9[015-7]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"96\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"9\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"9\"]]]],\"DE\":[\"49\",\"00\",\"[2579]\\\\d{5,14}|49(?:[34]0|69|8\\\\d)\\\\d\\\\d?|49(?:37|49|60|7[089]|9\\\\d)\\\\d{1,3}|49(?:2[024-9]|3[2-689]|7[1-7])\\\\d{1,8}|(?:1|[368]\\\\d|4[0-8])\\\\d{3,13}|49(?:[015]\\\\d|2[13]|31|[46][1-8])\\\\d{1,9}\",[4,5,6,7,8,9,10,11,12,13,14,15],[[\"(\\\\d{2})(\\\\d{3,13})\",\"$1 $2\",[\"3[02]|40|[68]9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,12})\",\"$1 $2\",[\"2(?:0[1-389]|1[124]|2[18]|3[14])|3(?:[35-9][15]|4[015])|906|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\",\"2(?:0[1-389]|12[0-8])|3(?:[35-9][15]|4[015])|906|2(?:[13][14]|2[18])|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{2,11})\",\"$1 $2\",[\"[24-6]|3(?:[3569][02-46-9]|4[2-4679]|7[2-467]|8[2-46-8])|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]\",\"[24-6]|3(?:3(?:0[1-467]|2[127-9]|3[124578]|7[1257-9]|8[1256]|9[145])|4(?:2[135]|4[13578]|9[1346])|5(?:0[14]|2[1-3589]|6[1-4]|7[13468]|8[13568])|6(?:2[1-489]|3[124-6]|6[13]|7[12579]|8[1-356]|9[135])|7(?:2[1-7]|4[145]|6[1-5]|7[1-4])|8(?:21|3[1468]|6|7[1467]|8[136])|9(?:0[12479]|2[1358]|4[134679]|6[1-9]|7[136]|8[147]|9[1468]))|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]|3[68]4[1347]|3(?:47|60)[1356]|3(?:3[46]|46|5[49])[1246]|3[4579]3[1357]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"138\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{2,10})\",\"$1 $2\",[\"3\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5,11})\",\"$1 $2\",[\"181\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{4,10})\",\"$1 $2 $3\",[\"1(?:3|80)|9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7,8})\",\"$1 $2\",[\"1[67]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7,12})\",\"$1 $2\",[\"8\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{6})\",\"$1 $2\",[\"185\",\"1850\",\"18500\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{7})\",\"$1 $2\",[\"18[68]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{7})\",\"$1 $2\",[\"15[1279]\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{6})\",\"$1 $2\",[\"15[03568]\",\"15(?:[0568]|31)\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{8})\",\"$1 $2\",[\"18\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{7,8})\",\"$1 $2 $3\",[\"1(?:6[023]|7)\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{2})(\\\\d{7})\",\"$1 $2 $3\",[\"15[279]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{8})\",\"$1 $2 $3\",[\"15\"],\"0$1\"]],\"0\"],\"DJ\":[\"253\",\"00\",\"(?:2\\\\d|77)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[27]\"]]]],\"DK\":[\"45\",\"00\",\"[2-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-9]\"]]]],\"DM\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|767|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-7]\\\\d{6})$|1\",\"767$1\",0,\"767\"],\"DO\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,0,0,0,\"8001|8[024]9\"],\"DZ\":[\"213\",\"00\",\"(?:[1-4]|[5-79]\\\\d|80)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[1-4]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-8]\"],\"0$1\"]],\"0\"],\"EC\":[\"593\",\"00\",\"1\\\\d{9,10}|(?:[2-7]|9\\\\d)\\\\d{7}\",[8,9,10,11],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2-$3\",[\"[2-7]\"],\"(0$1)\",0,\"$1-$2-$3\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"EE\":[\"372\",\"00\",\"8\\\\d{9}|[4578]\\\\d{7}|(?:[3-8]\\\\d|90)\\\\d{5}\",[7,8,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[369]|4[3-8]|5(?:[0-2]|5[0-478]|6[45])|7[1-9]|88\",\"[369]|4[3-8]|5(?:[02]|1(?:[0-8]|95)|5[0-478]|6(?:4[0-4]|5[1-589]))|7[1-9]|88\"]],[\"(\\\\d{4})(\\\\d{3,4})\",\"$1 $2\",[\"[45]|8(?:00|[1-49])\",\"[45]|8(?:00[1-9]|[1-49])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]]]],\"EG\":[\"20\",\"00\",\"[189]\\\\d{8,9}|[24-6]\\\\d{8}|[135]\\\\d{7}\",[8,9,10],[[\"(\\\\d)(\\\\d{7,8})\",\"$1 $2\",[\"[23]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{6,7})\",\"$1 $2\",[\"1[35]|[4-6]|8[2468]|9[235-7]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{8})\",\"$1 $2\",[\"1\"],\"0$1\"]],\"0\"],\"EH\":[\"212\",\"00\",\"[5-8]\\\\d{8}\",[9],0,\"0\",0,0,0,0,\"528[89]\"],\"ER\":[\"291\",\"00\",\"[178]\\\\d{6}\",[7],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[178]\"],\"0$1\"]],\"0\"],\"ES\":[\"34\",\"00\",\"[5-9]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[89]00\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-9]\"]]]],\"ET\":[\"251\",\"00\",\"(?:11|[2-579]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-579]\"],\"0$1\"]],\"0\"],\"FI\":[\"358\",\"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\",\"[1-35689]\\\\d{4}|7\\\\d{10,11}|(?:[124-7]\\\\d|3[0-46-9])\\\\d{8}|[1-9]\\\\d{5,8}\",[5,6,7,8,9,10,11,12],[[\"(\\\\d{5})\",\"$1\",[\"20[2-59]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,7})\",\"$1 $2\",[\"(?:[1-3]0|[68])0|70[07-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4,8})\",\"$1 $2\",[\"[14]|2[09]|50|7[135]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{6,10})\",\"$1 $2\",[\"7\"],\"0$1\"],[\"(\\\\d)(\\\\d{4,9})\",\"$1 $2\",[\"(?:19|[2568])[1-8]|3(?:0[1-9]|[1-9])|9\"],\"0$1\"]],\"0\",0,0,0,0,\"1[03-79]|[2-9]\",0,\"00\"],\"FJ\":[\"679\",\"0(?:0|52)\",\"45\\\\d{5}|(?:0800\\\\d|[235-9])\\\\d{6}\",[7,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[235-9]|45\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"0\"]]],0,0,0,0,0,0,0,\"00\"],\"FK\":[\"500\",\"00\",\"[2-7]\\\\d{4}\",[5]],\"FM\":[\"691\",\"00\",\"(?:[39]\\\\d\\\\d|820)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[389]\"]]]],\"FO\":[\"298\",\"00\",\"[2-9]\\\\d{5}\",[6],[[\"(\\\\d{6})\",\"$1\",[\"[2-9]\"]]],0,0,\"(10(?:01|[12]0|88))\"],\"FR\":[\"33\",\"00\",\"[1-9]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0 $1\"],[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"[1-79]\"],\"0$1\"]],\"0\"],\"GA\":[\"241\",\"00\",\"(?:[067]\\\\d|11)\\\\d{6}|[2-7]\\\\d{6}\",[7,8],[[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-7]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"0\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"11|[67]\"],\"0$1\"]],0,0,\"0(11\\\\d{6}|60\\\\d{6}|61\\\\d{6}|6[256]\\\\d{6}|7[467]\\\\d{6})\",\"$1\"],\"GB\":[\"44\",\"00\",\"[1-357-9]\\\\d{9}|[18]\\\\d{8}|8\\\\d{6}\",[7,9,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"800\",\"8001\",\"80011\",\"800111\",\"8001111\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"845\",\"8454\",\"84546\",\"845464\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"800\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{4,5})\",\"$1 $2\",[\"1(?:38|5[23]|69|76|94)\",\"1(?:(?:38|69)7|5(?:24|39)|768|946)\",\"1(?:3873|5(?:242|39[4-6])|(?:697|768)[347]|9467)\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5,6})\",\"$1 $2\",[\"1(?:[2-69][02-9]|[78])\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[25]|7(?:0|6[02-9])\",\"[25]|7(?:0|6(?:[03-9]|2[356]))\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"7\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1389]\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"(?:1(?:1(?:3(?:[0-58]\\\\d\\\\d|73[0-35])|4(?:(?:[0-5]\\\\d|70)\\\\d|69[7-9])|(?:(?:5[0-26-9]|[78][0-49])\\\\d|6(?:[0-4]\\\\d|50))\\\\d)|(?:2(?:(?:0[024-9]|2[3-9]|3[3-79]|4[1-689]|[58][02-9]|6[0-47-9]|7[013-9]|9\\\\d)\\\\d|1(?:[0-7]\\\\d|8[0-3]))|(?:3(?:0\\\\d|1[0-8]|[25][02-9]|3[02-579]|[468][0-46-9]|7[1-35-79]|9[2-578])|4(?:0[03-9]|[137]\\\\d|[28][02-57-9]|4[02-69]|5[0-8]|[69][0-79])|5(?:0[1-35-9]|[16]\\\\d|2[024-9]|3[015689]|4[02-9]|5[03-9]|7[0-35-9]|8[0-468]|9[0-57-9])|6(?:0[034689]|1\\\\d|2[0-35689]|[38][013-9]|4[1-467]|5[0-69]|6[13-9]|7[0-8]|9[0-24578])|7(?:0[0246-9]|2\\\\d|3[0236-8]|4[03-9]|5[0-46-9]|6[013-9]|7[0-35-9]|8[024-9]|9[02-9])|8(?:0[35-9]|2[1-57-9]|3[02-578]|4[0-578]|5[124-9]|6[2-69]|7\\\\d|8[02-9]|9[02569])|9(?:0[02-589]|[18]\\\\d|2[02-689]|3[1-57-9]|4[2-9]|5[0-579]|6[2-47-9]|7[0-24578]|9[2-57]))\\\\d)\\\\d)|2(?:0[013478]|3[0189]|4[017]|8[0-46-9]|9[0-2])\\\\d{3})\\\\d{4}|1(?:2(?:0(?:46[1-4]|87[2-9])|545[1-79]|76(?:2\\\\d|3[1-8]|6[1-6])|9(?:7(?:2[0-4]|3[2-5])|8(?:2[2-8]|7[0-47-9]|8[3-5])))|3(?:6(?:38[2-5]|47[23])|8(?:47[04-9]|64[0157-9]))|4(?:044[1-7]|20(?:2[23]|8\\\\d)|6(?:0(?:30|5[2-57]|6[1-8]|7[2-8])|140)|8(?:052|87[1-3]))|5(?:2(?:4(?:3[2-79]|6\\\\d)|76\\\\d)|6(?:26[06-9]|686))|6(?:06(?:4\\\\d|7[4-79])|295[5-7]|35[34]\\\\d|47(?:24|61)|59(?:5[08]|6[67]|74)|9(?:55[0-4]|77[23]))|7(?:26(?:6[13-9]|7[0-7])|(?:442|688)\\\\d|50(?:2[0-3]|[3-68]2|76))|8(?:27[56]\\\\d|37(?:5[2-5]|8[239])|843[2-58])|9(?:0(?:0(?:6[1-8]|85)|52\\\\d)|3583|4(?:66[1-8]|9(?:2[01]|81))|63(?:23|3[1-4])|9561))\\\\d{3}\",[9,10]],[\"7(?:457[0-57-9]|700[01]|911[028])\\\\d{5}|7(?:[1-3]\\\\d\\\\d|4(?:[0-46-9]\\\\d|5[0-689])|5(?:0[0-8]|[13-9]\\\\d|2[0-35-9])|7(?:0[1-9]|[1-7]\\\\d|8[02-9]|9[0-689])|8(?:[014-9]\\\\d|[23][0-8])|9(?:[024-9]\\\\d|1[02-9]|3[0-689]))\\\\d{6}\",[10]],[\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"],[\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[2-49]))\\\\d{7}|845464\\\\d\",[7,10]],[\"70\\\\d{8}\",[10]],0,[\"(?:3[0347]|55)\\\\d{8}\",[10]],[\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\",[10]],[\"56\\\\d{8}\",[10]]],0,\" x\"],\"GD\":[\"1\",\"011\",\"(?:473|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"473$1\",0,\"473\"],\"GE\":[\"995\",\"00\",\"(?:[3-57]\\\\d\\\\d|800)\\\\d{6}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"70\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"32\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[57]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[348]\"],\"0$1\"]],\"0\"],\"GF\":[\"594\",\"00\",\"(?:[56]94\\\\d|7093)\\\\d{5}|(?:80|9\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-7]|9[47]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[89]\"],\"0$1\"]],\"0\"],\"GG\":[\"44\",\"00\",\"(?:1481|[357-9]\\\\d{3})\\\\d{6}|8\\\\d{6}(?:\\\\d{2})?\",[7,9,10],0,\"0\",0,\"([25-9]\\\\d{5})$|0\",\"1481$1\",0,0,[[\"1481[25-9]\\\\d{5}\",[10]],[\"7(?:(?:781|839)\\\\d|911[17])\\\\d{5}\",[10]],[\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"],[\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[0-3]))\\\\d{7}|845464\\\\d\",[7,10]],[\"70\\\\d{8}\",[10]],0,[\"(?:3[0347]|55)\\\\d{8}\",[10]],[\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\",[10]],[\"56\\\\d{8}\",[10]]]],\"GH\":[\"233\",\"00\",\"(?:[235]\\\\d{3}|800)\\\\d{5}\",[8,9],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[235]\"],\"0$1\"]],\"0\"],\"GI\":[\"350\",\"00\",\"(?:[25]\\\\d|60)\\\\d{6}\",[8],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"2\"]]]],\"GL\":[\"299\",\"00\",\"(?:19|[2-689]\\\\d|70)\\\\d{4}\",[6],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"19|[2-9]\"]]]],\"GM\":[\"220\",\"00\",\"[2-9]\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-9]\"]]]],\"GN\":[\"224\",\"00\",\"722\\\\d{6}|(?:3|6\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"3\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[67]\"]]]],\"GP\":[\"590\",\"00\",\"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-79]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"590(?:0[1-68]|[14][0-24-9]|2[0-68]|3[1-9]|5[3-579]|[68][0-689]|7[08]|9\\\\d)\\\\d{4}\"],[\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"],[\"80[0-5]\\\\d{6}\"],0,0,0,0,0,[\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\"GQ\":[\"240\",\"00\",\"222\\\\d{6}|(?:3\\\\d|55|[89]0)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[235]\"]],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"[89]\"]]]],\"GR\":[\"30\",\"00\",\"5005000\\\\d{3}|8\\\\d{9,11}|(?:[269]\\\\d|70)\\\\d{8}\",[10,11,12],[[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"21|7\"]],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"2(?:2|3[2-57-9]|4[2-469]|5[2-59]|6[2-9]|7[2-69]|8[2-49])|5\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2689]\"]],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{5})\",\"$1 $2 $3\",[\"8\"]]]],\"GT\":[\"502\",\"00\",\"80\\\\d{6}|(?:1\\\\d{3}|[2-7])\\\\d{7}\",[8,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2-8]\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]]]],\"GU\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|671|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"671$1\",0,\"671\"],\"GW\":[\"245\",\"00\",\"[49]\\\\d{8}|4\\\\d{6}\",[7,9],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"40\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[49]\"]]]],\"GY\":[\"592\",\"001\",\"(?:[2-8]\\\\d{3}|9008)\\\\d{3}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-9]\"]]]],\"HK\":[\"852\",\"00(?:30|5[09]|[126-9]?)\",\"8[0-46-9]\\\\d{6,7}|9\\\\d{4,7}|(?:[2-7]|9\\\\d{3})\\\\d{7}\",[5,6,7,8,9,11],[[\"(\\\\d{3})(\\\\d{2,5})\",\"$1 $2\",[\"900\",\"9003\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2-7]|8[1-4]|9(?:0[1-9]|[1-8])\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"9\"]]],0,0,0,0,0,0,0,\"00\"],\"HN\":[\"504\",\"00\",\"8\\\\d{10}|[237-9]\\\\d{7}\",[8,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"[237-9]\"]]]],\"HR\":[\"385\",\"00\",\"[2-69]\\\\d{8}|80\\\\d{5,7}|[1-79]\\\\d{7}|6\\\\d{6}\",[7,8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"6[01]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"6|7[245]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-57]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"],\"0$1\"]],\"0\"],\"HT\":[\"509\",\"00\",\"(?:[2-489]\\\\d|55)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-589]\"]]]],\"HU\":[\"36\",\"00\",\"[235-7]\\\\d{8}|[1-9]\\\\d{7}\",[8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"(06 $1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[27][2-9]|3[2-7]|4[24-9]|5[2-79]|6|8[2-57-9]|9[2-69]\"],\"(06 $1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-9]\"],\"06 $1\"]],\"06\"],\"ID\":[\"62\",\"00[89]\",\"00[1-9]\\\\d{9,14}|(?:[1-36]|8\\\\d{5})\\\\d{6}|00\\\\d{9}|[1-9]\\\\d{8,10}|[2-9]\\\\d{7}\",[7,8,9,10,11,12,13,14,15,16,17],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"15\"]],[\"(\\\\d{2})(\\\\d{5,9})\",\"$1 $2\",[\"2[124]|[36]1\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{5,7})\",\"$1 $2\",[\"800\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5,8})\",\"$1 $2\",[\"[2-79]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{3})\",\"$1-$2-$3\",[\"8[1-35-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6,8})\",\"$1 $2\",[\"1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"804\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"80\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\",\"$1-$2-$3\",[\"8\"],\"0$1\"]],\"0\"],\"IE\":[\"353\",\"00\",\"(?:1\\\\d|[2569])\\\\d{6,8}|4\\\\d{6,9}|7\\\\d{8}|8\\\\d{8,9}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"2[24-9]|47|58|6[237-9]|9[35-9]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[45]0\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2569]|4[1-69]|7[14]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"70\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"81\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[78]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"4\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"IL\":[\"972\",\"0(?:0|1[2-9])\",\"1\\\\d{6}(?:\\\\d{3,5})?|[57]\\\\d{8}|[1-489]\\\\d{7}\",[7,8,9,10,11,12],[[\"(\\\\d{4})(\\\\d{3})\",\"$1-$2\",[\"125\"]],[\"(\\\\d{4})(\\\\d{2})(\\\\d{2})\",\"$1-$2-$3\",[\"121\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[2-489]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[57]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1-$2-$3\",[\"12\"]],[\"(\\\\d{4})(\\\\d{6})\",\"$1-$2\",[\"159\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1-$2-$3-$4\",[\"1[7-9]\"]],[\"(\\\\d{3})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\",\"$1-$2 $3-$4\",[\"15\"]]],\"0\"],\"IM\":[\"44\",\"00\",\"1624\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\",[10],0,\"0\",0,\"([25-8]\\\\d{5})$|0\",\"1624$1\",0,\"74576|(?:16|7[56])24\"],\"IN\":[\"91\",\"00\",\"(?:000800|[2-9]\\\\d\\\\d)\\\\d{7}|1\\\\d{7,12}\",[8,9,10,11,12,13],[[\"(\\\\d{8})\",\"$1\",[\"5(?:0|2[23]|3[03]|[67]1|88)\",\"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|888)\",\"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|8888)\"],0,1],[\"(\\\\d{4})(\\\\d{4,5})\",\"$1 $2\",[\"180\",\"1800\"],0,1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"140\"],0,1],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"11|2[02]|33|4[04]|79[1-7]|80[2-46]\",\"11|2[02]|33|4[04]|79(?:[1-6]|7[19])|80(?:[2-4]|6[0-589])\",\"11|2[02]|33|4[04]|79(?:[124-6]|3(?:[02-9]|1[0-24-9])|7(?:1|9[1-6]))|80(?:[2-4]|6[0-589])\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1(?:2[0-249]|3[0-25]|4[145]|[68]|7[1257])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|5[12]|[78]1)|6(?:12|[2-4]1|5[17]|6[13]|80)|7(?:12|3[134]|4[47]|61|88)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91)|(?:43|59|75)[15]|(?:1[59]|29|67|72)[14]\",\"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|674|7(?:(?:2[14]|3[34]|5[15])[2-6]|61[346]|88[0-8])|8(?:70[2-6]|84[235-7]|91[3-7])|(?:1(?:29|60|8[06])|261|552|6(?:12|[2-47]1|5[17]|6[13]|80)|7(?:12|31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))[2-7]\",\"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|6(?:12(?:[2-6]|7[0-8])|74[2-7])|7(?:(?:2[14]|5[15])[2-6]|3171|61[346]|88(?:[2-7]|82))|8(?:70[2-6]|84(?:[2356]|7[19])|91(?:[3-6]|7[19]))|73[134][2-6]|(?:74[47]|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[2-6]|7[19])|(?:1(?:29|60|8[06])|261|552|6(?:[2-4]1|5[17]|6[13]|7(?:1|4[0189])|80)|7(?:12|88[01]))[2-7]\"],\"0$1\",1],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2[2457-9]|3[2-5]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1[013-9]|28|3[129]|4[1-35689]|5[29]|6[02-5]|70)|807\",\"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2(?:[2457]|84|95)|3(?:[2-4]|55)|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1(?:[013-8]|9[6-9])|28[6-8]|3(?:17|2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4|5[0-367])|70[13-7])|807[19]\",\"1(?:[2-479]|5(?:[0236-9]|5[013-9]))|[2-5]|6(?:2(?:84|95)|355|83)|73179|807(?:1|9[1-3])|(?:1552|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[124-6])\\\\d|7(?:1(?:[013-8]\\\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\\\d|5[0-367])|70[13-7]))[2-7]\"],\"0$1\",1],[\"(\\\\d{5})(\\\\d{5})\",\"$1 $2\",[\"[6-9]\"],\"0$1\",1],[\"(\\\\d{4})(\\\\d{2,4})(\\\\d{4})\",\"$1 $2 $3\",[\"1(?:6|8[06])\",\"1(?:6|8[06]0)\"],0,1],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"18\"],0,1]],\"0\"],\"IO\":[\"246\",\"00\",\"3\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"3\"]]]],\"IQ\":[\"964\",\"00\",\"(?:1|7\\\\d\\\\d)\\\\d{7}|[2-6]\\\\d{7,8}\",[8,9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-6]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\"],\"IR\":[\"98\",\"00\",\"[1-9]\\\\d{9}|(?:[1-8]\\\\d\\\\d|9)\\\\d{3,4}\",[4,5,6,7,10],[[\"(\\\\d{4,5})\",\"$1\",[\"96\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4,5})\",\"$1 $2\",[\"(?:1[137]|2[13-68]|3[1458]|4[145]|5[1468]|6[16]|7[1467]|8[13467])[12689]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-8]\"],\"0$1\"]],\"0\"],\"IS\":[\"354\",\"00|1(?:0(?:01|[12]0)|100)\",\"(?:38\\\\d|[4-9])\\\\d{6}\",[7,9],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[4-9]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"3\"]]],0,0,0,0,0,0,0,\"00\"],\"IT\":[\"39\",\"00\",\"0\\\\d{5,10}|1\\\\d{8,10}|3(?:[0-8]\\\\d{7,10}|9\\\\d{7,8})|(?:43|55|70)\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?\",[6,7,8,9,10,11,12],[[\"(\\\\d{2})(\\\\d{4,6})\",\"$1 $2\",[\"0[26]\"]],[\"(\\\\d{3})(\\\\d{3,6})\",\"$1 $2\",[\"0[13-57-9][0159]|8(?:03|4[17]|9[2-5])\",\"0[13-57-9][0159]|8(?:03|4[17]|9(?:2|3[04]|[45][0-4]))\"]],[\"(\\\\d{4})(\\\\d{2,6})\",\"$1 $2\",[\"0(?:[13-579][2-46-8]|8[236-8])\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"894\"]],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"0[26]|5\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"1(?:44|[679])|[378]|43\"]],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"0[13-57-9][0159]|14\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{5})\",\"$1 $2 $3\",[\"0[26]\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\",\"$1 $2 $3\",[\"3\"]]],0,0,0,0,0,0,[[\"0669[0-79]\\\\d{1,6}|0(?:1(?:[0159]\\\\d|[27][1-5]|31|4[1-4]|6[1356]|8[2-57])|2\\\\d\\\\d|3(?:[0159]\\\\d|2[1-4]|3[12]|[48][1-6]|6[2-59]|7[1-7])|4(?:[0159]\\\\d|[23][1-9]|4[245]|6[1-5]|7[1-4]|81)|5(?:[0159]\\\\d|2[1-5]|3[2-6]|4[1-79]|6[4-6]|7[1-578]|8[3-8])|6(?:[0-57-9]\\\\d|6[0-8])|7(?:[0159]\\\\d|2[12]|3[1-7]|4[2-46]|6[13569]|7[13-6]|8[1-59])|8(?:[0159]\\\\d|2[3-578]|3[1-356]|[6-8][1-5])|9(?:[0159]\\\\d|[238][1-5]|4[12]|6[1-8]|7[1-6]))\\\\d{2,7}\",[6,7,8,9,10,11]],[\"3[2-9]\\\\d{7,8}|(?:31|43)\\\\d{8}\",[9,10]],[\"80(?:0\\\\d{3}|3)\\\\d{3}\",[6,9]],[\"(?:0878\\\\d{3}|89(?:2\\\\d|3[04]|4(?:[0-4]|[5-9]\\\\d\\\\d)|5[0-4]))\\\\d\\\\d|(?:1(?:44|6[346])|89(?:38|5[5-9]|9))\\\\d{6}\",[6,8,9,10]],[\"1(?:78\\\\d|99)\\\\d{6}\",[9,10]],[\"3[2-8]\\\\d{9,10}\",[11,12]],0,0,[\"55\\\\d{8}\",[10]],[\"84(?:[08]\\\\d{3}|[17])\\\\d{3}\",[6,9]]]],\"JE\":[\"44\",\"00\",\"1534\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\",[10],0,\"0\",0,\"([0-24-8]\\\\d{5})$|0\",\"1534$1\",0,0,[[\"1534[0-24-8]\\\\d{5}\"],[\"7(?:(?:(?:50|82)9|937)\\\\d|7(?:00[378]|97\\\\d))\\\\d{5}\"],[\"80(?:07(?:35|81)|8901)\\\\d{4}\"],[\"(?:8(?:4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|90(?:066[59]|1810|71(?:07|55)))\\\\d{4}\"],[\"701511\\\\d{4}\"],0,[\"(?:3(?:0(?:07(?:35|81)|8901)|3\\\\d{4}|4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|55\\\\d{4})\\\\d{4}\"],[\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\"],[\"56\\\\d{8}\"]]],\"JM\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|658|900)\\\\d{7}\",[10],0,\"1\",0,0,0,0,\"658|876\"],\"JO\":[\"962\",\"00\",\"(?:(?:[2689]|7\\\\d)\\\\d|32|53)\\\\d{6}\",[8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2356]|87\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{5,6})\",\"$1 $2\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"70\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\"],\"JP\":[\"81\",\"010\",\"00[1-9]\\\\d{6,14}|[257-9]\\\\d{9}|(?:00|[1-9]\\\\d\\\\d)\\\\d{6}\",[8,9,10,11,12,13,14,15,16,17],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1-$2-$3\",[\"(?:12|57|99)0\"],\"0$1\"],[\"(\\\\d{4})(\\\\d)(\\\\d{4})\",\"$1-$2-$3\",[\"1(?:26|3[79]|4[56]|5[4-68]|6[3-5])|499|5(?:76|97)|746|8(?:3[89]|47|51)|9(?:80|9[16])\",\"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:76|97)9|7468|8(?:3(?:8[7-9]|96)|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\",\"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:769|979[2-69])|7468|8(?:3(?:8[7-9]|96[2457-9])|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"60\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1-$2-$3\",[\"[36]|4(?:2[09]|7[01])\",\"[36]|4(?:2(?:0|9[02-69])|7(?:0[019]|1))\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"1(?:1|5[45]|77|88|9[69])|2(?:2[1-37]|3[0-269]|4[59]|5|6[24]|7[1-358]|8[1369]|9[0-38])|4(?:[28][1-9]|3[0-57]|[45]|6[248]|7[2-579]|9[29])|5(?:2|3[0459]|4[0-369]|5[29]|8[02389]|9[0-389])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9[2-6])|8(?:2[124589]|3[26-9]|49|51|6|7[0-468]|8[68]|9[019])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9[1-489])\",\"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2(?:[127]|3[014-9])|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9[19])|62|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|8[1-9]|9[29])|5(?:2|3(?:[045]|9[0-8])|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0-2469])|3(?:[29]|60)|49|51|6(?:[0-24]|36|5[0-3589]|7[23]|9[01459])|7[0-468]|8[68])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9(?:[1289]|3[34]|4[0178]))|(?:264|837)[016-9]|2(?:57|93)[015-9]|(?:25[0468]|422|838)[01]|(?:47[59]|59[89]|8(?:6[68]|9))[019]\",\"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2[127]|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9(?:17|99))|6(?:2|4[016-9])|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|9[29])|5(?:2|3(?:[045]|9(?:[0-58]|6[4-9]|7[0-35689]))|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0169])|3(?:[29]|60|7(?:[017-9]|6[6-8]))|49|51|6(?:[0-24]|36[2-57-9]|5(?:[0-389]|5[23])|6(?:[01]|9[178])|7(?:2[2-468]|3[78])|9[0145])|7[0-468]|8[68])|9(?:4[15]|5[138]|7[156]|8[189]|9(?:[1289]|3(?:31|4[357])|4[0178]))|(?:8294|96)[1-3]|2(?:57|93)[015-9]|(?:223|8699)[014-9]|(?:25[0468]|422|838)[01]|(?:48|8292|9[23])[1-9]|(?:47[59]|59[89]|8(?:68|9))[019]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1-$2-$3\",[\"[14]|[289][2-9]|5[3-9]|7[2-4679]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"800\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1-$2-$3\",[\"[257-9]\"],\"0$1\"]],\"0\",0,\"(000[259]\\\\d{6})$|(?:(?:003768)0?)|0\",\"$1\"],\"KE\":[\"254\",\"000\",\"(?:[17]\\\\d\\\\d|900)\\\\d{6}|(?:2|80)0\\\\d{6,7}|[4-6]\\\\d{6,8}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{5,7})\",\"$1 $2\",[\"[24-6]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"[17]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"]],\"0\"],\"KG\":[\"996\",\"00\",\"8\\\\d{9}|[235-9]\\\\d{8}\",[9,10],[[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"3(?:1[346]|[24-79])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[235-79]|88\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d)(\\\\d{2,3})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"KH\":[\"855\",\"00[14-9]\",\"1\\\\d{9}|[1-9]\\\\d{7,8}\",[8,9,10],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[1-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"KI\":[\"686\",\"00\",\"(?:[37]\\\\d|6[0-79])\\\\d{6}|(?:[2-48]\\\\d|50)\\\\d{3}\",[5,8],0,\"0\"],\"KM\":[\"269\",\"00\",\"[3478]\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[3478]\"]]]],\"KN\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-7]\\\\d{6})$|1\",\"869$1\",0,\"869\"],\"KP\":[\"850\",\"00|99\",\"85\\\\d{6}|(?:19\\\\d|[2-7])\\\\d{7}\",[8,10],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-7]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"]],\"0\"],\"KR\":[\"82\",\"00(?:[125689]|3(?:[46]5|91)|7(?:00|27|3|55|6[126]))\",\"00[1-9]\\\\d{8,11}|(?:[12]|5\\\\d{3})\\\\d{7}|[13-6]\\\\d{9}|(?:[1-6]\\\\d|80)\\\\d{7}|[3-6]\\\\d{4,5}|(?:00|7)0\\\\d{8}\",[5,6,8,9,10,11,12,13,14],[[\"(\\\\d{2})(\\\\d{3,4})\",\"$1-$2\",[\"(?:3[1-3]|[46][1-4]|5[1-5])1\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"1\"]],[\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\",\"$1-$2-$3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[36]0|8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1-$2-$3\",[\"[1346]|5[1-5]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1-$2-$3\",[\"[57]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\",\"$1-$2-$3\",[\"5\"],\"0$1\"]],\"0\",0,\"0(8(?:[1-46-8]|5\\\\d\\\\d))?\"],\"KW\":[\"965\",\"00\",\"18\\\\d{5}|(?:[2569]\\\\d|41)\\\\d{6}\",[7,8],[[\"(\\\\d{4})(\\\\d{3,4})\",\"$1 $2\",[\"[169]|2(?:[235]|4[1-35-9])|52\"]],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[245]\"]]]],\"KY\":[\"1\",\"011\",\"(?:345|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"345$1\",0,\"345\"],\"KZ\":[\"7\",\"810\",\"(?:33622|8\\\\d{8})\\\\d{5}|[78]\\\\d{9}\",[10,14],0,\"8\",0,0,0,0,\"33|7\",0,\"8~10\"],\"LA\":[\"856\",\"00\",\"[23]\\\\d{9}|3\\\\d{8}|(?:[235-8]\\\\d|41)\\\\d{6}\",[8,9,10],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2[13]|3[14]|[4-8]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"30[0135-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"[23]\"],\"0$1\"]],\"0\"],\"LB\":[\"961\",\"00\",\"[27-9]\\\\d{7}|[13-9]\\\\d{6}\",[7,8],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[13-69]|7(?:[2-57]|62|8[0-7]|9[04-9])|8[02-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[27-9]\"]]],\"0\"],\"LC\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|758|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-8]\\\\d{6})$|1\",\"758$1\",0,\"758\"],\"LI\":[\"423\",\"00\",\"[68]\\\\d{8}|(?:[2378]\\\\d|90)\\\\d{5}\",[7,9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[2379]|8(?:0[09]|7)\",\"[2379]|8(?:0(?:02|9)|7)\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"69\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6\"]]],\"0\",0,\"(1001)|0\"],\"LK\":[\"94\",\"00\",\"[1-9]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[1-689]\"],\"0$1\"]],\"0\"],\"LR\":[\"231\",\"00\",\"(?:[245]\\\\d|33|77|88)\\\\d{7}|(?:2\\\\d|[4-6])\\\\d{6}\",[7,8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"4[67]|[56]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-578]\"],\"0$1\"]],\"0\"],\"LS\":[\"266\",\"00\",\"(?:[256]\\\\d\\\\d|800)\\\\d{5}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2568]\"]]]],\"LT\":[\"370\",\"00\",\"(?:[3469]\\\\d|52|[78]0)\\\\d{6}\",[8],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"52[0-7]\"],\"(0-$1)\",1],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"[7-9]\"],\"0 $1\",1],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"37|4(?:[15]|6[1-8])\"],\"(0-$1)\",1],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[3-6]\"],\"(0-$1)\",1]],\"0\",0,\"[08]\"],\"LU\":[\"352\",\"00\",\"35[013-9]\\\\d{4,8}|6\\\\d{8}|35\\\\d{2,4}|(?:[2457-9]\\\\d|3[0-46-9])\\\\d{2,9}\",[4,5,6,7,8,9,10,11],[[\"(\\\\d{2})(\\\\d{3})\",\"$1 $2\",[\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"20[2-689]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\",\"$1 $2 $3 $4\",[\"2(?:[0367]|4[3-8])\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"80[01]|90[015]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"20\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\",\"$1 $2 $3 $4 $5\",[\"2(?:[0367]|4[3-8])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,5})\",\"$1 $2 $3 $4\",[\"[3-57]|8[13-9]|9(?:0[89]|[2-579])|(?:2|80)[2-9]\"]]],0,0,\"(15(?:0[06]|1[12]|[35]5|4[04]|6[26]|77|88|99)\\\\d)\"],\"LV\":[\"371\",\"00\",\"(?:[268]\\\\d|90)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[269]|8[01]\"]]]],\"LY\":[\"218\",\"00\",\"[2-9]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{7})\",\"$1-$2\",[\"[2-9]\"],\"0$1\"]],\"0\"],\"MA\":[\"212\",\"00\",\"[5-8]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"5[45]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5})\",\"$1-$2\",[\"5(?:2[2-46-9]|3[3-9]|9)|8(?:0[89]|92)\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1-$2\",[\"8\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1-$2\",[\"[5-7]\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"5(?:2(?:[0-25-79]\\\\d|3[1-578]|4[02-46-8]|8[0235-7])|3(?:[0-47]\\\\d|5[02-9]|6[02-8]|8[014-9]|9[3-9])|(?:4[067]|5[03])\\\\d)\\\\d{5}\"],[\"(?:6(?:[0-79]\\\\d|8[0-247-9])|7(?:[0167]\\\\d|2[0-467]|5[0-3]|8[0-5]))\\\\d{6}\"],[\"80[0-7]\\\\d{6}\"],[\"89\\\\d{7}\"],0,0,0,0,[\"(?:592(?:4[0-2]|93)|80[89]\\\\d\\\\d)\\\\d{4}\"]]],\"MC\":[\"377\",\"00\",\"(?:[3489]|6\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"4\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[389]\"]],[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"6\"],\"0$1\"]],\"0\"],\"MD\":[\"373\",\"00\",\"(?:[235-7]\\\\d|[89]0)\\\\d{6}\",[8],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"22|3\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"[25-7]\"],\"0$1\"]],\"0\"],\"ME\":[\"382\",\"00\",\"(?:20|[3-79]\\\\d)\\\\d{6}|80\\\\d{6,7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-9]\"],\"0$1\"]],\"0\"],\"MF\":[\"590\",\"00\",\"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\",[9],0,\"0\",0,0,0,0,0,[[\"590(?:0[079]|[14]3|[27][79]|3[03-7]|5[0-268]|87)\\\\d{4}\"],[\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"],[\"80[0-5]\\\\d{6}\"],0,0,0,0,0,[\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\"MG\":[\"261\",\"00\",\"[23]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[23]\"],\"0$1\"]],\"0\",0,\"([24-9]\\\\d{6})$|0\",\"20$1\"],\"MH\":[\"692\",\"011\",\"329\\\\d{4}|(?:[256]\\\\d|45)\\\\d{5}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[2-6]\"]]],\"1\"],\"MK\":[\"389\",\"00\",\"[2-578]\\\\d{7}\",[8],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"2|34[47]|4(?:[37]7|5[47]|64)\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[347]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[58]\"],\"0$1\"]],\"0\"],\"ML\":[\"223\",\"00\",\"[24-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[24-9]\"]]]],\"MM\":[\"95\",\"00\",\"1\\\\d{5,7}|95\\\\d{6}|(?:[4-7]|9[0-46-9])\\\\d{6,8}|(?:2|8\\\\d)\\\\d{5,8}\",[6,7,8,9,10],[[\"(\\\\d)(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"16|2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"4(?:[2-46]|5[3-5])|5|6(?:[1-689]|7[235-7])|7(?:[0-4]|5[2-7])|8[1-5]|(?:60|86)[23]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[12]|452|678|86\",\"[12]|452|6788|86\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[4-7]|8[1-35]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4,6})\",\"$1 $2 $3\",[\"9(?:2[0-4]|[35-9]|4[137-9])\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"92\"],\"0$1\"],[\"(\\\\d)(\\\\d{5})(\\\\d{4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"]],\"0\"],\"MN\":[\"976\",\"001\",\"[12]\\\\d{7,9}|[5-9]\\\\d{7}\",[8,9,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"[12]1\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[5-9]\"]],[\"(\\\\d{3})(\\\\d{5,6})\",\"$1 $2\",[\"[12]2[1-3]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5,6})\",\"$1 $2\",[\"[12](?:27|3[2-8]|4[2-68]|5[1-4689])\",\"[12](?:27|3[2-8]|4[2-68]|5[1-4689])[0-3]\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{4,5})\",\"$1 $2\",[\"[12]\"],\"0$1\"]],\"0\"],\"MO\":[\"853\",\"00\",\"0800\\\\d{3}|(?:28|[68]\\\\d)\\\\d{6}\",[7,8],[[\"(\\\\d{4})(\\\\d{3})\",\"$1 $2\",[\"0\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[268]\"]]]],\"MP\":[\"1\",\"011\",\"[58]\\\\d{9}|(?:67|90)0\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"670$1\",0,\"670\"],\"MQ\":[\"596\",\"00\",\"(?:596\\\\d|7091)\\\\d{5}|(?:69|[89]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-79]|8(?:0[6-9]|[36])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"MR\":[\"222\",\"00\",\"(?:[2-4]\\\\d\\\\d|800)\\\\d{5}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-48]\"]]]],\"MS\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|664|900)\\\\d{7}\",[10],0,\"1\",0,\"([34]\\\\d{6})$|1\",\"664$1\",0,\"664\"],\"MT\":[\"356\",\"00\",\"3550\\\\d{4}|(?:[2579]\\\\d\\\\d|800)\\\\d{5}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2357-9]\"]]]],\"MU\":[\"230\",\"0(?:0|[24-7]0|3[03])\",\"(?:[57]|8\\\\d\\\\d)\\\\d{7}|[2-468]\\\\d{6}\",[7,8,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-46]|8[013]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[57]\"]],[\"(\\\\d{5})(\\\\d{5})\",\"$1 $2\",[\"8\"]]],0,0,0,0,0,0,0,\"020\"],\"MV\":[\"960\",\"0(?:0|19)\",\"(?:800|9[0-57-9]\\\\d)\\\\d{7}|[34679]\\\\d{6}\",[7,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[34679]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"]]],0,0,0,0,0,0,0,\"00\"],\"MW\":[\"265\",\"00\",\"(?:[1289]\\\\d|31|77)\\\\d{7}|1\\\\d{6}\",[7,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1[2-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[137-9]\"],\"0$1\"]],\"0\"],\"MX\":[\"52\",\"0[09]\",\"[2-9]\\\\d{9}\",[10],[[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"33|5[56]|81\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-9]\"]]],0,0,0,0,0,0,0,\"00\"],\"MY\":[\"60\",\"00\",\"1\\\\d{8,9}|(?:3\\\\d|[4-9])\\\\d{7}\",[8,9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1-$2 $3\",[\"[4-79]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1-$2 $3\",[\"1(?:[02469]|[378][1-9]|53)|8\",\"1(?:[02469]|[37][1-9]|53|8(?:[1-46-9]|5[7-9]))|8\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1-$2 $3\",[\"3\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1-$2-$3-$4\",[\"1(?:[367]|80)\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2 $3\",[\"15\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1-$2 $3\",[\"1\"],\"0$1\"]],\"0\"],\"MZ\":[\"258\",\"00\",\"(?:2|8\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2|8[2-79]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]]]],\"NA\":[\"264\",\"00\",\"[68]\\\\d{7,8}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"88\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"6\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"87\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"],\"0$1\"]],\"0\"],\"NC\":[\"687\",\"00\",\"(?:050|[2-57-9]\\\\d\\\\d)\\\\d{3}\",[6],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1.$2.$3\",[\"[02-57-9]\"]]]],\"NE\":[\"227\",\"00\",\"[027-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"08\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[089]|2[013]|7[0467]\"]]]],\"NF\":[\"672\",\"00\",\"[13]\\\\d{5}\",[6],[[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"1[0-3]\"]],[\"(\\\\d)(\\\\d{5})\",\"$1 $2\",[\"[13]\"]]],0,0,\"([0-258]\\\\d{4})$\",\"3$1\"],\"NG\":[\"234\",\"009\",\"38\\\\d{6}|[78]\\\\d{9,13}|(?:20|9\\\\d)\\\\d{8}\",[8,10,11,12,13,14],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\",\"$1 $2 $3\",[\"3\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[7-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"20[129]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\",\"$1 $2 $3\",[\"[78]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5})(\\\\d{5,6})\",\"$1 $2 $3\",[\"[78]\"],\"0$1\"]],\"0\"],\"NI\":[\"505\",\"00\",\"(?:1800|[25-8]\\\\d{3})\\\\d{4}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[125-8]\"]]]],\"NL\":[\"31\",\"00\",\"(?:[124-7]\\\\d\\\\d|3(?:[02-9]\\\\d|1[0-8]))\\\\d{6}|8\\\\d{6,9}|9\\\\d{6,10}|1\\\\d{4,5}\",[5,6,7,8,9,10,11],[[\"(\\\\d{3})(\\\\d{4,7})\",\"$1 $2\",[\"[89]0\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"66\"],\"0$1\"],[\"(\\\\d)(\\\\d{8})\",\"$1 $2\",[\"6\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1[16-8]|2[259]|3[124]|4[17-9]|5[124679]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-578]|91\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\",\"$1 $2 $3\",[\"9\"],\"0$1\"]],\"0\"],\"NO\":[\"47\",\"00\",\"(?:0|[2-9]\\\\d{3})\\\\d{4}\",[5,8],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-79]\"]]],0,0,0,0,0,\"[02-689]|7[0-8]\"],\"NP\":[\"977\",\"00\",\"(?:1\\\\d|9)\\\\d{9}|[1-9]\\\\d{7}\",[8,10,11],[[\"(\\\\d)(\\\\d{7})\",\"$1-$2\",[\"1[2-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1-$2\",[\"1[01]|[2-8]|9(?:[1-59]|[67][2-6])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1-$2\",[\"9\"]]],\"0\"],\"NR\":[\"674\",\"00\",\"(?:222|444|(?:55|8\\\\d)\\\\d|666|777|999)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[24-9]\"]]]],\"NU\":[\"683\",\"00\",\"(?:[4-7]|888\\\\d)\\\\d{3}\",[4,7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"8\"]]]],\"NZ\":[\"64\",\"0(?:0|161)\",\"[1289]\\\\d{9}|50\\\\d{5}(?:\\\\d{2,3})?|[27-9]\\\\d{7,8}|(?:[34]\\\\d|6[0-35-9])\\\\d{6}|8\\\\d{4,6}\",[5,6,7,8,9,10],[[\"(\\\\d{2})(\\\\d{3,8})\",\"$1 $2\",[\"8[1-79]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"50[036-8]|8|90\",\"50(?:[0367]|88)|8|90\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"24|[346]|7[2-57-9]|9[2-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2(?:10|74)|[589]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"1|2[028]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,5})\",\"$1 $2 $3\",[\"2(?:[169]|7[0-35-9])|7\"],\"0$1\"]],\"0\",0,0,0,0,0,0,\"00\"],\"OM\":[\"968\",\"00\",\"(?:1505|[279]\\\\d{3}|500)\\\\d{4}|800\\\\d{5,6}\",[7,8,9],[[\"(\\\\d{3})(\\\\d{4,6})\",\"$1 $2\",[\"[58]\"]],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"2\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[179]\"]]]],\"PA\":[\"507\",\"00\",\"(?:00800|8\\\\d{3})\\\\d{6}|[68]\\\\d{7}|[1-57-9]\\\\d{6}\",[7,8,10,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[1-57-9]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"[68]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]]]],\"PE\":[\"51\",\"00|19(?:1[124]|77|90)00\",\"(?:[14-8]|9\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"80\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"1\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[4-8]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"9\"]]],\"0\",0,0,0,0,0,0,\"00\",\" Anexo \"],\"PF\":[\"689\",\"00\",\"4\\\\d{5}(?:\\\\d{2})?|8\\\\d{7,8}\",[6,8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"44\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"4|8[7-9]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"]]]],\"PG\":[\"675\",\"00|140[1-3]\",\"(?:180|[78]\\\\d{3})\\\\d{4}|(?:[2-589]\\\\d|64)\\\\d{5}\",[7,8],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"18|[2-69]|85\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[78]\"]]],0,0,0,0,0,0,0,\"00\"],\"PH\":[\"63\",\"00\",\"(?:[2-7]|9\\\\d)\\\\d{8}|2\\\\d{5}|(?:1800|8)\\\\d{7,9}\",[6,8,9,10,11,12,13],[[\"(\\\\d)(\\\\d{5})\",\"$1 $2\",[\"2\"],\"(0$1)\"],[\"(\\\\d{4})(\\\\d{4,6})\",\"$1 $2\",[\"3(?:23|39|46)|4(?:2[3-6]|[35]9|4[26]|76)|544|88[245]|(?:52|64|86)2\",\"3(?:230|397|461)|4(?:2(?:35|[46]4|51)|396|4(?:22|63)|59[347]|76[15])|5(?:221|446)|642[23]|8(?:622|8(?:[24]2|5[13]))\"],\"(0$1)\"],[\"(\\\\d{5})(\\\\d{4})\",\"$1 $2\",[\"346|4(?:27|9[35])|883\",\"3469|4(?:279|9(?:30|56))|8834\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[3-7]|8[2-8]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]],[\"(\\\\d{4})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"1\"]]],\"0\"],\"PK\":[\"92\",\"00\",\"122\\\\d{6}|[24-8]\\\\d{10,11}|9(?:[013-9]\\\\d{8,10}|2(?:[01]\\\\d\\\\d|2(?:[06-8]\\\\d|1[01]))\\\\d{7})|(?:[2-8]\\\\d{3}|92(?:[0-7]\\\\d|8[1-9]))\\\\d{6}|[24-9]\\\\d{8}|[89]\\\\d{7}\",[8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,7})\",\"$1 $2 $3\",[\"[89]0\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"1\"]],[\"(\\\\d{3})(\\\\d{6,7})\",\"$1 $2\",[\"2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:2[2-8]|3[27-9]|4[2-6]|6[3569]|9[25-8])\",\"9(?:2[3-8]|98)|(?:2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:22|3[27-9]|4[2-6]|6[3569]|9[25-7]))[2-9]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{7,8})\",\"$1 $2\",[\"(?:2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91)[2-9]\"],\"(0$1)\"],[\"(\\\\d{5})(\\\\d{5})\",\"$1 $2\",[\"58\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"3\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"[24-9]\"],\"(0$1)\"]],\"0\"],\"PL\":[\"48\",\"00\",\"(?:6|8\\\\d\\\\d)\\\\d{7}|[1-9]\\\\d{6}(?:\\\\d{2})?|[26]\\\\d{5}\",[6,7,8,9,10],[[\"(\\\\d{5})\",\"$1\",[\"19\"]],[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"11|20|64\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])1\",\"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])19\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"64\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"21|39|45|5[0137]|6[0469]|7[02389]|8(?:0[14]|8)\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"1[2-8]|[2-7]|8[1-79]|9[145]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"8\"]]]],\"PM\":[\"508\",\"00\",\"[45]\\\\d{5}|(?:708|8\\\\d\\\\d)\\\\d{6}\",[6,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[45]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"7\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"PR\":[\"1\",\"011\",\"(?:[589]\\\\d\\\\d|787)\\\\d{7}\",[10],0,\"1\",0,0,0,0,\"787|939\"],\"PS\":[\"970\",\"00\",\"[2489]2\\\\d{6}|(?:1\\\\d|5)\\\\d{8}\",[8,9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2489]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"5\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"PT\":[\"351\",\"00\",\"1693\\\\d{5}|(?:[26-9]\\\\d|30)\\\\d{7}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"2[12]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"16|[236-9]\"]]]],\"PW\":[\"680\",\"01[12]\",\"(?:[24-8]\\\\d\\\\d|345|900)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-9]\"]]]],\"PY\":[\"595\",\"00\",\"59\\\\d{4,6}|9\\\\d{5,10}|(?:[2-46-8]\\\\d|5[0-8])\\\\d{4,7}\",[6,7,8,9,10,11],[[\"(\\\\d{3})(\\\\d{3,6})\",\"$1 $2\",[\"[2-9]0\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"[26]1|3[289]|4[1246-8]|7[1-3]|8[1-36]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{4,5})\",\"$1 $2\",[\"2[279]|3[13-5]|4[359]|5|6(?:[34]|7[1-46-8])|7[46-8]|85\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2[14-68]|3[26-9]|4[1246-8]|6(?:1|75)|7[1-35]|8[1-36]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"87\"]],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"9(?:[5-79]|8[1-7])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-8]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"9\"]]],\"0\"],\"QA\":[\"974\",\"00\",\"800\\\\d{4}|(?:2|800)\\\\d{6}|(?:0080|[3-7])\\\\d{7}\",[7,8,9,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"2[16]|8\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[3-7]\"]]]],\"RE\":[\"262\",\"00\",\"709\\\\d{6}|(?:26|[689]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[26-9]\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"26(?:2\\\\d\\\\d|3(?:0\\\\d|1[0-6]))\\\\d{4}\"],[\"(?:69(?:2\\\\d\\\\d|3(?:[06][0-6]|1[0-3]|2[0-2]|3[0-39]|4\\\\d|5[0-5]|7[0-37]|8[0-8]|9[0-479]))|7092[0-3])\\\\d{4}\"],[\"80\\\\d{7}\"],[\"89[1-37-9]\\\\d{6}\"],0,0,0,0,[\"9(?:399[0-3]|479[0-6]|76(?:2[278]|3[0-37]))\\\\d{4}\"],[\"8(?:1[019]|2[0156]|84|90)\\\\d{6}\"]]],\"RO\":[\"40\",\"00\",\"(?:[236-8]\\\\d|90)\\\\d{7}|[23]\\\\d{5}\",[6,9],[[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"2[3-6]\",\"2[3-6]\\\\d9\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"219|31\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[23]1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[236-9]\"],\"0$1\"]],\"0\",0,0,0,0,0,0,0,\" int \"],\"RS\":[\"381\",\"00\",\"38[02-9]\\\\d{6,9}|6\\\\d{7,9}|90\\\\d{4,8}|38\\\\d{5,6}|(?:7\\\\d\\\\d|800)\\\\d{3,9}|(?:[12]\\\\d|3[0-79])\\\\d{5,10}\",[6,7,8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{3,9})\",\"$1 $2\",[\"(?:2[389]|39)0|[7-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5,10})\",\"$1 $2\",[\"[1-36]\"],\"0$1\"]],\"0\"],\"RU\":[\"7\",\"810\",\"8\\\\d{13}|[347-9]\\\\d{9}\",[10,14],[[\"(\\\\d{4})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"7(?:1[0-8]|2[1-9])\",\"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:1[23]|[2-9]2))\",\"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:13[03-69]|62[013-9]))|72[1-57-9]2\"],\"8 ($1)\",1],[\"(\\\\d{5})(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"7(?:1[0-68]|2[1-9])\",\"7(?:1(?:[06][3-6]|[18]|2[35]|[3-5][3-5])|2(?:[13][3-5]|[24-689]|7[457]))\",\"7(?:1(?:0(?:[356]|4[023])|[18]|2(?:3[013-9]|5)|3[45]|43[013-79]|5(?:3[1-8]|4[1-7]|5)|6(?:3[0-35-9]|[4-6]))|2(?:1(?:3[178]|[45])|[24-689]|3[35]|7[457]))|7(?:14|23)4[0-8]|71(?:33|45)[1-79]\"],\"8 ($1)\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"8 ($1)\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"[349]|8(?:[02-7]|1[1-8])\"],\"8 ($1)\",1],[\"(\\\\d{4})(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"8\"],\"8 ($1)\"]],\"8\",0,0,0,0,\"3[04-689]|[489]\",0,\"8~10\"],\"RW\":[\"250\",\"00\",\"(?:06|[27]\\\\d\\\\d|[89]00)\\\\d{6}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"0\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[7-9]\"],\"0$1\"]],\"0\"],\"SA\":[\"966\",\"00\",\"92\\\\d{7}|(?:[15]|8\\\\d)\\\\d{8}\",[9,10],[[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"9\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"5\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"81\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]]],\"0\"],\"SB\":[\"677\",\"0[01]\",\"[6-9]\\\\d{6}|[1-6]\\\\d{4}\",[5,7],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"6[89]|7|8[4-9]|9(?:[1-8]|9[0-8])\"]]]],\"SC\":[\"248\",\"010|0[0-2]\",\"(?:[2489]\\\\d|64)\\\\d{5}\",[7],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[246]|9[57]\"]]],0,0,0,0,0,0,0,\"00\"],\"SD\":[\"249\",\"00\",\"[19]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[19]\"],\"0$1\"]],\"0\"],\"SE\":[\"46\",\"00\",\"(?:[26]\\\\d\\\\d|9)\\\\d{9}|[1-9]\\\\d{8}|[1-689]\\\\d{7}|[1-4689]\\\\d{6}|2\\\\d{5}\",[6,7,8,9,10,12],[[\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})\",\"$1-$2 $3\",[\"20\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"9(?:00|39|44|9)\"],\"0$1\",0,\"$1 $2\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})\",\"$1-$2 $3\",[\"[12][136]|3[356]|4[0246]|6[03]|90[1-9]\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d)(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"8\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2,3})(\\\\d{2})\",\"$1-$2 $3\",[\"1[2457]|2(?:[247-9]|5[0138])|3[0247-9]|4[1357-9]|5[0-35-9]|6(?:[125689]|4[02-57]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d{3})(\\\\d{2,3})(\\\\d{3})\",\"$1-$2 $3\",[\"9(?:00|39|44)\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"1[13689]|2[0136]|3[1356]|4[0246]|54|6[03]|90[1-9]\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"10|7\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"8\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"[13-5]|2(?:[247-9]|5[0138])|6(?:[124-689]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1-$2 $3 $4\",[\"9\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4 $5\",[\"[26]\"],\"0$1\",0,\"$1 $2 $3 $4 $5\"]],\"0\"],\"SG\":[\"65\",\"0[0-3]\\\\d\",\"(?:(?:1\\\\d|8)\\\\d\\\\d|7000)\\\\d{7}|[3689]\\\\d{7}\",[8,10,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[369]|8(?:0[1-9]|[1-9])\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{4})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"7\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]]]],\"SH\":[\"290\",\"00\",\"(?:[256]\\\\d|8)\\\\d{3}\",[4,5],0,0,0,0,0,0,\"[256]\"],\"SI\":[\"386\",\"00|10(?:22|66|88|99)\",\"[1-7]\\\\d{7}|8\\\\d{4,7}|90\\\\d{4,6}\",[5,6,7,8],[[\"(\\\\d{2})(\\\\d{3,6})\",\"$1 $2\",[\"8[09]|9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"59|8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[37][01]|4[0139]|51|6\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[1-57]\"],\"(0$1)\"]],\"0\",0,0,0,0,0,0,\"00\"],\"SJ\":[\"47\",\"00\",\"0\\\\d{4}|(?:[489]\\\\d|79)\\\\d{6}\",[5,8],0,0,0,0,0,0,\"79\"],\"SK\":[\"421\",\"00\",\"[2-689]\\\\d{8}|[2-59]\\\\d{6}|[2-5]\\\\d{5}\",[6,7,9],[[\"(\\\\d)(\\\\d{2})(\\\\d{3,4})\",\"$1 $2 $3\",[\"21\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"[3-5][1-8]1\",\"[3-5][1-8]1[67]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1/$2 $3 $4\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[689]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1/$2 $3 $4\",[\"[3-5]\"],\"0$1\"]],\"0\"],\"SL\":[\"232\",\"00\",\"(?:[237-9]\\\\d|66)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[236-9]\"],\"(0$1)\"]],\"0\"],\"SM\":[\"378\",\"00\",\"(?:0549|[5-7]\\\\d)\\\\d{6}\",[8,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-7]\"]],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"0\"]]],0,0,\"([89]\\\\d{5})$\",\"0549$1\"],\"SN\":[\"221\",\"00\",\"(?:[378]\\\\d|93)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[379]\"]]]],\"SO\":[\"252\",\"00\",\"[346-9]\\\\d{8}|[12679]\\\\d{7}|[1-5]\\\\d{6}|[1348]\\\\d{5}\",[6,7,8,9],[[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"8[125]\"]],[\"(\\\\d{6})\",\"$1\",[\"[134]\"]],[\"(\\\\d)(\\\\d{6})\",\"$1 $2\",[\"[15]|2[0-79]|3[0-46-8]|4[0-7]\"]],[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"(?:2|90)4|[67]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[348]|64|79|90\"]],[\"(\\\\d{2})(\\\\d{5,7})\",\"$1 $2\",[\"1|28|6[0-35-9]|7[67]|9[2-9]\"]]],\"0\"],\"SR\":[\"597\",\"00\",\"(?:[2-5]|68|[78]\\\\d)\\\\d{5}\",[6,7],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1-$2-$3\",[\"56\"]],[\"(\\\\d{3})(\\\\d{3})\",\"$1-$2\",[\"[2-5]\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[6-8]\"]]]],\"SS\":[\"211\",\"00\",\"[19]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[19]\"],\"0$1\"]],\"0\"],\"ST\":[\"239\",\"00\",\"(?:22|9\\\\d)\\\\d{5}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[29]\"]]]],\"SV\":[\"503\",\"00\",\"[267]\\\\d{7}|(?:80\\\\d|900)\\\\d{4}(?:\\\\d{4})?\",[7,8,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[89]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[267]\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"]]]],\"SX\":[\"1\",\"011\",\"7215\\\\d{6}|(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"(5\\\\d{6})$|1\",\"721$1\",0,\"721\"],\"SY\":[\"963\",\"00\",\"[1-359]\\\\d{8}|[1-5]\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[1-4]|5[1-3]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[59]\"],\"0$1\",1]],\"0\"],\"SZ\":[\"268\",\"00\",\"0800\\\\d{4}|(?:[237]\\\\d|900)\\\\d{6}\",[8,9],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[0237]\"]],[\"(\\\\d{5})(\\\\d{4})\",\"$1 $2\",[\"9\"]]]],\"TA\":[\"290\",\"00\",\"8\\\\d{3}\",[4],0,0,0,0,0,0,\"8\"],\"TC\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|649|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-479]\\\\d{6})$|1\",\"649$1\",0,\"649\"],\"TD\":[\"235\",\"00|16\",\"(?:22|[689]\\\\d|77)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[26-9]\"]]],0,0,0,0,0,0,0,\"00\"],\"TG\":[\"228\",\"00\",\"[279]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[279]\"]]]],\"TH\":[\"66\",\"00[1-9]\",\"(?:001800|[2-57]|[689]\\\\d)\\\\d{7}|1\\\\d{7,9}\",[8,9,10,13],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[13-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"TJ\":[\"992\",\"810\",\"[0-57-9]\\\\d{8}\",[9],[[\"(\\\\d{6})(\\\\d)(\\\\d{2})\",\"$1 $2 $3\",[\"331\",\"3317\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"44[02-479]|[34]7\"]],[\"(\\\\d{4})(\\\\d)(\\\\d{4})\",\"$1 $2 $3\",[\"3(?:[1245]|3[12])\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[0-57-9]\"]]],0,0,0,0,0,0,0,\"8~10\"],\"TK\":[\"690\",\"00\",\"[2-47]\\\\d{3,6}\",[4,5,6,7]],\"TL\":[\"670\",\"00\",\"7\\\\d{7}|(?:[2-47]\\\\d|[89]0)\\\\d{5}\",[7,8],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-489]|70\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"7\"]]]],\"TM\":[\"993\",\"810\",\"(?:[1-6]\\\\d|71)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"12\"],\"(8 $1)\"],[\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"[1-5]\"],\"(8 $1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[67]\"],\"8 $1\"]],\"8\",0,0,0,0,0,0,\"8~10\"],\"TN\":[\"216\",\"00\",\"[2-57-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-57-9]\"]]]],\"TO\":[\"676\",\"00\",\"(?:0800|(?:[5-8]\\\\d\\\\d|999)\\\\d)\\\\d{3}|[2-8]\\\\d{4}\",[5,7],[[\"(\\\\d{2})(\\\\d{3})\",\"$1-$2\",[\"[2-4]|50|6[09]|7[0-24-69]|8[05]\"]],[\"(\\\\d{4})(\\\\d{3})\",\"$1 $2\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[5-9]\"]]]],\"TR\":[\"90\",\"00\",\"4\\\\d{6}|8\\\\d{11,12}|(?:[2-58]\\\\d\\\\d|900)\\\\d{7}\",[7,10,12,13],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"512|8[01589]|90\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"5(?:[0-59]|61)\",\"5(?:[0-59]|61[06])\",\"5(?:[0-59]|61[06]1)\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[24][1-8]|3[1-9]\"],\"(0$1)\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{6,7})\",\"$1 $2 $3\",[\"80\"],\"0$1\",1]],\"0\"],\"TT\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-46-8]\\\\d{6})$|1\",\"868$1\",0,\"868\"],\"TV\":[\"688\",\"00\",\"(?:2|7\\\\d\\\\d|90)\\\\d{4}\",[5,6,7],[[\"(\\\\d{2})(\\\\d{3})\",\"$1 $2\",[\"2\"]],[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"90\"]],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"7\"]]]],\"TW\":[\"886\",\"0(?:0[25-79]|19)\",\"[2-689]\\\\d{8}|7\\\\d{9,10}|[2-8]\\\\d{7}|2\\\\d{6}\",[7,8,9,10,11],[[\"(\\\\d{2})(\\\\d)(\\\\d{4})\",\"$1 $2 $3\",[\"202\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[258]0\"],\"0$1\"],[\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"[23568]|4(?:0[02-48]|[1-47-9])|7[1-9]\",\"[23568]|4(?:0[2-48]|[1-47-9])|(?:400|7)[1-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[49]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4,5})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\",0,0,0,0,0,0,0,\"#\"],\"TZ\":[\"255\",\"00[056]\",\"(?:[25-8]\\\\d|41|90)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[24]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"5\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[67]\"],\"0$1\"]],\"0\"],\"UA\":[\"380\",\"00\",\"[89]\\\\d{9}|[3-9]\\\\d{8}\",[9,10],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6[12][29]|(?:3[1-8]|4[136-8]|5[12457]|6[49])2|(?:56|65)[24]\",\"6[12][29]|(?:35|4[1378]|5[12457]|6[49])2|(?:56|65)[24]|(?:3[1-46-8]|46)2[013-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6[0135689]|7[4-6])|6(?:[12][3-7]|[459])\",\"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6(?:[015689]|3[02389])|7[4-6])|6(?:[12][3-7]|[459])\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[3-7]|89|9[1-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"]],\"0\",0,0,0,0,0,0,\"0~0\"],\"UG\":[\"256\",\"00[057]\",\"800\\\\d{6}|(?:[29]0|[347]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"202\",\"2024\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"[27-9]|4(?:6[45]|[7-9])\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"[34]\"],\"0$1\"]],\"0\"],\"US\":[\"1\",\"011\",\"[2-9]\\\\d{9}|3\\\\d{6}\",[10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"310\"],0,1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"($1) $2-$3\",[\"[2-9]\"],0,1,\"$1-$2-$3\"]],\"1\",0,0,0,0,0,[[\"(?:3052(?:0[0-8]|[1-9]\\\\d)|5056(?:[0-35-9]\\\\d|4[0-468]))\\\\d{4}|(?:2742|305[3-9]|472[247-9]|505[2-57-9]|983[2-47-9])\\\\d{6}|(?:2(?:0[1-35-9]|1[02-9]|2[03-57-9]|3[1459]|4[08]|5[1-46]|6[0279]|7[0269]|8[13])|3(?:0[1-47-9]|1[02-9]|2[0135-79]|3[0-24679]|4[167]|5[0-2]|6[01349]|8[056])|4(?:0[124-9]|1[02-579]|2[3-5]|3[0245]|4[023578]|58|6[349]|7[0589]|8[04])|5(?:0[1-47-9]|1[0235-8]|20|3[0149]|4[01]|5[179]|6[1-47]|7[0-5]|8[0256])|6(?:0[1-35-9]|1[024-9]|2[03689]|3[016]|4[0156]|5[01679]|6[0-279]|78|8[0-29])|7(?:0[1-46-8]|1[2-9]|2[04-8]|3[0-247]|4[037]|5[47]|6[02359]|7[0-59]|8[156])|8(?:0[1-68]|1[02-8]|2[0168]|3[0-2589]|4[03578]|5[046-9]|6[02-5]|7[028])|9(?:0[1346-9]|1[02-9]|2[0589]|3[0146-8]|4[01357-9]|5[12469]|7[0-389]|8[04-69]))[2-9]\\\\d{6}\"],[\"\"],[\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\"],[\"900[2-9]\\\\d{6}\"],[\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\\\d{6}\"],0,0,0,[\"305209\\\\d{4}\"]]],\"UY\":[\"598\",\"0(?:0|1[3-9]\\\\d)\",\"0004\\\\d{2,9}|[1249]\\\\d{7}|(?:[49]\\\\d|80)\\\\d{5}\",[6,7,8,9,10,11,12,13],[[\"(\\\\d{3})(\\\\d{3,4})\",\"$1 $2\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[49]0|8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[124]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3\",[\"0\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3 $4\",[\"0\"]]],\"0\",0,0,0,0,0,0,\"00\",\" int. \"],\"UZ\":[\"998\",\"00\",\"(?:20|33|[5-9]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[235-9]\"]]]],\"VA\":[\"39\",\"00\",\"0\\\\d{5,10}|3[0-8]\\\\d{7,10}|55\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?|(?:1\\\\d|39)\\\\d{7,8}\",[6,7,8,9,10,11,12],0,0,0,0,0,0,\"06698\"],\"VC\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|784|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-7]\\\\d{6})$|1\",\"784$1\",0,\"784\"],\"VE\":[\"58\",\"00\",\"[68]00\\\\d{7}|(?:[24]\\\\d|[59]0)\\\\d{8}\",[10],[[\"(\\\\d{3})(\\\\d{7})\",\"$1-$2\",[\"[24-689]\"],\"0$1\"]],\"0\"],\"VG\":[\"1\",\"011\",\"(?:284|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-578]\\\\d{6})$|1\",\"284$1\",0,\"284\"],\"VI\":[\"1\",\"011\",\"[58]\\\\d{9}|(?:34|90)0\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"340$1\",0,\"340\"],\"VN\":[\"84\",\"00\",\"[12]\\\\d{9}|[135-9]\\\\d{8}|[16]\\\\d{7}|[16-8]\\\\d{6}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"80\"],\"0$1\",1],[\"(\\\\d{4})(\\\\d{4,6})\",\"$1 $2\",[\"1\"],0,1],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"6\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[357-9]\"],\"0$1\",1],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2[48]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"],\"0$1\",1]],\"0\"],\"VU\":[\"678\",\"00\",\"[57-9]\\\\d{6}|(?:[238]\\\\d|48)\\\\d{3}\",[5,7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[57-9]\"]]]],\"WF\":[\"681\",\"00\",\"(?:40|72|8\\\\d{4})\\\\d{4}|[89]\\\\d{5}\",[6,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[47-9]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"]]]],\"WS\":[\"685\",\"0\",\"(?:[2-6]|8\\\\d{5})\\\\d{4}|[78]\\\\d{6}|[68]\\\\d{5}\",[5,6,7,10],[[\"(\\\\d{5})\",\"$1\",[\"[2-5]|6[1-9]\"]],[\"(\\\\d{3})(\\\\d{3,7})\",\"$1 $2\",[\"[68]\"]],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"7\"]]]],\"XK\":[\"383\",\"00\",\"2\\\\d{7,8}|3\\\\d{7,11}|(?:4\\\\d\\\\d|[89]00)\\\\d{5}\",[8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-4]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2|39\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7,10})\",\"$1 $2\",[\"3\"],\"0$1\"]],\"0\"],\"YE\":[\"967\",\"00\",\"(?:1|7\\\\d)\\\\d{7}|[1-7]\\\\d{6}\",[7,8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[1-6]|7(?:[24-6]|8[0-7])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\"],\"YT\":[\"262\",\"00\",\"7093\\\\d{5}|(?:80|9\\\\d)\\\\d{7}|(?:26|63)9\\\\d{6}\",[9],0,\"0\",0,0,0,0,0,[[\"269(?:0[0-467]|15|5[0-4]|6\\\\d|[78]0)\\\\d{4}\"],[\"(?:639(?:0[0-79]|1[019]|[267]\\\\d|3[09]|40|5[05-9]|9[04-79])|7093[5-7])\\\\d{4}\"],[\"80\\\\d{7}\"],0,0,0,0,0,[\"9(?:(?:39|47)8[01]|769\\\\d)\\\\d{4}\"]]],\"ZA\":[\"27\",\"00\",\"[1-79]\\\\d{8}|8\\\\d{4,9}\",[5,6,7,8,9,10],[[\"(\\\\d{2})(\\\\d{3,4})\",\"$1 $2\",[\"8[1-4]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\",\"$1 $2 $3\",[\"8[1-4]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"860\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"],\"0$1\"]],\"0\"],\"ZM\":[\"260\",\"00\",\"800\\\\d{6}|(?:21|[579]\\\\d|63)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[28]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"[579]\"],\"0$1\"]],\"0\"],\"ZW\":[\"263\",\"00\",\"2(?:[0-57-9]\\\\d{6,8}|6[0-24-9]\\\\d{6,7})|[38]\\\\d{9}|[35-8]\\\\d{8}|[3-6]\\\\d{7}|[1-689]\\\\d{6}|[1-3569]\\\\d{5}|[1356]\\\\d{4}\",[5,6,7,8,9,10],[[\"(\\\\d{3})(\\\\d{3,5})\",\"$1 $2\",[\"2(?:0[45]|2[278]|[49]8)|3(?:[09]8|17)|6(?:[29]8|37|75)|[23][78]|(?:33|5[15]|6[68])[78]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3\",[\"[49]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"80\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"24|8[13-59]|(?:2[05-79]|39|5[45]|6[15-8])2\",\"2(?:02[014]|4|[56]20|[79]2)|392|5(?:42|525)|6(?:[16-8]21|52[013])|8[13-59]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:12|29)\",\"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:123|29)\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,5})\",\"$1 $2\",[\"1|2(?:0[0-36-9]|12|29|[56])|3(?:1[0-689]|[24-6])|5(?:[0236-9]|1[2-4])|6(?:[013-59]|7[0-46-9])|(?:33|55|6[68])[0-69]|(?:29|3[09]|62)[0-79]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"29[013-9]|39|54\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3,5})\",\"$1 $2\",[\"(?:25|54)8\",\"258|5483\"],\"0$1\"]],\"0\"]},\"nonGeographic\":{\"800\":[\"800\",0,\"(?:00|[1-9]\\\\d)\\\\d{6}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"\\\\d\"]]],0,0,0,0,0,0,[0,0,[\"(?:00|[1-9]\\\\d)\\\\d{6}\"]]],\"808\":[\"808\",0,\"[1-9]\\\\d{7}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[1-9]\"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,0,[\"[1-9]\\\\d{7}\"]]],\"870\":[\"870\",0,\"7\\\\d{11}|[235-7]\\\\d{8}\",[9,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[235-7]\"]]],0,0,0,0,0,0,[0,[\"(?:[356]|774[45])\\\\d{8}|7[6-8]\\\\d{7}\"],0,0,0,0,0,0,[\"2\\\\d{8}\",[9]]]],\"878\":[\"878\",0,\"10\\\\d{10}\",[12],[[\"(\\\\d{2})(\\\\d{5})(\\\\d{5})\",\"$1 $2 $3\",[\"1\"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,[\"10\\\\d{10}\"]]],\"881\":[\"881\",0,\"6\\\\d{9}|[0-36-9]\\\\d{8}\",[9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{5})\",\"$1 $2 $3\",[\"[0-37-9]\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{5,6})\",\"$1 $2 $3\",[\"6\"]]],0,0,0,0,0,0,[0,[\"6\\\\d{9}|[0-36-9]\\\\d{8}\"]]],\"882\":[\"882\",0,\"[13]\\\\d{6}(?:\\\\d{2,5})?|[19]\\\\d{7}|(?:[25]\\\\d\\\\d|4)\\\\d{7}(?:\\\\d{2})?\",[7,8,9,10,11,12],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"16|342\"]],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"49\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"1[36]|9\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"3[23]\"]],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"16\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"10|23|3(?:[15]|4[57])|4|51\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"34\"]],[\"(\\\\d{2})(\\\\d{4,5})(\\\\d{5})\",\"$1 $2 $3\",[\"[1-35]\"]]],0,0,0,0,0,0,[0,[\"342\\\\d{4}|(?:337|49)\\\\d{6}|(?:3(?:2|47|7\\\\d{3})|50\\\\d{3})\\\\d{7}\",[7,8,9,10,12]],0,0,0,[\"348[57]\\\\d{7}\",[11]],0,0,[\"1(?:3(?:0[0347]|[13][0139]|2[035]|4[013568]|6[0459]|7[06]|8[15-8]|9[0689])\\\\d{4}|6\\\\d{5,10})|(?:345\\\\d|9[89])\\\\d{6}|(?:10|2(?:3|85\\\\d)|3(?:[15]|[69]\\\\d\\\\d)|4[15-8]|51)\\\\d{8}\"]]],\"883\":[\"883\",0,\"(?:[1-4]\\\\d|51)\\\\d{6,10}\",[8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,8})\",\"$1 $2 $3\",[\"[14]|2[24-689]|3[02-689]|51[24-9]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"510\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"21\"]],[\"(\\\\d{4})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"51[13]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"[235]\"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,[\"(?:2(?:00\\\\d\\\\d|10)|(?:370[1-9]|51\\\\d0)\\\\d)\\\\d{7}|51(?:00\\\\d{5}|[24-9]0\\\\d{4,7})|(?:1[0-79]|2[24-689]|3[02-689]|4[0-4])0\\\\d{5,9}\"]]],\"888\":[\"888\",0,\"\\\\d{11}\",[11],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\",\"$1 $2 $3\"]],0,0,0,0,0,0,[0,0,0,0,0,0,[\"\\\\d{11}\"]]],\"979\":[\"979\",0,\"[1359]\\\\d{8}\",[9],[[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[1359]\"]]],0,0,0,0,0,0,[0,0,0,[\"[1359]\\\\d{8}\"]]]}}", "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.min.json.js'\r\n\r\nexport default function withMetadataArgument(func, _arguments) {\r\n\tvar args = Array.prototype.slice.call(_arguments)\r\n\targs.push(metadata)\r\n\treturn func.apply(this, args)\r\n}"], "names": ["_excluded", "Bold", "_ref", "color", "react__WEBPACK_IMPORTED_MODULE_0__", "createElement", "Fragment", "d", "fill", "Broken", "_ref2", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "Bulk", "_ref3", "opacity", "Linear", "_ref4", "Outline", "_ref5", "TwoTone", "_ref6", "choose<PERSON><PERSON><PERSON>", "variant", "Global", "forwardRef", "_ref7", "ref", "size", "rest", "_rollupPluginBabelHelpers_3bc641ae_js__WEBPACK_IMPORTED_MODULE_1__", "_", "a", "xmlns", "width", "height", "viewBox", "propTypes", "prop_types__WEBPACK_IMPORTED_MODULE_2___default", "oneOf", "string", "oneOfType", "number", "defaultProps", "displayName", "CHECKBOX_NAME", "createCheckboxContext", "createCheckboxScope", "createContextScope", "CheckboxProvider", "useCheckboxContext", "Checkbox", "React", "props", "forwardedRef", "__scopeCheckbox", "name", "checked", "checkedProp", "defaultChecked", "required", "disabled", "value", "onCheckedChange", "form", "checkboxProps", "button", "setButton", "composedRefs", "useComposedRefs", "node", "hasConsumerStoppedPropagationRef", "isFormControl", "closest", "setChecked", "useControllableState", "prop", "defaultProp", "onChange", "caller", "initialCheckedStateRef", "reset", "current", "addEventListener", "removeEventListener", "jsxs", "scope", "state", "children", "jsx", "Primitive", "type", "role", "isIndeterminate", "getState", "onKeyDown", "composeEventHandlers", "event", "key", "preventDefault", "onClick", "prevChecked", "isPropagationStopped", "stopPropagation", "CheckboxBubbleInput", "control", "bubbles", "style", "transform", "INDICATOR_NAME", "CheckboxIndicator", "forceMount", "indicatorProps", "context", "Presence", "present", "span", "pointerEvents", "usePrevious", "controlSize", "useSize", "input", "descriptor", "getOwnPropertyDescriptor", "window", "HTMLInputElement", "prototype", "set", "Event", "indeterminate", "call", "dispatchEvent", "defaultCheckedRef", "tabIndex", "position", "margin", "Root", "Indicator", "RADIO_NAME", "createRadioContext", "createRadioScope", "RadioProvider", "useRadioContext", "Radio", "__scopeRadio", "onCheck", "radioProps", "RadioBubbleInput", "RadioIndicator", "ARROW_KEYS", "RADIO_GROUP_NAME", "createRadioGroupContext", "createRadioGroupScope", "createRovingFocusGroupScope", "useRovingFocusGroupScope", "useRadioScope", "RadioGroupProvider", "useRadioGroupContext", "RadioGroup", "React2", "__scopeRadioGroup", "defaultValue", "valueProp", "orientation", "dir", "loop", "onValueChange", "groupProps", "rovingFocusGroupScope", "direction", "useDirection", "setValue", "RovingFocusGroup", "<PERSON><PERSON><PERSON><PERSON>", "div", "ITEM_NAME", "RadioGroupItem", "itemProps", "isDisabled", "radioScope", "isArrowKeyPressedRef", "handleKeyDown", "includes", "handleKeyUp", "document", "focusable", "active", "onFocus", "click", "RadioGroupIndicator", "<PERSON><PERSON>", "_typeof", "obj", "Symbol", "iterator", "constructor", "_assertThisInitialized", "self", "_wrapNativeSuper", "Class", "_cache", "Map", "undefined", "Function", "toString", "indexOf", "has", "get", "Wrapper", "_construct", "arguments", "_getPrototypeOf", "Object", "create", "enumerable", "writable", "configurable", "_setPrototypeOf", "Parent", "args", "_isNativeReflectConstruct", "Reflect", "construct", "push", "apply", "instance", "bind", "sham", "Proxy", "Boolean", "valueOf", "e", "o", "p", "setPrototypeOf", "__proto__", "getPrototypeOf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Error", "_inherits", "subClass", "superClass", "defineProperty", "hasNativeReflectConstruct", "_super", "result", "Super", "_possibleConstructorReturn", "code", "_this", "_classCallCheck", "<PERSON><PERSON><PERSON><PERSON>", "Error", "FIRST_GROUP_PATTERN", "SINGLE_IDD_PREFIX_REG_EXP", "format_arrayLikeToArray", "arr", "len", "length", "i", "arr2", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "_objectSpread", "target", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "DEFAULT_OPTIONS", "formatExtension", "formattedNumber", "extension", "metadata", "concat", "ext", "formatNationalNumber", "carrierCode", "formatAs", "options", "useInternationalFormat", "withNationalPrefix", "format", "chooseFormatForNumber", "availableFormats", "nationalNnumber", "_step", "_iterator", "format_createForOfIteratorHelperLoose", "allowArrayLike", "it", "next", "Array", "isArray", "format_unsupportedIterableToArray", "minLen", "n", "slice", "from", "test", "done", "leadingDigitsPatterns", "lastLeadingDigitsPattern", "search", "matchesEntirely", "Z", "pattern", "formats", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefix", "replace", "RegExp", "internationalFormat", "nationalPrefixFormattingRule", "constants", "uv", "trim", "PhoneNumber_ownKeys", "PhoneNumber_objectSpread", "PhoneNumber", "protoProps", "countryOrCountryCallingCode", "nationalNumber", "isObject", "countries", "metadataJson", "country", "countryCallingCode", "e164Number", "E164_NUMBER_REGEXP", "_extractCountryCallin", "extractCountryCallingCode", "_countryCallingCode", "es6_metadata", "zW", "_getCountryAndCountry", "ZP", "selectNumberingPlan", "getMetadata", "callingCode", "possibleCountries", "_metadata", "getCountryCodesForCallingCode", "numberingPlan", "possibleLengths", "es6_isPossible", "v2", "es6_isValid", "isNonGeographicCallingCode", "phoneNumber", "getNumberType", "_format", "formatNumber", "hasCountry", "phone", "formatRFC3966", "fromCountry", "formatIDD", "fromCountryCallingCode", "Gg", "countryMetadata", "iddPrefix", "defaultIDDPrefix", "IDDPrefix", "_defineProperties", "MIN_LENGTH_FOR_NSN", "MAX_LENGTH_FOR_NSN", "MAX_LENGTH_COUNTRY_CODE", "VALID_DIGITS", "VALID_PUNCTUATION", "PLUS_CHARS", "_arrayLikeToArray", "checkNumberLength", "checkNumberLengthForType", "type_info", "possible_lengths", "mobile_type", "mergeArrays", "b", "merged", "_createForOfIteratorHelperLoose", "_unsupportedIterableToArray", "element", "sort", "actual_length", "minimum_length", "getExtensionDigitsPattern", "max<PERSON><PERSON><PERSON>", "_constants_js__WEBPACK_IMPORTED_MODULE_0__", "xc", "createExtensionPattern", "purpose", "possibleSeparatorsBetweenNumberAndExtLabel", "possibleCharsAfterExtLabel", "possibleSeparatorsNumberExtLabelNoComma", "rfcExtn", "CAPTURING_DIGIT_PATTERN", "isNumberWithIddPrefix", "numberWithoutIDD", "stripIddPrefix", "IDDPrefixPattern", "matchedGroups", "match", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "possibleShorterNumber", "possibleShorterNationalNumber", "_extractNationalNumbe", "extractNationalNumber", "_extractNationalNumbe2", "nationalNumberPattern", "shorterNumber", "countryCallingCodeSource", "xg", "hasCallingCode", "extractNationalNumberFromPossiblyIncompleteNumber", "nationalPrefixForParsing", "prefixPattern", "prefixMatch", "exec", "capturedGroupsCount", "hasCapturedGroups", "nationalPrefixTransformRule", "prefixBeforeNationalNumber", "possiblePositionOfTheFirstCapturedGroup", "isPossibleIncompleteNationalNumber", "NON_FIXED_LINE_PHONE_TYPES", "_metadata_js__WEBPACK_IMPORTED_MODULE_0__", "_matchesEntirely_js__WEBPACK_IMPORTED_MODULE_1__", "isNumberTypeEqualTo", "objectConstructor", "MIN_LENGTH_PHONE_NUMBER_PATTERN", "ex", "VALID_PHONE_NUMBER", "xy", "VALID_PHONE_NUMBER_START_REG_EXP", "VALID_PHONE_NUMBER_PATTERN", "_extension_createExtensionPattern_js__WEBPACK_IMPORTED_MODULE_1__", "isViablePhoneNumber", "isViablePhoneNumberStart", "text", "regular_expression", "isPossiblePhoneNumber", "isPossibleNumber", "_helpers_checkNumberLength_js__WEBPACK_IMPORTED_MODULE_1__", "isValidNumber", "hasTypes", "_helpers_getNumberType_js__WEBPACK_IMPORTED_MODULE_1__", "_helpers_matchesEntirely_js__WEBPACK_IMPORTED_MODULE_2__", "semver_compare", "split", "pa", "pb", "na", "Number", "nb", "isNaN", "_createClass", "staticProps", "DEFAULT_EXT_PREFIX", "CALLING_CODE_REG_EXP", "<PERSON><PERSON><PERSON>", "validateMetadata", "setVersion", "countryCode", "v1", "v3", "nonGeographic", "nonGeographical", "getCountryMetadata", "countryCodes", "countryCallingCodes", "NumberingPlan", "getNumberingPlanMetadata", "getCountryCodeForCallingCode", "leadingDigits", "_type", "country_phone_code_to_countries", "country_calling_codes", "globalMetadataObject", "_getFormats", "getDefaultCountryMetadataForRegion", "map", "Format", "_getNationalPrefixFormattingRule", "_nationalPrefixForParsing", "_getNationalPrefixIsOptionalWhenFormatting", "types", "_type2", "getType", "Type", "usesNationalPrefix", "FIRST_GROUP_ONLY_PREFIX_PATTERN", "join", "typeOf", "getCountryCallingCode", "isSupportedCountry", "hasOwnProperty", "version", "v4", "normalizeArguments", "_Array$prototype$slic2", "_arrayWithHoles", "_iterableToArrayLimit", "_s", "_e", "_i", "_arr", "_n", "_d", "err", "_nonIterableRest", "arg_1", "arg_2", "arg_3", "arg_4", "_helpers_isObject_js__WEBPACK_IMPORTED_MODULE_0__", "defaultCountry", "EXTN_PATTERN", "DIGITS", "parseIncompletePhoneNumber_arrayLikeToArray", "parseIncompletePhoneNumber", "parseIncompletePhoneNumber_createForOfIteratorHelperLoose", "parseIncompletePhoneNumber_unsupportedIterableToArray", "character", "parsePhoneNumberCharacter", "prevParsedCharacters", "emitEvent", "getCountryByNationalNumber_arrayLikeToArray", "RFC3966_PHONE_DIGIT_", "RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_", "ALPHANUM_", "RFC3966_DOMAINNAME_PATTERN_", "RFC3966_PREFIX_", "RFC3966_PHONE_CONTEXT_", "PHONE_NUMBER_START_PATTERN", "AFTER_PHONE_NUMBER_END_PATTERN", "parse", "_parseInput", "parseInput", "extract", "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "numberToParse", "extractFormattedPhoneNumber", "phoneContext", "extractPhoneContext", "numberToExtractFrom", "indexOfPhoneContext", "phoneContextStart", "phoneContextEnd", "substring", "phoneNumberString", "char<PERSON>t", "indexOfNationalNumber", "indexOfRfc3966Prefix", "indexOfIsdn", "_extractFormattedPhoneNumber", "throwOnError", "startsAt", "ch", "error", "withExtensionStripped", "extractExtension", "start", "numberWithoutExtension", "matches", "formattedPhoneNumber", "_parsePhoneNumber", "parsePhoneNumber", "defaultCallingCode", "nationalPhoneNumber", "exactCountry", "getCountryByNationalNumber", "getCountryByNationalNumber_createForOfIteratorHelperLoose", "getCountryByNationalNumber_unsupportedIterableToArray", "hasSelectedNumberingPlan", "sJ", "__countryCallingCodeSource", "valid", "extended", "possible", "isPossible", "D", "parsePhoneNumberWithError", "_parse_js__WEBPACK_IMPORTED_MODULE_0__", "aS", "_parsePhoneNumberWithError_js__WEBPACK_IMPORTED_MODULE_1__", "_ParseError_js__WEBPACK_IMPORTED_MODULE_2__", "__WEBPACK_DEFAULT_EXPORT__", "_normalizeArguments", "<PERSON><PERSON><PERSON><PERSON>", "isValidNumber_isValidNumber", "withMetadataArgument", "getCountries", "getCountries_getCountries", "_withMetadataArgument_js__WEBPACK_IMPORTED_MODULE_0__", "_core_index_js__WEBPACK_IMPORTED_MODULE_1__", "getExampleNumber", "examples", "getExampleNumber_getExampleNumber", "parsePhoneNumber_", "isPossiblePhoneNumber_isPossiblePhoneNumber", "isValidPhoneNumber", "isValidPhoneNumber_isValidPhoneNumber", "parsePhoneNumber_parsePhoneNumber", "validatePhoneNumberLength", "parsePhoneNumberWithError_", "message", "validatePhoneNumberLength_validatePhoneNumberLength", "metadata_min_json", "func", "_arguments"], "sourceRoot": ""}