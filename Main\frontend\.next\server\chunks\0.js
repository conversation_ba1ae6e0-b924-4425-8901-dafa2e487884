exports.id=0,exports.ids=[0],exports.modules={83362:(e,t,r)=>{Promise.resolve().then(r.bind(r,19792))},78497:(e,t,r)=>{Promise.resolve().then(r.bind(r,19792))},19792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var a=r(10326),n=r(63761),l=r(8281),s=r(75584),o=r(77863),c=r(35047),i=r(17577),d=r.n(i),u=r(70012),m=r(56140),f=r(28758),h=r(53095);class p{constructor(e){this.id=e?.id,this.userId=e?.userId,this.contactId=e?.contactId,this.createdAt=new Date(e?.createdAt),this.updatedAt=new Date(e?.updatedAt),this.contact=new h.n(e?.contact)}}var x=r(54033),g=r(33436),v=r(90772),j=r(49547),b=r(10734);async function w(e){try{let t=await j.Z.delete(`/contacts/delete/${e}`);return(0,b.B)(t)}catch(e){return(0,b.D)(e)}}var k=r(72871),N=r(85999),y=r(7291);function Z({row:e}){let{t}=(0,u.$G)(),{mutate:r}=(0,y.kY)(),n=(0,c.useSearchParams)(),l=e=>{N.toast.promise(w(String(e)),{loading:t("Deleting..."),success:e=>{if(!e.status)throw Error(e.message);return r(`/contacts?page=${n.get("page")??1}&limit=${n.get("limit")??10}`),e.message},error:e=>e.message})};return a.jsx("div",{className:"flex items-center gap-2",children:a.jsx(v.z,{type:"button",onClick:()=>l(e.id),size:"sm",className:"h-8 w-8 bg-[#D13438] hover:bg-[#c1262b]",children:a.jsx(k.Z,{size:"20",color:"#fff"})})})}function E({data:e,meta:t,isLoading:r}){let[n,l]=d().useState([]),{t:s}=(0,u.$G)();return a.jsx(m.Z,{data:e?[...e.map(e=>new p(e))]:[],sorting:n,isLoading:r,setSorting:l,pagination:{total:t?.total,page:t?.currentPage,limit:t?.perPage},structure:[{id:"name",header:s("Name"),cell:({row:e})=>{let{contact:t}=e?.original;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(f.qE,{className:"size-8",children:[a.jsx(f.F$,{src:(0,o.qR)(t.customer?.avatar),alt:t.customer?.name}),a.jsx(f.Q5,{children:(0,x.v)(t.customer?.name)})]}),a.jsx("span",{className:"block min-w-24 text-xs font-normal sm:text-sm",children:t.customer?.name})]})}},{id:"contact_number",header:s("Contact number"),cell:({row:e})=>{let{contact:t}=e?.original,r=(0,g.S)(t.customer?.phone);return a.jsx("span",{className:"whitespace-nowrap text-xs font-normal sm:text-sm",children:r?.formatInternational()})}},{id:"email",header:s("Email"),cell:({row:e})=>{let{contact:t}=e?.original;return a.jsx("span",{className:"text-xs font-normal sm:text-sm",children:t?.email})}},{id:"menu",header:s("Menu"),cell:({row:e})=>{let t=e?.original;return a.jsx(Z,{row:t})}}]})}function P(){let{t:e}=(0,u.$G)(),t=(0,c.useSearchParams)(),[r,d]=i.useState(t.get("search")??""),m=(0,c.useRouter)(),f=(0,c.usePathname)(),{data:h,meta:p,isLoading:x}=(0,s.Z)(`/contacts?${t.toString()}`,{keepPreviousData:!0});return a.jsx("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[a.jsx("div",{className:"flex w-full flex-col gap-4 md:justify-between xl:h-12 xl:flex-row xl:items-center",children:a.jsx("div",{className:"ml-auto flex items-center gap-4",children:a.jsx(n.R,{iconPlacement:"end",placeholder:e("Search..."),className:"h-10 w-full rounded-lg",containerClass:"w-full sm:w-[280px]",value:r,onChange:e=>{e.preventDefault();let t=(0,o.w4)(e.target.value);d(e.target.value),m.replace(`${f}?${t.toString()}`)}})})}),a.jsx(l.Z,{className:"my-4"}),a.jsx(E,{data:h,isLoading:x,meta:p})]})})}},56140:(e,t,r)=>{"use strict";r.d(t,{Z:()=>k});var a=r(10326),n=r(77863),l=r(86508),s=r(11798),o=r(77132),c=r(6216),i=r(75817),d=r(40420),u=r(35047),m=r(93327),f=r(17577),h=r(70012),p=r(90772);let x=f.forwardRef(({className:e,...t},r)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:r,className:(0,n.ZP)("w-full caption-bottom text-sm",e),...t})}));x.displayName="Table";let g=f.forwardRef(({className:e,...t},r)=>a.jsx("thead",{ref:r,className:(0,n.ZP)("",e),...t}));g.displayName="TableHeader";let v=f.forwardRef(({className:e,...t},r)=>a.jsx("tbody",{ref:r,className:(0,n.ZP)("[&_tr:last-child]:border-0",e),...t}));v.displayName="TableBody",f.forwardRef(({className:e,...t},r)=>a.jsx("tfoot",{ref:r,className:(0,n.ZP)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let j=f.forwardRef(({className:e,...t},r)=>a.jsx("tr",{ref:r,className:(0,n.ZP)("transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));j.displayName="TableRow";let b=f.forwardRef(({className:e,...t},r)=>a.jsx("th",{ref:r,className:(0,n.ZP)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));b.displayName="TableHead";let w=f.forwardRef(({className:e,...t},r)=>a.jsx("td",{ref:r,className:(0,n.ZP)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));function k({data:e,isLoading:t=!1,structure:r,sorting:k,setSorting:N,padding:y=!1,className:Z,onRefresh:E,pagination:P}){let M=(0,f.useMemo)(()=>r,[r]),L=(0,u.useRouter)(),S=(0,u.usePathname)(),C=(0,u.useSearchParams)(),{t:O}=(0,h.$G)(),R=(0,l.b7)({data:e||[],columns:M,state:{sorting:k,onRefresh:E},onSortingChange:N,getCoreRowModel:(0,s.sC)(),getSortedRowModel:(0,s.tj)(),debugTable:!1});return t?a.jsx("div",{className:"rounded-md bg-background p-10",children:a.jsx("div",{className:"flex h-32 w-full items-center justify-center",children:O("Loading...")})}):e?.length?(0,a.jsxs)("div",{className:(0,n.ZP)(`${y?"p-3":"p-0"} overflow-x-hidden rounded-md bg-background`,Z),children:[(0,a.jsxs)(x,{children:[a.jsx(g,{children:R.getHeaderGroups().map(e=>a.jsx(j,{className:"border-none bg-background hover:bg-background",children:e.headers.map(e=>a.jsx(b,{className:(0,n.ZP)("",e?.column?.columnDef?.meta?.className),children:e.isPlaceholder?null:(0,a.jsxs)(p.z,{variant:"ghost",className:"flex h-10 w-full cursor-pointer items-center justify-between px-0 text-xs font-bold capitalize text-secondary-text hover:bg-transparent",onClick:e.column.getToggleSortingHandler(),children:[O((0,l.ie)(e.column.columnDef.header,e.getContext())),e.column.getCanSort()&&(({asc:a.jsx(c.Z,{size:"16",className:"text-secondary-text",transform:"rotate(180)"}),desc:a.jsx(c.Z,{size:"16",className:"text-secondary-text"})})[e.column.getIsSorted()]??a.jsx(c.Z,{size:"16",className:"text-transparent"}))]})},e.id))},e.id))}),a.jsx(v,{children:R.getRowModel().rows.map(e=>a.jsx(j,{className:"cursor-default border-none odd:bg-accent",children:e.getVisibleCells().map(e=>a.jsx(w,{className:"py-3 text-sm font-semibold",children:(0,l.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),P&&P.total>10&&a.jsx("div",{className:"pb-2 pt-6",children:a.jsx(m.Z,{showTotal:(e,t)=>O("Showing {{start}}-{{end}} of {{total}}",{start:t[0],end:t[1],total:e}),align:"start",current:P?.page,total:P?.total,pageSize:P?.limit,hideOnSinglePage:!0,showLessItems:!0,onChange:e=>{let t=new URLSearchParams(C);t.set("page",e.toString()),L.push(`${S}?${t.toString()}`)},className:"flex flex-row items-center justify-between gap-2",prevIcon:e=>a.jsx("a",{...e,children:a.jsx(i.Z,{size:"18"})}),nextIcon:e=>a.jsx("a",{...e,children:a.jsx(d.Z,{size:"18"})})})})]}):a.jsx("div",{className:"rounded-md bg-background p-10",children:(0,a.jsxs)("div",{className:"flex h-32 w-full flex-col items-center justify-center gap-4",children:[a.jsx(o.Z,{size:"38",variant:"Bulk",className:"text-primary-400"}),O("No data found!")]})})}w.displayName="TableCell",f.forwardRef(({className:e,...t},r)=>a.jsx("caption",{ref:r,className:(0,n.ZP)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},63761:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var a=r(10326);r(17577);var n=r(54432),l=r(77863),s=r(32894);function o({iconPlacement:e="start",className:t,containerClass:r,...o}){return(0,a.jsxs)("div",{className:(0,l.ZP)("relative flex items-center",r),children:[a.jsx(s.Z,{size:"20",className:(0,l.ZP)("absolute top-1/2 -translate-y-1/2","end"===e?"right-2.5":"left-2.5")}),a.jsx(n.I,{type:"text",className:(0,l.ZP)("h-10","end"===e?"pr-10":"pl-10",t),...o})]})}},54432:(e,t,r)=>{"use strict";r.d(t,{I:()=>s});var a=r(10326),n=r(17577),l=r(77863);let s=n.forwardRef(({className:e,type:t,...r},n)=>a.jsx("input",{type:t,className:(0,l.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:n,...r}));s.displayName="Input"},75584:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(90799),n=r(35047);function l(e,t){let r=(0,n.usePathname)(),l=(0,n.useSearchParams)(),s=(0,n.useRouter)(),[o,c]=e.split("?"),i=new URLSearchParams(c);i.has("page")||i.set("page","1"),i.has("limit")||i.set("limit","10");let d=`${o}?${i.toString()}`,{data:u,error:m,isLoading:f,mutate:h,...p}=(0,a.d)(d,t);return{refresh:()=>h(u),data:u?.data?.data??[],meta:u?.data?.meta,filter:(e,t,a)=>{let n=new URLSearchParams(l.toString());t?n.set(e,t.toString()):n.delete(e),s.replace(`${r}?${n.toString()}`),a?.()},isLoading:f,error:m,...p}}},32894:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var a=r(52920),n=r(17577),l=r.n(n),s=r(78439),o=r.n(s),c=["variant","color","size"],i=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},d=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 2c5.25 0 9.5 4.25 9.5 9.5S16.75 21 11.5 21 2 16.75 2 11.5c0-3.7 2.11-6.9 5.2-8.47M22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{opacity:".4",d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",fill:t}),l().createElement("path",{d:"M21.3 21.999c-.18 0-.36-.07-.49-.2l-1.86-1.86a.706.706 0 0 1 0-.99c.27-.27.71-.27.99 0l1.86 1.86c.27.27.27.71 0 .99-.14.13-.32.2-.5.2Z",fill:t}))},m=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19ZM22 22l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21.75c-5.65 0-10.25-4.6-10.25-10.25S5.85 1.25 11.5 1.25s10.25 4.6 10.25 10.25-4.6 10.25-10.25 10.25Zm0-19c-4.83 0-8.75 3.93-8.75 8.75s3.92 8.75 8.75 8.75 8.75-3.93 8.75-8.75-3.92-8.75-8.75-8.75ZM22 22.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}))},h=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M11.5 21a9.5 9.5 0 1 0 0-19 9.5 9.5 0 0 0 0 19Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{opacity:".4",d:"m22 22-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return l().createElement(i,{color:t});case"Broken":return l().createElement(d,{color:t});case"Bulk":return l().createElement(u,{color:t});case"Linear":default:return l().createElement(m,{color:t});case"Outline":return l().createElement(f,{color:t});case"TwoTone":return l().createElement(h,{color:t})}},x=(0,n.forwardRef)(function(e,t){var r=e.variant,n=e.color,s=e.size,o=(0,a._)(e,c);return l().createElement("svg",(0,a.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(r,n))});x.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="SearchNormal1"},72871:(e,t,r)=>{"use strict";r.d(t,{Z:()=>x});var a=r(52920),n=r(17577),l=r.n(n),s=r(78439),o=r.n(s),c=["variant","color","size"],i=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21.07 5.23c-1.61-.16-3.22-.28-4.84-.37v-.01l-.22-1.3c-.15-.92-.37-2.3-2.71-2.3h-2.62c-2.33 0-2.55 1.32-2.71 2.29l-.21 1.28c-.93.06-1.86.12-2.79.21l-2.04.2c-.42.04-.72.41-.***********.4.71.82.67l2.04-.2c5.24-.52 10.52-.32 15.82.21h.08c.38 0 .71-.29.75-.68a.766.766 0 0 0-.69-.82ZM19.23 8.14c-.24-.25-.57-.39-.91-.39H5.68c-.34 0-.68.14-.91.39-.23.25-.36.59-.34.94l.62 10.26c.11 1.52.25 3.42 3.74 3.42h6.42c3.49 0 3.63-1.89 3.74-3.42l.62-10.25c.02-.36-.11-.7-.34-.95Zm-5.57 9.61h-3.33c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h3.33c.41 0 .75.34.75.75s-.34.75-.75.75Zm.84-4h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},d=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98M8.5 4.97l.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3M15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14M18.85 9.14l-.65 10.07M10.33 16.5h3.33M12.82 12.5h1.68M9.5 12.5h.83",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},u=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21.07 5.23c-1.61-.16-3.22-.28-4.84-.37v-.01l-.22-1.3c-.15-.92-.37-2.3-2.71-2.3h-2.62c-2.33 0-2.55 1.32-2.71 2.29l-.21 1.28c-.93.06-1.86.12-2.79.21l-2.04.2c-.42.04-.72.41-.***********.4.71.82.67l2.04-.2c5.24-.52 10.52-.32 15.82.21h.08c.38 0 .71-.29.75-.68a.766.766 0 0 0-.69-.82Z",fill:t}),l().createElement("path",{opacity:".399",d:"M19.23 8.14c-.24-.25-.57-.39-.91-.39H5.68c-.34 0-.68.14-.91.39-.23.25-.36.59-.34.94l.62 10.26c.11 1.52.25 3.42 3.74 3.42h6.42c3.49 0 3.63-1.89 3.74-3.42l.62-10.25c.02-.36-.11-.7-.34-.95Z",fill:t}),l().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.58 17a.75.75 0 0 1 .75-.75h3.33a.75.75 0 0 1 0 1.5h-3.33a.75.75 0 0 1-.75-.75ZM8.75 13a.75.75 0 0 1 .75-.75h5a.75.75 0 0 1 0 1.5h-5a.75.75 0 0 1-.75-.75Z",fill:t}))},m=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98M8.5 4.97l.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3M18.85 9.14l-.65 10.07C18.09 20.78 18 22 15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14M10.33 16.5h3.33M9.5 12.5h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 6.73h-.08c-5.29-.53-10.57-.73-15.8-.2l-2.04.2a.755.755 0 0 1-.83-.68c-.04-.42.26-.78.67-.82l2.04-.2c5.32-.54 10.71-.33 **********.***********.82a.74.74 0 0 1-.74.68Z",fill:t}),l().createElement("path",{d:"M8.5 5.72c-.04 0-.08 0-.13-.01a.753.753 0 0 1-.61-.86l.22-1.31c.16-.96.38-2.29 2.71-2.29h2.62c2.34 0 2.56 1.38 2.71 2.3l.22 1.3c.07.41-.21.8-.61.86-.41.07-.8-.21-.86-.61l-.22-1.3c-.14-.87-.17-1.04-1.23-1.04H10.7c-1.06 0-1.08.14-1.23 1.03l-.23 1.3a.75.75 0 0 1-.74.63ZM15.21 22.752H8.79c-3.49 0-3.63-1.93-3.74-3.49L4.4 9.192c-.03-.41.29-.77.7-.8.42-.02.77.29.8.7l.65 10.07c.11 1.52.15 2.09 2.24 2.09h6.42c2.1 0 2.14-.57 2.24-2.09l.65-10.07c.03-.41.39-.72.8-.7.41.03.73.38.7.8l-.65 10.07c-.11 1.56-.25 3.49-3.74 3.49Z",fill:t}),l().createElement("path",{d:"M13.66 17.25h-3.33c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h3.33c.41 0 .75.34.75.75s-.34.75-.75.75ZM14.5 13.25h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75Z",fill:t}))},h=function(e){var t=e.color;return l().createElement(l().Fragment,null,l().createElement("path",{d:"M21 5.98c-3.33-.33-6.68-.5-10.02-.5-1.98 0-3.96.1-5.94.3L3 5.98",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{opacity:".34",d:"m8.5 4.97.22-1.31C8.88 2.71 9 2 10.69 2h2.62c1.69 0 1.82.75 1.97 1.67l.22 1.3",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{d:"m18.85 9.14-.65 10.07C18.09 20.78 18 22 15.21 22H8.79C6 22 5.91 20.78 5.8 19.21L5.15 9.14",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),l().createElement("path",{opacity:".34",d:"M10.33 16.5h3.33M9.5 12.5h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return l().createElement(i,{color:t});case"Broken":return l().createElement(d,{color:t});case"Bulk":return l().createElement(u,{color:t});case"Linear":default:return l().createElement(m,{color:t});case"Outline":return l().createElement(f,{color:t});case"TwoTone":return l().createElement(h,{color:t})}},x=(0,n.forwardRef)(function(e,t){var r=e.variant,n=e.color,s=e.size,o=(0,a._)(e,c);return l().createElement("svg",(0,a.a)({},o,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(r,n))});x.propTypes={variant:o().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:o().string,size:o().oneOfType([o().string,o().number])},x.defaultProps={variant:"Linear",color:"currentColor",size:"24"},x.displayName="Trash"},14946:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),n=r(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(n.a,{})})}},98775:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\contacts\page.tsx#default`)},88728:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(19510),n=r(40099),l=r(76609);function s({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(l.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(n.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}r(71159)},80549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),n=r(48413);function l(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(n.a,{})})}},27069:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),n=r(98775);function l(){return a.jsx(n.default,{})}},99987:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var a=r(89637),n=r(89162),l=r(64579);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var a;a=r[t],t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e,t,r){t&&t.defaultCountry&&!(0,l.aS)(t.defaultCountry,r)&&(t=o(o({},t),{},{defaultCountry:void 0}));try{return(0,a.Z)(e,t,r)}catch(e){if(e instanceof n.Z);else throw e}}},33436:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var a=r(34878),n=r(36159),l=r(99987);function s(){var e=(0,n.Z)(arguments),t=e.text,r=e.options,a=e.metadata;return(0,l.Z)(t,r,a)}function o(){return(0,a.Z)(s,arguments)}}};