(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{52356:function(e,a,s){Promise.resolve().then(s.bind(s,98342))},98342:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return h}});var t=s(57437),n=s(2541),l=s(85539),r=s(27186),c=s(85017),i=s(6512),u=s(75730),d=s(94508),o=s(99376),m=s(2265),f=s(43949);function h(){var e;let a=(0,o.useSearchParams)(),[s,h]=m.useState(null!==(e=a.get("search"))&&void 0!==e?e:""),x=(0,o.useRouter)(),p=(0,o.usePathname)(),{t:v}=(0,f.$G)(),{data:g,isLoading:j,meta:N,refresh:w}=(0,u.Z)("/admin/deposits?".concat(a.toString()));return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(l.R,{value:s,onChange:e=>{e.preventDefault();let a=(0,d.w4)(e.target.value);h(e.target.value),x.replace("".concat(p,"?").concat(a.toString()))},iconPlacement:"end",className:"flex-1",placeholder:v("Search...")}),(0,t.jsx)(c.k,{canFilterByGateway:!0}),(0,t.jsx)(r._,{url:"/admin/deposits/export/all"})]}),(0,t.jsx)("div",{})]}),(0,t.jsx)(i.Z,{className:"my-4"}),(0,t.jsx)(n.Z,{data:g,meta:N,isLoading:j,refresh:w})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,227,56993,85017,30585,92971,95030,1744],function(){return e(e.s=52356)}),_N_E=e.O()}]);