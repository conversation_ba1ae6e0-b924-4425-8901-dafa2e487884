(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[56189],{93770:function(e,s,t){Promise.resolve().then(t.bind(t,36553))},36553:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return ep}});var r=t(57437),n=t(85487),a=t(6596),l=t(79981),i=t(94508),o=t(19571),d=t(15066),c=t(98491),u=t(17943),x=t(99376),m=t(43949),p=t(85323),f=t(41709),h=t(52323),j=t(62869),C=t(15681),v=t(95186),g=t(26815),b=t(81123),y=t(40593),N=t(13590),w=t(22291),z=t(2265),Z=t(29501),L=t(14438),I=t(31229);let k=I.z.object({street:I.z.string({required_error:"Street is required."}),country:I.z.string({required_error:"Country is required."}),city:I.z.string({required_error:"city is required."}),zipCode:I.z.string({required_error:"Zip code is required."})});function G(e){let{customer:s,onMutate:t}=e,[l,i]=z.useTransition(),[o,d]=z.useState(),{getCountryByCode:c}=(0,y.F)(),{t:u}=(0,m.$G)(),x=(0,Z.cI)({resolver:(0,N.F)(k),defaultValues:{street:"",city:"",country:"",zipCode:""}});return z.useEffect(()=>{if(s&&(null==s?void 0:s.address)){var e,t,r,n;c(null==s?void 0:null===(e=s.address)||void 0===e?void 0:e.countryCode,d),x.reset({street:null==s?void 0:null===(t=s.address)||void 0===t?void 0:t.addressLine,city:null==s?void 0:null===(r=s.address)||void 0===r?void 0:r.city,country:null==s?void 0:s.address.countryCode,zipCode:null==s?void 0:null===(n=s.address)||void 0===n?void 0:n.zipCode})}return()=>{x.reset({street:"",city:"",country:"",zipCode:""})}},[s]),(0,r.jsx)(C.l0,{...x,children:(0,r.jsx)("form",{onSubmit:x.handleSubmit(e=>{i(async()=>{let r=await (0,b.H)(e,s.id);(null==r?void 0:r.status)?(t(),L.toast.success(r.message)):L.toast.error(u(r.message))})}),className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(a.Qd,{value:"ADDRESS_INFORMATION",className:"border-none px-4 py-0",children:[(0,r.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:u("Address")})}),(0,r.jsxs)(a.vF,{className:"flex flex-col gap-2 border-t px-1 pt-4",children:[(0,r.jsx)(g.Z,{children:u("Full mailing address")}),(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-2.5",children:[(0,r.jsx)(C.Wi,{control:x.control,name:"street",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:u("Full name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:x.control,name:"country",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(h.g,{defaultValue:o,onSelectChange:e=>s.onChange(e.code.cca2)})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:x.control,name:"city",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12 md:col-span-6",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:u("City"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:x.control,name:"zipCode",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12 md:col-span-6",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:u("Zip code"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,r.jsx)(C.zG,{})]})}})]}),(0,r.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,r.jsxs)(j.z,{disabled:l,children:[(0,r.jsxs)(f.J,{condition:!l,children:[u("Save"),(0,r.jsx)(w.Z,{size:20})]}),(0,r.jsx)(f.J,{condition:l,children:(0,r.jsx)(n.Loader,{title:u("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}var R=t(19060),S=t(26110),F=t(84190),A=t(6512),B=t(4995),M=t(7211),P=t(83504);function q(e){let{wallets:s,onMutate:t}=e,{t:n}=(0,m.$G)();return(0,r.jsxs)(a.Qd,{value:"BALANCE",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,r.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:n("Balance")})}),(0,r.jsx)(a.vF,{className:"grid grid-cols-12 gap-4 border-t pt-4",children:null==s?void 0:s.map(e=>(0,r.jsx)(E,{item:e,onMutate:t},e.id))})]})}function E(e){let{item:s,onMutate:t}=e,{t:n}=(0,m.$G)();return(0,r.jsxs)("div",{className:"relative col-span-12 flex flex-col gap-2 rounded-xl border border-border bg-accent p-6 text-accent-foreground sm:col-span-6 md:col-span-4 lg:col-span-3",children:[(0,r.jsx)("div",{className:"absolute right-1 top-1 flex items-center gap-1",children:(0,r.jsxs)(F.h_,{children:[(0,r.jsx)(F.$F,{asChild:!0,children:(0,r.jsx)(j.z,{variant:"ghost",size:"icon",className:"size-8 hover:bg-secondary-text/30 active:bg-secondary-text/50",children:(0,r.jsx)(P.Z,{strokeWidth:3,size:17})})}),(0,r.jsxs)(F.AW,{className:"flex flex-col rounded-sm",align:"end",children:[(0,r.jsx)(J,{wallet:s,userId:null==s?void 0:s.userId,onMutate:t}),(0,r.jsx)(W,{wallet:s,userId:null==s?void 0:s.userId,onMutate:t}),(0,r.jsx)(_,{wallet:s,onMutate:t})]})]})}),(0,r.jsx)("span",{className:"text-xs font-normal leading-4",children:s.currency.code}),(0,r.jsxs)("h6",{className:"text-sm font-semibold leading-5",children:[s.balance," ",s.currency.code]}),(null==s?void 0:s.dailyTransferAmount)?(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsxs)("span",{className:"text-xs font-normal leading-4",children:[n("Daily transfer limit"),":"]}),(0,r.jsxs)("h6",{className:"text-xs font-normal leading-4",children:[null==s?void 0:s.dailyTransferAmount," ",s.currency.code]})]}):null]})}function J(e){let{userId:s,wallet:t,onMutate:a}=e,[l,i]=z.useState(!1),[o,d]=z.useState(!1),{t:c}=(0,m.$G)(),[u,x]=z.useState({amount:"0",currencyCode:null==t?void 0:t.currency.code,userId:s,keepRecords:!0}),p=()=>{x({amount:"0",currencyCode:null==t?void 0:t.currency.code,userId:s,keepRecords:!0})},f=async e=>{e.preventDefault(),d(!0);let s=await (0,B.y)({amount:Number(u.amount),currencyCode:u.currencyCode,userId:u.userId,keepRecords:u.keepRecords},"add");s.status?(L.toast.success(s.message),a(),d(!1),i(!1)):(L.toast.error(s.message),d(!1))};return(0,r.jsxs)(S.Vq,{open:l,onOpenChange:e=>{i(e),p()},children:[(0,r.jsx)(S.hg,{asChild:!0,children:(0,r.jsx)(j.z,{variant:"ghost",className:"relative flex h-8 cursor-pointer select-none items-center justify-start rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",children:c("Add balance")})}),(0,r.jsxs)(S.cZ,{children:[(0,r.jsxs)(S.fK,{children:[(0,r.jsx)(S.$N,{className:"text-semibold",children:c("Add Balance")}),(0,r.jsx)(S.Be,{className:"hidden"})]}),(0,r.jsx)(A.Z,{}),(0,r.jsx)("div",{children:(0,r.jsxs)("form",{onSubmit:f,className:"flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)(g.Z,{className:"text-sm",children:[" ",c("Balance")," "]}),(0,r.jsx)(v.I,{type:"number",value:u.amount,min:0,onChange:e=>x(s=>({...s,amount:e.target.value}))})]}),(0,r.jsxs)(g.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,r.jsx)(R.X,{checked:u.keepRecords,onCheckedChange:e=>x(s=>({...s,keepRecords:e}))}),(0,r.jsx)("span",{children:c("Keep in record")})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,r.jsx)(S.GG,{asChild:!0,children:(0,r.jsx)(j.z,{type:"button",variant:"ghost",children:"Cancel"})}),(0,r.jsx)(j.z,{disabled:o,children:o?(0,r.jsx)(n.Loader,{title:c("Uploading..."),className:"text-primary-foreground"}):c("Update")})]})]})})]})]})}function W(e){let{userId:s,wallet:t,onMutate:a}=e,[l,i]=z.useState(!1),[o,d]=z.useState(!1),{t:c}=(0,m.$G)(),[u,x]=z.useState({amount:"0",currencyCode:null==t?void 0:t.currency.code,userId:s,keepRecords:!0}),p=()=>{x({amount:"0",currencyCode:null==t?void 0:t.currency.code,userId:s,keepRecords:!0})},f=async e=>{e.preventDefault(),i(!0);let s=await (0,B.y)({amount:Number(u.amount),currencyCode:u.currencyCode,userId:u.userId,keepRecords:u.keepRecords},"remove");s.status?(p(),a(),d(!1),i(!1),L.toast.success(s.status)):(i(!1),L.toast.error(s.status))};return(0,r.jsxs)(S.Vq,{open:o,onOpenChange:e=>{d(e),p()},children:[(0,r.jsx)(S.hg,{asChild:!0,children:(0,r.jsx)(j.z,{variant:"ghost",className:"relative flex h-8 cursor-pointer select-none items-center justify-start rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",children:c("Remove balance")})}),(0,r.jsxs)(S.cZ,{children:[(0,r.jsxs)(S.fK,{children:[(0,r.jsx)(S.$N,{className:"text-semibold",children:c("Remove Balance")}),(0,r.jsx)(S.Be,{className:"hidden"})]}),(0,r.jsx)(A.Z,{}),(0,r.jsx)("div",{children:(0,r.jsxs)("form",{onSubmit:f,className:"flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)(g.Z,{className:"text-sm",children:[" ",c("Balance")," "]}),(0,r.jsx)(v.I,{type:"number",value:u.amount,min:0,onChange:e=>x(s=>({...s,amount:e.target.value}))})]}),(0,r.jsxs)(g.Z,{className:"flex items-center gap-2.5 text-sm",children:[(0,r.jsx)(R.X,{checked:u.keepRecords,onCheckedChange:e=>x(s=>({...s,keepRecords:e}))}),(0,r.jsx)("span",{children:c("Keep in record")})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,r.jsx)(S.GG,{asChild:!0,children:(0,r.jsx)(j.z,{type:"button",variant:"ghost",children:c("Cancel")})}),(0,r.jsx)(j.z,{disabled:l,children:l?(0,r.jsx)(n.Loader,{title:c("Uploading..."),className:"text-primary-foreground"}):c("Update")})]})]})})]})]})}function _(e){let{wallet:s,onMutate:t}=e,[a,l]=z.useState(!1),[i,o]=z.useState(!1),{t:d}=(0,m.$G)(),[c,u]=z.useState(null==s?void 0:s.dailyTransferAmount),x=()=>{u(c||0)},p=async e=>{e.preventDefault(),l(!0);let r={dailyTransferAmount:Number(c)},n=await (0,M.I)(r,null==s?void 0:s.id);n.status?(x(),t(),o(!1),l(!1),t(),L.toast.success(n.status)):(l(!1),L.toast.error(n.status))};return(0,r.jsxs)(S.Vq,{open:i,onOpenChange:e=>{o(e),x()},children:[(0,r.jsx)(S.hg,{asChild:!0,children:(0,r.jsx)(j.z,{variant:"ghost",className:"relative flex h-8 cursor-pointer select-none items-center justify-start rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",children:d("Transfer limit")})}),(0,r.jsxs)(S.cZ,{children:[(0,r.jsxs)(S.fK,{children:[(0,r.jsx)(S.$N,{className:"text-semibold flex items-center gap-4",children:d("Transfer amount limit")}),(0,r.jsx)(S.Be,{className:"hidden"})]}),(0,r.jsx)(A.Z,{}),(0,r.jsx)("div",{children:(0,r.jsxs)("form",{onSubmit:p,className:"flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)(g.Z,{className:"text-sm",children:[" ",d("Daily transfer amount")," "]}),(0,r.jsx)(v.I,{type:"string",value:c,min:0,onChange:e=>u(e.target.value)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-2.5",children:[(0,r.jsx)(S.GG,{asChild:!0,children:(0,r.jsx)(j.z,{type:"button",variant:"ghost",children:d("Cancel")})}),(0,r.jsx)(j.z,{disabled:a,children:a?(0,r.jsx)(n.Loader,{title:d("Uploading..."),className:"text-primary-foreground"}):d("Update")})]})]})})]})]})}var V=t(65613),O=t(66424),D=t(97751);async function T(e,s){try{let t=await l.Z.put("/admin/customers/convert-account/".concat(s),e);return(0,D.B)(t)}catch(e){return(0,D.D)(e)}}var X=t(90433);let $=I.z.object({roleId:I.z.number().optional(),name:I.z.string({required_error:"Agent name is required."}),occupation:I.z.string({required_error:"Occupation is required."}),whatsapp:I.z.string({required_error:"Whatsapp number/link is required."})});function U(e){let{customer:s}=e,[t,a]=z.useTransition(),[l,i]=z.useState(!1),{t:o}=(0,m.$G)(),d=(0,Z.cI)({resolver:(0,N.F)($),defaultValues:{roleId:4,name:"",occupation:"",whatsapp:""}});return(0,r.jsxs)("div",{className:"flex flex-1 flex-col items-center rounded-xl border border-border bg-background px-6 py-4 sm:w-[300px]",children:[(0,r.jsx)("div",{className:"mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-spacial-red-foreground/50",children:(0,r.jsx)(O.S,{})}),(0,r.jsx)(A.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,r.jsx)("div",{className:"mt-2 px-2",children:(0,r.jsxs)(S.Vq,{open:l,onOpenChange:i,children:[(0,r.jsx)(S.hg,{disabled:(null==s?void 0:s.roleId)===4,asChild:!0,children:(0,r.jsxs)(j.z,{className:"rounded-xl",children:[o("Convert to Agent"),(0,r.jsx)(w.Z,{size:16})]})}),(0,r.jsxs)(S.cZ,{className:"flex max-w-[716px] flex-col gap-6 p-16",children:[(0,r.jsxs)(S.fK,{className:"p-0",children:[(0,r.jsx)(S.$N,{className:"text-[32px] font-medium leading-10",children:o("Add agent information")}),(0,r.jsx)(S.Be,{className:"hidden","aria-hidden":!0,children:o("dialog description")})]}),(0,r.jsx)(A.Z,{}),(0,r.jsx)(C.l0,{...d,children:(0,r.jsxs)("form",{onSubmit:d.handleSubmit(e=>{a(async()=>{let t=await T({roleId:e.roleId,agent:e},s.id);(null==t?void 0:t.status)?(i(!1),L.toast.success(t.message)):L.toast.error(o(t.message))})}),className:"flex flex-col gap-y-6",children:[(0,r.jsx)(C.Wi,{name:"name",control:d.control,render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(v.I,{type:"hidden","aria-hidden":!0,placeholder:o("Enter agent name"),...s}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{name:"name",control:d.control,render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:"Agent Name"}),(0,r.jsx)(v.I,{type:"text",placeholder:o("Enter agent name"),...s}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{name:"occupation",control:d.control,render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:o("Job/Occupation")}),(0,r.jsx)(v.I,{type:"text",placeholder:o("Enter your job/occupation"),...s}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{name:"whatsapp",control:d.control,render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:o("WhatsApp number/link")}),(0,r.jsx)(v.I,{type:"text",placeholder:o("Enter your WhatsApp account number or link"),...s}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(j.z,{onClick:()=>i(!1),type:"button",variant:"outline",children:[(0,r.jsx)(X.Z,{}),o("Back")]}),(0,r.jsxs)(j.z,{className:"w-[286px]",disabled:t,children:[(0,r.jsxs)(f.J,{condition:!t,children:[o("Convert"),(0,r.jsx)(w.Z,{})]}),(0,r.jsx)(f.J,{condition:t,children:(0,r.jsx)(n.Loader,{title:o("Converting..."),className:"text-primary-foreground"})})]})]})]})})]})]})})]})}var H=t(28456);function K(){let{t:e}=(0,m.$G)();return(0,r.jsxs)("div",{className:"flex flex-1 flex-col items-center rounded-xl border border-border bg-background px-6 py-4",children:[(0,r.jsx)("div",{className:"mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-selected text-primary",children:(0,r.jsx)(H.Z,{variant:"Bold",size:32})}),(0,r.jsx)(A.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,r.jsx)("div",{className:"mt-2 px-2",children:(0,r.jsxs)(j.z,{variant:"secondary",disabled:!0,className:"rounded-xl",children:[e("Convert to Customer"),(0,r.jsx)(w.Z,{size:16})]})})]})}var Q=t(37128),Y=t(25429),ee=t(71792),es=t(73490);function et(e){let{onPrev:s,onSubmit:t,nextButtonLabel:a,isLoading:l=!1}=e,{t:i}=(0,m.$G)(),o=(0,Z.cI)({resolver:(0,N.F)(ee.yC),defaultValues:{name:"",email:"",license:"",street:"",country:"",city:"",zipCode:""}});return(0,r.jsx)(C.l0,{...o,children:(0,r.jsx)("form",{onSubmit:o.handleSubmit(t),children:(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)(C.Wi,{control:o.control,name:"name",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:i("Merchant name")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",className:"placeholder:font-normal",placeholder:i("Enter merchant name"),...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:o.control,name:"email",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:i("Merchant email")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"email",placeholder:i("Enter your merchant email address"),...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:o.control,name:"license",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsxs)(C.lX,{className:"flex items-center justify-between gap-4",children:[i("Merchant proof"),(0,r.jsxs)(S.Vq,{children:[(0,r.jsx)(S.hg,{className:"inline-flex items-center gap-1",asChild:!0,children:(0,r.jsxs)(j.z,{type:"button",variant:"ghost",size:"sm",children:[i("Help"),(0,r.jsx)(es.Z,{size:16})]})}),(0,r.jsxs)(S.cZ,{children:[(0,r.jsxs)(S.fK,{children:[(0,r.jsx)(S.$N,{children:i("Dialog title")}),(0,r.jsx)(S.Be,{children:i("Make changes to your profile here. Click save when youre done.")})]}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"flex aspect-video w-full items-center justify-center rounded-lg bg-neutral-200",children:(0,r.jsx)(Y.X,{})})})]})]})]}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:i("Enter merchant license or register number"),...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,r.jsx)(g.Z,{className:"col-span-12",children:i("Merchant address")}),(0,r.jsx)(C.Wi,{control:o.control,name:"street",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:i("Address Line"),...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:o.control,name:"country",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(h.g,{onSelectChange:e=>s.onChange(e.code.cca2)})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:o.control,name:"city",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-6",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:i("City"),...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:o.control,name:"zipCode",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-6",children:[(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:i("Zip Code"),...s})}),(0,r.jsx)(C.zG,{})]})}})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,r.jsxs)(j.z,{className:"p-4 text-base leading-[22px]",variant:"outline",type:"button",onClick:s,children:[(0,r.jsx)(X.Z,{size:24}),i("Back")]}),(0,r.jsxs)(j.z,{type:"submit",disabled:l,className:"w-[286px] p-4 text-base leading-[22px]",children:[(0,r.jsxs)(f.J,{condition:!l,children:[a,(0,r.jsx)(w.Z,{size:16})]}),(0,r.jsx)(f.J,{condition:l,children:(0,r.jsx)(n.Loader,{className:"text-background"})})]})]})]})})})}function er(e){let{customer:s}=e,[t,n]=z.useTransition(),[a,l]=z.useState(!1),{t:i}=(0,m.$G)();return(0,r.jsxs)("div",{className:"flex flex-1 flex-col items-center rounded-xl border border-border bg-background px-6 py-4",children:[(0,r.jsx)("div",{className:"mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-spacial-blue-foreground/50",children:(0,r.jsx)(Q.Z,{})}),(0,r.jsx)(A.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,r.jsx)("div",{className:"mt-2 px-2",children:(0,r.jsxs)(S.Vq,{open:a,onOpenChange:l,children:[(0,r.jsx)(S.hg,{asChild:!0,children:(0,r.jsxs)(j.z,{className:"rounded-xl",children:[i("Convert to Merchant"),(0,r.jsx)(w.Z,{size:16})]})}),(0,r.jsxs)(S.cZ,{className:"flex max-h-[90%] max-w-[716px] flex-col gap-6 p-0",children:[(0,r.jsx)(S.fK,{className:"px-16 pb-0 pt-16",children:(0,r.jsx)(S.$N,{className:"text-[32px] font-medium leading-10",children:i("Add merchant information")})}),(0,r.jsx)(A.Z,{className:"mx-16"}),(0,r.jsx)("div",{className:"h-auto overflow-y-auto px-16 pb-16 pt-0",children:(0,r.jsx)(et,{onPrev:()=>{l(!1)},isLoading:t,onSubmit:e=>{n(async()=>{let t=await T({roleId:3,merchant:{name:e.name,email:e.email,proof:e.license,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city}},s.id);(null==t?void 0:t.status)?(l(!1),L.toast.success(t.message)):L.toast.error(i(t.message))})},nextButtonLabel:"Convert"})})]})]})})]})}function en(e){let{customer:s}=e,{t}=(0,m.$G)();return(0,r.jsxs)(a.Qd,{value:"ConvertAccountType",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,r.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:t("Convert account type")})}),(0,r.jsxs)(a.vF,{className:"flex flex-col gap-4 border-t p-[1px] py-4",children:[(0,r.jsxs)(V.bZ,{className:"border-none bg-transparent shadow-default",children:[(0,r.jsx)(u.Z,{color:"#0B6A0B",variant:"Bulk",className:"-mt-1"}),(0,r.jsx)(V.Cd,{className:"pl-2 text-sm font-semibold leading-5",children:t("This is a Customer Account")}),(0,r.jsx)(V.X,{className:"pl-2 text-sm font-normal",children:t("You will need to add additional information to convert this account into a Merchant of Agent.")})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-y-4 sm:gap-4",children:[(0,r.jsx)(K,{}),(0,r.jsx)(U,{customer:s}),(0,r.jsx)(er,{customer:s})]})]})]})}var ea=t(39785),el=t(78939),ei=t(18629),eo=t(74991),ed=t(17110),ec=t(54995);let eu=I.z.object({profile:ec.K,firstName:I.z.string({required_error:"Full name is required."}),lastName:I.z.string({required_error:"Full name is required."}),email:I.z.string({required_error:"Email is required."}),phone:I.z.string({required_error:"Phone is required."}),dateOfBirth:I.z.date({required_error:"Date of Birth is required."}),gender:I.z.string({required_error:"Gender is required"})});function ex(e){let{customer:s,onMutate:t,isLoading:l=!1}=e,[o,d]=(0,z.useTransition)(),{t:c}=(0,m.$G)(),u=(0,Z.cI)({resolver:(0,N.F)(eu),defaultValues:{profile:"",firstName:"",lastName:"",email:"",phone:"",dateOfBirth:void 0,gender:""}}),x=(0,z.useCallback)(()=>{if(s){var e;u.reset({firstName:null==s?void 0:s.firstName,lastName:null==s?void 0:s.lastName,email:null==s?void 0:null===(e=s.user)||void 0===e?void 0:e.email,phone:null==s?void 0:s.phone,dateOfBirth:new Date(null==s?void 0:s.dob),gender:s.gender})}},[l]);return(0,z.useEffect)(()=>x(),[x]),(0,r.jsx)(C.l0,{...u,children:(0,r.jsx)("form",{onSubmit:u.handleSubmit(e=>{d(async()=>{let r=await (0,ed.n)(e,s.id);(null==r?void 0:r.status)?(t(),L.toast.success(r.message)):L.toast.error(c(r.message))})}),className:"rounded-xl border border-border bg-background",children:(0,r.jsxs)(a.Qd,{value:"PROFILE_INFORMATION",className:"border-none px-4 py-0",children:[(0,r.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,r.jsx)("p",{className:"text-base font-medium leading-[22px]",children:c("Profile")})}),(0,r.jsxs)(a.vF,{className:"flex flex-col gap-6 border-t px-1 py-4",children:[(0,r.jsx)(C.Wi,{control:u.control,name:"profile",render:e=>{let{field:t}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:c("Profile picture")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(el.S,{id:"documentFrontSideFile",defaultValue:(0,i.qR)(null==s?void 0:s.profileImage),onChange:e=>{t.onChange(e)},className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,r.jsx)(Y.X,{}),(0,r.jsx)("p",{className:"text-sm font-normal text-primary",children:c("Upload photo")})]})})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,r.jsx)(C.Wi,{control:u.control,name:"firstName",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,r.jsx)(C.lX,{children:c("First name")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:c("First name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:u.control,name:"lastName",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{className:"col-span-12 lg:col-span-6",children:[(0,r.jsx)(C.lX,{children:c("Last name")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"text",placeholder:c("Last name"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,r.jsx)(C.zG,{})]})}})]}),(0,r.jsx)(C.Wi,{control:u.control,name:"email",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:c("Email")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(v.I,{type:"email",placeholder:c("Enter your email"),className:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:u.control,name:"phone",render:e=>{let{field:t}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:c("Phone")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(ei.E,{value:null==s?void 0:s.phone,onChange:t.onChange,inputClassName:"text-base font-normal disabled:cursor-auto disabled:bg-accent disabled:opacity-100",onBlur:e=>{e?u.setError("phone",{type:"custom",message:c(e)}):u.clearErrors("phone")}})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:u.control,name:"dateOfBirth",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:c("Date of birth")}),(0,r.jsx)(C.NI,{children:(0,r.jsx)(ea.M,{...s})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)(C.Wi,{control:u.control,name:"gender",render:e=>{let{field:s}=e;return(0,r.jsxs)(C.xJ,{children:[(0,r.jsx)(C.lX,{children:c("Gender")}),(0,r.jsx)(C.NI,{children:(0,r.jsxs)(eo.E,{defaultValue:s.value,onValueChange:s.onChange,className:"flex",children:[(0,r.jsxs)(g.Z,{htmlFor:"GenderMale","data-selected":"male"===s.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,r.jsx)(eo.m,{id:"GenderMale",value:"male",className:"absolute opacity-0"}),(0,r.jsx)("span",{children:c("Male")})]}),(0,r.jsxs)(g.Z,{htmlFor:"GenderFemale","data-selected":"female"===s.value,className:"relative h-12 flex-1 rounded-xl border-2 border-transparent bg-muted p-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected",children:[(0,r.jsx)(eo.m,{id:"GenderFemale",value:"female",className:"absolute opacity-0"}),(0,r.jsx)("span",{children:c("Female")})]})]})}),(0,r.jsx)(C.zG,{})]})}}),(0,r.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,r.jsxs)(j.z,{disabled:o,children:[(0,r.jsx)(f.J,{condition:o,children:(0,r.jsx)(n.Loader,{className:"text-primary-foreground"})}),(0,r.jsxs)(f.J,{condition:!o,children:[c("Save"),(0,r.jsx)(w.Z,{size:20})]})]})})]})]})})})}function em(e){let{title:s,status:t,icon:n,iconClass:a,statusClass:l,className:o}=e;return(0,r.jsxs)("div",{className:(0,i.ZP)("inline-flex items-center gap-4 rounded-xl bg-background px-6 py-3 shadow-default",o),children:[(0,r.jsx)("div",{className:(0,i.ZP)("flex h-[54px] w-[54px] items-center justify-center rounded-full",a),children:n({size:34,variant:"Bulk"})}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,r.jsxs)("span",{className:"block text-xs font-normal leading-4",children:[s," "]}),(0,r.jsx)("h6",{className:(0,i.ZP)("text-sm font-semibold leading-5",l),children:t})]})]})}function ep(){var e,s,t,f,h,j,C;let v=(0,x.useParams)(),{t:g}=(0,m.$G)(),{data:b,isLoading:y,mutate:N}=(0,p.ZP)("/admin/customers/".concat(v.customerId),e=>(0,l.Z)(e));if(y)return(0,r.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,r.jsx)(n.Loader,{})});let w=null==b?void 0:b.data,z=null==w?void 0:null===(s=w.user)||void 0===s?void 0:null===(e=s.wallets)||void 0===e?void 0:e.find(e=>e.default);return(0,r.jsx)(a.UQ,{type:"multiple",defaultValue:["PROFILE_INFORMATION","ADDRESS_INFORMATION","BALANCE","ConvertAccountType"],children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,r.jsxs)("div",{className:"grid w-full grid-cols-12 gap-4",children:[(0,r.jsx)(em,{title:g("Account Status"),icon:e=>(0,r.jsx)(o.Z,{...e,variant:"Outline"}),statusClass:(null==w?void 0:null===(t=w.user)||void 0===t?void 0:t.status)?"text-success":"",status:(null==w?void 0:null===(f=w.user)||void 0===f?void 0:f.status)?"Active":"Inactive",iconClass:"bg-success/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,r.jsx)(em,{title:g("KYC Status"),icon:e=>(0,r.jsx)(d.Z,{className:(0,i.ZP)(e.className,"text-primary"),...e}),statusClass:"text-primary",status:g((null==w?void 0:null===(h=w.user)||void 0===h?void 0:h.kycStatus)?"Verified":"Pending Verification"),iconClass:"bg-primary/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,r.jsx)(em,{title:g("Default Wallet"),icon:e=>(0,r.jsx)(c.Z,{...e}),statusClass:"text-spacial-blue",status:"".concat(null==z?void 0:z.balance," ").concat(null==z?void 0:null===(j=z.currency)||void 0===j?void 0:j.code),iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"}),(0,r.jsx)(em,{title:g("Account type"),icon:e=>(0,r.jsx)(u.Z,{...e}),statusClass:"text-spacial-blue",status:"Customer",iconClass:"bg-important/20",className:"col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-3"})]}),(0,r.jsx)(q,{wallets:null==w?void 0:null===(C=w.user)||void 0===C?void 0:C.wallets,onMutate:()=>N(b)}),(0,r.jsx)(ex,{isLoading:y,customer:w,onMutate:()=>N(b)}),(0,r.jsx)(G,{customer:w,onMutate:()=>N(b)}),(0,r.jsx)(en,{customer:w})]})})}},78939:function(e,s,t){"use strict";t.d(s,{S:function(){return o}});var r=t(57437),n=t(94508),a=t(33145),l=t(2265),i=t(85598);function o(e){let{defaultValue:s,onChange:t,className:o,children:d,disabled:c=!1,id:u}=e,[x,m]=l.useState(s);l.useEffect(()=>{m(s)},[s]);let{getRootProps:p,getInputProps:f}=(0,i.uI)({onDrop:e=>{let s=null==e?void 0:e[0];s&&(t(s),m(URL.createObjectURL(s)))},disabled:c});return(0,r.jsxs)("div",{...p({className:(0,n.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o)}),children:[!!x&&(0,r.jsx)(a.default,{src:x,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,r.jsx)("input",{id:u,...f()}),!x&&(0,r.jsx)("div",{children:d})]})}},66424:function(e,s,t){"use strict";t.d(s,{S:function(){return a}});var r=t(57437),n=t(94508);function a(e){let{className:s}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"37",height:"37",viewBox:"0 0 37 37",fill:"none",className:(0,n.ZP)("fill-[#E04242]",s),children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M28.1625 18.712L20.0806 13.7707C19.5225 13.4222 18.8592 13.2824 18.208 13.376C17.5568 13.4696 16.9597 13.7907 16.5224 14.2823L14.976 15.9878C14.8404 16.1373 14.6701 16.2511 14.4802 16.3191C14.2902 16.3872 14.0864 16.4074 13.8868 16.378C13.6872 16.3486 13.4978 16.2704 13.3356 16.1505C13.1733 16.0306 13.0431 15.8725 12.9564 15.6903C12.9233 15.6204 12.8903 15.5462 12.8574 15.4677L15.9665 11.7763C16.1683 11.5269 16.425 11.3276 16.7165 11.1939C17.0081 11.0601 17.3266 10.9956 17.6473 11.0053L25.3485 11.087L29.3086 17.9455L28.1628 18.712H28.1625ZM28.5711 21.6074C28.7221 21.3579 28.7686 21.0589 28.7002 20.7754C28.6318 20.4919 28.4542 20.2469 28.2061 20.0937L19.4775 14.757C19.1484 14.5515 18.7572 14.469 18.3731 14.5242C17.9891 14.5794 17.6369 14.7687 17.3791 15.0587L15.8326 16.7647C15.5694 17.0549 15.2389 17.2758 14.8701 17.4079C14.5013 17.54 14.1056 17.5792 13.718 17.5221C13.3305 17.4649 12.963 17.3132 12.648 17.0802C12.333 16.8473 12.0803 16.5404 11.9121 16.1866C11.8513 16.0583 11.7911 15.9192 11.7333 15.7723C11.6698 15.615 11.6507 15.4432 11.6781 15.2758C11.7055 15.1084 11.7784 14.9517 11.8888 14.8229L13.4522 12.9666L11.2064 11.8475L7.39269 18.4526L8.33308 18.9592L8.53065 18.7614C8.81909 18.4726 9.18046 18.2675 9.57628 18.1679C9.97209 18.0683 10.3875 18.0779 10.7783 18.1958C11.169 18.3137 11.5205 18.5353 11.7952 18.8372C12.0699 19.1391 12.2576 19.5098 12.3383 19.9099C12.7562 19.7218 13.2214 19.6651 13.6723 19.7474C14.1232 19.8297 14.5384 20.0471 14.8629 20.3707C15.0729 20.58 15.2392 20.8289 15.3524 21.1029C15.4655 21.377 15.5232 21.6707 15.5221 21.9672C15.522 22.0931 15.5114 22.2188 15.4905 22.343C15.8096 22.3574 16.122 22.4395 16.407 22.5838C16.6919 22.7281 16.943 22.9313 17.1435 23.18C17.344 23.4287 17.4893 23.7171 17.5698 24.0262C17.6504 24.3353 17.6643 24.658 17.6107 24.9729C17.6652 24.9756 17.7197 24.9807 17.7741 24.9874C17.7852 24.8876 17.822 24.7925 17.881 24.7114C17.94 24.6302 18.0192 24.5658 18.1106 24.5245C18.2021 24.4831 18.3027 24.4663 18.4026 24.4757C18.5025 24.4851 18.5983 24.5203 18.6804 24.5779L20.7682 26.0403L21.3722 26.4074C21.5291 26.5059 21.7102 26.5592 21.8955 26.5613C22.0808 26.5635 22.2631 26.5144 22.4223 26.4196C22.822 26.2021 23.108 25.7736 23.1179 25.3775C23.1265 25.0366 22.9359 24.7441 22.5513 24.5093C22.5513 24.5093 22.5513 24.5093 22.5506 24.5083L19.4643 22.6214C19.3992 22.582 19.3425 22.5301 19.2974 22.4687C19.2524 22.4073 19.22 22.3376 19.202 22.2636C19.184 22.1896 19.1808 22.1128 19.1925 22.0376C19.2043 21.9624 19.2308 21.8902 19.2705 21.8252C19.3102 21.7603 19.3624 21.7038 19.424 21.6591C19.4856 21.6143 19.5554 21.5822 19.6295 21.5645C19.7035 21.5468 19.7803 21.544 19.8555 21.5561C19.9307 21.5681 20.0027 21.595 20.0675 21.635L24.2558 24.1961C24.505 24.3485 24.8044 24.3957 25.0884 24.3272C25.3723 24.2588 25.6174 24.0804 25.7698 23.8312C25.9221 23.582 25.9693 23.2826 25.9008 22.9986C25.8324 22.7147 25.654 22.4696 25.4048 22.3172L20.7769 19.4877C20.6476 19.4071 20.5555 19.2786 20.5204 19.1302C20.4854 18.9819 20.5103 18.8258 20.5898 18.6957C20.6693 18.5657 20.7969 18.4723 20.9449 18.4359C21.0929 18.3995 21.2493 18.423 21.3801 18.5013L26.0068 21.3298L26.0076 21.3305C26.0082 21.3305 26.0087 21.3315 26.009 21.3315L27.0573 21.9725C27.3068 22.1236 27.6058 22.17 27.8894 22.1016C28.1729 22.0332 28.4179 21.8556 28.5711 21.6074ZM20.2492 28.3281C20.1313 28.5205 19.9572 28.6721 19.7505 28.7623C19.5437 28.8526 19.3141 28.8771 19.0929 28.8326L19.1022 28.8231C19.3122 28.6138 19.4786 28.3649 19.5917 28.0909C19.7049 27.8169 19.7625 27.5231 19.7614 27.2267C19.7614 27.0507 19.741 26.8754 19.7008 26.7041L20.082 26.9709C20.2565 27.1464 20.3677 27.3751 20.398 27.6208C20.4283 27.8666 20.3759 28.1154 20.2492 28.3281ZM16.0749 29.7569C15.8571 29.7577 15.644 29.6936 15.4627 29.573C15.2814 29.4524 15.14 29.2806 15.0566 29.0794C14.9731 28.8783 14.9513 28.6569 14.994 28.4433C15.0366 28.2297 15.1418 28.0337 15.2962 27.88L15.2968 27.879L16.7271 26.4492C16.934 26.2449 17.2134 26.1308 17.5041 26.1317C17.7949 26.1327 18.0735 26.2486 18.2791 26.4542C18.4847 26.6598 18.6007 26.9384 18.6016 27.2291C18.6026 27.5199 18.4885 27.7993 18.2842 28.0062L16.8533 29.4374C16.7512 29.5396 16.6297 29.6205 16.4961 29.6754C16.3624 29.7303 16.2192 29.7582 16.0747 29.7574L16.0749 29.7569ZM12.6664 27.3166C12.4603 27.11 12.3444 26.8301 12.3444 26.5383C12.3443 26.2465 12.4599 25.9665 12.666 25.7598L14.607 23.819H14.6075C14.7097 23.7167 14.831 23.6356 14.9645 23.5803C15.0981 23.5249 15.2412 23.4964 15.3858 23.4964C15.5303 23.4964 15.6735 23.5248 15.807 23.5801C15.9406 23.6354 16.062 23.7164 16.1642 23.8186C16.2665 23.9208 16.3476 24.0422 16.4029 24.1757C16.4583 24.3092 16.4868 24.4524 16.4868 24.5969C16.4868 24.7415 16.4584 24.8846 16.4031 25.0182C16.3478 25.1518 16.2668 25.2731 16.1646 25.3754L15.9096 25.6305L14.2237 27.3166C14.0171 27.5229 13.737 27.6388 13.445 27.6388C13.1531 27.6388 12.873 27.5229 12.6664 27.3166ZM10.0365 25.1969C9.83033 24.9904 9.71452 24.7104 9.71452 24.4186C9.71452 24.1267 9.83033 23.8468 10.0365 23.6402L10.7939 22.8821C10.7966 22.8801 10.7994 22.8771 10.802 22.8749C10.8034 22.8735 10.8044 22.8718 10.8058 22.8705L11.7183 21.9577C11.7197 21.9563 11.7214 21.9557 11.7227 21.9543C11.7254 21.9513 11.728 21.9482 11.7308 21.9455L12.4878 21.1882C12.6947 20.9834 12.9743 20.8689 13.2654 20.8697C13.5564 20.8705 13.8353 20.9865 14.0411 21.1923C14.2469 21.3982 14.3629 21.6771 14.3636 21.9682C14.3643 22.2593 14.2497 22.5388 14.045 22.7456L11.5936 25.1968C11.387 25.4029 11.107 25.5187 10.8151 25.5187C10.5232 25.5187 10.2432 25.4029 10.0365 25.1968V25.1969ZM8.42724 20.4997L9.34791 19.5791C9.55381 19.3721 9.8335 19.2554 10.1255 19.2546C10.4174 19.2538 10.6977 19.3691 10.9047 19.575C11.1117 19.7809 11.2284 20.0606 11.2292 20.3525C11.23 20.6445 11.1147 20.9248 10.9088 21.1318L9.97987 22.0617C9.7728 22.2676 9.49243 22.3828 9.20043 22.3819C8.90844 22.3811 8.62874 22.2643 8.42287 22.0572C8.217 21.8501 8.10182 21.5697 8.10266 21.2778C8.10351 20.9858 8.22032 20.7061 8.42739 20.5002L8.42724 20.4997ZM31.5329 6.37782C31.4562 6.24504 31.33 6.14814 31.1819 6.10843C31.0338 6.06872 30.876 6.08946 30.7432 6.16609L25.067 9.4434C24.9818 9.49223 24.9105 9.56211 24.8601 9.64635C24.8096 9.7306 24.7816 9.82639 24.7788 9.92455L17.6595 9.84925C17.1682 9.83704 16.6806 9.93723 16.2338 10.1422C15.7871 10.3471 15.3931 10.6514 15.0818 11.0317L14.2175 12.0579C14.2103 12.0538 14.2027 12.0491 14.1948 12.0453L11.7854 10.8445L12.145 10.2216C12.2216 10.0888 12.2423 9.931 12.2026 9.78293C12.1629 9.63486 12.066 9.50863 11.9333 9.43199L6.25703 6.15452C6.19126 6.11657 6.11865 6.09195 6.04335 6.08207C5.96806 6.07218 5.89156 6.07723 5.81821 6.09692C5.74487 6.11661 5.67613 6.15056 5.6159 6.19682C5.55568 6.24308 5.50517 6.30076 5.46724 6.36655L0.0775274 15.7017C0.000998188 15.8344 -0.0197106 15.9921 0.0199506 16.1401C0.0596118 16.2881 0.156399 16.4143 0.289049 16.491L5.96522 19.7683C6.03098 19.8062 6.10357 19.8309 6.17885 19.8408C6.25413 19.8507 6.33062 19.8457 6.40396 19.826C6.4773 19.8064 6.54605 19.7725 6.60629 19.7262C6.66652 19.68 6.71706 19.6224 6.75502 19.5566L6.81384 19.4545L7.48634 19.8168C7.11961 20.2491 6.92869 20.8035 6.95145 21.3699C6.9742 21.9363 7.20897 22.4736 7.6092 22.8751C7.92237 23.1896 8.32218 23.4036 8.75757 23.4899C8.60201 23.8335 8.53516 24.2107 8.56315 24.5869C8.59113 24.963 8.71306 25.3261 8.91776 25.643C9.12246 25.9598 9.40339 26.2202 9.73481 26.4003C10.0662 26.5804 10.4376 26.6745 10.8148 26.6739C10.9407 26.6735 11.0663 26.663 11.1905 26.6424C11.2051 26.9615 11.2873 27.2738 11.4317 27.5587C11.5762 27.8436 11.7795 28.0946 12.0282 28.2949C12.2769 28.4953 12.5654 28.6406 12.8745 28.721C13.1836 28.8015 13.5063 28.8153 13.8211 28.7617C13.8418 29.1984 13.9889 29.6197 14.2444 29.9745C14.4999 30.3293 14.853 30.6023 15.2606 30.7603C15.6683 30.9183 16.1131 30.9546 16.541 30.8647C16.9689 30.7748 17.3615 30.5626 17.6711 30.254L18.2043 29.7206C18.5402 29.911 18.9198 30.0112 19.306 30.0114C19.6931 30.0118 20.0739 29.9127 20.4118 29.7236C20.7496 29.5346 21.0333 29.2619 21.2355 28.9317C21.4628 28.5588 21.5759 28.1274 21.5607 27.6908C22.0473 27.7667 22.5454 27.6769 22.9749 27.4357C23.7321 27.0231 24.2371 26.244 24.2714 25.4426C24.6074 25.5288 24.9587 25.5364 25.298 25.4647C25.6374 25.393 25.9556 25.244 26.228 25.0292C26.5003 24.8144 26.7194 24.5396 26.8682 24.2263C27.017 23.913 27.0915 23.5696 27.0859 23.2228C27.262 23.2667 27.4428 23.2893 27.6244 23.29C27.805 23.2897 27.985 23.2681 28.1605 23.2255C28.5531 23.1303 28.913 22.9315 29.2026 22.6498C29.4922 22.3681 29.701 22.0139 29.8071 21.6241C29.9132 21.2343 29.9128 20.8231 29.8059 20.4335C29.6991 20.0439 29.4896 19.6901 29.1995 19.409L29.8878 18.9489L30.2451 19.5678C30.3218 19.7006 30.4481 19.7974 30.5962 19.8371C30.7443 19.8768 30.9021 19.8561 31.035 19.7796L36.7109 16.5027C36.7767 16.4648 36.8343 16.4142 36.8805 16.354C36.9267 16.2937 36.9606 16.2249 36.9802 16.1516C36.9998 16.0782 37.0048 16.0018 36.9949 15.9265C36.985 15.8512 36.9603 15.7786 36.9223 15.7129L31.5329 6.37782Z"})})}},25429:function(e,s,t){"use strict";t.d(s,{X:function(){return a}});var r=t(57437),n=t(94508);function a(e){let{className:s}=e;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,n.ZP)("fill-primary",s),children:[(0,r.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},37128:function(e,s,t){"use strict";t.d(s,{Z:function(){return a}});var r=t(57437),n=t(94508);function a(e){let{className:s}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"34",height:"34",viewBox:"0 0 34 34",fill:"none",className:(0,n.ZP)("fill-[#09A7FF]",s),children:(0,r.jsx)("path",{d:"M26.8317 25.452H13.1851L12.3417 22.0782H30.1758L34 6.78136H8.51752L7.17339 1.40479H0V3.40753H5.60966L11.1408 25.5319C9.53061 25.8777 8.31969 27.3116 8.31969 29.0236C8.31969 30.9929 9.92187 32.5951 11.8913 32.5951C13.8607 32.5951 15.4629 30.9928 15.4629 29.0236C15.4629 28.461 15.3317 27.9286 15.099 27.4547H23.624C23.3912 27.9286 23.2601 28.461 23.2601 29.0236C23.2601 30.9929 24.8623 32.5951 26.8317 32.5951C28.801 32.5951 30.4032 30.9928 30.4032 29.0236C30.4032 27.0542 28.8009 25.452 26.8317 25.452Z"})})}},6596:function(e,s,t){"use strict";t.d(s,{Qd:function(){return d},UQ:function(){return o},o4:function(){return c},vF:function(){return u}});var r=t(57437),n=t(13134),a=t(2265),l=t(94508),i=t(36887);let o=n.fC,d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.ck,{ref:s,className:(0,l.ZP)("border-b",t),...a})});d.displayName="AccordionItem";let c=a.forwardRef((e,s)=>{let{className:t,children:a,...o}=e;return(0,r.jsx)(n.h4,{className:"flex",children:(0,r.jsxs)(n.xz,{ref:s,className:(0,l.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",t),...o,children:[a,(0,r.jsx)(i.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=n.xz.displayName;let u=a.forwardRef((e,s)=>{let{className:t,children:a,...i}=e;return(0,r.jsx)(n.VY,{ref:s,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...i,children:(0,r.jsx)("div",{className:(0,l.ZP)("pb-4 pt-0",t),children:a})})});u.displayName=n.VY.displayName},65613:function(e,s,t){"use strict";t.d(s,{Cd:function(){return d},X:function(){return c},bZ:function(){return o}});var r=t(57437),n=t(90535),a=t(2265),l=t(94508);let i=(0,n.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef((e,s)=>{let{className:t,variant:n,...a}=e;return(0,r.jsx)("div",{ref:s,role:"alert",className:(0,l.ZP)(i({variant:n}),t),...a})});o.displayName="Alert";let d=a.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("h5",{ref:s,className:(0,l.ZP)("mb-1 font-medium leading-none tracking-tight",t),...n})});d.displayName="AlertTitle";let c=a.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.ZP)("text-sm [&_p]:leading-relaxed",t),...n})});c.displayName="AlertDescription"},84190:function(e,s,t){"use strict";t.d(s,{$F:function(){return u},AW:function(){return x},VD:function(){return p},Xi:function(){return m},h_:function(){return c}});var r=t(57437),n=t(70085),a=t(10407),l=t(30401),i=t(40519),o=t(2265),d=t(94508);let c=n.fC,u=n.xz;n.ZA,n.Uv,n.Tr,n.Ee,o.forwardRef((e,s)=>{let{className:t,inset:l,children:i,...o}=e;return(0,r.jsxs)(n.fF,{ref:s,className:(0,d.ZP)("items-left my-1 flex h-8 cursor-pointer select-none rounded-sm px-3 py-1 text-sm font-bold text-secondary-800 outline-none hover:bg-secondary focus:bg-accent data-[state=open]:bg-accent",l&&"pl-8",t),...o,children:[i,(0,r.jsx)(a.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=n.fF.displayName,o.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.tu,{ref:s,className:(0,d.ZP)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...a})}).displayName=n.tu.displayName;let x=o.forwardRef((e,s)=>{let{className:t,sideOffset:a=4,...l}=e;return(0,r.jsx)(n.Uv,{children:(0,r.jsx)(n.VY,{ref:s,sideOffset:a,className:(0,d.ZP)("z-50 mx-5 min-w-[253px] overflow-hidden rounded-md border bg-white p-1 text-secondary-800 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})})});x.displayName=n.VY.displayName;let m=o.forwardRef((e,s)=>{let{className:t,onClick:a,inset:l,...i}=e;return(0,r.jsx)(n.ck,{ref:s,className:(0,d.ZP)("relative my-1 flex h-8 cursor-pointer select-none items-center rounded px-3 py-1 text-sm font-medium text-secondary-800 outline-none transition-colors hover:bg-secondary focus:bg-secondary focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",t),...i})});m.displayName=n.ck.displayName,o.forwardRef((e,s)=>{let{className:t,onClick:a,inset:l,...i}=e;return(0,r.jsx)(n.ck,{ref:s,onClick:e=>{e.preventDefault(),a&&a(e)},className:(0,d.ZP)("focus:secondary relative my-1 flex h-8 cursor-pointer select-none items-center rounded px-3 py-1 text-sm outline-none transition-colors hover:bg-secondary focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",t),...i})}).displayName=n.ck.displayName,o.forwardRef((e,s)=>{let{className:t,children:a,checked:i,...o}=e;return(0,r.jsxs)(n.oC,{ref:s,className:(0,d.ZP)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:i,...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)(l.Z,{className:"h-4 w-4"})})}),a]})}).displayName=n.oC.displayName,o.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsxs)(n.Rk,{ref:s,className:(0,d.ZP)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)(i.Z,{className:"h-2 w-2 fill-current"})})}),a]})}).displayName=n.Rk.displayName,o.forwardRef((e,s)=>{let{className:t,inset:a,...l}=e;return(0,r.jsx)(n.__,{ref:s,className:(0,d.ZP)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",t),...l})}).displayName=n.__.displayName;let p=o.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.Z0,{ref:s,className:(0,d.ZP)("-mx-1 my-1 h-px bg-muted",t),...a})});p.displayName=n.Z0.displayName},81123:function(e,s,t){"use strict";t.d(s,{H:function(){return a}});var r=t(79981),n=t(97751);async function a(e,s){try{let t=await r.Z.put("/admin/customers/update-address/".concat(s),{addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city});return(0,n.B)(t)}catch(e){return(0,n.D)(e)}}},17110:function(e,s,t){"use strict";t.d(s,{n:function(){return l}});var r=t(79981),n=t(97751),a=t(2901);async function l(e,s){try{var t;let l=new FormData;l.append("firstName",e.firstName),l.append("lastName",e.lastName),l.append("email",e.email),l.append("phone",e.phone),l.append("gender",e.gender),l.append("dob",(0,a.WU)(e.dateOfBirth,"yyyy-MM-dd")),l.append("profileImage",null!==(t=e.profile)&&void 0!==t?t:"");let i=await r.Z.put("/admin/customers/update/".concat(s),l,{headers:{"Content-Type":"multipart/form-data"}});return(0,n.B)(i)}catch(e){return(0,n.D)(e)}}},4995:function(e,s,t){"use strict";t.d(s,{y:function(){return a}});var r=t(79981),n=t(97751);async function a(e,s){try{let t=await r.Z.post("/admin/users/".concat(s,"-balance"),e);return(0,n.B)(t)}catch(e){return(0,n.D)(e)}}},7211:function(e,s,t){"use strict";t.d(s,{I:function(){return a}});var r=t(79981),n=t(97751);async function a(e,s){try{let t=await r.Z.put("/admin/wallets/transfer-limit/".concat(s),e);return(0,n.B)(t)}catch(e){return(0,n.D)(e)}}},97751:function(e,s,t){"use strict";t.d(s,{B:function(){return n},D:function(){return a}});var r=t(43577);function n(e){var s,t,r;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(r=null===(s=e.data)||void 0===s?void 0:s.message)&&void 0!==r?r:"",data:null===(t=e.data)||void 0===t?void 0:t.data}}function a(e){let s=500,t="Internal Server Error",n="An unknown error occurred";if((0,r.IZ)(e)){var a,l,i,o,d,c,u,x,m,p,f,h;s=null!==(m=null===(a=e.response)||void 0===a?void 0:a.status)&&void 0!==m?m:500,t=null!==(p=null===(l=e.response)||void 0===l?void 0:l.statusText)&&void 0!==p?p:"Internal Server Error",n=null!==(h=null!==(f=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(i=o[0])||void 0===i?void 0:i.message)&&void 0!==f?f:null===(x=e.response)||void 0===x?void 0:null===(u=x.data)||void 0===u?void 0:u.message)&&void 0!==h?h:e.message}else e instanceof Error&&(n=e.message);return{statusCode:s,statusText:t,status:!1,message:n,data:void 0,error:e}}},54995:function(e,s,t){"use strict";t.d(s,{K:function(){return l},S:function(){return i}});var r=t(31229);let n=["image/jpeg","image/jpg","image/png","image/svg+xml"],a=["image/x-icon","image/vnd.microsoft.icon","image/png"],l=r.z.union([r.z.string(),r.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&n.includes(e.type),"Invalid file format. Please upload a .jpg, .jpeg, .png or .svg file."),i=r.z.union([r.z.string(),r.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&a.includes(e.type),"Invalid file format. Please upload a .ico or .png file.")}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,48248,31384,60627,85598,21564,31284,86901,227,12042,92971,95030,1744],function(){return e(e.s=93770)}),_N_E=e.O()}]);