(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[87631],{20882:function(e,a,s){Promise.resolve().then(s.bind(s,89054))},89054:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return h}});var t=s(57437),n=s(49657),r=s(85539),l=s(27186),c=s(85017),u=s(6512),i=s(75730),d=s(94508),o=s(99376),f=s(2265),m=s(43949);function h(){var e;let{t:a}=(0,m.$G)(),s=(0,o.useSearchParams)(),[h,x]=f.useState(null!==(e=s.get("search"))&&void 0!==e?e:""),p=(0,o.useRouter)(),v=(0,o.usePathname)(),{data:g,isLoading:j,meta:w,refresh:N}=(0,i.Z)("/admin/transfers?".concat(s.toString(),"&status=pending"));return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(r.R,{value:h,onChange:e=>{e.preventDefault();let a=(0,d.w4)(e.target.value);x(e.target.value),p.replace("".concat(v,"?").concat(a.toString()))},iconPlacement:"end",placeholder:a("Search..."),containerClass:"w-full sm:w-auto"}),(0,t.jsx)(c.k,{canFilterByStatus:!1}),(0,t.jsx)(l._,{url:"/admin/transfers/export/all"})]}),(0,t.jsx)("div",{})]}),(0,t.jsx)(u.Z,{className:"my-4"}),(0,t.jsx)(n.Z,{data:g,meta:w,isLoading:j,refresh:N})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,227,56993,85017,58804,92971,95030,1744],function(){return e(e.s=20882)}),_N_E=e.O()}]);