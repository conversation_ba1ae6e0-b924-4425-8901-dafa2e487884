"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[19813],{41709:function(e,t,r){function n(e){let{condition:t,children:r}=e;return t?r:null}r.d(t,{J:function(){return n}}),r(2265)},85487:function(e,t,r){r.d(t,{Loader:function(){return i}});var n=r(57437),s=r(94508),a=r(43949);function i(e){let{title:t="Loading...",className:r}=e,{t:i}=(0,a.$G)();return(0,n.jsxs)("div",{className:(0,s.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:i(t)})]})}},78939:function(e,t,r){r.d(t,{S:function(){return l}});var n=r(57437),s=r(94508),a=r(33145),i=r(2265),o=r(85598);function l(e){let{defaultValue:t,onChange:r,className:l,children:d,disabled:c=!1,id:u}=e,[m,f]=i.useState(t);i.useEffect(()=>{f(t)},[t]);let{getRootProps:x,getInputProps:p}=(0,o.uI)({onDrop:e=>{let t=null==e?void 0:e[0];t&&(r(t),f(URL.createObjectURL(t)))},disabled:c});return(0,n.jsxs)("div",{...x({className:(0,s.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",l)}),children:[!!m&&(0,n.jsx)(a.default,{src:m,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,n.jsx)("input",{id:u,...p()}),!m&&(0,n.jsx)("div",{children:d})]})}},25429:function(e,t,r){r.d(t,{X:function(){return a}});var n=r(57437),s=r(94508);function a(e){let{className:t}=e;return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,s.ZP)("fill-primary",t),children:[(0,n.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},70093:function(e,t,r){r.d(t,{u:function(){return z}});var n=r(57437),s=r(41709),a=r(25429),i=r(6596),o=r(62869),l=r(15681),d=r(26815),c=r(6512),u=r(13590),m=r(2265),f=r(29501),x=r(78939),p=r(85487),h=r(53647),v=r(79981),g=r(43577);async function b(e){var t,r,n,s,a,i,o,l,d;try{let n=new FormData;n.append("documentType",e.documentType),n.append("front",e.documentFrontSide),n.append("back",e.documentBackSide),n.append("selfie",e.selfie);let s=await v.Z.post("/kycs/submit",n,{headers:{"Content-Type":"multipart/form-data"}});return{statusCode:s.status,statusText:s.statusText,status:200===s.status||201===s.status,message:null!==(r=null===(t=s.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(c){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,g.IZ)(c)?(e=null!==(o=null===(n=c.response)||void 0===n?void 0:n.status)&&void 0!==o?o:500,t=null!==(l=null===(s=c.response)||void 0===s?void 0:s.statusText)&&void 0!==l?l:"Internal Server Error",r=null!==(d=null===(i=c.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.message)&&void 0!==d?d:c.message):c instanceof Error&&(r=c.message),{statusCode:e,statusText:t,status:!1,message:r,error:c}}}var j=r(94508),y=r(31229);let N=["image/png","image/jpeg","image/jpg","image/webp"],w=y.z.any().optional().refine(e=>!e||e.size<=3145728,"File size must be less than 3MB").refine(e=>!e||N.includes(e.type),"File must be a PNG,JPEG,JPG,WEBP"),C=y.z.object({documentType:y.z.string({required_error:"Document type is required."}),documentFrontSide:w,documentBackSide:w,selfie:w});var Z=r(75448),S=r(22291),P=r(43949),F=r(14438),k=r(55988);let R=["NID","Passport","Driving"];function z(e){var t;let{fetchData:r,isLoading:v,refresh:y}=e,[N,w]=(0,m.useTransition)(),{t:z}=(0,P.$G)(),I=(0,f.cI)({mode:"all",resolver:(0,u.F)(C),defaultValues:{documentType:null!==(t=null==r?void 0:r.documentType)&&void 0!==t?t:"",documentFrontSide:"",documentBackSide:"",selfie:""}});return(0,m.useEffect)(()=>{(null==r?void 0:r.documentType)&&I.setValue("documentType",r.documentType)},[r]),(0,n.jsx)(l.l0,{...I,children:(0,n.jsx)("form",{onSubmit:I.handleSubmit(e=>{w(async()=>{let t=await b(e);if(t&&t.status)y(),F.toast.success(t.message);else if(422===t.statusCode){var r,n,s,a,i,o,l,d,c;if((0,g.IZ)(t.error)&&(null===(s=t.error)||void 0===s?void 0:null===(n=s.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.messages)&&Array.isArray(null===(o=t.error)||void 0===o?void 0:null===(i=o.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.messages)){let e=null===(c=t.error)||void 0===c?void 0:null===(d=c.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.messages;null==e||e.forEach(e=>{(0,k.EQ)(e).with({field:"front"},()=>I.setError("documentFrontSide",{message:z(null==e?void 0:e.message),type:"custom"})).with({field:"back"},()=>I.setError("documentBackSide",{message:z(null==e?void 0:e.message),type:"custom"})).with({field:"selfie"},()=>I.setError("selfie",{message:z(null==e?void 0:e.message),type:"custom"})).otherwise(()=>null)}),F.toast.error(z("Please provide all required field."))}}else F.toast.error(z(t.message))})}),"data-loading":v,className:"rounded-xl border border-border bg-background data-[loading=true]:pointer-events-none data-[loading=true]:opacity-50",children:(0,n.jsxs)(i.Qd,{value:"DOCUMENT_INFORMATION",className:"border-none px-4 py-0",children:[(0,n.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,n.jsxs)("div",{className:"flex items-center gap-10 text-base font-medium leading-[22px]",children:[z("Documents"),v&&(0,n.jsx)(p.Loader,{title:z("Loading...")})]})}),(0,n.jsxs)(i.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[(0,n.jsx)(l.Wi,{control:I.control,name:"documentType",render:e=>{let{field:t}=e;return(0,n.jsxs)(l.xJ,{children:[(0,n.jsx)(l.lX,{children:z("Document Type")}),(0,n.jsx)(l.NI,{children:(0,n.jsxs)(h.Ph,{disabled:(null==r?void 0:r.status)==="verified",value:t.value,onValueChange:t.onChange,children:[(0,n.jsx)(h.i4,{className:"data-[placeholder]:text-placeholder w-full max-w-[337px] bg-accent disabled:opacity-100 data-[placeholder]:text-base [&>svg>path]:stroke-primary [&>svg]:size-6 [&>svg]:opacity-100",children:(0,n.jsx)(h.ki,{placeholder:z("Select document type")})}),(0,n.jsx)(h.Bw,{align:"start",children:R.map(e=>(0,n.jsx)(h.Ql,{value:e.toLowerCase(),children:e},e))})]})}),(0,n.jsx)(l.zG,{})]})}}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h5",{className:"mb-4 font-medium",children:[" ",z("Attach pictures")," "]}),(0,n.jsxs)("div",{className:"flex w-full flex-wrap items-center gap-2.5 gap-y-4",children:[(0,n.jsx)(l.Wi,{control:I.control,name:"documentFrontSide",render:e=>{let{field:t}=e;return(0,n.jsxs)(l.xJ,{className:"w-full md:w-auto",children:[(0,n.jsx)(l.lX,{className:"text-base font-medium text-secondary-text",children:z("Front Side")}),(0,n.jsx)(l.NI,{children:(0,n.jsxs)("div",{className:"flex w-full flex-col gap-2",children:[(0,n.jsx)(x.S,{id:"documentFrontSideFile",disabled:(null==r?void 0:r.status)==="verified",defaultValue:(0,j.qR)(null==r?void 0:r.front),onChange:e=>t.onChange(e),className:"flex h-[270px] w-full items-center justify-center border-dashed border-primary bg-transparent sm:w-[400px]",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,n.jsx)(a.X,{}),(0,n.jsxs)("p",{className:"text-sm font-normal text-primary",children:[z("Drag and drop file here or upload")," "]})]})}),(null==r?void 0:r.status)==="verified"?null:(0,n.jsx)(o.z,{asChild:!0,type:"button",className:"h-8 px-[12px] py-[5px] hover:cursor-pointer disabled:pointer-events-none",children:(0,n.jsxs)(d.Z,{htmlFor:"documentFrontSideFile",children:[(0,n.jsx)(Z.Z,{size:20}),(0,n.jsx)("span",{className:"text-sm font-semibold",children:z("Upload")})]})})]})}),(0,n.jsx)(l.zG,{})]})}}),(0,n.jsx)(s.J,{condition:"passport"!==I.watch("documentType"),children:(0,n.jsx)(l.Wi,{control:I.control,name:"documentBackSide",render:e=>{let{field:t}=e;return(0,n.jsxs)(l.xJ,{className:"w-full md:w-auto",children:[(0,n.jsx)(l.lX,{className:"text-base font-medium text-secondary-text",children:z("Back Side")}),(0,n.jsx)(l.NI,{children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)(x.S,{id:"documentBackSideFile",disabled:(null==r?void 0:r.status)==="verified",defaultValue:(0,j.qR)(null==r?void 0:r.back),onChange:e=>t.onChange(e),className:"flex h-[270px] w-full items-center justify-center border-dashed border-primary bg-transparent sm:w-[400px]",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,n.jsx)(a.X,{}),(0,n.jsxs)("p",{className:"text-sm font-normal text-primary",children:[z("Drag and drop file here or upload")," "]})]})}),(null==r?void 0:r.status)==="verified"?null:(0,n.jsx)(o.z,{asChild:!0,type:"button",className:"h-8 px-[12px] py-[5px] hover:cursor-pointer",children:(0,n.jsxs)(d.Z,{htmlFor:"documentBackSideFile",children:[(0,n.jsx)(Z.Z,{size:20}),(0,n.jsx)("span",{className:"text-sm font-semibold",children:z("Upload")})]})})]})}),(0,n.jsx)(l.zG,{})]})}})})]})]}),(0,n.jsx)(c.Z,{className:"border-b border-dashed bg-transparent"}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h5",{className:"mb-4 font-medium",children:[" ",z("Attach selfie")," "]}),(0,n.jsx)(l.Wi,{control:I.control,name:"selfie",render:e=>{let{field:t}=e;return(0,n.jsxs)(l.xJ,{className:"w-full md:w-auto",children:[(0,n.jsx)(l.lX,{className:"text-base font-medium text-secondary-text",children:z("Selfie")}),(0,n.jsx)(l.NI,{children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)(x.S,{disabled:(null==r?void 0:r.status)==="verified",defaultValue:(0,j.qR)(null==r?void 0:r.selfie),id:"selfieFile",onChange:e=>t.onChange(e),className:"flex h-[270px] w-full items-center justify-center border-dashed border-primary bg-transparent sm:w-[400px]",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,n.jsx)(a.X,{}),(0,n.jsx)("p",{className:"text-sm font-normal text-primary",children:z("Drag and drop file here or upload")})]})}),(null==r?void 0:r.status)==="verified"?null:(0,n.jsx)(o.z,{asChild:!0,type:"button",className:"h-8 max-w-[400px] px-[12px] py-[5px] hover:cursor-pointer",children:(0,n.jsxs)(d.Z,{htmlFor:"selfieFile",children:[(0,n.jsx)(Z.Z,{size:20}),(0,n.jsx)("span",{className:"text-sm font-semibold",children:z("Upload")})]})})]})}),(0,n.jsx)(l.zG,{})]})}})]}),(null==r?void 0:r.status)==="verified"?null:(0,n.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,n.jsxs)(o.z,{disabled:N,children:[(0,n.jsxs)(s.J,{condition:!N,children:[z("Save"),(0,n.jsx)(S.Z,{size:20})]}),(0,n.jsx)(s.J,{condition:N,children:(0,n.jsx)(p.Loader,{title:z("Processing..."),className:"text-primary-foreground"})})]})})]})]})})})}},32211:function(e,t,r){r.d(t,{Z:function(){return f}});var n=r(57437),s=r(41709),a=r(85487),i=r(6596),o=r(65613),l=r(35974),d=r(19049),c=r(15066),u=r(77376),m=r(43949);function f(e){let{fetchData:t,isLoading:r}=e,{t:f}=(0,m.$G)();return(0,n.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,n.jsxs)(i.Qd,{value:"KYC_STATUS",className:"border-none px-4 py-0",children:[(0,n.jsx)(i.o4,{className:"py-6 hover:no-underline",children:(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)("p",{className:"text-base font-medium leading-[22px]",children:f("KYC Status")}),(0,n.jsx)(s.J,{condition:(null==t?void 0:t.status)==="verified",children:(0,n.jsx)(l.C,{className:"h-5 bg-spacial-green text-[10px] text-spacial-green-foreground",children:f("Verified")})}),(0,n.jsx)(s.J,{condition:(null==t?void 0:t.status)==="pending",children:(0,n.jsx)(l.C,{className:"h-5 bg-primary text-[10px] text-primary-foreground",children:f("Pending")})}),(0,n.jsx)(s.J,{condition:!["pending","verified","failed"].includes(null==t?void 0:t.status),children:(0,n.jsx)(l.C,{className:"h-5 bg-foreground text-[10px] text-background",children:f("Awaiting submission")})})]})}),(0,n.jsx)(s.J,{condition:r,children:(0,n.jsx)(i.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:(0,n.jsx)(a.Loader,{})})}),(0,n.jsx)(s.J,{condition:!r,children:(0,n.jsxs)(i.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:[(0,n.jsx)(s.J,{condition:(null==t?void 0:t.status)==="verified",children:(0,n.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-spacial-green",children:[(0,n.jsx)(d.Z,{size:"32",variant:"Bulk"}),(0,n.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:f("Your account is verified")}),(0,n.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:f("Your account has been successfully verified. If you have any questions feel free to reach out to our support team.")})]})}),(0,n.jsx)(s.J,{condition:(null==t?void 0:t.status)==="pending",children:(0,n.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-primary",children:[(0,n.jsx)(c.Z,{size:"32",variant:"Bulk"}),(0,n.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:f("Pending verification")}),(0,n.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:f("Thank you for submitting your documents! Your KYC verification is currently under review. Our team is working to process your submission as quickly as possible.")})]})}),(0,n.jsx)(s.J,{condition:!["pending","verified","failed"].includes(null==t?void 0:t.status),children:(0,n.jsxs)(o.bZ,{className:"border-none bg-transparent shadow-default [&>svg]:text-foreground",children:[(0,n.jsx)(u.Z,{size:"32",variant:"Bulk"}),(0,n.jsx)(o.Cd,{className:"ml-2.5 text-sm font-semibold leading-5",children:f("You have not submitted documents yet.")}),(0,n.jsx)(o.X,{className:"ml-2.5 text-sm font-normal",children:f("Your account is not yet verified. Please complete the KYC process by submitting the required documents.")})]})})]})})]})})}},6596:function(e,t,r){r.d(t,{Qd:function(){return d},UQ:function(){return l},o4:function(){return c},vF:function(){return u}});var n=r(57437),s=r(13134),a=r(2265),i=r(94508),o=r(36887);let l=s.fC,d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.ck,{ref:t,className:(0,i.ZP)("border-b",r),...a})});d.displayName="AccordionItem";let c=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,n.jsx)(s.h4,{className:"flex",children:(0,n.jsxs)(s.xz,{ref:t,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",r),...l,children:[a,(0,n.jsx)(o.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=s.xz.displayName;let u=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,n.jsx)(s.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...o,children:(0,n.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",r),children:a})})});u.displayName=s.VY.displayName},65613:function(e,t,r){r.d(t,{Cd:function(){return d},X:function(){return c},bZ:function(){return l}});var n=r(57437),s=r(90535),a=r(2265),i=r(94508);let o=(0,s.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:s,...a}=e;return(0,n.jsx)("div",{ref:t,role:"alert",className:(0,i.ZP)(o({variant:s}),r),...a})});l.displayName="Alert";let d=a.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h5",{ref:t,className:(0,i.ZP)("mb-1 font-medium leading-none tracking-tight",r),...s})});d.displayName="AlertTitle";let c=a.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.ZP)("text-sm [&_p]:leading-relaxed",r),...s})});c.displayName="AlertDescription"},35974:function(e,t,r){r.d(t,{C:function(){return o}});var n=r(57437),s=r(90535);r(2265);var a=r(94508);let i=(0,s.j)("inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-muted text-secondary-foreground",success:"border-transparent bg-success text-success-foreground",important:"border-transparent bg-important text-important-foreground",error:"border-transparent bg-destructive text-destructive-foreground",warning:"border-transparent bg-warning text-warning-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...s}=e;return(0,n.jsx)("div",{className:(0,a.ZP)(i({variant:r}),t),...s})}},62869:function(e,t,r){r.d(t,{d:function(){return l},z:function(){return d}});var n=r(57437),s=r(37053),a=r(90535),i=r(2265),o=r(94508);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:d=!1,...c}=e,u=d?s.g7:"button";return(0,n.jsx)(u,{className:(0,o.ZP)(l({variant:a,size:i,className:r})),ref:t,...c})});d.displayName="Button"},15681:function(e,t,r){r.d(t,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return p},xJ:function(){return x},zG:function(){return v}});var n=r(57437),s=r(37053),a=r(2265),i=r(29501),o=r(26815),l=r(94508);let d=i.RV,c=a.createContext({}),u=e=>{let{...t}=e;return(0,n.jsx)(c.Provider,{value:{name:t.name},children:(0,n.jsx)(i.Qr,{...t})})},m=()=>{let e=a.useContext(c),t=a.useContext(f),{getFieldState:r,formState:n}=(0,i.Gc)(),s=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...s}},f=a.createContext({}),x=a.forwardRef((e,t)=>{let{className:r,...s}=e,i=a.useId();return(0,n.jsx)(f.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:t,className:(0,l.ZP)("space-y-2",r),...s})})});x.displayName="FormItem";let p=a.forwardRef((e,t)=>{let{className:r,required:s,...a}=e,{error:i,formItemId:d}=m();return(0,n.jsx)("span",{children:(0,n.jsx)(o.Z,{ref:t,className:(0,l.ZP)(i&&"text-base font-medium text-destructive",r),htmlFor:d,...a})})});p.displayName="FormLabel";let h=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:i,formDescriptionId:o,formMessageId:l}=m();return(0,n.jsx)(s.g7,{ref:t,id:i,"aria-describedby":a?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!a,...r})});h.displayName="FormControl",a.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:a}=m();return(0,n.jsx)("p",{ref:t,id:a,className:(0,l.ZP)("text-sm text-muted-foreground",r),...s})}).displayName="FormDescription";let v=a.forwardRef((e,t)=>{let{className:r,children:s,...a}=e,{error:i,formMessageId:o}=m(),d=i?String(null==i?void 0:i.message):s;return d?(0,n.jsx)("p",{ref:t,id:o,className:(0,l.ZP)("text-sm font-medium text-destructive",r),...a,children:d}):null});v.displayName="FormMessage"},26815:function(e,t,r){var n=r(57437),s=r(6394),a=r(90535),i=r(2265),o=r(94508);let l=(0,a.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.f,{ref:t,className:(0,o.ZP)(l(),r),...a})});d.displayName=s.f.displayName,t.Z=d},53647:function(e,t,r){r.d(t,{Bw:function(){return h},Ph:function(){return u},Ql:function(){return v},i4:function(){return f},ki:function(){return m}});var n=r(57437),s=r(68856),a=r(22135),i=r(40875),o=r(2265),l=r(94508),d=r(36887),c=r(22291);let u=s.fC;s.ZA;let m=s.B4,f=o.forwardRef((e,t)=>{let{className:r,children:a,...i}=e;return(0,n.jsxs)(s.xz,{ref:t,className:(0,l.ZP)("data-[placeholder]:text-placeholder flex h-12 w-full items-center justify-between rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[a,(0,n.jsx)(s.JO,{asChild:!0,children:(0,n.jsx)(d.Z,{size:"24",color:"#292D32"})})]})});f.displayName=s.xz.displayName;let x=o.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(s.u_,{ref:t,className:(0,l.ZP)("flex cursor-default items-center justify-center py-1",r),...i,children:(0,n.jsx)(a.Z,{className:"h-4 w-4"})})});x.displayName=s.u_.displayName;let p=o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.$G,{ref:t,className:(0,l.ZP)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,n.jsx)(i.Z,{className:"h-4 w-4"})})});p.displayName=s.$G.displayName;let h=o.forwardRef((e,t)=>{let{className:r,children:a,position:i="popper",...o}=e;return(0,n.jsx)(s.h_,{children:(0,n.jsxs)(s.VY,{ref:t,className:(0,l.ZP)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:i,...o,children:[(0,n.jsx)(x,{}),(0,n.jsx)(s.l_,{className:(0,l.ZP)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,n.jsx)(p,{})]})})});h.displayName=s.VY.displayName,o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.__,{ref:t,className:(0,l.ZP)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=s.__.displayName;let v=o.forwardRef((e,t)=>{let{className:r,children:a,...i}=e;return(0,n.jsxs)(s.ck,{ref:t,className:(0,l.ZP)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-input focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...i,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(s.wU,{children:(0,n.jsx)(c.Z,{variant:"Bold",className:"h-4 w-4"})})}),(0,n.jsx)(s.eT,{children:a})]})});v.displayName=s.ck.displayName,o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.Z0,{ref:t,className:(0,l.ZP)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=s.Z0.displayName},6512:function(e,t,r){var n=r(57437),s=r(55156),a=r(2265),i=r(94508);let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:o=!0,...l}=e;return(0,n.jsx)(s.f,{ref:t,decorative:o,orientation:a,className:(0,i.ZP)("shrink-0 bg-divider","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...l})});o.displayName=s.f.displayName,t.Z=o},79981:function(e,t,r){var n=r(78040),s=r(83464);t.Z=s.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){r.d(t,{rH:function(){return n},sp:function(){return s}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},s=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){r.d(t,{F:function(){return c},Fg:function(){return f},Fp:function(){return d},Qp:function(){return m},ZP:function(){return o},fl:function(){return l},qR:function(){return u},w4:function(){return x}});var n=r(78040),s=r(61994),a=r(14438),i=r(53335);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,s.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let d=e=>{e&&navigator.clipboard.writeText(e).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class c{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let s;let a=void 0===t?this.currencyCode:t;try{s=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=s.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:a,o=s.format(e),l=o.substring(i.length).trim();return{currencyCode:a,currencySymbol:i,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let u=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",x=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",s=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?s.set(n,e):s.delete(n),s}}}]);