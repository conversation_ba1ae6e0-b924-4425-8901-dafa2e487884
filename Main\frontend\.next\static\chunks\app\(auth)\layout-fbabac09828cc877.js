(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[56695],{80531:function(e,t,n){Promise.resolve().then(n.bind(n,65198))},65198:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return d}});var u=n(57437),s=n(80114),r=n(3612),a=n(99376),c=n(2265),i=n(13710),l=n(21251),o=n(94508);function f(){let{authBanner:e}=(0,l.T)();return(0,u.jsx)("div",{style:{backgroundImage:"url(".concat((0,o.qR)(e),")")},className:"hidden h-full w-full border-r bg-cover bg-no-repeat md:block md:max-w-[350px] lg:max-w-[510px]"})}function d(e){let{children:t}=e,{isAuthenticate:n,isLoading:l}=(0,r.a)(),o=(0,a.useRouter)(),[d,h]=c.useState(!1);return(c.useLayoutEffect(()=>{!l&&n&&o.push("/")},[l]),c.useLayoutEffect(()=>{l||n||h(!0)},[l,n]),d)?(0,u.jsxs)("div",{className:"flex h-screen",children:[(0,u.jsx)(f,{}),(0,u.jsxs)("div",{className:"flex h-full w-full flex-col bg-background",children:[(0,u.jsx)(i.w,{path:"/signin"}),(0,u.jsx)("div",{className:"overflow-y-auto",children:t})]})]}):(0,u.jsx)(s.default,{})}},3612:function(e,t,n){"use strict";n.d(t,{a:function(){return s}});var u=n(17062);let s=()=>{let e=(0,u.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,28453,49027,33145,27648,42592,18862,25166,92971,95030,1744],function(){return e(e.s=80531)}),_N_E=e.O()}]);