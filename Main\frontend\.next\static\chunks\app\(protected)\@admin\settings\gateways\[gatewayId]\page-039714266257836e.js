(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[30731],{56402:function(e,t,s){Promise.resolve().then(s.bind(s,51022))},51022:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return _},runtime:function(){return $}});var l=s(57437),r=s(85487),n=s(6596),a=s(16831),i=s(62869),o=s(6512),d=s(73578),c=s(52312),u=s(79981),m=s(94508),f=s(59532),x=s(99376),v=s(43949),p=s(14438),h=s(85323);s(93022),s(24458),s(41062),s(23518),s(26110),s(40593);var g=s(78939),j=s(25429),b=s(15681),y=s(95186),N=s(26815),C=s(74991),w=s(1828),k=s(97751);let Z=["uploadLogo","active","activeApi","recommended","allowedCurrencies"];async function z(e,t){try{let s=function(e){let t=new FormData;return Z.forEach(s=>{void 0!==e[s]&&null!==e[s]&&t.append(s,e[s]instanceof File?e[s]:e[s].toString())}),Object.entries(e).forEach(e=>{let[s,l]=e;Z.includes(s)||null==l||t.append(s,l.toString())}),t}(e),l=await u.Z.put("/admin/gateways/".concat(t),s,{headers:{"Content-Type":"multipart/form-data"}});return(0,k.B)(l)}catch(e){return(0,k.D)(e)}}var I=s(31117),A=s(54995),F=s(13590),L=s(22291),S=s(2265),P=s(29501),R=s(31229);let E=e=>{let t={};return null==e||e.forEach(e=>{t[e.key]=e.required?R.z.string().min(1,{message:"".concat(e.label," is required")}):R.z.string().optional()}),R.z.object({uploadLogo:A.K,active:R.z.boolean().default(!1),activeApi:R.z.boolean().default(!1),recommended:R.z.boolean().default(!1),allowedCurrencies:R.z.string(),...t})};function D(e){var t;let{gateway:s,onMutate:a}=e,o=(0,x.useParams)(),d=(0,x.useSearchParams)().get("name"),[c,u]=(0,S.useTransition)(),{data:f,isLoading:h}=(0,I.d)("/admin/gateways/config"),{t:k}=(0,v.$G)(),Z=null==f?void 0:null===(t=f.data)||void 0===t?void 0:t[d],A=E(Z),R=(0,P.cI)({resolver:(0,F.F)(A),defaultValues:{uploadLogo:(null==s?void 0:s.logoImage)||"",active:!!(null==s?void 0:s.active),activeApi:!!(null==s?void 0:s.activeApi),recommended:!!(null==s?void 0:s.recommended),allowedCurrencies:(null==s?void 0:s.allowedCurrencies)||""}});if((0,S.useEffect)(()=>{if(!h&&Z&&s){let e={};Z.forEach(t=>{e[t.key]=s[t.key]||""});let t={uploadLogo:s.uploadLogo,active:!!s.active,activeApi:!!s.activeApi,recommended:!!s.recommended,allowedCurrencies:s.allowedCurrencies?JSON.parse(s.allowedCurrencies).join(", "):"",...e};R.reset(t)}},[h,Z,s]),h)return(0,l.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,l.jsx)(r.Loader,{})});let D=e=>(null==e?void 0:e.type)==="select"?(0,l.jsx)(b.Wi,{control:R.control,name:null==e?void 0:e.key,render:t=>{let{field:s}=t;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:k(null==e?void 0:e.label)}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(C.E,{defaultValue:s.value,onValueChange:s.onChange,className:"grid-cols-12 gap-4",children:e.options.map(e=>(0,l.jsxs)(N.Z,{htmlFor:e.value,"data-active":s.value===e.value,className:"col-span-12 flex h-12 cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 text-sm font-semibold leading-5 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[(0,l.jsx)(C.m,{id:e.value,value:e.value,className:"absolute left-0 top-0 opacity-0"}),(0,l.jsx)("span",{children:k(e.label)})]},e.value))})}),(0,l.jsx)(b.zG,{})]})}}):(0,l.jsx)(b.Wi,{name:null==e?void 0:e.key,control:R.control,render:t=>{let{field:s}=t;return(0,l.jsxs)(b.xJ,{className:"mt-2",children:[(0,l.jsx)(b.lX,{children:k(null==e?void 0:e.label)}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(y.I,{type:null==e?void 0:e.type,placeholder:k("Enter {{label}}",{label:null==e?void 0:e.label}),...s})}),(0,l.jsx)(b.zG,{})]})}},null==e?void 0:e.key);return(0,l.jsxs)(n.Qd,{value:"GatewayDetails",className:"mb-4 rounded-xl border border-border bg-background px-4 py-0",children:[(0,l.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,l.jsx)("p",{className:"text-base font-medium leading-[22px]",children:(0,m.fl)(null==s?void 0:s.name)})}),(0,l.jsx)(n.vF,{className:"gap-4 border-t pt-4",children:(0,l.jsx)(b.l0,{...R,children:(0,l.jsxs)("form",{onSubmit:R.handleSubmit(e=>{let t={...e,allowedCurrencies:JSON.stringify([e.allowedCurrencies]),name:null==s?void 0:s.name,variables:(null==s?void 0:s.variables)||JSON.stringify({}),value:null==s?void 0:s.value,isCrypto:null==s?void 0:s.isCrypto,allowedCountries:null==s?void 0:s.allowedCountries};u(async()=>{let e=await z(t,null==o?void 0:o.gatewayId);e.status?(a(),p.toast.success(e.message)):p.toast.error(k(e.message))})}),className:"flex flex-col gap-6 px-1",children:[(0,l.jsx)(b.Wi,{control:R.control,name:"uploadLogo",render:e=>{let{field:t}=e;return(0,l.jsxs)(b.xJ,{children:[(0,l.jsx)(b.lX,{children:k("Gateway logo")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(g.S,{defaultValue:(0,m.qR)(null==s?void 0:s.logoImage),id:"uploadLogo",onChange:e=>t.onChange(e),className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,l.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,l.jsx)(j.X,{}),(0,l.jsx)("p",{className:"text-sm font-normal text-primary",children:k("Upload logo")})]})})})]})}}),null==Z?void 0:Z.map(e=>D(e)),(0,l.jsx)(b.Wi,{name:"active",control:R.control,render:e=>{let{field:t}=e;return(0,l.jsxs)(b.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,l.jsx)(N.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:k("Active")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(w.Z,{defaultChecked:t.value,onCheckedChange:t.onChange})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{name:"activeApi",control:R.control,render:e=>{let{field:t}=e;return(0,l.jsxs)(b.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,l.jsx)(N.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:k("Active API")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(w.Z,{defaultChecked:t.value,onCheckedChange:t.onChange})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{name:"recommended",control:R.control,render:e=>{let{field:t}=e;return(0,l.jsxs)(b.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,l.jsx)(N.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:k("Recommended")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(w.Z,{defaultChecked:t.value,onCheckedChange:t.onChange})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)(b.Wi,{name:"allowedCurrencies",control:R.control,render:e=>{let{field:t}=e;return(0,l.jsxs)(b.xJ,{className:"mt-2",children:[(0,l.jsx)(b.lX,{children:k("Supported Currencies")}),(0,l.jsx)(b.NI,{children:(0,l.jsx)(y.I,{type:"text",placeholder:"USD, EUR, GBP, AUD, CAD, SGD",...t})}),(0,l.jsx)(b.zG,{})]})}}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)(i.z,{className:"rounded-lg",children:c?(0,l.jsx)(r.Loader,{title:k("Updating..."),className:"text-primary-foreground"}):(0,l.jsxs)(l.Fragment,{children:[k("Update gateway"),(0,l.jsx)(L.Z,{size:20})]})})})]})})})]})}var G=s(41709),U=s(85539),M=s(17814),J=s(75515),T=s(28152),O=s(83130),B=s(8877),V=s(90433),q=s(83983),W=s(30119);function X(e){var t,s,n,a,o,c;let{gatewayId:m,onMutate:f,blackListedUsers:p}=e,{t:h}=(0,v.$G)(),{width:g}=(0,T.B)(),j=(0,x.useSearchParams)(),[b,y]=(0,S.useState)(!1),[N,C]=S.useState(null!==(c=j.get("search"))&&void 0!==c?c:""),{data:w,isLoading:k,size:Z,setSize:z,mutate:I}=(0,W.ZP)(e=>"/admin/users?page=".concat(e+1,"&limit=").concat(25,"&search=").concat(N),e=>u.Z.get(e));return(0,l.jsxs)(M.dy,{open:b,onOpenChange:y,direction:g<640?"bottom":"right",children:[(0,l.jsx)(M.Qz,{asChild:!0,children:(0,l.jsx)("div",{className:"pt-4",children:(0,l.jsxs)(i.z,{variant:"outline",className:"gap-1 rounded-lg",children:[(0,l.jsx)(B.Z,{}),h("Add Customer")]})})}),(0,l.jsxs)(M.sc,{className:"inset-x-auto bottom-auto left-auto right-0 top-0 m-0 mt-20 flex h-full w-full max-w-[540px] flex-col rounded-t-none bg-background px-0 pt-4 sm:inset-y-0 sm:mt-0 sm:pt-8",children:[(0,l.jsx)("span",{className:"mx-auto mb-8 block h-2.5 w-20 rounded-lg bg-divider-secondary sm:hidden"}),(0,l.jsxs)("div",{className:"flex items-center gap-4 px-6 pb-6",children:[(0,l.jsx)(i.z,{variant:"outline",size:"icon",className:"hidden sm:flex",asChild:!0,children:(0,l.jsx)(M.uh,{children:(0,l.jsx)(V.Z,{size:16})})}),(0,l.jsxs)(M.OX,{className:"flex-1 p-0",children:[(0,l.jsx)(M.iI,{className:"text-left text-base font-semibold leading-[22px]",children:h("Customers")}),(0,l.jsx)(M.u6,{className:"invisible absolute text-xs font-normal text-secondary-text",children:h("You can add customers to the block list to prevent them from using the platform.")})]})]}),(0,l.jsx)("div",{className:"flex flex-col p-6 pt-0",children:(0,l.jsx)(U.R,{value:N,onChange:e=>{e.preventDefault(),C(e.target.value)},iconPlacement:"end",placeholder:h("Search..."),className:"w-full"})}),(0,l.jsx)("div",{id:"scrollbarTrigger",className:"flex-1 overflow-y-auto overflow-x-hidden",children:(0,l.jsxs)("div",{className:"flex flex-col gap-2 p-6 pt-0",children:[(0,l.jsx)(G.J,{condition:k,children:(0,l.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,l.jsx)(r.Loader,{})})}),(0,l.jsx)(G.J,{condition:!k&&!!(null==w?void 0:w.length),children:(0,l.jsx)(q.Z,{dataLength:null==w?void 0:w.reduce((e,t)=>{var s,l,r;return e+Number(null!==(r=null===(l=t.data)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.length)&&void 0!==r?r:0)},0),next:()=>z(Z+1),hasMore:!!(null==w?void 0:null===(n=w[w.length-1])||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:null===(t=s.meta)||void 0===t?void 0:t.nextPageUrl),loader:(0,l.jsx)(r.Loader,{className:"flex justify-center py-4"}),endMessage:(0,l.jsx)("p",{className:"py-4",style:{textAlign:"center"},children:(0,l.jsx)("b",{children:h("No more")})}),scrollableTarget:"scrollbarTrigger",children:(0,l.jsxs)(d.iA,{children:[(0,l.jsx)(d.xD,{children:(0,l.jsxs)(d.SC,{children:[(0,l.jsxs)(d.ss,{className:"w-full",children:[" ",h("Name")," "]}),(0,l.jsxs)(d.ss,{children:[" ",h("Action")," "]})]})}),(0,l.jsx)(d.RM,{children:null===(o=null==w?void 0:w.reduce((e,t)=>{var s,l;return(null==t?void 0:null===(l=t.data)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.length)?[...e,...t.data.data]:e},[]))||void 0===o?void 0:null===(a=o.map(e=>new O.n(e)))||void 0===a?void 0:a.map(e=>(0,l.jsx)(S.Fragment,{children:(0,l.jsx)(Q,{data:e,gatewayId:m,blackListedUsers:p,onMutate:()=>{I(),f()}})},e.id))})]})})})]})})]})]})}function Q(e){let{data:t,gatewayId:s,blackListedUsers:r,onMutate:n}=e,{t:o}=(0,v.$G)(),u=null==t?void 0:t.customer;if(!u)return null;let x=e=>{p.toast.promise((0,J.O)({gatewayId:s,userId:e},"gateways"),{loading:o("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return n(),e.message},error:e=>e.message})},h=e=>{p.toast.promise((0,c.E)({gatewayId:s,userId:e},"gateways"),{loading:o("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return n(),e.message},error:e=>e.message})},g=r.includes(u.id);return(0,l.jsxs)(d.SC,{className:"border-b border-border-primary",children:[(0,l.jsxs)(d.pj,{className:"flex w-full items-center gap-2.5 py-2",children:[(0,l.jsxs)(a.qE,{children:[(0,l.jsx)(a.F$,{src:(0,m.qR)(u.avatar)}),(0,l.jsxs)(a.Q5,{children:[" ",(0,f.v)(u.name)," "]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"block font-medium",children:u.name}),(0,l.jsx)("span",{className:"block text-xs",children:t.email})]})]}),(0,l.jsx)(d.pj,{className:"py-2",children:(0,l.jsx)(i.z,{variant:"outline",onClick:g?()=>h(u.id):()=>x(u.id),size:"sm",className:"rounded-lg",children:g?o("Unblock user"):o("Add to blacklist")})})]})}let $="edge";function _(){var e;let t=(0,x.useParams)(),{t:s}=(0,v.$G)(),{data:g,isLoading:j,mutate:b}=(0,h.ZP)("/admin/gateways/".concat(t.gatewayId),e=>(0,u.Z)(e));if(j)return(0,l.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,l.jsx)(r.Loader,{})});let y=null==g?void 0:g.data,N=e=>{let l={gatewayId:Number(t.gatewayId),userId:e};p.toast.promise((0,c.E)(l,"gateways"),{loading:s("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return b(),e.message},error:e=>e.message})},C=(null==y?void 0:null===(e=y.blackListedUsers)||void 0===e?void 0:e.map(e=>e.customer.userId))||[];return(0,l.jsxs)(n.UQ,{type:"multiple",defaultValue:["GatewayDetails","GatewayDetailsAllowed","BlockList"],children:[(0,l.jsx)(D,{gateway:y,onMutate:b}),(0,l.jsxs)(n.Qd,{value:"BlockList",className:"rounded-xl border border-border bg-background px-4 py-0",children:[(0,l.jsx)(n.o4,{className:"flex items-center justify-between py-6 hover:no-underline",children:(0,l.jsx)("p",{className:"text-base font-medium leading-[22px]",children:s("Block List")})}),(0,l.jsxs)(n.vF,{className:"border-t pt-4",children:[(0,l.jsx)("div",{className:"w-full max-w-[700px]",children:(0,l.jsxs)(d.iA,{children:[(0,l.jsx)(d.xD,{children:(0,l.jsxs)(d.SC,{children:[(0,l.jsxs)(d.ss,{className:"w-full",children:[" ",s("Name")," "]}),(0,l.jsxs)(d.ss,{children:[" ",s("Action")," "]})]})}),(0,l.jsx)(d.RM,{children:null==y?void 0:y.blackListedUsers.map(e=>(0,l.jsxs)(d.SC,{children:[(0,l.jsxs)(d.pj,{className:"flex w-full items-center gap-2.5 py-2",children:[(0,l.jsxs)(a.qE,{children:[(0,l.jsx)(a.F$,{src:(0,m.qR)(null==e?void 0:e.customer.profileImage)}),(0,l.jsxs)(a.Q5,{children:[(0,f.v)(null==e?void 0:e.customer.name)," "]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"block font-medium",children:null==e?void 0:e.customer.name}),(0,l.jsx)("span",{className:"block text-xs",children:e.email})]})]}),(0,l.jsx)(d.pj,{className:"py-2",children:(0,l.jsx)(i.z,{variant:"outline",size:"sm",onClick:()=>N(null==e?void 0:e.customer.userId),className:"rounded-lg",children:s("Unblock")})})]},null==e?void 0:e.id))})]})}),(0,l.jsx)(o.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,l.jsx)(X,{gatewayId:Number(t.gatewayId),onMutate:()=>b(g),blackListedUsers:C})]})]})]})}},78939:function(e,t,s){"use strict";s.d(t,{S:function(){return o}});var l=s(57437),r=s(94508),n=s(33145),a=s(2265),i=s(85598);function o(e){let{defaultValue:t,onChange:s,className:o,children:d,disabled:c=!1,id:u}=e,[m,f]=a.useState(t);a.useEffect(()=>{f(t)},[t]);let{getRootProps:x,getInputProps:v}=(0,i.uI)({onDrop:e=>{let t=null==e?void 0:e[0];t&&(s(t),f(URL.createObjectURL(t)))},disabled:c});return(0,l.jsxs)("div",{...x({className:(0,r.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o)}),children:[!!m&&(0,l.jsx)(n.default,{src:m,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,l.jsx)("input",{id:u,...v()}),!m&&(0,l.jsx)("div",{children:d})]})}},25429:function(e,t,s){"use strict";s.d(t,{X:function(){return n}});var l=s(57437),r=s(94508);function n(e){let{className:t}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,r.ZP)("fill-primary",t),children:[(0,l.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},15681:function(e,t,s){"use strict";s.d(t,{NI:function(){return p},Wi:function(){return u},l0:function(){return d},lX:function(){return v},xJ:function(){return x},zG:function(){return h}});var l=s(57437),r=s(37053),n=s(2265),a=s(29501),i=s(26815),o=s(94508);let d=a.RV,c=n.createContext({}),u=e=>{let{...t}=e;return(0,l.jsx)(c.Provider,{value:{name:t.name},children:(0,l.jsx)(a.Qr,{...t})})},m=()=>{let e=n.useContext(c),t=n.useContext(f),{getFieldState:s,formState:l}=(0,a.Gc)(),r=s(e.name,l);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...r}},f=n.createContext({}),x=n.forwardRef((e,t)=>{let{className:s,...r}=e,a=n.useId();return(0,l.jsx)(f.Provider,{value:{id:a},children:(0,l.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",s),...r})})});x.displayName="FormItem";let v=n.forwardRef((e,t)=>{let{className:s,required:r,...n}=e,{error:a,formItemId:d}=m();return(0,l.jsx)("span",{children:(0,l.jsx)(i.Z,{ref:t,className:(0,o.ZP)(a&&"text-base font-medium text-destructive",s),htmlFor:d,...n})})});v.displayName="FormLabel";let p=n.forwardRef((e,t)=>{let{...s}=e,{error:n,formItemId:a,formDescriptionId:i,formMessageId:o}=m();return(0,l.jsx)(r.g7,{ref:t,id:a,"aria-describedby":n?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!n,...s})});p.displayName="FormControl",n.forwardRef((e,t)=>{let{className:s,...r}=e,{formDescriptionId:n}=m();return(0,l.jsx)("p",{ref:t,id:n,className:(0,o.ZP)("text-sm text-muted-foreground",s),...r})}).displayName="FormDescription";let h=n.forwardRef((e,t)=>{let{className:s,children:r,...n}=e,{error:a,formMessageId:i}=m(),d=a?String(null==a?void 0:a.message):r;return d?(0,l.jsx)("p",{ref:t,id:i,className:(0,o.ZP)("text-sm font-medium text-destructive",s),...n,children:d}):null});h.displayName="FormMessage"},74991:function(e,t,s){"use strict";s.d(t,{E:function(){return o},m:function(){return d}});var l=s(57437),r=s(2265),n=s(42325),a=s(40519),i=s(94508);let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,l.jsx)(n.fC,{className:(0,i.ZP)("grid gap-2",s),...r,ref:t})});o.displayName=n.fC.displayName;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,l.jsx)(n.ck,{ref:t,className:(0,i.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),...r,children:(0,l.jsx)(n.z$,{className:"flex items-center justify-center",children:(0,l.jsx)(a.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=n.ck.displayName},6512:function(e,t,s){"use strict";var l=s(57437),r=s(55156),n=s(2265),a=s(94508);let i=n.forwardRef((e,t)=>{let{className:s,orientation:n="horizontal",decorative:i=!0,...o}=e;return(0,l.jsx)(r.f,{ref:t,decorative:i,orientation:n,className:(0,a.ZP)("shrink-0 bg-divider","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",s),...o})});i.displayName=r.f.displayName,t.Z=i},1828:function(e,t,s){"use strict";var l=s(57437),r=s(50721),n=s(2265),a=s(94508);let i=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,l.jsx)(r.fC,{className:(0,a.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",s),...n,ref:t,children:(0,l.jsx)(r.bU,{className:(0,a.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});i.displayName=r.fC.displayName,t.Z=i},97751:function(e,t,s){"use strict";s.d(t,{B:function(){return r},D:function(){return n}});var l=s(43577);function r(e){var t,s,l;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(l=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==l?l:"",data:null===(s=e.data)||void 0===s?void 0:s.data}}function n(e){let t=500,s="Internal Server Error",r="An unknown error occurred";if((0,l.IZ)(e)){var n,a,i,o,d,c,u,m,f,x,v,p;t=null!==(f=null===(n=e.response)||void 0===n?void 0:n.status)&&void 0!==f?f:500,s=null!==(x=null===(a=e.response)||void 0===a?void 0:a.statusText)&&void 0!==x?x:"Internal Server Error",r=null!==(p=null!==(v=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(i=o[0])||void 0===i?void 0:i.message)&&void 0!==v?v:null===(m=e.response)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:u.message)&&void 0!==p?p:e.message}else e instanceof Error&&(r=e.message);return{statusCode:t,statusText:s,status:!1,message:r,data:void 0,error:e}}},24458:function(e,t,s){"use strict";s.d(t,{j:function(){return a}});var l=s(28315),r=s(79981),n=s(85323);function a(){let{data:e,isLoading:t,error:s,mutate:a}=(0,n.ZP)("/currencies",e=>r.Z.get(e)),i=null==e?void 0:e.data;return{currencies:i?i.map(e=>new l.F(e)):[],isLoading:t,error:s,mutate:a}}},31117:function(e,t,s){"use strict";s.d(t,{d:function(){return n}});var l=s(79981),r=s(85323);let n=(e,t)=>(0,r.ZP)(e||null,e=>l.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},54995:function(e,t,s){"use strict";s.d(t,{K:function(){return a},S:function(){return i}});var l=s(31229);let r=["image/jpeg","image/jpg","image/png","image/svg+xml"],n=["image/x-icon","image/vnd.microsoft.icon","image/png"],a=l.z.union([l.z.string(),l.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&r.includes(e.type),"Invalid file format. Please upload a .jpg, .jpeg, .png or .svg file."),i=l.z.union([l.z.string(),l.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&n.includes(e.type),"Invalid file format. Please upload a .ico or .png file.")},28315:function(e,t,s){"use strict";s.d(t,{F:function(){return l}});class l{format(e){let{currencySymbol:t,amountText:s}=this.formatter(e);return"".concat(s," ").concat(t)}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}constructor(e){var t;this.formatter=e=>{var t,s;let l=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),r=null!==(s=null===(t=l.formatToParts(e).find(e=>"currency"===e.type))||void 0===t?void 0:t.value)&&void 0!==s?s:this.code,n=l.format(e),a=n.substring(r.length).trim();return{currencyCode:this.code,currencySymbol:r,formattedAmount:n,amountText:a}},this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.code=null==e?void 0:e.code,this.logo=null!==(t=null==e?void 0:e.logo)&&void 0!==t?t:"",this.usdRate=null==e?void 0:e.usdRate,this.acceptApiRate=!!(null==e?void 0:e.acceptApiRate),this.isCrypto=!!(null==e?void 0:e.isCrypto),this.active=!!(null==e?void 0:e.active),this.metaData=null==e?void 0:e.metaData,this.minAmount=null==e?void 0:e.minAmount,this.kycLimit=null==e?void 0:e.kycLimit,this.maxAmount=null==e?void 0:e.maxAmount,this.dailyTransferAmount=null==e?void 0:e.dailyTransferAmount,this.dailyTransferLimit=null==e?void 0:e.dailyTransferLimit,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,49027,33145,38658,42592,85598,19935,50581,29799,56219,227,90539,92971,95030,1744],function(){return e(e.s=56402)}),_N_E=e.O()}]);