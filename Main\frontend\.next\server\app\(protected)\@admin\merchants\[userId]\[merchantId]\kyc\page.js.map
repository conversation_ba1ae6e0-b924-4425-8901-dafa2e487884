{"version": 3, "file": "app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page.js", "mappings": "qFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,2CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,kGEAAC,gxBDKA,IAAAC,EACA,CACA,GACA,CACAC,SAAA,CACA,cACA,CACAC,MAAA,CACA,WACA,CACAD,SAAA,CACA,YACA,CACAA,SAAA,CACA,WACA,CACAA,SAAA,CACA,eACA,CACAA,SAAA,CACA,MACA,CACAA,SAAA,eAAiC,CACjCE,KAAA,KAAuBC,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAA+K,+IAE7L,EAET,CACA,aAA0BH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkL,kJAG5M,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA4K,2IACrM,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA6K,6IAG/L,EAEA,CAGA,EACA,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoJ,mHAC7K,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqJ,qHAGvK,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAyI,wGAClK,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0I,0GAG5J,CACAN,SAAA,CACA,cACA,GACA,CACAQ,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAG,MAAA,CACA,cACA,GACA,CACAD,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAI,SAAA,CACA,cACA,GACA,CACAF,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,CACAK,SAAA,CACA,cACA,GACA,CACAH,YAAA,KAAgCL,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAA0F,0DAE1H,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAiI,gGAC1J,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAkI,kGAGpJ,EAEA,CACA,YAAyBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAoH,mFAC7I,aAAkBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,QAAqH,oFACvI,iBAAoBH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,OAAuH,uFAG3I,CAEAM,EAAA,+IAKOC,EAAA,+DACAC,EAAA,CACPjB,QAJ6BS,EAK7BS,UAJA,IAAAZ,QAAAC,OAAA,EAKA,EAGOY,EAAA,IAAwBC,EAAAC,kBAAkB,EACjDC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,QAAA,CACvBrB,KAAA,+DACAsB,SAAA,uCAEAC,WAAA,GACAC,SAAA,GACAC,SAAA,IAEAC,SAAA,CACAC,WAAA9B,CACA,CACA,uCCjHA,IAAA+B,EAAA,GAAAC,EAAAC,KAAAC,KAAA,CAAAF,GAAAG,KAAAA,EACAC,EAAAC,KAAAC,gBAAA,CACAC,EAAAR,EAAAM,KAAAG,yBAAA,EACAC,EAAA,MAAA1C,CAAAA,EAAAsC,KAAAK,cAAA,SAAA3C,CAAA,iEACA4C,EAAAZ,EAAAM,KAAAO,qBAAA,EAEAC,EAAAd,EAAAM,KAAAS,oBAAA,EACAC,EAAAhB,EAAAM,KAAAW,qCAAA,MACAP,GAAAE,GACI,GAAAM,EAAAC,EAAA,EAA8B,CAClCC,wBAAAV,EACAW,sBAAAT,EACAU,gBAAyB,GAAAC,EAAAC,CAAA,EAAqB,CAC9CH,sBAAAT,EACAa,SAAA,8DACA,EACA,GAEA,IAAMC,EAAS,GAAAC,EAAAC,CAAA,EAAS,CACxBC,UAAeC,EAAAC,CAAU,CAAAC,GAAA,CACzBC,IAvBA,GAwBA7D,KAAA,+DACA8D,OA9BA,KA+BAC,QAAWC,EACXC,SA/BA,KAgCAC,YA/BA,KAgCAC,SAnCA,KAoCAlC,cAAAA,EACAmC,aAAgBC,EAAAC,CAAA,CAChBlC,sBAAAA,EACAY,wBAAAV,EACAW,sBAAAT,EACA+B,cAlCAvC,KAAAA,EAmCAwC,6BA5BAxC,KAAAA,EA6BAyC,OAnCA,CAAoB,OAAQ,qBAA0B,uBAA2B,YAAe,mDAAyD,qPAAwR,wTAAkV,iDAAuD,sCAAyC,CAAE,gCAAmC,iBAAsB,eAAkB,uDAA4D,iBAAoB,wCAA6C,KAAQ,kBAAmB,2BAA+B,iIAAuJ,uBAAyB,mEAA6E,cAAiB,oFAAiG,uBAAuB,0CAA2C,EAAE,QAAW,6BAA8B,GAAG,cAAiB,w6BAA4lC,uBAA0B,o9BAAs9B,+HAoCnqHC,QAAAC,QAAAC,GAAA,CAAAC,eAAA,CACAnC,iBAAAA,EACAoC,wBA/CA,KAgDAlC,0BAAAA,CACA,GACOmC,EAAqBf,EACb,SAAAgB,EAAAC,CAAA,EACf,MAAW,GAAAC,EAAAC,CAAA,EAAO,CAClB,GAAAF,CAAA,CACAG,iBAAwBC,EAAAC,CAAA,CACxBC,QAAiBjC,CACjB,EACA,mBCnEArD,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,yBCAAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,6VCgDe,SAASoF,IACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IAET,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,EAAO,CAAC,iBAAiB,EAAEJ,EAAOK,UAAU,CAAC,CAAC,EAEpE,CAAEC,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAGRC,EAAY,CAACC,EAAqBC,KACtCC,EAAAA,KAAKA,CAACC,OAAO,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,EAAiBJ,EAAIC,GAAO,CACxCI,QAASR,EAAE,cACXS,QAAS,IACP,GAAI,CAACC,EAAIC,MAAM,CAAE,MAAM,MAAUD,EAAIE,OAAO,EAC5C,OAAOF,EAAIE,OAAO,EAEpBC,MAAO,GAASC,EAAIF,OAAO,EAE/B,EAEMG,EAAMnB,GAAMA,MAAMoB,MAAMD,IAAM,IAAIE,EAAAA,CAAGA,CAACrB,EAAKA,IAAI,CAACoB,IAAI,CAACD,GAAG,EAAI,KAElE,MACE,GAAAG,EAAAC,GAAA,EAACC,EAAAA,EAASA,CAAAA,CACRhB,KAAK,WACLiB,aAAc,CAAC,aAAc,uBAAuB,UAEpD,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACb,GAAAN,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,yDACb,GAAAN,EAAAI,IAAA,EAACG,EAAAA,EAAaA,CAAAA,CAACC,MAAM,aAAaF,UAAU,kCAC1C,GAAAN,EAAAC,GAAA,EAACQ,EAAAA,EAAgBA,CAAAA,CAACH,UAAU,mCAC1B,GAAAN,EAAAI,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACb,GAAAN,EAAAC,GAAA,EAACS,IAAAA,CAAEJ,UAAU,gDACVxB,EAAE,gBAIL,GAAAkB,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAACf,WAChB,GAAAG,EAAAC,GAAA,EAACY,EAAAA,CAAKA,CAAAA,CAACP,UAAU,yDACdxB,EAAE,uBAKP,GAAAkB,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAWf,GAAKJ,SAAW,mBAC/B,GAAAO,EAAAC,GAAA,EAACY,EAAAA,CAAKA,CAAAA,CAACP,UAAU,8DACdxB,EAAE,eAIP,GAAAkB,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAWf,GAAKJ,SAAW,oBAC/B,GAAAO,EAAAC,GAAA,EAACY,EAAAA,CAAKA,CAAAA,CAACP,UAAU,0EACdxB,EAAE,gBAIP,GAAAkB,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAWf,GAAKJ,SAAW,kBAC/B,GAAAO,EAAAC,GAAA,EAACY,EAAAA,CAAKA,CAAAA,CAACP,UAAU,iEACdxB,EAAE,qBAKX,GAAAkB,EAAAI,IAAA,EAACU,EAAAA,EAAgBA,CAAAA,CAACR,UAAU,kEAE1B,GAAAN,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAW,CAACf,WAChB,GAAAG,EAAAI,IAAA,EAACW,EAAAA,EAAKA,CAAAA,CAACT,UAAU,8EACf,GAAAN,EAAAC,GAAA,EAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKC,QAAQ,SAC1B,GAAAlB,EAAAC,GAAA,EAACkB,EAAAA,EAAUA,CAAAA,CAACb,UAAU,kDACnBxB,EAAE,2CAEL,GAAAkB,EAAAC,GAAA,EAACmB,EAAAA,CAAgBA,CAAAA,CAACd,UAAU,sCACzBxB,EACC,kJAMR,GAAAkB,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAWf,GAAKJ,SAAW,mBAE/B,GAAAO,EAAAI,IAAA,EAACW,EAAAA,EAAKA,CAAAA,CAACT,UAAU,2EACf,GAAAN,EAAAC,GAAA,EAACoB,EAAAA,CAAYA,CAAAA,CAACJ,KAAK,KAAKC,QAAQ,SAChC,GAAAlB,EAAAC,GAAA,EAACkB,EAAAA,EAAUA,CAAAA,CAACb,UAAU,kDACnBxB,EAAE,0BAEL,GAAAkB,EAAAC,GAAA,EAACmB,EAAAA,CAAgBA,CAAAA,CAACd,UAAU,sCACzBxB,EACC,kJAMR,GAAAkB,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAWf,GAAKJ,SAAW,oBAC/B,GAAAO,EAAAI,IAAA,EAACW,EAAAA,EAAKA,CAAAA,CAACT,UAAU,iFACf,GAAAN,EAAAC,GAAA,EAACqB,EAAAA,CAAUA,CAAAA,CAACL,KAAK,KAAKC,QAAQ,SAC9B,GAAAlB,EAAAC,GAAA,EAACkB,EAAAA,EAAUA,CAAAA,CAACb,UAAU,kDACnBxB,EAAE,8BAEL,GAAAkB,EAAAC,GAAA,EAACmB,EAAAA,CAAgBA,CAAAA,CAACd,UAAU,sCACzBxB,EACC,kJAMR,GAAAkB,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAWf,GAAKJ,SAAW,kBAC/B,GAAAO,EAAAI,IAAA,EAACW,EAAAA,EAAKA,CAAAA,CAACT,UAAU,0EACf,GAAAN,EAAAC,GAAA,EAACsB,EAAAA,CAAWA,CAAAA,CAACN,KAAK,KAAKC,QAAQ,SAC/B,GAAAlB,EAAAC,GAAA,EAACkB,EAAAA,EAAUA,CAAAA,CAACb,UAAU,kDACnBxB,EAAE,2BAEL,GAAAkB,EAAAC,GAAA,EAACmB,EAAAA,CAAgBA,CAAAA,CAACd,UAAU,sCACzBxB,EACC,sMASd,GAAAkB,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,yDACb,GAAAN,EAAAI,IAAA,EAACG,EAAAA,EAAaA,CAAAA,CACZC,MAAM,uBACNF,UAAU,kCAEV,GAAAN,EAAAC,GAAA,EAACQ,EAAAA,EAAgBA,CAAAA,CAACH,UAAU,mCAC1B,GAAAN,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,mCACb,GAAAN,EAAAC,GAAA,EAACS,IAAAA,CAAEJ,UAAU,gDACVxB,EAAE,mBAIT,GAAAkB,EAAAC,GAAA,EAACa,EAAAA,EAAgBA,CAAAA,CAACR,UAAU,iEACzB,EAOC,GAAAN,EAAAI,IAAA,EAACC,MAAAA,WACC,GAAAL,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,yBACb,GAAAN,EAAAI,IAAA,EAACoB,EAAAA,EAAKA,CAAAA,CAAClB,UAAU,wBACf,GAAAN,EAAAC,GAAA,EAACwB,EAAAA,EAAWA,CAAAA,CAACnB,UAAU,6BACrB,GAAAN,EAAAI,IAAA,EAACsB,EAAAA,EAAQA,CAAAA,WACP,GAAA1B,EAAAC,GAAA,EAAC0B,EAAAA,EAASA,CAAAA,UAAE7C,EAAE,SACd,GAAAkB,EAAAC,GAAA,EAAC0B,EAAAA,EAASA,CAAAA,UAAE7C,EAAE,eAIlB,GAAAkB,EAAAC,GAAA,EAAC2B,EAAAA,EAASA,CAAAA,UACPjD,EACC,GAAAqB,EAAAC,GAAA,EAACyB,EAAAA,EAAQA,CAAAA,UACP,GAAA1B,EAAAC,GAAA,EAAC4B,EAAAA,EAASA,CAAAA,CAACC,QAAS,WAClB,GAAA9B,EAAAC,GAAA,EAAC8B,EAAAA,MAAMA,CAAAA,CAAAA,OAIX,GAAA/B,EAAAI,IAAA,EAAAJ,EAAAgC,QAAA,YACE,GAAAhC,EAAAI,IAAA,EAACsB,EAAAA,EAAQA,CAAAA,CAACpB,UAAU,0BAClB,GAAAN,EAAAC,GAAA,EAAC4B,EAAAA,EAASA,CAAAA,UAAE/C,EAAE,mBACd,GAAAkB,EAAAC,GAAA,EAAC4B,EAAAA,EAASA,CAAAA,UACR,GAAA7B,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,6CACb,GAAAN,EAAAC,GAAA,EAACgC,OAAAA,CAAK3B,UAAU,yCACbT,GAAKqC,sBAKd,GAAAlC,EAAAC,GAAA,EAACkC,EAAAA,CACCC,MAAOtD,EAAE,eACTuD,QAASxC,GAAKyC,QAGhB,GAAAtC,EAAAC,GAAA,EAACkC,EAAAA,CACCC,MAAOtD,EAAE,cACTuD,QAASxC,GAAK0C,OAGhB,GAAAvC,EAAAC,GAAA,EAACkC,EAAAA,CACCC,MAAOtD,EAAE,UACTuD,QAASxC,GAAK2C,mBAQ1B,GAAAxC,EAAAC,GAAA,EAACU,EAAAA,CAAIA,CAAAA,CAACC,UAAWf,EAAIJ,MAAM,EAAEgD,gBAAkB,mBAC7C,GAAAzC,EAAAI,IAAA,EAACC,MAAAA,CAAIC,UAAU,yDACb,GAAAN,EAAAI,IAAA,EAACsC,EAAAA,CAAMA,CAAAA,CACLxD,KAAK,SACLyD,QAAS,IAAM3D,EAAUa,EAAIZ,EAAE,CAAE,UACjCqB,UAAU,uDAEV,GAAAN,EAAAC,GAAA,EAAC2C,EAAAA,CAAUA,CAAAA,CAAAA,GACV9D,EAAE,cAEL,GAAAkB,EAAAI,IAAA,EAACsC,EAAAA,CAAMA,CAAAA,CACLxD,KAAK,SACLyD,QAAS,IAAM3D,EAAUa,EAAIZ,EAAE,CAAE,WACjCqB,UAAU,uDAEV,GAAAN,EAAAC,GAAA,EAAC4C,EAAAA,CAAWA,CAAAA,CAAAA,GACX/D,EAAE,qBAvEX,GAAAkB,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,gBACb,GAAAN,EAAAC,GAAA,EAACS,IAAAA,CAAEJ,UAAU,+BACVxB,EAAE,kDAiFvB,CAEA,SAASqD,EAAY,CAAEC,MAAAA,CAAK,CAAEC,QAAAA,CAAO,CAAsC,EACzE,GAAM,CAAEvD,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAEd,MACE,GAAAiB,EAAAI,IAAA,EAACsB,EAAAA,EAAQA,CAAAA,CAACpB,UAAU,0BAClB,GAAAN,EAAAC,GAAA,EAAC4B,EAAAA,EAASA,CAAAA,UAAEO,IACZ,GAAApC,EAAAC,GAAA,EAAC4B,EAAAA,EAASA,CAAAA,UACR,GAAA7B,EAAAI,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAN,EAAAI,IAAA,EAAC0C,EAAAA,EAAMA,CAAAA,WACL,GAAA9C,EAAAC,GAAA,EAAC8C,EAAAA,EAAaA,CAAAA,CAACC,QAAO,YACpB,GAAAhD,EAAAI,IAAA,EAACsC,EAAAA,CAAMA,CAAAA,CACLxD,KAAK,SACLgC,QAAQ,UACRZ,UAAU,yCAEV,GAAAN,EAAAC,GAAA,EAACgD,EAAAA,CAAGA,CAAAA,CAAAA,GACJ,GAAAjD,EAAAC,GAAA,EAACgC,OAAAA,CAAK3B,UAAU,2BAAmBxB,EAAE,eAIzC,GAAAkB,EAAAI,IAAA,EAAC8C,EAAAA,EAAaA,CAAAA,CAAC5C,UAAU,sBACvB,GAAAN,EAAAI,IAAA,EAAC+C,EAAAA,EAAYA,CAAAA,WACX,GAAAnD,EAAAI,IAAA,EAACgD,EAAAA,EAAWA,CAAAA,WAAC,IAAEhB,EAAM,OACrB,GAAApC,EAAAC,GAAA,EAACoD,EAAAA,EAAiBA,CAAAA,CAAC/C,UAAU,SAASgD,cAAW,QAGnD,GAAAtD,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,0DACZ+B,EACC,GAAArC,EAAAC,GAAA,EAACsD,EAAAA,CAAKA,CAAAA,CACJC,IAAKnB,EACLoB,IAAKrB,EACLsB,KAAI,GACJC,MAAM,QACNC,MAAO,CAAEC,MAAO,OAAQC,OAAQ,MAAO,EACvCC,QAAQ,KACRzE,QAAQ,OACR0E,YAAY,OACZ1D,UAAU,iBACV2D,YAAY,uHAGd,GAAAjE,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,+EACb,GAAAN,EAAAC,GAAA,EAACgC,OAAAA,CAAK3B,UAAU,oCACbxB,EAAE,0BAQf,GAAAkB,EAAAC,GAAA,EAACyC,EAAAA,CAAMA,CAAAA,CACLxD,KAAK,SACLgC,QAAQ,UACRZ,UAAU,+BACV0C,QAAO,YAEP,GAAAhD,EAAAI,IAAA,EAAC8D,IAAAA,CAAEC,KAAM9B,EAAS+B,SAAQ,aACxB,GAAApE,EAAAC,GAAA,EAACoE,EAAAA,CAAgBA,CAAAA,CAAAA,GACjB,GAAArE,EAAAC,GAAA,EAACgC,OAAAA,CAAK3B,UAAU,kCAA0BxB,EAAE,0BAO1D,+PCxTO,IAAMwF,EAAU,OAER,SAASC,EAAsB,CAC5C1L,SAAAA,CAAQ,CAGT,EACC,IAAM2F,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACT+F,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,IACfC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,IACTtK,EAAWuK,CAAAA,EAAAA,EAAAA,EAAAA,IAEX,CAAE9F,EAAAA,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,IAER8F,EAAO,CACX,CACEzC,MAAOtD,EAAE,mBACTgG,KAAM,GAAA9E,EAAAC,GAAA,EAAC8E,EAAAA,CAAQA,CAAAA,CAAC9D,KAAK,KAAKC,QAAQ,SAClCiD,KAAM,CAAC,WAAW,EAAE3F,GAAQwG,OAAO,CAAC,EAAExG,GAAQK,WAAW,CAAC,EAAE2F,EAAaS,QAAQ,GAAG,CAAC,CACrFhG,GAAI,aACN,EACA,CACEmD,MAAOtD,EAAE,gBACTgG,KAAM,GAAA9E,EAAAC,GAAA,EAACiF,EAAAA,CAAKA,CAAAA,CAACjE,KAAK,KAAKC,QAAQ,SAC/BiD,KAAM,CAAC,WAAW,EAAE3F,GAAQwG,OAAO,CAAC,EAAExG,GAAQK,WAAW,cAAc,EAAE2F,EAAaS,QAAQ,GAAG,CAAC,CAClGhG,GAAI,cACN,EACA,CACEmD,MAAOtD,EAAE,OACTgG,KAAM,GAAA9E,EAAAC,GAAA,EAACkF,EAAAA,CAAcA,CAAAA,CAAClE,KAAK,KAAKC,QAAQ,SACxCiD,KAAM,CAAC,WAAW,EAAE3F,GAAQwG,OAAO,CAAC,EAAExG,GAAQK,WAAW,KAAK,EAAE2F,EAAaS,QAAQ,GAAG,CAAC,CACzFhG,GAAI,KACN,EACA,CACEmD,MAAOtD,EAAE,QACTgG,KAAM,GAAA9E,EAAAC,GAAA,EAACkF,EAAAA,CAAcA,CAAAA,CAAClE,KAAK,KAAKC,QAAQ,SACxCiD,KAAM,CAAC,WAAW,EAAE3F,GAAQwG,OAAO,CAAC,EAAExG,GAAQK,WAAW,MAAM,EAAE2F,EAAaS,QAAQ,GAAG,CAAC,CAC1FhG,GAAI,MACN,EACA,CACEmD,MAAOtD,EAAE,eACTgG,KAAM,GAAA9E,EAAAC,GAAA,EAACmF,EAAAA,CAAOA,CAAAA,CAACnE,KAAK,KAAKC,QAAQ,SACjCiD,KAAM,CAAC,WAAW,EAAE3F,GAAQwG,OAAO,CAAC,EAAExG,GAAQK,WAAW,aAAa,EAAE2F,EAAaS,QAAQ,GAAG,CAAC,CACjGhG,GAAI,aACN,EACA,CACEmD,MAAOtD,EAAE,cACTgG,KAAM,GAAA9E,EAAAC,GAAA,EAACoF,EAAAA,CAAGA,CAAAA,CAACpE,KAAK,KAAKC,QAAQ,SAC7BiD,KAAM,CAAC,WAAW,EAAE3F,GAAQwG,OAAO,CAAC,EAAExG,GAAQK,WAAW,YAAY,EAAE2F,EAAaS,QAAQ,GAAG,CAAC,CAChGhG,GAAI,YACN,EACD,CAED,MACE,GAAAe,EAAAI,IAAA,EAAAJ,EAAAgC,QAAA,YACE,GAAAhC,EAAAI,IAAA,EAACC,MAAAA,CAAIC,UAAU,+FACb,GAAAN,EAAAI,IAAA,EAACC,MAAAA,CAAIC,UAAU,mEACb,GAAAN,EAAAI,IAAA,EAACkF,KAAAA,CAAGhF,UAAU,iJACZ,GAAAN,EAAAC,GAAA,EAACsF,KAAAA,UACC,GAAAvF,EAAAI,IAAA,EAACoF,EAAAA,CAAIA,CAAAA,CACHrB,KAAK,kBACL7D,UAAU,0FAEV,GAAAN,EAAAC,GAAA,EAACwF,EAAAA,CAAUA,CAAAA,CAAAA,GACV3G,EAAE,aAGP,GAAAkB,EAAAI,IAAA,EAACmF,KAAAA,CAAGjF,UAAU,2CAAiC,KAC1CkE,EAAakB,GAAG,CAAC,WAEtB,GAAA1F,EAAAI,IAAA,EAACmF,KAAAA,CAAGjF,UAAU,2CAAiC,KAC1CxB,EAAE,YAAY,KAAGN,EAAOK,UAAU,OAGzC,GAAAmB,EAAAI,IAAA,EAACC,MAAAA,CAAIC,UAAU,wEACb,GAAAN,EAAAC,GAAA,EAACgC,OAAAA,UAAMnD,EAAE,YACT,GAAAkB,EAAAC,GAAA,EAAC0F,EAAAA,CAAMA,CAAAA,CACLC,eAAgBpB,MAAAA,EAAakB,GAAG,CAAC,UACjCpF,UAAU,kCACVuF,gBAAiB,IACf1G,EAAAA,KAAKA,CAACC,OAAO,CAAC0G,CAAAA,EAAAA,EAAAA,CAAAA,EAAetH,EAAOwG,MAAM,EAAa,CACrD1F,QAASR,EAAE,cACXS,QAAS,IACP,GAAI,CAACC,EAAIC,MAAM,CAAE,MAAM,MAAUD,EAAIE,OAAO,EAC5C,IAAMqG,EAAK,IAAIC,gBAAgBxB,GAI/B,MAHAyB,CAAAA,EAAAA,EAAAA,CAAAA,EAAO,CAAC,iBAAiB,EAAEzH,EAAOK,UAAU,CAAC,CAAC,EAC9CkH,EAAGG,GAAG,CAAC,SAAUC,EAAU,IAAM,KACjCzB,EAAO0B,IAAI,CAAC,CAAC,EAAE/L,EAAS,CAAC,EAAE0L,EAAGd,QAAQ,GAAG,CAAC,EACnCzF,EAAIE,OAAO,EAEpBC,MAAO,GAASC,EAAIF,OAAO,EAE/B,UAKN,GAAAM,EAAAC,GAAA,EAACoG,EAAAA,CAAYA,CAAAA,CAACxB,KAAMA,OAGrBhM,IAGP,wICxHA,IAAMqH,EAAYoG,EAAAA,EAAuB,CAEnC/F,EAAgBgG,EAAAA,UAAgB,CAGpC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAACqG,EAAAA,EAAuB,EACtBG,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,WAAYpG,GACzB,GAAGkG,CAAK,GAGbjG,CAAAA,EAAcoG,WAAW,CAAG,gBAE5B,IAAMlG,EAAmB8F,EAAAA,UAAgB,CAGvC,CAAC,CAAEjG,UAAAA,CAAS,CAAEzH,SAAAA,CAAQ,CAAE,GAAG2N,EAAO,CAAEC,IACpC,GAAAzG,EAAAC,GAAA,EAACqG,EAAAA,EAAyB,EAAChG,UAAU,gBACnC,GAAAN,EAAAI,IAAA,EAACkG,EAAAA,EAA0B,EACzBG,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,+HACApG,GAED,GAAGkG,CAAK,WAER3N,EACD,GAAAmH,EAAAC,GAAA,EAAC2G,EAAAA,CAAUA,CAAAA,CAACtG,UAAU,4DAI5BG,CAAAA,EAAiBkG,WAAW,CAAGL,EAAAA,EAA0B,CAACK,WAAW,CAErE,IAAM7F,EAAmByF,EAAAA,UAAgB,CAGvC,CAAC,CAAEjG,UAAAA,CAAS,CAAEzH,SAAAA,CAAQ,CAAE,GAAG2N,EAAO,CAAEC,IACpC,GAAAzG,EAAAC,GAAA,EAACqG,EAAAA,EAA0B,EACzBG,IAAKA,EACLnG,UAAU,2HACT,GAAGkG,CAAK,UAET,GAAAxG,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,YAAapG,YAAazH,MAIjDiI,CAAAA,EAAiB6F,WAAW,CAAGL,EAAAA,EAA0B,CAACK,WAAW,iHClDrE,IAAME,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,EACpB,4JACA,CACEC,SAAU,CACR7F,QAAS,CACP8F,QAAS,gCACTC,YACE,yFACJ,CACF,EACAC,gBAAiB,CACfhG,QAAS,SACX,CACF,GAGIH,EAAQwF,EAAAA,UAAgB,CAG5B,CAAC,CAAEjG,UAAAA,CAAS,CAAEY,QAAAA,CAAO,CAAE,GAAGsF,EAAO,CAAEC,IACnC,GAAAzG,EAAAC,GAAA,EAACI,MAAAA,CACCoG,IAAKA,EACLU,KAAK,QACL7G,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAGG,EAAc,CAAE3F,QAAAA,CAAQ,GAAIZ,GACzC,GAAGkG,CAAK,GAGbzF,CAAAA,EAAM4F,WAAW,CAAG,QAEpB,IAAMxF,EAAaoF,EAAAA,UAAgB,CAGjC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAACmH,KAAAA,CACCX,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,+CAAgDpG,GAC7D,GAAGkG,CAAK,GAGbrF,CAAAA,EAAWwF,WAAW,CAAG,aAEzB,IAAMvF,EAAmBmF,EAAAA,UAAgB,CAGvC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAACI,MAAAA,CACCoG,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,gCAAiCpG,GAC9C,GAAGkG,CAAK,GAGbpF,CAAAA,EAAiBuF,WAAW,CAAG,mHCnD/B,IAAMU,EAAgBP,CAAAA,EAAAA,EAAAA,CAAAA,EACpB,uLACA,CACEC,SAAU,CACR7F,QAAS,CACP8F,QAAS,wDACTM,UAAW,wDACX/H,QAAS,wDACTgI,UAAW,4DACX5H,MAAO,gEACP6H,QAAS,wDACTP,YACE,gEACFQ,QAAS,iBACX,CACF,EACAP,gBAAiB,CACfhG,QAAS,SACX,CACF,GAOF,SAASL,EAAM,CAAEP,UAAAA,CAAS,CAAEY,QAAAA,CAAO,CAAE,GAAGsF,EAAmB,EACzD,MACE,GAAAxG,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAGW,EAAc,CAAEnG,QAAAA,CAAQ,GAAIZ,GAAa,GAAGkG,CAAK,EAExE,oIC/BA,IAAMhF,EAAQ+E,EAAAA,UAAgB,CAG5B,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,yCACb,GAAAN,EAAAC,GAAA,EAACyH,QAAAA,CACCjB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,gCAAiCpG,GAC9C,GAAGkG,CAAK,KAIfhF,CAAAA,EAAMmF,WAAW,CAAG,QAEpB,IAAMlF,EAAc8E,EAAAA,UAAgB,CAGlC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAAC0H,QAAAA,CAAMlB,IAAKA,EAAKnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,GAAIpG,GAAa,GAAGkG,CAAK,GAE1D/E,CAAAA,EAAYkF,WAAW,CAAG,cAE1B,IAAM/E,EAAY2E,EAAAA,UAAgB,CAGhC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAAC2H,QAAAA,CACCnB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,6BAA8BpG,GAC3C,GAAGkG,CAAK,GAGb5E,CAAAA,EAAU+E,WAAW,CAAG,YAexBkB,EAboBtB,UAAgB,CAGlC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAAC6H,QAAAA,CACCrB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,0DACApG,GAED,GAAGkG,CAAK,IAGDG,WAAW,CAAG,cAE1B,IAAMjF,EAAW6E,EAAAA,UAAgB,CAG/B,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAAC8H,KAAAA,CACCtB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,qEACApG,GAED,GAAGkG,CAAK,GAGb9E,CAAAA,EAASiF,WAAW,CAAG,WAEvB,IAAMhF,EAAY4E,EAAAA,UAAgB,CAGhC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAAC+H,KAAAA,CACCvB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EACT,mGACApG,GAED,GAAGkG,CAAK,GAGb7E,CAAAA,EAAUgF,WAAW,CAAG,YAExB,IAAM9E,EAAY0E,EAAAA,UAAgB,CAGhC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAACgI,KAAAA,CACCxB,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,iDAAkDpG,GAC/D,GAAGkG,CAAK,GAGb3E,CAAAA,EAAU8E,WAAW,CAAG,YAYxBuB,EAVqB3B,UAAgB,CAGnC,CAAC,CAAEjG,UAAAA,CAAS,CAAE,GAAGkG,EAAO,CAAEC,IAC1B,GAAAzG,EAAAC,GAAA,EAACkI,UAAAA,CACC1B,IAAKA,EACLnG,UAAWoG,CAAAA,EAAAA,EAAAA,EAAAA,EAAG,qCAAsCpG,GACnD,GAAGkG,CAAK,IAGAG,WAAW,CAAG,uFCrGpB,eAAeb,EACpBsC,CAA2B,EAE3B,GAAI,CACF,IAAMC,EAAW,MAAMC,EAAAA,CAAKA,CAACC,GAAG,CAC9B,CAAC,2BAA2B,EAAEH,EAAW,CAAC,CAC1C,CAAC,GAGH,MAAOI,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBH,EAC3B,CAAE,MAAO1I,EAAO,CACd,MAAO8I,CAAAA,EAAAA,EAAAA,CAAAA,EAAuB9I,EAChC,CACF,0ECdO,IAAMN,EAAmB,MAC9BJ,EACAC,KAEA,GAAI,CACF,IAAMM,EAAM,MAAM8I,EAAAA,CAAKA,CAACC,GAAG,CAAC,CAAC,YAAY,EAAErJ,EAAK,CAAC,EAAED,EAAG,CAAC,CAAE,CAAC,GAC1D,MAAOuJ,CAAAA,EAAAA,EAAAA,CAAAA,EAAkBhJ,EAC3B,CAAE,MAAOG,EAAO,CACd,MAAO8I,CAAAA,EAAAA,EAAAA,CAAAA,EAAuB9I,EAChC,CACF,+DCXO,OAAMI,EAWX2I,YAAYhK,CAAyB,CAAE,CACrC,IAAI,CAACO,EAAE,CAAGP,GAAMO,GAChB,IAAI,CAAC+F,MAAM,CAAGtG,GAAMsG,OACpB,IAAI,CAAC9C,YAAY,CAAGxD,GAAMwD,cAAcyG,cACxC,IAAI,CAACnG,MAAM,CAAGoG,CAAAA,EAAAA,EAAAA,EAAAA,EAASlK,GAAM8D,QAC7B,IAAI,CAACF,KAAK,CAAGsG,CAAAA,EAAAA,EAAAA,EAAAA,EAASlK,GAAM4D,OAC5B,IAAI,CAACC,IAAI,CAAGqG,CAAAA,EAAAA,EAAAA,EAAAA,EAASlK,GAAM6D,MAC3B,IAAI,CAAC9C,MAAM,CAAGf,GAAMe,OACpB,IAAI,CAACoJ,SAAS,CAAG,IAAIC,KAAKpK,EAAKmK,SAAS,EACxC,IAAI,CAACE,SAAS,CAAG,IAAID,KAAKpK,EAAKqK,SAAS,CAC1C,CACF,wFCtBe,SAASC,IACtB,MACE,GAAAhJ,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,kDACb,GAAAN,EAAAC,GAAA,EAAC8B,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,wsBCNe,SAASiH,IACtB,MACE,GAAAhJ,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,kDACb,GAAAN,EAAAC,GAAA,EAAC8B,EAAAA,CAAMA,CAAAA,CAAAA,IAGb,gCCNe,SAASkH,EAAe,CACrCpQ,SAAAA,CAAQ,CAGT,EACC,OAAOA,CACT,8HCNe,SAASmQ,IACtB,MACE,GAAAhJ,EAAAC,GAAA,EAACI,MAAAA,CAAIC,UAAU,kDACb,GAAAN,EAAAC,GAAA,EAAC8B,EAAAA,CAAMA,CAAAA,CAAAA,IAGb", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page.tsx?ba18", "webpack://_N_E/|ssr?76bc", "webpack://_N_E/?d58e", "webpack://_N_E/?054d", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/layout.tsx", "webpack://_N_E/./components/ui/accordion.tsx", "webpack://_N_E/./components/ui/alert.tsx", "webpack://_N_E/./components/ui/badge.tsx", "webpack://_N_E/./components/ui/table.tsx", "webpack://_N_E/./data/admin/toggleActivity.ts", "webpack://_N_E/./data/settings/kyc-settings.ts", "webpack://_N_E/./types/kyc.ts", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/[userId]/[merchantId]/loading.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/layout.tsx", "webpack://_N_E/./app/(protected)/@admin/merchants/loading.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "\"TURBOPACK { transition: next-ssr }\";\nimport { AppPageRouteModule } from \"next/dist/server/future/route-modules/app-page/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(protected)',\n        {\n        admin: [\n        'children',\n        {\n        children: [\n        'merchants',\n        {\n        children: [\n        '[userId]',\n        {\n        children: [\n        '[merchantId]',\n        {\n        children: [\n        'kyc',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\kyc\\\\page.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\kyc\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\kyc\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\kyc\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\loading.tsx\"],\n        \n      }\n      ],\nchildren: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nagent: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\ncustomer: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ],\nmerchant: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/parallel-route-default.js\"), \"next/dist/client/components/parallel-route-default.js\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [() => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\kyc\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const originalPathname = \"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page\";\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page\",\n        pathname: \"/merchants/[userId]/[merchantId]/kyc\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Fkyc%2Fpage&page=%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Fkyc%2Fpage&pagePath=private-next-app-dir%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Fkyc%2Fpage.tsx&appDir=C%3A%5CUsers%5CDELL%5CDesktop%5CIDEES%20SQQS%5CPAYSNAPo%5CMain%5Cfrontend%5Capp&appPaths=%2F(protected)%2F%40admin%2Fmerchants%2F%5BuserId%5D%2F%5BmerchantId%5D%2Fkyc%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"webpack\":null,\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.mjs\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"analyticsId\":\"\",\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":200,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"inline\",\"remotePatterns\":[{\"protocol\":\"http\",\"hostname\":\"localhost\"},{\"protocol\":\"https\",\"hostname\":\"**\"}],\"unoptimized\":false},\"devIndicators\":{\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"optimizeFonts\":true,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"httpAgentOptions\":{\"keepAlive\":true},\"outputFileTracing\":true,\"staticPageGenerationTimeout\":60,\"swcMinify\":true,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"experimental\":{\"multiZoneDraftMode\":false,\"prerenderEarlyExit\":false,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":3,\"memoryBasedWorkersCount\":false,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"outputFileTracingRoot\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\",\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"adjustFontFallbacks\":false,\"adjustFontFallbacksWithSizeAdjust\":false,\"typedRoutes\":false,\"instrumentationHook\":false,\"bundlePagesExternals\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"missingSuspenseWithCSRBailout\":true,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":30,\"static\":300},\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"configFile\":\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\next.config.mjs\",\"configFileName\":\"next.config.mjs\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest,\n            pageName: \"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page\"\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/(protected)/@admin/merchants/[userId]/[merchantId]/kyc/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\kyc\\\\page.tsx\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\IDEES SQQS\\\\PAYSNAPo\\\\Main\\\\frontend\\\\app\\\\(protected)\\\\@admin\\\\merchants\\\\[userId]\\\\[merchantId]\\\\layout.tsx\");\n", "\"use client\";\r\n\r\nimport { Case } from \"@/components/common/Case\";\r\nimport { Loader } from \"@/components/common/Loader\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { kycAcceptDecline } from \"@/data/settings/kyc-settings\";\r\nimport { useSWR } from \"@/hooks/useSWR\";\r\nimport { KYC } from \"@/types/kyc\";\r\nimport {\r\n  CloseCircle,\r\n  DocumentDownload,\r\n  Eye,\r\n  Shield,\r\n  ShieldCross,\r\n  ShieldSearch,\r\n  ShieldTick,\r\n  TickCircle,\r\n} from \"iconsax-react\";\r\nimport Image from \"next/image\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport default function KFCSettings() {\r\n  const params = useParams(); // get customerId from params\r\n  // fetch user by id\r\n  const { data, isLoading } = useSWR(`/admin/merchants/${params.merchantId}`);\r\n\r\n  const { t } = useTranslation();\r\n\r\n  // toggling kyc\r\n  const handleKYC = (id: string | number, type: \"accept\" | \"decline\") => {\r\n    toast.promise(kycAcceptDecline(id, type), {\r\n      loading: t(\"Loading...\"),\r\n      success: (res) => {\r\n        if (!res.status) throw new Error(res.message);\r\n        return res.message;\r\n      },\r\n      error: (err) => err.message,\r\n    });\r\n  };\r\n\r\n  const kyc = data?.data?.user?.kyc ? new KYC(data.data.user.kyc) : null;\r\n\r\n  return (\r\n    <Accordion\r\n      type=\"multiple\"\r\n      defaultValue={[\"KYC_STATUS\", \"DOCUMENT_INFORMATION\"]}\r\n    >\r\n      <div className=\"flex flex-col gap-4 p-4\">\r\n        <div className=\"rounded-xl border border-border bg-background\">\r\n          <AccordionItem value=\"KYC_STATUS\" className=\"border-none px-4 py-0\">\r\n            <AccordionTrigger className=\"py-6 hover:no-underline\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <p className=\"text-base font-medium leading-[22px]\">\r\n                  {t(\"KYC Status\")}\r\n                </p>\r\n\r\n                {/* If KYC data is not found, display 'Awaiting Status' */}\r\n                <Case condition={!kyc}>\r\n                  <Badge className=\"h-5 bg-foreground text-[10px] text-background\">\r\n                    {t(\"Awaiting Status\")}\r\n                  </Badge>\r\n                </Case>\r\n\r\n                {/* If KYC status is pending, display 'Pending Status' */}\r\n                <Case condition={kyc?.status === \"pending\"}>\r\n                  <Badge className=\"h-5 bg-primary text-[10px] text-primary-foreground\">\r\n                    {t(\"Pending\")}\r\n                  </Badge>\r\n                </Case>\r\n\r\n                <Case condition={kyc?.status === \"verified\"}>\r\n                  <Badge className=\"h-5 bg-spacial-green text-[10px] text-spacial-green-foreground\">\r\n                    {t(\"Verified\")}\r\n                  </Badge>\r\n                </Case>\r\n\r\n                <Case condition={kyc?.status === \"failed\"}>\r\n                  <Badge className=\"h-5 bg-danger text-[10px] text-destructive-foreground\">\r\n                    {t(\"Rejected\")}\r\n                  </Badge>\r\n                </Case>\r\n              </div>\r\n            </AccordionTrigger>\r\n            <AccordionContent className=\"flex flex-col gap-6 border-t border-divider px-1 pt-4\">\r\n              {/* Awaiting alert type */}\r\n              <Case condition={!kyc}>\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-foreground\">\r\n                  <Shield size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"User have not submitted documents yet\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n\r\n              <Case condition={kyc?.status === \"pending\"}>\r\n                {/* Pending alert type */}\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-primary\">\r\n                  <ShieldSearch size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"Pending verification\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n\r\n              <Case condition={kyc?.status === \"verified\"}>\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-spacial-green\">\r\n                  <ShieldTick size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"Your account is verified\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"To ensure maximum security, two-factor authentication is always on by default. You will have to verify your email each time you log in.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n\r\n              <Case condition={kyc?.status === \"failed\"}>\r\n                <Alert className=\"border-none bg-transparent shadow-default [&>svg]:text-danger\">\r\n                  <ShieldCross size=\"32\" variant=\"Bulk\" />\r\n                  <AlertTitle className=\"ml-2.5 text-sm font-semibold leading-5\">\r\n                    {t(\"KYC Document Rejected\")}\r\n                  </AlertTitle>\r\n                  <AlertDescription className=\"ml-2.5 text-sm font-normal\">\r\n                    {t(\r\n                      \"The submitted KYC document has been rejected. Please review the document for discrepancies or invalid details and request the user to submit accurate information for verification.\",\r\n                    )}\r\n                  </AlertDescription>\r\n                </Alert>\r\n              </Case>\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </div>\r\n\r\n        <div className=\"rounded-xl border border-border bg-background\">\r\n          <AccordionItem\r\n            value=\"DOCUMENT_INFORMATION\"\r\n            className=\"border-none px-4 py-0\"\r\n          >\r\n            <AccordionTrigger className=\"py-6 hover:no-underline\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <p className=\"text-base font-medium leading-[22px]\">\r\n                  {t(\"Documents\")}\r\n                </p>\r\n              </div>\r\n            </AccordionTrigger>\r\n            <AccordionContent className=\"flex flex-col gap-6 border-t border-divider px-1 pt-4\">\r\n              {!kyc ? (\r\n                <div className=\"py-4\">\r\n                  <p className=\"text-secondary-text\">\r\n                    {t(\"KYC Documents not submitted yet\")}\r\n                  </p>\r\n                </div>\r\n              ) : (\r\n                <div>\r\n                  <div className=\"max-w-[900px]\">\r\n                    <Table className=\"table-fixed\">\r\n                      <TableHeader className=\"[&_tr]:border-b-0\">\r\n                        <TableRow>\r\n                          <TableHead>{t(\"KYC\")}</TableHead>\r\n                          <TableHead>{t(\"Menu\")}</TableHead>\r\n                        </TableRow>\r\n                      </TableHeader>\r\n\r\n                      <TableBody>\r\n                        {isLoading ? (\r\n                          <TableRow>\r\n                            <TableCell colSpan={2}>\r\n                              <Loader />\r\n                            </TableCell>\r\n                          </TableRow>\r\n                        ) : (\r\n                          <>\r\n                            <TableRow className=\"odd:bg-accent\">\r\n                              <TableCell>{t(\"Document type\")}</TableCell>\r\n                              <TableCell>\r\n                                <div className=\"flex items-center gap-1 sm:gap-10\">\r\n                                  <span className=\"hidden font-semibold sm:block\">\r\n                                    {kyc?.documentType as string}\r\n                                  </span>\r\n                                </div>\r\n                              </TableCell>\r\n                            </TableRow>\r\n                            <KYCTableRow\r\n                              title={t(\"Front image\")}\r\n                              preview={kyc?.front as string}\r\n                            />\r\n\r\n                            <KYCTableRow\r\n                              title={t(\"Back image\")}\r\n                              preview={kyc?.back as string}\r\n                            />\r\n\r\n                            <KYCTableRow\r\n                              title={t(\"Selfie\")}\r\n                              preview={kyc?.selfie as string}\r\n                            />\r\n                          </>\r\n                        )}\r\n                      </TableBody>\r\n                    </Table>\r\n                  </div>\r\n\r\n                  <Case condition={kyc.status?.toLowerCase() === \"pending\"}>\r\n                    <div className=\"flex flex-wrap items-center gap-2.5 sm:gap-4\">\r\n                      <Button\r\n                        type=\"button\"\r\n                        onClick={() => handleKYC(kyc.id, \"accept\")}\r\n                        className=\"bg-[#0B6A0B] text-white hover:bg-[#208c20]\"\r\n                      >\r\n                        <TickCircle />\r\n                        {t(\"Approve\")}\r\n                      </Button>\r\n                      <Button\r\n                        type=\"button\"\r\n                        onClick={() => handleKYC(kyc.id, \"decline\")}\r\n                        className=\"bg-[#D13438] text-white hover:bg-[#c32d32]\"\r\n                      >\r\n                        <CloseCircle />\r\n                        {t(\"Reject\")}\r\n                      </Button>\r\n                    </div>\r\n                  </Case>\r\n                </div>\r\n              )}\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </div>\r\n      </div>\r\n    </Accordion>\r\n  );\r\n}\r\n\r\nfunction KYCTableRow({ title, preview }: { title: string; preview: string }) {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <TableRow className=\"odd:bg-accent\">\r\n      <TableCell>{title}</TableCell>\r\n      <TableCell>\r\n        <div className=\"flex items-center gap-1 sm:gap-10\">\r\n          <Dialog>\r\n            <DialogTrigger asChild>\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                className=\"bg-background hover:bg-muted\"\r\n              >\r\n                <Eye />\r\n                <span className=\"hidden sm:block\">{t(\"View\")}</span>\r\n              </Button>\r\n            </DialogTrigger>\r\n\r\n            <DialogContent className=\"max-w-7xl\">\r\n              <DialogHeader>\r\n                <DialogTitle> {title} </DialogTitle>\r\n                <DialogDescription className=\"hidden\" aria-hidden />\r\n              </DialogHeader>\r\n\r\n              <div className=\"relative mx-auto aspect-square w-full max-w-xl\">\r\n                {preview ? (\r\n                  <Image\r\n                    src={preview}\r\n                    alt={title}\r\n                    fill\r\n                    sizes=\"500px\"\r\n                    style={{ width: \"100%\", height: \"100%\" }}\r\n                    quality=\"90\"\r\n                    loading=\"lazy\"\r\n                    placeholder=\"blur\"\r\n                    className=\"object-contain\"\r\n                    blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mN8Vw8AAmEBb87E6jIAAAAASUVORK5CYII=\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"flex h-full w-full items-center justify-center rounded-md bg-accent\">\r\n                    <span className=\"font-semibold opacity-70\">\r\n                      {t(\"No preview\")}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </DialogContent>\r\n          </Dialog>\r\n\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            className=\"bg-background hover:bg-muted\"\r\n            asChild\r\n          >\r\n            <a href={preview} download>\r\n              <DocumentDownload />\r\n              <span className=\"hidden sm:inline-block\">{t(\"Download\")}</span>\r\n            </a>\r\n          </Button>\r\n        </div>\r\n      </TableCell>\r\n    </TableRow>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport { SecondaryNav } from \"@/components/common/layout/SecondaryNav\";\r\nimport Switch from \"@/components/ui/switch\";\r\nimport { toggleActivity } from \"@/data/admin/toggleActivity\";\r\nimport {\r\n  ArrowLeft2,\r\n  Candle2,\r\n  Clock,\r\n  ShieldSecurity,\r\n  Sms,\r\n  UserEdit,\r\n} from \"iconsax-react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  useParams,\r\n  usePathname,\r\n  useRouter,\r\n  useSearchParams,\r\n} from \"next/navigation\";\r\nimport React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { toast } from \"sonner\";\r\nimport { mutate } from \"swr\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport default function CustomerDetailsLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const params = useParams();\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const { t } = useTranslation();\r\n\r\n  const tabs = [\r\n    {\r\n      title: t(\"Account Details\"),\r\n      icon: <UserEdit size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}?${searchParams.toString()}`,\r\n      id: \"__DEFAULT__\",\r\n    },\r\n    {\r\n      title: t(\"Transactions\"),\r\n      icon: <Clock size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/transactions?${searchParams.toString()}`,\r\n      id: \"transactions\",\r\n    },\r\n    {\r\n      title: t(\"KYC\"),\r\n      icon: <ShieldSecurity size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/kyc?${searchParams.toString()}`,\r\n      id: \"kyc\",\r\n    },\r\n    {\r\n      title: t(\"Fees\"),\r\n      icon: <ShieldSecurity size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/fees?${searchParams.toString()}`,\r\n      id: \"fees\",\r\n    },\r\n    {\r\n      title: t(\"Permissions\"),\r\n      icon: <Candle2 size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/permissions?${searchParams.toString()}`,\r\n      id: \"permissions\",\r\n    },\r\n    {\r\n      title: t(\"Send Email\"),\r\n      icon: <Sms size=\"24\" variant=\"Bulk\" />,\r\n      href: `/merchants/${params?.userId}/${params?.merchantId}/send-email?${searchParams.toString()}`,\r\n      id: \"send-email\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <div className=\"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4\">\r\n        <div className=\"mb-4 flex flex-wrap items-center justify-between gap-2\">\r\n          <ul className=\"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center\">\r\n            <li>\r\n              <Link\r\n                href=\"/merchants/list\"\r\n                className=\"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4\"\r\n              >\r\n                <ArrowLeft2 />\r\n                {t(\"Back\")}\r\n              </Link>\r\n            </li>\r\n            <li className=\"line-clamp-1 whitespace-nowrap\">\r\n              / {searchParams.get(\"name\")}\r\n            </li>\r\n            <li className=\"line-clamp-1 whitespace-nowrap\">\r\n              / {t(\"Merchant\")} #{params.merchantId}\r\n            </li>\r\n          </ul>\r\n          <div className=\"ml-auto inline-flex items-center gap-2 text-sm sm:text-base\">\r\n            <span>{t(\"Active\")}</span>\r\n            <Switch\r\n              defaultChecked={searchParams.get(\"active\") === \"1\"}\r\n              className=\"data-[state=unchecked]:bg-muted\"\r\n              onCheckedChange={(checked) => {\r\n                toast.promise(toggleActivity(params.userId as string), {\r\n                  loading: t(\"Loading...\"),\r\n                  success: (res) => {\r\n                    if (!res.status) throw new Error(res.message);\r\n                    const sp = new URLSearchParams(searchParams);\r\n                    mutate(`/admin/merchants/${params.merchantId}`);\r\n                    sp.set(\"active\", checked ? \"1\" : \"0\");\r\n                    router.push(`${pathname}?${sp.toString()}`);\r\n                    return res.message;\r\n                  },\r\n                  error: (err) => err.message,\r\n                });\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <SecondaryNav tabs={tabs} />\r\n      </div>\r\n\r\n      {children}\r\n    </>\r\n  );\r\n}\r\n", "\"use client\";\r\n\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\nimport { ArrowDown2 } from \"iconsax-react\";\r\n\r\nconst Accordion = AccordionPrimitive.Root;\r\n\r\nconst AccordionItem = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\r\n>(({ className, ...props }, ref) => (\r\n  <AccordionPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\"border-b\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAccordionItem.displayName = \"AccordionItem\";\r\n\r\nconst AccordionTrigger = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Header className=\"flex\">\r\n    <AccordionPrimitive.Trigger\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ArrowDown2 className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\r\n    </AccordionPrimitive.Trigger>\r\n  </AccordionPrimitive.Header>\r\n));\r\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;\r\n\r\nconst AccordionContent = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Content\r\n    ref={ref}\r\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\r\n    {...props}\r\n  >\r\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\r\n  </AccordionPrimitive.Content>\r\n));\r\n\r\nAccordionContent.displayName = AccordionPrimitive.Content.displayName;\r\n\r\nexport { Accordion, AccordionContent, AccordionItem, AccordionTrigger };\r\n", "import { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n));\r\nAlert.displayName = \"Alert\";\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAlertTitle.displayName = \"AlertTitle\";\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAlertDescription.displayName = \"AlertDescription\";\r\n\r\nexport { Alert, AlertDescription, AlertTitle };\r\n", "import { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-1.5 py-[3px] text-[10px] font-medium leading-4 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border-transparent bg-primary text-primary-foreground\",\r\n        secondary: \"border-transparent bg-muted text-secondary-foreground\",\r\n        success: \"border-transparent bg-success text-success-foreground\",\r\n        important: \"border-transparent bg-important text-important-foreground\",\r\n        error: \"border-transparent bg-destructive text-destructive-foreground\",\r\n        warning: \"border-transparent bg-warning text-warning-foreground\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n", "import * as React from \"react\";\r\n\r\nimport cn from \"@/lib/utils\";\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = \"Table\";\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"\", className)} {...props} />\r\n));\r\nTableHeader.displayName = \"TableHeader\";\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = \"TableBody\";\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = \"TableFooter\";\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = \"TableRow\";\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = \"TableHead\";\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = \"TableCell\";\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = \"TableCaption\";\r\n\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n};\r\n", "import axios from \"@/lib/axios\";\r\nimport { ErrorResponseGenerator, ResponseGenerator } from \"@/data/response\";\r\nimport type { ReturnType } from \"@/types/return-type\";\r\n\r\nexport async function toggleActivity(\r\n  customerId: string | number,\r\n): Promise<ReturnType> {\r\n  try {\r\n    const response = await axios.put(\r\n      `/admin/users/toggle-active/${customerId}`,\r\n      {},\r\n    );\r\n\r\n    return ResponseGenerator(response);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n}\r\n", "import axios from \"@/lib/axios\";\r\nimport { ErrorResponseGenerator, ResponseGenerator } from \"@/data/response\";\r\n\r\nexport const kycAcceptDecline = async (\r\n  id: string | number,\r\n  type: \"accept\" | \"decline\",\r\n) => {\r\n  try {\r\n    const res = await axios.put(`/admin/kycs/${type}/${id}`, {});\r\n    return ResponseGenerator(res);\r\n  } catch (error) {\r\n    return ErrorResponseGenerator(error);\r\n  }\r\n};\r\n", "import { imageURL } from \"@/lib/utils\";\r\n\r\nexport class KYC {\r\n  id: number;\r\n  userId: number;\r\n  documentType: \"NID\" | \"PASSPORT\" | \"DRIVING\";\r\n  selfie?: string | undefined;\r\n  front: string;\r\n  back?: string | undefined;\r\n  status: string;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n\r\n  constructor(data: Record<string, any>) {\r\n    this.id = data?.id;\r\n    this.userId = data?.userId;\r\n    this.documentType = data?.documentType?.toUpperCase();\r\n    this.selfie = imageURL(data?.selfie);\r\n    this.front = imageURL(data?.front);\r\n    this.back = imageURL(data?.back);\r\n    this.status = data?.status;\r\n    this.createdAt = new Date(data.createdAt);\r\n    this.updatedAt = new Date(data.updatedAt);\r\n  }\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n", "import React from \"react\";\r\n\r\nexport default function CustomerLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return children;\r\n}\r\n", "import { Loader } from \"@/components/common/Loader\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex items-center justify-center py-10\">\r\n      <Loader />\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["module", "exports", "require", "_self___RSC_MANIFEST", "tree", "children", "admin", "page", "Promise", "resolve", "then", "__webpack_require__", "bind", "defaultPage", "agent", "customer", "merchant", "pages", "originalPathname", "__next_app__", "loadChunk", "routeModule", "module_compiled", "AppPageRouteModule", "definition", "kind", "route_kind", "x", "APP_PAGE", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "self", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "encryption_utils", "Mo", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "action_utils", "w", "pageName", "page_stringifiedConfig_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_pagesType_app_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRm1lcmNoYW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1Qm1lcmNoYW50SWQlNUQlMkZreWMlMkZwYWdlJnBhZ2U9JTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRm1lcmNoYW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1Qm1lcmNoYW50SWQlNUQlMkZreWMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGKHByb3RlY3RlZCklMkYlNDBhZG1pbiUyRm1lcmNoYW50cyUyRiU1QnVzZXJJZCU1RCUyRiU1Qm1lcmNoYW50SWQlNUQlMkZreWMlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDREVMTCU1Q0Rlc2t0b3AlNUNJREVFUyUyMFNRUVMlNUNQQVlTTkFQbyU1Q01haW4lNUNmcm9udGVuZCU1Q2FwcCZhcHBQYXRocz0lMkYocHJvdGVjdGVkKSUyRiU0MGFkbWluJTJGbWVyY2hhbnRzJTJGJTVCdXNlcklkJTVEJTJGJTVCbWVyY2hhbnRJZCU1RCUyRmt5YyUyRnBhZ2UmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0Qh_sriEnabled_false_middlewareConfig_e30_render", "render", "d", "pagesType", "page_types", "s", "APP", "dev", "appMod", "pageMod", "page_next_edge_ssr_entry_namespaceObject", "errorMod", "error500Mod", "Document", "renderToHTML", "app_render", "f", "serverActions", "subresourceIntegrityManifest", "config", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "adapter", "C", "IncrementalCache", "incremental_cache", "k", "handler", "KFCSettings", "params", "useParams", "data", "isLoading", "useSWR", "merchantId", "t", "useTranslation", "handleKYC", "id", "type", "toast", "promise", "kycAcceptDecline", "loading", "success", "res", "status", "message", "error", "err", "kyc", "user", "KYC", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "Accordion", "defaultValue", "jsxs", "div", "className", "AccordionItem", "value", "AccordionTrigger", "p", "Case", "condition", "Badge", "Accordi<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shield", "size", "variant", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDescription", "ShieldSearch", "ShieldTick", "ShieldCross", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "colSpan", "Loader", "Fragment", "span", "documentType", "KYCTableRow", "title", "preview", "front", "back", "selfie", "toLowerCase", "<PERSON><PERSON>", "onClick", "TickCircle", "CloseCircle", "Dialog", "DialogTrigger", "<PERSON><PERSON><PERSON><PERSON>", "Eye", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "aria-hidden", "Image", "src", "alt", "fill", "sizes", "style", "width", "height", "quality", "placeholder", "blurDataURL", "a", "href", "download", "DocumentDownload", "runtime", "CustomerDetailsLayout", "searchParams", "useSearchParams", "router", "useRouter", "usePathname", "tabs", "icon", "UserEdit", "userId", "toString", "Clock", "ShieldSecurity", "Candle2", "Sms", "ul", "li", "Link", "ArrowLeft2", "get", "Switch", "defaultChecked", "onCheckedChange", "toggleActivity", "sp", "URLSearchParams", "mutate", "set", "checked", "push", "SecondaryNav", "AccordionPrimitive", "React", "props", "ref", "cn", "displayName", "ArrowDown2", "alertVariants", "cva", "variants", "default", "destructive", "defaultVariants", "role", "h5", "badgeVariants", "secondary", "important", "warning", "outline", "table", "thead", "tbody", "TableFooter", "tfoot", "tr", "th", "td", "TableCaption", "caption", "customerId", "response", "axios", "put", "ResponseGenerator", "ErrorResponseGenerator", "constructor", "toUpperCase", "imageURL", "createdAt", "Date", "updatedAt", "Loading", "CustomerLayout"], "sourceRoot": ""}