(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[15277],{38312:function(e,s,r){Promise.resolve().then(r.bind(r,69395))},69395:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return y}});var t=r(57437),n=r(15054),i=r(41709),a=r(56353),l=r(65613),o=r(62869),d=r(15681),c=r(6512),u=r(31092),f=r(13590),x=r(22291),p=r(19571),m=r(27648),g=r(99376),b=r(2265),h=r(29501),w=r(43949),v=r(14438),j=r(31229);let N=j.z.object({password:j.z.string({required_error:"Password is required."}).min(1,"Password is required."),confirmPassword:j.z.string({required_error:"Password is required."}).min(1,"Password is required.")});function y(){let[e,s]=b.useState(!1),[,r]=(0,b.useTransition)(),j=(0,g.useSearchParams)(),{t:y}=(0,w.$G)(),P=(0,h.cI)({resolver:(0,f.F)(N),defaultValues:{password:"",confirmPassword:""}});return(0,t.jsxs)("div",{className:"container mt-10 w-full max-w-[716px] px-4 py-6",children:[(0,t.jsx)(n.h,{title:y("Reset your password"),subTitle:y("Congrats! Your account has been recovered")}),(0,t.jsx)("div",{className:"my-6 flex h-[5px] items-center",children:(0,t.jsx)(c.Z,{className:"bg-divider"})}),(0,t.jsx)(i.J,{condition:!e,children:(0,t.jsx)(d.l0,{...P,children:(0,t.jsxs)("form",{onSubmit:P.handleSubmit(e=>{var t;let n=null!==(t=j.get("token"))&&void 0!==t?t:"";r(async()=>{let r=await (0,u.c0)({password:e.password,passwordConfirmation:e.confirmPassword,token:n});r&&r.status?(v.toast.success(r.message),s(r.status)):v.toast.error(y(r.message))})}),className:"flex flex-col gap-6",children:[(0,t.jsx)(d.Wi,{control:P.control,name:"password",render:e=>{let{field:s}=e;return(0,t.jsxs)(d.xJ,{children:[(0,t.jsx)(d.lX,{children:y("Create new password")}),(0,t.jsx)(d.NI,{children:(0,t.jsx)(a.W,{placeholder:y("Create a strong password"),...s})}),(0,t.jsx)(d.zG,{})]})}}),(0,t.jsx)(d.Wi,{control:P.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,t.jsxs)(d.xJ,{children:[(0,t.jsx)(d.lX,{required:!0,children:y("Confirm password")}),(0,t.jsx)(d.NI,{children:(0,t.jsx)(a.W,{placeholder:y("Enter the password again"),...s})}),(0,t.jsx)(d.zG,{})]})}}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(o.z,{className:"w-[286px] px-4 py-2 text-base font-medium leading-[22px]",children:[y("Update password"),(0,t.jsx)(x.Z,{size:19})]})})]})})}),(0,t.jsxs)(i.J,{condition:e,children:[(0,t.jsxs)(l.bZ,{className:"border-0 bg-background p-3 shadow-default",children:[(0,t.jsx)(p.Z,{size:"16",color:"#107C10",variant:"Bold",className:"-mt-0.5"}),(0,t.jsx)(l.Cd,{className:"text-sm font-semibold leading-5 text-foreground",children:y("Password reset successful")}),(0,t.jsx)(l.X,{className:"py-1.5 text-sm font-normal leading-5",children:y("We’ve reset your password. Please sign in again with your new password to continue.")})]}),(0,t.jsx)("div",{className:"mt-6 flex justify-end",children:(0,t.jsx)(o.z,{asChild:!0,className:"w-[286px] px-4 py-2 text-base font-medium leading-[22px]",children:(0,t.jsxs)(m.default,{href:"/signin",prefetch:!1,children:[y("Sign in"),(0,t.jsx)(x.Z,{size:19})]})})})]})]})}},56353:function(e,s,r){"use strict";r.d(s,{W:function(){return c}});var t=r(57437),n=r(2265),i=r(62869),a=r(95186),l=r(94508),o=r(93824),d=r(32706);let c=n.forwardRef((e,s)=>{let{className:r,type:c,...u}=e,[f,x]=n.useState(!1);return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(a.I,{type:f?"text":"password",className:(0,l.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...u}),(0,t.jsx)(i.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),x(e=>!e)},children:f?(0,t.jsx)(o.Z,{}):(0,t.jsx)(d.Z,{})})]})});c.displayName="PasswordInput"},65613:function(e,s,r){"use strict";r.d(s,{Cd:function(){return d},X:function(){return c},bZ:function(){return o}});var t=r(57437),n=r(90535),i=r(2265),a=r(94508);let l=(0,n.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=i.forwardRef((e,s)=>{let{className:r,variant:n,...i}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,a.ZP)(l({variant:n}),r),...i})});o.displayName="Alert";let d=i.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)("h5",{ref:s,className:(0,a.ZP)("mb-1 font-medium leading-none tracking-tight",r),...n})});d.displayName="AlertTitle";let c=i.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,a.ZP)("text-sm [&_p]:leading-relaxed",r),...n})});c.displayName="AlertDescription"},95186:function(e,s,r){"use strict";r.d(s,{I:function(){return a}});var t=r(57437),n=r(2265),i=r(94508);let a=n.forwardRef((e,s)=>{let{className:r,type:n,...a}=e;return(0,t.jsx)("input",{type:n,className:(0,i.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:s,...a})});a.displayName="Input"}},function(e){e.O(0,[14438,31304,83464,27648,38658,82323,70961,92971,95030,1744],function(){return e(e.s=38312)}),_N_E=e.O()}]);