(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8840],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},13271:(e,s,t)=>{"use strict";t.r(s),t.d(s,{ComponentMod:()=>I,default:()=>D});var a,r={};t.r(r),t.d(r,{AppRouter:()=>p.WY,ClientPageRoot:()=>p.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>p.yO,NotFoundBoundary:()=>p.O4,Postpone:()=>p.hQ,RenderFromTemplateContext:()=>p.b5,__next_app__:()=>g,actionAsyncStorage:()=>p.Wz,createDynamicallyTrackedSearchParams:()=>p.rL,createUntrackedSearchParams:()=>p.S5,decodeAction:()=>p.Hs,decodeFormState:()=>p.dH,decodeReply:()=>p.kf,originalPathname:()=>x,pages:()=>u,patchFetch:()=>p.XH,preconnect:()=>p.$P,preloadFont:()=>p.C5,preloadStyle:()=>p.oH,renderToReadableStream:()=>p.aW,requestAsyncStorage:()=>p.Fg,routeModule:()=>j,serverHooks:()=>p.GP,staticGenerationAsyncStorage:()=>p.AT,taintObjectReference:()=>p.nr,tree:()=>h}),t(67206);var n=t(79319),i=t(20518),o=t(61902),l=t(62042),d=t(44630),c=t(44828),m=t(65505),p=t(13839);let h=["",{children:["(protected)",{admin:["children",{children:["settings",{children:["withdraw-methods",{children:["[withdrawId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29780)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\withdraw-methods\\[withdrawId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,84302)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\withdraw-methods\\[withdrawId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,90056)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\withdraw-methods\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,15171)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,5897)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(t.bind(t,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],u=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\settings\\withdraw-methods\\[withdrawId]\\page.tsx"],x="/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page",g={require:t,loadChunk:()=>Promise.resolve()},j=new d.AppPageRouteModule({definition:{kind:c.x.APP_PAGE,page:"/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page",pathname:"/settings/withdraw-methods/[withdrawId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}});var f=t(69094),b=t(5787),N=t(90527);let S=e=>e?JSON.parse(e):void 0,y=self.__BUILD_MANIFEST,w=S(self.__REACT_LOADABLE_MANIFEST),v=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page"],C=S(self.__RSC_SERVER_MANIFEST),P=S(self.__NEXT_FONT_MANIFEST),E=S(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];v&&C&&(0,b.Mo)({clientReferenceManifest:v,serverActionsManifest:C,serverModuleMap:(0,N.w)({serverActionsManifest:C,pageName:"/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page"})});let A=(0,i.d)({pagesType:f.s.APP,dev:!1,page:"/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page",appMod:null,pageMod:r,errorMod:null,error500Mod:null,Document:null,buildManifest:y,renderToHTML:l.f,reactLoadableManifest:w,clientReferenceManifest:v,serverActionsManifest:C,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:P,incrementalCacheHandler:null,interceptionRouteRewrites:E}),I=r;function D(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:A})}},85976:(e,s,t)=>{Promise.resolve().then(t.bind(t,1589))},1589:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ee,runtime:()=>K});var a=t(60926),r=t(29411),n=t(59571),i=t(15185),o=t(36162),l=t(74988),d=t(92207),c=t(9885),m=t(1181),p=t(65091),h=t(9172),u=t(64947),x=t(39228),g=t(32167),j=t(32898);t(87198);var f=t(4825);t(7643),t(98019),t(68870),t(92773);var b=t(73806),N=t(7602),S=t(5670),y=t(34451),w=t(18662),v=t(66817),C=t(83968),P=t(30417),E=t(59141);let A=(0,E.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),I=(0,E.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);function D({form:e,logoImage:s}){let{currencies:t,isLoading:r}=(0,f.j)(),{t:n}=(0,x.$G)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.Wi,{control:e.control,name:"uploadLogo",render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[(0,a.jsx)(y.lX,{children:n("Method logo")}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(N.S,{id:"uploadLogo",defaultValue:(0,p.qR)(s),onChange:s=>e.onChange(s),className:"flex aspect-square h-[160px] w-[160px] items-center justify-center border-dashed border-primary bg-transparent",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2.5",children:[(0,a.jsx)(S.X,{}),(0,a.jsx)("p",{className:"text-sm font-normal text-primary",children:n("Upload logo")})]})})})]})}),(0,a.jsx)(y.Wi,{control:e.control,name:"name",render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[(0,a.jsx)(y.lX,{children:n("Name")}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(w.I,{placeholder:n("Method name"),type:"text",className:"disabled:bg-input",...e})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(y.Wi,{control:e.control,name:"countryCode",render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"col-span-12",children:[(0,a.jsx)(y.lX,{children:n("Country")}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(b.g,{allCountry:!0,onSelectChange:s=>e.onChange(s.code.cca2)})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(y.Wi,{name:"currencyCode",control:e.control,render:({field:e})=>(0,a.jsxs)(y.xJ,{children:[(0,a.jsx)(y.lX,{children:n("Currency")}),(0,a.jsx)(y.NI,{children:(0,a.jsxs)(C.Ph,{defaultValue:e.value,onValueChange:e.onChange,children:[(0,a.jsx)(C.i4,{className:"text-base disabled:bg-input",children:(0,a.jsx)(C.ki,{placeholder:n("Select currency")})}),(0,a.jsx)(C.Bw,{children:!r&&t?.map(e=>a.jsx(C.Ql,{value:e.code,children:e.code},e.code))})]})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(y.Wi,{name:"active",control:e.control,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,a.jsx)(v.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:n("Active")}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(P.Z,{defaultChecked:!!e.value,onCheckedChange:e.onChange,className:"disabled:opacity-100"})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(y.Wi,{name:"recommended",control:e.control,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,a.jsx)(v.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:n("Recommended")}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(P.Z,{defaultChecked:!!e.value,onCheckedChange:e.onChange,className:"disabled:opacity-100"})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(l.Z,{}),(0,a.jsx)(y.Wi,{name:"minAmount",control:e.control,render:({field:s})=>(0,a.jsxs)(y.xJ,{className:"mt-2",children:[(0,a.jsxs)(y.lX,{children:[n("Minimum amount"),e.watch("currencyCode")&&` (${e.watch("currencyCode")})`]}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(w.I,{type:"text",placeholder:"300",className:"disabled:bg-input",...s})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(y.Wi,{name:"maxAmount",control:e.control,render:({field:s})=>(0,a.jsxs)(y.xJ,{className:"mt-2",children:[(0,a.jsxs)(y.lX,{children:[n("Maximum amount"),e.watch("currencyCode")&&` (${e.watch("currencyCode")})`]}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(w.I,{type:"text",placeholder:"3200000",className:"disabled:bg-input",...s})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(y.Wi,{name:"fixedCharge",control:e.control,render:({field:s})=>(0,a.jsxs)(y.xJ,{className:"mt-2",children:[(0,a.jsxs)(y.lX,{children:[n("Fixed charge"),e.watch("currencyCode")&&` (${e.watch("currencyCode")})`]}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(w.I,{type:"text",placeholder:"300",className:"disabled:bg-input",...s})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(y.Wi,{name:"percentageCharge",control:e.control,render:({field:e})=>(0,a.jsxs)(y.xJ,{className:"mt-2",children:[(0,a.jsx)(y.lX,{children:n("Percentage charge (%)")}),(0,a.jsx)(y.NI,{children:(0,a.jsx)(w.I,{type:"text",placeholder:"300",className:"disabled:bg-input",...e})}),(0,a.jsx)(y.zG,{})]})}),(0,a.jsx)(l.Z,{}),(0,a.jsxs)("div",{className:"withdraw-method-repeater",children:[(0,a.jsx)("h3",{className:"mb-3",children:n("Method Fields")}),(0,a.jsxs)("div",{className:"controls mb-3 flex gap-2",children:[(0,a.jsxs)(o.z,{variant:"secondary",type:"button",onClick:()=>{e.setValue("params",[...e.watch("params")||[],{name:"",label:"",type:"text",required:!1}])},children:[n("Add"),(0,a.jsx)(A,{size:20})]}),(0,a.jsxs)(o.z,{variant:"destructive",type:"button",onClick:()=>{e.setValue("params",e.watch("params")?.slice(0,-1))},children:[n("Remove"),(0,a.jsx)(I,{size:20})]})]}),(0,a.jsx)("div",{className:"params-fields",children:e.watch("params")?.map((s,t)=>a.jsxs("div",{className:"repeater-field-single mb-6 grid grid-cols-4 gap-6",children:[a.jsx(y.Wi,{name:`params.${t}.name`,control:e.control,render:({field:e})=>a.jsxs(y.xJ,{className:"mt-2",children:[a.jsx(y.lX,{children:n("Name (Unique)")}),a.jsx(y.NI,{children:a.jsx(w.I,{type:"text",placeholder:"Field Name",className:"disabled:bg-input",...e})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{name:`params.${t}.label`,control:e.control,render:({field:e})=>a.jsxs(y.xJ,{className:"mt-2",children:[a.jsx(y.lX,{children:n("Label")}),a.jsx(y.NI,{children:a.jsx(w.I,{type:"text",placeholder:"Field Label",className:"disabled:bg-input",...e})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{name:`params.${t}.type`,control:e.control,render:({field:e})=>a.jsxs(y.xJ,{className:"mt-2",children:[a.jsx(y.lX,{children:n("Type")}),a.jsx(y.NI,{children:a.jsxs(C.Ph,{defaultValue:e.value,onValueChange:e.onChange,children:[a.jsx(C.i4,{className:"disabled:bg-input",children:a.jsx(C.ki,{placeholder:n("Field type")})}),a.jsx(C.Bw,{children:["text","number"].map(e=>a.jsx(C.Ql,{value:e,children:e},e))})]})}),a.jsx(y.zG,{})]})}),a.jsx(y.Wi,{name:`params.${t}.required`,control:e.control,render:({field:e})=>a.jsxs(y.xJ,{className:"space-y-auto flex flex-col gap-2",children:[a.jsx(v.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:n("Required")}),a.jsx(y.NI,{children:a.jsx(P.Z,{defaultChecked:!!e.value,onCheckedChange:e.onChange,className:"disabled:opacity-100"})}),a.jsx(y.zG,{})]})})]},t))})]})]})}var k=t(25694);async function L(e,s){try{let t=new FormData;t.append("name",e.name),t.append("countryCode",e.countryCode),t.append("currencyCode",e.currencyCode),t.append("active",e.active.toString()),t.append("recommended",e.recommended.toString()),t.append("minAmount",e.minAmount?.toString()??""),t.append("maxAmount",e.maxAmount?.toString()??""),t.append("fixedCharge",e.fixedCharge?.toString()??""),t.append("percentageCharge",e.percentageCharge?.toString()??""),t.append("inputParams",e.params?JSON.stringify(e.params):""),e.uploadLogo&&t.append("uploadLogo",e.uploadLogo);let a=await m.Z.put(`/admin/methods/${s}`,t,{headers:{"Content-Type":"multipart/form-data"}});return(0,k.B)(a)}catch(e){return(0,k.D)(e)}}var M=t(93633),_=t(72382);let z=M.z.object({uploadLogo:_.K,name:M.z.string({required_error:"Name is required"}),countryCode:M.z.string({required_error:"Country is required"}),currencyCode:M.z.string({required_error:"Currency is required"}),active:M.z.boolean().default(!1),recommended:M.z.boolean().default(!1),minAmount:M.z.string().optional(),maxAmount:M.z.string().optional(),fixedCharge:M.z.string().optional(),percentageCharge:M.z.string().optional(),params:M.z.array(M.z.object({name:M.z.string().refine(e=>!e.includes(" "),"No Spaces in name field!"),label:M.z.string(),type:M.z.string(),required:M.z.boolean()})).optional()});var T=t(15487),F=t(14761),Q=t(29220),R=t(45475);function U({method:e,onMutate:s}){let t=(0,u.UO)(),{t:i}=(0,x.$G)(),[l,d]=(0,Q.useTransition)(),c=(0,R.cI)({resolver:(0,T.F)(z),defaultValues:{uploadLogo:e?.logoImage||"",name:e?.name,countryCode:e?.countryCode,currencyCode:e?.currencyCode,active:!!e?.active,recommended:!!e?.recommended,minAmount:String(e.minAmount)||"0",maxAmount:String(e?.maxAmount)||"0",fixedCharge:String(e?.fixedCharge||"0"),percentageCharge:String(e?.percentageCharge)||"0",params:JSON.parse(e?.params)}});return(0,a.jsxs)(n.Qd,{value:"withdrawDetails",className:"mb-4 rounded-xl border border-border bg-background px-4 py-0",children:[(0,a.jsx)(n.o4,{className:"py-6 hover:no-underline",children:(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:i("Method details")})}),(0,a.jsx)(n.vF,{className:"gap-4 border-t pt-4",children:(0,a.jsx)(y.l0,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(e=>{d(async()=>{let a=await L(e,t?.withdrawId);a.status?(s(),g.toast.success(a.message)):g.toast.error(i(a.message))})}),className:"flex flex-col gap-6 px-1",children:[(0,a.jsx)(D,{form:c,logoImage:e?.uploadLogo}),(0,a.jsx)("div",{className:"flex flex-row items-center justify-end gap-4",children:(0,a.jsx)(o.z,{children:l?(0,a.jsx)(r.Loader,{title:i("Updating..."),className:"text-primary-foreground"}):(0,a.jsxs)(a.Fragment,{children:[i("Update method"),(0,a.jsx)(F.Z,{size:20})]})})})]})})})]})}var G=t(58387),Z=t(78133),B=t(7680),W=t(75643),q=t(23065),O=t(12393),J=t(32917),Y=t(47020),$=t(56402),X=t(11230);function V({methodId:e,onMutate:s,blackListedUsers:t}){let{t:n}=(0,x.$G)(),{width:i}=(0,q.B)(),l=(0,u.lr)(),[c,p]=(0,Q.useState)(!1),[h,g]=Q.useState(l.get("search")??""),{data:j,isLoading:f,size:b,setSize:N,mutate:S}=(0,X.ZP)(e=>`/admin/users?page=${e+1}&limit=25&search=${h}`,e=>m.Z.get(e));return(0,a.jsxs)(B.dy,{open:c,onOpenChange:p,direction:i<640?"bottom":"right",children:[(0,a.jsx)(B.Qz,{asChild:!0,children:(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsxs)(o.z,{variant:"outline",className:"gap-1 rounded-lg",children:[(0,a.jsx)(J.Z,{}),n("Add Customer")]})})}),(0,a.jsxs)(B.sc,{className:"inset-x-auto bottom-auto left-auto right-0 top-0 m-0 mt-20 flex h-full w-full max-w-[540px] flex-col rounded-t-none bg-background px-0 pt-4 sm:inset-y-0 sm:mt-0 sm:pt-8",children:[(0,a.jsx)("span",{className:"mx-auto mb-8 block h-2.5 w-20 rounded-lg bg-divider-secondary sm:hidden"}),(0,a.jsxs)("div",{className:"flex items-center gap-4 px-6 pb-6",children:[(0,a.jsx)(o.z,{variant:"outline",size:"icon",className:"hidden sm:flex",asChild:!0,children:(0,a.jsx)(B.uh,{children:(0,a.jsx)(Y.Z,{size:16})})}),(0,a.jsxs)(B.OX,{className:"flex-1 p-0",children:[(0,a.jsx)(B.iI,{className:"text-left text-base font-semibold leading-[22px]",children:n("Customers")}),(0,a.jsx)(B.u6,{className:"invisible absolute text-xs font-normal text-secondary-text",children:n("You can add customers to the block list to prevent them from using the platform.")})]})]}),(0,a.jsx)("div",{className:"flex flex-col p-6 pt-0",children:(0,a.jsx)(Z.R,{value:h,onChange:e=>{e.preventDefault(),g(e.target.value)},iconPlacement:"end",placeholder:n("Search..."),className:"w-full"})}),(0,a.jsx)("div",{id:"scrollbarTrigger",className:"flex-1 overflow-y-auto overflow-x-hidden",children:(0,a.jsxs)("div",{className:"flex flex-col gap-2 p-6 pt-0",children:[(0,a.jsx)(G.J,{condition:f,children:(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.Loader,{})})}),(0,a.jsx)(G.J,{condition:!f&&!!j?.length,children:(0,a.jsx)($.Z,{dataLength:j?.reduce((e,s)=>e+Number(s.data?.data?.length??0),0),next:()=>N(b+1),hasMore:!!j?.[j.length-1]?.data?.meta?.nextPageUrl,loader:(0,a.jsx)(r.Loader,{className:"flex justify-center py-4"}),endMessage:(0,a.jsx)("p",{className:"py-4",style:{textAlign:"center"},children:(0,a.jsx)("b",{children:n("No more")})}),scrollableTarget:"scrollbarTrigger",children:(0,a.jsxs)(d.iA,{children:[(0,a.jsx)(d.xD,{children:(0,a.jsxs)(d.SC,{children:[(0,a.jsxs)(d.ss,{className:"w-full",children:[" ",n("Name")," "]}),(0,a.jsxs)(d.ss,{children:[" ",n("Action")," "]})]})}),(0,a.jsx)(d.RM,{children:j?.reduce((e,s)=>s?.data?.data?.length?[...e,...s.data.data]:e,[])?.map(e=>new O.n(e))?.map(r=>a.jsx(Q.Fragment,{children:a.jsx(H,{data:r,methodId:e,blackListedUsers:t,onMutate:()=>{S(),s()}})},r.id))})]})})})]})})]})]})}function H({data:e,methodId:s,blackListedUsers:t,onMutate:r}){let{t:n}=(0,x.$G)(),l=e?.customer;if(!l)return null;let m=e=>{g.toast.promise((0,W.O)({methodId:s,userId:e},"methods"),{loading:n("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return r(),e.message},error:e=>e.message})},p=e=>{g.toast.promise((0,c.E)({methodId:s,userId:e},"methods"),{loading:n("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return r(),e.message},error:e=>e.message})},u=t.includes(l.id);return(0,a.jsxs)(d.SC,{className:"border-b border-border-primary",children:[(0,a.jsxs)(d.pj,{className:"flex w-full items-center gap-2.5 py-2",children:[(0,a.jsxs)(i.qE,{children:[(0,a.jsx)(i.F$,{src:l.avatar}),(0,a.jsxs)(i.Q5,{children:[" ",(0,h.v)(l.name)," "]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"block font-medium",children:l.name}),(0,a.jsx)("span",{className:"block text-xs",children:e.email})]})]}),(0,a.jsx)(d.pj,{className:"py-2",children:(0,a.jsx)(o.z,{variant:"outline",onClick:u?()=>p(l.id):()=>m(l.id),size:"sm",className:"rounded-lg",children:u?n("Unblock user"):n("Add to blacklist")})})]})}let K="edge";function ee(){let e=(0,u.UO)(),{t:s}=(0,x.$G)(),{data:t,isLoading:f,mutate:b}=(0,j.ZP)(`/admin/methods/${e.withdrawId}`,e=>(0,m.Z)(e));if(f)return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.Loader,{})});let N=t=>{let a={methodId:Number(e.withdrawId),userId:t};g.toast.promise((0,c.E)(a,"methods"),{loading:s("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return b(),e.message},error:e=>e.message})},S=t?.data,y=S?.blackListedUsers?.map(e=>e.customer.userId)||[];return(0,a.jsxs)(n.UQ,{type:"multiple",defaultValue:["withdrawDetails","withdrawDetailsAllowed","Blacklist"],children:[(0,a.jsx)(U,{method:S,onMutate:b}),(0,a.jsxs)(n.Qd,{value:"Blacklist",className:"mt-4 rounded-xl border border-border bg-background px-4 py-0",children:[(0,a.jsx)(n.o4,{className:"flex items-center justify-between py-6 hover:no-underline",children:(0,a.jsx)("p",{className:"text-base font-medium leading-[22px]",children:s("Blacklist")})}),(0,a.jsxs)(n.vF,{className:"border-t pt-4",children:[(0,a.jsx)("div",{className:"w-full max-w-[700px]",children:(0,a.jsxs)(d.iA,{children:[(0,a.jsx)(d.xD,{children:(0,a.jsxs)(d.SC,{children:[(0,a.jsxs)(d.ss,{className:"w-full",children:[" ",s("Name")," "]}),(0,a.jsxs)(d.ss,{children:[" ",s("Action")," "]})]})}),(0,a.jsx)(d.RM,{children:S?.blackListedUsers.map(e=>a.jsxs(d.SC,{children:[a.jsxs(d.pj,{className:"flex w-full items-center gap-2.5 py-2",children:[a.jsxs(i.qE,{children:[a.jsx(i.F$,{src:p.qR(e?.customer.profileImage)}),a.jsxs(i.Q5,{children:[h.v(e?.customer.name)," "]})]}),a.jsxs("div",{children:[a.jsx("span",{className:"block font-medium",children:e?.customer.name}),a.jsx("span",{className:"block text-xs",children:e.email})]})]}),a.jsx(d.pj,{className:"py-2",children:a.jsx(o.z,{variant:"outline",size:"sm",onClick:()=>N(e?.customer.userId),className:"rounded-lg",children:s("Unblock")})})]},e?.id))})]})}),(0,a.jsx)(l.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsx)(V,{methodId:Number(e.withdrawId),onMutate:()=>b(t),blackListedUsers:y})]})]})]})}},10471:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(59141).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},84302:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(42416),r=t(21237);function n(){return(0,a.jsx)("div",{className:"flex justify-center py-10",children:(0,a.jsx)(r.a,{})})}},29780:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,runtime:()=>r});var a=t(18264);let r=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\withdraw-methods\[withdrawId]\page.tsx#runtime`),n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\settings\withdraw-methods\[withdrawId]\page.tsx#default`)},90056:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(42416),r=t(21237);function n(){return(0,a.jsx)("div",{className:"flex justify-center py-10",children:(0,a.jsx)(r.a,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[529,6578,3390,4969,1474,7848,8583,7283,5089,3711,4656,3020],()=>s(13271));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/settings/withdraw-methods/[withdrawId]/page"]=t}]);
//# sourceMappingURL=page.js.map