(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72944],{2603:function(e,a,n){Promise.resolve().then(n.bind(n,68835))},68835:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return f}});var s=n(57437),t=n(32356),l=n(85539),r=n(27186),c=n(85017),u=n(6512),i=n(75730),d=n(94508),o=n(99376),h=n(2265),m=n(43949);function f(){var e;let a=(0,o.useSearchParams)(),[n,f]=h.useState(null!==(e=a.get("search"))&&void 0!==e?e:""),x=(0,o.useRouter)(),g=(0,o.usePathname)(),{t:v}=(0,m.$G)(),{data:p,isLoading:j,meta:w,refresh:N}=(0,i.Z)("/admin/exchanges?".concat(a.toString()));return(0,s.jsx)("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,s.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,s.jsxs)("div",{className:"ml-auto flex flex-wrap items-center gap-4",children:[(0,s.jsx)(l.R,{value:n,onChange:e=>{e.preventDefault();let a=(0,d.w4)(e.target.value);f(e.target.value),x.replace("".concat(g,"?").concat(a.toString()))},iconPlacement:"end",placeholder:v("Search..."),containerClass:"w-full sm:w-auto"}),(0,s.jsx)(c.k,{}),(0,s.jsx)(r._,{url:"/admin/exchanges/export/all",align:"end"})]}),(0,s.jsx)("div",{})]}),(0,s.jsx)(u.Z,{className:"my-4"}),(0,s.jsx)(t.Z,{data:p,meta:w,isLoading:j,refresh:N})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,48248,27443,86687,227,56993,85017,32356,92971,95030,1744],function(){return e(e.s=2603)}),_N_E=e.O()}]);