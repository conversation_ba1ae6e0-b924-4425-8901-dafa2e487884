"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { IEscrow } from "@/types/escrow";
import { AdminEscrowFilters } from "@/types/admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import Label from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Shield, 
  MoreHorizontal, 
  Eye, 
  DollarSign, 
  XCircle,
  AlertTriangle,
  Search,
  Filter,
  Download
} from "lucide-react";

interface AdminEscrowManagementProps {
  escrows: IEscrow[];
  isLoading?: boolean;
  onFiltersChange?: (filters: AdminEscrowFilters) => void;
  onAction?: (action: string, escrowId: number, data?: any) => void;
  onExport?: () => void;
}

export function AdminEscrowManagement({ 
  escrows, 
  isLoading = false, 
  onFiltersChange,
  onAction,
  onExport 
}: AdminEscrowManagementProps) {
  const { t } = useTranslation();
  const [filters, setFilters] = useState<AdminEscrowFilters>({});
  const [selectedEscrows, setSelectedEscrows] = useState<number[]>([]);

  const updateFilter = (key: keyof AdminEscrowFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    if (value === '' || value === undefined) {
      delete newFilters[key];
    }
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'active':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'disputed':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const toggleEscrowSelection = (escrowId: number) => {
    setSelectedEscrows(prev => 
      prev.includes(escrowId) 
        ? prev.filter(id => id !== escrowId)
        : [...prev, escrowId]
    );
  };

  const toggleAllSelection = () => {
    setSelectedEscrows(
      selectedEscrows.length === escrows.length 
        ? [] 
        : escrows.map(e => e.id)
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t('Escrow Management')}</h2>
          <p className="text-muted-foreground">
            {t('Monitor and manage all escrow transactions')}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={onExport}>
            <Download className="h-4 w-4 mr-2" />
            {t('Export')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>{t('Filters')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="space-y-2">
              <Label>{t('Search')}</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('Search escrows...')}
                  value={filters.search || ''}
                  onChange={(e) => updateFilter('search', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>{t('Status')}</Label>
              <Select
                value={filters.status || ''}
                onValueChange={(value) => updateFilter('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('All statuses')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{t('All statuses')}</SelectItem>
                  <SelectItem value="pending">{t('Pending')}</SelectItem>
                  <SelectItem value="active">{t('Active')}</SelectItem>
                  <SelectItem value="completed">{t('Completed')}</SelectItem>
                  <SelectItem value="cancelled">{t('Cancelled')}</SelectItem>
                  <SelectItem value="expired">{t('Expired')}</SelectItem>
                  <SelectItem value="disputed">{t('Disputed')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>{t('Amount Range')}</Label>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  type="number"
                  placeholder={t('Min')}
                  value={filters.amountMin || ''}
                  onChange={(e) => updateFilter('amountMin', e.target.value ? parseFloat(e.target.value) : undefined)}
                />
                <Input
                  type="number"
                  placeholder={t('Max')}
                  value={filters.amountMax || ''}
                  onChange={(e) => updateFilter('amountMax', e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>{t('Sort By')}</Label>
              <Select
                value={filters.sortBy || ''}
                onValueChange={(value) => updateFilter('sortBy', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('Sort by...')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">{t('Created Date')}</SelectItem>
                  <SelectItem value="amount">{t('Amount')}</SelectItem>
                  <SelectItem value="deadline">{t('Deadline')}</SelectItem>
                  <SelectItem value="status">{t('Status')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedEscrows.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {t('{{count}} escrow(s) selected', { count: selectedEscrows.length })}
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAction?.('bulk-export', 0, { escrowIds: selectedEscrows })}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t('Export Selected')}
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => onAction?.('bulk-force-cancel', 0, { escrowIds: selectedEscrows })}
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  {t('Force Cancel')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Escrows Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedEscrows.length === escrows.length && escrows.length > 0}
                    onChange={toggleAllSelection}
                    className="rounded"
                  />
                </TableHead>
                <TableHead>{t('Escrow ID')}</TableHead>
                <TableHead>{t('Amount')}</TableHead>
                <TableHead>{t('Status')}</TableHead>
                <TableHead>{t('Sender')}</TableHead>
                <TableHead>{t('Recipient')}</TableHead>
                <TableHead>{t('Created')}</TableHead>
                <TableHead>{t('Deadline')}</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {escrows.map((escrow) => (
                <TableRow key={escrow.id}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedEscrows.includes(escrow.id)}
                      onChange={() => toggleEscrowSelection(escrow.id)}
                      className="rounded"
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    {escrow.escrowId}
                  </TableCell>
                  <TableCell>
                    {escrow.amount.toLocaleString()} {escrow.currencyCode}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusColor(escrow.status)}>
                      {t(escrow.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {escrow.sender?.name || t('Unknown')}
                  </TableCell>
                  <TableCell>
                    {escrow.recipient?.name || t('Unknown')}
                  </TableCell>
                  <TableCell>
                    {new Date(escrow.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <span>{new Date(escrow.deadline).toLocaleDateString()}</span>
                      {new Date() > new Date(escrow.deadline) && (
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onAction?.('view', escrow.id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          {t('View Details')}
                        </DropdownMenuItem>
                        {escrow.status === 'active' && (
                          <DropdownMenuItem onClick={() => onAction?.('force-release', escrow.id)}>
                            <DollarSign className="h-4 w-4 mr-2" />
                            {t('Force Release')}
                          </DropdownMenuItem>
                        )}
                        {['pending', 'active'].includes(escrow.status) && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => onAction?.('force-cancel', escrow.id)}
                              className="text-red-600"
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              {t('Force Cancel')}
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Empty State */}
      {escrows.length === 0 && !isLoading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Shield className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('No escrows found')}</h3>
            <p className="text-muted-foreground text-center">
              {t('No escrows match your current filters.')}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
