(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[18358],{42906:function(e,a,n){Promise.resolve().then(n.bind(n,28735))},28735:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return f}});var s=n(57437),t=n(90117),l=n(85539),c=n(27186),r=n(85017),u=n(6512),i=n(75730),d=n(94508),o=n(99376),m=n(2265),h=n(43949);function f(){var e;let a=(0,o.useSearchParams)(),n=(0,o.usePathname)(),f=(0,o.useRouter)(),[v,x]=m.useState(null!==(e=a.get("search"))&&void 0!==e?e:""),{t:g}=(0,h.$G)(),{data:p,meta:j,isLoading:N}=(0,i.Z)("/admin/merchants/payment-requests/all?".concat(a.toString()));return(0,s.jsx)("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,s.jsxs)("div",{className:"flex h-12 items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(l.R,{value:v,onChange:e=>{e.preventDefault();let a=(0,d.w4)(e.target.value);x(e.target.value),f.replace("".concat(n,"?").concat(a.toString()))},iconPlacement:"end",placeholder:g("Search..."),containerClass:"w-full sm:w-auto"}),(0,s.jsx)(r.k,{}),(0,s.jsx)(c._,{url:"",align:"end"})]}),(0,s.jsx)("div",{})]}),(0,s.jsx)(u.Z,{className:"my-4"}),(0,s.jsx)(t.Z,{data:p,meta:j,isLoading:N})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,227,56993,85017,13154,92971,95030,1744],function(){return e(e.s=42906)}),_N_E=e.O()}]);