exports.id=4370,exports.ids=[4370],exports.modules={5959:(e,s,t)=>{Promise.resolve().then(t.bind(t,68986))},42441:(e,s,t)=>{Promise.resolve().then(t.bind(t,68986))},68986:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ea});var a=t(10326),r=t(17577),n=t.n(r),l=t(5158),i=t(14926),c=t(82548),d=t(48054),o=t(92392),m=t(28758),x=t(77863),u=t(54033),h=t(70012);function j({avatar:e,name:s,accountNumber:t,checked:r,onChange:n}){let{t:l}=(0,h.$G)();return(0,a.jsxs)("div",{"data-checked":r,className:"relative flex items-center gap-2 border-b border-dashed p-2 last:border-b-0 hover:bg-accent data-[checked=true]:border-solid data-[checked=true]:border-accent",children:[a.jsx("input",{type:"radio",checked:r,onChange:()=>n&&n(t),className:"absolute inset-0 left-0 top-0 z-10 opacity-0 hover:cursor-pointer"}),a.jsx("div",{className:"h-8 min-w-8 rounded-full",children:(0,a.jsxs)(m.qE,{className:"h-8 w-8",children:[a.jsx(m.F$,{src:(0,x.qR)(e)}),a.jsx(m.Q5,{className:"bg-primary text-primary-foreground",children:(0,u.v)(s)})]})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{children:s}),(0,a.jsxs)("p",{className:"text-sm text-secondary-text",children:[l("MerchantID"),": ",t]})]})]})}var f=t(90799);function p({value:e,onSelect:s}){let{t}=(0,h.$G)(),{data:r,error:n,isLoading:l}=(0,f.d)("/merchants/saved");if(n)return a.jsx("div",{className:"rounded-lg bg-destructive text-destructive-foreground",children:t("An unexpected error occurred. Please try reloading the page or contacting our administrator for assistance.")});if(l)return a.jsx(o.Loader,{title:t("Loading...")});if(r?.data.length>0){let t=r?.data,n=e=>JSON.parse(e.info);return t.map(t=>a.jsx("div",{children:a.jsx(j,{avatar:n(t)?.image,name:n(t)?.label,accountNumber:t?.value,checked:e===t?.value,onChange:e=>s(e,{...t,info:n(t)})})},t.id))}return a.jsx("p",{className:"text-sm font-medium text-foreground/50",children:t("No data found")})}var v=t(55632),N=t(49547),g=t(10734);async function b(e){try{let s=await N.Z.post("/payments/create",{currencyCode:e?.sender_wallet_id,amount:Number(e?.amount),merchantId:e?.receiver_merchant_id});return(0,g.B)(s)}catch(e){return(0,g.D)(e)}}var y=t(19395),w=t(74064),z=t(35047),I=t(74723),Z=t(85999),_=t(27256),k=t(2454),C=t(54432),P=t(30811);function $({selected:e,onSelect:s}){let{t}=(0,h.$G)(),[r,l]=n().useState(!1),[i,c]=n().useState(e),{data:d,isLoading:m}=(0,f.d)(`/merchants/global?search=${i}`);return(0,a.jsxs)(P.J2,{open:r,onOpenChange:l,children:[a.jsx(P.xo,{className:"w-full",children:a.jsx(C.I,{placeholder:t("Enter merchant account"),value:i,onChange:e=>{e.preventDefault();let t=e.target.value;c(t),s(t),t&&!r&&setTimeout(()=>{l(!0)},600)}})}),a.jsx(P.yk,{onOpenAutoFocus:e=>e.preventDefault(),className:"w-[var(--radix-popover-trigger-width)] p-0",children:a.jsx(k.mY,{autoFocus:!1,children:(0,a.jsxs)(k.e8,{children:[a.jsx(k.rb,{children:t("No results found.")}),(0,a.jsxs)(k.fu,{children:[m&&a.jsx(k.di,{children:a.jsx(o.Loader,{})}),d?.data.length?d?.data.map(e=>a.jsx(k.di,{value:e?.merchantId,onSelect:()=>{s(e?.merchantId,e)},children:a.jsx(S,{name:e?.name,avatar:e?.profileImage,email:e?.email})},e.merchantId)):null]})]})})})]})}function S({avatar:e,name:s,email:t}){return(0,a.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,a.jsxs)(m.qE,{children:[a.jsx(m.F$,{src:e,className:"border-2 border-border"}),a.jsx(m.Q5,{className:"border-2 border-border",children:(0,u.v)(s)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{children:s}),a.jsx("p",{className:"text-sm text-gray",children:t})]})]})}var F=t(74743),D=t(90772),E=t(47237),G=t(3001),L=t(44284);function J({form:e,onNext:s,merchant:t,selectedMerchant:r,merchantIdInputMode:n,setMerchantIdInputMode:i}){let{t:c}=(0,h.$G)();return(0,a.jsxs)(a.Fragment,{children:[a.jsx("h2",{className:"mb-4",children:c("Add Merchant")}),a.jsx(l.J,{condition:n,children:a.jsx(v.Wi,{control:e.control,name:"receiver_merchant_id",render:({field:e})=>(0,a.jsxs)(v.xJ,{children:[a.jsx(v.NI,{children:a.jsx($,{selected:e.value,onSelect:(s,t)=>{e.onChange(s),t&&(i(!1),r(t))}})}),a.jsx(v.zG,{})]})})}),a.jsx(l.J,{condition:!n,children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,a.jsxs)(m.qE,{children:[a.jsx(m.F$,{src:t?.profileImage,className:"border-2 border-border"}),a.jsx(m.Q5,{className:"border-2 border-border",children:(0,u.v)(t?.name)})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{children:t?.name}),a.jsx("p",{className:"text-sm text-gray",children:t?.email})]})]}),(0,a.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-sm",children:[a.jsx(E.Z,{variant:"Bold",size:17,className:"text-primary"}),c("Selected")]}),(0,a.jsxs)(D.z,{variant:"link",size:"sm",className:"gap-1.5 justify-self-end text-sm text-foreground hover:text-primary",type:"button",onClick:()=>i(!0),children:[c("Change"),a.jsx(G.Z,{size:15})]})]})}),(0,a.jsxs)("div",{className:"my-8",children:[a.jsx("h2",{className:"mb-4",children:c("Select wallet")}),a.jsx(v.Wi,{name:"sender_wallet_id",control:e.control,render:({field:e})=>(0,a.jsxs)(v.xJ,{className:"col-span-12 sm:col-span-6 lg:col-span-4",children:[a.jsx(v.NI,{children:a.jsx(F.R,{...e})}),a.jsx(v.zG,{className:"col-span-12"})]})})]}),(0,a.jsxs)("div",{className:"mb-10",children:[a.jsx("h2",{className:"mb-4",children:c("How much?")}),a.jsx(v.Wi,{control:e.control,name:"amount",render:({field:e})=>(0,a.jsxs)(v.xJ,{children:[a.jsx(v.NI,{children:a.jsx(C.I,{placeholder:c("Enter payment amount"),...e,type:"number"})}),a.jsx(v.zG,{})]})})]}),a.jsx("div",{className:"flex w-full justify-end",children:(0,a.jsxs)(D.z,{type:"submit",className:"w-52",onClick:s,children:[c("Next"),a.jsx(L.Z,{size:17})]})})]})}var R=t(12649),T=t(567),q=t(8281),A=t(60814),B=t(44221);function Q({onNext:e,onBack:s,formData:t,isLoading:r=!1,merchant:n}){let{wallets:i,getWalletByCurrencyCode:c}=(0,A.r)(),{t:d}=(0,h.$G)(),j=c(i,t.sender_wallet_id),{data:p,isLoading:v}=(0,f.d)(`/payments/preview/create?amount=${t.amount}`);return(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"mb-8",children:d("Confirm and proceed")}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[a.jsx("h5",{className:"text-sm font-medium sm:text-base",children:d("Selected wallet")}),(0,a.jsxs)("div",{className:"flex flex-row items-center gap-2.5",children:[(0,a.jsxs)(m.qE,{className:"size-8",children:[a.jsx(m.F$,{src:j?.logo}),a.jsx(m.Q5,{className:"bg-important text-xs font-bold text-important-foreground",children:j?.currency?.code})]}),a.jsx("h6",{className:"font-bold",children:j?.currency?.code})]})]}),a.jsx(q.Z,{className:"my-8"}),(0,a.jsxs)(R.Y,{children:[a.jsx(R.r,{title:`${n?.name} will get`,value:`${p?.data?.amount} ${t.sender_wallet_id}`,isLoading:v}),a.jsx(R.r,{title:d("Service charge"),value:a.jsx(T.C,{variant:"success",className:"font-medium",children:d("Free")}),isLoading:v}),a.jsx(R.r,{title:d("Total"),value:`${p?.data?.amount} ${t.sender_wallet_id}`,isLoading:v})]}),a.jsx(q.Z,{className:"my-8"}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[a.jsx("h5",{className:"text-medium text-sm sm:text-base",children:d("Recipient")}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(m.qE,{children:[a.jsx(m.F$,{src:(0,x.qR)(n?.profileImage),alt:n?.name}),a.jsx(m.Q5,{className:"bg-primary font-bold text-primary-foreground",children:(0,u.v)(n?.name)})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm sm:text-base",children:n?.name}),a.jsx("p",{className:"text-[10px] text-secondary-text sm:text-xs",children:n?.email})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex flex-1 justify-between gap-4",children:[(0,a.jsxs)(D.z,{variant:"outline",onClick:s,children:[a.jsx(B.Z,{size:17}),d("Back")]}),(0,a.jsxs)(D.z,{disabled:r,onClick:e,children:[(0,a.jsxs)(l.J,{condition:!r,children:[d("Pay Now"),a.jsx(L.Z,{size:17})]}),a.jsx(l.J,{condition:r,children:a.jsx(o.Loader,{title:d("Processing..."),className:"text-primary-foreground"})})]})]})]})}var M=t(29197),O=t(45806),V=t(91778),W=t(60097),X=t(30414),Y=t(25896),H=t(31112),U=t(7310),K=t(62047),ee=t(66678);function es({formResponse:e,onPaymentAgain:s,merchant:t}){let r=(0,z.useRouter)(),{t:n}=(0,h.$G)(),i=e?.data,{wallets:c,getWalletByCurrencyCode:d}=(0,A.r)(),o=d(c,i?.from?.currency),j=new x.F(i?.metaData?.currency);return(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"mb-1 flex items-center justify-center gap-2 text-2xl font-semibold text-foreground",children:[a.jsx(l.J,{condition:i?.status==="completed",children:a.jsx(E.Z,{size:"32",color:"#13A10E",variant:"Bold"})}),a.jsx(l.J,{condition:i?.status==="failed",children:a.jsx(Y.Z,{size:"32",color:"#C72400",variant:"Bold"})}),(0,a.jsxs)("span",{children:[n("Payment")," ",i?.status]})]}),a.jsx(q.Z,{orientation:"horizontal",className:"my-7"}),a.jsx(V.z,{className:"mb-8",senderAvatar:(0,x.qR)(i?.from?.image),senderName:i?.from?.label,receiverAvatar:(0,x.qR)(i?.to?.image),receiverName:i?.to?.label}),(0,a.jsxs)(R.Y,{groupName:n("Payment details"),children:[a.jsx(R.r,{title:`${i?.to?.label} will get`,value:j.formatVC(i?.amount)}),a.jsx(R.r,{title:n("Service charge"),value:a.jsx(T.C,{variant:"success",className:"font-medium",children:n("Free")})}),a.jsx(R.r,{title:n("Total"),value:j.formatVC(i?.amount)})]}),a.jsx(q.Z,{orientation:"horizontal",className:"my-7"}),a.jsx(l.J,{condition:i?.status==="completed",children:a.jsx(O.T,{id:i?.trxId,className:"mb-4 text-sm sm:text-base"})}),(0,a.jsxs)("div",{className:"mb-8 space-y-4 text-sm sm:text-base",children:[a.jsx("h5",{className:"text-sm font-medium sm:text-base",children:n("New balance")}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(m.qE,{className:"h-8 w-8",children:[a.jsx(m.F$,{src:i?.from?.currency}),a.jsx(m.Q5,{className:"bg-black text-white",children:(0,u.v)(i?.from?.currency)})]}),a.jsx("span",{className:"text-sm font-bold",children:i?.from?.currency})]}),(0,a.jsxs)("p",{className:"text-sm font-medium sm:text-base",children:[o?.balance," ",o?.currency?.code]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[a.jsx(M.T,{trxId:i?.trxId,className:"w-full md:w-auto"}),(0,a.jsxs)("div",{className:"flex w-full flex-wrap gap-4 md:w-auto md:justify-end",children:[(0,a.jsxs)(W.h_,{children:[(0,a.jsxs)(W.$F,{className:(0,x.ZP)("flex w-full items-center space-x-1.5 md:w-fit",(0,D.d)({variant:"outline"})),children:[a.jsx("span",{children:n("Menu")}),a.jsx(H.Z,{size:16})]}),(0,a.jsxs)(W.AW,{align:"start",className:"m-0",children:[(0,a.jsxs)(W.Xi,{onSelect:()=>(0,x.Fp)(i?.trxId),className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[a.jsx(U.Z,{variant:"Outline",size:"20"}),n("Copy transaction ID")]}),(0,a.jsxs)(W.Xi,{onSelect:()=>{Z.toast.promise((0,X.hs)({merchantId:String(t?.merchantId)}),{loading:n("Loading..."),success:e=>{if(!e?.status)throw Error(e.message);return e.message},error:e=>e.message})},className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[a.jsx(K.Z,{size:"20",variant:"Outline"}),n("Add to favorites")]}),a.jsx(W.VD,{}),(0,a.jsxs)(W.Xi,{onSelect:s,className:"flex items-center gap-2 text-sm font-medium focus:text-primary",children:[a.jsx(ee.Z,{size:"20",variant:"Outline"}),n("Payment again")]})]})]}),(0,a.jsxs)(D.z,{type:"button",onClick:()=>r.push("/"),className:"w-full md:max-w-48",children:[a.jsx("span",{children:n("Go to dashboard")}),a.jsx(L.Z,{size:16})]})]})]})]})}let et=_.z.object({sender_wallet_id:_.z.string().min(1,"Select a wallet"),receiver_merchant_id:_.z.string().min(1,"Receiver merchant account is required."),amount:_.z.string().min(1,"Payment amount is required.").refine(e=>Number(e)>0,{message:"Payment amount must be greater than 0."})});function ea(){let{auth:e}=(0,y.a)(),s=(0,z.useSearchParams)(),{t}=(0,h.$G)(),[o,m]=n().useState("payment_details"),[x,u]=n().useState(),[j,N]=n().useState(),[g,_]=n().useState([{value:"payment_details",title:t("Payment Details"),complete:!1},{value:"review",title:t("Payment & Review"),complete:!1},{value:"finish",title:t("Finish"),complete:!1}]),[k,C]=(0,r.useTransition)(),[P,$]=n().useState(!0),{data:S,isLoading:F}=(0,f.d)(`/merchants/global?search=${s.get("email")}`),D=(0,I.cI)({resolver:(0,w.F)(et),mode:"all",defaultValues:{sender_wallet_id:"",receiver_merchant_id:"",amount:""}}),E=e=>{_(g.map(s=>s.value===e?{...s,complete:!0}:s))};return e?.canMakePayment()?a.jsx(c.Xg,{children:a.jsx(v.l0,{...D,children:a.jsx("form",{className:"md:h-full",children:(0,a.jsxs)("div",{className:"relative flex md:h-full",children:[a.jsx("div",{className:"w-full p-4 pb-10 md:h-full md:p-12",children:(0,a.jsxs)("div",{className:"mx-auto max-w-3xl",children:[a.jsx(l.J,{condition:"payment_details"===o,children:a.jsx(c.cI,{})}),a.jsx(d.R,{tabs:g,onTabChange:e=>m(e),value:o,children:(0,a.jsxs)("div",{className:"p-4",children:[a.jsx(d.Q,{value:"payment_details",children:a.jsx(J,{form:D,onNext:D.handleSubmit(()=>{m("review"),E("payment_details")}),merchantIdInputMode:P,setMerchantIdInputMode:$,merchant:j,selectedMerchant:e=>N(e)})}),a.jsx(d.Q,{value:"review",children:a.jsx(Q,{onBack:()=>m("payment_details"),onNext:D.handleSubmit(e=>{C(async()=>{let s=await b(e);s&&s.status?(Z.toast.success(s.message),m("finish"),E("finish"),E("review"),u(s)):Z.toast.error(t(s.message))})}),formData:D.getValues(),merchant:j,isLoading:k})}),a.jsx(d.Q,{value:"finish",children:a.jsx(es,{merchant:j,formResponse:x,onPaymentAgain:()=>{m("payment_details"),u(void 0),D.reset(),_([{value:"payment_details",title:t("Payment Details"),complete:!1},{value:"review",title:t("Payment & Review"),complete:!1},{value:"finish",title:t("Finish"),complete:!1}])}})})]})})]})}),a.jsx(l.J,{condition:"payment_details"===o,children:a.jsx(c.jT,{children:(0,a.jsxs)("div",{className:"mb-4 rounded-xl bg-background p-6 shadow-default",children:[(0,a.jsxs)("div",{className:"mb-2 border-b border-divider-secondary pb-6",children:[a.jsx("p",{className:"mb-2 font-medium text-foreground",children:t("Favorite merchants")}),a.jsx("p",{className:"text-xs text-secondary-text",children:t("Click to autofill merchant")})]}),a.jsx("div",{className:"flex h-full max-h-72 flex-col overflow-y-auto",children:a.jsx(v.Wi,{control:D.control,name:"receiver_merchant_id",render:({field:e})=>a.jsx(v.xJ,{className:"max-h-[calc(100vh-250px)] overflow-y-auto",children:a.jsx(v.NI,{children:a.jsx(p,{value:e.value,onSelect:(s,t)=>{e.onChange(s),N({email:t?.info?.email,gender:"",merchantId:t?.value,name:t?.info?.label,profileImage:null,roleId:null}),$(!1)}})})})})})]})})})]})})})}):a.jsx(i.Z,{className:"flex-1 p-10"})}},29197:(e,s,t)=>{"use strict";t.d(s,{T:()=>d});var a=t(10326),r=t(90772),n=t(98196),l=t(77863),i=t(88010),c=t(70012);function d({trxId:e,className:s}){let{t}=(0,c.$G)();return a.jsx(r.z,{variant:"outline",type:"button",className:(0,l.ZP)("w-full md:w-auto",s),asChild:!0,children:(0,a.jsxs)("a",{href:`${n.rH.API_URL}/transactions/download-receipt/${e}`,children:[a.jsx(i.Z,{size:16}),a.jsx("span",{children:t("Download Receipt")})]})})}},14926:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});var a=t(10326),r=t(77863),n=t(90434),l=t(70012);function i({className:e}){let{t:s}=(0,l.$G)();return a.jsx("div",{className:(0,r.ZP)("flex items-center justify-center",e),children:(0,a.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[a.jsx("h3",{className:"mb-2.5",children:s("This feature is temporarily unavailable")}),(0,a.jsxs)("p",{className:"text-sm text-secondary-text",children:[s("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),a.jsx(n.default,{href:"/contact-supports",className:"text-primary hover:underline",children:s("support")}),"."]}),a.jsx("p",{className:"mt-2 text-sm text-secondary-text",children:s("Thank you for your understanding.")})]})})}},82548:(e,s,t)=>{"use strict";t.d(s,{Xg:()=>h,cI:()=>u,jT:()=>j});var a=t(10326),r=t(90772),n=t(95028),l=t(77863),i=t(62047),c=t(44221),d=t(17577),o=t(70012);let m=d.createContext(null),x=()=>{let e=d.useContext(m);if(!e)throw Error("usePageLayout must be used within an PageLayoutCtx. Please ensure that your component is wrapped with an PageLayoutCtx.");return e};function u({className:e}){let{t:s}=(0,o.$G)(),{setRightSidebar:t}=x();return a.jsx("div",{className:(0,l.ZP)("flex items-center justify-end md:mb-4 xl:hidden",e),children:(0,a.jsxs)(r.z,{onClick:()=>t(e=>!e),variant:"outline",size:"sm",type:"button",className:"text-sm",children:[a.jsx(i.Z,{size:"20"}),s("Bookmarks")]})})}function h({children:e}){let[s,t]=d.useState(!1),{width:r}=(0,n.B)();d.useEffect(()=>{r>=1280&&t(!0)},[r]);let l=d.useMemo(()=>({width:r,rightSidebar:s,setRightSidebar:t}),[r,s]);return a.jsx(m.Provider,{value:l,children:e})}function j({children:e}){let{t:s}=(0,o.$G)(),{width:t,rightSidebar:n,setRightSidebar:l}=x();return(0,a.jsxs)("div",{"data-expanded":t>=1280||t<1280&&n,className:"absolute inset-y-0 right-0 top-0 w-full max-w-96 translate-x-full bg-background-body p-6 transition-all duration-300 ease-in-out data-[expanded=true]:translate-x-0 xl:relative",children:[(0,a.jsxs)(r.z,{variant:"outline",size:"sm",type:"button",onClick:()=>l(!1),className:"mb-4 gap-[2px] bg-background text-sm hover:bg-background xl:hidden",children:[a.jsx(c.Z,{size:14}),s("Hide bookmarks")]}),e]})}},45806:(e,s,t)=>{"use strict";t.d(s,{T:()=>d});var a=t(10326),r=t(90772),n=t(77863),l=t(7310),i=t(70012),c=t(85999);function d({id:e,className:s}){let{t}=(0,i.$G)();return(0,a.jsxs)("div",{className:(0,n.ZP)("inline-flex w-full items-center gap-4",s),children:[a.jsx("div",{className:"flex-1",children:t("Transaction ID")}),(0,a.jsxs)("div",{className:"inline-flex items-center gap-4",children:[a.jsx("span",{children:e}),a.jsx(r.z,{type:"button",onClick:()=>{navigator.clipboard.writeText(e).then(()=>c.toast.success("Copied to clipboard!")).catch(()=>{c.toast.error("Failed to copy!")})},variant:"outline",size:"sm",children:a.jsx(l.Z,{size:"20"})})]})]})}},91778:(e,s,t)=>{"use strict";t.d(s,{z:()=>c});var a=t(10326),r=t(28758),n=t(77863),l=t(54033),i=t(47237);function c({senderName:e,senderAvatar:s,senderInfo:t,receiverName:r,receiverAvatar:l,receiverInfo:i,className:c}){return(0,a.jsxs)("div",{className:(0,n.ZP)("mb-4 flex items-start justify-around gap-1",c),children:[a.jsx(d,{name:e,avatar:s,info:t}),r&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"mt-8 h-[1px] flex-1 border-t border-dashed border-success sm:mt-10"}),a.jsx(d,{name:r,avatar:l,info:i})]})]})}function d({avatar:e,name:s,info:t=[]}){let n=t.filter(Boolean);return(0,a.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,a.jsxs)("div",{className:"relative mb-4 size-10 sm:size-14 md:mb-0",children:[(0,a.jsxs)(r.qE,{className:"size-10 rounded-full sm:size-14",children:[a.jsx(r.F$,{src:e,alt:s,width:56,height:56}),a.jsx(r.Q5,{className:"font-semibold",children:(0,l.v)(s)})]}),a.jsx("span",{className:"absolute bottom-0 right-0 rounded-full bg-background p-[1px]",children:a.jsx(i.Z,{color:"#13A10E",variant:"Bold",className:"size-4 sm:size-5"})})]}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"block w-full max-w-[100px] whitespace-nowrap text-sm font-medium leading-[4px] sm:max-w-[150px] sm:text-base",children:s}),n.length>0&&n.map((e,s)=>a.jsx("span",{className:"line-clamp-2 block max-w-[100px] text-xs font-normal leading-5 text-secondary-text sm:max-w-[150px] sm:text-sm",children:e},s))]})]})}},30414:(e,s,t)=>{"use strict";t.d(s,{Az:()=>i,Ch:()=>l,hs:()=>n});var a=t(49547),r=t(10734);async function n(e){try{let s=await a.Z.post("/merchants/save",e);return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}async function l(e){try{let s=await a.Z.post("/services/phone/save",e);return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}async function i(e){try{if(!e.contactId)throw Error("contact id not found");let s=await a.Z.post("/contacts/create",e);return(0,r.B)(s)}catch(e){return(0,r.D)(e)}}},88728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(19510),r=t(40099),n=t(76609);function l({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(n.Z,{userRole:"customer"}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},80549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},64325:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(48413);function n(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(r.a,{})})}},30360:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@customer\payment\page.tsx#default`)},44158:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(19510),r=t(30360);function n(){return a.jsx(r.default,{})}}};