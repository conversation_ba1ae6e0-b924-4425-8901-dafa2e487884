(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3910],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},97561:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>I,default:()=>M});var a,r={};s.r(r),s.d(r,{AppRouter:()=>p.WY,ClientPageRoot:()=>p.b1,GlobalError:()=>m.ZP,LayoutRouter:()=>p.yO,NotFoundBoundary:()=>p.O4,Postpone:()=>p.hQ,RenderFromTemplateContext:()=>p.b5,__next_app__:()=>f,actionAsyncStorage:()=>p.Wz,createDynamicallyTrackedSearchParams:()=>p.rL,createUntrackedSearchParams:()=>p.S5,decodeAction:()=>p.Hs,decodeFormState:()=>p.dH,decodeReply:()=>p.kf,originalPathname:()=>g,pages:()=>x,patchFetch:()=>p.XH,preconnect:()=>p.$P,preloadFont:()=>p.C5,preloadStyle:()=>p.oH,renderToReadableStream:()=>p.aW,requestAsyncStorage:()=>p.Fg,routeModule:()=>h,serverHooks:()=>p.GP,staticGenerationAsyncStorage:()=>p.AT,taintObjectReference:()=>p.nr,tree:()=>u}),s(67206);var n=s(79319),i=s(20518),o=s(61902),c=s(62042),l=s(44630),d=s(44828),m=s(65505),p=s(13839);let u=["",{children:["(protected)",{agent:["children",{children:["deposit-request",{children:["[trxId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,330)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\deposit-request\\[trxId]\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,66344)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\deposit-request\\[trxId]\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,9697)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\deposit-request\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,44491)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,20626)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],admin:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.bind(s,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],x=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@agent\\deposit-request\\[trxId]\\page.tsx"],g="/(protected)/@agent/deposit-request/[trxId]/page",f={require:s,loadChunk:()=>Promise.resolve()},h=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@agent/deposit-request/[trxId]/page",pathname:"/deposit-request/[trxId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}});var v=s(69094),b=s(5787),S=s(90527);let N=e=>e?JSON.parse(e):void 0,E=self.__BUILD_MANIFEST,j=N(self.__REACT_LOADABLE_MANIFEST),P=null==(a=self.__RSC_MANIFEST)?void 0:a["/(protected)/@agent/deposit-request/[trxId]/page"],y=N(self.__RSC_SERVER_MANIFEST),D=N(self.__NEXT_FONT_MANIFEST),A=N(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];P&&y&&(0,b.Mo)({clientReferenceManifest:P,serverActionsManifest:y,serverModuleMap:(0,S.w)({serverActionsManifest:y,pageName:"/(protected)/@agent/deposit-request/[trxId]/page"})});let _=(0,i.d)({pagesType:v.s.APP,dev:!1,page:"/(protected)/@agent/deposit-request/[trxId]/page",appMod:null,pageMod:r,errorMod:null,error500Mod:null,Document:null,buildManifest:E,renderToHTML:c.f,reactLoadableManifest:j,clientReferenceManifest:P,serverActionsManifest:y,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:D,incrementalCacheHandler:null,interceptionRouteRewrites:A}),I=r;function M(e){return(0,n.C)({...e,IncrementalCache:o.k,handler:_})}},81161:(e,t,s)=>{Promise.resolve().then(s.bind(s,1819))},1819:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D,runtime:()=>y});var a=s(60926),r=s(58387),n=s(29411),i=s(62797),o=s(36162),c=s(74988),l=s(1181),d=s(25694);async function m(e){try{let t=await l.Z.put(`/deposit-requests/accept/${e}`,{id:e});return(0,d.B)(t)}catch(e){return(0,d.D)(e)}}async function p(e){try{let t=await l.Z.put(`/deposit-requests/decline/${e}`,{id:e});return(0,d.B)(t)}catch(e){return(0,d.D)(e)}}var u=s(43291),x=s(65091),g=s(3632),f=s(14455),h=s(37988),v=s(90543),b=s(51018),S=s(30684),N=s(31949),E=s(64947),j=s(39228),P=s(32167);let y="edge";function D(){let{t:e}=(0,j.$G)(),t=(0,E.UO)(),{data:s,isLoading:l,mutate:d}=(0,u.d)(`/transactions/trx/${t.trxId}`);if(l)return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(n.Loader,{})});let y=t=>{P.toast.promise(m(t),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return d(s),e.message},error:e=>e.message})},D=t=>{P.toast.promise(p(t),{loading:e("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return d(s),e.message},error:e=>e.message})},A=s?.data?new g.C(s?.data):null,_=new x.F;return A?(0,a.jsx)("div",{className:"mb-10 p-2 sm:mb-0 sm:p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,a.jsx)("div",{className:"col-span-12 lg:col-span-7",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,a.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,a.jsx)(r.J,{condition:"completed"===A.status,children:(0,a.jsx)(v.Z,{variant:"Bulk",size:32,className:"text-success"})}),(0,a.jsx)(r.J,{condition:"failed"===A.status,children:(0,a.jsx)(b.Z,{variant:"Bulk",size:32,className:"text-destructive"})}),(0,a.jsx)(r.J,{condition:"pending"===A.status,children:(0,a.jsx)(S.Z,{variant:"Bulk",size:32,className:"text-primary"})}),(0,a.jsxs)("h2",{className:"font-semibold",children:[e("Deposit"),"#",t.depositId]})]}),(0,a.jsx)(i.z,{senderAvatar:(0,x.qR)(A.from.image),senderName:A.from.label,senderInfo:[A.from?.email,A?.from?.phone],receiverAvatar:(0,x.qR)(A?.to?.image),receiverName:A?.to?.label,receiverInfo:[A?.to?.email,A?.to?.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Date")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:A?.createdAt?(0,f.WU)(A.createdAt,"dd MMM yyyy; hh:mm a"):""})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Amount")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:_.formatVC(A.amount,A.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Service charge")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:_.formatVC(A.fee,A.metaData.currency)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("User gets")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-semibold sm:text-base",children:_.formatVC(A.total,A.metaData.currency)})]})]}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Transaction ID")}),(0,a.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[A.trxId,(0,a.jsx)(o.z,{type:"button",onClick:()=>(0,x.Fp)(A.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,a.jsx)(N.Z,{size:"20"})})]})]})}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),"pending"===A.status?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,a.jsxs)(o.z,{type:"button",onClick:()=>y(A?.id),className:"gap-1 rounded-lg bg-spacial-green px-4 py-2 font-medium text-background hover:bg-[#219621] hover:text-background",children:[(0,a.jsx)(v.Z,{}),e("Approve")]}),(0,a.jsxs)(o.z,{type:"button",onClick:()=>D(A?.id),className:"gap-1 rounded-lg bg-[#D13438] px-4 py-2 font-medium text-white hover:bg-[#a5272b] hover:text-white",children:[(0,a.jsx)(b.Z,{}),e("Reject")]})]}):null]})}),(0,a.jsx)("div",{className:"col-span-12 lg:col-span-5",children:(0,a.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("h2",{children:e("Method info")})}),(0,a.jsx)(c.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Method used")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:A?.metaData?.agentMethod??"--"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Number")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:A?.metaData?.value??"--"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,a.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:e("Wallet")}),(0,a.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:A?.metaData?.currency??"undefine"})]})]})]})})]})}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,a.jsx)(h.Z,{}),e("No data found")]})}},66344:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}},330:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,runtime:()=>r});var a=s(18264);let r=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\deposit-request\[trxId]\page.tsx#runtime`),n=(0,a.D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@agent\deposit-request\[trxId]\page.tsx#default`)},9697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(42416),r=s(21237);function n(){return(0,a.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,a.jsx)(r.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,2682,7827,7283,5089,7839],()=>t(97561));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@agent/deposit-request/[trxId]/page"]=s}]);
//# sourceMappingURL=page.js.map