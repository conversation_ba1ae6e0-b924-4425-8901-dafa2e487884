(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[99906],{75553:function(e,t,r){Promise.resolve().then(r.bind(r,36485))},36485:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return S},runtime:function(){return R}});var n=r(57437),a=r(85487),s=r(62869),i=r(15681),o=r(95186),l=r(26815),c=r(74991),u=r(1828),d=r(79981),f=r(97751);async function m(e,t){try{let r=await d.Z.put("/admin/external-plugins/".concat(t),e);return(0,f.B)(r)}catch(e){return(0,f.D)(e)}}var v=r(31117),p=r(94508),x=r(13590),g=r(32234),h=r(22291),b=r(27648),y=r(99376),j=r(2265),N=r(29501),w=r(43949),C=r(14438),k=r(31229);let I=e=>{let t={};return null==e||e.forEach(e=>{t[e.key]=e.required?k.z.string().min(1,{message:"".concat(e.label," is required")}):k.z.string().optional()}),k.z.object({active:k.z.boolean().default(!1),...t})};function P(e){var t,r;let{plugin:d,onMutate:f}=e,k=(0,y.useParams)(),P=(0,y.useSearchParams)().get("name"),[Z,R]=(0,j.useTransition)(),{data:S,isLoading:F}=(0,v.d)("/admin/external-plugins/config"),{t:E}=(0,w.$G)(),z=null==S?void 0:null===(t=S.data)||void 0===t?void 0:t[P],U=null==S?void 0:null===(r=S.data)||void 0===r?void 0:r[P].fields,_=I(U),L=(0,N.cI)({resolver:(0,x.F)(_),defaultValues:{active:!!(null==d?void 0:d.active)}});if((0,j.useEffect)(()=>{if(!F&&U&&d){let e={};U.forEach(t=>{e[t.key]=d[t.key]||""});let t={active:!!d.active,...e};L.reset(t)}},[F,U,d]),F)return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(a.Loader,{})});let T=e=>(null==e?void 0:e.type)==="select"?(0,n.jsx)(i.Wi,{control:L.control,name:null==e?void 0:e.key,render:t=>{let{field:r}=t;return(0,n.jsxs)(i.xJ,{children:[(0,n.jsx)(i.lX,{children:E(null==e?void 0:e.label)}),(0,n.jsx)(i.NI,{children:(0,n.jsx)(c.E,{defaultValue:r.value,onValueChange:r.onChange,className:"grid-cols-12 gap-4",children:e.options.map(e=>(0,n.jsxs)(l.Z,{htmlFor:e.value,"data-active":r.value===e.value,className:"col-span-12 flex h-12 cursor-pointer items-center justify-start rounded-xl border-[3px] border-border bg-input-disabled p-4 text-sm font-semibold leading-5 transition-all duration-300 ease-in-out hover:border-primary-selected hover:bg-primary-selected data-[active=true]:border-primary data-[active=true]:bg-primary-selected md:col-span-6",children:[(0,n.jsx)(c.m,{id:e.value,value:e.value,className:"absolute left-0 top-0 opacity-0"}),(0,n.jsx)("span",{children:E(e.label)})]},e.value))})}),(0,n.jsx)(i.zG,{})]})}}):(0,n.jsx)(i.Wi,{name:null==e?void 0:e.key,control:L.control,render:t=>{let{field:r}=t;return(0,n.jsxs)(i.xJ,{className:"mt-2",children:[(0,n.jsx)(i.lX,{children:E(null==e?void 0:e.label)}),(0,n.jsx)(i.NI,{children:(0,n.jsx)(o.I,{type:null==e?void 0:e.type,placeholder:E("Enter {{label}}",{label:null==e?void 0:e.label}),...r})}),(0,n.jsx)(i.zG,{})]})}},null==e?void 0:e.key);return(0,n.jsxs)("div",{className:"mb-4 rounded-xl border border-border bg-background px-4",children:[(0,n.jsxs)("div",{className:"flex justify-between py-6 hover:no-underline",children:[(0,n.jsxs)("div",{className:"flex flex-col items-start",children:[(0,n.jsx)("p",{className:"mb-1 text-base font-medium leading-[22px]",children:(0,p.fl)(null==d?void 0:d.name)}),(0,n.jsx)("p",{className:"text-sm text-secondary-text",children:null==z?void 0:z.description})]}),(0,n.jsxs)(b.default,{href:null==z?void 0:z.documentation,target:"_blank",rel:"noreferrer",className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text underline-offset-4 transition duration-300 ease-out hover:text-primary hover:underline",children:[(0,n.jsx)(g.Z,{size:20,variant:"Bulk"}),(0,n.jsx)("span",{className:"text-inherit",children:E("Read Documentation")})]})]}),(0,n.jsx)("div",{className:"gap-4 border-t py-4",children:(0,n.jsx)(i.l0,{...L,children:(0,n.jsxs)("form",{onSubmit:L.handleSubmit(e=>{R(async()=>{let t=await m(e,null==k?void 0:k.pluginId);t.status?(f(),C.toast.success(t.message)):C.toast.error(E(t.message))})}),className:"flex flex-col gap-6 px-1",children:[null==U?void 0:U.map(e=>T(e)),(0,n.jsx)(i.Wi,{name:"active",control:L.control,render:e=>{let{field:t}=e;return(0,n.jsxs)(i.xJ,{className:"space-y-auto flex flex-row items-center gap-2",children:[(0,n.jsx)(l.Z,{className:"space-y-auto m-0 mb-0 mt-2 h-auto w-[130px] py-0 text-sm font-semibold",children:E("Active")}),(0,n.jsx)(i.NI,{children:(0,n.jsx)(u.Z,{defaultChecked:t.value,onCheckedChange:t.onChange})}),(0,n.jsx)(i.zG,{})]})}}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)(s.z,{className:"rounded-lg",children:Z?(0,n.jsx)(a.Loader,{title:E("Updating..."),className:"text-primary-foreground"}):(0,n.jsxs)(n.Fragment,{children:[E("Update plugin"),(0,n.jsx)(h.Z,{size:20})]})})})]})})})]})}var Z=r(85323);let R="edge";function S(){let e=(0,y.useParams)(),{data:t,isLoading:r,mutate:s}=(0,Z.ZP)("/admin/external-plugins/".concat(e.pluginId),e=>(0,d.Z)(e));if(r)return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(a.Loader,{})});let i=null==t?void 0:t.data;return(0,n.jsx)(P,{plugin:i,onMutate:s})}},85487:function(e,t,r){"use strict";r.d(t,{Loader:function(){return i}});var n=r(57437),a=r(94508),s=r(43949);function i(e){let{title:t="Loading...",className:r}=e,{t:i}=(0,s.$G)();return(0,n.jsxs)("div",{className:(0,a.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:i(t)})]})}},62869:function(e,t,r){"use strict";r.d(t,{d:function(){return l},z:function(){return c}});var n=r(57437),a=r(37053),s=r(90535),i=r(2265),o=r(94508);let l=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:c=!1,...u}=e,d=c?a.g7:"button";return(0,n.jsx)(d,{className:(0,o.ZP)(l({variant:s,size:i,className:r})),ref:t,...u})});c.displayName="Button"},15681:function(e,t,r){"use strict";r.d(t,{NI:function(){return x},Wi:function(){return d},l0:function(){return c},lX:function(){return p},xJ:function(){return v},zG:function(){return g}});var n=r(57437),a=r(37053),s=r(2265),i=r(29501),o=r(26815),l=r(94508);let c=i.RV,u=s.createContext({}),d=e=>{let{...t}=e;return(0,n.jsx)(u.Provider,{value:{name:t.name},children:(0,n.jsx)(i.Qr,{...t})})},f=()=>{let e=s.useContext(u),t=s.useContext(m),{getFieldState:r,formState:n}=(0,i.Gc)(),a=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...a}},m=s.createContext({}),v=s.forwardRef((e,t)=>{let{className:r,...a}=e,i=s.useId();return(0,n.jsx)(m.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:t,className:(0,l.ZP)("space-y-2",r),...a})})});v.displayName="FormItem";let p=s.forwardRef((e,t)=>{let{className:r,required:a,...s}=e,{error:i,formItemId:c}=f();return(0,n.jsx)("span",{children:(0,n.jsx)(o.Z,{ref:t,className:(0,l.ZP)(i&&"text-base font-medium text-destructive",r),htmlFor:c,...s})})});p.displayName="FormLabel";let x=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:i,formDescriptionId:o,formMessageId:l}=f();return(0,n.jsx)(a.g7,{ref:t,id:i,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...r})});x.displayName="FormControl",s.forwardRef((e,t)=>{let{className:r,...a}=e,{formDescriptionId:s}=f();return(0,n.jsx)("p",{ref:t,id:s,className:(0,l.ZP)("text-sm text-muted-foreground",r),...a})}).displayName="FormDescription";let g=s.forwardRef((e,t)=>{let{className:r,children:a,...s}=e,{error:i,formMessageId:o}=f(),c=i?String(null==i?void 0:i.message):a;return c?(0,n.jsx)("p",{ref:t,id:o,className:(0,l.ZP)("text-sm font-medium text-destructive",r),...s,children:c}):null});g.displayName="FormMessage"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var n=r(57437),a=r(2265),s=r(94508);let i=a.forwardRef((e,t)=>{let{className:r,type:a,...i}=e;return(0,n.jsx)("input",{type:a,className:(0,s.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",r),ref:t,...i})});i.displayName="Input"},26815:function(e,t,r){"use strict";var n=r(57437),a=r(6394),s=r(90535),i=r(2265),o=r(94508);let l=(0,s.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.f,{ref:t,className:(0,o.ZP)(l(),r),...s})});c.displayName=a.f.displayName,t.Z=c},74991:function(e,t,r){"use strict";r.d(t,{E:function(){return l},m:function(){return c}});var n=r(57437),a=r(2265),s=r(42325),i=r(40519),o=r(94508);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.fC,{className:(0,o.ZP)("grid gap-2",r),...a,ref:t})});l.displayName=s.fC.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(s.ck,{ref:t,className:(0,o.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...a,children:(0,n.jsx)(s.z$,{className:"flex items-center justify-center",children:(0,n.jsx)(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});c.displayName=s.ck.displayName},1828:function(e,t,r){"use strict";var n=r(57437),a=r(50721),s=r(2265),i=r(94508);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.fC,{className:(0,i.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",r),...s,ref:t,children:(0,n.jsx)(a.bU,{className:(0,i.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});o.displayName=a.fC.displayName,t.Z=o},97751:function(e,t,r){"use strict";r.d(t,{B:function(){return a},D:function(){return s}});var n=r(43577);function a(e){var t,r,n;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(n=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==n?n:"",data:null===(r=e.data)||void 0===r?void 0:r.data}}function s(e){let t=500,r="Internal Server Error",a="An unknown error occurred";if((0,n.IZ)(e)){var s,i,o,l,c,u,d,f,m,v,p,x;t=null!==(m=null===(s=e.response)||void 0===s?void 0:s.status)&&void 0!==m?m:500,r=null!==(v=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==v?v:"Internal Server Error",a=null!==(x=null!==(p=null===(u=e.response)||void 0===u?void 0:null===(c=u.data)||void 0===c?void 0:null===(l=c.messages)||void 0===l?void 0:null===(o=l[0])||void 0===o?void 0:o.message)&&void 0!==p?p:null===(f=e.response)||void 0===f?void 0:null===(d=f.data)||void 0===d?void 0:d.message)&&void 0!==x?x:e.message}else e instanceof Error&&(a=e.message);return{statusCode:t,statusText:r,status:!1,message:a,data:void 0,error:e}}},31117:function(e,t,r){"use strict";r.d(t,{d:function(){return s}});var n=r(79981),a=r(85323);let s=(e,t)=>(0,a.ZP)(e||null,e=>n.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},79981:function(e,t,r){"use strict";var n=r(78040),a=r(83464);t.Z=a.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return n},sp:function(){return a}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},a=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return u},Fg:function(){return m},Fp:function(){return c},Qp:function(){return f},ZP:function(){return o},fl:function(){return l},qR:function(){return d},w4:function(){return v}});var n=r(78040),a=r(61994),s=r(14438),i=r(53335);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,a.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>s.toast.success("Copied to clipboard!")).catch(()=>{s.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let a;let s=void 0===t?this.currencyCode:t;try{a=new Intl.NumberFormat("en-US",{style:"currency",currency:s,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){a=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=a.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:s,o=a.format(e),l=o.substring(i.length).trim();return{currencyCode:s,currencySymbol:i,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",f=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",m=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",v=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",a=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?a.set(n,e):a.delete(n),a}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,93909,27648,38658,40754,92971,95030,1744],function(){return e(e.s=75553)}),_N_E=e.O()}]);