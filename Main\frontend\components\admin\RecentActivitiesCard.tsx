"use client";

import { useTranslation } from "react-i18next";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Clock, 
  User, 
  DollarSign, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Activity
} from "lucide-react";
import { RecentActivities } from "@/types/admin";

interface RecentActivitiesCardProps {
  activities: RecentActivities;
  maxItems?: number;
}

export function RecentActivitiesCard({ activities, maxItems = 10 }: RecentActivitiesCardProps) {
  const { t } = useTranslation();

  // Transform RecentActivities object into a flat array of activities
  const transformActivities = () => {
    const allActivities: Array<{
      id: string;
      type: 'escrow' | 'transaction' | 'fundraising';
      title: string;
      description: string;
      timestamp: string;
      status: 'success' | 'warning' | 'error' | 'info';
      amount?: number;
      currency?: string;
    }> = [];

    // Add escrows
    activities.recentEscrows.forEach((escrow, index) => {
      allActivities.push({
        id: `escrow-${escrow.id || index}`,
        type: 'escrow',
        title: t('New Escrow Created'),
        description: t('Escrow ID: {{id}}', { id: escrow.id }),
        timestamp: escrow.createdAt ? escrow.createdAt.toISOString() : new Date().toISOString(),
        status: 'success',
        amount: escrow.amount,
        currency: escrow.currencyCode
      });
    });

    // Add high value transactions
    activities.recentHighValueTransactions.forEach((transaction, index) => {
      allActivities.push({
        id: `transaction-${transaction.id || index}`,
        type: 'transaction',
        title: t('High Value Transaction'),
        description: t('Transaction ID: {{id}}', { id: transaction.id }),
        timestamp: transaction.createdAt ? transaction.createdAt.toISOString() : new Date().toISOString(),
        status: 'success',
        amount: transaction.amount,
        currency: transaction.currency
      });
    });

    // Add fundraising pools
    activities.recentFundraisingPools.forEach((pool, index) => {
      allActivities.push({
        id: `pool-${pool.id || index}`,
        type: 'fundraising',
        title: t('New Fundraising Pool'),
        description: t('Pool: {{title}}', { title: pool.title }),
        timestamp: pool.createdAt ? pool.createdAt.toISOString() : new Date().toISOString(),
        status: 'success',
        amount: pool.targetAmount,
        currency: pool.currencyCode
      });
    });

    // Sort by timestamp (most recent first)
    return allActivities.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  };

  const getActivityIcon = (type: 'escrow' | 'transaction' | 'fundraising') => {
    switch (type) {
      case 'transaction':
        return <DollarSign className="h-4 w-4" />;
      case 'escrow':
        return <Shield className="h-4 w-4" />;
      case 'fundraising':
        return <Activity className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: 'success' | 'warning' | 'error' | 'info') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'info':
        return <Activity className="h-4 w-4 text-blue-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadgeColor = (status: 'success' | 'warning' | 'error' | 'info') => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'info':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return t('Just now');
    if (diffInMinutes < 60) return t('{{minutes}}m ago', { minutes: diffInMinutes });
    if (diffInMinutes < 1440) return t('{{hours}}h ago', { hours: Math.floor(diffInMinutes / 60) });
    return date.toLocaleDateString();
  };

  const getAvatarFallback = (type: 'escrow' | 'transaction' | 'fundraising') => {
    switch (type) {
      case 'transaction':
        return 'T';
      case 'escrow':
        return 'E';
      case 'fundraising':
        return 'F';
      default:
        return 'A';
    }
  };

  const allActivities = transformActivities();
  const displayedActivities = allActivities.slice(0, maxItems);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>{t('Recent Activities')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayedActivities.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>{t('No recent activities')}</p>
            </div>
          ) : (
            displayedActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-xs">
                    {getAvatarFallback(activity.type)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getActivityIcon(activity.type)}
                      <p className="text-sm font-medium truncate">
                        {activity.title}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(activity.status)}
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getStatusBadgeColor(activity.status)}`}
                      >
                        {t(activity.status)}
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-xs text-muted-foreground mt-1">
                    {activity.description}
                  </p>
                  
                  {activity.amount && (
                    <div className="flex items-center space-x-1 mt-1">
                      <DollarSign className="h-3 w-3 text-muted-foreground" />
                      <span className="text-xs font-medium">
                        {activity.amount.toLocaleString()} {activity.currency}
                      </span>
                    </div>
                  )}
                  
                                     <div className="flex items-center space-x-2 mt-2">
                     <Clock className="h-3 w-3 text-muted-foreground" />
                     <span className="text-xs text-muted-foreground">
                       {formatTimestamp(activity.timestamp)}
                     </span>
                   </div>
                </div>
              </div>
            ))
          )}
        </div>
        
                 {allActivities.length > maxItems && (
           <div className="mt-4 pt-4 border-t">
             <p className="text-xs text-muted-foreground text-center">
               {t('Showing {{count}} of {{total}} activities', { 
                 count: maxItems, 
                 total: allActivities.length 
               })}
             </p>
           </div>
         )}
      </CardContent>
    </Card>
  );
} 