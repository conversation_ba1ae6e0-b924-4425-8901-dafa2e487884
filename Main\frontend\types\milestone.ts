import { format } from "date-fns";

// Milestone status types
export type MilestoneStatus = 
  | 'pending' 
  | 'in_progress' 
  | 'completed' 
  | 'approved' 
  | 'rejected' 
  | 'released';

// Evidence types
export type EvidenceType = 'image' | 'document' | 'video' | 'link';

// Evidence interface
export interface Evidence {
  type: EvidenceType;
  url: string;
  description?: string;
  filename?: string;
}

// Milestone interface
export interface IMilestone {
  id: number;
  escrowId: number;
  title: string;
  description?: string;
  orderIndex: number;
  amount: number;
  percentage: number;
  status: MilestoneStatus;
  dueDate?: Date;
  completedAt?: Date;
  releasedAt?: Date;
  senderApproved: boolean;
  recipientApproved: boolean;
  senderApprovedAt?: Date;
  recipientApprovedAt?: Date;
  rejectionReason?: string;
  completionNotes?: string;
  deliverables?: string[];
  evidence?: Evidence[];
  createdAt: Date;
  updatedAt: Date;
  
  // Computed properties
  isOverdue?: boolean;
  canBeCompleted?: boolean;
  canBeApproved?: boolean;
  canBeReleased?: boolean;
  timeRemaining?: string;
  getCreatedAt?: (formatStr?: string) => string;
  getUpdatedAt?: (formatStr?: string) => string;
  getDueDate?: (formatStr?: string) => string;
  getCompletedAt?: (formatStr?: string) => string;
  getReleasedAt?: (formatStr?: string) => string;
}

// Milestone completion data
export interface CompleteMilestoneData {
  completionNotes?: string;
  evidence?: Evidence[];
}

// Milestone approval data
export interface ApproveMilestoneData {
  userType: 'sender' | 'recipient';
}

// Milestone rejection data
export interface RejectMilestoneData {
  rejectionReason: string;
}

// Update deliverables data
export interface UpdateDeliverablesData {
  deliverables: string[];
}

// Submit evidence data
export interface SubmitEvidenceData {
  evidence: Evidence[];
}

// Milestone progress interface
export interface MilestoneProgress {
  totalMilestones: number;
  completedMilestones: number;
  approvedMilestones: number;
  releasedMilestones: number;
  rejectedMilestones: number;
  pendingMilestones: number;
  overdueMilestones: number;
  progressPercentage: number;
  completionPercentage: number;
  releasePercentage: number;
  amountReleased: number;
  amountApproved: number;
  amountCompleted: number;
  amountPending: number;
  totalAmount: number;
  releasePercentageByAmount: number;
  approvalPercentageByAmount: number;
  timeline: MilestoneTimelineItem[];
  nextMilestone: NextMilestone | null;
  isComplete: boolean;
}

// Timeline item interface
export interface MilestoneTimelineItem {
  milestoneId: number;
  title: string;
  orderIndex: number;
  amount: number;
  percentage: number;
  status: MilestoneStatus;
  dueDate?: Date;
  completedAt?: Date;
  releasedAt?: Date;
  isOverdue: boolean;
  events: MilestoneEvent[];
}

// Milestone event interface
export interface MilestoneEvent {
  type: string;
  timestamp: Date;
  description: string;
}

// Next milestone interface
export interface NextMilestone {
  id: number;
  title: string;
  orderIndex: number;
  amount: number;
  percentage: number;
  status: MilestoneStatus;
  dueDate?: Date;
  isOverdue: boolean;
  canBeCompleted: boolean;
  canBeApproved: boolean;
  canBeReleased: boolean;
  description?: string;
  deliverables?: string[];
  evidence?: Evidence[];
}

// Performance metrics interface
export interface MilestonePerformanceMetrics {
  completedMilestones: number;
  completedOnTime: number;
  completedLate: number;
  onTimePercentage: number;
  averageDaysToComplete: number;
  totalMilestones: number;
}

// Upcoming deadline interface
export interface UpcomingDeadline {
  id: number;
  title: string;
  orderIndex: number;
  amount: number;
  status: MilestoneStatus;
  dueDate: Date;
  daysUntilDue: number;
  isUrgent: boolean;
}

// Milestone class implementation
export class EscrowMilestone implements IMilestone {
  id: number;
  escrowId: number;
  title: string;
  description?: string;
  orderIndex: number;
  amount: number;
  percentage: number;
  status: MilestoneStatus;
  dueDate?: Date;
  completedAt?: Date;
  releasedAt?: Date;
  senderApproved: boolean;
  recipientApproved: boolean;
  senderApprovedAt?: Date;
  recipientApprovedAt?: Date;
  rejectionReason?: string;
  completionNotes?: string;
  deliverables?: string[];
  evidence?: Evidence[];
  createdAt: Date;
  updatedAt: Date;

  constructor(data: any) {
    this.id = data?.id;
    this.escrowId = data?.escrowId;
    this.title = data?.title;
    this.description = data?.description;
    this.orderIndex = data?.orderIndex;
    this.amount = data?.amount || 0;
    this.percentage = data?.percentage || 0;
    this.status = data?.status || 'pending';
    this.dueDate = data?.dueDate ? new Date(data.dueDate) : undefined;
    this.completedAt = data?.completedAt ? new Date(data.completedAt) : undefined;
    this.releasedAt = data?.releasedAt ? new Date(data.releasedAt) : undefined;
    this.senderApproved = Boolean(data?.senderApproved);
    this.recipientApproved = Boolean(data?.recipientApproved);
    this.senderApprovedAt = data?.senderApprovedAt ? new Date(data.senderApprovedAt) : undefined;
    this.recipientApprovedAt = data?.recipientApprovedAt ? new Date(data.recipientApprovedAt) : undefined;
    this.rejectionReason = data?.rejectionReason;
    this.completionNotes = data?.completionNotes;
    this.deliverables = data?.deliverables ? JSON.parse(data.deliverables) : undefined;
    this.evidence = data?.evidence ? JSON.parse(data.evidence) : undefined;
    this.createdAt = new Date(data?.createdAt);
    this.updatedAt = new Date(data?.updatedAt);
  }

  // Computed properties
  get isOverdue(): boolean {
    if (!this.dueDate) return false;
    return new Date() > this.dueDate && !['completed', 'approved', 'released'].includes(this.status);
  }

  get canBeCompleted(): boolean {
    return this.status === 'pending' || this.status === 'in_progress';
  }

  get canBeApproved(): boolean {
    return this.status === 'completed';
  }

  get canBeReleased(): boolean {
    return this.status === 'approved' && this.senderApproved && this.recipientApproved;
  }

  get statusColor(): string {
    switch (this.status) {
      case 'pending': return 'gray';
      case 'in_progress': return 'blue';
      case 'completed': return 'yellow';
      case 'approved': return 'green';
      case 'rejected': return 'red';
      case 'released': return 'emerald';
      default: return 'gray';
    }
  }

  get timeRemaining(): string {
    if (!this.dueDate) return 'No deadline';
    if (this.isOverdue) return 'Overdue';
    
    const now = new Date();
    const diff = this.dueDate.getTime() - now.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} remaining`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} remaining`;
    } else {
      return 'Due soon';
    }
  }

  // Utility methods
  getCreatedAt(formatStr: string = "dd MMM yyyy"): string {
    return format(this.createdAt, formatStr);
  }

  getUpdatedAt(formatStr: string = "dd MMM yyyy"): string {
    return format(this.updatedAt, formatStr);
  }

  getDueDate(formatStr: string = "dd MMM yyyy"): string {
    if (!this.dueDate) return 'No deadline';
    return format(this.dueDate, formatStr);
  }

  getCompletedAt(formatStr: string = "dd MMM yyyy HH:mm"): string {
    if (!this.completedAt) return 'Not completed';
    return format(this.completedAt, formatStr);
  }

  getReleasedAt(formatStr: string = "dd MMM yyyy HH:mm"): string {
    if (!this.releasedAt) return 'Not released';
    return format(this.releasedAt, formatStr);
  }
}
