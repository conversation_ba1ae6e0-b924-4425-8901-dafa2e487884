"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader } from "@/components/ui/card";
import Progress from "@/components/ui/progress";
import Separator from "@/components/ui/separator";
import { Escrow } from "@/types/escrow";
import { imageURL } from "@/lib/utils";
import { 
  Clock, 
  DollarSign, 
  User, 
  Shield, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Eye,
  MoreHorizontal
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface EscrowCardProps {
  escrow: Escrow;
  userRole: 'sender' | 'recipient';
  onAction?: (action: string, escrowId: number) => void;
}

export function EscrowCard({ escrow, userRole, onAction }: EscrowCardProps) {
  const { t } = useTranslation();

  const getStatusIcon = () => {
    switch (escrow.status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'active':
        return <Shield className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      case 'expired':
        return <AlertTriangle className="h-4 w-4" />;
      case 'disputed':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (escrow.status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'active':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'disputed':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const counterparty = userRole === 'sender' ? escrow.recipient : escrow.sender;
  const counterpartyRole = userRole === 'sender' ? 'recipient' : 'sender';

  const canConfirm = escrow.status === 'pending' && 
    ((userRole === 'sender' && !escrow.senderConfirmed) || 
     (userRole === 'recipient' && !escrow.recipientConfirmed));

  const canRelease = escrow.status === 'active' && 
    userRole === 'sender' && 
    escrow.canBeReleased;

  const canCancel = escrow.canBeCancelled && 
    (userRole === 'sender' || escrow.status === 'pending');

  return (
    <Card className="w-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge 
              variant="outline" 
              className={`${getStatusColor()} flex items-center space-x-1`}
            >
              {getStatusIcon()}
              <span className="capitalize">{t(escrow.status)}</span>
            </Badge>
            {escrow.hasMilestones && (
              <Badge variant="secondary" className="text-xs">
                {escrow.completedMilestones}/{escrow.totalMilestones} {t('milestones')}
              </Badge>
            )}
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/escrows/${escrow.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  {t('View Details')}
                </Link>
              </DropdownMenuItem>
              {canConfirm && (
                <DropdownMenuItem onClick={() => onAction?.('confirm', escrow.id)}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('Confirm Escrow')}
                </DropdownMenuItem>
              )}
              {canRelease && (
                <DropdownMenuItem onClick={() => onAction?.('release', escrow.id)}>
                  <DollarSign className="h-4 w-4 mr-2" />
                  {t('Release Funds')}
                </DropdownMenuItem>
              )}
              {canCancel && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onAction?.('cancel', escrow.id)}
                    className="text-red-600"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    {t('Cancel Escrow')}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-lg">
              {escrow.amount.toLocaleString()} {escrow.currencyCode}
            </h3>
            <p className="text-sm text-muted-foreground">
              {t('Escrow ID')}: {escrow.escrowId}
            </p>
          </div>
          
          <div className="text-right">
            <p className="text-sm font-medium">
              {userRole === 'sender' ? t('To') : t('From')}
            </p>
            <div className="flex items-center space-x-2">
              {counterparty?.customer?.avatar && (
                <Image
                  src={imageURL(counterparty.customer.avatar)}
                  alt={counterparty.name}
                  width={24}
                  height={24}
                  className="rounded-full"
                />
              )}
              <span className="text-sm text-muted-foreground">
                {counterparty?.name || t('Unknown User')}
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {escrow.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {escrow.description}
          </p>
        )}

        {escrow.hasMilestones && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>{t('Progress')}</span>
              <span>{escrow.progressPercentage}%</span>
            </div>
            <Progress value={escrow.progressPercentage} className="h-2" />
          </div>
        )}

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">{t('Deadline')}</p>
            <p className="font-medium">{escrow.getDeadline()}</p>
          </div>
          <div>
            <p className="text-muted-foreground">{t('Time Remaining')}</p>
            <p className={`font-medium ${escrow.isExpired ? 'text-red-600' : 'text-green-600'}`}>
              {escrow.timeRemaining}
            </p>
          </div>
        </div>

        {escrow.status === 'pending' && (
          <div className="space-y-2">
            <p className="text-sm font-medium">{t('Confirmation Status')}</p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center space-x-1">
                {escrow.senderConfirmed ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <Clock className="h-3 w-3 text-yellow-600" />
                )}
                <span>{t('Sender')}</span>
              </div>
              <div className="flex items-center space-x-1">
                {escrow.recipientConfirmed ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <Clock className="h-3 w-3 text-yellow-600" />
                )}
                <span>{t('Recipient')}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3">
        <div className="flex items-center justify-between w-full text-xs text-muted-foreground">
          <span>{t('Created')}: {escrow.getCreatedAt()}</span>
          <span>{t('Fee')}: {escrow.fee} {escrow.currencyCode}</span>
        </div>
      </CardFooter>
    </Card>
  );
}
