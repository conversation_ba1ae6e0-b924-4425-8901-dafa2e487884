(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[94551],{66344:function(e,t,r){Promise.resolve().then(r.bind(r,36700))},36700:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return w},runtime:function(){return y}});var n=r(57437),s=r(41709),a=r(85487),i=r(62869),o=r(66070),l=r(6512),c=r(56766),u=r(91083),d=r(19571),m=r(22291),f=r(73490),v=r(76865),x=r(89345),g=r(27648),p=r(2265),h=r(43949);let y="edge";function w(e){let{searchParams:t}=e,{token:r}=t,[y,w]=p.useState(!0),[N,j]=p.useState(),{t:b}=(0,h.$G)();return p.useEffect(()=>{(async()=>{let e=await (0,c.s8)({token:r});e&&e.status?j(!0):j(!1)})()},[]),p.useEffect(()=>{void 0!==N&&w(!1)},[N]),(0,n.jsxs)("div",{className:"container mt-10 max-w-[716px] px-4 py-6",children:[(0,n.jsx)(s.J,{condition:!y&&!0===N,children:(0,n.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,n.jsx)(u.Z,{size:70,variant:"Bulk",className:"text-success"}),(0,n.jsx)("h1",{className:"text-[32px] font-medium leading-10",children:b("Verification Successful")})]}),(0,n.jsx)(l.Z,{className:"mb-[2px] mt-[3px]"}),(0,n.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(d.Z,{size:17,variant:"Bold",className:"text-spacial-green"}),(0,n.jsx)("span",{className:"text-sm font-semibold leading-5",children:b("Account creation")})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(d.Z,{size:17,variant:"Bold",className:"text-spacial-green"}),(0,n.jsx)("span",{className:"text-sm font-semibold leading-5",children:b("Email verification")})]}),(0,n.jsx)("p",{className:"text-sm font-normal leading-[22px]",children:b("Congratulations! Your account has been successfully created and ready to use.")})]}),(0,n.jsx)(i.z,{className:"h-10 max-w-[286px] gap-0.5 rounded-lg text-base font-medium leading-[22px]",asChild:!0,children:(0,n.jsxs)(g.default,{href:"/signin",prefetch:!1,children:[b("Sign in to continue"),(0,n.jsx)(m.Z,{size:"16"})]})})]})}),(0,n.jsx)(s.J,{condition:!y&&!1===N,children:(0,n.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,n.jsx)(f.Z,{size:70,variant:"Bulk",className:"text-primary"}),(0,n.jsx)("h1",{className:"text-[32px] font-medium leading-10",children:b("Verification failed, but don’t worry")})]}),(0,n.jsx)(l.Z,{className:"mb-[2px] mt-[3px]"}),(0,n.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(d.Z,{size:17,variant:"Bold",className:"text-spacial-green"}),(0,n.jsx)("span",{className:"text-sm font-semibold leading-5",children:b("Account creation")})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(v.Z,{size:17,className:"fill-spacial-red text-background"}),(0,n.jsx)("span",{className:"text-sm font-semibold leading-5 text-secondary-text",children:b("Email verification")})]}),(0,n.jsx)("p",{className:"text-sm font-normal leading-[22px]",children:b("Your account has been created but we’ve failed to verify your email. Don’t worry at all, you can sign in again to get a new link anytime.")})]}),(0,n.jsx)(i.z,{className:"h-10 max-w-[286px] gap-0.5 rounded-lg text-base font-medium leading-[22px]",asChild:!0,children:(0,n.jsxs)(g.default,{href:"/signin",prefetch:!1,children:[b("Sign in again to fix it"),(0,n.jsx)(m.Z,{size:"16"})]})})]})}),(0,n.jsx)(s.J,{condition:y,children:(0,n.jsx)("div",{className:"flex h-full w-full flex-1 items-center justify-center",children:(0,n.jsxs)(o.Zb,{className:"w-full max-w-[400px] border-none shadow-none",children:[(0,n.jsxs)(o.Ol,{className:"items-center",children:[(0,n.jsx)(x.Z,{size:48,strokeWidth:1.5}),(0,n.jsx)(o.ll,{className:"mb-1",children:b("Email Verifying...")}),(0,n.jsx)(o.SZ,{className:"text-center",children:b("We are verifying your email address. This might take a few moments.")})]}),(0,n.jsx)(o.aY,{className:"flex items-center justify-center",children:(0,n.jsx)(a.Loader,{title:b("Please wait...")})})]})})})]})}},41709:function(e,t,r){"use strict";function n(e){let{condition:t,children:r}=e;return t?r:null}r.d(t,{J:function(){return n}}),r(2265)},85487:function(e,t,r){"use strict";r.d(t,{Loader:function(){return i}});var n=r(57437),s=r(94508),a=r(43949);function i(e){let{title:t="Loading...",className:r}=e,{t:i}=(0,a.$G)();return(0,n.jsxs)("div",{className:(0,s.ZP)("flex items-center gap-1 text-sm text-foreground",r),children:[(0,n.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"text-inherit",children:i(t)})]})}},62869:function(e,t,r){"use strict";r.d(t,{d:function(){return l},z:function(){return c}});var n=r(57437),s=r(37053),a=r(90535),i=r(2265),o=r(94508);let l=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transition duration-300 ease-in-out",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-300 gap-2",secondary:"bg-secondary rounded text-sm text-secondary-foreground hover:bg-secondary-500 gap-2",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-btn-outline-border bg-transparent hover:bg-accent hover:text-accent-foreground gap-2",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline hover:text-primary"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-2",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:c=!1,...u}=e,d=c?s.g7:"button";return(0,n.jsx)(d,{className:(0,o.ZP)(l({variant:a,size:i,className:r})),ref:t,...u})});c.displayName="Button"},66070:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return u},eW:function(){return d},ll:function(){return l}});var n=r(57437),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.ZP)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.ZP)("flex flex-col space-y-1.5 p-6",r),...s})});o.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,a.ZP)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,a.ZP)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.ZP)("p-6 pt-0",r),...s})});u.displayName="CardContent";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.ZP)("flex items-center p-6 pt-0",r),...s})});d.displayName="CardFooter"},6512:function(e,t,r){"use strict";var n=r(57437),s=r(55156),a=r(2265),i=r(94508);let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:o=!0,...l}=e;return(0,n.jsx)(s.f,{ref:t,decorative:o,orientation:a,className:(0,i.ZP)("shrink-0 bg-divider","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...l})});o.displayName=s.f.displayName,t.Z=o},56766:function(e,t,r){"use strict";r.d(t,{Pq:function(){return c},Pr:function(){return d},jd:function(){return l},o8:function(){return u},s8:function(){return m}});var n=r(2901),s=r(79981),a=r(78040),i=r(43577);let o=e=>{let t={...e,email:e.email,password:e.password,passwordConfirmation:e.confirmPassword,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,firstName:e.firstName,lastName:e.lastName,phone:e.phone,gender:e.title.toLowerCase(),dob:(0,n.WU)(e.dateOfBirth,"yyyy-MM-dd"),roleId:e.accountType,acceptTermsCondition:e.termAndCondition};if(void 0!==e.merchant)return{...t,merchant:{...e.merchant,name:e.merchant.name,email:e.merchant.email,proof:e.merchant.license,addressLine:e.merchant.street,zipCode:e.merchant.zipCode,countryCode:e.merchant.country,city:e.merchant.city}};if(void 0!==e.agent){var r,s,a;return{...t,agent:{...e.agent,proof:"agent",occupation:e.agent.occupation,email:e.email,name:e.agent.name,addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city,whatsapp:e.agent.whatsapp,agreeFundingCustomer:(null===(r=e.agent.fundingByAgentAccount)||void 0===r?void 0:r.toLowerCase())==="yes",agreeHonest:(null===(s=e.agent.honestyAgreement)||void 0===s?void 0:s.toLowerCase())==="yes",agreeRechargeCustomer:(null===(a=e.agent.rechargeAgreement)||void 0===a?void 0:a.toLowerCase())==="yes"}}}return t};async function l(e){var t,r,n,l,c,u,d,m,f,v,x,g,p,h;try{let n=await s.Z.post("".concat(a.rH.API_URL,"/auth/register"),o(e));return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(r=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==r?r:"",data:{email:e.email}}}catch(s){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(s)&&(e=null!==(x=null===(n=s.response)||void 0===n?void 0:n.status)&&void 0!==x?x:500,t=null!==(g=null===(l=s.response)||void 0===l?void 0:l.statusText)&&void 0!==g?g:"Internal Server Error",r=null!==(h=null!==(p=null===(u=s.response)||void 0===u?void 0:null===(c=u.data)||void 0===c?void 0:c.message)&&void 0!==p?p:null===(v=s.response)||void 0===v?void 0:null===(f=v.data)||void 0===f?void 0:null===(m=f.messages)||void 0===m?void 0:null===(d=m[0])||void 0===d?void 0:d.message)&&void 0!==h?h:s.message),{statusCode:e,statusText:t,status:!1,message:r}}}async function c(e){var t,r,n,l,c,u,d,m,f;try{let n=await s.Z.post("".concat(a.rH.API_URL,"/auth/register"),o(e));return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(r=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==r?r:"",data:{email:e.email}}}catch(s){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(s)?(e=null!==(d=null===(n=s.response)||void 0===n?void 0:n.status)&&void 0!==d?d:500,t=null!==(m=null===(l=s.response)||void 0===l?void 0:l.statusText)&&void 0!==m?m:"Internal Server Error",r=null!==(f=null===(u=s.response)||void 0===u?void 0:null===(c=u.data)||void 0===c?void 0:c.message)&&void 0!==f?f:s.message):s instanceof Error&&(r=s.message),{statusCode:e,statusText:t,status:!1,message:r}}}async function u(e){var t,r,n,l,c,u,d,m,f;try{let n=await s.Z.post("".concat(a.rH.API_URL,"/auth/register"),o(e));return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(r=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==r?r:"",data:{email:e.email}}}catch(s){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(s)?(e=null!==(d=null===(n=s.response)||void 0===n?void 0:n.status)&&void 0!==d?d:500,t=null!==(m=null===(l=s.response)||void 0===l?void 0:l.statusText)&&void 0!==m?m:"Internal Server Error",r=null!==(f=null===(u=s.response)||void 0===u?void 0:null===(c=u.data)||void 0===c?void 0:c.message)&&void 0!==f?f:s.message):s instanceof Error&&(r=s.message),{statusCode:e,statusText:t,status:!1,message:r}}}async function d(e){var t,r,n,o,l,c,u,d,m;try{let n=await s.Z.post("".concat(a.rH.API_URL,"/auth/resend-verify-email"),{email:e});return{statusCode:n.status,statusText:n.statusText,status:201===n.status||200===n.status,message:null!==(r=null===(t=n.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(s){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(s)?(e=null!==(u=null===(n=s.response)||void 0===n?void 0:n.status)&&void 0!==u?u:500,t=null!==(d=null===(o=s.response)||void 0===o?void 0:o.statusText)&&void 0!==d?d:"Internal Server Error",r=null!==(m=null===(c=s.response)||void 0===c?void 0:null===(l=c.data)||void 0===l?void 0:l.message)&&void 0!==m?m:s.message):s instanceof Error&&(r=s.message),{statusCode:e,statusText:t,status:!1,message:r}}}async function m(e){var t,r,n,o,l,c,u,d,m;let{token:f}=e;try{let e=await s.Z.post("".concat(a.rH.API_URL,"/auth/verify-email"),{token:f});return{statusCode:e.status,statusText:e.statusText,status:201===e.status||200===e.status,message:null!==(r=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==r?r:""}}catch(s){let e=500,t="Internal Server Error",r="An unknown error occurred";return(0,i.IZ)(s)?(e=null!==(u=null===(n=s.response)||void 0===n?void 0:n.status)&&void 0!==u?u:500,t=null!==(d=null===(o=s.response)||void 0===o?void 0:o.statusText)&&void 0!==d?d:"Internal Server Error",r=null!==(m=null===(c=s.response)||void 0===c?void 0:null===(l=c.data)||void 0===l?void 0:l.message)&&void 0!==m?m:s.message):s instanceof Error&&(r=s.message),{statusCode:e,statusText:t,status:!1,message:r}}}},79981:function(e,t,r){"use strict";var n=r(78040),s=r(83464);t.Z=s.default.create({baseURL:n.rH.API_URL,headers:{"Content-Type":"application/json"},withCredentials:!0})},78040:function(e,t,r){"use strict";r.d(t,{rH:function(){return n},sp:function(){return s}});let n={APP_URL:"http://localhost:3000",API_URL:"http://localhost:8000",SESSION_SECRET:r(40257).env.SESSION_SECRET,get STATIC_URL(){return"".concat(this.API_URL,"/uploads")}},s=["/signin","/signin/2fa","/reset-password","/forgot-password","/forgot-password/mail-send","/mpay","/mpay/review","/register","/register/agent","/register/merchant","/register/customer","/register/email-verification-message","/register/email-verification-status"]},94508:function(e,t,r){"use strict";r.d(t,{F:function(){return u},Fg:function(){return f},Fp:function(){return c},Qp:function(){return m},ZP:function(){return o},fl:function(){return l},qR:function(){return d},w4:function(){return v}});var n=r(78040),s=r(61994),a=r(14438),i=r(53335);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,s.W)(t))}function l(e){return e?e.toLowerCase().split(/[\s_-]+/).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):""}let c=e=>{e&&navigator.clipboard.writeText(e).then(()=>a.toast.success("Copied to clipboard!")).catch(()=>{a.toast.error("Failed to copy!")})};class u{format(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r)}formatVC(e,t){let{currencyCode:r,amountText:n}=this.formatter(e,t);return"".concat(n," ").concat(r," ")}constructor(e){this.formatter=(e,t)=>{var r,n;let s;let a=void 0===t?this.currencyCode:t;try{s=new Intl.NumberFormat("en-US",{style:"currency",currency:a,currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}catch(e){s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",currencySign:"accounting",currencyDisplay:"narrowSymbol",minimumFractionDigits:2})}let i=null!==(n=null===(r=s.formatToParts(e).find(e=>"currency"===e.type))||void 0===r?void 0:r.value)&&void 0!==n?n:a,o=s.format(e),l=o.substring(i.length).trim();return{currencyCode:a,currencySymbol:i,formattedAmount:o,amountText:l}},this.currencyCode=e||"USD"}}let d=e=>e?"".concat(n.rH.STATIC_URL,"/").concat(e):"",m=e=>e?"".concat(n.rH.API_URL,"/").concat(e):"",f=e=>e?(null==e?void 0:e.match(/^\+/))?e:"+".concat(e):"",v=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"search",s=new URLSearchParams(null===(r=window)||void 0===r?void 0:null===(t=r.location)||void 0===t?void 0:t.search);return e?s.set(n,e):s.delete(n),s}}},function(e){e.O(0,[14438,31304,83464,27648,2901,2353,92971,95030,1744],function(){return e(e.s=66344)}),_N_E=e.O()}]);