(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[85210],{36887:function(e,t,n){"use strict";n.d(t,{Z:function(){return m}});var l=n(74677),o=n(2265),r=n(40718),a=n.n(r),i=["variant","color","size"],u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M17.919 8.18H6.079c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M16.01 12.85l-2.62 2.62c-.77.77-2.03.77-2.8 0L4.08 8.95M19.92 8.95l-1.04 1.04"}))},g=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M15.48 13.23l-3.79-5.05H6.08c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97z",opacity:".4"}),o.createElement("path",{fill:t,d:"M17.92 8.18h-6.23l3.79 5.05 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M12 16.8c-.7 0-1.4-.27-1.93-.8L3.55 9.48a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.48.48 1.26.48 1.74 0l6.52-6.52c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L13.93 16c-.53.53-1.23.8-1.93.8z"}))},p=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},f=function(e,t){switch(e){case"Bold":return o.createElement(u,{color:t});case"Broken":return o.createElement(s,{color:t});case"Bulk":return o.createElement(g,{color:t});case"Linear":default:return o.createElement(c,{color:t});case"Outline":return o.createElement(d,{color:t});case"TwoTone":return o.createElement(p,{color:t})}},m=(0,o.forwardRef)(function(e,t){var n=e.variant,r=e.color,a=e.size,u=(0,l._)(e,i);return o.createElement("svg",(0,l.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),f(n,r))});m.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="ArrowDown2"},64394:function(e,t,n){"use strict";n.d(t,{Z:function(){return m}});var l=n(74677),o=n(2265),r=n(40718),a=n.n(r),i=["variant","color","size"],u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2zM18 12.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M12.82 12H3.5M20.33 12h-3.48"}))},g=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M7.81 2h8.37C19.83 2 22 4.17 22 7.81v8.37c0 3.64-2.17 5.81-5.81 5.81H7.81C4.17 22 2 19.83 2 16.19V7.81C2 4.17 4.17 2 7.81 2z",opacity:".4"}),o.createElement("path",{fill:t,d:"M5.47 11.47l4.29-4.29c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-3.01 3.01H18c.41 0 .75.34.75.75s-.34.75-.75.75H7.81l3.01 3.01c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22s-.38-.07-.53-.22l-4.29-4.29a.75.75 0 010-1.06z"}))},c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07M20.5 12H3.67"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M9.57 18.82c-.19 0-.38-.07-.53-.22l-6.07-6.07a.754.754 0 010-1.06L9.04 5.4c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L4.56 12l5.54 5.54c.29.29.29.77 0 1.06-.14.15-.34.22-.53.22z"}),o.createElement("path",{fill:t,d:"M20.5 12.75H3.67c-.41 0-.75-.34-.75-.75s.34-.75.75-.75H20.5c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},p=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M9.57 5.93L3.5 12l6.07 6.07"}),o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M20.5 12H3.67",opacity:".4"}))},f=function(e,t){switch(e){case"Bold":return o.createElement(u,{color:t});case"Broken":return o.createElement(s,{color:t});case"Bulk":return o.createElement(g,{color:t});case"Linear":default:return o.createElement(c,{color:t});case"Outline":return o.createElement(d,{color:t});case"TwoTone":return o.createElement(p,{color:t})}},m=(0,o.forwardRef)(function(e,t){var n=e.variant,r=e.color,a=e.size,u=(0,l._)(e,i);return o.createElement("svg",(0,l.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),f(n,r))});m.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="ArrowLeft"},61756:function(e,t,n){"use strict";n.d(t,{Z:function(){return m}});var l=n(74677),o=n(2265),r=n(40718),a=n.n(r),i=["variant","color","size"],u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2zm2.34 10.53l-4.29 4.29c-.15.15-.34.22-.53.22s-.38-.07-.53-.22a.754.754 0 010-1.06l3.01-3.01H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h10.19l-3.01-3.01a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l4.29 4.29a.75.75 0 010 1.06z"}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M14.43 5.93L20.5 12l-6.07 6.07M11.01 12h9.32M3.5 12h3.47"}))},g=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2z",opacity:".4"}),o.createElement("path",{fill:t,d:"M18.53 11.47l-4.29-4.29a.754.754 0 00-1.06 0c-.29.29-.29.77 0 1.06l3.01 3.01H6c-.41 0-.75.34-.75.75s.34.75.75.75h10.19l-3.01 3.01c-.29.29-.29.77 0 1.06.15.15.34.22.53.22s.38-.07.53-.22l4.29-4.29a.75.75 0 000-1.06z"}))},c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M14.43 5.93L20.5 12l-6.07 6.07M3.5 12h16.83"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{fill:t,d:"M14.43 18.82c-.19 0-.38-.07-.53-.22a.754.754 0 010-1.06L19.44 12 13.9 6.46a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.07 6.07c.29.29.29.77 0 1.06l-6.07 6.07c-.15.15-.34.22-.53.22z"}),o.createElement("path",{fill:t,d:"M20.33 12.75H3.5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h16.83c.41 0 .75.34.75.75s-.34.75-.75.75z"}))},p=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M14.43 5.93L20.5 12l-6.07 6.07"}),o.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M3.5 12h16.83",opacity:".4"}))},f=function(e,t){switch(e){case"Bold":return o.createElement(u,{color:t});case"Broken":return o.createElement(s,{color:t});case"Bulk":return o.createElement(g,{color:t});case"Linear":default:return o.createElement(c,{color:t});case"Outline":return o.createElement(d,{color:t});case"TwoTone":return o.createElement(p,{color:t})}},m=(0,o.forwardRef)(function(e,t){var n=e.variant,r=e.color,a=e.size,u=(0,l._)(e,i);return o.createElement("svg",(0,l.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),f(n,r))});m.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="ArrowRight"},73490:function(e,t,n){"use strict";n.d(t,{Z:function(){return m}});var l=n(74677),o=n(2265),r=n(40718),a=n.n(r),i=["variant","color","size"],u=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"m19.51 5.85-5.94-3.43c-.97-.56-2.17-.56-3.15 0L4.49 5.85a3.15 3.15 0 0 0-1.57 2.73v6.84c0 1.12.6 2.16 1.57 2.73l5.94 3.43c.97.56 2.17.56 3.15 0l5.94-3.43a3.15 3.15 0 0 0 1.57-2.73V8.58a3.192 3.192 0 0 0-1.58-2.73Zm-8.26 1.9c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75s-.75-.34-.75-.75V7.75Zm1.67 8.88c-.05.12-.12.23-.21.33a.99.99 0 0 1-1.09.21c-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38Z",fill:t}))},s=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 7.75V13M2.92 8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.56 1.57 1.6 1.57 2.73v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73v-2.76",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M12 16.2v.1",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},g=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{opacity:".4",d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",fill:t}),o.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.249c-.13 0-.26-.03-.38-.08-.13-.05-.23-.12-.33-.21-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.1-.52.29-.71.1-.09.2-.16.33-.21.37-.16.81-.07 1.09.21.09.1.16.2.21.33.05.12.08.25.08.38s-.03.26-.08.38-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:t}))},c=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 7.75V13M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M12 16.2v.1",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{d:"M12 13.75c-.41 0-.75-.34-.75-.75V7.75c0-.41.34-.75.75-.75s.75.34.75.75V13c0 .41-.34.75-.75.75ZM12 17.25a.99.99 0 0 1-.71-.29c-.09-.1-.16-.21-.22-.33a.986.986 0 0 1-.07-.38c0-.26.11-.52.29-.71.37-.37 1.05-.37 1.42 0 .18.19.29.45.29.71 0 .13-.03.26-.08.38s-.12.23-.21.33a.99.99 0 0 1-.71.29Z",fill:t}),o.createElement("path",{d:"M12 22.751c-.67 0-1.35-.17-1.95-.52l-5.94-3.43c-1.2-.7-1.95-1.99-1.95-3.38v-6.84c0-1.39.75-2.68 1.95-3.38l5.94-3.43c1.2-.7 2.69-.7 3.9 0l5.94 3.43c1.2.7 1.95 1.99 1.95 3.38v6.84c0 1.39-.75 2.68-1.95 3.38l-5.94 3.43c-.6.35-1.28.52-1.95.52Zm0-20c-.41 0-.83.11-1.2.32l-5.94 3.43c-.74.43-1.2 1.22-1.2 2.08v6.84c0 .85.46 1.65 1.2 2.08l5.94 3.43c.74.43 1.66.43 2.39 0l5.94-3.43c.74-.43 1.2-1.22 1.2-2.08v-6.84c0-.85-.46-1.65-1.2-2.08l-5.94-3.43c-.36-.21-.78-.32-1.19-.32Z",fill:t}))},p=function(e){var t=e.color;return o.createElement(o.Fragment,null,o.createElement("path",{opacity:".4",d:"M12 7.75V13",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M21.08 8.58v6.84c0 1.12-.6 2.16-1.57 2.73l-5.94 3.43c-.97.56-2.17.56-3.15 0l-5.94-3.43a3.15 3.15 0 0 1-1.57-2.73V8.58c0-1.12.6-2.16 1.57-2.73l5.94-3.43c.97-.56 2.17-.56 3.15 0l5.94 3.43c.97.57 1.57 1.6 1.57 2.73Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{opacity:".4",d:"M12 16.2v.1",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))},f=function(e,t){switch(e){case"Bold":return o.createElement(u,{color:t});case"Broken":return o.createElement(s,{color:t});case"Bulk":return o.createElement(g,{color:t});case"Linear":default:return o.createElement(c,{color:t});case"Outline":return o.createElement(d,{color:t});case"TwoTone":return o.createElement(p,{color:t})}},m=(0,o.forwardRef)(function(e,t){var n=e.variant,r=e.color,a=e.size,u=(0,l._)(e,i);return o.createElement("svg",(0,l.a)({},u,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:a,height:a,viewBox:"0 0 24 24",fill:"none"}),f(n,r))});m.propTypes={variant:a().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:a().string,size:a().oneOfType([a().string,a().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="Warning2"},99376:function(e,t,n){"use strict";var l=n(35475);n.o(l,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return l.permanentRedirect}}),n.o(l,"useParams")&&n.d(t,{useParams:function(){return l.useParams}}),n.o(l,"usePathname")&&n.d(t,{usePathname:function(){return l.usePathname}}),n.o(l,"useRouter")&&n.d(t,{useRouter:function(){return l.useRouter}}),n.o(l,"useSearchParams")&&n.d(t,{useSearchParams:function(){return l.useSearchParams}}),n.o(l,"useSelectedLayoutSegment")&&n.d(t,{useSelectedLayoutSegment:function(){return l.useSelectedLayoutSegment}}),n.o(l,"useSelectedLayoutSegments")&&n.d(t,{useSelectedLayoutSegments:function(){return l.useSelectedLayoutSegments}})},4751:function(e,t,n){"use strict";n.d(t,{Z:function(){return z}});var l=n(73882);function o(e,t,n){return(t=(0,l.Z)(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var r=n(1119),a=n(41154);function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,l)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach(function(t){o(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var s=n(29062);function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var l,o,r,a,i=[],u=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(l=r.call(n)).done)&&(i.push(l.value),i.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return i}}(e,t)||(0,s.Z)(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var c=n(36760),d=n.n(c),p=n(2265);function f(e){var t=p.useRef();return t.current=e,p.useCallback(function(){for(var e,n=arguments.length,l=Array(n),o=0;o<n;o++)l[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(l))},[])}var m="undefined"!=typeof window&&window.document&&window.document.createElement?p.useLayoutEffect:p.useEffect,v=function(e,t){var n=p.useRef(!0);m(function(){return e(n.current)},t),m(function(){return n.current=!1,function(){n.current=!0}},[])},h=function(e,t){v(function(t){if(!t)return e()},t)};function C(e){var t=p.useRef(!1),n=g(p.useState(e),2),l=n[0],o=n[1];return p.useEffect(function(){return t.current=!1,function(){t.current=!0}},[]),[l,function(e,n){n&&t.current||o(e)}]}function S(e){return void 0!==e}function w(e,t){var n=t||{},l=n.defaultValue,o=n.value,r=n.onChange,a=n.postState,i=g(C(function(){return S(o)?o:S(l)?"function"==typeof l?l():l:"function"==typeof e?e():e}),2),u=i[0],s=i[1],c=void 0!==o?o:u,d=a?a(c):c,p=f(r),m=g(C([c]),2),v=m[0],w=m[1];return h(function(){var e=v[0];u!==e&&p(u,e)},[v]),h(function(){S(o)||s(o)},[o]),[d,f(function(e,t){s(e,t),w([c],t)})]}var b={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(e){var t=e.keyCode;if(e.altKey&&!e.ctrlKey||e.metaKey||t>=b.F1&&t<=b.F12)return!1;switch(t){case b.ALT:case b.CAPS_LOCK:case b.CONTEXT_MENU:case b.CTRL:case b.DOWN:case b.END:case b.ESC:case b.HOME:case b.INSERT:case b.LEFT:case b.MAC_FF_META:case b.META:case b.NUMLOCK:case b.NUM_CENTER:case b.PAGE_DOWN:case b.PAGE_UP:case b.PAUSE:case b.PRINT_SCREEN:case b.RIGHT:case b.SHIFT:case b.UP:case b.WIN_KEY:case b.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(e){if(e>=b.ZERO&&e<=b.NINE||e>=b.NUM_ZERO&&e<=b.NUM_MULTIPLY||e>=b.A&&e<=b.Z||-1!==window.navigator.userAgent.indexOf("WebKit")&&0===e)return!0;switch(e){case b.SPACE:case b.QUESTION_MARK:case b.NUM_PLUS:case b.NUM_MINUS:case b.NUM_PERIOD:case b.NUM_DIVISION:case b.SEMICOLON:case b.DASH:case b.EQUALS:case b.COMMA:case b.PERIOD:case b.SLASH:case b.APOSTROPHE:case b.SINGLE_QUOTE:case b.OPEN_SQUARE_BRACKET:case b.BACKSLASH:case b.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}},R="".concat("accept acceptCharset accessKey action allowFullScreen allowTransparency\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\n    charSet checked classID className colSpan cols content contentEditable contextMenu\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\n    mediaGroup method min minLength multiple muted name noValidate nonce open\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\n    summary tabIndex target title type useMap value width wmode wrap"," ").concat("onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError").split(/[\s\n]+/);function E(e,t){return 0===e.indexOf(t)}var M={},y=[];function F(e,t){}function P(e,t){}function I(e,t,n){t||M[n]||(e(!1,n),M[n]=!0)}function _(e,t){I(F,e,t)}_.preMessage=function(e){y.push(e)},_.resetWarned=function(){M={}},_.noteOnce=function(e,t){I(P,e,t)};var L={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"},x=["10","20","50","100"],V=function(e){var t=e.pageSizeOptions,n=void 0===t?x:t,l=e.locale,o=e.changeSize,i=e.pageSize,u=e.goButton,s=e.quickGo,c=e.rootPrefixCls,f=e.selectComponentClass,m=e.selectPrefixCls,v=e.disabled,h=e.buildOptionText,C=e.showSizeChanger,S=g(p.useState(""),2),w=S[0],R=S[1],E=function(){return!w||Number.isNaN(w)?void 0:Number(w)},M="function"==typeof h?h:function(e){return"".concat(e," ").concat(l.items_per_page)},y=function(e){""!==w&&(e.keyCode===b.ENTER||"click"===e.type)&&(R(""),null==s||s(E()))},F="".concat(c,"-options");if(!C&&!s)return null;var P=null,I=null,_=null;if(C&&f){var L="object"===(0,a.Z)(C)?C:{},V=L.options,k=L.className,O=V?void 0:(n.some(function(e){return e.toString()===i.toString()})?n:n.concat([i.toString()]).sort(function(e,t){return(Number.isNaN(Number(e))?0:Number(e))-(Number.isNaN(Number(t))?0:Number(t))})).map(function(e,t){return p.createElement(f.Option,{key:t,value:e.toString()},M(e))});P=p.createElement(f,(0,r.Z)({disabled:v,prefixCls:m,showSearch:!1,optionLabelProp:V?"label":"children",popupMatchSelectWidth:!1,value:(i||n[0]).toString(),getPopupContainer:function(e){return e.parentNode},"aria-label":l.page_size,defaultOpen:!1},"object"===(0,a.Z)(C)?C:null,{className:d()("".concat(F,"-size-changer"),k),options:V,onChange:function(e,t){if(null==o||o(Number(e)),"object"===(0,a.Z)(C)){var n;null===(n=C.onChange)||void 0===n||n.call(C,e,t)}}}),O)}return s&&(u&&(_="boolean"==typeof u?p.createElement("button",{type:"button",onClick:y,onKeyUp:y,disabled:v,className:"".concat(F,"-quick-jumper-button")},l.jump_to_confirm):p.createElement("span",{onClick:y,onKeyUp:y},u)),I=p.createElement("div",{className:"".concat(F,"-quick-jumper")},l.jump_to,p.createElement("input",{disabled:v,type:"text",value:w,onChange:function(e){R(e.target.value)},onKeyUp:y,onBlur:function(e){!u&&""!==w&&(R(""),e.relatedTarget&&(e.relatedTarget.className.indexOf("".concat(c,"-item-link"))>=0||e.relatedTarget.className.indexOf("".concat(c,"-item"))>=0)||null==s||s(E()))},"aria-label":l.page}),l.page,_)),p.createElement("li",{className:F},P,I)},k=function(e){var t=e.rootPrefixCls,n=e.page,l=e.active,r=e.className,a=e.showTitle,i=e.onClick,u=e.onKeyPress,s=e.itemRender,g="".concat(t,"-item"),c=d()(g,"".concat(g,"-").concat(n),o(o({},"".concat(g,"-active"),l),"".concat(g,"-disabled"),!n),r),f=s(n,"page",p.createElement("a",{rel:"nofollow"},n));return f?p.createElement("li",{title:a?String(n):null,className:c,onClick:function(){i(n)},onKeyDown:function(e){u(e,i,n)},tabIndex:0},f):null},O=function(e,t,n){return n};function N(){}function A(e){var t=Number(e);return"number"==typeof t&&!Number.isNaN(t)&&isFinite(t)&&Math.floor(t)===t}function T(e,t,n){return Math.floor((n-1)/(void 0===e?t:e))+1}var z=function(e){var t,n,l,i,s=e.prefixCls,c=void 0===s?"rc-pagination":s,f=e.selectPrefixCls,m=e.className,v=e.selectComponentClass,h=e.current,C=e.defaultCurrent,S=e.total,M=void 0===S?0:S,y=e.pageSize,F=e.defaultPageSize,P=e.onChange,I=void 0===P?N:P,_=e.hideOnSinglePage,x=e.align,z=e.showPrevNextJumpers,D=e.showQuickJumper,H=e.showLessItems,G=e.showTitle,j=void 0===G||G,U=e.onShowSizeChange,B=void 0===U?N:U,K=e.locale,Z=void 0===K?L:K,W=e.style,q=e.totalBoundaryShowSizeChanger,X=e.disabled,$=e.simple,Q=e.showTotal,Y=e.showSizeChanger,J=void 0===Y?M>(void 0===q?50:q):Y,ee=e.pageSizeOptions,et=e.itemRender,en=void 0===et?O:et,el=e.jumpPrevIcon,eo=e.jumpNextIcon,er=e.prevIcon,ea=e.nextIcon,ei=p.useRef(null),eu=g(w(10,{value:y,defaultValue:void 0===F?10:F}),2),es=eu[0],eg=eu[1],ec=g(w(1,{value:h,defaultValue:void 0===C?1:C,postState:function(e){return Math.max(1,Math.min(e,T(void 0,es,M)))}}),2),ed=ec[0],ep=ec[1],ef=g(p.useState(ed),2),em=ef[0],ev=ef[1];(0,p.useEffect)(function(){ev(ed)},[ed]);var eh=Math.max(1,ed-(H?3:5)),eC=Math.min(T(void 0,es,M),ed+(H?3:5));function eS(t,n){var l=t||p.createElement("button",{type:"button","aria-label":n,className:"".concat(c,"-item-link")});return"function"==typeof t&&(l=p.createElement(t,u({},e))),l}function ew(e){var t=e.target.value,n=T(void 0,es,M);return""===t?t:Number.isNaN(Number(t))?em:t>=n?n:Number(t)}var eb=M>es&&D;function eR(e){var t=ew(e);switch(t!==em&&ev(t),e.keyCode){case b.ENTER:eE(t);break;case b.UP:eE(t-1);break;case b.DOWN:eE(t+1)}}function eE(e){if(A(e)&&e!==ed&&A(M)&&M>0&&!X){var t=T(void 0,es,M),n=e;return e>t?n=t:e<1&&(n=1),n!==em&&ev(n),ep(n),null==I||I(n,es),n}return ed}var eM=ed>1,ey=ed<T(void 0,es,M);function eF(){eM&&eE(ed-1)}function eP(){ey&&eE(ed+1)}function eI(){eE(eh)}function e_(){eE(eC)}function eL(e,t){if("Enter"===e.key||e.charCode===b.ENTER||e.keyCode===b.ENTER){for(var n=arguments.length,l=Array(n>2?n-2:0),o=2;o<n;o++)l[o-2]=arguments[o];t.apply(void 0,l)}}function ex(e){("click"===e.type||e.keyCode===b.ENTER)&&eE(em)}var eV=null,ek=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=!1===n?{aria:!0,data:!0,attr:!0}:!0===n?{aria:!0}:u({},n);var l={};return Object.keys(e).forEach(function(n){(t.aria&&("role"===n||E(n,"aria-"))||t.data&&E(n,"data-")||t.attr&&R.includes(n))&&(l[n]=e[n])}),l}(e,{aria:!0,data:!0}),eO=Q&&p.createElement("li",{className:"".concat(c,"-total-text")},Q(M,[0===M?0:(ed-1)*es+1,ed*es>M?M:ed*es])),eN=null,eA=T(void 0,es,M);if(_&&M<=es)return null;var eT=[],ez={rootPrefixCls:c,onClick:eE,onKeyPress:eL,showTitle:j,itemRender:en,page:-1},eD=ed-1>0?ed-1:0,eH=ed+1<eA?ed+1:eA,eG=D&&D.goButton,ej="object"===(0,a.Z)($)?$.readOnly:!$,eU=eG,eB=null;$&&(eG&&(eU="boolean"==typeof eG?p.createElement("button",{type:"button",onClick:ex,onKeyUp:ex},Z.jump_to_confirm):p.createElement("span",{onClick:ex,onKeyUp:ex},eG),eU=p.createElement("li",{title:j?"".concat(Z.jump_to).concat(ed,"/").concat(eA):null,className:"".concat(c,"-simple-pager")},eU)),eB=p.createElement("li",{title:j?"".concat(ed,"/").concat(eA):null,className:"".concat(c,"-simple-pager")},ej?em:p.createElement("input",{type:"text",value:em,disabled:X,onKeyDown:function(e){(e.keyCode===b.UP||e.keyCode===b.DOWN)&&e.preventDefault()},onKeyUp:eR,onChange:eR,onBlur:function(e){eE(ew(e))},size:3}),p.createElement("span",{className:"".concat(c,"-slash")},"/"),eA));var eK=H?1:2;if(eA<=3+2*eK){eA||eT.push(p.createElement(k,(0,r.Z)({},ez,{key:"noPager",page:1,className:"".concat(c,"-item-disabled")})));for(var eZ=1;eZ<=eA;eZ+=1)eT.push(p.createElement(k,(0,r.Z)({},ez,{key:eZ,page:eZ,active:ed===eZ})))}else{var eW=H?Z.prev_3:Z.prev_5,eq=H?Z.next_3:Z.next_5,eX=en(eh,"jump-prev",eS(el,"prev page")),e$=en(eC,"jump-next",eS(eo,"next page"));(void 0===z||z)&&(eV=eX?p.createElement("li",{title:j?eW:null,key:"prev",onClick:eI,tabIndex:0,onKeyDown:function(e){eL(e,eI)},className:d()("".concat(c,"-jump-prev"),o({},"".concat(c,"-jump-prev-custom-icon"),!!el))},eX):null,eN=e$?p.createElement("li",{title:j?eq:null,key:"next",onClick:e_,tabIndex:0,onKeyDown:function(e){eL(e,e_)},className:d()("".concat(c,"-jump-next"),o({},"".concat(c,"-jump-next-custom-icon"),!!eo))},e$):null);var eQ=Math.max(1,ed-eK),eY=Math.min(ed+eK,eA);ed-1<=eK&&(eY=1+2*eK),eA-ed<=eK&&(eQ=eA-2*eK);for(var eJ=eQ;eJ<=eY;eJ+=1)eT.push(p.createElement(k,(0,r.Z)({},ez,{key:eJ,page:eJ,active:ed===eJ})));if(ed-1>=2*eK&&3!==ed&&(eT[0]=p.cloneElement(eT[0],{className:d()("".concat(c,"-item-after-jump-prev"),eT[0].props.className)}),eT.unshift(eV)),eA-ed>=2*eK&&ed!==eA-2){var e1=eT[eT.length-1];eT[eT.length-1]=p.cloneElement(e1,{className:d()("".concat(c,"-item-before-jump-next"),e1.props.className)}),eT.push(eN)}1!==eQ&&eT.unshift(p.createElement(k,(0,r.Z)({},ez,{key:1,page:1}))),eY!==eA&&eT.push(p.createElement(k,(0,r.Z)({},ez,{key:eA,page:eA})))}var e0=(t=en(eD,"prev",eS(er,"prev page")),p.isValidElement(t)?p.cloneElement(t,{disabled:!eM}):t);if(e0){var e2=!eM||!eA;e0=p.createElement("li",{title:j?Z.prev_page:null,onClick:eF,tabIndex:e2?null:0,onKeyDown:function(e){eL(e,eF)},className:d()("".concat(c,"-prev"),o({},"".concat(c,"-disabled"),e2)),"aria-disabled":e2},e0)}var e7=(n=en(eH,"next",eS(ea,"next page")),p.isValidElement(n)?p.cloneElement(n,{disabled:!ey}):n);e7&&($?(l=!ey,i=eM?0:null):i=(l=!ey||!eA)?null:0,e7=p.createElement("li",{title:j?Z.next_page:null,onClick:eP,tabIndex:i,onKeyDown:function(e){eL(e,eP)},className:d()("".concat(c,"-next"),o({},"".concat(c,"-disabled"),l)),"aria-disabled":l},e7));var e5=d()(c,m,o(o(o(o(o({},"".concat(c,"-start"),"start"===x),"".concat(c,"-center"),"center"===x),"".concat(c,"-end"),"end"===x),"".concat(c,"-simple"),$),"".concat(c,"-disabled"),X));return p.createElement("ul",(0,r.Z)({className:e5,style:W,ref:ei},ek),eO,e0,$?eB:eT,e7,p.createElement(V,{locale:Z,rootPrefixCls:c,disabled:X,selectComponentClass:v,selectPrefixCls:void 0===f?"rc-select":f,changeSize:function(e){var t=T(e,es,M),n=ed>t&&0!==t?t:ed;eg(e),ev(n),null==B||B(ed,e),ep(n),null==I||I(n,e)},pageSize:es,pageSizeOptions:ee,quickGo:eb?eE:null,goButton:eU,showSizeChanger:J}))}},36760:function(e,t){var n;!function(){"use strict";var l={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)l.call(e,n)&&e[n]&&(t=r(t,n));return t}(n)))}return e}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0!==(n=(function(){return o}).apply(t,[]))&&(e.exports=n)}()},96240:function(e,t,n){"use strict";function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,l=Array(t);n<t;n++)l[n]=e[n];return l}n.d(t,{Z:function(){return l}})},1119:function(e,t,n){"use strict";function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e}).apply(null,arguments)}n.d(t,{Z:function(){return l}})},73882:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var l=n(41154);function o(e){var t=function(e,t){if("object"!=(0,l.Z)(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=(0,l.Z)(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==(0,l.Z)(t)?t:t+""}},41154:function(e,t,n){"use strict";function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}n.d(t,{Z:function(){return l}})},29062:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var l=n(96240);function o(e,t){if(e){if("string"==typeof e)return(0,l.Z)(e,t);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?(0,l.Z)(e,t):void 0}}},71594:function(e,t,n){"use strict";n.d(t,{b7:function(){return a},ie:function(){return r}});var l=n(2265),o=n(24525);function r(e,t){return e?"function"==typeof e&&(()=>{let t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()||"function"==typeof e||"object"==typeof e&&"symbol"==typeof e.$$typeof&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)?l.createElement(e,t):e:null}function a(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=l.useState(()=>({current:(0,o.W_)(t)})),[r,a]=l.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...r,...e.state},onStateChange:t=>{a(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}},24525:function(e,t,n){"use strict";function l(e,t){return"function"==typeof e?e(t):e}function o(e,t){return n=>{t.setState(t=>({...t,[e]:l(n,t[e])}))}}function r(e){return e instanceof Function}function a(e,t,n){let l,o=[];return r=>{let a,i;n.key&&n.debug&&(a=Date.now());let u=e(r);if(!(u.length!==o.length||u.some((e,t)=>o[t]!==e)))return l;if(o=u,n.key&&n.debug&&(i=Date.now()),l=t(...u),null==n||null==n.onChange||n.onChange(l),n.key&&n.debug&&null!=n&&n.debug()){let e=Math.round((Date.now()-a)*100)/100,t=Math.round((Date.now()-i)*100)/100,l=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*l,120))}deg 100% 31%);`,null==n?void 0:n.key)}return l}}function i(e,t,n,l){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:l}}n.d(t,{W_:function(){return U},sC:function(){return B},tj:function(){return K}});let u="debugHeaders";function s(e,t,n){var l;let o={id:null!=(l=n.id)?l:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(o,e)}),o}function g(e,t,n,l){var o,r;let a=0,i=function(e,t){void 0===t&&(t=1),a=Math.max(a,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var n;null!=(n=e.columns)&&n.length&&i(e.columns,t+1)},0)};i(e);let u=[],g=(e,t)=>{let o={depth:t,id:[l,`${t}`].filter(Boolean).join("_"),headers:[]},r=[];e.forEach(e=>{let a;let i=[...r].reverse()[0],u=e.column.depth===o.depth,g=!1;if(u&&e.column.parent?a=e.column.parent:(a=e.column,g=!0),i&&(null==i?void 0:i.column)===a)i.subHeaders.push(e);else{let o=s(n,a,{id:[l,t,a.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:g,placeholderId:g?`${r.filter(e=>e.column===a).length}`:void 0,depth:t,index:r.length});o.subHeaders.push(e),r.push(o)}o.headers.push(e),e.headerGroup=o}),u.push(o),t>0&&g(r,t-1)};g(t.map((e,t)=>s(n,e,{depth:a,index:t})),a-1),u.reverse();let c=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,n=0,l=[0];return e.subHeaders&&e.subHeaders.length?(l=[],c(e.subHeaders).forEach(e=>{let{colSpan:n,rowSpan:o}=e;t+=n,l.push(o)})):t=1,n+=Math.min(...l),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}});return c(null!=(o=null==(r=u[0])?void 0:r.headers)?o:[]),u}let c=(e,t,n,l,o,r,u)=>{let s={id:t,index:l,original:n,depth:o,parentId:u,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return s._valuesCache[t]=n.accessorFn(s.original,l),s._valuesCache[t]},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];let n=e.getColumn(t);return null!=n&&n.accessorFn?(n.columnDef.getUniqueValues?s._uniqueValuesCache[t]=n.columnDef.getUniqueValues(s.original,l):s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]):void 0},renderValue:t=>{var n;return null!=(n=s.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=r?r:[],getLeafRows:()=>(function(e,t){let n=[],l=e=>{e.forEach(e=>{n.push(e);let o=t(e);null!=o&&o.length&&l(o)})};return l(e),n})(s.subRows,e=>e.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let e=[],t=s;for(;;){let n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:a(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,n,l){let o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(l),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:a(()=>[e,n,t,o],(e,t,n,l)=>({table:e,column:t,row:n,cell:l,getValue:l.getValue,renderValue:l.renderValue}),i(e.options,"debugCells","cell.getContext"))};return e._features.forEach(l=>{null==l.createCell||l.createCell(o,n,t,e)},{}),o})(e,s,t,t.id)),i(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:a(()=>[s.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),i(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let n=e._features[t];null==n||null==n.createRow||n.createRow(s,e)}return s},d=(e,t,n)=>{var l,o;let r=null==n||null==(l=n.toString())?void 0:l.toLowerCase();return!!(null==(o=e.getValue(t))||null==(o=o.toString())||null==(o=o.toLowerCase())?void 0:o.includes(r))};d.autoRemove=e=>R(e);let p=(e,t,n)=>{var l;return!!(null==(l=e.getValue(t))||null==(l=l.toString())?void 0:l.includes(n))};p.autoRemove=e=>R(e);let f=(e,t,n)=>{var l;return(null==(l=e.getValue(t))||null==(l=l.toString())?void 0:l.toLowerCase())===(null==n?void 0:n.toLowerCase())};f.autoRemove=e=>R(e);let m=(e,t,n)=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)};m.autoRemove=e=>R(e);let v=(e,t,n)=>!n.some(n=>{var l;return!(null!=(l=e.getValue(t))&&l.includes(n))});v.autoRemove=e=>R(e)||!(null!=e&&e.length);let h=(e,t,n)=>n.some(n=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)});h.autoRemove=e=>R(e)||!(null!=e&&e.length);let C=(e,t,n)=>e.getValue(t)===n;C.autoRemove=e=>R(e);let S=(e,t,n)=>e.getValue(t)==n;S.autoRemove=e=>R(e);let w=(e,t,n)=>{let[l,o]=n,r=e.getValue(t);return r>=l&&r<=o};w.resolveFilterValue=e=>{let[t,n]=e,l="number"!=typeof t?parseFloat(t):t,o="number"!=typeof n?parseFloat(n):n,r=null===t||Number.isNaN(l)?-1/0:l,a=null===n||Number.isNaN(o)?1/0:o;if(r>a){let e=r;r=a,a=e}return[r,a]},w.autoRemove=e=>R(e)||R(e[0])&&R(e[1]);let b={includesString:d,includesStringSensitive:p,equalsString:f,arrIncludes:m,arrIncludesAll:v,arrIncludesSome:h,equals:C,weakEquals:S,inNumberRange:w};function R(e){return null==e||""===e}function E(e,t,n){return!!e&&!!e.autoRemove&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}let M={sum:(e,t,n)=>n.reduce((t,n)=>{let l=n.getValue(e);return t+("number"==typeof l?l:0)},0),min:(e,t,n)=>{let l;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(l>n||void 0===l&&n>=n)&&(l=n)}),l},max:(e,t,n)=>{let l;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(l<n||void 0===l&&n>=n)&&(l=n)}),l},extent:(e,t,n)=>{let l,o;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(void 0===l?n>=n&&(l=o=n):(l>n&&(l=n),o<n&&(o=n)))}),[l,o]},mean:(e,t)=>{let n=0,l=0;if(t.forEach(t=>{let o=t.getValue(e);null!=o&&(o=+o)>=o&&(++n,l+=o)}),n)return l/n},median:(e,t)=>{if(!t.length)return;let n=t.map(t=>t.getValue(e));if(!(Array.isArray(n)&&n.every(e=>"number"==typeof e)))return;if(1===n.length)return n[0];let l=Math.floor(n.length/2),o=n.sort((e,t)=>e-t);return n.length%2!=0?o[l]:(o[l-1]+o[l])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},y=()=>({left:[],right:[]}),F={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},P=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),I=null;function _(e){return"touchstart"===e.type}function L(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let x=()=>({pageIndex:0,pageSize:10}),V=()=>({top:[],bottom:[]}),k=(e,t,n,l,o)=>{var r;let a=o.getRow(t,!0);n?(a.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),a.getCanSelect()&&(e[t]=!0)):delete e[t],l&&null!=(r=a.subRows)&&r.length&&a.getCanSelectSubRows()&&a.subRows.forEach(t=>k(e,t.id,n,l,o))};function O(e,t){let n=e.getState().rowSelection,l=[],o={},r=function(e,t){return e.map(e=>{var t;let a=N(e,n);if(a&&(l.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:r(e.subRows)}),a)return e}).filter(Boolean)};return{rows:r(t.rows),flatRows:l,rowsById:o}}function N(e,t){var n;return null!=(n=t[e.id])&&n}function A(e,t,n){var l;if(!(null!=(l=e.subRows)&&l.length))return!1;let o=!0,r=!1;return e.subRows.forEach(e=>{if((!r||o)&&(e.getCanSelect()&&(N(e,t)?r=!0:o=!1),e.subRows&&e.subRows.length)){let n=A(e,t);"all"===n?r=!0:("some"===n&&(r=!0),o=!1)}}),o?"all":!!r&&"some"}let T=/([0-9]+)/gm;function z(e,t){return e===t?0:e>t?1:-1}function D(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function H(e,t){let n=e.split(T).filter(Boolean),l=t.split(T).filter(Boolean);for(;n.length&&l.length;){let e=n.shift(),t=l.shift(),o=parseInt(e,10),r=parseInt(t,10),a=[o,r].sort();if(isNaN(a[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(a[1]))return isNaN(o)?-1:1;if(o>r)return 1;if(r>o)return -1}return n.length-l.length}let G={alphanumeric:(e,t,n)=>H(D(e.getValue(n)).toLowerCase(),D(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>H(D(e.getValue(n)),D(t.getValue(n))),text:(e,t,n)=>z(D(e.getValue(n)).toLowerCase(),D(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>z(D(e.getValue(n)),D(t.getValue(n))),datetime:(e,t,n)=>{let l=e.getValue(n),o=t.getValue(n);return l>o?1:l<o?-1:0},basic:(e,t,n)=>z(e.getValue(n),t.getValue(n))},j=[{createTable:e=>{e.getHeaderGroups=a(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,l,o)=>{var r,a;let i=null!=(r=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?r:[],u=null!=(a=null==o?void 0:o.map(e=>n.find(t=>t.id===e)).filter(Boolean))?a:[];return g(t,[...i,...n.filter(e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id))),...u],e)},i(e.options,u,"getHeaderGroups")),e.getCenterHeaderGroups=a(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,l,o)=>g(t,n=n.filter(e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id))),e,"center"),i(e.options,u,"getCenterHeaderGroups")),e.getLeftHeaderGroups=a(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"left")},i(e.options,u,"getLeftHeaderGroups")),e.getRightHeaderGroups=a(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"right")},i(e.options,u,"getRightHeaderGroups")),e.getFooterGroups=a(()=>[e.getHeaderGroups()],e=>[...e].reverse(),i(e.options,u,"getFooterGroups")),e.getLeftFooterGroups=a(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),i(e.options,u,"getLeftFooterGroups")),e.getCenterFooterGroups=a(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),i(e.options,u,"getCenterFooterGroups")),e.getRightFooterGroups=a(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),i(e.options,u,"getRightFooterGroups")),e.getFlatHeaders=a(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),i(e.options,u,"getFlatHeaders")),e.getLeftFlatHeaders=a(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),i(e.options,u,"getLeftFlatHeaders")),e.getCenterFlatHeaders=a(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),i(e.options,u,"getCenterFlatHeaders")),e.getRightFlatHeaders=a(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),i(e.options,u,"getRightFlatHeaders")),e.getCenterLeafHeaders=a(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),i(e.options,u,"getCenterLeafHeaders")),e.getLeftLeafHeaders=a(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),i(e.options,u,"getLeftLeafHeaders")),e.getRightLeafHeaders=a(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),i(e.options,u,"getRightLeafHeaders")),e.getLeafHeaders=a(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,n)=>{var l,o,r,a,i,u;return[...null!=(l=null==(o=e[0])?void 0:o.headers)?l:[],...null!=(r=null==(a=t[0])?void 0:a.headers)?r:[],...null!=(i=null==(u=n[0])?void 0:u.headers)?i:[]].map(e=>e.getLeafHeaders()).flat()},i(e.options,u,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:o("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()}))},e.getIsVisible=()=>{var n,l;let o=e.columns;return null==(n=o.length?o.some(e=>e.getIsVisible()):null==(l=t.getState().columnVisibility)?void 0:l[e.id])||n},e.getCanHide=()=>{var n,l;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(l=t.options.enableHiding)||l)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=a(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),i(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=a(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,n)=>[...e,...t,...n],i(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,n)=>a(()=>[n(),n().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),i(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:o("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=a(e=>[L(t,e)],t=>t.findIndex(t=>t.id===e.id),i(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var l;return(null==(l=L(t,n)[0])?void 0:l.id)===e.id},e.getIsLastColumn=n=>{var l;let o=L(t,n);return(null==(l=o[o.length-1])?void 0:l.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=a(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,n)=>l=>{let o=[];if(null!=e&&e.length){let t=[...e],n=[...l];for(;n.length&&t.length;){let e=t.shift(),l=n.findIndex(t=>t.id===e);l>-1&&o.push(n.splice(l,1)[0])}o=[...o,...n]}else o=l;return function(e,t,n){if(!(null!=t&&t.length)||!n)return e;let l=e.filter(e=>!t.includes(e.id));return"remove"===n?l:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...l]}(o,t,n)},i(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:y(),...e}),getDefaultOptions:e=>({onColumnPinningChange:o("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{let l=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,o,r,a,i,u;return"right"===n?{left:(null!=(r=null==e?void 0:e.left)?r:[]).filter(e=>!(null!=l&&l.includes(e))),right:[...(null!=(a=null==e?void 0:e.right)?a:[]).filter(e=>!(null!=l&&l.includes(e))),...l]}:"left"===n?{left:[...(null!=(i=null==e?void 0:e.left)?i:[]).filter(e=>!(null!=l&&l.includes(e))),...l],right:(null!=(u=null==e?void 0:e.right)?u:[]).filter(e=>!(null!=l&&l.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=l&&l.includes(e))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter(e=>!(null!=l&&l.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var n,l,o;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(l=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||l)}),e.getIsPinned=()=>{let n=e.getLeafColumns().map(e=>e.id),{left:l,right:o}=t.getState().columnPinning,r=n.some(e=>null==l?void 0:l.includes(e)),a=n.some(e=>null==o?void 0:o.includes(e));return r?"left":!!a&&"right"},e.getPinnedIndex=()=>{var n,l;let o=e.getIsPinned();return o?null!=(n=null==(l=t.getState().columnPinning)||null==(l=l[o])?void 0:l.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=a(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,n)=>{let l=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!l.includes(e.column.id))},i(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=a(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),i(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=a(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),i(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,l;return e.setColumnPinning(t?y():null!=(n=null==(l=e.initialState)?void 0:l.columnPinning)?n:y())},e.getIsSomeColumnsPinned=t=>{var n,l,o;let r=e.getState().columnPinning;return t?!!(null==(n=r[t])?void 0:n.length):!!((null==(l=r.left)?void 0:l.length)||(null==(o=r.right)?void 0:o.length))},e.getLeftLeafColumns=a(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),i(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=a(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),i(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=a(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,n)=>{let l=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!l.includes(e.id))},i(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:o("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"string"==typeof l?b.includesString:"number"==typeof l?b.inNumberRange:"boolean"==typeof l||null!==l&&"object"==typeof l?b.equals:Array.isArray(l)?b.arrIncludes:b.weakEquals},e.getFilterFn=()=>{var n,l;return r(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(l=t.options.filterFns)?void 0:l[e.columnDef.filterFn])?n:b[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,l,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(l=t.options.enableColumnFilters)||l)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find(t=>t.id===e.id))?void 0:n.value},e.getFilterIndex=()=>{var n,l;return null!=(n=null==(l=t.getState().columnFilters)?void 0:l.findIndex(t=>t.id===e.id))?n:-1},e.setFilterValue=n=>{t.setColumnFilters(t=>{var o,r;let a=e.getFilterFn(),i=null==t?void 0:t.find(t=>t.id===e.id),u=l(n,i?i.value:void 0);if(E(a,u,e))return null!=(o=null==t?void 0:t.filter(t=>t.id!==e.id))?o:[];let s={id:e.id,value:u};return i?null!=(r=null==t?void 0:t.map(t=>t.id===e.id?s:t))?r:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var o;return null==(o=l(t,e))?void 0:o.filter(e=>{let t=n.find(t=>t.id===e.id);return!(t&&E(t.getFilterFn(),e.value,t))})})},e.resetColumnFilters=t=>{var n,l;e.setColumnFilters(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:o("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;let l=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof l||"number"==typeof l}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,l,o,r;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(l=t.options.enableGlobalFilter)||l)&&(null==(o=t.options.enableFilters)||o)&&(null==(r=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||r)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>b.includesString,e.getGlobalFilterFn=()=>{var t,n;let{globalFilterFn:l}=e.options;return r(l)?l:"auto"===l?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[l])?t:b[l]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:o("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let n=t.getFilteredRowModel().flatRows.slice(10),l=!1;for(let t of n){let n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return G.datetime;if("string"==typeof n&&(l=!0,n.split(T).length>1))return G.alphanumeric}return l?G.text:G.basic},e.getAutoSortDir=()=>{let n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,l;if(!e)throw Error();return r(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(l=t.options.sortingFns)?void 0:l[e.columnDef.sortingFn])?n:G[e.columnDef.sortingFn]},e.toggleSorting=(n,l)=>{let o=e.getNextSortingOrder(),r=null!=n;t.setSorting(a=>{let i;let u=null==a?void 0:a.find(t=>t.id===e.id),s=null==a?void 0:a.findIndex(t=>t.id===e.id),g=[],c=r?n:"desc"===o;if("toggle"!=(i=null!=a&&a.length&&e.getCanMultiSort()&&l?u?"toggle":"add":null!=a&&a.length&&s!==a.length-1?"replace":u?"toggle":"replace")||r||o||(i="remove"),"add"===i){var d;(g=[...a,{id:e.id,desc:c}]).splice(0,g.length-(null!=(d=t.options.maxMultiSortColCount)?d:Number.MAX_SAFE_INTEGER))}else g="toggle"===i?a.map(t=>t.id===e.id?{...t,desc:c}:t):"remove"===i?a.filter(t=>t.id!==e.id):[{id:e.id,desc:c}];return g})},e.getFirstSortDir=()=>{var n,l;return(null!=(n=null!=(l=e.columnDef.sortDescFirst)?l:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var l,o;let r=e.getFirstSortDir(),a=e.getIsSorted();return a?(a===r||null!=(l=t.options.enableSortingRemoval)&&!l||!!n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===a?"asc":"desc"):r},e.getCanSort=()=>{var n,l;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(l=t.options.enableSorting)||l)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,l;return null!=(n=null!=(l=e.columnDef.enableMultiSort)?l:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;let l=null==(n=t.getState().sorting)?void 0:n.find(t=>t.id===e.id);return!!l&&(l.desc?"desc":"asc")},e.getSortIndex=()=>{var n,l;return null!=(n=null==(l=t.getState().sorting)?void 0:l.findIndex(t=>t.id===e.id))?n:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let n=e.getCanSort();return l=>{n&&(null==l.persist||l.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(l))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,l;e.setSorting(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:o("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var n,l;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(l=t.options.enableGrouping)||l)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"number"==typeof l?M.sum:"[object Date]"===Object.prototype.toString.call(l)?M.extent:void 0},e.getAggregationFn=()=>{var n,l;if(!e)throw Error();return r(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(l=t.options.aggregationFns)?void 0:l[e.columnDef.aggregationFn])?n:M[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,l;e.setGrouping(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];let l=t.getColumn(n);return null!=l&&l.columnDef.getGroupingValue?(e._groupingValuesCache[n]=l.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,l)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=n.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:o("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var l,o;if(!t){e._queue(()=>{t=!0});return}if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?l:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,l;e.setExpanded(t?{}:null!=(n=null==(l=e.initialState)?void 0:l.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let n=e.split(".");t=Math.max(t,n.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(l=>{var o;let r=!0===l||!!(null!=l&&l[e.id]),a={};if(!0===l?Object.keys(t.getRowModel().rowsById).forEach(e=>{a[e]=!0}):a=l,n=null!=(o=n)?o:!r,!r&&n)return{...a,[e.id]:!0};if(r&&!n){let{[e.id]:t,...n}=a;return n}return l})},e.getIsExpanded=()=>{var n;let l=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===l||(null==l?void 0:l[e.id]))},e.getCanExpand=()=>{var n,l,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(l=t.options.enableExpanding)||l)&&!!(null!=(o=e.subRows)&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,l=e;for(;n&&l.parentId;)n=(l=t.getRow(l.parentId,!0)).getIsExpanded();return n},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...x(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:o("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var l,o;if(!t){e._queue(()=>{t=!0});return}if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?l:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>l(t,e)),e.resetPagination=t=>{var n;e.setPagination(t?x():null!=(n=e.initialState.pagination)?n:x())},e.setPageIndex=t=>{e.setPagination(n=>{let o=l(t,n.pageIndex);return o=Math.max(0,Math.min(o,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...n,pageIndex:o}})},e.resetPageIndex=t=>{var n,l;e.setPageIndex(t?0:null!=(n=null==(l=e.initialState)||null==(l=l.pagination)?void 0:l.pageIndex)?n:0)},e.resetPageSize=t=>{var n,l;e.setPageSize(t?10:null!=(n=null==(l=e.initialState)||null==(l=l.pagination)?void 0:l.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination(e=>{let n=Math.max(1,l(t,e.pageSize)),o=e.pageSize*e.pageIndex;return{...e,pageIndex:Math.floor(o/n),pageSize:n}})},e.setPageCount=t=>e.setPagination(n=>{var o;let r=l(t,null!=(o=e.options.pageCount)?o:-1);return"number"==typeof r&&(r=Math.max(-1,r)),{...n,pageCount:r}}),e.getPageOptions=a(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},i(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return -1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:V(),...e}),getDefaultOptions:e=>({onRowPinningChange:o("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,l,o)=>{let r=l?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],a=new Set([...o?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...r]);t.setRowPinning(e=>{var t,l,o,r,i,u;return"bottom"===n?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter(e=>!(null!=a&&a.has(e))),bottom:[...(null!=(r=null==e?void 0:e.bottom)?r:[]).filter(e=>!(null!=a&&a.has(e))),...Array.from(a)]}:"top"===n?{top:[...(null!=(i=null==e?void 0:e.top)?i:[]).filter(e=>!(null!=a&&a.has(e))),...Array.from(a)],bottom:(null!=(u=null==e?void 0:e.bottom)?u:[]).filter(e=>!(null!=a&&a.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=a&&a.has(e))),bottom:(null!=(l=null==e?void 0:e.bottom)?l:[]).filter(e=>!(null!=a&&a.has(e)))}})},e.getCanPin=()=>{var n;let{enableRowPinning:l,enablePinning:o}=t.options;return"function"==typeof l?l(e):null==(n=null!=l?l:o)||n},e.getIsPinned=()=>{let n=[e.id],{top:l,bottom:o}=t.getState().rowPinning,r=n.some(e=>null==l?void 0:l.includes(e)),a=n.some(e=>null==o?void 0:o.includes(e));return r?"top":!!a&&"bottom"},e.getPinnedIndex=()=>{var n,l;let o=e.getIsPinned();if(!o)return -1;let r=null==(n="top"===o?t.getTopRows():t.getBottomRows())?void 0:n.map(e=>{let{id:t}=e;return t});return null!=(l=null==r?void 0:r.indexOf(e.id))?l:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,l;return e.setRowPinning(t?V():null!=(n=null==(l=e.initialState)?void 0:l.rowPinning)?n:V())},e.getIsSomeRowsPinned=t=>{var n,l,o;let r=e.getState().rowPinning;return t?!!(null==(n=r[t])?void 0:n.length):!!((null==(l=r.top)?void 0:l.length)||(null==(o=r.bottom)?void 0:o.length))},e._getPinnedRows=(t,n,l)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=n?n:[]).map(t=>{let n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null}):(null!=n?n:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:l}))},e.getTopRows=a(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),i(e.options,"debugRows","getTopRows")),e.getBottomRows=a(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),i(e.options,"debugRows","getBottomRows")),e.getCenterRows=a(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,n)=>{let l=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter(e=>!l.has(e.id))},i(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:o("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let l={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(e=>{e.getCanSelect()&&(l[e.id]=!0)}):o.forEach(e=>{delete l[e.id]}),l})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{let l=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(t=>{k(o,t.id,l,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=a(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?O(e,n):{rows:[],flatRows:[],rowsById:{}},i(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=a(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?O(e,n):{rows:[],flatRows:[],rowsById:{}},i(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=a(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?O(e,n):{rows:[],flatRows:[],rowsById:{}},i(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState(),l=!!(t.length&&Object.keys(n).length);return l&&t.some(e=>e.getCanSelect()&&!n[e.id])&&(l=!1),l},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:n}=e.getState(),l=!!t.length;return l&&t.some(e=>!n[e.id])&&(l=!1),l},e.getIsSomeRowsSelected=()=>{var t;let n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,l)=>{let o=e.getIsSelected();t.setRowSelection(r=>{var a;if(n=void 0!==n?n:!o,e.getCanSelect()&&o===n)return r;let i={...r};return k(i,e.id,n,null==(a=null==l?void 0:l.selectChildren)||a,t),i})},e.getIsSelected=()=>{let{rowSelection:n}=t.getState();return N(e,n)},e.getIsSomeSelected=()=>{let{rowSelection:n}=t.getState();return"some"===A(e,n)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:n}=t.getState();return"all"===A(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return n=>{var l;t&&e.toggleSelected(null==(l=n.target)?void 0:l.checked)}}}},{getDefaultColumnDef:()=>F,getInitialState:e=>({columnSizing:{},columnSizingInfo:P(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:o("columnSizing",e),onColumnSizingInfoChange:o("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,l,o;let r=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:F.minSize,null!=(l=null!=r?r:e.columnDef.size)?l:F.size),null!=(o=e.columnDef.maxSize)?o:F.maxSize)},e.getStart=a(e=>[e,L(t,e),t.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),i(t.options,"debugColumns","getStart")),e.getAfter=a(e=>[e,L(t,e),t.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),i(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:n,...l}=t;return l})},e.getCanResize=()=>{var n,l;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(l=t.options.enableColumnResizing)||l)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,n=e=>{if(e.subHeaders.length)e.subHeaders.forEach(n);else{var l;t+=null!=(l=e.column.getSize())?l:0}};return n(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{let l=t.getColumn(e.column.id),o=null==l?void 0:l.getCanResize();return r=>{if(!l||!o||(null==r.persist||r.persist(),_(r)&&r.touches&&r.touches.length>1))return;let a=e.getSize(),i=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[l.id,l.getSize()]],u=_(r)?Math.round(r.touches[0].clientX):r.clientX,s={},g=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo(e=>{var l,o;let r="rtl"===t.options.columnResizeDirection?-1:1,a=(n-(null!=(l=null==e?void 0:e.startOffset)?l:0))*r,i=Math.max(a/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,n]=e;s[t]=Math.round(100*Math.max(n+n*i,0))/100}),{...e,deltaOffset:a,deltaPercentage:i}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...s})))},c=e=>g("move",e),d=e=>{g("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},p=n||("undefined"!=typeof document?document:null),f={moveHandler:e=>c(e.clientX),upHandler:e=>{null==p||p.removeEventListener("mousemove",f.moveHandler),null==p||p.removeEventListener("mouseup",f.upHandler),d(e.clientX)}},m={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(e.touches[0].clientX),!1),upHandler:e=>{var t;null==p||p.removeEventListener("touchmove",m.moveHandler),null==p||p.removeEventListener("touchend",m.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),d(null==(t=e.touches[0])?void 0:t.clientX)}},v=!!function(){if("boolean"==typeof I)return I;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return I=e}()&&{passive:!1};_(r)?(null==p||p.addEventListener("touchmove",m.moveHandler,v),null==p||p.addEventListener("touchend",m.upHandler,v)):(null==p||p.addEventListener("mousemove",f.moveHandler,v),null==p||p.addEventListener("mouseup",f.upHandler,v)),t.setColumnSizingInfo(e=>({...e,startOffset:u,startSize:a,deltaOffset:0,deltaPercentage:0,columnSizingStart:i,isResizingColumn:l.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?P():null!=(n=e.initialState.columnSizingInfo)?n:P())},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function U(e){var t,n;let o=[...j,...null!=(t=e._features)?t:[]],r={_features:o},u=r._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(r)),{}),s=e=>r.options.mergeOptions?r.options.mergeOptions(u,e):{...u,...e},g={...null!=(n=e.initialState)?n:{}};r._features.forEach(e=>{var t;g=null!=(t=null==e.getInitialState?void 0:e.getInitialState(g))?t:g});let c=[],d=!1,p={_features:o,options:{...u,...e},initialState:g,_queue:e=>{c.push(e),d||(d=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();d=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{r.setState(r.initialState)},setOptions:e=>{let t=l(e,r.options);r.options=s(t)},getState:()=>r.options.state,setState:e=>{null==r.options.onStateChange||r.options.onStateChange(e)},_getRowId:(e,t,n)=>{var l;return null!=(l=null==r.options.getRowId?void 0:r.options.getRowId(e,t,n))?l:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?r.getPrePaginationRowModel():r.getRowModel()).rowsById[e];if(!n&&!(n=r.getCoreRowModel().rowsById[e]))throw Error();return n},_getDefaultColumnDef:a(()=>[r.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...r._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},i(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>r.options.columns,getAllColumns:a(()=>[r._getColumnDefs()],e=>{let t=function(e,n,l){return void 0===l&&(l=0),e.map(e=>{let o=function(e,t,n,l){var o,r;let u;let s={...e._getDefaultColumnDef(),...t},g=s.accessorKey,c=null!=(o=null!=(r=s.id)?r:g?"function"==typeof String.prototype.replaceAll?g.replaceAll(".","_"):g.replace(/\./g,"_"):void 0)?o:"string"==typeof s.header?s.header:void 0;if(s.accessorFn?u=s.accessorFn:g&&(u=g.includes(".")?e=>{let t=e;for(let e of g.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[s.accessorKey]),!c)throw Error();let d={id:`${String(c)}`,accessorFn:u,parent:l,depth:n,columnDef:s,columns:[],getFlatColumns:a(()=>[!0],()=>{var e;return[d,...null==(e=d.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},i(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:a(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=d.columns)&&t.length?e(d.columns.flatMap(e=>e.getLeafColumns())):[d]},i(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(d,e);return d}(r,e,l,n);return o.columns=e.columns?t(e.columns,o,l+1):[],o})};return t(e)},i(e,"debugColumns","getAllColumns")),getAllFlatColumns:a(()=>[r.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),i(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:a(()=>[r.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),i(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:a(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),i(e,"debugColumns","getAllLeafColumns")),getColumn:e=>r._getAllFlatColumnsById()[e]};Object.assign(r,p);for(let e=0;e<r._features.length;e++){let t=r._features[e];null==t||null==t.createTable||t.createTable(r)}return r}function B(){return e=>a(()=>[e.options.data],t=>{let n={rows:[],flatRows:[],rowsById:{}},l=function(t,o,r){void 0===o&&(o=0);let a=[];for(let u=0;u<t.length;u++){let s=c(e,e._getRowId(t[u],u,r),t[u],u,o,void 0,null==r?void 0:r.id);if(n.flatRows.push(s),n.rowsById[s.id]=s,a.push(s),e.options.getSubRows){var i;s.originalSubRows=e.options.getSubRows(t[u],u),null!=(i=s.originalSubRows)&&i.length&&(s.subRows=l(s.originalSubRows,o+1,s))}}return a};return n.rows=l(t),n},i(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function K(){return e=>a(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(null!=t&&t.length))return n;let l=e.getState().sorting,o=[],r=l.filter(t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()}),a={};r.forEach(t=>{let n=e.getColumn(t.id);n&&(a[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})});let i=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let l=0;l<r.length;l+=1){var n;let o=r[l],i=a[o.id],u=i.sortUndefined,s=null!=(n=null==o?void 0:o.desc)&&n,g=0;if(u){let n=e.getValue(o.id),l=t.getValue(o.id),r=void 0===n,a=void 0===l;if(r||a){if("first"===u)return r?-1:1;if("last"===u)return r?1:-1;g=r&&a?0:r?u:-u}}if(0===g&&(g=i.sortingFn(e,t,o.id)),0!==g)return s&&(g*=-1),i.invertSorting&&(g*=-1),g}return e.index-t.index}),t.forEach(e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=i(e.subRows))}),t};return{rows:i(n.rows),flatRows:o,rowsById:n.rowsById}},i(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}}}]);