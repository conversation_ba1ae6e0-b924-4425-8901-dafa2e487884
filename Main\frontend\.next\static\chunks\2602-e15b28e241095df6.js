"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2602],{2602:function(e,t,n){n.d(t,{B:function(){return y},I:function(){return S},O:function(){return l},S:function(){return B},U:function(){return a},a:function(){return s},b:function(){return u},c:function(){return U},d:function(){return $},e:function(){return f},f:function(){return z},g:function(){return V},i:function(){return b},j:function(){return W},m:function(){return d},n:function(){return Q},o:function(){return P},r:function(){return _},s:function(){return x},t:function(){return T},u:function(){return A},z:function(){return O}});var r=n(2265),i=n(98183),o=Object.prototype.hasOwnProperty;let u=new WeakMap,c=()=>{},a=c(),l=Object,f=e=>e===a,s=e=>"function"==typeof e,d=(e,t)=>({...e,...t}),y=e=>s(e.then),p={},g={},h="undefined",b=typeof window!=h,v=typeof document!=h,w=b&&"Deno"in window,m=()=>b&&typeof window.requestAnimationFrame!=h,O=(e,t)=>{let n=u.get(e);return[()=>!f(t)&&e.get(t)||p,r=>{if(!f(t)){let i=e.get(t);t in g||(g[t]=i),n[5](t,d(i,r),i||p)}},n[6],()=>!f(t)&&t in g?g[t]:!f(t)&&e.get(t)||p]},E=!0,[k,j]=b&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[c,c],R={initFocus:e=>(v&&document.addEventListener("visibilitychange",e),k("focus",e),()=>{v&&document.removeEventListener("visibilitychange",e),j("focus",e)}),initReconnect:e=>{let t=()=>{E=!0,e()},n=()=>{E=!1};return k("online",t),k("offline",n),()=>{j("online",t),j("offline",n)}}},S=!r.useId,_=!b||w,T=e=>m()?window.requestAnimationFrame(e):setTimeout(e,1),A=_?r.useEffect:r.useLayoutEffect,C="undefined"!=typeof navigator&&navigator.connection,I=!_&&C&&(["slow-2g","2g"].includes(C.effectiveType)||C.saveData),L=new WeakMap,D=(e,t)=>l.prototype.toString.call(e)==="[object ".concat(t,"]"),M=0,N=e=>{let t,n;let r=typeof e,i=D(e,"Date"),o=D(e,"RegExp"),u=D(e,"Object");if(l(e)!==e||i||o)t=i?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=L.get(e))return t;if(t=++M+"~",L.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=N(e[n])+",";L.set(e,t)}if(u){t="#";let r=l.keys(e).sort();for(;!f(n=r.pop());)f(e[n])||(t+=n+":"+N(e[n])+",");L.set(e,t)}}return t},x=e=>{if(s(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?N(e):"",t]},F=0,P=()=>++F;async function Q(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o,c,l]=t,p=d({populateCache:!0,throwOnError:!0},"boolean"==typeof l?{revalidate:l}:l||{}),g=p.populateCache,h=p.rollbackOnError,b=p.optimisticData,v=e=>"function"==typeof h?h(e):!1!==h,w=p.throwOnError;if(s(o)){let e=[];for(let t of r.keys())!/^\$(inf|sub)\$/.test(t)&&o(r.get(t)._k)&&e.push(t);return Promise.all(e.map(m))}return m(o);async function m(e){let n;let[o]=x(e);if(!o)return;let[l,d]=O(r,o),[h,m,E,k]=u.get(r),j=()=>{let t=h[o];return(s(p.revalidate)?p.revalidate(l().data,e):!1!==p.revalidate)&&(delete E[o],delete k[o],t&&t[0])?t[0](i.QQ).then(()=>l().data):l().data};if(t.length<3)return j();let R=c,S=P();m[o]=[S,0];let _=!f(b),T=l(),A=T.data,C=T._c,I=f(C)?A:C;if(_&&d({data:b=s(b)?b(I,A):b,_c:I}),s(R))try{R=R(I)}catch(e){n=e}if(R&&y(R)){if(R=await R.catch(e=>{n=e}),S!==m[o][0]){if(n)throw n;return R}n&&_&&v(n)&&(g=!0,d({data:I,_c:a}))}if(g&&!n&&(s(g)?d({data:g(R,I),error:a,_c:a}):d({data:R,error:a,_c:a})),m[o][1]=P(),Promise.resolve(j()).then(()=>{d({_c:a})}),n){if(w)throw n;return}return R}}let q=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},J=(e,t)=>{if(!u.has(e)){let n=d(R,t),r=Object.create(null),o=Q.bind(a,e),l=c,f=Object.create(null),s=(e,t)=>{let n=f[e]||[];return f[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},y=(t,n,r)=>{e.set(t,n);let i=f[t];if(i)for(let e of i)e(n,r)},p=()=>{if(!u.has(e)&&(u.set(e,[r,Object.create(null),Object.create(null),Object.create(null),o,y,s]),!_)){let t=n.initFocus(setTimeout.bind(a,q.bind(a,r,i.N4))),o=n.initReconnect(setTimeout.bind(a,q.bind(a,r,i.l2)));l=()=>{t&&t(),o&&o(),u.delete(e)}}};return p(),[e,o,p,l]}return[e,u.get(e)[4]]},[U,W]=J(new Map),$=d({onLoadingSlow:c,onSuccess:c,onError:c,onErrorRetry:(e,t,n,r,i)=>{let o=n.errorRetryCount,u=i.retryCount,c=~~((Math.random()+.5)*(1<<(u<8?u:8)))*n.errorRetryInterval;(f(o)||!(u>o))&&setTimeout(r,c,i)},onDiscarded:c,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:I?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:I?5e3:3e3,compare:function e(t,n){var r,i;if(t===n)return!0;if(t&&n&&(r=t.constructor)===n.constructor){if(r===Date)return t.getTime()===n.getTime();if(r===RegExp)return t.toString()===n.toString();if(r===Array){if((i=t.length)===n.length)for(;i--&&e(t[i],n[i]););return -1===i}if(!r||"object"==typeof t){for(r in i=0,t)if(o.call(t,r)&&++i&&!o.call(n,r)||!(r in n)||!e(t[r],n[r]))return!1;return Object.keys(n).length===i}}return t!=t&&n!=n},isPaused:()=>!1,cache:U,mutate:W,fallback:{}},{isOnline:()=>E,isVisible:()=>{let e=v&&document.visibilityState;return f(e)||"hidden"!==e}}),z=(e,t)=>{let n=d(e,t);if(t){let{use:r,fallback:i}=e,{use:o,fallback:u}=t;r&&o&&(n.use=r.concat(o)),i&&u&&(n.fallback=d(i,u))}return n},B=(0,r.createContext)({}),V=e=>{let{value:t}=e,n=(0,r.useContext)(B),i=s(t),o=(0,r.useMemo)(()=>i?t(n):t,[i,n,t]),u=(0,r.useMemo)(()=>i?o:z(n,o),[i,n,o]),c=o&&o.provider,l=(0,r.useRef)(a);c&&!l.current&&(l.current=J(c(u.cache||U),o));let f=l.current;return f&&(u.cache=f[0],u.mutate=f[1]),A(()=>{if(f)return f[2]&&f[2](),f[3]},[]),(0,r.createElement)(B.Provider,d(e,{value:u}))}},98183:function(e,t,n){n.d(t,{N4:function(){return r},QQ:function(){return o},aU:function(){return u},l2:function(){return i}});let r=0,i=1,o=2,u=3}}]);