"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { RecurringTransfer } from "@/types/recurring-transfer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import Progress from "@/components/ui/progress";
import Separator from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { imageURL } from "@/lib/utils";
import { 
  Clock, 
  DollarSign, 
  User, 
  Play, 
  Pause, 
  XCircle, 
  CheckCircle,
  MoreHorizontal,
  Eye,
  Edit,
  RotateCcw,
  Calendar
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface RecurringTransferCardProps {
  transfer: RecurringTransfer;
  userRole: 'sender' | 'recipient';
  onAction?: (action: string, transferId: number, data?: any) => void;
}

export function RecurringTransferCard({ 
  transfer, 
  userRole, 
  onAction 
}: RecurringTransferCardProps) {
  const { t } = useTranslation();

  const getStatusIcon = () => {
    switch (transfer.status) {
      case 'active':
        return <Play className="h-4 w-4" />;
      case 'paused':
        return <Pause className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (transfer.status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const counterparty = userRole === 'sender' ? transfer.recipient : transfer.sender;

  const canPause = transfer.status === 'active' && userRole === 'sender';
  const canResume = transfer.status === 'paused' && userRole === 'sender';
  const canCancel = ['active', 'paused'].includes(transfer.status) && userRole === 'sender';
  const canEdit = ['active', 'paused'].includes(transfer.status) && userRole === 'sender';

  return (
    <Card className="w-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge 
              variant="outline" 
              className={`${getStatusColor()} flex items-center space-x-1`}
            >
              {getStatusIcon()}
              <span className="capitalize">{t(transfer.status)}</span>
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {transfer.frequencyLabel}
            </Badge>
            {transfer.maxOccurrences && (
              <Badge variant="outline" className="text-xs">
                {transfer.executedCount}/{transfer.maxOccurrences}
              </Badge>
            )}
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/recurring-transfers/${transfer.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  {t('View Details')}
                </Link>
              </DropdownMenuItem>
              {canEdit && (
                <DropdownMenuItem asChild>
                  <Link href={`/recurring-transfers/${transfer.id}/edit`}>
                    <Edit className="h-4 w-4 mr-2" />
                    {t('Edit')}
                  </Link>
                </DropdownMenuItem>
              )}
              {canPause && (
                <DropdownMenuItem onClick={() => onAction?.('pause', transfer.id)}>
                  <Pause className="h-4 w-4 mr-2" />
                  {t('Pause')}
                </DropdownMenuItem>
              )}
              {canResume && (
                <DropdownMenuItem onClick={() => onAction?.('resume', transfer.id)}>
                  <Play className="h-4 w-4 mr-2" />
                  {t('Resume')}
                </DropdownMenuItem>
              )}
              {transfer.status === 'failed' && userRole === 'sender' && (
                <DropdownMenuItem onClick={() => onAction?.('retry', transfer.id)}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  {t('Retry')}
                </DropdownMenuItem>
              )}
              {canCancel && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onAction?.('cancel', transfer.id)}
                    className="text-red-600"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    {t('Cancel')}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-lg">
              {transfer.amount.toLocaleString()} {transfer.currencyCode}
            </h3>
            <p className="text-sm text-muted-foreground">
              {t('Transfer ID')}: {transfer.recurringId}
            </p>
          </div>
          
          <div className="text-right">
            <p className="text-sm font-medium">
              {userRole === 'sender' ? t('To') : t('From')}
            </p>
            <div className="flex items-center space-x-2">
              {counterparty?.customer?.avatar && (
                <Image
                  src={imageURL(counterparty.customer.avatar)}
                  alt={counterparty.name}
                  width={24}
                  height={24}
                  className="rounded-full"
                />
              )}
              <span className="text-sm text-muted-foreground">
                {counterparty?.name || t('Unknown User')}
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {transfer.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {transfer.description}
          </p>
        )}

        {/* Progress for limited occurrences */}
        {transfer.maxOccurrences && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>{t('Progress')}</span>
              <span>{transfer.progressPercentage}%</span>
            </div>
            <Progress value={transfer.progressPercentage} className="h-2" />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{transfer.executedCount} executed</span>
              <span>{transfer.remainingExecutions} remaining</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">{t('Next Execution')}</p>
            <p className="font-medium">{transfer.getNextExecution()}</p>
          </div>
          <div>
            <p className="text-muted-foreground">{t('Time Until Next')}</p>
            <p className={`font-medium ${transfer.isActive ? 'text-green-600' : 'text-gray-600'}`}>
              {transfer.timeUntilNext}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">{t('Start Date')}</p>
            <p className="font-medium">{transfer.getStartDate()}</p>
          </div>
          <div>
            <p className="text-muted-foreground">{t('End Date')}</p>
            <p className="font-medium">{transfer.getEndDate()}</p>
          </div>
        </div>

        {/* Execution Stats */}
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="text-center p-2 bg-muted rounded">
            <p className="font-medium">{transfer.executedCount}</p>
            <p className="text-muted-foreground">{t('Executed')}</p>
          </div>
          <div className="text-center p-2 bg-muted rounded">
            <p className="font-medium">{transfer.retryCount}</p>
            <p className="text-muted-foreground">{t('Retries')}</p>
          </div>
          <div className="text-center p-2 bg-muted rounded">
            <p className="font-medium">
              {transfer.maxOccurrences ? transfer.remainingExecutions : '∞'}
            </p>
            <p className="text-muted-foreground">{t('Remaining')}</p>
          </div>
        </div>

        {/* Error Message */}
        {transfer.status === 'failed' && transfer.lastError && (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-xs">
            <p className="font-medium text-red-800">{t('Last Error')}:</p>
            <p className="text-red-600">{transfer.lastError}</p>
          </div>
        )}

        {/* Notifications */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-2">
            {transfer.notifySender && (
              <Badge variant="outline" className="text-xs">
                {t('Notify Sender')}
              </Badge>
            )}
            {transfer.notifyRecipient && (
              <Badge variant="outline" className="text-xs">
                {t('Notify Recipient')}
              </Badge>
            )}
          </div>
          <span>{t('Fee')}: {transfer.fee} {transfer.currencyCode}</span>
        </div>
      </CardContent>
    </Card>
  );
}
