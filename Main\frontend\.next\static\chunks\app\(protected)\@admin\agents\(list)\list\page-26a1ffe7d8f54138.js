(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[87265],{16834:function(e,a,n){Promise.resolve().then(n.bind(n,88218))},88218:function(e,a,n){"use strict";n.r(a),n.d(a,{default:function(){return f}});var t=n(57437),s=n(2265),r=n(40127),l=n(85539),c=n(27186),i=n(85017),u=n(6512),d=n(75730),o=n(94508),h=n(99376),m=n(43949);function f(){var e;let a=(0,h.useSearchParams)(),[n,f]=s.useState(null!==(e=a.get("search"))&&void 0!==e?e:""),x=(0,h.useRouter)(),v=(0,h.usePathname)(),{t:p}=(0,m.$G)(),{data:g,meta:j,isLoading:N,refresh:k}=(0,d.Z)("/admin/agents?".concat(a.toString()),{keepPreviousData:!0});return(0,t.jsx)("div",{className:"p-4",children:(0,t.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,t.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,t.jsx)(l.R,{value:n,onChange:e=>{e.preventDefault();let a=(0,o.w4)(e.target.value);f(e.target.value),x.replace("".concat(v,"?").concat(a.toString()))},iconPlacement:"end",placeholder:p("Search...")}),(0,t.jsx)(i.k,{canFilterUser:!0,canFilterByGender:!0,canFilterByCountryCode:!0}),(0,t.jsx)(c._,{url:"/admin/agents/export/all"})]}),(0,t.jsx)("div",{})]}),(0,t.jsx)(u.Z,{className:"my-4"}),(0,t.jsx)(r.Z,{data:g,meta:j,isLoading:N,refresh:k})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,31384,27443,83568,227,56993,85017,40127,92971,95030,1744],function(){return e(e.s=16834)}),_N_E=e.O()}]);