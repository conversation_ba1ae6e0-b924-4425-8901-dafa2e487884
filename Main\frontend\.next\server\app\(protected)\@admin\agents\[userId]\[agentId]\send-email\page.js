(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2389],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},26195:e=>{"use strict";e.exports=require("node:buffer")},1526:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>L,default:()=>_});var n,s={};r.r(s),r.d(s,{AppRouter:()=>m.WY,ClientPageRoot:()=>m.b1,GlobalError:()=>u.ZP,LayoutRouter:()=>m.yO,NotFoundBoundary:()=>m.O4,Postpone:()=>m.hQ,RenderFromTemplateContext:()=>m.b5,__next_app__:()=>h,actionAsyncStorage:()=>m.Wz,createDynamicallyTrackedSearchParams:()=>m.rL,createUntrackedSearchParams:()=>m.S5,decodeAction:()=>m.Hs,decodeFormState:()=>m.dH,decodeReply:()=>m.kf,originalPathname:()=>f,pages:()=>g,patchFetch:()=>m.XH,preconnect:()=>m.$P,preloadFont:()=>m.C5,preloadStyle:()=>m.oH,renderToReadableStream:()=>m.aW,requestAsyncStorage:()=>m.Fg,routeModule:()=>x,serverHooks:()=>m.GP,staticGenerationAsyncStorage:()=>m.AT,taintObjectReference:()=>m.nr,tree:()=>p}),r(67206);var a=r(79319),i=r(20518),o=r(61902),c=r(62042),l=r(44630),d=r(44828),u=r(65505),m=r(13839);let p=["",{children:["(protected)",{admin:["children",{children:["agents",{children:["[userId]",{children:["[agentId]",{children:["send-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86894)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\send-email\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,99984)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\send-email\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,12771)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,12885)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\loading.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,29670)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,74030)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73391)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,50517)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],agent:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],customer:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}],merchant:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(r.bind(r,46892)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,15688)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,95865)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,57665)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,38694)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4710)),"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\not-found.tsx"]}],g=["C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\app\\(protected)\\@admin\\agents\\[userId]\\[agentId]\\send-email\\page.tsx"],f="/(protected)/@admin/agents/[userId]/[agentId]/send-email/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new l.AppPageRouteModule({definition:{kind:d.x.APP_PAGE,page:"/(protected)/@admin/agents/[userId]/[agentId]/send-email/page",pathname:"/agents/[userId]/[agentId]/send-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var S=r(69094),E=r(5787),v=r(90527);let P=e=>e?JSON.parse(e):void 0,I=self.__BUILD_MANIFEST,b=P(self.__REACT_LOADABLE_MANIFEST),j=null==(n=self.__RSC_MANIFEST)?void 0:n["/(protected)/@admin/agents/[userId]/[agentId]/send-email/page"],k=P(self.__RSC_SERVER_MANIFEST),D=P(self.__NEXT_FONT_MANIFEST),A=P(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];j&&k&&(0,E.Mo)({clientReferenceManifest:j,serverActionsManifest:k,serverModuleMap:(0,v.w)({serverActionsManifest:k,pageName:"/(protected)/@admin/agents/[userId]/[agentId]/send-email/page"})});let M=(0,i.d)({pagesType:S.s.APP,dev:!1,page:"/(protected)/@admin/agents/[userId]/[agentId]/send-email/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:I,renderToHTML:c.f,reactLoadableManifest:b,clientReferenceManifest:j,serverActionsManifest:k,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:200,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[{protocol:"http",hostname:"localhost"},{protocol:"https",hostname:"**"}],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"C:\\Users\\<USER>\\Desktop\\IDEES SQQS\\PAYSNAPo\\Main\\frontend\\next.config.mjs",configFileName:"next.config.mjs"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:D,incrementalCacheHandler:null,interceptionRouteRewrites:A}),L=s;function _(e){return(0,a.C)({...e,IncrementalCache:o.k,handler:M})}},98254:(e,t,r)=>{Promise.resolve().then(r.bind(r,29431))},89434:(e,t,r)=>{Promise.resolve().then(r.bind(r,31792))},29431:(e,t,r)=>{"use strict";r.d(t,{Tabbar:()=>E});var n=r(60926),s=r(14579),a=r(30417),i=r(89551),o=r(53042),c=r(23181),l=r(44788),d=r(38071),u=r(28531),m=r(5764),p=r(47020),g=r(737),f=r(64947),h=r(39228),x=r(32167),S=r(91500);function E(){let e=(0,f.UO)(),t=(0,f.jD)(),r=(0,f.tv)(),E=(0,f.lr)(),{t:v}=(0,h.$G)(),P=[{title:v("Account Details"),icon:(0,n.jsx)(o.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}?${E.toString()}`,id:"__DEFAULT__"},{title:v("Charges/Commissions"),icon:(0,n.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/commissions?${E.toString()}`,id:"commissions"},{title:v("Fees"),icon:(0,n.jsx)(c.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/fees?${E.toString()}`,id:"fees"},{title:v("Transactions"),icon:(0,n.jsx)(l.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/transactions?${E.toString()}`,id:"transactions"},{title:v("KYC"),icon:(0,n.jsx)(d.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/kyc?${E.toString()}`,id:"kyc"},{title:v("Permissions"),icon:(0,n.jsx)(u.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/permissions?${E.toString()}`,id:"permissions"},{title:v("Send Email"),icon:(0,n.jsx)(m.Z,{size:"24",variant:"Bulk"}),href:`/agents/${e?.userId}/${e?.agentId}/send-email?${E.toString()}`,id:"send-email"}];return(0,n.jsxs)("div",{className:"sticky inset-0 left-0 top-0 z-10 border-b border-foreground/[8%] bg-background p-4",children:[(0,n.jsxs)("div",{className:"mb-4 flex flex-wrap items-center justify-between gap-2",children:[(0,n.jsxs)("ul",{className:"line-clamp-1 inline-flex items-center gap-2 p-0 text-sm font-medium text-secondary-text sm:text-base [&>li]:flex [&>li]:items-center",children:[(0,n.jsx)("li",{children:(0,n.jsxs)(g.Z,{href:"/agents/list",className:"flex items-center gap-x-1 p-0 pr-2 text-foreground hover:text-primary sm:pr-4",children:[(0,n.jsx)(p.Z,{className:"size-4 sm:size-6"}),v("Back")]})}),(0,n.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",E.get("name")," "]}),(0,n.jsxs)("li",{className:"line-clamp-1 whitespace-nowrap",children:["/ ",v("Agents")," #",e.agentId]})]}),(0,n.jsxs)("div",{className:"ml-auto inline-flex items-center gap-2 text-sm sm:text-base",children:[(0,n.jsx)("span",{children:v("Active")}),(0,n.jsx)(a.Z,{defaultChecked:"1"===E.get("active"),className:"data-[state=unchecked]:bg-muted",onCheckedChange:n=>{x.toast.promise((0,i.z)(e.userId),{loading:v("Loading..."),success:s=>{if(!s.status)throw Error(s.message);let a=new URLSearchParams(E);return(0,S.j)(`/admin/agents/${e.agentId}`),a.set("active",n?"1":"0"),r.push(`${t}?${a.toString()}`),s.message},error:e=>e.message})}})]})]}),(0,n.jsx)(s.a,{tabs:P})]})}},31792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var n=r(60926),s=r(29411),a=r(59571),i=r(36162),o=r(34451),c=r(18662),l=r(23009),d=r(52419),u=r(15487),m=r(14761),p=r(64947),g=r(29220),f=r(45475),h=r(39228),x=r(32167),S=r(93633);let E=S.z.object({subject:S.z.string({required_error:"User Key is required"}),message:S.z.string({required_error:"User Secret is required"})});function v(){let{t:e}=(0,h.$G)(),[t,r]=(0,g.useTransition)(),S=(0,p.lr)(),v=(0,p.UO)(),P=(0,f.cI)({resolver:(0,u.F)(E),defaultValues:{subject:"",message:""}});return(0,n.jsx)(a.UQ,{type:"multiple",defaultValue:["API"],children:(0,n.jsx)("div",{className:"flex flex-col gap-4 p-4",children:(0,n.jsx)("div",{className:"rounded-xl border border-border bg-background",children:(0,n.jsxs)(a.Qd,{value:"API",className:"border-none px-4 py-0",children:[(0,n.jsx)(a.o4,{className:"py-6 hover:no-underline",children:(0,n.jsx)("div",{className:"flex items-center gap-1",children:(0,n.jsxs)("p",{className:"text-base font-medium leading-[22px]",children:[e("Send an email to")," ",S.get("name")]})})}),(0,n.jsx)(a.vF,{className:"flex flex-col gap-6 border-t border-divider px-1 pt-4",children:(0,n.jsx)(o.l0,{...P,children:(0,n.jsxs)("form",{onSubmit:P.handleSubmit(t=>{r(async()=>{let r=await (0,d.Y)(t,v.userId);r.status?x.toast.success(r.message):x.toast.error(e(r.message))})}),className:"flex flex-col gap-4",children:[(0,n.jsx)(o.Wi,{name:"subject",control:P.control,render:({field:t})=>(0,n.jsxs)(o.xJ,{children:[(0,n.jsx)(o.lX,{children:e("Subject")}),(0,n.jsx)(c.I,{type:"text",placeholder:e("Subject of your mail..."),...t}),(0,n.jsx)(o.zG,{})]})}),(0,n.jsx)(o.Wi,{name:"message",control:P.control,render:({field:t})=>(0,n.jsxs)(o.xJ,{children:[(0,n.jsx)(o.lX,{children:e("Message")}),(0,n.jsx)(l.g,{placeholder:e("Write a message here..."),rows:10,...t}),(0,n.jsx)(o.zG,{})]})}),(0,n.jsx)("div",{className:"flex items-center justify-end gap-4",children:(0,n.jsx)(i.z,{disabled:t,className:"rounded-xl",children:t?(0,n.jsx)(s.Loader,{title:e("Sending..."),className:"text-primary-foreground"}):(0,n.jsxs)(n.Fragment,{children:[e("Send"),(0,n.jsx)(m.Z,{size:16})]})})})]})})})]})})})})}},23181:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var n=r(61394),s=r(29220),a=r(31036),i=r.n(a),o=["variant","color","size"],c=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M16.19 2H7.81C4.17 2 2 4.17 2 7.81v8.37C2 19.83 4.17 22 7.81 22h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81C22 4.17 19.83 2 16.19 2ZM7.75 9.14c0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48 0 .81-.66 1.48-1.48 1.48a1.49 1.49 0 0 1-1.48-1.48Zm1.07 6.88c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L9.35 15.8c-.15.15-.34.22-.53.22Zm6.95.32c-.81 0-1.48-.66-1.48-1.48 0-.81.66-1.48 1.48-1.48.81 0 1.48.66 1.48 1.48s-.66 1.48-1.48 1.48Z",fill:t}))},l=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M2 13.05V15c0 5 2 7 7 7h6c5 0 7-2 7-7V9c0-5-2-7-7-7H9C4 2 2 4 2 9M8.32 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{opacity:".4",d:"M16.192 2h-8.37c-3.64 0-5.81 2.17-5.81 5.81v8.37c0 3.64 2.17 5.81 5.81 5.81h8.37c3.64 0 5.81-2.17 5.81-5.81V7.81c0-3.64-2.17-5.81-5.81-5.81Z",fill:t}),s.createElement("path",{d:"M8.822 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM9.23 7.66c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48a1.49 1.49 0 0 0-1.48-1.48ZM15.769 13.39c-.81 0-1.48.66-1.48 1.48 0 .81.66 1.48 1.48 1.48.81 0 1.48-.66 1.48-1.48s-.66-1.48-1.48-1.48Z",fill:t}))},u=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7ZM8.57 15.27l6.54-6.54",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{d:"M8.98 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.52 16.09a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},m=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h6c5.43 0 7.75 2.32 7.75 7.75v6c0 5.43-2.32 7.75-7.75 7.75Zm-6-20C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25V9c0-4.61-1.64-6.25-6.25-6.25H9Z",fill:t}),s.createElement("path",{d:"M8.568 16.02c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l6.55-6.55c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.55 6.55c-.15.15-.34.22-.53.22ZM8.98 11.108c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48a.48.48 0 0 0-.48-.48ZM15.519 16.839c-1.09 0-1.98-.89-1.98-1.98 0-1.09.89-1.98 1.98-1.98 1.09 0 1.98.89 1.98 1.98 0 1.09-.89 1.98-1.98 1.98Zm0-2.45a.48.48 0 0 0-.48.48c0 .27.21.48.48.48s.48-.21.48-.48-.21-.48-.48-.48Z",fill:t}))},p=function(e){var t=e.color;return s.createElement(s.Fragment,null,s.createElement("path",{d:"M9 2h6c5 0 7 2 7 7v6c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.createElement("path",{opacity:".4",d:"m8.32 15.27 6.54-6.54M8.73 10.37a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46ZM15.269 16.089a1.23 1.23 0 1 0 0-2.46 1.23 1.23 0 0 0 0 2.46Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},g=function(e,t){switch(e){case"Bold":return s.createElement(c,{color:t});case"Broken":return s.createElement(l,{color:t});case"Bulk":return s.createElement(d,{color:t});case"Linear":default:return s.createElement(u,{color:t});case"Outline":return s.createElement(m,{color:t});case"TwoTone":return s.createElement(p,{color:t})}},f=(0,s.forwardRef)(function(e,t){var r=e.variant,a=e.color,i=e.size,c=(0,n._)(e,o);return s.createElement("svg",(0,n.a)({},c,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:i,height:i,viewBox:"0 0 24 24",fill:"none"}),g(r,a))});f.propTypes={variant:i().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:i().string,size:i().oneOfType([i().string,i().number])},f.defaultProps={variant:"Linear",color:"currentColor",size:"24"},f.displayName="PercentageSquare"},12771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,runtime:()=>a});var n=r(42416);r(87908);let s=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\_components\Tabbar.tsx#Tabbar`),a="edge";function i({children:e}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s,{}),e]})}},12885:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}},99984:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}},86894:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(18264).D)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\app\(protected)\@admin\agents\[userId]\[agentId]\send-email\page.tsx#default`)},29670:(e,t,r)=>{"use strict";function n({children:e}){return e}r.r(t),r.d(t,{default:()=>n}),r(87908)},74030:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(42416),s=r(21237);function a(){return(0,n.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,n.jsx)(s.a,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[529,6578,3390,6165,4969,7283,5089,3711,7066],()=>t(1526));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/(protected)/@admin/agents/[userId]/[agentId]/send-email/page"]=r}]);
//# sourceMappingURL=page.js.map