exports.id=1555,exports.ids=[1555],exports.modules={26807:(e,s,t)=>{Promise.resolve().then(t.bind(t,73e3)),Promise.resolve().then(t.bind(t,75285))},44677:(e,s,t)=>{"use strict";t.d(s,{Z:()=>p});var a=t(10326),n=t(61718),r=t(720),l=t(71227),i=t(29612),d=t(55632),c=t(54432),o=t(31048),m=t(88846),x=t(19395),u=t(35047),h=t(70012);function p({form:e,defaultCountry:s,defaultPhone:t}){let{t:p}=(0,h.$G)(),f=(0,u.useParams)(),{deviceLocation:g}=(0,x.a)();return(0,a.jsxs)(a.<PERSON>agment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[a.jsx(d.Wi,{control:e.control,name:"firstName",render:({field:e})=>(0,a.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[a.jsx(d.lX,{children:p("First name")}),a.jsx(d.NI,{children:a.jsx(c.I,{type:"text",placeholder:p("First name"),...e})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:e.control,name:"lastName",render:({field:e})=>(0,a.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[a.jsx(d.lX,{children:p("Last name")}),a.jsx(d.NI,{children:a.jsx(c.I,{type:"text",placeholder:p("Last name"),...e})}),a.jsx(d.zG,{})]})})]}),a.jsx(d.Wi,{control:e.control,name:"gender",render:({field:e})=>(0,a.jsxs)(d.xJ,{children:[(0,a.jsxs)(d.lX,{children:[" ",p("Gender")," "]}),a.jsx(d.NI,{children:(0,a.jsxs)(m.E,{defaultValue:e.value,onValueChange:e.onChange,className:"grid grid-cols-12 gap-4",children:[(0,a.jsxs)(o.Z,{"data-selected":"male"===e.value,className:"relative col-span-12 flex h-12 items-center space-x-2.5 rounded-md border-2 border-accent bg-accent px-4 py-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected sm:col-span-6",children:[a.jsx(m.m,{value:"male",className:"absolute opacity-0"}),a.jsx("span",{children:p("Male")})]}),(0,a.jsxs)(o.Z,{"data-selected":"female"===e.value,className:"relative col-span-12 flex h-12 items-center space-x-2.5 rounded-md border-2 border-accent bg-accent px-4 py-4 hover:cursor-pointer hover:bg-primary-selected data-[selected=true]:border-primary data-[selected=true]:bg-primary-selected sm:col-span-6",children:[a.jsx(m.m,{value:"female",className:"absolute opacity-0"}),a.jsx("span",{children:p("Female")})]})]})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:e.control,name:"email",render:({field:e})=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.lX,{children:p("Email")}),a.jsx(d.NI,{children:a.jsx(c.I,{type:"email",placeholder:p("Email address"),...e})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:e.control,name:"dob",render:({field:e})=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.lX,{children:p("Date of birth")}),a.jsx(d.NI,{children:a.jsx(r.M,{...e})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:e.control,name:"phone",render:({field:s})=>(0,a.jsxs)(d.xJ,{className:"w-full",children:[a.jsx(d.lX,{children:p("Phone")}),a.jsx(d.NI,{children:a.jsx(l.E,{value:t,onChange:s.onChange,onBlur:s=>{s?e.setError("phone",{type:"custom",message:s}):e.clearErrors("phone")},options:{initialCountry:g?.countryCode}})}),a.jsx(d.zG,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[a.jsx(d.Wi,{control:e.control,name:f.staffId?"newPassword":"password",render:({field:e})=>(0,a.jsxs)(d.xJ,{className:"col-span-12",children:[(0,a.jsxs)(d.lX,{children:[" ",p("Password")," "]}),a.jsx(d.NI,{children:a.jsx(i.W,{placeholder:p("Password"),...e})}),a.jsx(d.zG,{})]})}),!f.staffId&&a.jsx(d.Wi,{control:e.control,name:"passwordConfirmation",render:({field:e})=>(0,a.jsxs)(d.xJ,{className:"col-span-12",children:[(0,a.jsxs)(d.lX,{children:[" ",p("Confirm password")," "]}),a.jsx(d.NI,{children:a.jsx(i.W,{placeholder:p("Password"),...e})}),a.jsx(d.zG,{})]})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[a.jsx(d.Wi,{control:e.control,name:"addressLine",render:({field:e})=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.lX,{children:p("Address")}),a.jsx(d.NI,{children:a.jsx(c.I,{type:"text",placeholder:p("Address line"),...e})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:e.control,name:"countryCode",render:({field:e})=>(0,a.jsxs)(d.xJ,{children:[a.jsx(d.NI,{children:a.jsx(n.g,{defaultValue:s,onSelectChange:s=>{e.onChange(s.code.cca2)}})}),a.jsx(d.zG,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[a.jsx(d.Wi,{control:e.control,name:"city",render:({field:e})=>(0,a.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[a.jsx(d.NI,{children:a.jsx(c.I,{type:"text",placeholder:p("City"),...e})}),a.jsx(d.zG,{})]})}),a.jsx(d.Wi,{control:e.control,name:"zipCode",render:({field:e})=>(0,a.jsxs)(d.xJ,{className:"col-span-12 lg:col-span-6",children:[a.jsx(d.NI,{children:a.jsx(c.I,{type:"text",placeholder:p("Zip code"),...e})}),a.jsx(d.zG,{})]})})]})]})]})}},61718:(e,s,t)=>{"use strict";t.d(s,{g:()=>u});var a=t(10326),n=t(17577),r=t(92392),l=t(80609),i=t(2454),d=t(30811),c=t(1868),o=t(77863),m=t(6216),x=t(70012);function u({allCountry:e=!1,defaultValue:s,defaultCountry:t,onSelectChange:u,disabled:h=!1,triggerClassName:p,arrowClassName:f,flagClassName:g,display:j,placeholderClassName:y,align:b="start",side:v="bottom"}){let{t:N}=(0,x.$G)(),{countries:w,getCountryByCode:k,isLoading:C}=(0,c.F)(),[Z,P]=n.useState(!1),[I,z]=n.useState(s);return n.useEffect(()=>{s&&z(s)},[s]),n.useEffect(()=>{(async()=>{t&&await k(t,e=>{e&&(z(e),u(e))})})()},[t]),(0,a.jsxs)(d.J2,{open:Z,onOpenChange:P,children:[(0,a.jsxs)(d.xo,{disabled:h,className:(0,o.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",p),children:[I?a.jsx("div",{className:"flex flex-1 items-center",children:(0,a.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[a.jsx(l.W,{className:g,countryCode:I.code?.cca2==="*"?"UN":I.code?.cca2}),void 0!==j?j(I):a.jsx("span",{children:I.name})]})}):a.jsx("span",{className:(0,o.ZP)("text-placeholder",y),children:N("Select country")}),a.jsx(m.Z,{className:(0,o.ZP)("size-6",f)})]}),a.jsx(d.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:b,side:v,children:(0,a.jsxs)(i.mY,{children:[a.jsx(i.sZ,{placeholder:N("Search...")}),a.jsx(i.e8,{children:(0,a.jsxs)(i.fu,{children:[C&&a.jsx(r.Loader,{}),e&&(0,a.jsxs)(i.di,{value:N("All countries"),onSelect:()=>{z({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),u({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),P(!1)},children:[a.jsx(l.W,{countryCode:"UN"}),a.jsx("span",{className:"pl-1.5",children:N("All countries")})]}),w?.map(e=>"officially-assigned"===e.status?a.jsxs(i.di,{value:e.name,onSelect:()=>{z(e),u(e),P(!1)},children:[a.jsx(l.W,{countryCode:e.code.cca2}),a.jsxs("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},720:(e,s,t)=>{"use strict";t.d(s,{M:()=>x});var a=t(10326),n=t(9346),r=t(30811),l=t(77863),i=t(71305),d=t(76129),c=t(17577),o=t.n(c),m=t(70012);let x=o().forwardRef(({value:e,onChange:s,className:t,placeholderClassName:c,options:x},u)=>{let{t:h}=(0,m.$G)(),[p,f]=o().useState(!1);return(0,a.jsxs)(r.J2,{open:p,onOpenChange:f,children:[(0,a.jsxs)(r.xo,{disabled:!!x?.disabled,className:(0,l.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",t),children:[a.jsx("div",{ref:u,className:"flex flex-1 items-center",children:a.jsx("div",{className:"flex flex-1 items-center gap-2 text-left",children:e?(0,i.WU)(e,"dd/MM/yyyy"):a.jsx("span",{className:(0,l.ZP)("text-placeholder",c),children:h("Pick a Date")})})}),a.jsx(d.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),a.jsx(r.yk,{className:"w-auto p-0",align:"start",children:a.jsx(n.f,{...x,mode:"single",initialFocus:!0,selected:e??void 0,onSelect:e=>{s(e),f(!1)}})})]})})},71227:(e,s,t)=>{"use strict";t.d(s,{E:()=>C});var a=t(10326),n=t(92392),r=t(80609),l=t(2454),i=t(54432),d=t(30811),c=t(1868),o=t(77863),m=t(26138),x=t(6216),u=t(33436),h=t(34197),p=t(4017),f=t(70107),g=t(55991),j=t(4981),y=t(71132),b=t(60715),v=t(5389),N=t(70012),w=t(17577);let k={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function C({value:e,defaultValue:s="",onChange:t,onBlur:n,disabled:r,inputClassName:l,options:d}){let[c,m]=(0,w.useState)(s??""),[x,b]=(0,w.useState)(""),[N,C]=(0,w.useState)(d?.initialCountry),P=e=>{if(e)try{let s=u.S(e,N);s?(C(s.country),b(`+${s.countryCallingCode}`),m(s.formatNational())):m(e)}catch(s){m(e)}else m(e)},I=h.L(N||d?.initialCountry||"US",v.Z);return(0,a.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(Z,{country:N,disabled:r,initialCountry:d?.initialCountry,onSelect:e=>{let s=e.code.cca2?.toUpperCase(),t=p.G(s);b(`+${t}`),C(s)}}),a.jsx("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:x||`+${I?.countryCallingCode}`})]}),a.jsx(i.I,{type:"tel",className:(0,o.ZP)("rounded-l-none pl-2",l),value:c,onChange:e=>{let{value:s}=e.target,a=u.S(s,N);n?.(""),a&&f.t(s,N)&&g.q(s,N)?(C(a.country),b(`+${a.countryCallingCode}`),t?.(a.number),m(s)):(a?m(a.nationalNumber):m(s),t?.(s))},onPaste:e=>{let s=e.clipboardData.getData("Text"),a=u.S(s);if(a&&f.t(s))P(a.formatNational()),C(a.country),b(`+${a.countryCallingCode}`),t?.(a.number),n?.("");else{let e=u.S(s,N);e&&f.t(s,N)&&(P(e.formatNational()),t?.(e.number),n?.(""))}},onBlur:()=>{if(c&&!j.y(c,N)){let e=y.d(c,N);e&&n?.(k[e])}},placeholder:I?.formatNational(),disabled:r})]})}function Z({initialCountry:e,country:s,onSelect:t,disabled:n}){let[l,i]=(0,w.useState)(!1);return(0,a.jsxs)(d.J2,{open:l,onOpenChange:i,children:[(0,a.jsxs)(d.xo,{disabled:n,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[e||s?a.jsx(r.W,{countryCode:s||e,className:"aspect-auto h-[18px] w-7 flex-1"}):a.jsx(m.Z,{}),a.jsx(x.Z,{variant:"Bold",size:16})]}),a.jsx(d.yk,{align:"start",className:"h-fit p-0",children:a.jsx(P,{defaultValue:s||e,onSelect:e=>{t(e),i(!1)}})})]})}function P({defaultValue:e,onSelect:s}){let{countries:t,isLoading:i}=(0,c.F)(),{t:d}=(0,N.$G)();return(0,a.jsxs)(l.mY,{children:[a.jsx(l.sZ,{placeholder:d("Search country by name"),className:"placeholder:text-input-placeholder"}),a.jsx(l.e8,{children:a.jsx(l.fu,{children:i?a.jsx(l.di,{children:a.jsx(n.Loader,{})}):t.filter(e=>{let s=e.code.cca2?.toUpperCase();return b.o().includes(s)})?.map(t=>a.jsxs(l.di,{value:t.name,"data-active":t.code.cca2===e,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>s(t),children:[a.jsx(r.W,{countryCode:t.code.cca2}),t.name]},t.code.ccn3))})})]})}},29612:(e,s,t)=>{"use strict";t.d(s,{W:()=>o});var a=t(10326),n=t(17577),r=t(90772),l=t(54432),i=t(77863),d=t(9489),c=t(75138);let o=n.forwardRef(({className:e,type:s,...t},o)=>{let[m,x]=n.useState(!1);return(0,a.jsxs)("div",{className:"relative",children:[a.jsx(l.I,{type:m?"text":"password",className:(0,i.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...t}),a.jsx(r.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),x(e=>!e)},children:m?a.jsx(d.Z,{}):a.jsx(c.Z,{})})]})});o.displayName="PasswordInput"},75285:(e,s,t)=>{"use strict";t.d(s,{default:()=>_});var a=t(10326),n=t(5158),r=t(90772),l=t(81638),i=t(6216),d=t(90434),c=t(35047),o=t(17577);function m({sidebarItem:e}){let[s,t]=o.useState("(dashboard)"),[m,x]=o.useState(!1),{setIsExpanded:u,device:h}=(0,l.q)(),p=(0,c.useSelectedLayoutSegment)();return o.useEffect(()=>{t(p)},[]),o.useEffect(()=>{x(e.segment===p)},[p,e.segment]),(0,a.jsxs)("div",{"data-extended":m,className:"h-fit overflow-hidden transition-all duration-300 data-[extended=true]:rounded-2xl data-[extended=true]:bg-accent",children:[(0,a.jsxs)(d.default,{href:e.link,onClick:()=>{t(e.segment),e.children?.length||"Desktop"===h||u(!1)},"data-active":p===e.segment,className:"group inline-flex h-12 w-full items-center gap-2 rounded-2xl p-2 font-medium hover:bg-accent data-[active=true]:bg-accent",children:[a.jsx(n.J,{condition:!!e.icon,children:a.jsx("div",{"data-active":p===e.segment,className:"flex h-8 w-8 items-center justify-center rounded-full bg-secondary-500 data-[active=true]:bg-primary data-[active=true]:text-white",children:e?.icon})}),a.jsx("span",{className:"flex-1",children:e.name}),a.jsx(n.J,{condition:!!e.children?.length,children:a.jsx(r.z,{variant:"ghost",type:"button",size:"icon","data-extended":m,className:"group rounded-xl hover:bg-muted",onClick:e=>{e.stopPropagation(),e.preventDefault(),x(!m)},children:a.jsx(i.Z,{size:16,className:"group-data-[extended=true]:rotate-180"})})})]}),a.jsx(n.J,{condition:!!e.children?.length,children:a.jsx("ul",{"data-extended":m,className:"ml-5 flex flex-col gap-1.5 transition-all duration-300 data-[extended=true]:pb-2",style:{height:m&&e.children?.length?32*e.children.length+20:"0px"},children:e.children?.map(e=>a.jsx("li",{children:a.jsxs(d.default,{href:e.link,"data-active":s===e.segment,onClick:()=>{t(e.segment),"Desktop"!==h&&u(!1)},className:"group inline-flex w-full items-center gap-5 py-1.5 text-sm font-semibold leading-5 hover:text-primary data-[active=true]:text-primary",children:[a.jsx("span",{className:"block size-2 rounded-full bg-secondary-text group-hover:bg-primary group-data-[active=true]:bg-primary"}),e.name]})},e.key))})})]})}var x=t(8281),u=t(4066),h=t(77863),p=t(1178),f=t(29169),g=t(40420),j=t(78564),y=t(53105),b=t(81770),v=t(45922),N=t(29764),w=t(26920),k=t(9155),C=t(41334),Z=t(73686),P=t(75073),I=t(44221),z=t(46226),S=t(70012);function _(){let{t:e}=(0,S.$G)(),{isExpanded:s,setIsExpanded:t}=(0,l.q)(),{logo:n,siteName:i}=(0,u.T)(),c=[{id:"sidebarItem1",title:"",items:[{key:"dashboard",name:e("Dashboard"),icon:a.jsx(p.Z,{size:"20"}),link:"/",segment:"(dashboard)"},{key:"deposits",name:e("Deposits"),icon:a.jsx(f.Z,{size:"20"}),link:"/deposits",segment:"deposits",children:[{key:"deposits-pending",name:e("Pending"),link:"/deposits",segment:"deposits"},{key:"deposits-history",name:e("History"),link:"/deposits/history",segment:"history"}]},{key:"transfers",name:e("Transfers"),icon:a.jsx(g.Z,{size:"20"}),link:"/transfers",segment:"transfers",children:[{key:"transfers-pending",segment:"transfers",name:e("Pending"),link:"/transfers"},{key:"transfers-history",segment:"transfers-history ",name:e("History"),link:"/transfers/history"}]},{key:"withdraws",name:e("Withdraws"),icon:a.jsx(j.Z,{size:"20"}),link:"/withdraws",segment:"withdraws",children:[{key:"withdraws-pending",segment:"withdraws",name:e("Pending"),link:"/withdraws"},{key:"withdraws-history",segment:"withdraws-history",name:e("History"),link:"/withdraws/history"}]},{key:"exchanges",name:e("Exchanges"),icon:a.jsx(y.Z,{size:"20"}),link:"/exchanges",segment:"exchanges",children:[{key:"exchanges-pending",segment:"exchanges",name:e("Pending"),link:"/exchanges"},{key:"exchanges-list",segment:"exchanges-history",name:e("History"),link:"/exchanges/history"}]},{key:"payments",name:e("Payments"),icon:a.jsx(b.Z,{size:"20"}),link:"/payments",segment:"payments"},{key:"cards",segment:"cards",name:e("Cards"),icon:a.jsx(v.Z,{size:"20"}),link:"/cards"},{key:"investments",name:e("Investments"),icon:a.jsx(N.Z,{size:"20"}),link:"/investments",segment:"investments"}]},{id:"sidebarItem2",items:[{key:"customers",segment:"customers",name:e("Customers"),icon:a.jsx(w.Z,{size:"20"}),link:"/customers",children:[{key:"customers",segment:"customers",name:e("Pending Kyc"),link:"/customers"},{key:"customers-list",segment:"customers-list",name:e("Customer List"),link:"/customers/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/customers/bulk-email"}]},{key:"merchants",segment:"merchants",name:e("Merchants"),icon:a.jsx(k.Z,{size:"20"}),link:"/merchants",children:[{key:"merchants",segment:"merchants",name:e("Pending"),link:"/merchants"},{key:"merchant-list",segment:"merchants-list",name:e("Merchant List"),link:"/merchants/list"},{key:"payment-request",segment:"payment-request",name:e("Payment Request"),link:"/merchants/payment-request"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/merchants/bulk-email"}]},{key:"agents",segment:"agents",name:e("Agents"),icon:a.jsx(C.Z,{size:"20"}),link:"/agents",children:[{key:"agents",segment:"agents",name:e("Pending"),link:"/agents"},{key:"agent-list",segment:"agents-list",name:e("Agent List"),link:"/agents/list"},{key:"bulk-email",segment:"bulk-email",name:e("Bulk Email"),link:"/agents/bulk-email"}]},{key:"staffs",segment:"staffs",name:e("Staffs"),icon:a.jsx(Z.Z,{size:"20"}),link:"/staffs"},{key:"settings",segment:"settings",name:e("Settings"),icon:a.jsx(P.Z,{size:"20"}),link:"/settings"}]}];return(0,a.jsxs)("div",{"data-expanded":s,className:"absolute z-[60] flex h-screen min-h-80 min-w-80 flex-col border-r border-foreground/10 bg-background transition-all duration-500 data-[expanded=false]:absolute data-[expanded=false]:-translate-x-full lg:relative lg:z-auto",children:[a.jsx(r.z,{size:"icon",variant:"outline",onClick:()=>t(!1),className:`absolute -right-5 top-4 rounded-full bg-background ${s?"":"hidden"} lg:hidden`,children:a.jsx(I.Z,{})}),a.jsx("div",{className:"flex h-[76px] items-center justify-center border-b border-divider-secondary px-4",children:a.jsx(d.default,{href:"/",className:"flex items-center justify-center",children:a.jsx(z.default,{src:(0,h.qR)(n),width:160,height:40,alt:i,className:"max-h-10 object-contain"})})}),a.jsx("div",{className:"flex flex-1 flex-col overflow-y-auto overflow-x-hidden px-4 py-4",children:c.map(e=>(0,a.jsxs)("div",{children:[""!==e.title?a.jsx("div",{children:a.jsx(x.Z,{className:"my-4"})}):null,a.jsx("ul",{className:"flex flex-col gap-1",children:e.items?.map(e=>a.jsx("li",{children:a.jsx(m,{sidebarItem:e})},e.key))})]},e.id))})]})}},80609:(e,s,t)=>{"use strict";t.d(s,{W:()=>l});var a=t(10326),n=t(77863),r=t(46226);function l({countryCode:e,className:s,url:t}){return e||t?a.jsx(r.default,{src:t??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,n.ZP)("rounded-[2px]",s)}):null}},9346:(e,s,t)=>{"use strict";t.d(s,{f:()=>o});var a=t(10326),n=t(11890),r=t(39183),l=t(941);t(17577);var i=t(18493),d=t(90772),c=t(77863);function o({className:e,classNames:s,showOutsideDays:t=!0,...o}){return a.jsx(i._W,{showOutsideDays:t,className:(0,c.ZP)("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,c.ZP)((0,d.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.ZP)((0,d.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:({...e})=>a.jsx(n.Z,{className:"h-4 w-4"}),IconRight:({...e})=>a.jsx(r.Z,{className:"h-4 w-4"}),Dropdown:({...e})=>(0,a.jsxs)("div",{className:"relative",children:[a.jsx("select",{...e,style:{opacity:0,position:"absolute"}}),(0,a.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[a.jsx("span",{className:"text-sm",children:e.caption}),a.jsx(l.Z,{className:"size-3"})]})]})},...o})}o.displayName="Calendar"},55632:(e,s,t)=>{"use strict";t.d(s,{NI:()=>f,Wi:()=>m,l0:()=>c,lX:()=>p,xJ:()=>h,zG:()=>g});var a=t(10326),n=t(34214),r=t(17577),l=t(74723),i=t(31048),d=t(77863);let c=l.RV,o=r.createContext({}),m=({...e})=>a.jsx(o.Provider,{value:{name:e.name},children:a.jsx(l.Qr,{...e})}),x=()=>{let e=r.useContext(o),s=r.useContext(u),{getFieldState:t,formState:a}=(0,l.Gc)(),n=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=s;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}},u=r.createContext({}),h=r.forwardRef(({className:e,...s},t)=>{let n=r.useId();return a.jsx(u.Provider,{value:{id:n},children:a.jsx("div",{ref:t,className:(0,d.ZP)("space-y-2",e),...s})})});h.displayName="FormItem";let p=r.forwardRef(({className:e,required:s,...t},n)=>{let{error:r,formItemId:l}=x();return a.jsx("span",{children:a.jsx(i.Z,{ref:n,className:(0,d.ZP)(r&&"text-base font-medium text-destructive",e),htmlFor:l,...t})})});p.displayName="FormLabel";let f=r.forwardRef(({...e},s)=>{let{error:t,formItemId:r,formDescriptionId:l,formMessageId:i}=x();return a.jsx(n.g7,{ref:s,id:r,"aria-describedby":t?`${l} ${i}`:`${l}`,"aria-invalid":!!t,...e})});f.displayName="FormControl",r.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:n}=x();return a.jsx("p",{ref:t,id:n,className:(0,d.ZP)("text-sm text-muted-foreground",e),...s})}).displayName="FormDescription";let g=r.forwardRef(({className:e,children:s,...t},n)=>{let{error:r,formMessageId:l}=x(),i=r?String(r?.message):s;return i?a.jsx("p",{ref:n,id:l,className:(0,d.ZP)("text-sm font-medium text-destructive",e),...t,children:i}):null});g.displayName="FormMessage"},54432:(e,s,t)=>{"use strict";t.d(s,{I:()=>l});var a=t(10326),n=t(17577),r=t(77863);let l=n.forwardRef(({className:e,type:s,...t},n)=>a.jsx("input",{type:s,className:(0,r.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",e),ref:n,...t}));l.displayName="Input"},31048:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(10326),n=t(34478),r=t(79360),l=t(17577),i=t(77863);let d=(0,r.j)("text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef(({className:e,...s},t)=>a.jsx(n.f,{ref:t,className:(0,i.ZP)(d(),e),...s}));c.displayName=n.f.displayName;let o=c},88846:(e,s,t)=>{"use strict";t.d(s,{E:()=>d,m:()=>c});var a=t(10326),n=t(17577),r=t(18623),l=t(53982),i=t(77863);let d=n.forwardRef(({className:e,...s},t)=>a.jsx(r.fC,{className:(0,i.ZP)("grid gap-2",e),...s,ref:t}));d.displayName=r.fC.displayName;let c=n.forwardRef(({className:e,...s},t)=>a.jsx(r.ck,{ref:t,className:(0,i.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:a.jsx(r.z$,{className:"flex items-center justify-center",children:a.jsx(l.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));c.displayName=r.ck.displayName},1868:(e,s,t)=>{"use strict";t.d(s,{F:()=>c});class a{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var n=t(44099),r=t(85999),l=t(84455);let i=n.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),d="name,cca2,ccn3,cca3,status,flag,flags";function c(){let{data:e,isLoading:s,...t}=(0,l.ZP)(`/all?fields=${d}`,e=>i.get(e)),c=e?.data,o=async(e,s)=>{try{let t=await i.get(`/alpha/${e.toLowerCase()}?fields=${d}`),n=t.data?new a(t.data):null;s(n)}catch(e){n.default.isAxiosError(e)&&r.toast.error("Failed to fetch country")}};return{countries:c?c.map(e=>new a(e)):[],isLoading:s,getCountryByCode:o,...t}}},11840:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(19510),n=t(40099);let r=(0,t(68570).createProxy)(String.raw`C:\Users\<USER>\Desktop\IDEES SQQS\PAYSNAPo\Main\frontend\components\common\layout\AdminSidenav.tsx#default`);async function l({children:e}){return(0,a.jsxs)("div",{className:"flex h-screen",children:[a.jsx(r,{}),(0,a.jsxs)("div",{className:"relative h-full w-full overflow-hidden",children:[a.jsx(n.Z,{}),a.jsx("div",{className:"h-[calc(100vh-76px)] overflow-y-auto overflow-x-hidden pb-20 md:pb-0",children:e})]})]})}t(71159)},33661:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(19510),n=t(48413);function r(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(n.a,{})})}},44305:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(19510),n=t(48413);function r(){return a.jsx("div",{className:"flex items-center justify-center py-10",children:a.jsx(n.a,{})})}}};