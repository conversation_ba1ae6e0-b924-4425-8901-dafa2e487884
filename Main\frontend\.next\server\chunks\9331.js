"use strict";exports.id=9331,exports.ids=[9331],exports.modules={59331:(e,a,s)=>{s.d(a,{k:()=>w});var t=s(10326),l=s(17577),c=s(5158),n=s(61718),d=s(720),i=s(92392),r=s(90772),o=s(31048),h=s(30811),x=s(34474),m=s(90799),u=s(61610);class j{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.params=e?.params?JSON.parse(e?.params):null,this.currencyCode=e?.currencyCode,this.countryCode=e?.countryCode,this.active=!!e?.active,this.activeApi=!!e?.activeApi,this.recommended=!!e?.recommended,this.minAmount=e?.minAmount??0,this.maxAmount=e?.maxAmount??0,this.fixedCharge=e?.fixedCharge??0,this.percentageCharge=e?.percentageCharge,this.createdAt=e?.createdAt?new Date(e.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e.updatedAt):null}}var p=s(64414),f=s(71305),g=s(11342),y=s(35047),v=s(70012);function w({canFilterByStatus:e=!0,canFilterByDate:a=!0,canFilterByMethod:s=!1,canFilterByGateway:w=!1,canFilterByAgent:N=!1,canFilterByAgentMethod:C=!1,canFilterUser:b=!1,canFilterByGender:A=!1,canFilterByCountryCode:S=!1}){let{t:P}=(0,v.$G)(),Z=(0,y.useSearchParams)(),J=(0,y.usePathname)(),Q=(0,y.useRouter)(),[L,k]=l.useState({}),[D,$]=l.useState(!1),{data:M,isLoading:K}=(0,m.d)("/methods"),{data:F,isLoading:O}=(0,m.d)("/gateways"),{data:W,isLoading:z}=(0,m.d)(C?"/agent-methods?limit=100&page=1":""),E=(e,a)=>{let s=new URLSearchParams(Z.toString());a?(s.set(e,a),k(s=>({...s,[e]:a}))):(s.delete(e),k(a=>({...a,[e]:""}))),Q.replace(`${J}?${s.toString()}`)};return l.useEffect(()=>{let e=Object.fromEntries(Z.entries());e&&k(e)},[]),(0,t.jsxs)(h.J2,{open:D,onOpenChange:$,children:[t.jsx(h.xo,{asChild:!0,children:(0,t.jsxs)(r.z,{variant:"outline",children:[t.jsx(g.Z,{size:20}),P("Filter")]})}),t.jsx(h.yk,{className:"w-full min-w-[300px] max-w-[400px]",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[t.jsx(c.J,{condition:e,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[t.jsx(o.Z,{className:"text-sm font-normal text-secondary-text",children:b?"Status":"Transaction status"}),(0,t.jsxs)(x.Ph,{value:L?.status,onValueChange:e=>E("status",e),children:[t.jsx(x.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:t.jsx(x.ki,{placeholder:P("Status")})}),(0,t.jsxs)(x.Bw,{children:[(0,t.jsxs)(c.J,{condition:b,children:[t.jsx(x.Ql,{value:"true",children:P("Active")}),t.jsx(x.Ql,{value:"false",children:P("Inactive")})]}),(0,t.jsxs)(c.J,{condition:!b,children:[(0,t.jsxs)(x.Ql,{value:"pending",children:[" ",P("Pending")," "]}),(0,t.jsxs)(x.Ql,{value:"completed",children:[P("Completed")," "]}),(0,t.jsxs)(x.Ql,{value:"failed",children:[P("Failed")," "]})]})]})]})]})}),t.jsx(c.J,{condition:A,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[t.jsx(o.Z,{className:"text-sm font-normal text-secondary-text",children:P("Gender")}),(0,t.jsxs)(x.Ph,{value:L?.gender,onValueChange:e=>E("gender",e),children:[t.jsx(x.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:t.jsx(x.ki,{placeholder:P("Gender")})}),(0,t.jsxs)(x.Bw,{children:[t.jsx(x.Ql,{value:"male",children:P("Male")}),t.jsx(x.Ql,{value:"female",children:P("Female")})]})]})]})}),t.jsx(c.J,{condition:s,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[t.jsx(o.Z,{className:"text-sm font-normal text-secondary-text",children:P("Withdraw method")}),(0,t.jsxs)(x.Ph,{value:L?.method,onValueChange:e=>E("method",e),children:[t.jsx(x.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:t.jsx(x.ki,{placeholder:P("Withdraw method")})}),(0,t.jsxs)(x.Bw,{side:"right",align:"start",children:[t.jsx(c.J,{condition:N,children:t.jsx(x.Ql,{value:"agent",children:P("Agent")})}),K?t.jsx(i.Loader,{}):M?.data?.map(e=>new j(e))?.map(e=>t.jsx(x.Ql,{value:e.value,className:"border-b border-dashed",children:e.name},e.id))]})]})]})}),t.jsx(c.J,{condition:w,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[t.jsx(o.Z,{className:"text-sm font-normal text-secondary-text",children:P("Deposit gateway")}),(0,t.jsxs)(x.Ph,{value:L?.gateway,onValueChange:e=>E("method",e),children:[t.jsx(x.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:t.jsx(x.ki,{placeholder:P("Deposit gateway")})}),(0,t.jsxs)(x.Bw,{side:"right",align:"start",children:[t.jsx(c.J,{condition:N,children:t.jsx(x.Ql,{value:"agent",children:P("Agent")})}),O?t.jsx(i.Loader,{}):F?.data?.map(e=>new u.M(e))?.map(e=>t.jsxs(x.Ql,{value:e.value,className:"border-b border-dashed",children:[e.name," ",t.jsx("span",{className:"pl-1.5 text-secondary-text/80",children:e.value})]},e.id))]})]})]})}),t.jsx(c.J,{condition:C,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[t.jsx(o.Z,{className:"text-sm font-normal text-secondary-text",children:P("Agent method")}),(0,t.jsxs)(x.Ph,{value:L?.method,onValueChange:e=>E("method",e),children:[t.jsx(x.i4,{className:"h-10 w-full text-base data-[placeholder]:text-secondary-text",children:t.jsx(x.ki,{placeholder:P("Method")})}),t.jsx(x.Bw,{side:"right",align:"start",children:z?t.jsx(i.Loader,{}):W?.data?.data?.map(e=>new j(e))?.map(e=>t.jsx(x.Ql,{value:e.name,className:"border-b border-dashed",children:e.name},e.id))})]})]})}),t.jsx(c.J,{condition:a,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[t.jsx(o.Z,{className:"text-sm font-normal text-secondary-text",children:P("Date")}),t.jsx(d.M,{value:Object.prototype.hasOwnProperty.call(L,"date")&&L.date?new Date((0,p.Qc)(L.date,"yyyy-MM-dd",new Date)):void 0,onChange:e=>{E("date",e?(0,f.WU)(e,"yyyy-MM-dd"):"")},className:"h-10",placeholderClassName:"text-secondary-text"})]})}),t.jsx(c.J,{condition:S,children:(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2",children:[t.jsx(o.Z,{className:"text-sm font-normal text-secondary-text",children:P("Country")}),t.jsx(n.g,{defaultCountry:L?.countryCode,onSelectChange:e=>{E("countryCode",e.code.cca2)},triggerClassName:"h-10",placeholderClassName:"text-secondary-text",side:"right",align:"start"})]})}),(0,t.jsxs)("div",{className:"flex flex-col items-stretch space-y-2",children:[t.jsx(r.z,{type:"button",onClick:()=>$(!1),className:"h-10",children:P("Done")}),t.jsx(r.z,{type:"button",variant:"outline",onClick:()=>{let e=new URLSearchParams;Object.keys(L).forEach(a=>e.delete(a)),k({}),Q.replace(`${J}?${e.toString()}`)},className:"h-10",children:P("Clear Filter")})]})]})})]})}},61718:(e,a,s)=>{s.d(a,{g:()=>m});var t=s(10326),l=s(17577),c=s(92392),n=s(80609),d=s(2454),i=s(30811),r=s(1868),o=s(77863),h=s(6216),x=s(70012);function m({allCountry:e=!1,defaultValue:a,defaultCountry:s,onSelectChange:m,disabled:u=!1,triggerClassName:j,arrowClassName:p,flagClassName:f,display:g,placeholderClassName:y,align:v="start",side:w="bottom"}){let{t:N}=(0,x.$G)(),{countries:C,getCountryByCode:b,isLoading:A}=(0,r.F)(),[S,P]=l.useState(!1),[Z,J]=l.useState(a);return l.useEffect(()=>{a&&J(a)},[a]),l.useEffect(()=>{(async()=>{s&&await b(s,e=>{e&&(J(e),m(e))})})()},[s]),(0,t.jsxs)(i.J2,{open:S,onOpenChange:P,children:[(0,t.jsxs)(i.xo,{disabled:u,className:(0,o.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",j),children:[Z?t.jsx("div",{className:"flex flex-1 items-center",children:(0,t.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[t.jsx(n.W,{className:f,countryCode:Z.code?.cca2==="*"?"UN":Z.code?.cca2}),void 0!==g?g(Z):t.jsx("span",{children:Z.name})]})}):t.jsx("span",{className:(0,o.ZP)("text-placeholder",y),children:N("Select country")}),t.jsx(h.Z,{className:(0,o.ZP)("size-6",p)})]}),t.jsx(i.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:v,side:w,children:(0,t.jsxs)(d.mY,{children:[t.jsx(d.sZ,{placeholder:N("Search...")}),t.jsx(d.e8,{children:(0,t.jsxs)(d.fu,{children:[A&&t.jsx(c.Loader,{}),e&&(0,t.jsxs)(d.di,{value:N("All countries"),onSelect:()=>{J({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),m({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),P(!1)},children:[t.jsx(n.W,{countryCode:"UN"}),t.jsx("span",{className:"pl-1.5",children:N("All countries")})]}),C?.map(e=>"officially-assigned"===e.status?t.jsxs(d.di,{value:e.name,onSelect:()=>{J(e),m(e),P(!1)},children:[t.jsx(n.W,{countryCode:e.code.cca2}),t.jsxs("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},80609:(e,a,s)=>{s.d(a,{W:()=>n});var t=s(10326),l=s(77863),c=s(46226);function n({countryCode:e,className:a,url:s}){return e||s?t.jsx(c.default,{src:s??`https://flagcdn.com/${e?.toLowerCase()}.svg`,alt:e,width:20,height:16,loading:"lazy",className:(0,l.ZP)("rounded-[2px]",a)}):null}},1868:(e,a,s)=>{s.d(a,{F:()=>r});class t{constructor(e){this.name=e?.name?.common,this.flags=e?.flags,this.flag=e?.flag,this.code={cca2:e?.cca2,cca3:e?.cca3,ccn3:e?.ccn3},this.status=e?.status}}var l=s(44099),c=s(85999),n=s(84455);let d=l.default.create({baseURL:"https://restcountries.com/v3.1",headers:{"Content-Type":"application/json"}}),i="name,cca2,ccn3,cca3,status,flag,flags";function r(){let{data:e,isLoading:a,...s}=(0,n.ZP)(`/all?fields=${i}`,e=>d.get(e)),r=e?.data,o=async(e,a)=>{try{let s=await d.get(`/alpha/${e.toLowerCase()}?fields=${i}`),l=s.data?new t(s.data):null;a(l)}catch(e){l.default.isAxiosError(e)&&c.toast.error("Failed to fetch country")}};return{countries:r?r.map(e=>new t(e)):[],isLoading:a,getCountryByCode:o,...s}}},61610:(e,a,s)=>{s.d(a,{M:()=>t});class t{constructor(e){this.id=e?.id,this.logoImage=e?.logoImage,this.name=e?.name,this.value=e?.value,this.apiKey=e?.apiKey,this.secretKey=e?.secretKey,this.active=e?.active,this.activeApi=e?.activeApi,this.recommended=e?.recommended,this.variables=e?.variables,this.allowedCurrencies=e?.allowedCurrencies,this.allowedCountries=e?.allowedCountries,this.createdAt=e?.createdAt?new Date(e?.createdAt):null,this.updatedAt=e?.updatedAt?new Date(e?.updatedAt):null}}}};