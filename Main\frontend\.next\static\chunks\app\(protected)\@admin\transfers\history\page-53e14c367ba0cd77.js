(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[96493],{65274:function(e,a,s){Promise.resolve().then(s.bind(s,56999))},56999:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return h}});var n=s(57437),t=s(99376),r=s(2265),l=s(49657),c=s(85539),u=s(27186),i=s(85017),d=s(6512),o=s(75730),f=s(94508),m=s(43949);function h(){var e;let{t:a}=(0,m.$G)(),s=(0,t.useSearchParams)(),[h,x]=r.useState(null!==(e=s.get("search"))&&void 0!==e?e:""),v=(0,t.useRouter)(),p=(0,t.usePathname)(),{data:g,isLoading:j,meta:w,refresh:N}=(0,o.Z)("/admin/transfers?".concat(s.toString()));return(0,n.jsx)("div",{className:"p-4",children:(0,n.jsxs)("div",{className:"w-full rounded-xl bg-background p-4 shadow-default md:p-6",children:[(0,n.jsxs)("div",{className:"flex items-center sm:h-12",children:[(0,n.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,n.jsx)(c.R,{value:h,onChange:e=>{e.preventDefault();let a=(0,f.w4)(e.target.value);x(e.target.value),v.replace("".concat(p,"?").concat(a.toString()))},iconPlacement:"end",placeholder:a("Search..."),containerClass:"w-full sm:w-auto"}),(0,n.jsx)(i.k,{}),(0,n.jsx)(u._,{url:"/admin/transfers/export/all"})]}),(0,n.jsx)("div",{})]}),(0,n.jsx)(d.Z,{className:"my-4"}),(0,n.jsx)(l.Z,{data:g,meta:w,isLoading:j,refresh:N})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,27648,2901,42592,85210,58939,98604,27443,227,56993,85017,58804,92971,95030,1744],function(){return e(e.s=65274)}),_N_E=e.O()}]);