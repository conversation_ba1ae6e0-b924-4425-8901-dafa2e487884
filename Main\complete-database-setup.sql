-- PaySnap Complete Database Setup
-- This script combines the base schema with escrow system tables

-- First, ensure we have the base database structure
-- (This assumes the base tables are already created)

-- 1. ESCRO<PERSON> SYSTEM TABLES
CREATE TABLE escrows (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  escrow_id VARCHAR(50) UNIQUE NOT NULL, -- ESC-001 format
  sender_id int(10) UNSIGNED NOT NULL,
  recipient_id int(10) UNSIGNED NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
  status ENUM('pending', 'active', 'completed', 'cancelled', 'expired', 'disputed') DEFAULT 'pending',
  description TEXT,
  terms_conditions TEXT,
  deadline DATETIME NOT NULL,
  fee DECIMAL(15,2) NOT NULL DEFAULT 0,
  fee_payer ENUM('sender', 'recipient', 'split') DEFAULT 'sender',
  sender_confirmed B<PERSON>OLEAN DEFAULT FALSE,
  recipient_confirmed BOOLEAN DEFAULT FALSE,
  released_at DATETIME NULL,
  cancelled_at DATETIME NULL,
  cancellation_reason TEXT,
  has_milestones BOOLEAN DEFAULT FALSE,
  total_milestones INT DEFAULT 0,
  completed_milestones INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_escrow_status (status),
  INDEX idx_escrow_deadline (deadline),
  INDEX idx_escrow_sender (sender_id),
  INDEX idx_escrow_recipient (recipient_id)
);

-- 2. MILESTONE SYSTEM TABLES
CREATE TABLE milestones (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  escrow_id int(10) UNSIGNED NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  percentage DECIMAL(5,2) NOT NULL, -- 0.00 to 100.00
  amount DECIMAL(15,2) NOT NULL,
  order_index INT NOT NULL,
  status ENUM('pending', 'in_progress', 'completed', 'approved', 'rejected', 'released') DEFAULT 'pending',
  deliverables JSON, -- Array of deliverable descriptions
  due_date DATETIME NULL,
  completion_notes TEXT,
  rejection_reason TEXT,
  completed_at DATETIME NULL,
  approved_at DATETIME NULL,
  released_at DATETIME NULL,
  sender_approved BOOLEAN DEFAULT FALSE,
  recipient_approved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (escrow_id) REFERENCES escrows(id) ON DELETE CASCADE,
  INDEX idx_milestone_escrow (escrow_id),
  INDEX idx_milestone_status (status),
  INDEX idx_milestone_due_date (due_date)
);

-- 3. MILESTONE EVIDENCE TABLES
CREATE TABLE milestone_evidence (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  milestone_id int(10) UNSIGNED NOT NULL,
  type ENUM('document', 'image', 'video', 'link', 'other') NOT NULL,
  url VARCHAR(500) NOT NULL,
  filename VARCHAR(255),
  description TEXT,
  file_size BIGINT,
  mime_type VARCHAR(100),
  uploaded_by int(10) UNSIGNED NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (milestone_id) REFERENCES milestones(id) ON DELETE CASCADE,
  FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_evidence_milestone (milestone_id)
);

-- 4. RECURRING TRANSFER SYSTEM TABLES
CREATE TABLE recurring_transfers (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  recurring_id VARCHAR(50) UNIQUE NOT NULL, -- RT-001 format
  sender_id int(10) UNSIGNED NOT NULL,
  recipient_id int(10) UNSIGNED NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
  frequency ENUM('daily', 'weekly', 'monthly', 'yearly') NOT NULL,
  interval_value INT NOT NULL DEFAULT 1,
  status ENUM('active', 'paused', 'completed', 'cancelled', 'failed') DEFAULT 'active',
  description TEXT,
  start_date DATETIME NOT NULL,
  end_date DATETIME NULL,
  next_execution DATETIME NOT NULL,
  last_execution DATETIME NULL,
  max_occurrences INT NULL,
  executed_count INT DEFAULT 0,
  retry_count INT DEFAULT 0,
  last_error TEXT,
  schedule_config JSON, -- Day of week, month, time, timezone
  notify_sender BOOLEAN DEFAULT TRUE,
  notify_recipient BOOLEAN DEFAULT TRUE,
  notify_on_failure BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_recurring_status (status),
  INDEX idx_recurring_next_execution (next_execution),
  INDEX idx_recurring_sender (sender_id),
  INDEX idx_recurring_recipient (recipient_id)
);

-- 5. TRANSFER EXECUTION HISTORY
CREATE TABLE transfer_executions (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  recurring_transfer_id int(10) UNSIGNED NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
  executed_at DATETIME NOT NULL,
  error_message TEXT,
  transaction_id VARCHAR(100),
  retry_attempt INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (recurring_transfer_id) REFERENCES recurring_transfers(id) ON DELETE CASCADE,
  INDEX idx_execution_recurring (recurring_transfer_id),
  INDEX idx_execution_status (status),
  INDEX idx_execution_date (executed_at)
);

-- 6. FUNDRAISING POOL SYSTEM TABLES
CREATE TABLE fundraising_pools (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  pool_id VARCHAR(50) UNIQUE NOT NULL, -- POOL-001 format
  creator_id int(10) UNSIGNED NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  category ENUM('charity', 'education', 'health', 'environment', 'technology', 'arts', 'sports', 'community', 'business', 'personal', 'emergency', 'other'),
  tags JSON, -- Array of tag strings
  target_amount DECIMAL(15,2) NOT NULL,
  current_amount DECIMAL(15,2) DEFAULT 0,
  currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
  minimum_contribution DECIMAL(15,2) DEFAULT 0,
  maximum_contribution DECIMAL(15,2) NULL,
  status ENUM('draft', 'active', 'paused', 'completed', 'cancelled', 'expired') DEFAULT 'draft',
  visibility ENUM('public', 'private', 'invite_only') DEFAULT 'public',
  start_date DATETIME NOT NULL,
  end_date DATETIME NULL,
  has_deadline BOOLEAN DEFAULT FALSE,
  contributors_count INT DEFAULT 0,
  allow_anonymous BOOLEAN DEFAULT TRUE,
  auto_distribute BOOLEAN DEFAULT FALSE,
  distribution_type ENUM('immediate', 'on_completion', 'manual') DEFAULT 'on_completion',
  funds_distributed BOOLEAN DEFAULT FALSE,
  distributed_at DATETIME NULL,
  cover_image VARCHAR(500),
  gallery JSON, -- Array of image URLs
  video_url VARCHAR(500),
  allow_comments BOOLEAN DEFAULT TRUE,
  show_contributors BOOLEAN DEFAULT TRUE,
  send_updates BOOLEAN DEFAULT TRUE,
  platform_fee_percentage DECIMAL(5,2) DEFAULT 2.5,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_pool_status (status),
  INDEX idx_pool_category (category),
  INDEX idx_pool_creator (creator_id),
  INDEX idx_pool_end_date (end_date),
  FULLTEXT idx_pool_search (title, description)
);

-- 7. POOL CONTRIBUTIONS
CREATE TABLE pool_contributions (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  pool_id int(10) UNSIGNED NOT NULL,
  contributor_id int(10) UNSIGNED NULL, -- NULL for anonymous
  amount DECIMAL(15,2) NOT NULL,
  contributor_name VARCHAR(255),
  contributor_email VARCHAR(255),
  is_anonymous BOOLEAN DEFAULT FALSE,
  show_amount BOOLEAN DEFAULT TRUE,
  message TEXT,
  allow_contact BOOLEAN DEFAULT FALSE,
  social_links JSON, -- Twitter, LinkedIn, website
  payment_method VARCHAR(50),
  transaction_id VARCHAR(100),
  status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
  contributed_at DATETIME NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (pool_id) REFERENCES fundraising_pools(id) ON DELETE CASCADE,
  FOREIGN KEY (contributor_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_contribution_pool (pool_id),
  INDEX idx_contribution_contributor (contributor_id),
  INDEX idx_contribution_status (status)
);

-- 8. NOTIFICATION SYSTEM TABLES
CREATE TABLE escrow_notifications (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  user_id int(10) UNSIGNED NOT NULL,
  type ENUM('escrow', 'milestone', 'recurring_transfer', 'fundraising', 'admin') NOT NULL,
  subtype VARCHAR(50) NOT NULL, -- created, confirmed, released, etc.
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSON, -- Additional notification data
  channels JSON, -- Array of channels: email, push, sms, in_app
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
  status ENUM('pending', 'sent', 'delivered', 'failed', 'read') DEFAULT 'pending',
  read_at DATETIME NULL,
  sent_at DATETIME NULL,
  delivered_at DATETIME NULL,
  failed_reason TEXT,
  retry_count INT DEFAULT 0,
  related_id int(10) UNSIGNED, -- ID of related escrow/milestone/etc
  related_type VARCHAR(50), -- escrow, milestone, recurring_transfer, etc
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_notification_user (user_id),
  INDEX idx_notification_status (status),
  INDEX idx_notification_type (type),
  INDEX idx_notification_related (related_type, related_id)
);

-- 9. NOTIFICATION PREFERENCES
CREATE TABLE notification_preferences (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  user_id int(10) UNSIGNED NOT NULL,
  channel ENUM('email', 'push', 'sms', 'in_app') NOT NULL,
  type ENUM('escrow', 'milestone', 'recurring_transfer', 'fundraising', 'admin') NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  quiet_hours_enabled BOOLEAN DEFAULT FALSE,
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  timezone VARCHAR(50) DEFAULT 'UTC',
  frequency ENUM('immediate', 'digest_hourly', 'digest_daily') DEFAULT 'immediate',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_channel_type (user_id, channel, type),
  INDEX idx_preferences_user (user_id)
);

-- 10. ADMIN AUDIT TRAIL
CREATE TABLE admin_audit_trail (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  admin_id int(10) UNSIGNED NOT NULL,
  action VARCHAR(100) NOT NULL,
  table_name VARCHAR(50) NOT NULL,
  record_id int(10) UNSIGNED,
  old_values JSON,
  new_values JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_audit_admin (admin_id),
  INDEX idx_audit_action (action),
  INDEX idx_audit_table (table_name),
  INDEX idx_audit_created (created_at)
);

-- 11. SYSTEM HEALTH LOGS
CREATE TABLE system_health_logs (
  id int(10) UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  service_name VARCHAR(100) NOT NULL,
  status ENUM('healthy', 'warning', 'error', 'critical') NOT NULL,
  response_time_ms INT,
  error_message TEXT,
  additional_data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_health_service (service_name),
  INDEX idx_health_status (status),
  INDEX idx_health_created (created_at)
);

-- 12. ADDITIONAL INDEXES FOR PERFORMANCE
CREATE INDEX idx_escrows_composite ON escrows(status, sender_id, created_at);
CREATE INDEX idx_milestones_composite ON milestones(escrow_id, status, order_index);
CREATE INDEX idx_recurring_composite ON recurring_transfers(status, next_execution);
CREATE INDEX idx_pools_composite ON fundraising_pools(status, category, created_at);
CREATE INDEX idx_notifications_composite ON escrow_notifications(user_id, status, created_at);

-- Success message
SELECT 'PaySnap Escrow System tables created successfully!' as status; 