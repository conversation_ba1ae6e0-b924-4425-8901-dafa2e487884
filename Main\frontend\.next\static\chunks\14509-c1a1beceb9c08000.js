"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[14509],{41709:function(e,t,n){function a(e){let{condition:t,children:n}=e;return t?n:null}n.d(t,{J:function(){return a}}),n(2265)},52323:function(e,t,n){n.d(t,{g:function(){return m}});var a=n(57437),r=n(2265),s=n(85487),i=n(41062),o=n(23518),l=n(57054),d=n(40593),c=n(94508),u=n(36887),f=n(43949);function m(e){var t,n;let{allCountry:m=!1,defaultValue:p,defaultCountry:x,onSelectChange:h,disabled:v=!1,triggerClassName:g,arrowClassName:y,flagClassName:b,display:j,placeholderClassName:N,align:w="start",side:C="bottom"}=e,{t:Z}=(0,f.$G)(),{countries:_,getCountryByCode:P,isLoading:S}=(0,d.F)(),[k,z]=r.useState(!1),[R,F]=r.useState(p);return r.useEffect(()=>{p&&F(p)},[p]),r.useEffect(()=>{(async()=>{x&&await P(x,e=>{e&&(F(e),h(e))})})()},[x]),(0,a.jsxs)(l.J2,{open:k,onOpenChange:z,children:[(0,a.jsxs)(l.xo,{disabled:v,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",g),children:[R?(0,a.jsx)("div",{className:"flex flex-1 items-center",children:(0,a.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,a.jsx)(i.W,{className:b,countryCode:(null===(t=R.code)||void 0===t?void 0:t.cca2)==="*"?"UN":null===(n=R.code)||void 0===n?void 0:n.cca2}),void 0!==j?j(R):(0,a.jsx)("span",{children:R.name})]})}):(0,a.jsx)("span",{className:(0,c.ZP)("text-placeholder",N),children:Z("Select country")}),(0,a.jsx)(u.Z,{className:(0,c.ZP)("size-6",y)})]}),(0,a.jsx)(l.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:w,side:C,children:(0,a.jsxs)(o.mY,{children:[(0,a.jsx)(o.sZ,{placeholder:Z("Search...")}),(0,a.jsx)(o.e8,{children:(0,a.jsxs)(o.fu,{children:[S&&(0,a.jsx)(s.Loader,{}),m&&(0,a.jsxs)(o.di,{value:Z("All countries"),onSelect:()=>{F({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),h({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),z(!1)},children:[(0,a.jsx)(i.W,{countryCode:"UN"}),(0,a.jsx)("span",{className:"pl-1.5",children:Z("All countries")})]}),null==_?void 0:_.map(e=>"officially-assigned"===e.status?(0,a.jsxs)(o.di,{value:e.name,onSelect:()=>{F(e),h(e),z(!1)},children:[(0,a.jsx)(i.W,{countryCode:e.code.cca2}),(0,a.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},39785:function(e,t,n){n.d(t,{M:function(){return u}});var a=n(57437),r=n(1098),s=n(57054),i=n(94508),o=n(2901),l=n(76534),d=n(2265),c=n(43949);let u=d.forwardRef((e,t)=>{let{value:n,onChange:u,className:f,placeholderClassName:m,options:p}=e,{t:x}=(0,c.$G)(),[h,v]=d.useState(!1);return(0,a.jsxs)(s.J2,{open:h,onOpenChange:v,children:[(0,a.jsxs)(s.xo,{disabled:!!(null==p?void 0:p.disabled),className:(0,i.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3",f),children:[(0,a.jsx)("div",{ref:t,className:"flex flex-1 items-center",children:(0,a.jsx)("div",{className:"flex flex-1 items-center gap-2 text-left",children:n?(0,o.WU)(n,"dd/MM/yyyy"):(0,a.jsx)("span",{className:(0,i.ZP)("text-placeholder",m),children:x("Pick a Date")})})}),(0,a.jsx)(l.Z,{className:"ml-auto h-4 w-4 text-primary opacity-100"})]}),(0,a.jsx)(s.yk,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(r.f,{...p,mode:"single",initialFocus:!0,selected:null!=n?n:void 0,onSelect:e=>{u(e),v(!1)}})})]})})},78939:function(e,t,n){n.d(t,{S:function(){return l}});var a=n(57437),r=n(94508),s=n(33145),i=n(2265),o=n(85598);function l(e){let{defaultValue:t,onChange:n,className:l,children:d,disabled:c=!1,id:u}=e,[f,m]=i.useState(t);i.useEffect(()=>{m(t)},[t]);let{getRootProps:p,getInputProps:x}=(0,o.uI)({onDrop:e=>{let t=null==e?void 0:e[0];t&&(n(t),m(URL.createObjectURL(t)))},disabled:c});return(0,a.jsxs)("div",{...p({className:(0,r.ZP)("relative flex h-48 w-full rounded-md border border-input bg-secondary px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",l)}),children:[!!f&&(0,a.jsx)(s.default,{src:f,alt:"preview",width:400,height:400,className:"pointer-events-none absolute inset-0 left-0 top-0 h-full w-full object-contain"}),(0,a.jsx)("input",{id:u,...x()}),!f&&(0,a.jsx)("div",{children:d})]})}},18629:function(e,t,n){n.d(t,{E:function(){return Z}});var a=n(57437),r=n(85487),s=n(41062),i=n(23518),o=n(95186),l=n(57054),d=n(40593),c=n(94508),u=n(95550),f=n(36887),m=n(58414),p=n(78286),x=n(19368),h=n(68953),v=n(56555),g=n(5874),y=n(19615),b=n(93781),j=n(83057),N=n(43949),w=n(2265);let C={INVALID_COUNTRY:"The selected country is invalid.",NOT_A_NUMBER:"The input is not a valid phone number.",TOO_SHORT:"The phone number is too short.",TOO_LONG:"The phone number is too long.",INVALID_LENGTH:"The phone number length is invalid."};function Z(e){let{value:t,defaultValue:n="",onChange:r,onBlur:s,disabled:i,inputClassName:l,options:d}=e,[u,f]=(0,w.useState)(null!=n?n:""),[b,N]=(0,w.useState)(""),[Z,P]=(0,w.useState)(null==d?void 0:d.initialCountry),S=e=>{if(e)try{let t=m.S(e,Z);t?(P(t.country),N("+".concat(t.countryCallingCode)),f(t.formatNational())):f(e)}catch(t){f(e)}else f(e)};(0,w.useEffect)(()=>{t&&S(t)},[t]);let k=p.L(Z||(null==d?void 0:d.initialCountry)||"US",j.Z);return(0,a.jsxs)("div",{className:"flex items-center rounded-lg bg-input",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(_,{country:Z,disabled:i,initialCountry:null==d?void 0:d.initialCountry,onSelect:e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase(),a=x.G(n);N("+".concat(a)),P(n)}}),(0,a.jsx)("span",{className:"text-semibold inline-block pr-1.5 text-base empty:hidden",children:b||"+".concat(null==k?void 0:k.countryCallingCode)})]}),(0,a.jsx)(o.I,{type:"tel",className:(0,c.ZP)("rounded-l-none pl-2",l),value:u,onChange:e=>{let{value:t}=e.target,n=m.S(t,Z);null==s||s(""),n&&h.t(t,Z)&&v.q(t,Z)?(P(n.country),N("+".concat(n.countryCallingCode)),null==r||r(n.number),f(t)):(n?f(n.nationalNumber):f(t),null==r||r(t))},onPaste:e=>{let t=e.clipboardData.getData("Text"),n=m.S(t);if(n&&h.t(t))S(n.formatNational()),P(n.country),N("+".concat(n.countryCallingCode)),null==r||r(n.number),null==s||s("");else{let e=m.S(t,Z);e&&h.t(t,Z)&&(S(e.formatNational()),null==r||r(e.number),null==s||s(""))}},onBlur:()=>{if(u&&!g.y(u,Z)){let e=y.d(u,Z);e&&(null==s||s(C[e]))}},placeholder:null==k?void 0:k.formatNational(),disabled:i})]})}function _(e){let{initialCountry:t,country:n,onSelect:r,disabled:i}=e,[o,d]=(0,w.useState)(!1);return(0,a.jsxs)(l.J2,{open:o,onOpenChange:d,children:[(0,a.jsxs)(l.xo,{disabled:i,className:"flex h-12 w-[64px] items-center gap-1 border border-transparent pl-3 pr-1.5 hover:bg-muted",children:[t||n?(0,a.jsx)(s.W,{countryCode:n||t,className:"aspect-auto h-[18px] w-7 flex-1"}):(0,a.jsx)(u.Z,{}),(0,a.jsx)(f.Z,{variant:"Bold",size:16})]}),(0,a.jsx)(l.yk,{align:"start",className:"h-fit p-0",children:(0,a.jsx)(P,{defaultValue:n||t,onSelect:e=>{r(e),d(!1)}})})]})}function P(e){var t;let{defaultValue:n,onSelect:o}=e,{countries:l,isLoading:c}=(0,d.F)(),{t:u}=(0,N.$G)();return(0,a.jsxs)(i.mY,{children:[(0,a.jsx)(i.sZ,{placeholder:u("Search country by name"),className:"placeholder:text-input-placeholder"}),(0,a.jsx)(i.e8,{children:(0,a.jsx)(i.fu,{children:c?(0,a.jsx)(i.di,{children:(0,a.jsx)(r.Loader,{})}):null===(t=l.filter(e=>{var t;let n=null===(t=e.code.cca2)||void 0===t?void 0:t.toUpperCase();return b.o().includes(n)}))||void 0===t?void 0:t.map(e=>(0,a.jsxs)(i.di,{value:e.name,"data-active":e.code.cca2===n,className:"flex items-center gap-1.5 data-[active=true]:bg-input",onSelect:()=>o(e),children:[(0,a.jsx)(s.W,{countryCode:e.code.cca2}),e.name]},e.code.ccn3))})})]})}},25429:function(e,t,n){n.d(t,{X:function(){return s}});var a=n(57437),r=n(94508);function s(e){let{className:t}=e;return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",className:(0,r.ZP)("fill-primary",t),children:[(0,a.jsx)("path",{d:"M20 12.5052C20 10.204 21.8655 8.33854 24.1667 8.33854C26.4679 8.33854 28.3334 10.204 28.3334 12.5052C28.3334 14.8064 26.4679 16.6719 24.1667 16.6719C21.8655 16.6719 20 14.8064 20 12.5052Z"}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5938 1.67188C14.322 1.67186 12.5153 1.67185 11.0577 1.79095C9.56498 1.9129 8.2922 2.168 7.12678 2.76181C5.24516 3.72054 3.71536 5.25035 2.75662 7.13197C2.16282 8.29738 1.90771 9.57017 1.78576 11.0628C1.66666 12.5205 1.66667 14.3272 1.66669 16.5989V23.4115C1.66667 25.6832 1.66666 27.4899 1.78576 28.9476C1.90771 30.4403 2.16282 31.713 2.75662 32.8784C3.71536 34.7601 5.24516 36.2899 7.12678 37.2486C8.2922 37.8424 9.56498 38.0975 11.0577 38.2195C12.5153 38.3386 14.3219 38.3386 16.5936 38.3385H23.4063C25.678 38.3386 27.4848 38.3386 28.9424 38.2195C30.4351 38.0975 31.7078 37.8424 32.8733 37.2486C34.7549 36.2899 36.2847 34.7601 37.2434 32.8784C37.8372 31.713 38.0923 30.4403 38.2143 28.9476C38.3334 27.49 38.3334 25.6833 38.3334 23.4117V16.5989C38.3334 14.3272 38.3334 12.5204 38.2143 11.0628C38.0923 9.57017 37.8372 8.29738 37.2434 7.13197C36.2847 5.25035 34.7549 3.72054 32.8733 2.76181C31.7078 2.168 30.4351 1.9129 28.9424 1.79095C27.4847 1.67185 25.678 1.67186 23.4063 1.67188H16.5938ZM8.64008 5.73183C9.25727 5.41736 10.0426 5.21832 11.3291 5.11321C12.6351 5.00651 14.3056 5.00521 16.6667 5.00521H23.3334C25.6944 5.00521 27.365 5.00651 28.671 5.11321C29.9575 5.21832 30.7428 5.41736 31.36 5.73183C32.6144 6.37099 33.6342 7.39086 34.2734 8.64527C34.5879 9.26246 34.7869 10.0477 34.892 11.3343C34.9987 12.6403 35 14.3108 35 16.6719V23.3385C35 23.9359 34.9999 24.489 34.9981 25.0033L32.357 22.3622C31.0553 21.0605 28.9447 21.0605 27.643 22.3622L23.9226 26.0826C23.5972 26.4081 23.0695 26.4081 22.7441 26.0826L12.357 15.6956C11.0553 14.3938 8.94475 14.3938 7.643 15.6956L5.00002 18.3385V16.6719C5.00002 14.3108 5.00132 12.6403 5.10802 11.3343C5.21314 10.0477 5.41217 9.26246 5.72664 8.64527C6.3658 7.39086 7.38567 6.37099 8.64008 5.73183Z"})]})}},6596:function(e,t,n){n.d(t,{Qd:function(){return d},UQ:function(){return l},o4:function(){return c},vF:function(){return u}});var a=n(57437),r=n(13134),s=n(2265),i=n(94508),o=n(36887);let l=r.fC,d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.ck,{ref:t,className:(0,i.ZP)("border-b",n),...s})});d.displayName="AccordionItem";let c=s.forwardRef((e,t)=>{let{className:n,children:s,...l}=e;return(0,a.jsx)(r.h4,{className:"flex",children:(0,a.jsxs)(r.xz,{ref:t,className:(0,i.ZP)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",n),...l,children:[s,(0,a.jsx)(o.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=r.xz.displayName;let u=s.forwardRef((e,t)=>{let{className:n,children:s,...o}=e;return(0,a.jsx)(r.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...o,children:(0,a.jsx)("div",{className:(0,i.ZP)("pb-4 pt-0",n),children:s})})});u.displayName=r.VY.displayName},1098:function(e,t,n){n.d(t,{f:function(){return c}});var a=n(57437),r=n(92451),s=n(10407),i=n(40875);n(2265);var o=n(90827),l=n(62869),d=n(94508);function c(e){let{className:t,classNames:n,showOutsideDays:c=!0,...u}=e;return(0,a.jsx)(o._W,{showOutsideDays:c,className:(0,d.ZP)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium hidden",caption_dropdowns:"flex gap-1.5",dropdown:"text-sm w-fit appearance-none focus:outline-none",dropdown_icon:"hidden",dropdown_month:"[&>span]:hidden",dropdown_year:"[&>span]:hidden",nav:"space-x-1 flex items-center",nav_button:(0,d.ZP)((0,l.d)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,d.ZP)((0,l.d)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...n},captionLayout:"dropdown-buttons",fromYear:1950,toYear:2030,components:{IconLeft:e=>{let{...t}=e;return(0,a.jsx)(r.Z,{className:"h-4 w-4"})},IconRight:e=>{let{...t}=e;return(0,a.jsx)(s.Z,{className:"h-4 w-4"})},Dropdown:e=>{let{...t}=e;return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("select",{...t,style:{opacity:0,position:"absolute"}}),(0,a.jsxs)("div",{className:"pointer-events-none flex items-center gap-1",children:[(0,a.jsx)("span",{className:"text-sm",children:t.caption}),(0,a.jsx)(i.Z,{className:"size-3"})]})]})}},...u})}c.displayName="Calendar"},19060:function(e,t,n){n.d(t,{X:function(){return l}});var a=n(57437),r=n(9270),s=n(30401),i=n(2265),o=n(94508);let l=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,a.jsx)(r.fC,{ref:t,className:(0,o.ZP)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",n),...i,children:(0,a.jsx)(r.z$,{className:(0,o.ZP)("flex items-center justify-center text-current"),children:(0,a.jsx)(s.Z,{className:"h-4 w-4"})})})});l.displayName=r.fC.displayName},15681:function(e,t,n){n.d(t,{NI:function(){return h},Wi:function(){return u},l0:function(){return d},lX:function(){return x},xJ:function(){return p},zG:function(){return v}});var a=n(57437),r=n(37053),s=n(2265),i=n(29501),o=n(26815),l=n(94508);let d=i.RV,c=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(i.Qr,{...t})})},f=()=>{let e=s.useContext(c),t=s.useContext(m),{getFieldState:n,formState:a}=(0,i.Gc)(),r=n(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...r}},m=s.createContext({}),p=s.forwardRef((e,t)=>{let{className:n,...r}=e,i=s.useId();return(0,a.jsx)(m.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:t,className:(0,l.ZP)("space-y-2",n),...r})})});p.displayName="FormItem";let x=s.forwardRef((e,t)=>{let{className:n,required:r,...s}=e,{error:i,formItemId:d}=f();return(0,a.jsx)("span",{children:(0,a.jsx)(o.Z,{ref:t,className:(0,l.ZP)(i&&"text-base font-medium text-destructive",n),htmlFor:d,...s})})});x.displayName="FormLabel";let h=s.forwardRef((e,t)=>{let{...n}=e,{error:s,formItemId:i,formDescriptionId:o,formMessageId:l}=f();return(0,a.jsx)(r.g7,{ref:t,id:i,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...n})});h.displayName="FormControl",s.forwardRef((e,t)=>{let{className:n,...r}=e,{formDescriptionId:s}=f();return(0,a.jsx)("p",{ref:t,id:s,className:(0,l.ZP)("text-sm text-muted-foreground",n),...r})}).displayName="FormDescription";let v=s.forwardRef((e,t)=>{let{className:n,children:r,...s}=e,{error:i,formMessageId:o}=f(),d=i?String(null==i?void 0:i.message):r;return d?(0,a.jsx)("p",{ref:t,id:o,className:(0,l.ZP)("text-sm font-medium text-destructive",n),...s,children:d}):null});v.displayName="FormMessage"},57054:function(e,t,n){n.d(t,{J2:function(){return o},xo:function(){return l},yk:function(){return d}});var a=n(57437),r=n(2265),s=n(27312),i=n(94508);let o=s.fC,l=s.xz,d=r.forwardRef((e,t)=>{let{className:n,align:r="center",sideOffset:o=4,...l}=e;return(0,a.jsx)(s.h_,{children:(0,a.jsx)(s.VY,{ref:t,align:r,sideOffset:o,className:(0,i.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...l})})});d.displayName=s.VY.displayName},74991:function(e,t,n){n.d(t,{E:function(){return l},m:function(){return d}});var a=n(57437),r=n(2265),s=n(42325),i=n(40519),o=n(94508);let l=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.fC,{className:(0,o.ZP)("grid gap-2",n),...r,ref:t})});l.displayName=s.fC.displayName;let d=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(s.ck,{ref:t,className:(0,o.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),...r,children:(0,a.jsx)(s.z$,{className:"flex items-center justify-center",children:(0,a.jsx)(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=s.ck.displayName},6512:function(e,t,n){var a=n(57437),r=n(55156),s=n(2265),i=n(94508);let o=s.forwardRef((e,t)=>{let{className:n,orientation:s="horizontal",decorative:o=!0,...l}=e;return(0,a.jsx)(r.f,{ref:t,decorative:o,orientation:s,className:(0,i.ZP)("shrink-0 bg-divider","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",n),...l})});o.displayName=r.f.displayName,t.Z=o},1828:function(e,t,n){var a=n(57437),r=n(50721),s=n(2265),i=n(94508);let o=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.fC,{className:(0,i.ZP)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted",n),...s,ref:t,children:(0,a.jsx)(r.bU,{className:(0,i.ZP)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-[2px]")})})});o.displayName=r.fC.displayName,t.Z=o},81123:function(e,t,n){n.d(t,{H:function(){return s}});var a=n(79981),r=n(97751);async function s(e,t){try{let n=await a.Z.put("/admin/customers/update-address/".concat(t),{addressLine:e.street,zipCode:e.zipCode,countryCode:e.country,city:e.city});return(0,r.B)(n)}catch(e){return(0,r.D)(e)}}},17110:function(e,t,n){n.d(t,{n:function(){return i}});var a=n(79981),r=n(97751),s=n(2901);async function i(e,t){try{var n;let i=new FormData;i.append("firstName",e.firstName),i.append("lastName",e.lastName),i.append("email",e.email),i.append("phone",e.phone),i.append("gender",e.gender),i.append("dob",(0,s.WU)(e.dateOfBirth,"yyyy-MM-dd")),i.append("profileImage",null!==(n=e.profile)&&void 0!==n?n:"");let o=await a.Z.put("/admin/customers/update/".concat(t),i,{headers:{"Content-Type":"multipart/form-data"}});return(0,r.B)(o)}catch(e){return(0,r.D)(e)}}},4995:function(e,t,n){n.d(t,{y:function(){return s}});var a=n(79981),r=n(97751);async function s(e,t){try{let n=await a.Z.post("/admin/users/".concat(t,"-balance"),e);return(0,r.B)(n)}catch(e){return(0,r.D)(e)}}},97751:function(e,t,n){n.d(t,{B:function(){return r},D:function(){return s}});var a=n(43577);function r(e){var t,n,a;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(a=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==a?a:"",data:null===(n=e.data)||void 0===n?void 0:n.data}}function s(e){let t=500,n="Internal Server Error",r="An unknown error occurred";if((0,a.IZ)(e)){var s,i,o,l,d,c,u,f,m,p,x,h;t=null!==(m=null===(s=e.response)||void 0===s?void 0:s.status)&&void 0!==m?m:500,n=null!==(p=null===(i=e.response)||void 0===i?void 0:i.statusText)&&void 0!==p?p:"Internal Server Error",r=null!==(h=null!==(x=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(l=d.messages)||void 0===l?void 0:null===(o=l[0])||void 0===o?void 0:o.message)&&void 0!==x?x:null===(f=e.response)||void 0===f?void 0:null===(u=f.data)||void 0===u?void 0:u.message)&&void 0!==h?h:e.message}else e instanceof Error&&(r=e.message);return{statusCode:t,statusText:n,status:!1,message:r,data:void 0,error:e}}},54995:function(e,t,n){n.d(t,{K:function(){return i},S:function(){return o}});var a=n(31229);let r=["image/jpeg","image/jpg","image/png","image/svg+xml"],s=["image/x-icon","image/vnd.microsoft.icon","image/png"],i=a.z.union([a.z.string(),a.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&r.includes(e.type),"Invalid file format. Please upload a .jpg, .jpeg, .png or .svg file."),o=a.z.union([a.z.string(),a.z.instanceof(File)]).optional().refine(e=>!e||"string"==typeof e||e instanceof File&&e.size<=5242880,"File size must be less than 5MB").refine(e=>!e||"string"==typeof e||e instanceof File&&s.includes(e.type),"Invalid file format. Please upload a .ico or .png file.")}}]);