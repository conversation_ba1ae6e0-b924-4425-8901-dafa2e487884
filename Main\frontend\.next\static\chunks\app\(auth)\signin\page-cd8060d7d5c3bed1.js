(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[60161],{80253:function(e,t,i){Promise.resolve().then(i.bind(i,76083))},76083:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return C}});var s=i(57437),n=i(15054),r=i(41709),a=i(56353),o=i(3882),l=i(62869),d=i(15681),u=i(95186),c=i(6512),h=i(31092),p=i(21251),v=i(31229);let f=v.z.object({email:v.z.string({required_error:"Email address is required."}).email({message:"Invalid email address."}),password:v.z.string({required_error:"Password is required."}).min(1,"Password is required.")});var m=i(13590),g=i(10407),x=i(27648),w=i(99376),y=i(2265),b=i(29501),j=i(43949),N=i(14438);function C(){let[e,t]=(0,y.useTransition)(),i=(0,w.useRouter)(),{t:v}=(0,j.$G)(),{siteName:C,customerRegistration:k,merchantRegistration:A,agentRegistration:I}=(0,p.T)(),[z,P]=(0,y.useState)(""),L=[{type:"customer",enabled:k},{type:"merchant",enabled:A},{type:"agent",enabled:I}].filter(e=>e.enabled).length,E=(0,b.cI)({resolver:(0,m.F)(f),defaultValues:{email:"",password:""}}),S=e=>{if(e.statusCode>=422){let e=v("The credentials you entered are incorrect. Please check your email and password, and try again.");N.toast.error(v("Incorrect Credentials")),P(e)}else if(e.statusCode>=400&&e.statusCode<500){let t=v(e.message);N.toast.error(v(e.message)||v("Incorrect Credentials")),P(t)}else if(e.statusCode>=500){let t=v(e.message)||v("An unexpected error occurred. Please try again later.");N.toast.error(t),P(t)}};return(0,s.jsxs)("div",{className:"container mt-10 flex max-w-2xl flex-1 flex-col py-6",children:[(0,s.jsx)(n.h,{title:v("Sign in"),subTitle:v("Welcome to {{siteName}}",{siteName:C})}),(0,s.jsx)("div",{className:"my-6 flex h-[5px] items-center",children:(0,s.jsx)(c.Z,{className:"bg-divider"})}),(0,s.jsx)(d.l0,{...E,children:(0,s.jsxs)("form",{onSubmit:E.handleSubmit(e=>{t(async()=>{let t=await (0,h.z9)(e);t&&(null==t?void 0:t.redirectURL)&&i.push(t.redirectURL),(null==t?void 0:t.token)?i.push("/signin/2fa?token=".concat(t.token)):S(t)})}),className:"flex flex-col gap-6",children:[(0,s.jsx)(d.Wi,{control:E.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsxs)(d.xJ,{children:[(0,s.jsx)(d.lX,{required:!0,children:v("Email")}),(0,s.jsx)(d.NI,{children:(0,s.jsx)(u.I,{type:"email",className:"placeholder:text-placeholder",placeholder:v("Enter your email address"),...t})}),(0,s.jsx)(d.zG,{})]})}}),(0,s.jsx)(d.Wi,{control:E.control,name:"password",render:e=>{let{field:t}=e;return(0,s.jsxs)(d.xJ,{children:[(0,s.jsx)(d.lX,{required:!0,children:v("Password")}),(0,s.jsx)(d.NI,{children:(0,s.jsx)(a.W,{placeholder:v("Enter your password"),...t})}),(0,s.jsx)(d.zG,{})]})}}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(x.default,{href:"/forgot-password",prefetch:!1,className:"text-primary hover:underline",children:v("Forgot password?")})}),""===z?null:(0,s.jsx)("div",{className:"rounded-lg bg-destructive/10 px-4 py-3",children:(0,s.jsx)("p",{className:"text-destructive",children:z})}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(l.z,{className:"sm:w-52",disabled:e,children:[(0,s.jsxs)(r.J,{condition:!e,children:[v("Sign in"),(0,s.jsx)(g.Z,{size:19})]}),(0,s.jsxs)(r.J,{condition:e,children:[(0,s.jsx)(o.Z,{}),v("Processing...")]})]})})]})}),(0,s.jsx)("div",{className:"block md:hidden",children:(0,s.jsx)(r.J,{condition:L>0,children:(0,s.jsxs)("div",{className:"mt-6 flex w-full items-center justify-center gap-1 py-6 sm:hidden",children:[(0,s.jsx)("span",{children:v("Have an account?")}),(0,s.jsx)(l.z,{type:"button",variant:"link",className:"m-0 p-0 text-primary hover:underline",children:(0,s.jsx)(x.default,{href:"/register",prefetch:!1,children:v("Sign up")})})]})})})]})}},80114:function(e,t,i){"use strict";i.d(t,{default:function(){return o}});var s=i(57437),n=i(85487),r=i(94508),a=i(43949);function o(e){let{className:t}=e,{t:i}=(0,a.$G)();return(0,s.jsx)("div",{className:(0,r.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,s.jsx)(n.Loader,{title:i("Loading..."),className:"text-foreground"})})}},85487:function(e,t,i){"use strict";i.d(t,{Loader:function(){return a}});var s=i(57437),n=i(94508),r=i(43949);function a(e){let{title:t="Loading...",className:i}=e,{t:a}=(0,r.$G)();return(0,s.jsxs)("div",{className:(0,n.ZP)("flex items-center gap-1 text-sm text-foreground",i),children:[(0,s.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{className:"text-inherit",children:a(t)})]})}},56353:function(e,t,i){"use strict";i.d(t,{W:function(){return u}});var s=i(57437),n=i(2265),r=i(62869),a=i(95186),o=i(94508),l=i(93824),d=i(32706);let u=n.forwardRef((e,t)=>{let{className:i,type:u,...c}=e,[h,p]=n.useState(!1);return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(a.I,{type:h?"text":"password",className:(0,o.ZP)("placeholder:text-placeholder flex h-12 w-full rounded-[8px] border-none border-input bg-accent px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:t,...c}),(0,s.jsx)(r.z,{"aria-label":"PasswordVisibilityToggler",variant:"link",size:"icon",type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2",onClick:e=>{e.stopPropagation(),p(e=>!e)},children:h?(0,s.jsx)(l.Z,{}):(0,s.jsx)(d.Z,{})})]})});u.displayName="PasswordInput"},3882:function(e,t,i){"use strict";i.d(t,{Z:function(){return n}});var s=i(57437);function n(){return(0,s.jsxs)("svg",{className:"-ml-1 mr-3 h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}},95186:function(e,t,i){"use strict";i.d(t,{I:function(){return a}});var s=i(57437),n=i(2265),r=i(94508);let a=n.forwardRef((e,t)=>{let{className:i,type:n,...a}=e;return(0,s.jsx)("input",{type:n,className:(0,r.ZP)("flex h-12 w-full rounded-[8px] border-none border-input bg-input px-3 py-2 text-base font-normal ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:font-normal placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input-disabled",i),ref:t,...a})});a.displayName="Input"},17062:function(e,t,i){"use strict";i.d(t,{Z:function(){return f},O:function(){return v}});var s=i(57437),n=i(80114);i(83079);var r=(0,i(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=i(31117),o=i(79981),l=i(78040),d=i(83130);class u{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var c=i(99376),h=i(2265);let p=h.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),v=()=>h.useContext(p);function f(e){let{children:t}=e,[i,v]=h.useState("Desktop"),[f,m]=h.useState(!1),[g,x]=h.useState(),{data:w,isLoading:y,error:b,mutate:j}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:N,isLoading:C}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:k,isLoading:A}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),I=(0,c.useRouter)(),z=(0,c.usePathname)();h.useEffect(()=>{(async()=>{v((await r()).deviceType)})()},[]),h.useEffect(()=>{let e=()=>{let e=window.innerWidth;v(e<768?"Mobile":e<1024?"Tablet":"Desktop"),m(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),h.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await o.Z.post("/auth/geo-location");x(new u(e))}catch(e){}})()},[]),h.useLayoutEffect(()=>{b&&!l.sp.includes(z)&&I.push("/signin")},[b]);let P=h.useMemo(()=>{var e,t,s;return{isAuthenticate:!!(null==w?void 0:null===(e=w.data)||void 0===e?void 0:e.login),auth:(null==w?void 0:null===(t=w.data)||void 0===t?void 0:t.user)?new d.n(null==w?void 0:null===(s=w.data)||void 0===s?void 0:s.user):null,isLoading:y,deviceLocation:g,refreshAuth:()=>j(w),isExpanded:f,device:i,setIsExpanded:m,branding:null==N?void 0:N.data,googleAnalytics:(null==k?void 0:k.data)?{active:null==k?void 0:k.data.active,apiKey:null==k?void 0:k.data.apiKey}:{active:!1,apiKey:""}}},[w,g,f,i]),L=!y&&!C&&!A;return(0,s.jsx)(p.Provider,{value:P,children:L?t:(0,s.jsx)(n.default,{})})}},21251:function(e,t,i){"use strict";i.d(t,{T:function(){return n}});var s=i(17062);let n=()=>{let{branding:e}=(0,s.O)();return e}},31117:function(e,t,i){"use strict";i.d(t,{d:function(){return r}});var s=i(79981),n=i(85323);let r=(e,t)=>(0,n.ZP)(e||null,e=>s.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},74539:function(e,t,i){"use strict";i.d(t,{k:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,i){"use strict";i.d(t,{n:function(){return l}});class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var n=i(84937);class r{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=i(66419),o=i(78040);class l{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(o.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new n.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new r(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new s(e.agent):void 0}}},84937:function(e,t,i){"use strict";i.d(t,{O:function(){return n}});var s=i(74539);class n{constructor(e){var t,i;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(i=e.phone)||void 0===i?void 0:i.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new s.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},66419:function(e,t,i){"use strict";i.d(t,{u:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,27648,38658,82197,70961,92971,95030,1744],function(){return e(e.s=80253)}),_N_E=e.O()}]);