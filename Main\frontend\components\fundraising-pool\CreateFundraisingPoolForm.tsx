"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Label from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CreatePoolData, PoolCategory, PoolVisibility, DistributionType } from "@/types/fundraising-pool";
import { SelectCurrency } from "@/components/common/form/SelectCurrency";
import { DatePicker } from "@/components/common/form/DatePicker";
import { Target, Calendar, Settings, Eye, Upload, X, AlertTriangle } from "lucide-react";

const createPoolSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters").max(100, "Title cannot exceed 100 characters"),
  description: z.string().min(20, "Description must be at least 20 characters").max(5000, "Description cannot exceed 5000 characters"),
  category: z.enum(['charity', 'education', 'health', 'environment', 'technology', 'arts', 'sports', 'community', 'business', 'personal', 'emergency', 'other']).optional(),
  targetAmount: z.number().min(1, "Target amount must be greater than 0").max(1000000, "Target amount cannot exceed 1,000,000"),
  currencyCode: z.string().min(3, "Please select a currency"),
  minimumContribution: z.number().min(0, "Minimum contribution cannot be negative").optional(),
  maximumContribution: z.number().min(1, "Maximum contribution must be greater than 0").optional(),
  hasDeadline: z.boolean(),
  endDate: z.string().optional(),
  visibility: z.enum(['public', 'private', 'invite_only']),
  allowAnonymous: z.boolean(),
  autoDistribute: z.boolean(),
  distributionType: z.enum(['immediate', 'on_completion', 'manual']),
  allowComments: z.boolean(),
  showContributors: z.boolean(),
  sendUpdates: z.boolean(),
});

type CreatePoolFormData = z.infer<typeof createPoolSchema>;

interface CreateFundraisingPoolFormProps {
  onSubmit: (data: CreatePoolData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

export function CreateFundraisingPoolForm({ 
  onSubmit, 
  isLoading = false, 
  error 
}: CreateFundraisingPoolFormProps) {
  const { t } = useTranslation();
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreatePoolFormData>({
    resolver: zodResolver(createPoolSchema),
    defaultValues: {
      visibility: 'public',
      allowAnonymous: true,
      autoDistribute: false,
      distributionType: 'on_completion',
      allowComments: true,
      showContributors: true,
      sendUpdates: true,
      hasDeadline: false,
    },
  });

  const watchedCurrency = watch('currencyCode');
  const watchedHasDeadline = watch('hasDeadline');
  const watchedVisibility = watch('visibility');

  const categoryOptions: { value: PoolCategory; label: string }[] = [
    { value: 'charity', label: 'Charity' },
    { value: 'education', label: 'Education' },
    { value: 'health', label: 'Health' },
    { value: 'environment', label: 'Environment' },
    { value: 'technology', label: 'Technology' },
    { value: 'arts', label: 'Arts' },
    { value: 'sports', label: 'Sports' },
    { value: 'community', label: 'Community' },
    { value: 'business', label: 'Business' },
    { value: 'personal', label: 'Personal' },
    { value: 'emergency', label: 'Emergency' },
    { value: 'other', label: 'Other' },
  ];

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim()) && tags.length < 10) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const onFormSubmit = async (data: CreatePoolFormData) => {
    const submitData: CreatePoolData = {
      ...data,
      tags,
      endDate: watchedHasDeadline ? data.endDate : undefined,
    };

    await onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>{t('Pool Information')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">{t('Title')} *</Label>
            <Input
              id="title"
              placeholder={t('Enter a compelling title for your fundraising pool...')}
              {...register('title')}
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">{t('Description')} *</Label>
            <Textarea
              id="description"
              placeholder={t('Describe your fundraising goal, what the funds will be used for, and why people should contribute...')}
              rows={5}
              {...register('description')}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">{t('Category')}</Label>
            <Select
              value={watch('category') || ''}
              onValueChange={(value) => setValue('category', value as PoolCategory)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('Select a category')} />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {t(option.label)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.category && (
              <p className="text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>{t('Tags')}</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                  <span>{tag}</span>
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex space-x-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={t('Add tags to help people find your pool...')}
                className="flex-1"
              />
              <Button
                type="button"
                onClick={addTag}
                disabled={!newTag.trim() || tags.length >= 10}
                variant="outline"
              >
                {t('Add')}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              {t('{{count}}/10 tags added', { count: tags.length })}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Financial Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>{t('Financial Settings')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Target Amount and Currency */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="targetAmount">{t('Target Amount')} *</Label>
              <Input
                id="targetAmount"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...register('targetAmount', { valueAsNumber: true })}
              />
              {errors.targetAmount && (
                <p className="text-sm text-red-600">{errors.targetAmount.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">{t('Currency')} *</Label>
              <SelectCurrency
                value={watchedCurrency}
                onChange={(value) => setValue('currencyCode', value)}
              />
              {errors.currencyCode && (
                <p className="text-sm text-red-600">{errors.currencyCode.message}</p>
              )}
            </div>
          </div>

          {/* Contribution Limits */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minimumContribution">{t('Minimum Contribution')}</Label>
              <Input
                id="minimumContribution"
                type="number"
                step="0.01"
                placeholder="0.00"
                {...register('minimumContribution', { valueAsNumber: true })}
              />
              {errors.minimumContribution && (
                <p className="text-sm text-red-600">{errors.minimumContribution.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="maximumContribution">{t('Maximum Contribution')}</Label>
              <Input
                id="maximumContribution"
                type="number"
                step="0.01"
                placeholder={t('No limit')}
                {...register('maximumContribution', { valueAsNumber: true })}
              />
              {errors.maximumContribution && (
                <p className="text-sm text-red-600">{errors.maximumContribution.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>{t('Timeline Settings')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Has Deadline */}
          <div className="flex items-center justify-between">
            <Label htmlFor="hasDeadline">{t('Set End Date')}</Label>
            <Switch
              id="hasDeadline"
              checked={watchedHasDeadline}
              onCheckedChange={(checked) => setValue('hasDeadline', checked)}
            />
          </div>

          {/* End Date */}
          {watchedHasDeadline && (
            <div className="space-y-2">
              <Label>{t('End Date')} *</Label>
              <DatePicker
                date={watch('endDate') ? new Date(watch('endDate')) : undefined}
                onDateChange={(date) => 
                  setValue('endDate', date?.toISOString().split('T')[0] || '')
                }
                placeholder={t('Select end date')}
              />
              {errors.endDate && (
                <p className="text-sm text-red-600">{errors.endDate.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Privacy & Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>{t('Privacy & Settings')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Visibility */}
          <div className="space-y-2">
            <Label htmlFor="visibility">{t('Visibility')} *</Label>
            <Select
              value={watchedVisibility}
              onValueChange={(value) => setValue('visibility', value as PoolVisibility)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="public">{t('Public - Anyone can find and contribute')}</SelectItem>
                <SelectItem value="private">{t('Private - Only you can see it')}</SelectItem>
                <SelectItem value="invite_only">{t('Invite Only - Only invited people can contribute')}</SelectItem>
              </SelectContent>
            </Select>
            {errors.visibility && (
              <p className="text-sm text-red-600">{errors.visibility.message}</p>
            )}
          </div>

          {/* Settings Toggles */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="allowAnonymous">{t('Allow anonymous contributions')}</Label>
              <Switch
                id="allowAnonymous"
                checked={watch('allowAnonymous')}
                onCheckedChange={(checked) => setValue('allowAnonymous', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="showContributors">{t('Show contributor list')}</Label>
              <Switch
                id="showContributors"
                checked={watch('showContributors')}
                onCheckedChange={(checked) => setValue('showContributors', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="allowComments">{t('Allow comments')}</Label>
              <Switch
                id="allowComments"
                checked={watch('allowComments')}
                onCheckedChange={(checked) => setValue('allowComments', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="sendUpdates">{t('Send updates to contributors')}</Label>
              <Switch
                id="sendUpdates"
                checked={watch('sendUpdates')}
                onCheckedChange={(checked) => setValue('sendUpdates', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="autoDistribute">{t('Auto-distribute funds when target is reached')}</Label>
              <Switch
                id="autoDistribute"
                checked={watch('autoDistribute')}
                onCheckedChange={(checked) => setValue('autoDistribute', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline">
          {t('Save as Draft')}
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? t('Creating...') : t('Create Pool')}
        </Button>
      </div>
    </form>
  );
}
