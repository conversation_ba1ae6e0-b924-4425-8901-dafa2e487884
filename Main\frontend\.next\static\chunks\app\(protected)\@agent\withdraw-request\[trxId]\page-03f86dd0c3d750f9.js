(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72978],{6114:function(e,s,a){Promise.resolve().then(a.bind(a,61308))},61308:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return w},runtime:function(){return y}});var t=a(57437),l=a(85487),n=a(87806),i=a(62869),d=a(6512),r=a(27844),c=a(62092),o=a(31117),m=a(94508),x=a(48408),u=a(2901),v=a(77926),p=a(50934),g=a(43271),h=a(19571),f=a(60827),b=a(99376),j=a(43949),N=a(14438);let y="edge";function w(){var e,s,a,y,w,k,D,Z,C,z,E,I;let{t:q}=(0,j.$G)(),A=(0,b.useParams)(),{data:M,isLoading:_,mutate:W}=(0,o.d)("/transactions/trx/".concat(A.trxId));if(_)return(0,t.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,t.jsx)(l.Loader,{})});let B=e=>{N.toast.promise((0,r.l)(e),{loading:q("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return W(M),e.message},error:e=>e.message})},L=e=>{N.toast.promise((0,c.t)(e),{loading:q("Loading..."),success:e=>{if(!e.status)throw Error(e.message);return W(M),e.message},error:e=>e.message})},R=(null==M?void 0:M.data)?new x.C(null==M?void 0:M.data):null,V=new m.F;return R?(0,t.jsx)("div",{className:"mb-10 p-2 sm:mb-0 sm:p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[(0,t.jsx)("div",{className:"col-span-12 lg:col-span-7",children:(0,t.jsxs)("div",{className:"flex flex-col gap-4 rounded-xl bg-card px-2 py-8 sm:py-14",children:[(0,t.jsxs)("div",{className:"inline-flex items-center justify-center gap-2.5",children:[(0,t.jsx)(p.Z,{variant:"Bulk",size:32,className:"text-primary"}),(0,t.jsxs)("h2",{className:"font-semibold",children:[q("Withdraw"),"#",A.withdrawId]})]}),(0,t.jsx)(n.z,{senderAvatar:(0,m.qR)(R.from.image),senderName:R.from.label,senderInfo:[null===(e=R.from)||void 0===e?void 0:e.email,null==R?void 0:null===(s=R.from)||void 0===s?void 0:s.phone],receiverAvatar:(0,m.qR)(null==R?void 0:null===(a=R.to)||void 0===a?void 0:a.image),receiverName:null==R?void 0:null===(y=R.to)||void 0===y?void 0:y.label,receiverInfo:[null==R?void 0:null===(w=R.to)||void 0===w?void 0:w.email,null==R?void 0:null===(k=R.to)||void 0===k?void 0:k.phone],className:"px-3 sm:gap-4 sm:px-8"}),(0,t.jsx)(d.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("Date")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:(null==R?void 0:R.createdAt)?(0,u.WU)(R.createdAt,"dd MMM yyyy; hh:mm a"):""})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("Amount")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:V.formatVC(R.amount,R.metaData.currency)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("Service charge")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:V.formatVC(R.fee,R.metaData.currency)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-4 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("User gets")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-semibold sm:text-base",children:V.formatVC(R.total,R.metaData.currency)})]})]}),(0,t.jsx)(d.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,t.jsx)("div",{className:"flex flex-col",children:(0,t.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("Transaction ID")}),(0,t.jsxs)("div",{className:"col-span-6 flex items-center gap-2 text-sm font-medium sm:text-base",children:[R.trxId,(0,t.jsx)(i.z,{type:"button",onClick:()=>(0,m.Fp)(R.trxId),variant:"outline",size:"sm",className:"bg-background hover:bg-background",children:(0,t.jsx)(g.Z,{size:"20"})})]})]})}),(0,t.jsx)(d.Z,{className:"mb-1 mt-[5px] bg-border"}),"pending"===R.status?(0,t.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,t.jsxs)(i.z,{type:"button",onClick:()=>B(null==R?void 0:R.id),className:"gap-1 rounded-lg bg-spacial-green px-4 py-2 font-medium text-background hover:bg-[#219621] hover:text-background",children:[(0,t.jsx)(h.Z,{}),q("Approve")]}),(0,t.jsxs)(i.z,{type:"button",onClick:()=>L(null==R?void 0:R.id),className:"gap-1 rounded-lg bg-[#D13438] px-4 py-2 font-medium text-white hover:bg-[#a5272b] hover:text-white",children:[(0,t.jsx)(f.Z,{}),q("Reject")]})]}):null]})}),(0,t.jsx)("div",{className:"col-span-12 lg:col-span-5",children:(0,t.jsxs)("div",{className:"mb-4 flex flex-col gap-8 rounded-xl bg-card px-2 py-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsx)("h2",{children:q("Method info")})}),(0,t.jsx)(d.Z,{className:"mb-1 mt-[5px] bg-border"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("Method used")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:null!==(z=null==R?void 0:null===(D=R.metaData)||void 0===D?void 0:D.agentMethod)&&void 0!==z?z:"--"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("Number")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:null!==(E=null==R?void 0:null===(Z=R.metaData)||void 0===Z?void 0:Z.value)&&void 0!==E?E:"--"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 px-6 py-3 odd:bg-accent",children:[(0,t.jsx)("div",{className:"col-span-6 text-sm font-normal sm:text-base",children:q("Wallet")}),(0,t.jsx)("div",{className:"col-span-6 text-sm font-medium sm:text-base",children:null!==(I=null==R?void 0:null===(C=R.metaData)||void 0===C?void 0:C.currency)&&void 0!==I?I:"undefine"})]})]})]})})]})}):(0,t.jsxs)("div",{className:"flex items-center justify-center gap-4 py-10",children:[(0,t.jsx)(v.Z,{}),q("No data found")]})}},27844:function(e,s,a){"use strict";a.d(s,{l:function(){return n}});var t=a(79981),l=a(97751);async function n(e){try{if(!e)throw Error("Withdraw id is required");let s=await t.Z.put("/withdraw-requests/accept/".concat(e),{id:e});return(0,l.B)(s)}catch(e){return(0,l.D)(e)}}},62092:function(e,s,a){"use strict";a.d(s,{t:function(){return n}});var t=a(79981),l=a(97751);async function n(e){try{if(!e)throw Error("Withdraw id is required");let s=await t.Z.put("/withdraw-requests/decline/".concat(e),{id:e});return(0,l.B)(s)}catch(e){return(0,l.D)(e)}}}},function(e){e.O(0,[14438,31304,83464,2602,85323,2901,33157,28622,83113,92971,95030,1744],function(){return e(e.s=6114)}),_N_E=e.O()}]);