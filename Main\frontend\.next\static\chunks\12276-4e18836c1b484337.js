"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[12276],{63285:function(e,t,r){var n=r(84851),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function a(e){return n.isMemo(e)?s:l[e.$$typeof]||i}l[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[n.Memo]=s;var u=Object.defineProperty,c=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(p){var i=h(r);i&&i!==p&&e(t,i,n)}var s=c(r);f&&(s=s.concat(f(r)));for(var l=a(t),m=a(r),y=0;y<s.length;++y){var g=s[y];if(!o[g]&&!(n&&n[g])&&!(m&&m[g])&&!(l&&l[g])){var v=d(r,g);try{u(t,g,v)}catch(e){}}}}return t}},36887:function(e,t,r){r.d(t,{Z:function(){return m}});var n=r(74677),i=r(2265),o=r(40718),s=r.n(o),l=["variant","color","size"],a=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M17.919 8.18H6.079c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},u=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M16.01 12.85l-2.62 2.62c-.77.77-2.03.77-2.8 0L4.08 8.95M19.92 8.95l-1.04 1.04"}))},c=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M15.48 13.23l-3.79-5.05H6.08c-.96 0-1.44 1.16-.76 1.84l5.18 5.18c.83.83 2.18.83 3.01 0l1.97-1.97z",opacity:".4"}),i.createElement("path",{fill:t,d:"M17.92 8.18h-6.23l3.79 5.05 3.21-3.21c.67-.68.19-1.84-.77-1.84z"}))},f=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},d=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M12 16.8c-.7 0-1.4-.27-1.93-.8L3.55 9.48a.754.754 0 010-1.06c.29-.29.77-.29 1.06 0l6.52 6.52c.48.48 1.26.48 1.74 0l6.52-6.52c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06L13.93 16c-.53.53-1.23.8-1.93.8z"}))},h=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M19.92 8.95l-6.52 6.52c-.77.77-2.03.77-2.8 0L4.08 8.95"}))},p=function(e,t){switch(e){case"Bold":return i.createElement(a,{color:t});case"Broken":return i.createElement(u,{color:t});case"Bulk":return i.createElement(c,{color:t});case"Linear":default:return i.createElement(f,{color:t});case"Outline":return i.createElement(d,{color:t});case"TwoTone":return i.createElement(h,{color:t})}},m=(0,i.forwardRef)(function(e,t){var r=e.variant,o=e.color,s=e.size,a=(0,n._)(e,l);return i.createElement("svg",(0,n.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(r,o))});m.propTypes={variant:s().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:s().string,size:s().oneOfType([s().string,s().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="ArrowDown2"},90433:function(e,t,r){r.d(t,{Z:function(){return m}});var n=r(74677),i=r(2265),o=r(40718),s=r.n(o),l=["variant","color","size"],a=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M13.978 5.319l-3.21 3.21-1.97 1.96a2.13 2.13 0 000 3.01l5.18 5.18c.68.68 1.84.19 1.84-.76V6.079c0-.96-1.16-1.44-1.84-.76z"}))},u=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M11.19 7.94l-2.62 2.62c-.77.77-.77 2.03 0 2.8l6.52 6.52M15.09 4.04l-1.04 1.04"}))},c=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M10.77 8.52l5.05 3.79v5.61c0 .96-1.16 1.44-1.84.76L8.8 13.51a2.13 2.13 0 010-3.01l1.97-1.98z",opacity:".4"}),i.createElement("path",{fill:t,d:"M15.82 6.08v6.23l-5.05-3.79 3.21-3.21c.68-.67 1.84-.19 1.84.77z"}))},f=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M15 19.92L8.48 13.4c-.77-.77-.77-2.03 0-2.8L15 4.08"}))},d=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{fill:t,d:"M15 20.67c-.19 0-.38-.07-.53-.22l-6.52-6.52a2.74 2.74 0 010-3.86l6.52-6.52c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-6.52 6.52c-.48.48-.48 1.26 0 1.74l6.52 6.52c.29.29.29.77 0 1.06-.15.14-.34.22-.53.22z"}))},h=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",strokeWidth:"1.5",d:"M15 19.92L8.48 13.4c-.77-.77-.77-2.03 0-2.8L15 4.08"}))},p=function(e,t){switch(e){case"Bold":return i.createElement(a,{color:t});case"Broken":return i.createElement(u,{color:t});case"Bulk":return i.createElement(c,{color:t});case"Linear":default:return i.createElement(f,{color:t});case"Outline":return i.createElement(d,{color:t});case"TwoTone":return i.createElement(h,{color:t})}},m=(0,i.forwardRef)(function(e,t){var r=e.variant,o=e.color,s=e.size,a=(0,n._)(e,l);return i.createElement("svg",(0,n.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(r,o))});m.propTypes={variant:s().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:s().string,size:s().oneOfType([s().string,s().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="ArrowLeft2"},80093:function(e,t,r){r.d(t,{Z:function(){return m}});var n=r(74677),i=r(2265),o=r(40718),s=r.n(o),l=["variant","color","size"],a=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{d:"M20.5 10.19h-2.89c-2.37 0-4.3-1.93-4.3-4.3V3c0-.55-.45-1-1-1H8.07C4.99 2 2.5 4 2.5 7.57v8.86C2.5 20 4.99 22 8.07 22h7.86c3.08 0 5.57-2 5.57-5.57v-5.24c0-.55-.45-1-1-1Zm-8.22 5.59-2 2c-.07.07-.16.13-.25.16a.671.671 0 0 1-.56 0 .662.662 0 0 1-.22-.15c-.01-.01-.02-.01-.02-.02l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l.71.73v-4.19c0-.41.34-.75.75-.75s.75.34.75.75v4.19l.72-.72c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06Z",fill:t}),i.createElement("path",{d:"M17.43 8.81c.95.01 2.27.01 3.4.01.57 0 .87-.67.47-1.07-1.44-1.45-4.02-4.06-5.5-5.54-.41-.41-1.12-.13-1.12.44v3.49c0 1.46 1.24 2.67 2.75 2.67Z",fill:t}))},u=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{d:"M9 14.352v2.65l2-2M9 11v1M9 17l-2-2M2 9c0-5 2-7 7-7h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.createElement("path",{d:"M22 10v5c0 5-2 7-7 7H9c-5 0-7-2-7-7v-2.02M18 10c-3 0-4-1-4-4V2l8 8",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},c=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{opacity:".4",d:"M20.5 10.19h-2.89c-2.37 0-4.3-1.93-4.3-4.3V3c0-.55-.45-1-1-1H8.07C4.99 2 2.5 4 2.5 7.57v8.86C2.5 20 4.99 22 8.07 22h7.86c3.08 0 5.57-2 5.57-5.57v-5.24c0-.55-.45-1-1-1Z",fill:t}),i.createElement("path",{d:"M15.8 2.21c-.41-.41-1.12-.13-1.12.44v3.49c0 1.46 1.24 2.67 2.75 2.67.95.01 2.27.01 3.4.01.57 0 .87-.67.47-1.07-1.44-1.45-4.02-4.06-5.5-5.54ZM12.28 14.72a.754.754 0 0 0-1.06 0l-.72.72v-4.19c0-.41-.34-.75-.75-.75s-.75.34-.75.75v4.19l-.72-.72a.754.754 0 0 0-1.06 0c-.29.29-.29.77 0 1.06l2 2c.01.01.02.01.02.02.06.06.14.11.22.15.1.03.19.05.29.05.1 0 .19-.02.28-.06.09-.04.17-.09.25-.16l2-2c.29-.29.29-.77 0-1.06Z",fill:t}))},f=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{d:"M9 11v6l2-2M9 17l-2-2",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.createElement("path",{d:"M22 10v5c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.createElement("path",{d:"M22 10h-4c-3 0-4-1-4-4V2l8 8Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},d=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("path",{d:"M9 17.75c-.1 0-.19-.02-.29-.06a.74.74 0 0 1-.46-.69v-6c0-.41.34-.75.75-.75s.75.34.75.75v4.19l.72-.72c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-2 2c-.14.14-.34.22-.53.22Z",fill:t}),i.createElement("path",{d:"M9 17.751c-.19 0-.38-.07-.53-.22l-2-2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2 2c.29.29.29.77 0 1.06-.15.15-.34.22-.53.22Z",fill:t}),i.createElement("path",{d:"M15 22.75H9c-5.43 0-7.75-2.32-7.75-7.75V9c0-5.43 2.32-7.75 7.75-7.75h5c.41 0 .75.34.75.75s-.34.75-.75.75H9C4.39 2.75 2.75 4.39 2.75 9v6c0 4.61 1.64 6.25 6.25 6.25h6c4.61 0 6.25-1.64 6.25-6.25v-5c0-.41.34-.75.75-.75s.75.34.75.75v5c0 5.43-2.32 7.75-7.75 7.75Z",fill:t}),i.createElement("path",{d:"M22 10.748h-4c-3.42 0-4.75-1.33-4.75-4.75v-4c0-.3.18-.58.46-.69.28-.12.6-.05.82.16l8 8a.751.751 0 0 1-.53 1.28Zm-7.25-6.94v2.19c0 2.58.67 3.25 3.25 3.25h2.19l-5.44-5.44Z",fill:t}))},h=function(e){var t=e.color;return i.createElement(i.Fragment,null,i.createElement("g",{opacity:".4",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},i.createElement("path",{d:"M9 11v6l2-2M9 17l-2-2"})),i.createElement("path",{d:"M22 10v5c0 5-2 7-7 7H9c-5 0-7-2-7-7V9c0-5 2-7 7-7h5",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.createElement("path",{d:"M22 10h-4c-3 0-4-1-4-4V2l8 8Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))},p=function(e,t){switch(e){case"Bold":return i.createElement(a,{color:t});case"Broken":return i.createElement(u,{color:t});case"Bulk":return i.createElement(c,{color:t});case"Linear":default:return i.createElement(f,{color:t});case"Outline":return i.createElement(d,{color:t});case"TwoTone":return i.createElement(h,{color:t})}},m=(0,i.forwardRef)(function(e,t){var r=e.variant,o=e.color,s=e.size,a=(0,n._)(e,l);return i.createElement("svg",(0,n.a)({},a,{xmlns:"http://www.w3.org/2000/svg",ref:t,width:s,height:s,viewBox:"0 0 24 24",fill:"none"}),p(r,o))});m.propTypes={variant:s().oneOf(["Linear","Bold","Broken","Bulk","Outline","TwoTone"]),color:s().string,size:s().oneOfType([s().string,s().number])},m.defaultProps={variant:"Linear",color:"currentColor",size:"24"},m.displayName="DocumentDownload"},30401:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},29049:function(e,t){},13126:function(e,t){var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,s=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,a=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,c=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,p=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,y=r?Symbol.for("react.lazy"):60116,g=r?Symbol.for("react.block"):60121,v=r?Symbol.for("react.fundamental"):60117,b=r?Symbol.for("react.responder"):60118,w=r?Symbol.for("react.scope"):60119;function k(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case c:case f:case o:case l:case s:case h:return e;default:switch(e=e&&e.$$typeof){case u:case d:case y:case m:case a:return e;default:return t}}case i:return t}}}function x(e){return k(e)===f}t.AsyncMode=c,t.ConcurrentMode=f,t.ContextConsumer=u,t.ContextProvider=a,t.Element=n,t.ForwardRef=d,t.Fragment=o,t.Lazy=y,t.Memo=m,t.Portal=i,t.Profiler=l,t.StrictMode=s,t.Suspense=h,t.isAsyncMode=function(e){return x(e)||k(e)===c},t.isConcurrentMode=x,t.isContextConsumer=function(e){return k(e)===u},t.isContextProvider=function(e){return k(e)===a},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return k(e)===d},t.isFragment=function(e){return k(e)===o},t.isLazy=function(e){return k(e)===y},t.isMemo=function(e){return k(e)===m},t.isPortal=function(e){return k(e)===i},t.isProfiler=function(e){return k(e)===l},t.isStrictMode=function(e){return k(e)===s},t.isSuspense=function(e){return k(e)===h},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===f||e===l||e===s||e===h||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===m||e.$$typeof===a||e.$$typeof===u||e.$$typeof===d||e.$$typeof===v||e.$$typeof===b||e.$$typeof===w||e.$$typeof===g)},t.typeOf=k},84851:function(e,t,r){e.exports=r(13126)},24878:function(e,t,r){r.d(t,{QM:function(){return t1}});var n,i,o,s,l=r(1119);function a(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}var u=r(2265),c=r(63285),f=r.n(c);function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(){return(h=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r(29049);var m=(0,u.createContext)(),y={context:n=m,withTheme:function(e){var t=u.forwardRef(function(t,r){return u.createElement(n.Consumer,null,function(n){return u.createElement(e,h({theme:n,ref:r},t))})});return f()(t,e),t},useTheme:function(){return u.useContext(n)},ThemeProvider:function(e){function t(){for(var t,r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];return d(p(p(t=e.call.apply(e,[this].concat(i))||this)),"cachedTheme",void 0),d(p(p(t)),"lastOuterTheme",void 0),d(p(p(t)),"lastTheme",void 0),d(p(p(t)),"renderProvider",function(e){var r=t.props.children;return u.createElement(n.Provider,{value:t.getTheme(e)},r)}),t}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e;var r=t.prototype;return r.getTheme=function(e){if(this.props.theme!==this.lastTheme||e!==this.lastOuterTheme||!this.cachedTheme){if(this.lastOuterTheme=e,this.lastTheme=this.props.theme,"function"==typeof this.lastTheme){var t=this.props.theme;this.cachedTheme=t(e)}else{var r=this.props.theme;this.cachedTheme=e?h({},e,r):r}}return this.cachedTheme},r.render=function(){return this.props.children?u.createElement(n.Consumer,null,this.renderProvider):null},t}(u.Component)};y.withTheme,y.ThemeProvider,y.useTheme;var g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v=("undefined"==typeof window?"undefined":g(window))==="object"&&("undefined"==typeof document?"undefined":g(document))==="object"&&9===document.nodeType,b=function(e,t){},w=r(73882);function k(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(0,w.Z)(n.key),n)}}function x(e,t,r){return t&&k(e.prototype,t),r&&k(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function E(e,t){return(E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function R(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,E(e,t)}function S(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var P={}.constructor;function M(e,t,r){void 0===e&&(e="unnamed");var n=r.jss,i=function e(t){if(null==t||"object"!=typeof t)return t;if(Array.isArray(t))return t.map(e);if(t.constructor!==P)return t;var r={};for(var n in t)r[n]=e(t[n]);return r}(t);return n.plugins.onCreateRule(e,i,r)||(e[0],null)}var C=function(e,t){for(var r="",n=0;n<e.length&&"!important"!==e[n];n++)r&&(r+=t),r+=e[n];return r},O=function(e){if(!Array.isArray(e))return e;var t="";if(Array.isArray(e[0]))for(var r=0;r<e.length&&"!important"!==e[r];r++)t&&(t+=", "),t+=C(e[r]," ");else t=C(e,", ");return"!important"===e[e.length-1]&&(t+=" !important"),t};function A(e){return e&&!1===e.format?{linebreak:"",space:""}:{linebreak:"\n",space:" "}}function j(e,t){for(var r="",n=0;n<t;n++)r+="  ";return r+e}function N(e,t,r){void 0===r&&(r={});var n="";if(!t)return n;var i=r.indent,o=void 0===i?0:i,s=t.fallbacks;!1===r.format&&(o=-1/0);var l=A(r),a=l.linebreak,u=l.space;if(e&&o++,s){if(Array.isArray(s))for(var c=0;c<s.length;c++){var f=s[c];for(var d in f){var h=f[d];null!=h&&(n&&(n+=a),n+=j(d+":"+u+O(h)+";",o))}}else for(var p in s){var m=s[p];null!=m&&(n&&(n+=a),n+=j(p+":"+u+O(m)+";",o))}}for(var y in t){var g=t[y];null!=g&&"fallbacks"!==y&&(n&&(n+=a),n+=j(y+":"+u+O(g)+";",o))}return(n||r.allowEmpty)&&e?(o--,n&&(n=""+a+n+a),j(""+e+u+"{"+n,o)+j("}",o)):n}var T=/([[\].#*$><+~=|^:(),"'`\s])/g,L="undefined"!=typeof CSS&&CSS.escape,I=function(e){return L?L(e):e.replace(T,"\\$1")},z=function(){function e(e,t,r){this.type="style",this.isProcessed=!1;var n=r.sheet,i=r.Renderer;this.key=e,this.options=r,this.style=t,n?this.renderer=n.renderer:i&&(this.renderer=new i)}return e.prototype.prop=function(e,t,r){if(void 0===t)return this.style[e];var n=!!r&&r.force;if(!n&&this.style[e]===t)return this;var i=t;r&&!1===r.process||(i=this.options.jss.plugins.onChangeValue(t,e,this));var o=null==i||!1===i,s=e in this.style;if(o&&!s&&!n)return this;var l=o&&s;if(l?delete this.style[e]:this.style[e]=i,this.renderable&&this.renderer)return l?this.renderer.removeProperty(this.renderable,e):this.renderer.setProperty(this.renderable,e,i),this;var a=this.options.sheet;return a&&a.attached,this},e}(),F=function(e){function t(t,r,n){i=e.call(this,t,r,n)||this;var i,o=n.selector,s=n.scoped,l=n.sheet,a=n.generateId;return o?i.selectorText=o:!1!==s&&(i.id=a(S(S(i)),l),i.selectorText="."+I(i.id)),i}R(t,e);var r=t.prototype;return r.applyTo=function(e){var t=this.renderer;if(t){var r=this.toJSON();for(var n in r)t.setProperty(e,n,r[n])}return this},r.toJSON=function(){var e={};for(var t in this.style){var r=this.style[t];"object"!=typeof r?e[t]=r:Array.isArray(r)&&(e[t]=O(r))}return e},r.toString=function(e){var t=this.options.sheet,r=t&&t.options.link?(0,l.Z)({},e,{allowEmpty:!0}):e;return N(this.selectorText,this.style,r)},x(t,[{key:"selector",set:function(e){if(e!==this.selectorText){this.selectorText=e;var t=this.renderer,r=this.renderable;r&&t&&!t.setSelector(r,e)&&t.replaceRule(r,this)}},get:function(){return this.selectorText}}]),t}(z),_={indent:1,children:!0},Z=/@([\w-]+)/,B=function(){function e(e,t,r){this.type="conditional",this.isProcessed=!1,this.key=e;var n=e.match(Z);for(var i in this.at=n?n[1]:"unknown",this.query=r.name||"@"+this.at,this.options=r,this.rules=new ei((0,l.Z)({},r,{parent:this})),t)this.rules.add(i,t[i]);this.rules.process()}var t=e.prototype;return t.getRule=function(e){return this.rules.get(e)},t.indexOf=function(e){return this.rules.indexOf(e)},t.addRule=function(e,t,r){var n=this.rules.add(e,t,r);return n?(this.options.jss.plugins.onProcessRule(n),n):null},t.replaceRule=function(e,t,r){var n=this.rules.replace(e,t,r);return n&&this.options.jss.plugins.onProcessRule(n),n},t.toString=function(e){void 0===e&&(e=_);var t=A(e).linebreak;if(null==e.indent&&(e.indent=_.indent),null==e.children&&(e.children=_.children),!1===e.children)return this.query+" {}";var r=this.rules.toString(e);return r?this.query+" {"+t+r+t+"}":""},e}(),$=/@container|@media|@supports\s+/,D={indent:1,children:!0},V=/@keyframes\s+([\w-]+)/,H=function(){function e(e,t,r){this.type="keyframes",this.at="@keyframes",this.isProcessed=!1;var n=e.match(V);n&&n[1]?this.name=n[1]:this.name="noname",this.key=this.type+"-"+this.name,this.options=r;var i=r.scoped,o=r.sheet,s=r.generateId;for(var a in this.id=!1===i?this.name:I(s(this,o)),this.rules=new ei((0,l.Z)({},r,{parent:this})),t)this.rules.add(a,t[a],(0,l.Z)({},r,{parent:this}));this.rules.process()}return e.prototype.toString=function(e){void 0===e&&(e=D);var t=A(e).linebreak;if(null==e.indent&&(e.indent=D.indent),null==e.children&&(e.children=D.children),!1===e.children)return this.at+" "+this.id+" {}";var r=this.rules.toString(e);return r&&(r=""+t+r+t),this.at+" "+this.id+" {"+r+"}"},e}(),U=/@keyframes\s+/,W=/\$([\w-]+)/g,Q=function(e,t){return"string"==typeof e?e.replace(W,function(e,r){return r in t?t[r]:e}):e},G=function(e,t,r){var n=e[t],i=Q(n,r);i!==n&&(e[t]=i)},Y=function(e){function t(){return e.apply(this,arguments)||this}return R(t,e),t.prototype.toString=function(e){var t=this.options.sheet,r=t&&t.options.link?(0,l.Z)({},e,{allowEmpty:!0}):e;return N(this.key,this.style,r)},t}(z),q=function(){function e(e,t,r){this.type="font-face",this.at="@font-face",this.isProcessed=!1,this.key=e,this.style=t,this.options=r}return e.prototype.toString=function(e){var t=A(e).linebreak;if(Array.isArray(this.style)){for(var r="",n=0;n<this.style.length;n++)r+=N(this.at,this.style[n]),this.style[n+1]&&(r+=t);return r}return N(this.at,this.style,e)},e}(),K=/@font-face/,X=function(){function e(e,t,r){this.type="viewport",this.at="@viewport",this.isProcessed=!1,this.key=e,this.style=t,this.options=r}return e.prototype.toString=function(e){return N(this.key,this.style,e)},e}(),J=function(){function e(e,t,r){this.type="simple",this.isProcessed=!1,this.key=e,this.value=t,this.options=r}return e.prototype.toString=function(e){if(Array.isArray(this.value)){for(var t="",r=0;r<this.value.length;r++)t+=this.key+" "+this.value[r]+";",this.value[r+1]&&(t+="\n");return t}return this.key+" "+this.value+";"},e}(),ee={"@charset":!0,"@import":!0,"@namespace":!0},et=[{onCreateRule:function(e,t,r){return"@"===e[0]||r.parent&&"keyframes"===r.parent.type?null:new F(e,t,r)}},{onCreateRule:function(e,t,r){return $.test(e)?new B(e,t,r):null}},{onCreateRule:function(e,t,r){return"string"==typeof e&&U.test(e)?new H(e,t,r):null},onProcessStyle:function(e,t,r){return"style"===t.type&&r&&("animation-name"in e&&G(e,"animation-name",r.keyframes),"animation"in e&&G(e,"animation",r.keyframes)),e},onChangeValue:function(e,t,r){var n=r.options.sheet;if(!n)return e;switch(t){case"animation":case"animation-name":return Q(e,n.keyframes);default:return e}}},{onCreateRule:function(e,t,r){return r.parent&&"keyframes"===r.parent.type?new Y(e,t,r):null}},{onCreateRule:function(e,t,r){return K.test(e)?new q(e,t,r):null}},{onCreateRule:function(e,t,r){return"@viewport"===e||"@-ms-viewport"===e?new X(e,t,r):null}},{onCreateRule:function(e,t,r){return e in ee?new J(e,t,r):null}}],er={process:!0},en={force:!0,process:!0},ei=function(){function e(e){this.map={},this.raw={},this.index=[],this.counter=0,this.options=e,this.classes=e.classes,this.keyframes=e.keyframes}var t=e.prototype;return t.add=function(e,t,r){var n=this.options,i=n.parent,o=n.sheet,s=n.jss,a=n.Renderer,u=n.generateId,c=n.scoped,f=(0,l.Z)({classes:this.classes,parent:i,sheet:o,jss:s,Renderer:a,generateId:u,scoped:c,name:e,keyframes:this.keyframes,selector:void 0},r),d=e;e in this.raw&&(d=e+"-d"+this.counter++),this.raw[d]=t,d in this.classes&&(f.selector="."+I(this.classes[d]));var h=M(d,t,f);if(!h)return null;this.register(h);var p=void 0===f.index?this.index.length:f.index;return this.index.splice(p,0,h),h},t.replace=function(e,t,r){var n=this.get(e),i=this.index.indexOf(n);n&&this.remove(n);var o=r;return -1!==i&&(o=(0,l.Z)({},r,{index:i})),this.add(e,t,o)},t.get=function(e){return this.map[e]},t.remove=function(e){this.unregister(e),delete this.raw[e.key],this.index.splice(this.index.indexOf(e),1)},t.indexOf=function(e){return this.index.indexOf(e)},t.process=function(){var e=this.options.jss.plugins;this.index.slice(0).forEach(e.onProcessRule,e)},t.register=function(e){this.map[e.key]=e,e instanceof F?(this.map[e.selector]=e,e.id&&(this.classes[e.key]=e.id)):e instanceof H&&this.keyframes&&(this.keyframes[e.name]=e.id)},t.unregister=function(e){delete this.map[e.key],e instanceof F?(delete this.map[e.selector],delete this.classes[e.key]):e instanceof H&&delete this.keyframes[e.name]},t.update=function(){if("string"==typeof(arguments.length<=0?void 0:arguments[0])?(e=arguments.length<=0?void 0:arguments[0],t=arguments.length<=1?void 0:arguments[1],r=arguments.length<=2?void 0:arguments[2]):(t=arguments.length<=0?void 0:arguments[0],r=arguments.length<=1?void 0:arguments[1],e=null),e)this.updateOne(this.get(e),t,r);else for(var e,t,r,n=0;n<this.index.length;n++)this.updateOne(this.index[n],t,r)},t.updateOne=function(t,r,n){void 0===n&&(n=er);var i=this.options,o=i.jss.plugins,s=i.sheet;if(t.rules instanceof e){t.rules.update(r,n);return}var l=t.style;if(o.onUpdate(r,t,s,n),n.process&&l&&l!==t.style){for(var a in o.onProcessStyle(t.style,t,s),t.style){var u=t.style[a];u!==l[a]&&t.prop(a,u,en)}for(var c in l){var f=t.style[c],d=l[c];null==f&&f!==d&&t.prop(c,null,en)}}},t.toString=function(e){for(var t="",r=this.options.sheet,n=!!r&&r.options.link,i=A(e).linebreak,o=0;o<this.index.length;o++){var s=this.index[o].toString(e);(s||n)&&(t&&(t+=i),t+=s)}return t},e}(),eo=function(){function e(e,t){for(var r in this.attached=!1,this.deployed=!1,this.classes={},this.keyframes={},this.options=(0,l.Z)({},t,{sheet:this,parent:this,classes:this.classes,keyframes:this.keyframes}),t.Renderer&&(this.renderer=new t.Renderer(this)),this.rules=new ei(this.options),e)this.rules.add(r,e[r]);this.rules.process()}var t=e.prototype;return t.attach=function(){return this.attached||(this.renderer&&this.renderer.attach(),this.attached=!0,this.deployed||this.deploy()),this},t.detach=function(){return this.attached&&(this.renderer&&this.renderer.detach(),this.attached=!1),this},t.addRule=function(e,t,r){var n=this.queue;this.attached&&!n&&(this.queue=[]);var i=this.rules.add(e,t,r);return i?((this.options.jss.plugins.onProcessRule(i),this.attached)?this.deployed&&(n?n.push(i):(this.insertRule(i),this.queue&&(this.queue.forEach(this.insertRule,this),this.queue=void 0))):this.deployed=!1,i):null},t.replaceRule=function(e,t,r){var n=this.rules.get(e);if(!n)return this.addRule(e,t,r);var i=this.rules.replace(e,t,r);return(i&&this.options.jss.plugins.onProcessRule(i),this.attached)?this.deployed&&this.renderer&&(i?n.renderable&&this.renderer.replaceRule(n.renderable,i):this.renderer.deleteRule(n)):this.deployed=!1,i},t.insertRule=function(e){this.renderer&&this.renderer.insertRule(e)},t.addRules=function(e,t){var r=[];for(var n in e){var i=this.addRule(n,e[n],t);i&&r.push(i)}return r},t.getRule=function(e){return this.rules.get(e)},t.deleteRule=function(e){var t="object"==typeof e?e:this.rules.get(e);return!!t&&(!this.attached||!!t.renderable)&&(this.rules.remove(t),!this.attached||!t.renderable||!this.renderer||this.renderer.deleteRule(t.renderable))},t.indexOf=function(e){return this.rules.indexOf(e)},t.deploy=function(){return this.renderer&&this.renderer.deploy(),this.deployed=!0,this},t.update=function(){var e;return(e=this.rules).update.apply(e,arguments),this},t.updateOne=function(e,t,r){return this.rules.updateOne(e,t,r),this},t.toString=function(e){return this.rules.toString(e)},e}(),es=function(){function e(){this.plugins={internal:[],external:[]},this.registry={}}var t=e.prototype;return t.onCreateRule=function(e,t,r){for(var n=0;n<this.registry.onCreateRule.length;n++){var i=this.registry.onCreateRule[n](e,t,r);if(i)return i}return null},t.onProcessRule=function(e){if(!e.isProcessed){for(var t=e.options.sheet,r=0;r<this.registry.onProcessRule.length;r++)this.registry.onProcessRule[r](e,t);e.style&&this.onProcessStyle(e.style,e,t),e.isProcessed=!0}},t.onProcessStyle=function(e,t,r){for(var n=0;n<this.registry.onProcessStyle.length;n++)t.style=this.registry.onProcessStyle[n](t.style,t,r)},t.onProcessSheet=function(e){for(var t=0;t<this.registry.onProcessSheet.length;t++)this.registry.onProcessSheet[t](e)},t.onUpdate=function(e,t,r,n){for(var i=0;i<this.registry.onUpdate.length;i++)this.registry.onUpdate[i](e,t,r,n)},t.onChangeValue=function(e,t,r){for(var n=e,i=0;i<this.registry.onChangeValue.length;i++)n=this.registry.onChangeValue[i](n,t,r);return n},t.use=function(e,t){void 0===t&&(t={queue:"external"});var r=this.plugins[t.queue];-1===r.indexOf(e)&&(r.push(e),this.registry=[].concat(this.plugins.external,this.plugins.internal).reduce(function(e,t){for(var r in t)r in e&&e[r].push(t[r]);return e},{onCreateRule:[],onProcessRule:[],onProcessStyle:[],onProcessSheet:[],onChangeValue:[],onUpdate:[]}))},e}(),el=new(function(){function e(){this.registry=[]}var t=e.prototype;return t.add=function(e){var t=this.registry,r=e.options.index;if(-1===t.indexOf(e)){if(0===t.length||r>=this.index){t.push(e);return}for(var n=0;n<t.length;n++)if(t[n].options.index>r){t.splice(n,0,e);return}}},t.reset=function(){this.registry=[]},t.remove=function(e){var t=this.registry.indexOf(e);this.registry.splice(t,1)},t.toString=function(e){for(var t=void 0===e?{}:e,r=t.attached,n=a(t,["attached"]),i=A(n).linebreak,o="",s=0;s<this.registry.length;s++){var l=this.registry[s];(null==r||l.attached===r)&&(o&&(o+=i),o+=l.toString(n))}return o},x(e,[{key:"index",get:function(){return 0===this.registry.length?0:this.registry[this.registry.length-1].options.index}}]),e}()),ea="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window&&window.Math===Math?window:"undefined"!=typeof self&&self.Math===Math?self:Function("return this")(),eu="2f1acc6c3a606b082e5eef5e54414ffb";null==ea[eu]&&(ea[eu]=0);var ec=ea[eu]++,ef=function(e){void 0===e&&(e={});var t=0;return function(r,n){t+=1;var i="",o="";return(n&&(n.options.classNamePrefix&&(o=n.options.classNamePrefix),null!=n.options.jss.id&&(i=String(n.options.jss.id))),e.minify)?""+(o||"c")+ec+i+t:o+r.key+"-"+ec+(i?"-"+i:"")+"-"+t}},ed=function(e){var t;return function(){return t||(t=e()),t}},eh=function(e,t){try{if(e.attributeStyleMap)return e.attributeStyleMap.get(t);return e.style.getPropertyValue(t)}catch(e){return""}},ep=function(e,t,r){try{var n=r;if(Array.isArray(r)&&(n=O(r)),e.attributeStyleMap)e.attributeStyleMap.set(t,n);else{var i=n?n.indexOf("!important"):-1,o=i>-1?n.substr(0,i-1):n;e.style.setProperty(t,o,i>-1?"important":"")}}catch(e){return!1}return!0},em=function(e,t){try{e.attributeStyleMap?e.attributeStyleMap.delete(t):e.style.removeProperty(t)}catch(e){}},ey=function(e,t){return e.selectorText=t,e.selectorText===t},eg=ed(function(){return document.querySelector("head")}),ev=ed(function(){var e=document.querySelector('meta[property="csp-nonce"]');return e?e.getAttribute("content"):null}),eb=function(e,t,r){try{"insertRule"in e?e.insertRule(t,r):"appendRule"in e&&e.appendRule(t)}catch(e){return!1}return e.cssRules[r]},ew=function(e,t){var r=e.cssRules.length;return void 0===t||t>r?r:t},ek=function(){var e=document.createElement("style");return e.textContent="\n",e},ex=function(){function e(e){this.getPropertyValue=eh,this.setProperty=ep,this.removeProperty=em,this.setSelector=ey,this.hasInsertedRules=!1,this.cssRules=[],e&&el.add(e),this.sheet=e;var t=this.sheet?this.sheet.options:{},r=t.media,n=t.meta,i=t.element;this.element=i||ek(),this.element.setAttribute("data-jss",""),r&&this.element.setAttribute("media",r),n&&this.element.setAttribute("data-meta",n);var o=ev();o&&this.element.setAttribute("nonce",o)}var t=e.prototype;return t.attach=function(){if(!this.element.parentNode&&this.sheet){!function(e,t){var r=t.insertionPoint,n=function(e){var t=el.registry;if(t.length>0){var r=function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(n.attached&&n.options.index>t.index&&n.options.insertionPoint===t.insertionPoint)return n}return null}(t,e);if(r&&r.renderer)return{parent:r.renderer.element.parentNode,node:r.renderer.element};if((r=function(e,t){for(var r=e.length-1;r>=0;r--){var n=e[r];if(n.attached&&n.options.insertionPoint===t.insertionPoint)return n}return null}(t,e))&&r.renderer)return{parent:r.renderer.element.parentNode,node:r.renderer.element.nextSibling}}var n=e.insertionPoint;if(n&&"string"==typeof n){var i=function(e){for(var t=eg(),r=0;r<t.childNodes.length;r++){var n=t.childNodes[r];if(8===n.nodeType&&n.nodeValue.trim()===e)return n}return null}(n);if(i)return{parent:i.parentNode,node:i.nextSibling}}return!1}(t);if(!1!==n&&n.parent){n.parent.insertBefore(e,n.node);return}if(r&&"number"==typeof r.nodeType){var i=r.parentNode;i&&i.insertBefore(e,r.nextSibling);return}eg().appendChild(e)}(this.element,this.sheet.options);var e=!!(this.sheet&&this.sheet.deployed);this.hasInsertedRules&&e&&(this.hasInsertedRules=!1,this.deploy())}},t.detach=function(){if(this.sheet){var e=this.element.parentNode;e&&e.removeChild(this.element),this.sheet.options.link&&(this.cssRules=[],this.element.textContent="\n")}},t.deploy=function(){var e=this.sheet;if(e){if(e.options.link){this.insertRules(e.rules);return}this.element.textContent="\n"+e.toString()+"\n"}},t.insertRules=function(e,t){for(var r=0;r<e.index.length;r++)this.insertRule(e.index[r],r,t)},t.insertRule=function(e,t,r){if(void 0===r&&(r=this.element.sheet),e.rules){var n=r;if("conditional"===e.type||"keyframes"===e.type){var i=ew(r,t);if(!1===(n=eb(r,e.toString({children:!1}),i)))return!1;this.refCssRule(e,i,n)}return this.insertRules(e.rules,n),n}var o=e.toString();if(!o)return!1;var s=ew(r,t),l=eb(r,o,s);return!1!==l&&(this.hasInsertedRules=!0,this.refCssRule(e,s,l),l)},t.refCssRule=function(e,t,r){e.renderable=r,e.options.parent instanceof eo&&this.cssRules.splice(t,0,r)},t.deleteRule=function(e){var t=this.element.sheet,r=this.indexOf(e);return -1!==r&&(t.deleteRule(r),this.cssRules.splice(r,1),!0)},t.indexOf=function(e){return this.cssRules.indexOf(e)},t.replaceRule=function(e,t){var r=this.indexOf(e);return -1!==r&&(this.element.sheet.deleteRule(r),this.cssRules.splice(r,1),this.insertRule(t,r))},t.getRules=function(){return this.element.sheet.cssRules},e}(),eE=0,eR=function(){function e(e){this.id=eE++,this.version="10.10.0",this.plugins=new es,this.options={id:{minify:!1},createGenerateId:ef,Renderer:v?ex:null,plugins:[]},this.generateId=ef({minify:!1});for(var t=0;t<et.length;t++)this.plugins.use(et[t],{queue:"internal"});this.setup(e)}var t=e.prototype;return t.setup=function(e){return void 0===e&&(e={}),e.createGenerateId&&(this.options.createGenerateId=e.createGenerateId),e.id&&(this.options.id=(0,l.Z)({},this.options.id,e.id)),(e.createGenerateId||e.id)&&(this.generateId=this.options.createGenerateId(this.options.id)),null!=e.insertionPoint&&(this.options.insertionPoint=e.insertionPoint),"Renderer"in e&&(this.options.Renderer=e.Renderer),e.plugins&&this.use.apply(this,e.plugins),this},t.createStyleSheet=function(e,t){void 0===t&&(t={});var r=t.index;"number"!=typeof r&&(r=0===el.index?0:el.index+1);var n=new eo(e,(0,l.Z)({},t,{jss:this,generateId:t.generateId||this.generateId,insertionPoint:this.options.insertionPoint,Renderer:this.options.Renderer,index:r}));return this.plugins.onProcessSheet(n),n},t.removeStyleSheet=function(e){return e.detach(),el.remove(e),this},t.createRule=function(e,t,r){if(void 0===t&&(t={}),void 0===r&&(r={}),"object"==typeof e)return this.createRule(void 0,e,t);var n=(0,l.Z)({},r,{name:e,jss:this,Renderer:this.options.Renderer});n.generateId||(n.generateId=this.generateId),n.classes||(n.classes={}),n.keyframes||(n.keyframes={});var i=M(e,t,n);return i&&this.plugins.onProcessRule(i),i},t.use=function(){for(var e=this,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return r.forEach(function(t){e.plugins.use(t)}),this},e}(),eS=function(e){return new eR(e)},eP=function(){function e(){this.length=0,this.sheets=new WeakMap}var t=e.prototype;return t.get=function(e){var t=this.sheets.get(e);return t&&t.sheet},t.add=function(e,t){this.sheets.has(e)||(this.length++,this.sheets.set(e,{sheet:t,refs:0}))},t.manage=function(e){var t=this.sheets.get(e);if(t)return 0===t.refs&&t.sheet.attach(),t.refs++,t.sheet;b(!1,"[JSS] SheetsManager: can't find sheet to manage")},t.unmanage=function(e){var t=this.sheets.get(e);t?t.refs>0&&(t.refs--,0===t.refs&&t.sheet.detach()):b(!1,"SheetsManager: can't find sheet to unmanage")},x(e,[{key:"size",get:function(){return this.length}}]),e}(),eM="object"==typeof CSS&&null!=CSS&&"number"in CSS;eS();var eC=Date.now(),eO="fnValues"+eC,eA="fnStyle"+ ++eC,ej=r(66994),eN=function(e){return e&&e[ej.Z]&&e===e[ej.Z]()},eT=/;\n/,eL=function(e){for(var t={},r=e.split(eT),n=0;n<r.length;n++){var i=(r[n]||"").trim();if(i){var o=i.indexOf(":");if(-1===o)continue;var s=i.substr(0,o).trim(),l=i.substr(o+1).trim();t[s]=l}}return t},eI=function(e){"string"==typeof e.style&&(e.style=eL(e.style))},ez="@global",eF="@global ",e_=function(){function e(e,t,r){for(var n in this.type="global",this.at=ez,this.isProcessed=!1,this.key=e,this.options=r,this.rules=new ei((0,l.Z)({},r,{parent:this})),t)this.rules.add(n,t[n]);this.rules.process()}var t=e.prototype;return t.getRule=function(e){return this.rules.get(e)},t.addRule=function(e,t,r){var n=this.rules.add(e,t,r);return n&&this.options.jss.plugins.onProcessRule(n),n},t.replaceRule=function(e,t,r){var n=this.rules.replace(e,t,r);return n&&this.options.jss.plugins.onProcessRule(n),n},t.indexOf=function(e){return this.rules.indexOf(e)},t.toString=function(e){return this.rules.toString(e)},e}(),eZ=function(){function e(e,t,r){this.type="global",this.at=ez,this.isProcessed=!1,this.key=e,this.options=r;var n=e.substr(eF.length);this.rule=r.jss.createRule(n,t,(0,l.Z)({},r,{parent:this}))}return e.prototype.toString=function(e){return this.rule?this.rule.toString(e):""},e}(),eB=/\s*,\s*/g;function e$(e,t){for(var r=e.split(eB),n="",i=0;i<r.length;i++)n+=t+" "+r[i].trim(),r[i+1]&&(n+=", ");return n}var eD=function(e){return e&&"object"==typeof e&&!Array.isArray(e)},eV="extendCurrValue"+Date.now(),eH=/\s*,\s*/g,eU=/&/g,eW=/\$([\w-]+)/g,eQ=/[A-Z]/g,eG=/^ms-/,eY={};function eq(e){return"-"+e.toLowerCase()}var eK=function(e){if(eY.hasOwnProperty(e))return eY[e];var t=e.replace(eQ,eq);return eY[e]=eG.test(t)?"-"+t:t};function eX(e){var t={};for(var r in e)t[0===r.indexOf("--")?r:eK(r)]=e[r];return e.fallbacks&&(Array.isArray(e.fallbacks)?t.fallbacks=e.fallbacks.map(eX):t.fallbacks=eX(e.fallbacks)),t}var eJ=eM&&CSS?CSS.px:"px",e0=eM&&CSS?CSS.ms:"ms",e1=eM&&CSS?CSS.percent:"%";function e2(e){var t=/(-[a-z])/g,r=function(e){return e[1].toUpperCase()},n={};for(var i in e)n[i]=e[i],n[i.replace(t,r)]=e[i];return n}var e7=e2({"animation-delay":e0,"animation-duration":e0,"background-position":eJ,"background-position-x":eJ,"background-position-y":eJ,"background-size":eJ,border:eJ,"border-bottom":eJ,"border-bottom-left-radius":eJ,"border-bottom-right-radius":eJ,"border-bottom-width":eJ,"border-left":eJ,"border-left-width":eJ,"border-radius":eJ,"border-right":eJ,"border-right-width":eJ,"border-top":eJ,"border-top-left-radius":eJ,"border-top-right-radius":eJ,"border-top-width":eJ,"border-width":eJ,"border-block":eJ,"border-block-end":eJ,"border-block-end-width":eJ,"border-block-start":eJ,"border-block-start-width":eJ,"border-block-width":eJ,"border-inline":eJ,"border-inline-end":eJ,"border-inline-end-width":eJ,"border-inline-start":eJ,"border-inline-start-width":eJ,"border-inline-width":eJ,"border-start-start-radius":eJ,"border-start-end-radius":eJ,"border-end-start-radius":eJ,"border-end-end-radius":eJ,margin:eJ,"margin-bottom":eJ,"margin-left":eJ,"margin-right":eJ,"margin-top":eJ,"margin-block":eJ,"margin-block-end":eJ,"margin-block-start":eJ,"margin-inline":eJ,"margin-inline-end":eJ,"margin-inline-start":eJ,padding:eJ,"padding-bottom":eJ,"padding-left":eJ,"padding-right":eJ,"padding-top":eJ,"padding-block":eJ,"padding-block-end":eJ,"padding-block-start":eJ,"padding-inline":eJ,"padding-inline-end":eJ,"padding-inline-start":eJ,"mask-position-x":eJ,"mask-position-y":eJ,"mask-size":eJ,height:eJ,width:eJ,"min-height":eJ,"max-height":eJ,"min-width":eJ,"max-width":eJ,bottom:eJ,left:eJ,top:eJ,right:eJ,inset:eJ,"inset-block":eJ,"inset-block-end":eJ,"inset-block-start":eJ,"inset-inline":eJ,"inset-inline-end":eJ,"inset-inline-start":eJ,"box-shadow":eJ,"text-shadow":eJ,"column-gap":eJ,"column-rule":eJ,"column-rule-width":eJ,"column-width":eJ,"font-size":eJ,"font-size-delta":eJ,"letter-spacing":eJ,"text-decoration-thickness":eJ,"text-indent":eJ,"text-stroke":eJ,"text-stroke-width":eJ,"word-spacing":eJ,motion:eJ,"motion-offset":eJ,outline:eJ,"outline-offset":eJ,"outline-width":eJ,perspective:eJ,"perspective-origin-x":e1,"perspective-origin-y":e1,"transform-origin":e1,"transform-origin-x":e1,"transform-origin-y":e1,"transform-origin-z":e1,"transition-delay":e0,"transition-duration":e0,"vertical-align":eJ,"flex-basis":eJ,"shape-margin":eJ,size:eJ,gap:eJ,grid:eJ,"grid-gap":eJ,"row-gap":eJ,"grid-row-gap":eJ,"grid-column-gap":eJ,"grid-template-rows":eJ,"grid-template-columns":eJ,"grid-auto-rows":eJ,"grid-auto-columns":eJ,"box-shadow-x":eJ,"box-shadow-y":eJ,"box-shadow-blur":eJ,"box-shadow-spread":eJ,"font-line-height":eJ,"text-shadow-x":eJ,"text-shadow-y":eJ,"text-shadow-blur":eJ});function e5(e,t,r){if(null==t)return t;if(Array.isArray(t))for(var n=0;n<t.length;n++)t[n]=e5(e,t[n],r);else if("object"==typeof t){if("fallbacks"===e)for(var i in t)t[i]=e5(i,t[i],r);else for(var o in t)t[o]=e5(e+"-"+o,t[o],r)}else if("number"==typeof t&&!1===isNaN(t)){var s=r[e]||e7[e];return s&&!(0===t&&s===eJ)?"function"==typeof s?s(t).toString():""+t+s:t.toString()}return t}var e4=function(e){void 0===e&&(e={});var t=e2(e);return{onProcessStyle:function(e,r){if("style"!==r.type)return e;for(var n in e)e[n]=e5(n,e[n],t);return e},onChangeValue:function(e,r){return e5(r,e,t)}}},e8={"background-size":!0,"background-position":!0,border:!0,"border-bottom":!0,"border-left":!0,"border-top":!0,"border-right":!0,"border-radius":!0,"border-image":!0,"border-width":!0,"border-style":!0,"border-color":!0,"box-shadow":!0,flex:!0,margin:!0,padding:!0,outline:!0,"transform-origin":!0,transform:!0,transition:!0},e3={position:!0,size:!0},e6={padding:{top:0,right:0,bottom:0,left:0},margin:{top:0,right:0,bottom:0,left:0},background:{attachment:null,color:null,image:null,position:null,repeat:null},border:{width:null,style:null,color:null},"border-top":{width:null,style:null,color:null},"border-right":{width:null,style:null,color:null},"border-bottom":{width:null,style:null,color:null},"border-left":{width:null,style:null,color:null},outline:{width:null,style:null,color:null},"list-style":{type:null,position:null,image:null},transition:{property:null,duration:null,"timing-function":null,timingFunction:null,delay:null},animation:{name:null,duration:null,"timing-function":null,timingFunction:null,delay:null,"iteration-count":null,iterationCount:null,direction:null,"fill-mode":null,fillMode:null,"play-state":null,playState:null},"box-shadow":{x:0,y:0,blur:0,spread:0,color:null,inset:null},"text-shadow":{x:0,y:0,blur:null,color:null}},e9={border:{radius:"border-radius",image:"border-image",width:"border-width",style:"border-style",color:"border-color"},"border-bottom":{width:"border-bottom-width",style:"border-bottom-style",color:"border-bottom-color"},"border-top":{width:"border-top-width",style:"border-top-style",color:"border-top-color"},"border-left":{width:"border-left-width",style:"border-left-style",color:"border-left-color"},"border-right":{width:"border-right-width",style:"border-right-style",color:"border-right-color"},background:{size:"background-size",image:"background-image"},font:{style:"font-style",variant:"font-variant",weight:"font-weight",stretch:"font-stretch",size:"font-size",family:"font-family",lineHeight:"line-height","line-height":"line-height"},flex:{grow:"flex-grow",basis:"flex-basis",direction:"flex-direction",wrap:"flex-wrap",flow:"flex-flow",shrink:"flex-shrink"},align:{self:"align-self",items:"align-items",content:"align-content"},grid:{"template-columns":"grid-template-columns",templateColumns:"grid-template-columns","template-rows":"grid-template-rows",templateRows:"grid-template-rows","template-areas":"grid-template-areas",templateAreas:"grid-template-areas",template:"grid-template","auto-columns":"grid-auto-columns",autoColumns:"grid-auto-columns","auto-rows":"grid-auto-rows",autoRows:"grid-auto-rows","auto-flow":"grid-auto-flow",autoFlow:"grid-auto-flow",row:"grid-row",column:"grid-column","row-start":"grid-row-start",rowStart:"grid-row-start","row-end":"grid-row-end",rowEnd:"grid-row-end","column-start":"grid-column-start",columnStart:"grid-column-start","column-end":"grid-column-end",columnEnd:"grid-column-end",area:"grid-area",gap:"grid-gap","row-gap":"grid-row-gap",rowGap:"grid-row-gap","column-gap":"grid-column-gap",columnGap:"grid-column-gap"}};function te(e,t,r,n,i){if(!(e6[t]||e9[t]))return[];var o=[];if(e9[t]&&(e=function(e,t,r,n){for(var i in r){var o=r[i];if(void 0!==e[i]&&(n||!t.prop(o))){var s,l=tt(((s={})[o]=e[i],s),t)[o];n?t.style.fallbacks[o]=l:t.style[o]=l}delete e[i]}return e}(e,r,e9[t],n)),Object.keys(e).length)for(var s in e6[t]){if(e[s]){Array.isArray(e[s])?o.push(null===e3[s]?e[s]:e[s].join(" ")):o.push(e[s]);continue}null!=e6[t][s]&&o.push(e6[t][s])}return!o.length||i?o:[o]}function tt(e,t,r){for(var n in e){var i=e[n];if(Array.isArray(i)){if(!Array.isArray(i[0])){if("fallbacks"===n){for(var o=0;o<e.fallbacks.length;o++)e.fallbacks[o]=tt(e.fallbacks[o],t,!0);continue}e[n]=function e(t,r,n,i){return null==n[r]?t:0===t.length?[]:Array.isArray(t[0])?e(t[0],r,n,i):"object"==typeof t[0]?t.map(function(e){return te(e,r,i,!1,!0)}):[t]}(i,n,e8,t),e[n].length||delete e[n]}}else if("object"==typeof i){if("fallbacks"===n){e.fallbacks=tt(e.fallbacks,t,!0);continue}e[n]=te(i,n,t,r),e[n].length||delete e[n]}else""===e[n]&&delete e[n]}return e}var tr=r(96240),tn=r(29062),ti="",to="",ts="",tl="",ta=v&&"ontouchstart"in document.documentElement;if(v){var tu={Moz:"-moz-",ms:"-ms-",O:"-o-",Webkit:"-webkit-"},tc=document.createElement("p").style;for(var tf in tu)if(tf+"Transform" in tc){ti=tf,to=tu[tf];break}"Webkit"===ti&&"msHyphens"in tc&&(ti="ms",to=tu.ms,tl="edge"),"Webkit"===ti&&"-apple-trailing-word"in tc&&(ts="apple")}var td={js:ti,css:to,vendor:ts,browser:tl,isTouch:ta},th=/[-\s]+(.)?/g;function tp(e,t){return t?t.toUpperCase():""}function tm(e){return e.replace(th,tp)}function ty(e){return tm("-"+e)}var tg={"flex-grow":"flex-positive","flex-shrink":"flex-negative","flex-basis":"flex-preferred-size","justify-content":"flex-pack",order:"flex-order","align-items":"flex-align","align-content":"flex-line-pack"},tv={flex:"box-flex","flex-grow":"box-flex","flex-direction":["box-orient","box-direction"],order:"box-ordinal-group","align-items":"box-align","flex-flow":["box-orient","box-direction"],"justify-content":"box-pack"},tb=Object.keys(tv),tw=function(e){return td.css+e},tk=[{noPrefill:["appearance"],supportedProperty:function(e){return"appearance"===e&&("ms"===td.js?"-webkit-"+e:td.css+e)}},{noPrefill:["color-adjust"],supportedProperty:function(e){return"color-adjust"===e&&("Webkit"===td.js?td.css+"print-"+e:e)}},{noPrefill:["mask"],supportedProperty:function(e,t){if(!/^mask/.test(e))return!1;if("Webkit"===td.js){var r="mask-image";if(tm(r) in t)return e;if(td.js+ty(r) in t)return td.css+e}return e}},{noPrefill:["text-orientation"],supportedProperty:function(e){return"text-orientation"===e&&("apple"!==td.vendor||td.isTouch?e:td.css+e)}},{noPrefill:["transform"],supportedProperty:function(e,t,r){return"transform"===e&&(r.transform?e:td.css+e)}},{noPrefill:["transition"],supportedProperty:function(e,t,r){return"transition"===e&&(r.transition?e:td.css+e)}},{noPrefill:["writing-mode"],supportedProperty:function(e){return"writing-mode"===e&&("Webkit"===td.js||"ms"===td.js&&"edge"!==td.browser?td.css+e:e)}},{noPrefill:["user-select"],supportedProperty:function(e){return"user-select"===e&&("Moz"===td.js||"ms"===td.js||"apple"===td.vendor?td.css+e:e)}},{supportedProperty:function(e,t){return!!/^break-/.test(e)&&("Webkit"===td.js?"WebkitColumn"+ty(e) in t&&td.css+"column-"+e:"Moz"===td.js&&"page"+ty(e) in t&&"page-"+e)}},{supportedProperty:function(e,t){if(!/^(border|margin|padding)-inline/.test(e))return!1;if("Moz"===td.js)return e;var r=e.replace("-inline","");return td.js+ty(r) in t&&td.css+r}},{supportedProperty:function(e,t){return tm(e) in t&&e}},{supportedProperty:function(e,t){var r=ty(e);return"-"===e[0]||"-"===e[0]&&"-"===e[1]?e:td.js+r in t?td.css+e:"Webkit"!==td.js&&"Webkit"+r in t&&"-webkit-"+e}},{supportedProperty:function(e){return"scroll-snap"===e.substring(0,11)&&("ms"===td.js?""+td.css+e:e)}},{supportedProperty:function(e){return"overscroll-behavior"===e&&("ms"===td.js?td.css+"scroll-chaining":e)}},{supportedProperty:function(e,t){var r=tg[e];return!!r&&td.js+ty(r) in t&&td.css+r}},{supportedProperty:function(e,t,r){var n=r.multiple;if(tb.indexOf(e)>-1){var i=tv[e];if(!Array.isArray(i))return td.js+ty(i) in t&&td.css+i;if(!n)return!1;for(var o=0;o<i.length;o++)if(!(td.js+ty(i[0]) in t))return!1;return i.map(tw)}return!1}}],tx=tk.filter(function(e){return e.supportedProperty}).map(function(e){return e.supportedProperty}),tE=tk.filter(function(e){return e.noPrefill}).reduce(function(e,t){var r;return e.push.apply(e,function(e){if(Array.isArray(e))return(0,tr.Z)(e)}(r=t.noPrefill)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||(0,tn.Z)(r)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),e},[]),tR={};if(v){o=document.createElement("p");var tS=window.getComputedStyle(document.documentElement,"");for(var tP in tS)isNaN(tP)||(tR[tS[tP]]=tS[tP]);tE.forEach(function(e){return delete tR[e]})}function tM(e,t){if(void 0===t&&(t={}),!o)return e;if(null!=tR[e])return tR[e];("transition"===e||"transform"===e)&&(t[e]=e in o.style);for(var r=0;r<tx.length&&(tR[e]=tx[r](e,o.style,t),!tR[e]);r++);try{o.style[e]=""}catch(e){return!1}return tR[e]}var tC={},tO={transition:1,"transition-property":1,"-webkit-transition":1,"-webkit-transition-property":1},tA=/(^\s*[\w-]+)|, (\s*[\w-]+)(?![^()]*\))/g;function tj(e,t,r){return"var"===t?"var":"all"===t?"all":"all"===r?", all":(t?tM(t):", "+tM(r))||t||r}function tN(e,t){var r=t;if(!s||"content"===e)return t;if("string"!=typeof r||!isNaN(parseInt(r,10)))return r;var n=e+r;if(null!=tC[n])return tC[n];try{s.style[e]=r}catch(e){return tC[n]=!1,!1}if(tO[e])r=r.replace(tA,tj);else if(""===s.style[e]&&("-ms-flex"===(r=td.css+r)&&(s.style[e]="-ms-flexbox"),s.style[e]=r,""===s.style[e]))return tC[n]=!1,!1;return s.style[e]="",tC[n]=r,tC[n]}v&&(s=document.createElement("p"));var tT=function(){var e=function(e,t){return e.length===t.length?e>t?1:-1:e.length-t.length};return{onProcessStyle:function(t,r){if("style"!==r.type)return t;for(var n={},i=Object.keys(t).sort(e),o=0;o<i.length;o++)n[i[o]]=t[i[o]];return n}}},tL=function(e){var t;return void 0===e&&(e={}),{plugins:[{onCreateRule:function(e,t,r){if("function"!=typeof t)return null;var n=M(e,{},r);return n[eA]=t,n},onProcessStyle:function(e,t){if(eO in t||eA in t)return e;var r={};for(var n in e){var i=e[n];"function"==typeof i&&(delete e[n],r[n]=i)}return t[eO]=r,e},onUpdate:function(e,t,r,n){var i=t[eA];i&&(t.style=i(e)||{});var o=t[eO];if(o)for(var s in o)t.prop(s,o[s](e),n)}},(t=e.observable,{onCreateRule:function(e,r,n){if(!eN(r))return null;var i=M(e,{},n);return r.subscribe(function(e){for(var r in e)i.prop(r,e[r],t)}),i},onProcessRule:function(e){if(!e||"style"===e.type){var r=e.style,n=function(n){var i=r[n];if(!eN(i))return"continue";delete r[n],i.subscribe({next:function(r){e.prop(n,r,t)}})};for(var i in r)if("continue"===n(i))continue}}}),{onProcessRule:eI},{onCreateRule:function(e,t,r){if(!e)return null;if(e===ez)return new e_(e,t,r);if("@"===e[0]&&e.substr(0,eF.length)===eF)return new eZ(e,t,r);var n=r.parent;return n&&("global"===n.type||n.options.parent&&"global"===n.options.parent.type)&&(r.scoped=!1),r.selector||!1!==r.scoped||(r.selector=e),null},onProcessRule:function(e,t){"style"===e.type&&t&&(function(e,t){var r=e.options,n=e.style,i=n?n[ez]:null;if(i){for(var o in i)t.addRule(o,i[o],(0,l.Z)({},r,{selector:e$(o,e.selector)}));delete n[ez]}}(e,t),function(e,t){var r=e.options,n=e.style;for(var i in n)if("@"===i[0]&&i.substr(0,ez.length)===ez){var o=e$(i.substr(ez.length),e.selector);t.addRule(o,n[i],(0,l.Z)({},r,{selector:o})),delete n[i]}}(e,t))}},{onProcessStyle:function(e,t,r){return"extend"in e?function e(t,r,n,i){return void 0===i&&(i={}),function(t,r,n,i){if("string"==typeof t.extend){if(!n)return;var o=n.getRule(t.extend);if(!o||o===r)return;var s=o.options.parent;s&&e(s.rules.raw[t.extend],r,n,i);return}if(Array.isArray(t.extend)){for(var a=0;a<t.extend.length;a++){var u=t.extend[a];e("string"==typeof u?(0,l.Z)({},t,{extend:u}):t.extend[a],r,n,i)}return}for(var c in t.extend){if("extend"===c){e(t.extend.extend,r,n,i);continue}if(eD(t.extend[c])){c in i||(i[c]={}),e(t.extend[c],r,n,i[c]);continue}i[c]=t.extend[c]}}(t,r,n,i),function(t,r,n,i){for(var o in t)if("extend"!==o){if(eD(i[o])&&eD(t[o])){e(t[o],r,n,i[o]);continue}if(eD(t[o])){i[o]=e(t[o],r,n);continue}i[o]=t[o]}}(t,r,n,i),i}(e,t,r):e},onChangeValue:function(e,t,r){if("extend"!==t)return e;if(null==e||!1===e){for(var n in r[eV])r.prop(n,null);return r[eV]=null,null}if("object"==typeof e){for(var i in e)r.prop(i,e[i]);r[eV]=e}return null}},{onProcessStyle:function(e,t,r){if("style"!==t.type)return e;var n,i,o=t.options.parent;for(var s in e){var a=-1!==s.indexOf("&"),u="@"===s[0];if(a||u){if(n=function(e,t,r){if(r)return(0,l.Z)({},r,{index:r.index+1});var n=e.options.nestingLevel;n=void 0===n?1:n+1;var i=(0,l.Z)({},e.options,{nestingLevel:n,index:t.indexOf(e)+1});return delete i.name,i}(t,o,n),a){var c=function(e,t){for(var r=t.split(eH),n=e.split(eH),i="",o=0;o<r.length;o++)for(var s=r[o],l=0;l<n.length;l++){var a=n[l];i&&(i+=", "),i+=-1!==a.indexOf("&")?a.replace(eU,s):s+" "+a}return i}(s,t.selector);i||(i=function(e,t){return function(r,n){var i=e.getRule(n)||t&&t.getRule(n);return i?i.selector:n}}(o,r)),c=c.replace(eW,i);var f=t.key+"-"+s;"replaceRule"in o?o.replaceRule(f,e[s],(0,l.Z)({},n,{selector:c})):o.addRule(f,e[s],(0,l.Z)({},n,{selector:c}))}else u&&o.addRule(s,{},n).addRule(t.key,e[s],{selector:t.selector});delete e[s]}}return e}},{onProcessStyle:function(e,t){return"composes"in e&&(function e(t,r){if(!r)return!0;if(Array.isArray(r)){for(var n=0;n<r.length;n++)if(!e(t,r[n]))return!1;return!0}if(r.indexOf(" ")>-1)return e(t,r.split(" "));var i=t.options.parent;if("$"===r[0]){var o=i.getRule(r.substr(1));return!!o&&o!==t&&(i.classes[t.key]+=" "+i.classes[o.key],!0)}return i.classes[t.key]+=" "+r,!0}(t,e.composes),delete e.composes),e}},{onProcessStyle:function(e){if(Array.isArray(e)){for(var t=0;t<e.length;t++)e[t]=eX(e[t]);return e}return eX(e)},onChangeValue:function(e,t,r){if(0===t.indexOf("--"))return e;var n=eK(t);return t===n?e:(r.prop(n,e),null)}},e4(e.defaultUnit),{onProcessStyle:function(e,t){if(!e||"style"!==t.type)return e;if(Array.isArray(e)){for(var r=0;r<e.length;r++)e[r]=tt(e[r],t);return e}return tt(e,t)}},{onProcessRule:function(e){if("keyframes"===e.type){var t;e.at="-"===(t=e.at)[1]||"ms"===td.js?t:"@"+td.css+"keyframes"+t.substr(10)}},onProcessStyle:function(e,t){return"style"!==t.type?e:function e(t){for(var r in t){var n=t[r];if("fallbacks"===r&&Array.isArray(n)){t[r]=n.map(e);continue}var i=!1,o=tM(r);o&&o!==r&&(i=!0);var s=!1,l=tN(o,O(n));l&&l!==n&&(s=!0),(i||s)&&(i&&delete t[r],t[o||r]=l||n)}return t}(e)},onChangeValue:function(e,t){return tN(t,O(e))||e}},tT()]}},tI=eS(tL()),tz=function(e){void 0===e&&(e=tI);var t,r=new Map,n=0,i=function(){return(!t||t.rules.index.length>1e4)&&(t=e.createStyleSheet().attach()),t};function o(){var e=arguments,t=JSON.stringify(e),o=r.get(t);if(o)return o.className;var s=[];for(var l in e){var a=e[l];if(!Array.isArray(a)){s.push(a);continue}for(var u=0;u<a.length;u++)s.push(a[u])}for(var c={},f=[],d=0;d<s.length;d++){var h=s[d];if(h){var p=h;if("string"==typeof h){var m=r.get(h);m&&(m.labels.length&&f.push.apply(f,m.labels),p=m.style)}p.label&&-1===f.indexOf(p.label)&&f.push(p.label),Object.assign(c,p)}}delete c.label;var y=(0===f.length?"css":f.join("-"))+"-"+n++;i().addRule(y,c);var g=i().classes[y],v={style:c,labels:f,className:g};return r.set(t,v),r.set(g,v),g}return o.getSheet=i,o}(),tF=Number.MIN_SAFE_INTEGER||-1e9,t_=(0,u.createContext)({classNamePrefix:"",disableStylesGeneration:!1,isSSR:!v}),tZ=new Map,tB=function(e,t){var r=e.managers;if(r)return r[t]||(r[t]=new eP),r[t];var n=tZ.get(t);return n||(n=new eP,tZ.set(t,n)),n},t$=function(e){var t=e.sheet,r=e.context,n=e.index,i=e.theme;t&&(tB(r,n).manage(i),r.registry&&r.registry.add(t))},tD=function(e){e.sheet&&tB(e.context,e.index).unmanage(e.theme)},tV=eS(tL()),tH=new WeakMap,tU=function(e){return tH.get(e)},tW=function(e,t){tH.set(e,t)},tQ=function(e){var t=e.styles;return"function"!=typeof t?t:t(e.theme)},tG=function(e){if(!e.context.disableStylesGeneration){var t,r,n,i,o=tB(e.context,e.index),s=o.get(e.theme);if(s)return s;var a=e.context.jss||tV,u=tQ(e),c=function e(t){var r=null;for(var n in t){var i=t[n],o=typeof i;if("function"===o)r||(r={}),r[n]=i;else if("object"===o&&null!==i&&!Array.isArray(i)){var s=e(i);s&&(r||(r={}),r[n]=s)}}return r}(u),f=a.createStyleSheet(u,(t=null!==c,e.context.id&&null!=e.context.id.minify&&(r=e.context.id.minify),n=e.context.classNamePrefix||"",e.name&&!r&&(n+=e.name.replace(/\s/g,"-")+"-"),i="",e.name&&(i=e.name+", "),i+="function"==typeof e.styles?"Themed":"Unthemed",(0,l.Z)({},e.sheetOptions,{index:e.index,meta:i,classNamePrefix:n,link:t,generateId:e.sheetOptions&&e.sheetOptions.generateId?e.sheetOptions.generateId:e.context.generateId})));return tW(f,{dynamicStyles:c,styles:u}),o.add(e.theme,f),f}},tY=function(e,t){for(var r in t)e.deleteRule(t[r])},tq=function(e,t,r){for(var n in r)t.updateOne(r[n],e)},tK=function(e,t){var r=tU(e);if(r){var n={};for(var i in r.dynamicStyles)for(var o=e.rules.index.length,s=e.addRule(i,r.dynamicStyles[i]),l=o;l<e.rules.index.length;l++){var a=e.rules.index[l];e.updateOne(a,t),n[s===a?i:a.key]=a}return n}},tX=function(e,t){if(!t)return e.classes;var r=tU(e);if(!r)return e.classes;var n={};for(var i in r.styles)n[i]=e.classes[i],i in t&&(n[i]+=" "+e.classes[t[i].key]);return n};function tJ(e){return e?u.useEffect:u.useInsertionEffect||u.useLayoutEffect}var t0={},t1=function(e,t){void 0===t&&(t={});var r=t,n=r.index,i=void 0===n?tF++:n,o=r.theming,s=r.name,l=a(r,["index","theming","name"]),c=o&&o.context||m,f={};return function(t){var r,n=(0,u.useRef)(!0),o=(0,u.useContext)(t_),a=(r=t&&t.theme,"function"==typeof e&&(r||(0,u.useContext)(c))||t0),d=(0,u.useMemo)(function(){var r=tG({context:o,styles:e,name:s,theme:a,index:i,sheetOptions:l});return r&&o.isSSR&&t$({index:i,context:o,sheet:r,theme:a}),[r,r?tK(r,t):null]},[o,a]),h=d[0],p=d[1];tJ(o.isSSR)(function(){h&&p&&!n.current&&tq(t,h,p)},[t]),tJ(o.isSSR)(function(){return h&&t$({index:i,context:o,sheet:h,theme:a}),function(){h&&(tD({index:i,context:o,sheet:h,theme:a}),p&&tY(h,p))}},[h]);var m=(0,u.useMemo)(function(){return h&&p?tX(h,p):f},[h,p]);return(0,u.useDebugValue)(m),(0,u.useDebugValue)(a===t0?"No theme":a),(0,u.useEffect)(function(){n.current=!1}),m}};Symbol("react-jss-styled"),void 0===i&&(i=tz)},66994:function(e,t,r){r.d(t,{Z:function(){return s}}),e=r.hmd(e),"undefined"!=typeof self?o=self:o="undefined"!=typeof window?window:void 0!==r.g?r.g:e;var n,i,o,s=("function"==typeof(i=o.Symbol)?i.observable?n=i.observable:(n=i("observable"),i.observable=n):n="@@observable",n)},96240:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}},1119:function(e,t,r){r.d(t,{Z:function(){return n}});function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}},73882:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(41154);function i(e){var t=function(e,t){if("object"!=(0,n.Z)(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=(0,n.Z)(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==(0,n.Z)(t)?t:t+""}},41154:function(e,t,r){r.d(t,{Z:function(){return n}});function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}},29062:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(96240);function i(e,t){if(e){if("string"==typeof e)return(0,n.Z)(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?(0,n.Z)(e,t):void 0}}},9270:function(e,t,r){r.d(t,{fC:function(){return R},z$:function(){return S}});var n=r(2265),i=r(98575),o=r(73966),s=r(6741),l=r(80886),a=r(6718),u=r(90420),c=r(71599),f=r(66840),d=r(57437),h="Checkbox",[p,m]=(0,o.b)(h),[y,g]=p(h),v=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:o,checked:a,defaultChecked:u,required:c,disabled:p,value:m="on",onCheckedChange:g,form:v,...b}=e,[w,R]=n.useState(null),S=(0,i.e)(t,e=>R(e)),P=n.useRef(!1),M=!w||v||!!w.closest("form"),[C,O]=(0,l.T)({prop:a,defaultProp:null!=u&&u,onChange:g,caller:h}),A=n.useRef(C);return n.useEffect(()=>{let e=null==w?void 0:w.form;if(e){let t=()=>O(A.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[w,O]),(0,d.jsxs)(y,{scope:r,state:C,disabled:p,children:[(0,d.jsx)(f.WV.button,{type:"button",role:"checkbox","aria-checked":x(C)?"mixed":C,"aria-required":c,"data-state":E(C),"data-disabled":p?"":void 0,disabled:p,value:m,...b,ref:S,onKeyDown:(0,s.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.M)(e.onClick,e=>{O(e=>!!x(e)||!e),M&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),M&&(0,d.jsx)(k,{control:w,bubbles:!P.current,name:o,value:m,checked:C,required:c,disabled:p,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!x(u)&&u})]})});v.displayName=h;var b="CheckboxIndicator",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...i}=e,o=g(b,r);return(0,d.jsx)(c.z,{present:n||x(o.state)||!0===o.state,children:(0,d.jsx)(f.WV.span,{"data-state":E(o.state),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,control:o,checked:s,bubbles:l=!0,defaultChecked:c,...h}=e,p=n.useRef(null),m=(0,i.e)(p,t),y=(0,a.D)(s),g=(0,u.t)(o);n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(y!==s&&t){let r=new Event("click",{bubbles:l});e.indeterminate=x(s),t.call(e,!x(s)&&s),e.dispatchEvent(r)}},[y,s,l]);let v=n.useRef(!x(s)&&s);return(0,d.jsx)(f.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=c?c:v.current,...h,tabIndex:-1,ref:m,style:{...h.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return"indeterminate"===e}function E(e){return x(e)?"indeterminate":e?"checked":"unchecked"}k.displayName="CheckboxBubbleInput";var R=v,S=w},35398:function(e,t,r){r.d(t,{Q:function(){return x}});var n,i,o,s=r(2265),l=Object.defineProperty,a=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,f=(e,t,r)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,d=(e,t)=>{for(var r in t||(t={}))u.call(t,r)&&f(e,r,t[r]);if(a)for(var r of a(t))c.call(t,r)&&f(e,r,t[r]);return e},h=(e,t)=>{var r={};for(var n in e)u.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&a)for(var n of a(e))0>t.indexOf(n)&&c.call(e,n)&&(r[n]=e[n]);return r};(e=>{let t=class t{constructor(e,r,n,o){if(this.version=e,this.errorCorrectionLevel=r,this.modules=[],this.isFunction=[],e<t.MIN_VERSION||e>t.MAX_VERSION)throw RangeError("Version value out of range");if(o<-1||o>7)throw RangeError("Mask value out of range");this.size=4*e+17;let s=[];for(let e=0;e<this.size;e++)s.push(!1);for(let e=0;e<this.size;e++)this.modules.push(s.slice()),this.isFunction.push(s.slice());this.drawFunctionPatterns();let l=this.addEccAndInterleave(n);if(this.drawCodewords(l),-1==o){let e=1e9;for(let t=0;t<8;t++){this.applyMask(t),this.drawFormatBits(t);let r=this.getPenaltyScore();r<e&&(o=t,e=r),this.applyMask(t)}}i(0<=o&&o<=7),this.mask=o,this.applyMask(o),this.drawFormatBits(o),this.isFunction=[]}static encodeText(r,n){let i=e.QrSegment.makeSegments(r);return t.encodeSegments(i,n)}static encodeBinary(r,n){let i=e.QrSegment.makeBytes(r);return t.encodeSegments([i],n)}static encodeSegments(e,n,o=1,l=40,a=-1,u=!0){let c,f;if(!(t.MIN_VERSION<=o&&o<=l&&l<=t.MAX_VERSION)||a<-1||a>7)throw RangeError("Invalid value");for(c=o;;c++){let r=8*t.getNumDataCodewords(c,n),i=s.getTotalBits(e,c);if(i<=r){f=i;break}if(c>=l)throw RangeError("Data too long")}for(let e of[t.Ecc.MEDIUM,t.Ecc.QUARTILE,t.Ecc.HIGH])u&&f<=8*t.getNumDataCodewords(c,e)&&(n=e);let d=[];for(let t of e)for(let e of(r(t.mode.modeBits,4,d),r(t.numChars,t.mode.numCharCountBits(c),d),t.getData()))d.push(e);i(d.length==f);let h=8*t.getNumDataCodewords(c,n);i(d.length<=h),r(0,Math.min(4,h-d.length),d),r(0,(8-d.length%8)%8,d),i(d.length%8==0);for(let e=236;d.length<h;e^=253)r(e,8,d);let p=[];for(;8*p.length<d.length;)p.push(0);return d.forEach((e,t)=>p[t>>>3]|=e<<7-(7&t)),new t(c,n,p,a)}getModule(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]}getModules(){return this.modules}drawFunctionPatterns(){for(let e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);let e=this.getAlignmentPatternPositions(),t=e.length;for(let r=0;r<t;r++)for(let n=0;n<t;n++)0==r&&0==n||0==r&&n==t-1||r==t-1&&0==n||this.drawAlignmentPattern(e[r],e[n]);this.drawFormatBits(0),this.drawVersion()}drawFormatBits(e){let t=this.errorCorrectionLevel.formatBits<<3|e,r=t;for(let e=0;e<10;e++)r=r<<1^(r>>>9)*1335;let o=(t<<10|r)^21522;i(o>>>15==0);for(let e=0;e<=5;e++)this.setFunctionModule(8,e,n(o,e));this.setFunctionModule(8,7,n(o,6)),this.setFunctionModule(8,8,n(o,7)),this.setFunctionModule(7,8,n(o,8));for(let e=9;e<15;e++)this.setFunctionModule(14-e,8,n(o,e));for(let e=0;e<8;e++)this.setFunctionModule(this.size-1-e,8,n(o,e));for(let e=8;e<15;e++)this.setFunctionModule(8,this.size-15+e,n(o,e));this.setFunctionModule(8,this.size-8,!0)}drawVersion(){if(this.version<7)return;let e=this.version;for(let t=0;t<12;t++)e=e<<1^(e>>>11)*7973;let t=this.version<<12|e;i(t>>>18==0);for(let e=0;e<18;e++){let r=n(t,e),i=this.size-11+e%3,o=Math.floor(e/3);this.setFunctionModule(i,o,r),this.setFunctionModule(o,i,r)}}drawFinderPattern(e,t){for(let r=-4;r<=4;r++)for(let n=-4;n<=4;n++){let i=Math.max(Math.abs(n),Math.abs(r)),o=e+n,s=t+r;0<=o&&o<this.size&&0<=s&&s<this.size&&this.setFunctionModule(o,s,2!=i&&4!=i)}}drawAlignmentPattern(e,t){for(let r=-2;r<=2;r++)for(let n=-2;n<=2;n++)this.setFunctionModule(e+n,t+r,1!=Math.max(Math.abs(n),Math.abs(r)))}setFunctionModule(e,t,r){this.modules[t][e]=r,this.isFunction[t][e]=!0}addEccAndInterleave(e){let r=this.version,n=this.errorCorrectionLevel;if(e.length!=t.getNumDataCodewords(r,n))throw RangeError("Invalid argument");let o=t.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][r],s=t.ECC_CODEWORDS_PER_BLOCK[n.ordinal][r],l=Math.floor(t.getNumRawDataModules(r)/8),a=o-l%o,u=Math.floor(l/o),c=[],f=t.reedSolomonComputeDivisor(s);for(let r=0,n=0;r<o;r++){let i=e.slice(n,n+u-s+(r<a?0:1));n+=i.length;let o=t.reedSolomonComputeRemainder(i,f);r<a&&i.push(0),c.push(i.concat(o))}let d=[];for(let e=0;e<c[0].length;e++)c.forEach((t,r)=>{(e!=u-s||r>=a)&&d.push(t[e])});return i(d.length==l),d}drawCodewords(e){if(e.length!=Math.floor(t.getNumRawDataModules(this.version)/8))throw RangeError("Invalid argument");let r=0;for(let t=this.size-1;t>=1;t-=2){6==t&&(t=5);for(let i=0;i<this.size;i++)for(let o=0;o<2;o++){let s=t-o,l=(t+1&2)==0?this.size-1-i:i;!this.isFunction[l][s]&&r<8*e.length&&(this.modules[l][s]=n(e[r>>>3],7-(7&r)),r++)}}i(r==8*e.length)}applyMask(e){if(e<0||e>7)throw RangeError("Mask value out of range");for(let t=0;t<this.size;t++)for(let r=0;r<this.size;r++){let n;switch(e){case 0:n=(r+t)%2==0;break;case 1:n=t%2==0;break;case 2:n=r%3==0;break;case 3:n=(r+t)%3==0;break;case 4:n=(Math.floor(r/3)+Math.floor(t/2))%2==0;break;case 5:n=r*t%2+r*t%3==0;break;case 6:n=(r*t%2+r*t%3)%2==0;break;case 7:n=((r+t)%2+r*t%3)%2==0;break;default:throw Error("Unreachable")}!this.isFunction[t][r]&&n&&(this.modules[t][r]=!this.modules[t][r])}}getPenaltyScore(){let e=0;for(let r=0;r<this.size;r++){let n=!1,i=0,o=[0,0,0,0,0,0,0];for(let s=0;s<this.size;s++)this.modules[r][s]==n?5==++i?e+=t.PENALTY_N1:i>5&&e++:(this.finderPenaltyAddHistory(i,o),n||(e+=this.finderPenaltyCountPatterns(o)*t.PENALTY_N3),n=this.modules[r][s],i=1);e+=this.finderPenaltyTerminateAndCount(n,i,o)*t.PENALTY_N3}for(let r=0;r<this.size;r++){let n=!1,i=0,o=[0,0,0,0,0,0,0];for(let s=0;s<this.size;s++)this.modules[s][r]==n?5==++i?e+=t.PENALTY_N1:i>5&&e++:(this.finderPenaltyAddHistory(i,o),n||(e+=this.finderPenaltyCountPatterns(o)*t.PENALTY_N3),n=this.modules[s][r],i=1);e+=this.finderPenaltyTerminateAndCount(n,i,o)*t.PENALTY_N3}for(let r=0;r<this.size-1;r++)for(let n=0;n<this.size-1;n++){let i=this.modules[r][n];i==this.modules[r][n+1]&&i==this.modules[r+1][n]&&i==this.modules[r+1][n+1]&&(e+=t.PENALTY_N2)}let r=0;for(let e of this.modules)r=e.reduce((e,t)=>e+(t?1:0),r);let n=this.size*this.size,o=Math.ceil(Math.abs(20*r-10*n)/n)-1;return i(0<=o&&o<=9),i(0<=(e+=o*t.PENALTY_N4)&&e<=2568888),e}getAlignmentPatternPositions(){if(1==this.version)return[];{let e=Math.floor(this.version/7)+2,t=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*e-2)),r=[6];for(let n=this.size-7;r.length<e;n-=t)r.splice(1,0,n);return r}}static getNumRawDataModules(e){if(e<t.MIN_VERSION||e>t.MAX_VERSION)throw RangeError("Version number out of range");let r=(16*e+128)*e+64;if(e>=2){let t=Math.floor(e/7)+2;r-=(25*t-10)*t-55,e>=7&&(r-=36)}return i(208<=r&&r<=29648),r}static getNumDataCodewords(e,r){return Math.floor(t.getNumRawDataModules(e)/8)-t.ECC_CODEWORDS_PER_BLOCK[r.ordinal][e]*t.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][e]}static reedSolomonComputeDivisor(e){if(e<1||e>255)throw RangeError("Degree out of range");let r=[];for(let t=0;t<e-1;t++)r.push(0);r.push(1);let n=1;for(let i=0;i<e;i++){for(let e=0;e<r.length;e++)r[e]=t.reedSolomonMultiply(r[e],n),e+1<r.length&&(r[e]^=r[e+1]);n=t.reedSolomonMultiply(n,2)}return r}static reedSolomonComputeRemainder(e,r){let n=r.map(e=>0);for(let i of e){let e=i^n.shift();n.push(0),r.forEach((r,i)=>n[i]^=t.reedSolomonMultiply(r,e))}return n}static reedSolomonMultiply(e,t){if(e>>>8!=0||t>>>8!=0)throw RangeError("Byte out of range");let r=0;for(let n=7;n>=0;n--)r=r<<1^(r>>>7)*285^(t>>>n&1)*e;return i(r>>>8==0),r}finderPenaltyCountPatterns(e){let t=e[1];i(t<=3*this.size);let r=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(r&&e[0]>=4*t&&e[6]>=t?1:0)+(r&&e[6]>=4*t&&e[0]>=t?1:0)}finderPenaltyTerminateAndCount(e,t,r){return e&&(this.finderPenaltyAddHistory(t,r),t=0),t+=this.size,this.finderPenaltyAddHistory(t,r),this.finderPenaltyCountPatterns(r)}finderPenaltyAddHistory(e,t){0==t[0]&&(e+=this.size),t.pop(),t.unshift(e)}};function r(e,t,r){if(t<0||t>31||e>>>t!=0)throw RangeError("Value out of range");for(let n=t-1;n>=0;n--)r.push(e>>>n&1)}function n(e,t){return(e>>>t&1)!=0}function i(e){if(!e)throw Error("Assertion error")}t.MIN_VERSION=1,t.MAX_VERSION=40,t.PENALTY_N1=3,t.PENALTY_N2=3,t.PENALTY_N3=40,t.PENALTY_N4=10,t.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],t.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],e.QrCode=t;let o=class e{constructor(e,t,r){if(this.mode=e,this.numChars=t,this.bitData=r,t<0)throw RangeError("Invalid argument");this.bitData=r.slice()}static makeBytes(t){let n=[];for(let e of t)r(e,8,n);return new e(e.Mode.BYTE,t.length,n)}static makeNumeric(t){if(!e.isNumeric(t))throw RangeError("String contains non-numeric characters");let n=[];for(let e=0;e<t.length;){let i=Math.min(t.length-e,3);r(parseInt(t.substring(e,e+i),10),3*i+1,n),e+=i}return new e(e.Mode.NUMERIC,t.length,n)}static makeAlphanumeric(t){let n;if(!e.isAlphanumeric(t))throw RangeError("String contains unencodable characters in alphanumeric mode");let i=[];for(n=0;n+2<=t.length;n+=2){let o=45*e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n));r(o+=e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n+1)),11,i)}return n<t.length&&r(e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n)),6,i),new e(e.Mode.ALPHANUMERIC,t.length,i)}static makeSegments(t){return""==t?[]:e.isNumeric(t)?[e.makeNumeric(t)]:e.isAlphanumeric(t)?[e.makeAlphanumeric(t)]:[e.makeBytes(e.toUtf8ByteArray(t))]}static makeEci(t){let n=[];if(t<0)throw RangeError("ECI assignment value out of range");if(t<128)r(t,8,n);else if(t<16384)r(2,2,n),r(t,14,n);else if(t<1e6)r(6,3,n),r(t,21,n);else throw RangeError("ECI assignment value out of range");return new e(e.Mode.ECI,0,n)}static isNumeric(t){return e.NUMERIC_REGEX.test(t)}static isAlphanumeric(t){return e.ALPHANUMERIC_REGEX.test(t)}getData(){return this.bitData.slice()}static getTotalBits(e,t){let r=0;for(let n of e){let e=n.mode.numCharCountBits(t);if(n.numChars>=1<<e)return 1/0;r+=4+e+n.bitData.length}return r}static toUtf8ByteArray(e){e=encodeURI(e);let t=[];for(let r=0;r<e.length;r++)"%"!=e.charAt(r)?t.push(e.charCodeAt(r)):(t.push(parseInt(e.substring(r+1,r+3),16)),r+=2);return t}};o.NUMERIC_REGEX=/^[0-9]*$/,o.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,o.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:";let s=o;e.QrSegment=o})(o||(o={})),(e=>{let t=class{constructor(e,t){this.ordinal=e,this.formatBits=t}};t.LOW=new t(0,1),t.MEDIUM=new t(1,0),t.QUARTILE=new t(2,3),t.HIGH=new t(3,2),e.Ecc=t})((n=o||(o={})).QrCode||(n.QrCode={})),(e=>{let t=class{constructor(e,t){this.modeBits=e,this.numBitsCharCount=t}numCharCountBits(e){return this.numBitsCharCount[Math.floor((e+7)/17)]}};t.NUMERIC=new t(1,[10,12,14]),t.ALPHANUMERIC=new t(2,[9,11,13]),t.BYTE=new t(4,[8,16,16]),t.KANJI=new t(8,[8,10,12]),t.ECI=new t(7,[0,0,0]),e.Mode=t})((i=o||(o={})).QrSegment||(i.QrSegment={}));var p=o,m={L:p.QrCode.Ecc.LOW,M:p.QrCode.Ecc.MEDIUM,Q:p.QrCode.Ecc.QUARTILE,H:p.QrCode.Ecc.HIGH},y="#FFFFFF",g="#000000";function v(e,t=0){let r=[];return e.forEach(function(e,n){let i=null;e.forEach(function(o,s){if(!o&&null!==i){r.push(`M${i+t} ${n+t}h${s-i}v1H${i+t}z`),i=null;return}if(s===e.length-1){if(!o)return;null===i?r.push(`M${s+t},${n+t} h1v1H${s+t}z`):r.push(`M${i+t},${n+t} h${s+1-i}v1H${i+t}z`);return}o&&null===i&&(i=s)})}),r.join("")}function b(e,t){return e.slice().map((e,r)=>r<t.y||r>=t.y+t.h?e:e.map((e,r)=>(r<t.x||r>=t.x+t.w)&&e))}function w({value:e,level:t,minVersion:r,includeMargin:n,marginSize:i,imageSettings:o,size:l,boostLevel:a}){let u=s.useMemo(()=>{let n=(Array.isArray(e)?e:[e]).reduce((e,t)=>(e.push(...p.QrSegment.makeSegments(t)),e),[]);return p.QrCode.encodeSegments(n,m[t],r,void 0,void 0,a)},[e,t,r,a]),{cells:c,margin:f,numCells:d,calculatedImageSettings:h}=s.useMemo(()=>{let e=u.getModules(),t=null!=i?Math.max(Math.floor(i),0):n?4:0,r=e.length+2*t,s=function(e,t,r,n){if(null==n)return null;let i=e.length+2*r,o=Math.floor(.1*t),s=i/t,l=(n.width||o)*s,a=(n.height||o)*s,u=null==n.x?e.length/2-l/2:n.x*s,c=null==n.y?e.length/2-a/2:n.y*s,f=null==n.opacity?1:n.opacity,d=null;if(n.excavate){let e=Math.floor(u),t=Math.floor(c);d={x:e,y:t,w:Math.ceil(l+u-e),h:Math.ceil(a+c-t)}}return{x:u,y:c,h:a,w:l,excavation:d,opacity:f,crossOrigin:n.crossOrigin}}(e,l,t,o);return{cells:e,margin:t,numCells:r,calculatedImageSettings:s}},[u,l,o,n,i]);return{qrcode:u,margin:f,cells:c,numCells:d,calculatedImageSettings:h}}var k=function(){try{new Path2D().addPath(new Path2D)}catch(e){return!1}return!0}(),x=s.forwardRef(function(e,t){let{value:r,size:n=128,level:i="L",bgColor:o=y,fgColor:l=g,includeMargin:a=!1,minVersion:u=1,boostLevel:c,marginSize:f,imageSettings:p}=e,m=h(e,["value","size","level","bgColor","fgColor","includeMargin","minVersion","boostLevel","marginSize","imageSettings"]),{style:x}=m,E=h(m,["style"]),R=null==p?void 0:p.src,S=s.useRef(null),P=s.useRef(null),M=s.useCallback(e=>{S.current=e,"function"==typeof t?t(e):t&&(t.current=e)},[t]),[C,O]=s.useState(!1),{margin:A,cells:j,numCells:N,calculatedImageSettings:T}=w({value:r,level:i,minVersion:u,boostLevel:c,includeMargin:a,marginSize:f,imageSettings:p,size:n});s.useEffect(()=>{if(null!=S.current){let e=S.current,t=e.getContext("2d");if(!t)return;let r=j,i=P.current,s=null!=T&&null!==i&&i.complete&&0!==i.naturalHeight&&0!==i.naturalWidth;s&&null!=T.excavation&&(r=b(j,T.excavation));let a=window.devicePixelRatio||1;e.height=e.width=n*a;let u=n/N*a;t.scale(u,u),t.fillStyle=o,t.fillRect(0,0,N,N),t.fillStyle=l,k?t.fill(new Path2D(v(r,A))):j.forEach(function(e,r){e.forEach(function(e,n){e&&t.fillRect(n+A,r+A,1,1)})}),T&&(t.globalAlpha=T.opacity),s&&t.drawImage(i,T.x+A,T.y+A,T.w,T.h)}}),s.useEffect(()=>{O(!1)},[R]);let L=d({height:n,width:n},x),I=null;return null!=R&&(I=s.createElement("img",{src:R,key:R,style:{display:"none"},onLoad:()=>{O(!0)},ref:P,crossOrigin:null==T?void 0:T.crossOrigin})),s.createElement(s.Fragment,null,s.createElement("canvas",d({style:L,height:n,width:n,ref:M,role:"img"},E)),I)});x.displayName="QRCodeCanvas",s.forwardRef(function(e,t){let{value:r,size:n=128,level:i="L",bgColor:o=y,fgColor:l=g,includeMargin:a=!1,minVersion:u=1,boostLevel:c,title:f,marginSize:p,imageSettings:m}=e,k=h(e,["value","size","level","bgColor","fgColor","includeMargin","minVersion","boostLevel","title","marginSize","imageSettings"]),{margin:x,cells:E,numCells:R,calculatedImageSettings:S}=w({value:r,level:i,minVersion:u,boostLevel:c,includeMargin:a,marginSize:p,imageSettings:m,size:n}),P=E,M=null;null!=m&&null!=S&&(null!=S.excavation&&(P=b(E,S.excavation)),M=s.createElement("image",{href:m.src,height:S.h,width:S.w,x:S.x+x,y:S.y+x,preserveAspectRatio:"none",opacity:S.opacity,crossOrigin:S.crossOrigin}));let C=v(P,x);return s.createElement("svg",d({height:n,width:n,viewBox:`0 0 ${R} ${R}`,ref:t,role:"img"},k),!!f&&s.createElement("title",null,f),s.createElement("path",{fill:o,d:`M0,0 h${R}v${R}H0z`,shapeRendering:"crispEdges"}),s.createElement("path",{fill:l,d:C,shapeRendering:"crispEdges"}),M)}).displayName="QRCodeSVG"}}]);