"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[52359],{52359:function(e,t,n){n.r(t),n.d(t,{default:function(){return es}});var s=n(57437),i=n(41709),l=n(27300),a=n(70880),r=n(15681),o=n(79981),d=n(97751);async function c(e){try{let t=await o.Z.post("/deposit-requests/create",e);return(0,d.B)(t)}catch(e){return(0,d.D)(e)}}async function u(e){try{let t=await o.Z.post("/deposits/create",e);return(0,d.B)(t)}catch(e){return(0,d.D)(e)}}var m=n(3612),h=n(13590),v=n(99376),x=n(2265),f=n(29501),p=n(43949),g=n(14438),j=n(31229),y=n(85487),w=n(33145);function N(e){let{res:t}=e,{t:n}=(0,p.$G)(),i=e=>{if(!e)return;let t=document.createElement("form");t.method="POST",t.action="https://perfectmoney.is/api/step1.asp",Object.entries(e).forEach(e=>{let[n,s]=e;if(s){let e=document.createElement("input");e.type="hidden",e.name=n,e.value=s,t.appendChild(e)}}),document.body.appendChild(t),t.submit()};return x.useEffect(()=>{t.postData&&i(t.postData)},[t]),(0,s.jsxs)("div",{className:"md:px-auto flex flex-col items-center justify-center px-4 md:py-10",children:[(0,s.jsx)(w.default,{src:"/phone.svg",alt:"Phone",width:168,height:168,priority:!0,quality:70}),(0,s.jsxs)("h3",{className:"my-4 flex flex-col items-center text-center",children:[n("Request is processing..."),(0,s.jsx)(y.Loader,{title:n("Please wait"),className:"mt-2.5"})]})]})}var b=n(55988);function C(e){let{res:t}=e,n=(0,v.useRouter)();return(0,b.EQ)(t).with({type:"redirect"},()=>(n.push(t.redirect),null)).with({type:"post",data:{method:"perfectmoney"}},()=>(0,s.jsx)(N,{res:t})).otherwise(()=>null)}var A=n(45932),Z=n(62869),S=n(95186),k=n(22291);function I(e){let{form:t,onNext:n}=e,{t:i}=(0,p.$G)();return(0,s.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsx)("h2",{children:i("Select wallet")}),(0,s.jsx)(r.Wi,{name:"wallet",control:t.control,render:e=>{let{field:t}=e;return(0,s.jsxs)(r.xJ,{className:"w-full",children:[(0,s.jsx)(r.NI,{children:(0,s.jsx)(A.R,{...t})}),(0,s.jsx)(r.zG,{})]})}})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsx)("h2",{children:i("How much?")}),(0,s.jsx)(r.Wi,{name:"amount",control:t.control,render:e=>{let{field:t}=e;return(0,s.jsxs)(r.xJ,{className:"w-full",children:[(0,s.jsx)(r.NI,{children:(0,s.jsx)(S.I,{type:"number",placeholder:i("Enter deposit amount"),...t,onKeyDown:e=>{"enter"===e.key&&(e.preventDefault(),e.stopPropagation(),n())}})}),(0,s.jsx)(r.zG,{})]})}})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(Z.z,{type:"submit",onClick:()=>{let e=0;t.getValues("wallet")||(t.setError("wallet",{message:"Please select a wallet.",type:"custom"},{shouldFocus:!0}),e+=1),t.getValues("amount")||(t.setError("amount",{message:"Amount is required.",type:"custom"},{shouldFocus:!0}),e+=1),e||n()},className:"min-w-48",children:[(0,s.jsx)("span",{children:i("Next")}),(0,s.jsx)(k.Z,{size:16})]})})]})}var z=n(52323);function P(){return(0,s.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 80 80",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{opacity:"0.4",d:"M60 62.8665H57.4666C54.8 62.8665 52.2667 63.8999 50.4 65.7665L44.6999 71.3999C42.0999 73.9665 37.8667 73.9665 35.2667 71.3999L29.5667 65.7665C27.7 63.8999 25.1333 62.8665 22.5 62.8665H20C14.4667 62.8665 10 58.4333 10 52.9666V16.5999C10 11.1332 14.4667 6.69995 20 6.69995H60C65.5333 6.69995 70 11.1332 70 16.5999V52.9666C70 58.4 65.5333 62.8665 60 62.8665Z",fill:"#01A79E"}),(0,s.jsx)("path",{d:"M40.0001 34.7C44.2895 34.7 47.7668 31.2229 47.7668 26.9335C47.7668 22.6441 44.2895 19.1667 40.0001 19.1667C35.7107 19.1667 32.2334 22.6441 32.2334 26.9335C32.2334 31.2229 35.7107 34.7 40.0001 34.7Z",fill:"#01A79E"}),(0,s.jsx)("path",{d:"M48.9333 50.2002C51.6333 50.2002 53.2 47.2002 51.7 44.9668C49.4334 41.6002 45.0333 39.3335 40 39.3335C34.9667 39.3335 30.5666 41.6002 28.3 44.9668C26.8 47.2002 28.3667 50.2002 31.0667 50.2002H48.9333Z",fill:"#01A79E"})]})}var E=n(41062),F=n(26815),D=n(74991),R=n(6512),V=n(40593),L=n(31117),J=n(52789),M=n(19571),B=n(58926),G=n(90433),O=n(85539),T=n(85323),q=n(16831),$=n(23518),W=n(57054),Y=n(94508),U=n(59532),_=n(36887);function K(e){var t,n,i,l,a,r,o;let{onNext:d,form:c,agent:u}=e,{t:m}=(0,p.$G)(),[h,v]=(0,x.useState)(!1),[f,g]=(0,x.useState)(),j=null===(n=c.getFieldState("method"))||void 0===n?void 0:null===(t=n.error)||void 0===t?void 0:t.message;return x.useEffect(()=>{c.getValues("method")&&c.clearErrors("method")},[c.getValues("method")]),(0,s.jsxs)("div",{className:"space-y-6 rounded-xl px-6 py-4 shadow-default",children:[(0,s.jsxs)("div",{className:"flex flex-col justify-between md:flex-row md:items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(q.qE,{children:[(0,s.jsx)(q.F$,{src:null==u?void 0:u.profileImage}),(0,s.jsxs)(q.Q5,{children:[" ",(0,U.v)(null==u?void 0:u.name)," "]})]}),(0,s.jsx)("p",{className:"text-sm font-bold",children:null==u?void 0:u.name})]}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-6",children:[(0,s.jsxs)("div",{className:"w-full md:w-auto",children:[(0,s.jsx)("span",{className:"mb-1 text-[10px]",children:m("Phone number")}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(w.default,{src:"https://flagcdn.com/".concat(null==u?void 0:null===(l=u.address)||void 0===l?void 0:null===(i=l.countryCode)||void 0===i?void 0:i.toLowerCase(),".svg"),alt:m("france flag"),width:64,height:64,className:"h-4 w-6"}),(0,s.jsx)("p",{className:"text-sm leading-6",children:null==u?void 0:null===(r=u.user)||void 0===r?void 0:null===(a=r.customer)||void 0===a?void 0:a.phone})]})]}),(0,s.jsxs)("div",{className:"flex-1 md:flex-auto",children:[(0,s.jsx)("span",{className:"mb-1 text-[10px]",children:m("Agent ID")}),(0,s.jsx)("p",{className:"text-sm leading-6",children:null==u?void 0:u.agentId})]}),(0,s.jsxs)("div",{className:"flex-1 md:flex-auto",children:[(0,s.jsx)("span",{className:"mb-1 text-[10px]",children:m("Charges")}),(0,s.jsx)("p",{className:"text-sm leading-6",children:(null==u?void 0:u.depositCharge)?"".concat(null==u?void 0:u.depositCharge,"%"):m("Free")})]}),(0,s.jsxs)("div",{className:"flex-1 md:flex-auto",children:[(0,s.jsx)("span",{className:"mb-1 text-[10px]",children:m("Processing time")}),(0,s.jsxs)("p",{className:"text-sm leading-6",children:[null==u?void 0:u.processingTime," ",m("Hours")]})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col justify-end gap-2 sm:flex-row",children:[(0,s.jsxs)("div",{className:"flex w-full flex-col",children:[(0,s.jsxs)(W.J2,{open:h,onOpenChange:v,children:[(0,s.jsxs)(W.xo,{className:"flex h-10 w-full items-center justify-between gap-2 rounded bg-secondary px-4 text-sm font-medium text-secondary-foreground transition duration-300 ease-in-out hover:bg-accent",children:[f?(0,s.jsxs)("span",{children:[" ",(0,Y.fl)(null==f?void 0:f.name)," "]}):(0,s.jsx)("span",{children:m("Select a method")}),(0,s.jsx)(_.Z,{size:16})]}),(0,s.jsx)(W.yk,{className:"w-[var(--radix-popover-trigger-width)] p-0",children:(0,s.jsx)($.mY,{children:(0,s.jsxs)($.e8,{children:[(0,s.jsx)($.rb,{}),(0,s.jsx)($.fu,{children:(null==u?void 0:u.agentMethods)?null==u?void 0:null===(o=u.agentMethods)||void 0===o?void 0:o.map(e=>(0,s.jsxs)($.di,{onSelect:()=>{var t,n;g(e),c.setValue("method",null==e?void 0:null===(t=e.name)||void 0===t?void 0:t.toString()),c.setValue("agent",null==u?void 0:null===(n=u.agentId)||void 0===n?void 0:n.toString()),v(!1)},disabled:!e.active,children:[(null==f?void 0:f.id)===(null==e?void 0:e.id)&&(0,s.jsx)(k.Z,{variant:"Bold"}),(0,Y.fl)(null==e?void 0:e.name)]},e.id)):(0,s.jsx)("span",{children:m("No available methods")})})]})})})]}),j?(0,s.jsx)("span",{className:"mt-2 block text-sm text-destructive",children:j}):null]}),(0,s.jsxs)(Z.z,{type:"button",onClick:d,className:"min-w-48",children:[(0,s.jsx)("span",{children:m("Next")}),(0,s.jsx)(k.Z,{size:16})]})]})]})}function Q(e){var t,n,l;let{countryCode:a,currencyCode:r,form:d,onNext:c}=e,{t:u}=(0,p.$G)(),{data:m,isLoading:h}=(0,T.ZP)("/agents/deposit?countryCode=".concat(a.toUpperCase(),"&currencyCode=").concat(r),e=>o.Z.get(e));return(0,s.jsxs)("div",{className:"mb-10 flex flex-col gap-4 px-2 md:mb-0",children:[(0,s.jsxs)("div",{className:"mb-4 flex flex-col flex-wrap gap-2 sm:flex-row sm:items-center sm:justify-between",children:[(0,s.jsx)("h2",{className:"whitespace-nowrap text-2xl font-semibold text-foreground",children:u("Select agent")}),(0,s.jsx)(O.R,{placeholder:u("Search agent"),iconPlacement:"end"})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsx)(i.J,{condition:h,children:(0,s.jsx)(y.Loader,{})}),(0,s.jsx)(i.J,{condition:!h&&(null==m?void 0:null===(t=m.data)||void 0===t?void 0:t.length)===0,children:(0,s.jsx)("p",{className:"text-sm text-secondary-text",children:u("Empty data")})}),(0,s.jsx)(i.J,{condition:(null==m?void 0:null===(n=m.data)||void 0===n?void 0:n.length)>0,children:null==m?void 0:null===(l=m.data)||void 0===l?void 0:l.map(e=>(0,s.jsx)(K,{agent:e,form:d,onNext:c},null==e?void 0:e.id))})]})]})}function H(e){var t;let{form:n,changeCountry:l,onBack:a,onNext:o}=e,{t:d}=(0,p.$G)(),{getCountryByCode:c}=(0,V.F)(),u=n.getValues("country"),[m,h]=(0,x.useState)(),[v,f]=(0,x.useState)(!1),{data:g,isLoading:j}=(0,L.d)("/gateways?currency=".concat(n.getValues("wallet"),"&country=").concat(u));return(0,x.useEffect)(()=>{u&&c(u,h)},[u]),(0,s.jsxs)("div",{className:"flex flex-col gap-y-4 pt-6 md:gap-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-4 px-2 sm:px-0",children:[(0,s.jsx)("h2",{children:d("Select your preferred method")}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[m&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(E.W,{countryCode:null==m?void 0:null===(t=m.code)||void 0===t?void 0:t.cca2,className:"h-5 w-8 rounded-sm"}),(0,s.jsx)("span",{children:null==m?void 0:m.name})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(M.Z,{variant:"Bold",size:"16",className:"text-primary"}),(0,s.jsx)("span",{children:d("Selected")})]}),(0,s.jsxs)(Z.z,{variant:"link",type:"button",onClick:l,className:"h-fit w-fit gap-1 px-0 py-0 text-sm font-semibold text-secondary-text sm:ml-auto",children:[(0,s.jsx)("span",{children:d("Change country")}),(0,s.jsx)(B.Z,{size:16})]})]}),(0,s.jsx)(r.Wi,{control:n.control,name:"method",render:e=>{var t,l;let{field:a}=e;return(0,s.jsx)(r.xJ,{children:(0,s.jsx)(r.NI,{children:(0,s.jsxs)(D.E,{onValueChange:e=>{"agent"===e?(a.onChange(""),n.setValue("isAgent",!0),f(!0)):(a.onChange(e),n.setValue("isAgent",!1),f(!1))},className:"grid ".concat(j?"grid-cols-1":"grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3"),children:[(0,s.jsx)(i.J,{condition:j,children:(0,s.jsx)("div",{className:"flex justify-center py-10",children:(0,s.jsx)(y.Loader,{})})}),(0,s.jsxs)(i.J,{condition:!j,children:[(0,s.jsxs)(F.Z,{"data-active":""===a.value&&v,className:"relative col-span-1 flex cursor-pointer items-center gap-2 rounded-xl border-[3px] border-transparent bg-muted p-4 text-sm font-bold transition-all duration-300 ease-linear hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",htmlFor:"agent",children:[(0,s.jsx)(D.m,{value:"agent",id:"agent",className:"absolute left-0 top-0 opacity-0"}),(0,s.jsx)(P,{}),(0,s.jsx)("span",{children:d("By Agent")})]}),null==g?void 0:null===(l=g.data)||void 0===l?void 0:null===(t=l.map(e=>new J.M(e)))||void 0===t?void 0:t.map(e=>(0,s.jsxs)(F.Z,{"data-active":a.value===e.value,className:"relative col-span-1 flex cursor-pointer items-center gap-2 rounded-xl border-[3px] border-transparent bg-muted p-4 text-sm font-bold transition-all duration-300 ease-linear hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",htmlFor:"".concat(e.value,"-").concat(e.id),children:[(0,s.jsx)(D.m,{id:"".concat(e.value,"-").concat(e.id),value:e.value,className:"absolute left-0 top-0 opacity-0"}),e.logoImage?(0,s.jsx)(w.default,{src:e.logoImage,alt:e.name,width:32,height:32,className:"h-8 w-8 rounded-md bg-background object-contain"}):null,(0,s.jsx)("span",{children:e.name.replace(/\//g," / ")})]},e.id))]})]})})})}})]}),(0,s.jsx)(R.Z,{className:"mb-1 mt-[5px] bg-divider-secondary"}),(0,s.jsx)(i.J,{condition:v,children:(0,s.jsx)(Q,{countryCode:u,currencyCode:n.getValues("wallet"),form:n,onNext:o})}),(0,s.jsx)(i.J,{condition:"agent"!==n.watch("method")&&!v,children:(0,s.jsxs)("div",{className:"mt-8 flex justify-end gap-4",children:[(0,s.jsxs)(Z.z,{variant:"outline",onClick:a,type:"button",children:[(0,s.jsx)(G.Z,{size:16}),(0,s.jsx)("span",{children:d("Back")})]}),(0,s.jsxs)(Z.z,{type:"submit",onClick:o,className:"min-w-48",children:[(0,s.jsx)("span",{children:d("Next")}),(0,s.jsx)(k.Z,{size:16})]})]})})]})}function X(e){let{form:t,updateTab:n,onBack:l}=e,{t:a}=(0,p.$G)(),[o,d]=x.useState("details");return(0,x.useEffect)(()=>{t.getValues("country")&&d("agentSelection")},[t]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.J,{condition:"details"===o,children:(0,s.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsx)("h2",{children:a("Country of deposit")}),(0,s.jsx)(r.Wi,{name:"country",control:t.control,render:e=>{let{field:t}=e;return(0,s.jsxs)(r.xJ,{className:"w-full",children:[(0,s.jsx)(r.NI,{children:(0,s.jsx)(z.g,{defaultCountry:t.value,onSelectChange:e=>{var n;t.onChange(null==e?void 0:null===(n=e.code)||void 0===n?void 0:n.cca2)}})}),(0,s.jsx)(r.zG,{})]})}})]}),(0,s.jsx)("div",{className:"mt-8 flex justify-end",children:(0,s.jsxs)(Z.z,{type:"submit",onClick:()=>{t.getValues("country")?d("agentSelection"):t.setError("country",{message:"Select a country to continue.",type:"required"})},className:"min-w-48",children:[(0,s.jsx)("span",{children:a("Next")}),(0,s.jsx)(k.Z,{size:16})]})})]})}),(0,s.jsx)(i.J,{condition:"agentSelection"===o,children:(0,s.jsx)(H,{form:t,changeCountry:()=>d("details"),onBack:l,onNext:n})})]})}var ee=n(25318);function et(e){var t,n,l,a,r,o,d,c,u,m,h,v,f;let{onBack:g,onNext:j,isLoading:N,formData:b}=e,{t:C}=(0,p.$G)(),{getCountryByCode:A}=(0,V.F)(),[S,I]=x.useState(),{data:z}=(0,L.d)(b.isAgent?"/agents/deposit?countryCode=".concat(b.country,"&currencyCode=").concat(b.wallet):""),{data:P}=(0,L.d)("/deposits/preview/create?amount=".concat(b.amount)),{data:F}=(0,L.d)((null==b?void 0:b.isAgent)||!b.method?"":"/gateways?currency=".concat(b.wallet,"&country=").concat(b.country));x.useEffect(()=>{A(b.country,I)},[b.country]);let D=((e,t,n)=>{if(!t)return null;let s=e.find(e=>e.agentId===t);return{...s,method:s.agentMethods.find(e=>e.name===n)}})(null==z?void 0:z.data,null==b?void 0:b.agent,null==b?void 0:b.method),J=(v=null==F?void 0:F.data,(f=null==b?void 0:b.method)?null==v?void 0:v.find(e=>e.value===f):null),O=new Y.F(null==b?void 0:b.wallet);return(0,s.jsxs)("div",{className:"flex flex-col gap-y-4 md:gap-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsx)("h2",{children:C("Select your preferred method")}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 py-2",children:[S&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(E.W,{countryCode:null==S?void 0:null===(t=S.code)||void 0===t?void 0:t.cca2,className:"h-5 w-8 rounded-sm"}),(0,s.jsx)("span",{children:null==S?void 0:S.name})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(M.Z,{variant:"Bold",size:"16",className:"text-primary"}),(0,s.jsx)("span",{children:C("Selected")})]}),(0,s.jsxs)(Z.z,{variant:"link",type:"button",onClick:g,className:"h-fit w-fit gap-1 px-0 py-0 text-sm font-semibold text-secondary-text sm:ml-auto",children:[(0,s.jsx)("span",{children:C("Change country")}),(0,s.jsx)(B.Z,{size:16})]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)(i.J,{condition:b.isAgent,children:(0,s.jsxs)("div",{className:"flex w-full items-center gap-2 rounded-xl border border-secondary-200 px-4 py-3 sm:w-auto",children:[(0,s.jsx)(i.J,{condition:null==D?void 0:null===(n=D.method)||void 0===n?void 0:n.logo,children:(0,s.jsx)(w.default,{src:(0,Y.qR)(null==D?void 0:null===(l=D.method)||void 0===l?void 0:l.logo),alt:null==D?void 0:null===(a=D.method)||void 0===a?void 0:a.name,width:64,height:64,className:"h-8 w-8"})}),(0,s.jsx)("p",{className:"text-sm",children:null==D?void 0:null===(r=D.method)||void 0===r?void 0:r.name})]})}),(0,s.jsx)(i.J,{condition:!b.isAgent&&!!b.method,children:(0,s.jsxs)("div",{className:"flex w-full items-center gap-2 rounded-xl border border-secondary-200 px-4 py-3 sm:w-auto",children:[(null==J?void 0:J.logoImage)?(0,s.jsx)(w.default,{src:(0,Y.qR)(null==J?void 0:J.logoImage),alt:null==J?void 0:J.name,width:64,height:64,className:"h-8 w-8"}):null,(0,s.jsx)("p",{className:"text-sm",children:null==J?void 0:J.name})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2 pl-4",children:[(0,s.jsx)(M.Z,{className:"text-primary",variant:"Bold",size:"20"}),(0,s.jsx)("p",{className:"text-sm",children:C("Selected")})]}),(0,s.jsxs)("button",{type:"button",onClick:g,className:"ml-auto flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[(0,s.jsx)("span",{children:C("Change")}),(0,s.jsx)(B.Z,{size:12})]})]})]}),(0,s.jsx)(R.Z,{className:"mb-1 mt-[5px] bg-divider-secondary"}),(0,s.jsxs)(ee.Y,{groupName:C("Deposit details"),children:[(0,s.jsx)(ee.r,{title:C("Amount"),value:"".concat(O.formatVC(Number(null!==(u=null==P?void 0:null===(o=P.data)||void 0===o?void 0:o.amount)&&void 0!==u?u:0)))}),(0,s.jsx)(ee.r,{title:C("Service charge"),value:"".concat(O.formatVC(null!==(m=null==P?void 0:null===(d=P.data)||void 0===d?void 0:d.fee)&&void 0!==m?m:0))}),(0,s.jsx)(ee.r,{title:C("You get"),value:"".concat(O.formatVC(null!==(h=null==P?void 0:null===(c=P.data)||void 0===c?void 0:c.totalAmount)&&void 0!==h?h:0))})]}),(0,s.jsx)(R.Z,{className:"mb-1 mt-[5px] bg-divider-secondary"}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsxs)(Z.z,{variant:"outline",onClick:g,type:"button",children:[(0,s.jsx)(G.Z,{size:16}),(0,s.jsx)("span",{children:C("Back")})]}),(0,s.jsxs)(Z.z,{type:"button",disabled:N,onClick:j,className:"min-w-48",children:[(0,s.jsx)(i.J,{condition:N,children:(0,s.jsx)(y.Loader,{title:C("Processing..."),className:"text-primary-foreground"})}),(0,s.jsxs)(i.J,{condition:!N,children:[(0,s.jsx)("span",{children:C("Next")}),(0,s.jsx)(k.Z,{size:16})]})]})]})]})}let en=j.z.object({wallet:j.z.string().min(1,"Wallet is required."),amount:j.z.string().min(1,"Amount is required."),agent:j.z.string().optional(),country:j.z.string().optional(),method:j.z.string().optional(),isAgent:j.z.boolean().default(!1)});function es(){let{auth:e,deviceLocation:t}=(0,m.a)(),{t:n}=(0,p.$G)(),[o,d]=x.useTransition(),[j,y]=x.useState(null),[w,N]=x.useState("amount"),[b,A]=x.useState([{value:"amount",title:n("Amount"),complete:!1},{value:"payment_method",title:n("Payment method"),complete:!1},{value:"review",title:n("Review"),complete:!1}]),Z=(0,v.useRouter)(),S=(0,f.cI)({resolver:(0,h.F)(en),mode:"all",defaultValues:{wallet:"",amount:"",agent:"",country:null==t?void 0:t.countryCode,method:"",isAgent:!1}});x.useEffect(()=>{"amount"===w&&S.reset({wallet:S.getValues("wallet"),amount:S.getValues("amount"),agent:"",country:null==t?void 0:t.countryCode,method:"",isAgent:!1})},[w]),x.useEffect(()=>{t&&"amount"===w&&S.reset({wallet:S.getValues("wallet"),amount:S.getValues("amount"),agent:"",country:null==t?void 0:t.countryCode,method:"",isAgent:!1})},[t]),x.useEffect(()=>()=>{S.reset({wallet:"",amount:"",agent:"",country:null==t?void 0:t.countryCode,method:"",isAgent:!1})},[]);let k=e=>{let t=[...b];A(null==t?void 0:t.map(t=>t.value===e?{...t,complete:!0}:t))};return(null==e?void 0:e.canMakeDeposit())?(0,s.jsx)(r.l0,{...S,children:(0,s.jsx)("form",{children:(0,s.jsx)("div",{className:"w-full p-4 pb-10 md:p-12",children:(0,s.jsxs)("div",{className:"mx-auto max-w-3xl",children:[(0,s.jsx)(a.R,{tabs:b,value:w,onTabChange:e=>N(e),children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)(a.Q,{value:"amount",children:(0,s.jsx)(I,{form:S,onNext:()=>{N("payment_method"),k("amount")}})}),(0,s.jsx)(a.Q,{value:"payment_method",children:(0,s.jsx)(X,{form:S,updateTab:()=>{if(S.getValues("isAgent")&&!S.getValues("method")){S.setError("method",{message:n("Select a method to continue."),type:"required"});return}N("review"),k("payment_method")},onBack:()=>N("amount")})}),(0,s.jsx)(a.Q,{value:"review",children:(0,s.jsx)(et,{formData:S.getValues(),isLoading:o,onBack:()=>N("payment_method"),onNext:S.handleSubmit(e=>{d(async()=>{var t,n;if(e.isAgent){let n=await c({agentId:e.agent?e.agent.toString():"",method:e.method?null===(t=e.method)||void 0===t?void 0:t.toString():"",inputValue:"",amount:Number(e.amount),currencyCode:e.wallet,countryCode:e.country});(null==n?void 0:n.status)?(g.toast.success(null==n?void 0:n.message),Z.push("/deposit/transaction-status?trxId=".concat(n.data.trxId)),y({res:n})):g.toast.error(null==n?void 0:n.message)}if(!e.isAgent&&e.method){let t=await u({method:e.method?null===(n=e.method)||void 0===n?void 0:n.toString():"",amount:Number(e.amount),currencyCode:e.wallet,country:e.country});(null==t?void 0:t.status)?(g.toast.success(null==t?void 0:t.message),Z.push("/deposit/transaction-status?trxId=".concat(t.data.trxId)),N("confirm"),y(t)):g.toast.error(null==t?void 0:t.message)}}),k("review")},()=>{g.toast.error(n("Something went wrong."))})})})]})}),(0,s.jsx)(i.J,{condition:"confirm"===w,children:(0,s.jsx)(C,{res:j})})]})})})}):(0,s.jsx)(l.Z,{className:"flex-1 p-10"})}},41709:function(e,t,n){function s(e){let{condition:t,children:n}=e;return t?n:null}n.d(t,{J:function(){return s}}),n(2265)},80114:function(e,t,n){n.d(t,{default:function(){return r}});var s=n(57437),i=n(85487),l=n(94508),a=n(43949);function r(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,s.jsx)("div",{className:(0,l.ZP)("absolute inset-0 left-0 top-0 z-[999] flex h-screen w-screen items-center justify-center overflow-hidden bg-background",t),children:(0,s.jsx)(i.Loader,{title:n("Loading..."),className:"text-foreground"})})}},27300:function(e,t,n){n.d(t,{Z:function(){return r}});var s=n(57437),i=n(94508),l=n(27648),a=n(43949);function r(e){let{className:t}=e,{t:n}=(0,a.$G)();return(0,s.jsx)("div",{className:(0,i.ZP)("flex items-center justify-center",t),children:(0,s.jsxs)("div",{className:"w-full max-w-[600px] rounded-xl border bg-background p-10",children:[(0,s.jsx)("h3",{className:"mb-2.5",children:n("This feature is temporarily unavailable")}),(0,s.jsxs)("p",{className:"text-sm text-secondary-text",children:[n("You no longer have permission to use this feature. If you believe this is an error or require further assistance, please contact "),(0,s.jsx)(l.default,{href:"/contact-supports",className:"text-primary hover:underline",children:n("support")}),"."]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-secondary-text",children:n("Thank you for your understanding.")})]})})}},25318:function(e,t,n){n.d(t,{Y:function(){return a},r:function(){return r}});var s=n(57437),i=n(93022),l=n(94508);function a(e){let{groupName:t,children:n,className:i}=e;return(0,s.jsxs)("ul",{className:(0,l.ZP)("flex flex-col gap-y-4 text-sm sm:text-base",i),children:[t?(0,s.jsx)("li",{className:"text-base font-medium leading-[22px]",children:t}):null,n]})}function r(e){let{title:t,value:n,className:a,titleClassName:r,valueClassName:o,isLoading:d=!1}=e;return d?(0,s.jsx)("li",{className:(0,l.ZP)("flex items-center gap-4",a),children:(0,s.jsx)(i.O,{className:"h-5 w-2/3"})}):(0,s.jsxs)("li",{className:(0,l.ZP)("flex items-center gap-4",a),children:[(0,s.jsx)("div",{className:(0,l.ZP)("flex-1",r),children:t}),(0,s.jsx)("div",{className:(0,l.ZP)("justify-self-end text-right font-medium leading-[22px]",o),children:n})]})}n(2265)},70880:function(e,t,n){n.d(t,{Q:function(){return c},R:function(){return d}});var s=n(57437),i=n(12339),l=n(94508),a=n(19571),r=n(2265),o=n(6512);function d(e){let{value:t="",tabs:n=[],children:d,onTabChange:c}=e,[u,m]=r.useState(0),h=n.filter(e=>void 0===e.isVisible||!0===e.isVisible),v=h.findIndex(e=>e.value===t),x=h.length;return r.useEffect(()=>{m((v+1)/x*100)},[v,x,t]),(0,s.jsxs)(i.mQ,{value:t,onValueChange:c,children:[(0,s.jsx)("div",{className:"hidden h-0.5 w-full bg-background-body md:flex",children:(0,s.jsx)(o.Z,{className:(0,l.ZP)("h-0.5 bg-primary transition-[width] duration-200"),style:{width:"".concat(u,"%")}})}),(0,s.jsx)(i.dr,{className:"hidden bg-transparent md:flex",children:h.map((e,t)=>(0,s.jsxs)(i.SP,{value:e.value,disabled:t>v,"data-complete":e.complete,className:"ring-none group h-8 justify-start rounded-lg border-none border-border px-3 text-sm font-normal leading-5 text-foreground shadow-none outline-none transition-all duration-200 hover:bg-accent hover:text-primary data-[state=active]:bg-transparent data-[complete=true]:text-primary data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:hover:bg-accent",children:[(0,s.jsx)(a.Z,{size:19,className:"mr-2 group-hover:text-primary",variant:e.complete?"Bold":"Linear"}),e.title]},e.value))}),d]})}function c(e){let{children:t,...n}=e;return(0,s.jsx)(i.nU,{...n,children:t})}},52323:function(e,t,n){n.d(t,{g:function(){return h}});var s=n(57437),i=n(2265),l=n(85487),a=n(41062),r=n(23518),o=n(57054),d=n(40593),c=n(94508),u=n(36887),m=n(43949);function h(e){var t,n;let{allCountry:h=!1,defaultValue:v,defaultCountry:x,onSelectChange:f,disabled:p=!1,triggerClassName:g,arrowClassName:j,flagClassName:y,display:w,placeholderClassName:N,align:b="start",side:C="bottom"}=e,{t:A}=(0,m.$G)(),{countries:Z,getCountryByCode:S,isLoading:k}=(0,d.F)(),[I,z]=i.useState(!1),[P,E]=i.useState(v);return i.useEffect(()=>{v&&E(v)},[v]),i.useEffect(()=>{(async()=>{x&&await S(x,e=>{e&&(E(e),f(e))})})()},[x]),(0,s.jsxs)(o.J2,{open:I,onOpenChange:z,children:[(0,s.jsxs)(o.xo,{disabled:p,className:(0,c.ZP)("flex h-12 w-full items-center justify-between rounded-md border-none border-input bg-accent px-3 text-base",g),children:[P?(0,s.jsx)("div",{className:"flex flex-1 items-center",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-2 text-left",children:[(0,s.jsx)(a.W,{className:y,countryCode:(null===(t=P.code)||void 0===t?void 0:t.cca2)==="*"?"UN":null===(n=P.code)||void 0===n?void 0:n.cca2}),void 0!==w?w(P):(0,s.jsx)("span",{children:P.name})]})}):(0,s.jsx)("span",{className:(0,c.ZP)("text-placeholder",N),children:A("Select country")}),(0,s.jsx)(u.Z,{className:(0,c.ZP)("size-6",j)})]}),(0,s.jsx)(o.yk,{className:"min-w-[var(--radix-popover-trigger-width)] p-0",align:b,side:C,children:(0,s.jsxs)(r.mY,{children:[(0,s.jsx)(r.sZ,{placeholder:A("Search...")}),(0,s.jsx)(r.e8,{children:(0,s.jsxs)(r.fu,{children:[k&&(0,s.jsx)(l.Loader,{}),h&&(0,s.jsxs)(r.di,{value:A("All countries"),onSelect:()=>{E({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),f({name:"All Countries",code:{cca2:"*",cca3:"SGS",ccn3:"239"},status:"officially-assigned"}),z(!1)},children:[(0,s.jsx)(a.W,{countryCode:"UN"}),(0,s.jsx)("span",{className:"pl-1.5",children:A("All countries")})]}),null==Z?void 0:Z.map(e=>"officially-assigned"===e.status?(0,s.jsxs)(r.di,{value:e.name,onSelect:()=>{E(e),f(e),z(!1)},children:[(0,s.jsx)(a.W,{countryCode:e.code.cca2}),(0,s.jsxs)("span",{className:"pl-1.5",children:[" ",e.name]})]},e.code.ccn3):null)]})})]})})]})}},85539:function(e,t,n){n.d(t,{R:function(){return r}});var s=n(57437);n(2265);var i=n(95186),l=n(94508),a=n(48674);function r(e){let{iconPlacement:t="start",className:n,containerClass:r,...o}=e;return(0,s.jsxs)("div",{className:(0,l.ZP)("relative flex items-center",r),children:[(0,s.jsx)(a.Z,{size:"20",className:(0,l.ZP)("absolute top-1/2 -translate-y-1/2","end"===t?"right-2.5":"left-2.5")}),(0,s.jsx)(i.I,{type:"text",className:(0,l.ZP)("h-10","end"===t?"pr-10":"pl-10",n),...o})]})}},45932:function(e,t,n){n.d(t,{R:function(){return v}});var s=n(57437),i=n(41709),l=n(33145),a=n(43949);function r(e){let{walletId:t,logo:n,name:i,balance:r,selectedWallet:o,onSelect:d,id:c}=e,{t:u}=(0,a.$G)();return(0,s.jsxs)("label",{htmlFor:"wallet-".concat(t,"-").concat(c),"data-active":t===o,className:"relative flex w-full cursor-pointer flex-col gap-2.5 rounded-xl border-[3px] border-transparent bg-secondary-500 px-6 py-4 transition-all duration-300 ease-linear hover:border-transparent hover:bg-background hover:shadow-light-8 data-[active=true]:border-primary data-[active=true]:bg-primary-selected",children:[(0,s.jsx)("input",{type:"radio",id:"wallet-".concat(t,"-").concat(c),checked:t===o,onChange:()=>d(t),className:"absolute inset-0 left-0 top-0 z-10 cursor-pointer opacity-0"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,s.jsx)(l.default,{src:n,alt:i,width:100,height:100,className:"size-8"}),(0,s.jsx)("h6",{className:"text-sm font-bold leading-5",children:i})]}),(0,s.jsxs)("div",{className:"mt-2.5",children:[(0,s.jsx)("p",{className:"text-xs font-normal leading-4 text-foreground",children:u("Your Balance")}),(0,s.jsx)("p",{className:"text-base font-medium leading-[22px]",children:Number(r).toFixed(2)})]})]})}var o=n(62869),d=n(93022),c=n(48358),u=n(66605),m=n(36887),h=n(2265);let v=(0,h.forwardRef)(function(e,t){var n;let{value:l,onChange:v,id:x}=e,{t:f}=(0,a.$G)(),[p,g]=h.useState(!1),{wallets:j,isLoading:y}=(0,c.r)(),w=h.useMemo(()=>j,[j]);return(h.useEffect(()=>{let e=w.find(e=>e.defaultStatus);e&&!l&&v(null==e?void 0:e.currency.code)},[w]),y)?(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:[(0,s.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,s.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"}),(0,s.jsx)(d.O,{className:"h-[128px] w-full rounded-xl"})]}):(0,s.jsxs)("div",{ref:t,id:x,children:[(0,s.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3",children:null===(n=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;return t?e:e.slice(0,n)}(j,p))||void 0===n?void 0:n.map(e=>(null==e?void 0:e.currency.code)&&(0,s.jsx)(h.Fragment,{children:(0,s.jsx)(r,{walletId:null==e?void 0:e.currency.code,logo:e.logo,name:null==e?void 0:e.currency.code,balance:e.balance,selectedWallet:l,onSelect:v,id:x})},e.walletId))}),(0,s.jsx)(i.J,{condition:(null==j?void 0:j.length)>3,children:(0,s.jsx)("div",{className:"mt-2 flex justify-end",children:(0,s.jsxs)(o.z,{type:"button",variant:"link",onClick:()=>g(!p),className:"flex items-center gap-1 px-3 py-1 text-sm font-semibold text-secondary-text transition duration-300 ease-out hover:text-primary",children:[(0,s.jsx)("span",{className:"text-inherit",children:f(p?"Show less":"Show more")}),p?(0,s.jsx)(u.Z,{size:12}):(0,s.jsx)(m.Z,{size:12})]})})})]})})},16831:function(e,t,n){n.d(t,{F$:function(){return o},Q5:function(){return d},qE:function(){return r}});var s=n(57437),i=n(2265),l=n(61146),a=n(94508);let r=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.fC,{ref:t,className:(0,a.ZP)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",n),...i})});r.displayName=l.fC.displayName;let o=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.Ee,{ref:t,className:(0,a.ZP)("aspect-square h-full w-full",n),...i})});o.displayName=l.Ee.displayName;let d=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.NY,{ref:t,className:(0,a.ZP)("flex h-full w-full items-center justify-center rounded-full bg-muted",n),...i})});d.displayName=l.NY.displayName},15681:function(e,t,n){n.d(t,{NI:function(){return f},Wi:function(){return u},l0:function(){return d},lX:function(){return x},xJ:function(){return v},zG:function(){return p}});var s=n(57437),i=n(37053),l=n(2265),a=n(29501),r=n(26815),o=n(94508);let d=a.RV,c=l.createContext({}),u=e=>{let{...t}=e;return(0,s.jsx)(c.Provider,{value:{name:t.name},children:(0,s.jsx)(a.Qr,{...t})})},m=()=>{let e=l.useContext(c),t=l.useContext(h),{getFieldState:n,formState:s}=(0,a.Gc)(),i=n(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:r}=t;return{id:r,name:e.name,formItemId:"".concat(r,"-form-item"),formDescriptionId:"".concat(r,"-form-item-description"),formMessageId:"".concat(r,"-form-item-message"),...i}},h=l.createContext({}),v=l.forwardRef((e,t)=>{let{className:n,...i}=e,a=l.useId();return(0,s.jsx)(h.Provider,{value:{id:a},children:(0,s.jsx)("div",{ref:t,className:(0,o.ZP)("space-y-2",n),...i})})});v.displayName="FormItem";let x=l.forwardRef((e,t)=>{let{className:n,required:i,...l}=e,{error:a,formItemId:d}=m();return(0,s.jsx)("span",{children:(0,s.jsx)(r.Z,{ref:t,className:(0,o.ZP)(a&&"text-base font-medium text-destructive",n),htmlFor:d,...l})})});x.displayName="FormLabel";let f=l.forwardRef((e,t)=>{let{...n}=e,{error:l,formItemId:a,formDescriptionId:r,formMessageId:o}=m();return(0,s.jsx)(i.g7,{ref:t,id:a,"aria-describedby":l?"".concat(r," ").concat(o):"".concat(r),"aria-invalid":!!l,...n})});f.displayName="FormControl",l.forwardRef((e,t)=>{let{className:n,...i}=e,{formDescriptionId:l}=m();return(0,s.jsx)("p",{ref:t,id:l,className:(0,o.ZP)("text-sm text-muted-foreground",n),...i})}).displayName="FormDescription";let p=l.forwardRef((e,t)=>{let{className:n,children:i,...l}=e,{error:a,formMessageId:r}=m(),d=a?String(null==a?void 0:a.message):i;return d?(0,s.jsx)("p",{ref:t,id:r,className:(0,o.ZP)("text-sm font-medium text-destructive",n),...l,children:d}):null});p.displayName="FormMessage"},57054:function(e,t,n){n.d(t,{J2:function(){return r},xo:function(){return o},yk:function(){return d}});var s=n(57437),i=n(2265),l=n(27312),a=n(94508);let r=l.fC,o=l.xz,d=i.forwardRef((e,t)=>{let{className:n,align:i="center",sideOffset:r=4,...o}=e;return(0,s.jsx)(l.h_,{children:(0,s.jsx)(l.VY,{ref:t,align:i,sideOffset:r,className:(0,a.ZP)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...o})})});d.displayName=l.VY.displayName},74991:function(e,t,n){n.d(t,{E:function(){return o},m:function(){return d}});var s=n(57437),i=n(2265),l=n(42325),a=n(40519),r=n(94508);let o=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.fC,{className:(0,r.ZP)("grid gap-2",n),...i,ref:t})});o.displayName=l.fC.displayName;let d=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.ck,{ref:t,className:(0,r.ZP)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),...i,children:(0,s.jsx)(l.z$,{className:"flex items-center justify-center",children:(0,s.jsx)(a.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=l.ck.displayName},6512:function(e,t,n){var s=n(57437),i=n(55156),l=n(2265),a=n(94508);let r=l.forwardRef((e,t)=>{let{className:n,orientation:l="horizontal",decorative:r=!0,...o}=e;return(0,s.jsx)(i.f,{ref:t,decorative:r,orientation:l,className:(0,a.ZP)("shrink-0 bg-divider","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",n),...o})});r.displayName=i.f.displayName,t.Z=r},93022:function(e,t,n){n.d(t,{O:function(){return l}});var s=n(57437),i=n(94508);function l(e){let{className:t,...n}=e;return(0,s.jsx)("div",{className:(0,i.ZP)("animate-pulse rounded-md bg-muted",t),...n})}},12339:function(e,t,n){n.d(t,{SP:function(){return d},dr:function(){return o},mQ:function(){return r},nU:function(){return c}});var s=n(57437),i=n(2265),l=n(20271),a=n(94508);let r=l.fC,o=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.aV,{ref:t,className:(0,a.ZP)("inline-flex h-10 w-full items-center justify-center rounded-md bg-secondary p-1 text-muted-foreground",n),...i})});o.displayName=l.aV.displayName;let d=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.xz,{ref:t,className:(0,a.ZP)("inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-semibold text-secondary-800 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-defaultLite",n),...i})});d.displayName=l.xz.displayName;let c=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,s.jsx)(l.VY,{ref:t,className:(0,a.ZP)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",n),...i})});c.displayName=l.VY.displayName},17062:function(e,t,n){n.d(t,{Z:function(){return x},O:function(){return v}});var s=n(57437),i=n(80114);n(83079);var l=(0,n(12119).$)("56c3f2e139a4c6bb157d9b7969ec956e3494f518"),a=n(31117),r=n(79981),o=n(78040),d=n(83130);class c{printLocation(){return"".concat(this.city,", ").concat(this.country," (").concat(this.lat,", ").concat(this.lon,")")}constructor(e){this.as=e.as,this.asname=e.asname,this.city=e.city,this.continent=e.continent,this.continentCode=e.continentCode,this.country=e.country,this.countryCode=e.countryCode,this.currency=e.currency,this.district=e.district,this.hosting=e.hosting,this.isp=e.isp,this.lat=e.lat,this.lon=e.lon,this.mobile=e.mobile,this.offset=e.offset,this.org=e.org,this.proxy=e.proxy,this.query=e.query,this.region=e.region,this.regionName=e.regionName,this.reverse=e.reverse,this.status=e.status,this.timezone=e.timezone,this.zip=e.zip}}var u=n(99376),m=n(2265);let h=m.createContext({isAuthenticate:!1,auth:null,isLoading:!0,refreshAuth:()=>{},deviceLocation:void 0,isExpanded:!1,device:"Desktop",setIsExpanded:()=>{},branding:{siteName:"",siteUrl:"",apiUrl:"",defaultCurrency:"",defaultLanguage:"",logo:void 0,favicon:void 0,authBanner:void 0,cardBg:void 0,customerRegistration:!1,agentRegistration:!1,merchantRegistration:!1,referral:{applyOn:"",bonusAmount:"",bonusReceiver:""}},googleAnalytics:{active:!1,apiKey:""}}),v=()=>m.useContext(h);function x(e){let{children:t}=e,[n,v]=m.useState("Desktop"),[x,f]=m.useState(!1),[p,g]=m.useState(),{data:j,isLoading:y,error:w,mutate:N}=(0,a.d)("/auth/check",{revalidateOnFocus:!1}),{data:b,isLoading:C}=(0,a.d)("/settings/global/branding",{revalidateOnFocus:!1}),{data:A,isLoading:Z}=(0,a.d)("/external-plugins/google-analytics",{revalidateOnFocus:!1}),S=(0,u.useRouter)(),k=(0,u.usePathname)();m.useEffect(()=>{(async()=>{v((await l()).deviceType)})()},[]),m.useEffect(()=>{let e=()=>{let e=window.innerWidth;v(e<768?"Mobile":e<1024?"Tablet":"Desktop"),f(e>1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),m.useLayoutEffect(()=>{(async()=>{try{let{data:e}=await r.Z.post("/auth/geo-location");g(new c(e))}catch(e){}})()},[]),m.useLayoutEffect(()=>{w&&!o.sp.includes(k)&&S.push("/signin")},[w]);let I=m.useMemo(()=>{var e,t,s;return{isAuthenticate:!!(null==j?void 0:null===(e=j.data)||void 0===e?void 0:e.login),auth:(null==j?void 0:null===(t=j.data)||void 0===t?void 0:t.user)?new d.n(null==j?void 0:null===(s=j.data)||void 0===s?void 0:s.user):null,isLoading:y,deviceLocation:p,refreshAuth:()=>N(j),isExpanded:x,device:n,setIsExpanded:f,branding:null==b?void 0:b.data,googleAnalytics:(null==A?void 0:A.data)?{active:null==A?void 0:A.data.active,apiKey:null==A?void 0:A.data.apiKey}:{active:!1,apiKey:""}}},[j,p,x,n]),z=!y&&!C&&!Z;return(0,s.jsx)(h.Provider,{value:I,children:z?t:(0,s.jsx)(i.default,{})})}},97751:function(e,t,n){n.d(t,{B:function(){return i},D:function(){return l}});var s=n(43577);function i(e){var t,n,s;return{...e.data,statusCode:e.status,statusText:e.statusText,status:200===e.status||201===e.status,message:null!==(s=null===(t=e.data)||void 0===t?void 0:t.message)&&void 0!==s?s:"",data:null===(n=e.data)||void 0===n?void 0:n.data}}function l(e){let t=500,n="Internal Server Error",i="An unknown error occurred";if((0,s.IZ)(e)){var l,a,r,o,d,c,u,m,h,v,x,f;t=null!==(h=null===(l=e.response)||void 0===l?void 0:l.status)&&void 0!==h?h:500,n=null!==(v=null===(a=e.response)||void 0===a?void 0:a.statusText)&&void 0!==v?v:"Internal Server Error",i=null!==(f=null!==(x=null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(o=d.messages)||void 0===o?void 0:null===(r=o[0])||void 0===r?void 0:r.message)&&void 0!==x?x:null===(m=e.response)||void 0===m?void 0:null===(u=m.data)||void 0===u?void 0:u.message)&&void 0!==f?f:e.message}else e instanceof Error&&(i=e.message);return{statusCode:t,statusText:n,status:!1,message:i,data:void 0,error:e}}},3612:function(e,t,n){n.d(t,{a:function(){return i}});var s=n(17062);let i=()=>{let e=(0,s.O)();return{isAuthenticate:e.isAuthenticate,auth:e.auth,isLoading:e.isLoading,refreshAuth:e.refreshAuth,deviceLocation:e.deviceLocation}}},31117:function(e,t,n){n.d(t,{d:function(){return l}});var s=n(79981),i=n(85323);let l=(e,t)=>(0,i.ZP)(e||null,e=>s.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...t})},48358:function(e,t,n){n.d(t,{r:function(){return a}});var s=n(79981),i=n(54763),l=n(85323);function a(){var e,t;let{data:n,isLoading:a,mutate:r}=(0,l.ZP)("/wallets",e=>s.Z.get(e));return{wallets:null!==(t=null==n?void 0:null===(e=n.data)||void 0===e?void 0:e.map(e=>new i.w(e)))&&void 0!==t?t:[],isLoading:a,getWalletByCurrencyCode:(e,t)=>null==e?void 0:e.find(e=>{var n;return(null==e?void 0:null===(n=e.currency)||void 0===n?void 0:n.code)===t}),mutate:r}}},74539:function(e,t,n){n.d(t,{k:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.city=null==e?void 0:e.city,this.countryCode=null==e?void 0:e.countryCode,this.addressLine=null==e?void 0:e.addressLine,this.street=null==e?void 0:e.street,this.type=null==e?void 0:e.type,this.zipCode=null==e?void 0:e.zipCode,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},83130:function(e,t,n){n.d(t,{n:function(){return o}});class s{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.agentId=null==e?void 0:e.agentId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.occupation=null==e?void 0:e.occupation,this.status=null==e?void 0:e.status,this.isRecommended=!!(null==e?void 0:e.isRecommended),this.isSuspend=!!(null==e?void 0:e.isSuspend),this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.withdrawCommission=null==e?void 0:e.withdrawCommission,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var i=n(84937);class l{constructor(e){this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.merchantId=null==e?void 0:e.merchantId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:e.email,this.status=null==e?void 0:e.status,this.isSuspend=null==e?void 0:e.isSuspend,this.proof=null==e?void 0:e.proof,this.depositFee=null==e?void 0:e.depositFee,this.withdrawFee=null==e?void 0:e.withdrawFee,this.webhookUrl=null==e?void 0:e.webhookUrl,this.allowedIp=null==e?void 0:e.allowedIp,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}var a=n(66419),r=n(78040);class o{canMakeDeposit(){return!!this.permission.deposit}canMakeTransfer(){return!!this.permission.transfer}canMakeWithdraw(){return!!this.permission.withdraw}canMakeExchange(){return!!this.permission.exchange}canMakePayment(){return!!this.permission.payment}canMakeService(){return!!this.permission.services}hasAccountCreationPermission(){return!!this.permission.services}canModifyUserBalance(){return!!this.permission.services}constructor(e){this.getKYCStatus=()=>!!this.kycStatus,this.getReferralCode=()=>this.referralCode,this.getReferralLink=()=>this.referralCode?"".concat(r.rH.APP_URL,"/register?referral=").concat(this.referralCode):"",this.id=e.id,this.roleId=e.roleId,this.email=e.email,this.isEmailVerified=e.isEmailVerified,this.status=e.status,this.kycStatus=e.kycStatus,this.kyc=e.kyc||null,this.lastIpAddress=e.lastIpAddress,this.lastCountryName=e.lastCountryName,this.passwordUpdated=e.passwordUpdated,this.referredBy=e.referredBy,this.referralCode=e.referralCode,this.otpCode=e.otpCode,this.createdAt=new Date(e.createdAt),this.updatedAt=new Date(e.updatedAt),this.role=new a.u(e.role),this.permission=e.permission,this.customer=(null==e?void 0:e.customer)?new i.O(e.customer):void 0,this.merchant=(null==e?void 0:e.merchant)?new l(e.merchant):void 0,this.agent=(null==e?void 0:e.agent)?new s(e.agent):void 0}}},502:function(e,t,n){n.d(t,{Z:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.cardId=null==e?void 0:e.cardId,this.userId=null==e?void 0:e.userId,this.walletId=null==e?void 0:e.walletId,this.number=null==e?void 0:e.number,this.cvc=null==e?void 0:e.cvc,this.lastFour=null==e?void 0:e.lastFour,this.brand=null==e?void 0:e.brand,this.expMonth=null==e?void 0:e.expMonth,this.expYear=null==e?void 0:e.expYear,this.status=null==e?void 0:e.status,this.type=null==e?void 0:e.type,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.wallet=null==e?void 0:e.wallet,this.user=null==e?void 0:e.user}}},28315:function(e,t,n){n.d(t,{F:function(){return s}});class s{format(e){let{currencySymbol:t,amountText:n}=this.formatter(e);return"".concat(n," ").concat(t)}getCurrencySymbol(){let{currencySymbol:e}=this.formatter(0);return e}getFormattedAmountWithoutSymbol(e){let{amountText:t}=this.formatter(e);return t}constructor(e){var t;this.formatter=e=>{var t,n;let s=new Intl.NumberFormat("en-US",{style:"currency",currency:this.code,currencySign:"accounting",currencyDisplay:"code",minimumFractionDigits:2}),i=null!==(n=null===(t=s.formatToParts(e).find(e=>"currency"===e.type))||void 0===t?void 0:t.value)&&void 0!==n?n:this.code,l=s.format(e),a=l.substring(i.length).trim();return{currencyCode:this.code,currencySymbol:i,formattedAmount:l,amountText:a}},this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.code=null==e?void 0:e.code,this.logo=null!==(t=null==e?void 0:e.logo)&&void 0!==t?t:"",this.usdRate=null==e?void 0:e.usdRate,this.acceptApiRate=!!(null==e?void 0:e.acceptApiRate),this.isCrypto=!!(null==e?void 0:e.isCrypto),this.active=!!(null==e?void 0:e.active),this.metaData=null==e?void 0:e.metaData,this.minAmount=null==e?void 0:e.minAmount,this.kycLimit=null==e?void 0:e.kycLimit,this.maxAmount=null==e?void 0:e.maxAmount,this.dailyTransferAmount=null==e?void 0:e.dailyTransferAmount,this.dailyTransferLimit=null==e?void 0:e.dailyTransferLimit,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},84937:function(e,t,n){n.d(t,{O:function(){return i}});var s=n(74539);class i{constructor(e){var t,n;this.id=null==e?void 0:e.id,this.userId=null==e?void 0:e.userId,this.name=null==e?void 0:e.name,this.email=null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.email,this.phone=(null==e?void 0:null===(n=e.phone)||void 0===n?void 0:n.match(/^\+/))?e.phone:"+".concat(null==e?void 0:e.phone),this.gender=null==e?void 0:e.gender,this.dob=new Date(null==e?void 0:e.dob),this.avatar=null==e?void 0:e.profileImage,this.address=new s.k(null==e?void 0:e.address),this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},52789:function(e,t,n){n.d(t,{M:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.logoImage=null==e?void 0:e.logoImage,this.name=null==e?void 0:e.name,this.value=null==e?void 0:e.value,this.apiKey=null==e?void 0:e.apiKey,this.secretKey=null==e?void 0:e.secretKey,this.active=null==e?void 0:e.active,this.activeApi=null==e?void 0:e.activeApi,this.recommended=null==e?void 0:e.recommended,this.variables=null==e?void 0:e.variables,this.allowedCurrencies=null==e?void 0:e.allowedCurrencies,this.allowedCountries=null==e?void 0:e.allowedCountries,this.createdAt=(null==e?void 0:e.createdAt)?new Date(null==e?void 0:e.createdAt):null,this.updatedAt=(null==e?void 0:e.updatedAt)?new Date(null==e?void 0:e.updatedAt):null}}},66419:function(e,t,n){n.d(t,{u:function(){return s}});class s{constructor(e){this.id=null==e?void 0:e.id,this.name=null==e?void 0:e.name,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt)}}},54763:function(e,t,n){n.d(t,{w:function(){return l}});var s=n(502),i=n(28315);class l{constructor(e){var t;this.id=null==e?void 0:e.id,this.walletId=null==e?void 0:e.walletId,this.logo=null==e?void 0:e.logo,this.userId=null==e?void 0:e.userId,this.balance=null==e?void 0:e.balance,this.defaultStatus=!!(null==e?void 0:e.default),this.pinDashboard=!!(null==e?void 0:e.pinDashboard),this.currencyId=null==e?void 0:e.currencyId,this.createdAt=new Date(null==e?void 0:e.createdAt),this.updatedAt=new Date(null==e?void 0:e.updatedAt),this.currency=new i.F(null==e?void 0:e.currency),this.cards=null==e?void 0:null===(t=e.cards)||void 0===t?void 0:t.map(e=>new s.Z(e))}}},59532:function(e,t,n){n.d(t,{v:function(){return s}});function s(e){if(!e)return"";let t=e.split(" ");return(t.length>2?t[0].length>3?t[0][0]+t[t.length-1][0]:t[1][0]+t[t.length-1][0]:2===t.length?t[0][0]+t[1][0]:t[0][0]).toUpperCase()}}}]);