"use client";

import { useTranslation } from "react-i18next";
import { IMilestone } from "@/types/milestone";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Progress from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  Target, 
  DollarSign, 
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react";

interface MilestoneProgressProps {
  milestones: IMilestone[];
}

export function MilestoneProgress({ milestones }: MilestoneProgressProps) {
  const { t } = useTranslation();

  // Calculate progress metrics
  const totalMilestones = milestones.length;
  const completedMilestones = milestones.filter(m => 
    ['completed', 'approved', 'released'].includes(m.status)
  ).length;
  const releasedMilestones = milestones.filter(m => m.status === 'released').length;
  const overdueMilestones = milestones.filter(m => m.isOverdue).length;

  const totalAmount = milestones.reduce((sum, m) => sum + m.amount, 0);
  const completedAmount = milestones
    .filter(m => ['completed', 'approved', 'released'].includes(m.status))
    .reduce((sum, m) => sum + m.amount, 0);
  const releasedAmount = milestones
    .filter(m => m.status === 'released')
    .reduce((sum, m) => sum + m.amount, 0);

  const completionPercentage = totalMilestones > 0 
    ? Math.round((completedMilestones / totalMilestones) * 100) 
    : 0;
  const releasePercentage = totalMilestones > 0 
    ? Math.round((releasedMilestones / totalMilestones) * 100) 
    : 0;
  const amountCompletionPercentage = totalAmount > 0 
    ? Math.round((completedAmount / totalAmount) * 100) 
    : 0;
  const amountReleasePercentage = totalAmount > 0 
    ? Math.round((releasedAmount / totalAmount) * 100) 
    : 0;

  // Get next milestone
  const nextMilestone = milestones
    .filter(m => ['pending', 'in_progress'].includes(m.status))
    .sort((a, b) => a.orderIndex - b.orderIndex)[0];

  // Get currency from first milestone (assuming all milestones use same currency)
  const currency = milestones[0]?.amount ? 'USD' : ''; // In real implementation, get from milestone data

  return (
    <div className="space-y-6">
      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>{t('Milestone Progress')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Completion Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t('Completion Progress')}</span>
              <span className="text-sm text-muted-foreground">
                {completedMilestones} of {totalMilestones} milestones
              </span>
            </div>
            <Progress value={completionPercentage} className="h-3" />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{completionPercentage}% completed</span>
              <span>{totalMilestones - completedMilestones} remaining</span>
            </div>
          </div>

          {/* Release Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t('Release Progress')}</span>
              <span className="text-sm text-muted-foreground">
                {releasedMilestones} of {totalMilestones} released
              </span>
            </div>
            <Progress value={releasePercentage} className="h-3" />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{releasePercentage}% released</span>
              <span>{totalMilestones - releasedMilestones} pending release</span>
            </div>
          </div>

          {/* Amount Progress */}
          {totalAmount > 0 && (
            <>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{t('Amount Completed')}</span>
                  <span className="text-sm text-muted-foreground">
                    {completedAmount.toLocaleString()} of {totalAmount.toLocaleString()} {currency}
                  </span>
                </div>
                <Progress value={amountCompletionPercentage} className="h-3" />
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{amountCompletionPercentage}% of total amount</span>
                  <span>{(totalAmount - completedAmount).toLocaleString()} {currency} remaining</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{t('Amount Released')}</span>
                  <span className="text-sm text-muted-foreground">
                    {releasedAmount.toLocaleString()} of {totalAmount.toLocaleString()} {currency}
                  </span>
                </div>
                <Progress value={amountReleasePercentage} className="h-3" />
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{amountReleasePercentage}% of total amount</span>
                  <span>{(totalAmount - releasedAmount).toLocaleString()} {currency} pending</span>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Progress Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Total')}</p>
                <p className="text-lg font-semibold">{totalMilestones}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Completed')}</p>
                <p className="text-lg font-semibold">{completedMilestones}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-emerald-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Released')}</p>
                <p className="text-lg font-semibold">{releasedMilestones}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">{t('Overdue')}</p>
                <p className="text-lg font-semibold">{overdueMilestones}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Next Milestone */}
      {nextMilestone && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>{t('Next Milestone')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{nextMilestone.title}</h3>
                <p className="text-sm text-muted-foreground">
                  {nextMilestone.amount.toLocaleString()} {currency} • {nextMilestone.percentage}%
                </p>
                {nextMilestone.dueDate && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('Due')}: {nextMilestone.getDueDate()}
                  </p>
                )}
              </div>
              <div className="text-right">
                <Badge 
                  variant="outline" 
                  className={nextMilestone.isOverdue ? 'border-red-200 text-red-800' : 'border-blue-200 text-blue-800'}
                >
                  {nextMilestone.status}
                </Badge>
                <p className={`text-sm mt-1 ${nextMilestone.isOverdue ? 'text-red-600' : 'text-green-600'}`}>
                  {nextMilestone.timeRemaining}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overdue Alert */}
      {overdueMilestones > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <h3 className="font-semibold text-red-800">
                  {t('Overdue Milestones')}
                </h3>
                <p className="text-sm text-red-600">
                  {t('{{count}} milestone(s) are overdue and require immediate attention.', {
                    count: overdueMilestones
                  })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
