(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[60236],{39714:function(e,r,t){Promise.resolve().then(t.bind(t,10422))},10422:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return _}});var i=t(57437),n=t(94097),s=t(83898),u=t(15681),a=t(79981),d=t(97751);async function o(e){let{formData:r,investmentId:t}=e;try{let e={...r,isActive:r.isActive?1:0,isFeatured:r.isFeatured?1:0},i=await a.Z.put("/admin/investment-plans/".concat(t),e);return(0,d.B)(i)}catch(e){return(0,d.D)(e)}}var c=t(13590),m=t(99376),l=t(2265),f=t(29501),v=t(43949),g=t(14438),p=t(31229);let q=p.z.object({name:p.z.string({required_error:"Name is required"}),description:p.z.string({required_error:"Description is required"}),isActive:p.z.boolean().default(!0),isFeatured:p.z.boolean().default(!1),isRange:p.z.string({required_error:"Investment type is required"}),minAmount:p.z.string({required_error:"Minimum amount is required"}),maxAmount:p.z.string({required_error:"Maximum amount is required"}),currency:p.z.string({required_error:"Currency is required"}),interestRate:p.z.string({required_error:"Interest rate is required"}),duration:p.z.string({required_error:"Duration is required"}),durationType:p.z.string({required_error:"Duration type is required"}),withdrawAfterMatured:p.z.string({required_error:"Withdraw after matured is required"})});function h(e){let{formData:r}=e,[t,a]=(0,l.useState)(1),d=(0,m.useRouter)(),[p,h]=(0,l.useTransition)(),{t:A}=(0,v.$G)(),y=(0,m.useParams)(),_=(0,f.cI)({resolver:(0,c.F)(q),defaultValues:{name:"",description:"",isActive:!1,isFeatured:!1,isRange:"",minAmount:"0",maxAmount:"0",currency:r.currency,interestRate:"",duration:"",durationType:r.durationType,withdrawAfterMatured:""}});return(0,l.useEffect)(()=>{r&&_.reset({...r,minAmount:String(r.minAmount),maxAmount:String(r.maxAmount),interestRate:String(r.interestRate),duration:String(r.duration),isActive:!!r.isActive,isFeatured:!!r.isFeatured,isRange:r.isRange?"1":"0",withdrawAfterMatured:r.withdrawAfterMatured?"1":"0"})},[r]),(0,i.jsx)(u.l0,{..._,children:(0,i.jsxs)("form",{onSubmit:_.handleSubmit(e=>{h(async()=>{let r=await o({formData:e,investmentId:String(y.investmentId)});(null==r?void 0:r.status)?(g.toast.success(A("Investment plan updated successfully")),d.push("/investments/manage-plans")):g.toast.error(A(null==r?void 0:r.message))})}),children:[1===t&&(0,i.jsx)(s.Z,{form:_,setStep:a}),2===t&&(0,i.jsx)(n.Z,{form:_,setStep:a,isPending:p})]})})}var A=t(85487),y=t(31117);function _(){let e=(0,m.useParams)(),{data:r,isLoading:t}=(0,y.d)("/admin/investment-plans/".concat(e.investmentId)),n=null==r?void 0:r.data;return t?(0,i.jsx)("div",{className:"flex items-center justify-center py-10",children:(0,i.jsx)(A.Loader,{})}):(0,i.jsx)("div",{className:"h-[calc(100vh-157px)] overflow-y-auto bg-background p-4",children:(0,i.jsx)(h,{formData:n})})}},31117:function(e,r,t){"use strict";t.d(r,{d:function(){return s}});var i=t(79981),n=t(85323);let s=(e,r)=>(0,n.ZP)(e||null,e=>i.Z.get(e),{shouldRetryOnError:!1,revalidateOnFocus:!1,...r})}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,38658,58939,80080,29355,92971,95030,1744],function(){return e(e.s=39714)}),_N_E=e.O()}]);