(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[63045],{60736:function(e,r,s){Promise.resolve().then(s.bind(s,87388))},87388:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return b}});var i=s(57437),a=s(8142),t=s(85487),n=s(62869),o=s(15681),d=s(79981),l=s(97751);async function u(e){try{let r=await d.Z.post("/admin/users/create-admin",e);return(0,l.B)(r)}catch(e){return(0,l.D)(e)}}var c=s(31229);let m=c.z.object({firstName:c.z.string({required_error:"First name is required"}),lastName:c.z.string({required_error:"Last name is required"}),email:c.z.string().email("Invalid email address"),password:c.z.string({required_error:"Password is required"}),passwordConfirmation:c.z.string().optional(),addressLine:c.z.string({required_error:"Address line is required"}),zipCode:c.z.string({required_error:"Zip code is required"}),city:c.z.string({required_error:"City is required"}),countryCode:c.z.string({required_error:"Country is required"}),phone:c.z.string({required_error:"Phone is required"}),gender:c.z.enum(["male","female"]).default("male"),dob:c.z.date()}).refine(e=>e.password===e.passwordConfirmation,{message:"Passwords don't match",path:["passwordConfirmation"]});var f=s(13590),p=s(2901),h=s(64394),g=s(99376),x=s(2265),y=s(29501),z=s(43949),w=s(14438);function b(){let{t:e}=(0,z.$G)(),[r,s]=(0,x.useTransition)(),d=(0,g.useRouter)(),l=(0,y.cI)({resolver:(0,f.F)(m),defaultValues:{firstName:"",lastName:"",email:"",password:"",passwordConfirmation:"",addressLine:"",zipCode:"",city:"",phone:"",gender:"male",dob:void 0}});return(0,i.jsx)("div",{className:"min-h-[calc(100vh-78px)] bg-background",children:(0,i.jsxs)("div",{className:"container max-w-[716px] py-16",children:[(0,i.jsxs)("div",{className:"mb-8 flex items-center gap-4",children:[(0,i.jsx)(n.z,{type:"button",variant:"outline",size:"icon",onClick:()=>d.back(),children:(0,i.jsx)(h.Z,{})}),(0,i.jsx)("h3",{children:e("Create New Staff")})]}),(0,i.jsx)(o.l0,{...l,children:(0,i.jsxs)("form",{onSubmit:l.handleSubmit(r=>{let i={...r,dob:(0,p.WU)(r.dob,"yyyy-MM-dd")};s(async()=>{let r=await u(i);(null==r?void 0:r.status)?(w.toast.success(r.message),d.push("/staffs"),l.reset()):w.toast.error(e(r.message))})},()=>{w.toast.error(e("Something went wrong."))}),className:"flex flex-col space-y-4",children:[(0,i.jsx)(a.Z,{form:l}),(0,i.jsx)("div",{className:"flex items-center justify-end py-4",children:(0,i.jsx)(n.z,{disabled:r,children:r?(0,i.jsx)(t.Loader,{title:e("Processing..."),className:"text-primary-foreground"}):e("Create")})})]})})]})})}}},function(e){e.O(0,[14438,31304,83464,2602,85323,5062,80566,93909,28453,49027,33145,2901,38658,42592,98604,31384,60627,50666,227,71400,92971,95030,1744],function(){return e(e.s=60736)}),_N_E=e.O()}]);